import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MyAccountPage } from './my-account/my-account.page';
import { MobileAccountComponentsModule } from "@wph/mobile/account/components";
import { MobileAccountPagesRoutingModule } from "./mobile-account-pages-routing.module";
import { MobileSharedModule } from "@wph/mobile/shared";
import { AjouterPreparateurPage } from './ajouter-preparateur/ajouter-preparateur.page';

@NgModule({
  imports: [CommonModule, MobileAccountComponentsModule, MobileAccountPagesRoutingModule, MobileSharedModule],
  declarations: [MyAccountPage, AjouterPreparateurPage],
})
export class MobileAccountPagesModule {}
