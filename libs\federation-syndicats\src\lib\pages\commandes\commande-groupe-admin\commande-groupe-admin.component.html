<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-6 col-sm-4">Liste des Commandes Groupées</h4>

        <div class="col-6 col-sm-8 px-1">
            <div class="row justify-content-end align-items-center">
                <!-- add button if needed -->
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="row d-flex m-0 px-1">
    <div class="card m-0 w-100 p-0 bg-white" [style.height]="'calc(100vh - 62px)'">
        <div class="card-header py-1 pl-2 mx-0 bg-white">
            <div class="col-12 p-0 d-flex justify-content-end">
                <div class="row p-0">
                    <ng-container *ngIf="selectedOffre">
                        <button (click)="displayFilter = !displayFilter" type="button"
                            class="btn btn-sm search-btn b-radius px-2 m-1">
                            <span *ngIf="!displayFilter">
                                <i class="bi bi-sliders"></i>
                                Recherche Avancée
                            </span>

                            <span *ngIf="displayFilter">
                                <i class="mdi mdi-close"></i>
                                Fermer la recherche
                            </span>
                        </button>
                    </ng-container>

                    <div class="col p-0 mx-2 my-1">
                        <div class="input-group picker-input">
                            <input type="search" [formControl]="searchControl" placeholder="Titre de l'offre"
                                class="form-control form-control-md pl-4 rounded-lg search-inpt-cstm"
                                id="groupeCritere" />

                            <div class="picker-icons picker-icons-alt">
                                <i class="mdi mdi-magnify pointer"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form [formGroup]="filterForm" (ngSubmit)="appliquerFiltre()" *ngIf="displayFilter && selectedOffre" wphFocusTrap>
                <div class="row px-1 py-0 d-flex flex-wrap k-gap-2">

                    <div class="col-auto p-0 m-0">
                        <label for="client" class="col-12 px-0 col-form-label text-left">Groupe</label>

                        <div class="col-12 px-0 input-group picker-input">
                            <input type="text" name="groupeEntreprise" id="client" formControlName="groupeEntreprise"
                                class="form-control pl-4 form-control-md b-radius bg-white"
                                [ngbTypeahead]="searchGroupeEntreprise" [resultFormatter]="groupeEntrepriseFormatter"
                                [inputFormatter]="groupeEntrepriseFormatter">

                            <div class="picker-icons picker-icons-alt">
                                <i class="mdi mdi-magnify pointer"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-auto p-0 m-0">
                        <label for="offreur" class="col-12 px-0 col-form-label text-left">Offreur</label>

                        <div class="col-12 px-0 input-group picker-input">
                            <input type="text" name="offreur" id="offreur" formControlName="offreur"
                                class="form-control pl-4 form-control-md b-radius bg-white"
                                [ngbTypeahead]="searchFournisseur" [resultFormatter]="laboFormatter"
                                [inputFormatter]="laboFormatter">

                            <div class="picker-icons picker-icons-alt">
                                <i class="mdi mdi-magnify pointer"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-auto p-0 m-0">
                        <label for="distributeur" class="col-12 px-0 col-form-label text-left">Distributeur</label>

                        <div class="col-12 px-0 input-group picker-input">
                            <input type="text" name="distributeur" id="distributeur" formControlName="distributeur"
                                class="form-control pl-4 form-control-md b-radius bg-white"
                                [ngbTypeahead]="searchFournisseur" [resultFormatter]="fournisseurFormatter"
                                [inputFormatter]="fournisseurFormatter">

                            <div class="picker-icons picker-icons-alt">
                                <i class="mdi mdi-magnify pointer"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-auto p-0 m-0">
                        <label for="dateDebut" class="col-12 px-0 col-form-label text-left">Date Création Début</label>

                        <div class="col-12 px-0 input-group picker-input">
                            <input [readOnly]="true" (click)="drange1.toggle()" type="text" name="dateDebut"
                                id="dateDebut" formControlName="dateDebut" ngbDatepicker #drange1="ngbDatepicker"
                                class="form-control pr-4 form-control-md b-radius bg-white" placeholder="jj/mm/aaaa">

                            <div class="picker-icons picker-icons">
                                <i (click)="drange1.toggle()" class="mdi mdi-calendar text-dark pointer"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-auto p-0 m-0">
                        <label for="dateFin" class="col-12 px-0 col-form-label text-left">Date Création Fin</label>

                        <div class="col-12 px-0 input-group picker-input">
                            <input [readOnly]="true" (click)="drange2.toggle()" type="text" name="dateFin" id="dateFin"
                                formControlName="dateFin" ngbDatepicker #drange2="ngbDatepicker"
                                class="form-control pr-4 form-control-md b-radius bg-white" placeholder="jj/mm/aaaa">

                            <div class="picker-icons picker-icons">
                                <i (click)="drange2.toggle()" class="mdi mdi-calendar text-dark pointer"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-6 mt-1 py-1 pb-0 px-0">
                        <label class="col-12 form-label p-0 m-0" for="selectstatut" style="margin-bottom: 4px !important;">Statut</label>

                        <div class="col-12 px-0 input-group">
                            <select2 id="selectstatut" [data]="stautsLabelsValues" formControlName="statut"
                                hideSelectedItems="false" class="form-control-sm p-0 w-100" multiple="false"></select2>
                        </div>
                    </div>

                    <div class="col d-flex align-items-end p-0">
                        <button (click)="viderFiltre()" type="button" title="Vider"
                            class="btn btn-sm btn-outline-primary b-radius">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>

                        <button type="submit" title="Appliquer filtre" class="btn btn-sm btn-primary b-radius mx-1">
                            <i class="mdi mdi-filter"></i>
                        </button>

                    </div>
                </div>
            </form>
        </div>
        <div class="sondages-container" [id]="currentPlateforme === 'WIN_GROUPE' ? 'wf-container' : 'fs-container'">
            <div class="sondages-list m-0 p-2 bg-white card-view">
                <div *ngIf="filteredOffres.length === 0 "
                    class="w-100 d-flex justify-content-center align-items-center">
                    Aucun résultat trouvé. </div>
                <div *ngFor="let offre of filteredOffres"
                    class="sondage-item w-100 py-2 px-3 d-flex flex-column justify-content-between align-items-start mb-2"
                    [ngClass]="{'expanded': offre === selectedOffre}"
                    style="background-color: #F6F6F6; border-radius: 12px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); transition: all 0.3s ease;">

                    <!-- Header Section -->
                    <div class="d-flex w-100 justify-content-between align-items-center" style="cursor: pointer;"
                        (click)="toggleOffre(offre)">
                        <div class="d-flex align-items-center">
                            <div class="image-container"
                                [ngStyle]="{'background-image': getImageUrl(offre) ? 'url(' + getImageUrl(offre) + ')' : 'var(--fs-actu-default-img)', 'background-size': 'cover', 'background-position': 'center', 'height': '50px', 'width': '50px', 'margin-right': '8px', 'border-radius': '12px'}">
                            </div>
                            <span class="offre-title" style="font-size: 18px; color: #696C75; font-weight: 800;">{{
                                offre?.titre }}</span>
                        </div>

                        <div class="d-flex align-items-center">
                            <i [ngClass]="{'bi-chevron-double-down': offre !== selectedOffre,'bi-chevron-double-up': offre === selectedOffre}"
                                class="transition-icon" style="font-size: 28px; color: #696C75;"></i>
                        </div>
                    </div>

                    <!-- Table Section -->
                    <div *ngIf="offre === selectedOffre" class="table-container mt-2 w-100">
                        <div class="px-1">
                            <kendo-grid [data]="gridData" [pageable]="true" [pageSize]="navigation.pageSize"
                                class="fs-grid fs-listing-grid" (cellClick)="cellClickHandler($event)"
                                (sortChange)="sortChange($event)" [sortable]="{ mode: 'single'}" [sort]="commandeSort"
                                [resizable]="true" [skip]="navigation.skip" style="height: 100%">

                                <kendo-grid-column field="codeCommande" title="Code Cmd" [width]="120">
                                    <ng-template kendoGridHeaderTemplate let-column>
                                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                                            [active]="navigation.sortField === column.field" [title]="column.title"
                                            [type]="'numeric'"></app-grid-sort-header>
                                    </ng-template>
                                </kendo-grid-column>

                                <kendo-grid-column title="Groupe" field="groupeEntreprise.raisonSociale"
                                    class="text-wrap" [width]="160">
                                    <ng-template kendoGridCellTemplate let-dataItem>
                                        {{ dataItem?.groupeEntreprise?.raisonSociale }}
                                    </ng-template>

                                    <ng-template kendoGridHeaderTemplate let-column>
                                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                                            [active]="navigation.sortField === column.field" [title]="column.title"
                                            [type]="'alpha'"></app-grid-sort-header>
                                    </ng-template>
                                </kendo-grid-column>
                                
                                <kendo-grid-column title="Ville" field="groupeEntreprise.ville"
                                    class="text-wrap" [width]="140">
                                    <ng-template kendoGridCellTemplate let-dataItem>
                                        {{ dataItem?.groupeEntreprise?.ville }}
                                    </ng-template>

                                    <ng-template kendoGridHeaderTemplate let-column>
                                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                                            [active]="navigation.sortField === column.field" [title]="column.title"
                                            [type]="'alpha'"></app-grid-sort-header>
                                    </ng-template>
                                </kendo-grid-column>

                                <kendo-grid-column field="offre.offreur.raisonSociale" title="Offreur" class="text-wrap"
                                    [width]="140">
                                    <ng-template kendoGridCellTemplate let-dataItem>
                                        {{ dataItem?.offreur?.raisonSociale | uppercase }}
                                    </ng-template>
                                    <ng-template kendoGridHeaderTemplate let-column>
                                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                                            [active]="navigation.sortField === column.field" [title]="column.title"
                                            [type]="'alpha'"></app-grid-sort-header>
                                    </ng-template>
                                </kendo-grid-column>

                                <kendo-grid-column field="distributeur.raisonSociale" class="text-wrap"
                                    title="Distributeur" [width]="140">
                                    <ng-template kendoGridCellTemplate let-dataItem>
                                        <ng-container *ngIf="dataItem?.distributeur; else: noData">
                                            {{ dataItem?.distributeur?.raisonSociale }}
                                        </ng-container>
                                    </ng-template>
                                    <ng-template kendoGridHeaderTemplate let-column>
                                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                                            [active]="navigation.sortField === column.field" [title]="column.title"
                                            [type]="'alpha'"></app-grid-sort-header>
                                    </ng-template>
                                </kendo-grid-column>

                                <kendo-grid-column title="Supporteur" field="supporterEntreprise.nomResponsable" class="text-wrap" [width]="140">
                                    <ng-template kendoGridCellTemplate let-dataItem>
                                        <span *ngIf="dataItem?.supporterEntreprise; else: noData">
                                            Dr. {{ dataItem?.supporterEntreprise?.nomResponsable | uppercase }}
                                        </span>
                                    </ng-template>

                                    <ng-template kendoGridHeaderTemplate let-column>
                                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                                            [active]="navigation.sortField === column.field" [title]="column.title"
                                            [type]="'alpha'"></app-grid-sort-header>
                                    </ng-template>
                                </kendo-grid-column>

                                <kendo-grid-column field="dateCreation" title="Date Création" [width]="140">
                                    <ng-template kendoGridCellTemplate let-dataItem>
                                        <span *ngIf="dataItem?.dateCreationCmd; else: emptyDate">{{
                                            dataItem?.dateCreationCmd | date:
                                            'dd/MM/yyyy' }}</span>
                                    </ng-template>
                                    <ng-template kendoGridHeaderTemplate let-column>
                                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                                            [active]="navigation.sortField === column.field" [title]="column.title"
                                            [type]="'numeric'"></app-grid-sort-header>
                                    </ng-template>
                                </kendo-grid-column>

                                <kendo-grid-column title="Statut" [width]="120">
                                    <ng-template kendoGridCellTemplate let-dataItem>
                                        <app-element-status
                                            [state]="dataItem?.etatCommandeAchatGroupe"></app-element-status>
                                    </ng-template>
                                </kendo-grid-column>

                                <ng-template kendoPagerTemplate let-totalPages="totalPages"
                                    let-currentPage="currentPage" let-total="total">
                                    <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages"
                                        [currentPage]="currentPage" [navigation]="navigation" style="width: 100%;"
                                        (pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
                                </ng-template>

                                <ng-template kendoGridNoRecordsTemplate>
                                    <span>Aucun résultat trouvé.</span>
                                </ng-template>

                                <ng-template #emptyDate>
                                    <span>--/--/----</span>
                                </ng-template>

                                <ng-template #noData>
                                    <div class="d-flex justify-content-center align-items-center">
                                        <span>--</span>
                                    </div>
                                </ng-template>
                            </kendo-grid>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>