::ng-deep #WIN_GROUPE-container {
    .simple-value-card {
        border-radius: var(--winoffre-base-border-radius);
        max-width: 400px;
    
        transition: box-shadow 0.5s;
    
        &:hover {
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
    
        }
    
        .title {
            font-size: clamp(1.5rem, 2vw, 2rem);
            font-weight: 700;
            color: var(--fs-secondary-text);
        }
    
        .title-alt {
            font-size: clamp(1rem, 2vw, 1.5rem);
            font-weight: 700;
            color: var(--fs-secondary-text);
        }
    
        .value {
            letter-spacing: normal;
            font-size: clamp(4.5rem, 2vw, 5rem);
            font-weight: 800;
            font-family: sans-serif;
            color: var(--wf-primary-500);
        }
    
        .value-alt {
            letter-spacing: normal;
            font-size: clamp(3rem, 2vw, 3.5rem);
            font-weight: 800;
            font-family: sans-serif;
            color: var(--wf-primary-500);
        }
    
        .link-text {
            font-weight: 600;
            font-size: 1.2rem;
        }
    
        .card-badge {
            color: #fff;
            font-weight: 700;
            font-size: clamp(.85rem, 2vw, 1rem);
            background: var(--fs-success);
            border-radius: 10px 0px 10px 0px;
    
            width: fit-content;
            position: relative;
            top: 0;
            left: 0;
    
            padding: 5px;
        }
    }
} 

::ng-deep #FEDERATION_SYNDICAT-container {
    .simple-value-card {
        border-radius: var(--winoffre-base-border-radius);
        max-width: 400px;
    
        transition: box-shadow 0.5s;
    
        &:hover {
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
    
        }
    
        .title {
            font-size: clamp(1.5rem, 2vw, 2rem);
            font-weight: 700;
            color: var(--fs-secondary-text);
        }
    
        .title-alt {
            font-size: clamp(1rem, 2vw, 1.5rem);
            font-weight: 700;
            color: var(--fs-secondary-text);
        }
    
        .value {
            letter-spacing: normal;
            font-size: clamp(4.5rem, 2vw, 5rem);
            font-weight: 800;
            font-family: sans-serif;
            color: var(--fs-primary-600);
        }
    
        .value-alt {
            letter-spacing: normal;
            font-size: clamp(3rem, 2vw, 3.5rem);
            font-weight: 800;
            font-family: sans-serif;
            color: var(--fs-primary-600);
        }
    
        .link-text {
            font-weight: 600;
            font-size: 1.2rem;
        }
    
        .card-badge {
            color: #fff;
            font-weight: 700;
            font-size: clamp(.85rem, 2vw, 1rem);
            background: var(--fs-success);
            border-radius: 10px 0px 10px 0px;
    
            width: fit-content;
            position: relative;
            top: 0;
            left: 0;
    
            padding: 5px;
        }
    }
} 

