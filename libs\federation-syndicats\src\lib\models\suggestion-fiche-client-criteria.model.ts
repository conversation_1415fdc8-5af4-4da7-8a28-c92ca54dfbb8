import { User } from "@wph/shared";
import { PharmacieEntreprise } from "./pharmacie-entreprise.model"

export class SuggestionFicheClientCriteria {
    clientCible?: PharmacieEntreprise;
    createdAt?: Date;
    dateTraitement?: Date;
    email?: string;
    etatSuggestion?: string[];
    id?: number;
    localite?: string;
    nomResponsable?: string;
    raisonSociale?: string;
    userSuggereur?: User;
    userTraitantId?: User;
    villes?: string[];

    constructor(value?: Partial<SuggestionFicheClientCriteria>) {
        this.clientCible = value?.clientCible || null;
        this.createdAt = value?.createdAt || null;
        this.dateTraitement = value?.dateTraitement || null;
        this.email = value?.email || null;
        this.etatSuggestion = value?.etatSuggestion || null;
        this.id = value?.id || null;
        this.localite = value?.localite || null;
        this.nomResponsable = value?.nomResponsable || null;
        this.raisonSociale = value?.raisonSociale || null;
        this.userSuggereur = value?.userSuggereur || null;
        this.villes = value?.villes || null;
    }
}