import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ListeGuidesComponent } from "./liste/liste-guides.component";


const routes: Routes = [
    {
        path: '',
        pathMatch: 'full',
        redirectTo: 'liste'
    },
    {
        path: 'liste',
        title: 'Liste Guides',
        component: ListeGuidesComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class GuidesRoutingModule {}
