

.container {
  padding: 20px;
  position: relative;
  height: 95%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}


:host {
  line-height: 1.5;
  font-family: <PERSON>o;
}
.send-button-container {
  margin-top: 50px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .send-button {
    width: 80%;
    margin-bottom: 10px;
  }
  span {
    margin-top: 10px;
    color: gray(0.3);
  }
}
.input {
  margin-bottom: 20px;
}
.error-messages {
  color: red;
  position: absolute;
  font-size: 12px;
  width: 100%;
  bottom: -45px;
  left: 0;
  text-align: center;
}
.inputs-container {
  position: relative;
  >div:first-child {
    margin-bottom: 40px;
  }
}

wph-input {
  margin-bottom: 30px;
}


.logo-container {
  margin-bottom: 50px;
  text-align: center;
}
