<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-12">Clients à traiter</h4>
  </div>
</div>
<!-- END HEADER -->

<div class="card">
  <div class="card-header py-1 px-0 mx-0 mb-1 border">
    <div class="d-flex row justify-content-start align-items-center w-100 mx-0 px-0">
      <div class="col-auto p-0 m-1 d-flex">
        <div class="input-group picker-input" style="width: 275px;">
          <input type="search" placeholder="Rechercher par Raison Sociale"
            class="form-control b-radius form-control-md pl-4" id="titreOffre" style="border-radius: 8px;"
            [formControl]="searchControl" autocomplete="off" />

          <div class="picker-icons picker-icons-alt">
            <i class="mdi mdi-magnify pointer"></i>
          </div>
        </div>
      </div>

      <button type="button" class="btn btn-sm search-btn m-1 b-radius" (click)="displayFilter = !displayFilter">
        <span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
          <i class="bi bi-sliders"></i>
          <span class="mx-1">Recherche Avancée</span>
        </span>

        <ng-template #closeFilter>
          <span class="d-flex align-items-center">
            <i class="mdi mdi-close"></i>
            <span class="mx-1">Fermer la recherche</span>
          </span>
        </ng-template>
      </button>

      <button (click)="openRechercheClientLocalModal(rechercheClientLocalAvance)" type="button"
        class="btn btn-sm search-btn-green m-1 b-radius shadow-sm">
        <span class="d-flex align-items-center">
          <span class="sparkle-icon"></span>
          <span class="mx-1 gradient-text">Recherche de client à partir d'un client local</span>
        </span>
      </button>
    </div>

    <form *ngIf="displayFilter" [formGroup]="filterForm" (ngSubmit)="appliquerFiltrer()">
      <div class="d-flex flex-wrap row w-100 mx-0 px-2 k-gap-2" wphFocusTrap>
        <div class="col-lg-auto col-12 m-0 p-0">
          <label for="nomResponsable" class="col-form-label text-dark">Nom Responsable</label>
          <div class="input-group">
            <input id="nomResponsable" type="text" formControlName="nomResponsable" autocomplete="off"
              class="form-control form-control-md text-dark  b-radius">
          </div>
        </div>

        <div class="col-lg-auto col-12 m-0 p-0">
          <label for="ville" class="col-form-label text-dark">Ville</label>
          <div class="input-group">
            <input id="ville" type="text" formControlName="ville" autocomplete="off"
              class="form-control form-control-md text-dark  b-radius">
          </div>
        </div>

        <div class="col-lg-auto col-12 m-0 p-0">
          <label for="localite" class="col-form-label text-dark">Localité</label>
          <div class="input-group">
            <input id="localite" type="text" formControlName="localite" autocomplete="off"
              class="form-control form-control-md text-dark  b-radius">
          </div>
        </div>

        <div class="col-auto d-flex align-items-end p-0 m-0">
          <button (click)="viderFiltrer()" type="button" class="btn btn-sm btn-outline-primary b-radius">
            <i class="bi bi-arrow-clockwise mr-1"></i> <span>Vider</span>
          </button>

          <button type="submit" class="btn btn-sm btn-primary b-radius mx-1">
            <i class="mdi mdi-filter mr-1"></i> <span>Rechercher</span>
          </button>
        </div>
      </div>
    </form>
  </div>

  <div class="card-body p-0 m-0">
    <kendo-grid [data]="clientGroupeData" [pageable]="{
        buttonCount: 5,
        info: true,
        type: 'numeric',
        pageSizes: pageSizes,
        previousNext: true,
        position: 'bottom'
      }" [pageSize]="navigation.pageSize" [skip]="navigation.skip" [style.minHeight]="'calc(100vh - 192px)'"
      (pageChange)="pageChange($event)" [sortable]="{ mode: 'single'}" [sort]="sort" [resizable]="true"
      (sortChange)="sortChange($event)">

      <kendo-grid-column [width]="180" field="raisonSociale" class="text-wrap">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap d-flex align-items-center">Raison Sociale</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.clientGroupe?.raisonSociale }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="180" field="nomResponsable" class="text-wrap">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap d-flex align-items-center">Nom Responsable</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.clientGroupe?.nomResponsable }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="120" field="ville" class="text-wrap">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap d-flex align-items-center">Ville</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.clientGroupe?.ville }}
        </ng-template>

      </kendo-grid-column>

      <kendo-grid-column [width]="120" field="localite" class="text-wrap">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap d-flex align-items-center">Localité</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.clientGroupe?.localite }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="180" field="adresse" class="text-wrap">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap d-flex align-items-center">Adresse</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <span class="truncate-two-lines">{{ dataItem?.clientGroupe?.adresse }}</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="70" media="(min-width: 992px)" [sortable]="false" [resizable]="true"
        class="text-start no-ellipsis">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Action</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="d-flex justify-content-center w-100">
            <span (click)="openModal(activationAccesModal, dataItem?.clientGroupe, 'xl')"
              class="badge badge-warning rounded-pill py-1 px-2 pointer-cus">Demander</span>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="120" media="(max-width: 991px)" [sortable]="false" [resizable]="true"
        class="text-start no-ellipsis">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Action</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="d-flex justify-content-center w-100">
            <span (click)="openModal(activationAccesModal, dataItem?.clientGroupe, 'xl')"
              class="badge badge-warning rounded-pill py-1 px-2 pointer-cus">Demander</span>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-messages pagerItems="lignes" pagerOf="de" pagerItemsPerPage="éléments par page"></kendo-grid-messages>

      <ng-template kendoGridNoRecordsTemplate>
        <span>Aucun résultat trouvé.</span>
      </ng-template>

    </kendo-grid>
  </div>
</div>

<ng-template #activationAccesModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title text-dark">DEMANDER L'ACTIVATION D'ACCÈS</h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss()">
      <i class="mdi mdi-close"></i>
    </button>
  </div>

  <form>
    <div id="DEFAULT-container" class="modal-body p-2 m-0" style="padding-bottom: 130px !important">
      <kendo-grid id="client-groupe-grid" [data]="{data: [selectedPharmacie], total: 1}"
        class="fs-grid fs-grid-white mb-4" [pageable]="false" [pageSize]="navigation.pageSize" [skip]="navigation.skip"
        (pageChange)="pageChange($event)" [sortable]="false" [sort]="sort" [resizable]="true">

        <ng-template kendoGridToolbarTemplate>
          <div class="d-flex row p-0 my-0 mx-0 w-100 align-items-center justify-content-between grid-top-radius">
            <span class="h4 text-dark">{{'informations pharmacie du maroc' | uppercase }}</span>
          </div>
        </ng-template>

        <kendo-grid-column headerClass="bg-even" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Raison Sociale</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{dataItem?.raisonSociale ?? ''}}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column headerClass="bg-even" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Nom Responsable</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{ dataItem?.nomResponsable ?? '' }}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column headerClass="bg-even" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Ville</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{dataItem?.ville ?? ''}}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column headerClass="bg-even" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Localité</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{dataItem?.localite ?? ''}}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column headerClass="bg-even" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Adresse</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{dataItem?.adresse ?? ''}}</span>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>

      <kendo-grid id="client-local-grid" [data]="{data: [clientLocalForm?.value], total: 1}"
        class="fs-grid fs-grid-white" [pageable]="false" [pageSize]="navigation.pageSize" [skip]="navigation.skip"
        (pageChange)="pageChange($event)" [sortable]="false" [sort]="sort" [resizable]="true" style="overflow: visible">

        <ng-template kendoGridToolbarTemplate>
          <div class="d-flex row p-0 my-0 mx-0 w-100 align-items-center justify-content-between grid-top-radius">
            <span class="h4 text-dark">{{'informations Client Local' | uppercase }}</span>

            <div class="col-12 col-lg-5 px-0 d-flex justify-content-center">
              <label for="searchClientLocal" class="h4 text-dark mx-2">{{'Rechercher' | uppercase}}</label>
              <div id="client-picker-input" class="input-group picker-input">
                <input type="text" placeholder="Recherche client : code ou raison sociale" id="searchClientLocal"
                  [ngbTypeahead]="searchClientFournisseur" [inputFormatter]="clientFournisseurFormatter"
                  [resultFormatter]="clientFournisseurFormatter" [resultTemplate]="clientSearchTemplate2"
                  class="form-control form-control-md text-dark b-radius col-12 pl-4" name="searchClientLocal"
                  (ngModelChange)="clientLocalValueChange($event)" [ngModel]="searchClientLocal"
                  style="border-radius: 10px !important;">

                <ng-template #clientSearchTemplate2 let-result="result">
                  <div>
                    <span class="badge badge-info mr-2">{{ result?.code}}</span>
                    <ngb-highlight [result]="result?.raisonSociale" class="text-dark"></ngb-highlight>
                  </div>
                  <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                </ng-template>

                <div class="picker-icons picker-icons-alt">
                  <i class="mdi mdi-magnify"></i>
                </div>
              </div>
            </div>
          </div>
        </ng-template>

        <kendo-grid-column headerClass="bg-odd" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Raison Sociale</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{dataItem?.raisonSociale ?? '---'}}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column headerClass="bg-odd" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Nom Responsable</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{ dataItem?.nomPharmacien ?? '---' }}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column headerClass="bg-odd" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Ville</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{dataItem?.ville ?? '---'}}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column headerClass="bg-odd" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Localité</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{dataItem?.localite ?? '---'}}</span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column headerClass="bg-odd" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-dark">Adresse</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem="dataItem">
            <span class="text-wrap">{{dataItem?.adresse ?? '---'}}</span>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>

      <hr class="w-100 mt-4">

      <div [formGroup]="clientLocalForm" class="row d-flex w-100 mx-0 my-2 justify-content-end">
        <div class="col-lg-6 col-12">
          <label for="gsmPharmacieLocal" class="col-form-label text-dark">GSM <span class="text-danger"
              style="font-size: .75rem;">(Obligatoire*)</span></label>
          <div class="input-group">
            <input id="gsmPharmacieLocal"
              [ngClass]="{'invalid-input': clientLocalForm.get('gsm')?.invalid && clientLocalForm.get('gsm')?.dirty}"
              type="text" formControlName="gsm" class="form-control form-control-md text-dark b-radius input-border">
          </div>

          <div class="text-danger py-1"
            *ngIf="clientLocalForm.get('gsm')?.invalid && clientLocalForm.get('gsm')?.dirty">
            GSM invalide*
          </div>
        </div>

        <div class="col-lg-6 col-12">
          <label for="emailPharmacieLocal" class="col-form-label text-dark">E-mail <span class="text-muted"
              style="font-size: .75rem;">(Facultatif)</span></label>
          <div class="input-group">
            <input id="emailPharmacieLocal"
              [ngClass]="{'invalid-input': clientLocalForm.get('email')?.invalid && clientLocalForm.get('email')?.dirty}"
              type="text" formControlName="email" class="form-control form-control-md text-dark input-border b-radius">
          </div>

          <div class="text-danger py-1"
            *ngIf="clientLocalForm.get('email')?.invalid && clientLocalForm.get('email')?.dirty">
            E-mail invalide*
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer d-flex flex-wrap">
      <button type="button" (click)="modal.dismiss()" class="btn btn-light" tabindex="-1">Fermer</button>

      <ng-container
        *ngIf="!etatDemandeAccesOfSelectedPharmacie || (etatDemandeAccesOfSelectedPharmacie && (etatDemandeAccesOfSelectedPharmacie === 'E' || etatDemandeAccesOfSelectedPharmacie === 'R'))">
        <button type="button" (click)="resetClientLocalForm()" class="btn btn-secondary text-white"
          tabindex="-1">Réinitialiser</button>

        <button type="button" (click)="envoyerDemandeAccess(modal)" [disabled]="clientLocalForm?.invalid"
          class="btn btn-primary ml-1 text-white" tabindex="-1">
          <span *ngIf="!etatDemandeAccesOfSelectedPharmacie">Envoyer <span class="d-none d-lg-inline">la
              demande</span></span>
          <span *ngIf="etatDemandeAccesOfSelectedPharmacie === 'R'">Renvoyer <span class="d-none d-lg-inline">la
              demande</span></span>
          <span *ngIf="etatDemandeAccesOfSelectedPharmacie === 'E'">Modifier <span class="d-none d-lg-inline">la
              demande</span></span>
        </button>
      </ng-container>
    </div>
  </form>
</ng-template>

<ng-template #rechercheClientLocalAvance let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title text-dark">RECHERCHE DE CLIENT À PARTIR D'UN CLIENT LOCAL</h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss()">
      <i class="mdi mdi-close"></i>
    </button>
  </div>

  <div id="DEFAULT-container" class="modal-body">
    <form [formGroup]="rechercheClientLocalForm" (ngSubmit)="appliquerRechercheClientLocal()"
      class="d-flex flex-wrap p-0 mx-0 mt-0 mb-2 w-100">
      <div class="col-lg col-12">
        <label for="raison_sociale" class="col-form-label text-dark">Raison Sociale <span class="text-danger"
            style="font-size: .75rem;">(Obligatoire*)</span>
        </label>

        <div class="input-group">
          <input id="raison_sociale" formControlName="raison_sociale" type="text"
            [ngClass]="{'invalid-input': rechercheClientLocalSubmitted && rechercheClientLocalForm.get('raison_sociale')?.invalid}"
            class="form-control form-control-md text-dark b-radius input-border">
        </div>
      </div>
      
      <div class="col-lg col-12">
        <label for="nom_pharmacien" class="col-form-label text-dark">Nom Responsable <span class="text-danger"
            style="font-size: .75rem;">(Obligatoire*)</span>
        </label>

        <div class="input-group">
          <input id="nom_pharmacien" formControlName="nom_pharmacien" type="text" 
          [ngClass]="{'invalid-input': rechercheClientLocalSubmitted && rechercheClientLocalForm.get('nom_pharmacien')?.invalid}"
            class="form-control form-control-md text-dark b-radius input-border">
        </div>
      </div>

      <div class="col-lg col-12">
        <label for="ville" class="col-form-label text-dark">Ville <span class="text-danger"
            style="font-size: .75rem;">(Obligatoire*)</span>
        </label>

        <div class="input-group">
          <input id="ville" formControlName="ville" type="text"
            [ngClass]="{'invalid-input': rechercheClientLocalSubmitted && rechercheClientLocalForm.get('ville')?.invalid}"
            class="form-control form-control-md text-dark b-radius input-border">
        </div>
      </div>

      <div class="col-lg-3 col-12 my-1 my-lg-0 d-flex align-items-end k-gap-2">
        <button type="button" (click)="viderRechercheClientLocalForm()" class="btn btn-light b-radius">Vider</button>
        <button type="submit" class="btn btn-primary b-radius">Rechercher</button>
      </div>
    </form>

    <div class="d-flex p-0 m-0 w-100">
      <kendo-grid [data]="clientLocalTacGridData" [pageable]="true" [skip]="0" id="recherche-client-local-grid"
        [pageSize]="clientLocalTacGridData?.total || 20" [style.height]="'100%'">
        <kendo-grid-column field="raison_sociale" title="Raison Sociale" [width]="180">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.raison_sociale }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="nom_pharmacien" title="Nom Responsable" [width]="180">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.nom_pharmacien }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="ville" title="Ville" [width]="120">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.ville }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="localite" title="Localité" [width]="120">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.localite }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="adresse1" title="Adresse" [width]="180">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.adresse1 }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="70" media="(min-width: 992px)" [sortable]="false" [resizable]="true"
          class="text-start no-ellipsis">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Action</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            <div class="d-flex justify-content-center w-100">
              <span (click)="selectedClientLocalTac = dataItem; openModal(activationAccesModal, null, 'xl')"
                class="badge badge-warning rounded-pill py-1 px-2 pointer-cus">Demander</span>
            </div>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="120" media="(max-width: 991px)" [sortable]="false" [resizable]="true"
          class="text-start no-ellipsis">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Action</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            <div class="d-flex justify-content-center w-100">
              <span (click)="selectedClientLocalTac = dataItem; openModal(activationAccesModal, null, 'xl')"
                class="badge badge-warning rounded-pill py-1 px-2 pointer-cus">Demander</span>
            </div>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-messages pagerItems="lignes" pagerOf="de"
          pagerItemsPerPage="éléments par page"></kendo-grid-messages>

        <ng-template kendoGridNoRecordsTemplate>
          <span>Aucun résultat trouvé.</span>
        </ng-template>

      </kendo-grid>
    </div>
  </div>

  <div class="modal-footer d-flex flex-wrap">
    <button type="button" (click)="modal.dismiss()" class="btn btn-light" tabindex="-1">Fermer</button>
  </div>
</ng-template>