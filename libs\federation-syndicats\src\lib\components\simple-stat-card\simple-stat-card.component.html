<div (click)="navigateToPage()" class="card m-1 card-radius shadow-lg p-1">
    <div class="row">
        <div class="col-9 d-flex align-items-center">
            <div *ngIf="!stacked; else: stackedRef" class="row card-labels">
                <span class="col-12 label">{{ label | titlecase }}</span>
                <span class="col-12 value">
                    <span *ngIf="isCurrency; else: simple">{{ value | number: '1.2-2' : 'fr-FR' }} Dh</span>
                    <ng-template #simple>
                        <span>{{ value | number : '1.0-0' }}</span>
                    </ng-template>
                    
                </span>
            </div>

            <ng-template #stackedRef>
                <div class="row card-labels">
                    <div *ngIf="(label2 && value2); else: singleLayer" class="col-12">
                        <span class="col-8 label px-0">{{ label | titlecase }}</span>
                        <span class="col-4 value-alt">{{ value | number }}</span>
                    </div>
    
                    <div class="col-12" *ngIf="label2 && value2">
                        <span class="col-8 label px-0">{{ label2 | titlecase }}</span>
                        <span class="col-4 value-alt">{{ value2 | number }}</span>
                    </div>

                    <ng-template #singleLayer>
                        <div class="col-12 single-layer">
                            <span class="col-8 label">{{ label | titlecase }}</span>
                            <span class="col-4 value">{{ value | number }}</span>
                        </div>
                    </ng-template>
                </div>
            </ng-template>
            
        </div>

        <div class="col-3 d-flex align-items-center justify-content-end">
            <span class="card-icon-container d-flex align-items-center justify-content-center">
                <i class="mdi {{iconClass}} mdi-18px"></i>
            </span>
        </div>
    </div>
</div>