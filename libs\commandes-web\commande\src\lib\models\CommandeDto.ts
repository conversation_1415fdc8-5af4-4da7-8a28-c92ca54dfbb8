import {LigneCommandeDto} from './LigneCommandeDto';
import {EnteteBl} from './EnteteBl';

export class CommandeDto {
    id: number;
    societeId: number;
    idhash: string;
    isPanier: boolean;
    dateCommande: string;
    codeSite?: number;
    codeCommande: string;
    type: string;
    statut: string;
    dateValidation: string;
    dateAnnulation: string;
    motifAnnulation: string;
    distributeur: any;
    clientId: number;
    codeClientCatalogue: string;
    codeProduitSite: string;
    raisonSociale: string;
    nomPharmacien: string;
    ville: string;
    origineCommande?: string;
    userCreateur: any;
    userTraitement: any;
    valeurCmdBruteHt: number;
    valeurCmdBruteTtc: number;
    valeurCmdNetHt: number;
    valeurCmdNetTtc: number;
    qteTotale: number;
    qteUg: number;
    tauxRf: number;
    tauxUg: number;
    nbrCmds?: number;
    commentaire: string;
    lignes: LigneCommandeDto[];

    dateRetourBl: string;
    bl: EnteteBl;

    dateEnvoiToFournisseur?: string;
    dateTraitementBl?: string;

    constructor() {
        this.lignes = [];
    }
}
