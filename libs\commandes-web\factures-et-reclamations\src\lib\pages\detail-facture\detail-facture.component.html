<!-- Start Of Header -->
<div class="rowline mb-0" id="bl-header">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-7">Détails Facture</h4>
        <div class="col-5 px-1">
            <div class="row justify-content-end align-items-center">

                <button (click)="back()" class="btn btn-sm btn-dark rounded-pharma m-1">
                    <i class="mdi mdi-close"></i>
                    Quitter
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="card" style="height: auto; height: calc(100vh - 123px)">
    <div class="card-header px-1">
        <div class="d-flex row justify-content-between px-0 mx-0">
            <div class="mx-1 py-1 d-flex row">
                <span><b class="mr-1">Date Facture: </b> {{ enteteFacture?.dateFacture | date: 'dd-MM-yyy' }}</span>
            </div>

            <div class="mx-1 py-1 d-flex row">
                <span><b class="mr-1">N° Facture: </b> {{ enteteFacture?.codeFacture }}</span>
            </div>
        </div>
    </div>

    <div class="card-body px-0 py-0 m-0">
        <kendo-grid (pageChange)="pageChange($event)" [sortable]="false" [pageSize]="navigation.pageSize" [skip]="navigation.skip" [data]="detailFactureData" [resizable]="true" [pageable]="true"
            style="height: 100%">
            <kendo-grid-column media="(max-width: 768px)" title="Détails Facture">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <!---  Mobile Column Template  --->
                    <dl>
                        <dt class="my-2 ml-2">Code Famille Tarifaire: <span>{{ dataItem?.codeFamilleTarifaire }}</span>
                        </dt>

                        <dt class="my-2 ml-2 limited-width">Libellé Famille Tarifaire: <span>{{
                                dataItem?.libelleFamilleTarifaire
                                }}</span>
                        </dt>

                        <dt class="my-2 ml-2">TVA: <span>{{ dataItem?.montantTva | number: "1.2-2":
                                "fr-FR" }} DH</span></dt>

                        <dt class="my-2 ml-2">Remise: <span>{{ dataItem?.montantRemise | number: "1.2-2":
                                "fr-FR" }} DH</span></dt>

                        <dt class="my-2 ml-2">Brut TTC: <span>{{ dataItem?.montantBrutTtc | number: "1.2-2":
                                "fr-FR" }}</span> DH</dt>

                        <dt class="my-2 ml-2">Net TTC: <span>{{ (dataItem?.montantBrutTtc - dataItem?.montantRemise )|
                                number: "1.2-2": "fr-FR" }} DH</span>
                        </dt>
                    </dl>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="codeFamilleTarifaire" class="text-left"
                title="Code Famille Tarifaire">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.codeFamilleTarifaire }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="libelleFamilleTarifaire" class="text-left"
                title="Libellé Famille Tarifaire">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.libelleFamilleTarifaire }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="montantTva" class="text-right" title="TVA">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.montantTva | number: "1.2-2": "fr-FR" }} DH
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="montantRemise" class="text-right" title="Remise">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.montantRemise | number: "1.2-2": "fr-FR" }} DH
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="montantBrutTtc" class="text-right" title="Brut TTC">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.montantBrutTtc | number: "1.2-2": "fr-FR" }} DH
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="montantBrutTtc" class="text-right" title="Net TTC">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ (dataItem?.montantBrutTtc - dataItem?.montantRemise) | number: "1.2-2": "fr-FR" }} DH
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-messages pagerItems="lignes" pagerOf="de"></kendo-grid-messages>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>
        </kendo-grid>
    </div>

    <div class="card-footer m-0 h4 row flex-wrap d-flex justify-content-between px-2 pb-0 pt-2">
        <span class="col-auto text-right p-1 my-1 synthese-container">
            Total TVA: {{ enteteFacture?.montantTva
                | number: "1.2-2": "fr-FR" }} DH 
        </span>

        <span class="col-auto text-right p-1 my-1 synthese-container">
            Total Remise: {{ enteteFacture?.montantRemise
                | number: "1.2-2": "fr-FR" }} DH
        </span>

        <span class="col-auto text-right p-1 my-1 synthese-container">
            Total Brut TTC: {{ enteteFacture?.montantBrutTtc
                | number: "1.2-2": "fr-FR" }} DH
        </span>

        <span class="col-auto text-right p-1 my-1 synthese-container">
            Total Net TTC: {{ (enteteFacture?.montantBrutTtc - enteteFacture?.montantRemise)
                | number: "1.2-2": "fr-FR" }} DH
        </span>
    </div>
</div>