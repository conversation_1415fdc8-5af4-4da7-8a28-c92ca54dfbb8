// src/app/focus-trap.directive.ts
import { AfterViewInit, Directive, ElementRef, HostListener, Input, OnInit } from '@angular/core';

@Directive({
  selector: '[wphFocusTrapPrefixed]'
})
export class FocusTrapPrefixedDirective implements AfterViewInit {
  private allFocusableElements: HTMLInputElement[] = [];
  private currentPrefixElements: HTMLInputElement[] = [];
  private currentPrefix: string | null = null;
  private currentIndex = 0;
  private observer: MutationObserver;

  constructor(private el: ElementRef) {}

  ngAfterViewInit() {

    this.refreshFocusableElements();
    this.setupMutationObserver();
  }

 private refreshFocusableElements() {
    this.allFocusableElements = Array.from(this.el.nativeElement.querySelectorAll('input[data-prefix]'));
  }

    private setupMutationObserver() {
    this.observer = new MutationObserver(() => {
      this.refreshFocusableElements();
    });

    this.observer.observe(this.el.nativeElement, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['data-prefix']
    });
  }


  @HostListener('focusin', ['$event'])
  onFocusIn(event: FocusEvent) {
    const target = event.target as HTMLInputElement;
    const prefix = target.getAttribute('data-prefix');
    if (prefix) {
      this.currentPrefix = prefix;
      this.currentPrefixElements = this.allFocusableElements.filter(el => el.getAttribute('data-prefix') === prefix);
      this.currentIndex = this.currentPrefixElements.indexOf(target);
    }
  }

  @HostListener('keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    switch (event.key) {
      case 'Tab':
        event.preventDefault();
        this.handleFocusChange(event.shiftKey ? -1 : 1);
        break;
      case 'Enter':
        event.preventDefault();
        this.handleFocusChange(1);
        break;
      // case 'ArrowRight':
      case 'ArrowDown':
        event.preventDefault();
        this.handleFocusChange(1);
        break;
      // case 'ArrowLeft':
      case 'ArrowUp':
        event.preventDefault();
        this.handleFocusChange(-1);
        break;
    }
  }

  private handleFocusChange(direction: number) {
    if (this.currentPrefixElements.length === 0) return;

    let nextIndex = this.currentIndex + direction;
    if (nextIndex < 0 || nextIndex >= this.currentPrefixElements.length) {
      nextIndex = this.findNextPrefixIndex(direction);
    }

    this.currentPrefixElements[nextIndex].focus();
    this.currentPrefixElements[nextIndex].select();
  }

  private findNextPrefixIndex(direction: number): number {
    const currentElement = this.currentPrefixElements[this.currentIndex];
    const currentElementIndexInAll = this.allFocusableElements.indexOf(currentElement);
    let nextElementIndexInAll = currentElementIndexInAll;

    while (true) {
      nextElementIndexInAll = (nextElementIndexInAll + direction + this.allFocusableElements.length) % this.allFocusableElements.length;
      const nextElement = this.allFocusableElements[nextElementIndexInAll];
      const nextPrefix = nextElement.getAttribute('data-prefix');
      if (nextPrefix !== this.currentPrefix) {
        this.currentPrefix = nextPrefix;
        this.currentPrefixElements = this.allFocusableElements.filter(el => el.getAttribute('data-prefix') === nextPrefix);
        return direction === 1 ? 0 : this.currentPrefixElements.length - 1;
      }
    }
  }
}




