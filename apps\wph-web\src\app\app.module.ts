import { LOCALE_ID, NgModule } from "@angular/core";
import { BrowserModule } from '@angular/platform-browser';

import { AppComponent } from './app.component';
import { NxWelcomeComponent } from './nx-welcome.component';
import { RouteReuseStrategy, RouterModule } from '@angular/router';
import { AppRoutingModule } from './app-routing.module';
import { environment } from '../environments/environment';
import { AuthModule } from '@wph/core/auth';
import { ENV } from "../../../const";
import { HttpClientModule } from "@angular/common/http";
import { WebLayoutModule } from "@wph/web/layout";
import { NgbDateAdapter, NgbDateParserFormatter } from "@ng-bootstrap/ng-bootstrap";
import { AlertComponent, CustomDateParserFormatter, NgbDateMomentAdapter, RouteCustomStrategy } from "@wph/web/shared";

import localeFr from '@angular/common/locales/fr';
import localeFrExtra from '@angular/common/locales/extra/fr';

import { registerLocaleData } from "@angular/common";
import * as moment from "moment";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { FlexLayoutModule } from "@angular/flex-layout";
import { register } from 'swiper/element/bundle';
import { GoogleAnalyticsDataModule } from "@sophatel/google-analytics-data";

import { ToastrModule } from 'ngx-toastr';
import { provideAnimations } from '@angular/platform-browser/animations';
import { TOAST_TIMEOUT } from "@wph/data-access";


registerLocaleData(localeFr, 'fr-FR', localeFrExtra);
moment.fn.toJSON = function () { return this.format('YYYY-MM-DD HH:mm:ss'); };

@NgModule({
  declarations: [AppComponent, NxWelcomeComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    RouterModule,
    AuthModule,
    WebLayoutModule,
    HttpClientModule,
    FlexLayoutModule,
    AlertComponent,
    GoogleAnalyticsDataModule.forRoot({
      GTAG_ID: environment?.GTAG_ID || null,
      production: environment.production,
      ATTACH_TAG_MANAGER: environment?.ATTACH_TAG_MANAGER || false,
      TAG_MANAGER_CONTAINER_ID: environment?.TAG_MANAGER_CONTAINER_ID || null,
    }),
    ToastrModule.forRoot({ 
      maxOpened: 3, 
      timeOut: TOAST_TIMEOUT, 
      progressBar: true, 
      closeButton: true, 
      preventDuplicates: true, 
      extendedTimeOut: 2000,
      positionClass: 'toast-bottom-right' 
    }),
  ],
  providers: [
    {
      provide: ENV,
      useValue: environment,
    },
    { provide: LOCALE_ID, useValue: 'fr-FR' },

    { provide: NgbDateParserFormatter, useClass: CustomDateParserFormatter },
    { provide: NgbDateAdapter, useClass: NgbDateMomentAdapter },
    {
      provide: RouteReuseStrategy,
      useClass: RouteCustomStrategy
    },
    provideAnimations(),
  ],
  bootstrap: [AppComponent],
})
export class AppModule {
  constructor() { register(); }
}
