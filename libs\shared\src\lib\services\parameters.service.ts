import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { Parameter } from "../models/parameter.model";

@Injectable({
    providedIn: 'root'
})
export class ParameterService {
    baseUrl: string;
    
    constructor(@Inject('ENVIROMENT') private env: any, private http: HttpClient) {
        this.baseUrl = env?.base_url;
    }

    getAllParameters(): Observable<Parameter[]> {
        return this.http.get<Parameter[]>(`${this.baseUrl}/api/parametrecmdweb`, { observe: 'body' });
    }

    getParametersByCodeSite(codeSite: number): Observable<Parameter[]> {
        return this.http.get<Parameter[]>(`${this.baseUrl}/api/parametrecmdweb/${codeSite}`, { observe: 'body' });
    }

    getParametersById(id: number): Observable<Parameter> {
        return this.http.get<Parameter>(`${this.baseUrl}/api/parametrecmdweb/${id}`, { observe: 'body' });
    }
 
    addOrUpdateParameter(payload: Parameter): Observable<Parameter> {
        return this.http.post<Parameter>(`${this.baseUrl}/api/parametrecmdweb`, payload, { observe: 'body' });
    }

    declarerStatutDuBatch(code_site: number, batchStatut: boolean): Observable<any> {
        return this.http.get<any>(`${this.baseUrl}/api/conso_ext/batch-site-statut/declarer-statut-platform`, { observe: 'body', params: { code_site, batchStatut } });
    }
 } 