import { Component, Input } from "@angular/core";
import { Router } from "@angular/router";
import { FsOffreService } from "../../services/fs-offres.service";
import { Pagination } from "@wph/data-access";
import { FsOffreCriteria, SearchOffre } from "../../models/fs-offre.model";

@Component({
    selector: 'wph-link-card',
    templateUrl: './link-card.component.html',
    styleUrls: ['./link-card.component.scss']
})
export class LinkCardComponent {
    @Input() label: string;
    @Input() value: number;
    @Input() link?: string;
    @Input() linkLabel?: string = 'consulter';
    @Input() singleLine?: boolean = false;
    @Input() badgeValue?: number;


    constructor(private router: Router, private fsOffreService: FsOffreService) {}

    
    navigateToPage(): void {
        if (this.link) {
            this.router.navigateByUrl(this.link);
        }
    }
}