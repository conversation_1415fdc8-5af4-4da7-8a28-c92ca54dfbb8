type Catalogue = {
  audited: boolean;
  code: string;
  id: number;
  nom: string;
  typeCatalogue: string;
};

type Noeud = {
  adresse: string;
  code: string;
  codeSite: number;
  id: number;
  nom: string;
  nomSignificatif: string;
  port: number;
};

type EntrepriseDTO = {
  adresse: string;
  adresse2: string;
  catalogue: Catalogue;
  code: string;
  email: string;
  id: number;
  isEnrolled: boolean;
  localite: string;
  noeud: Noeud;
  nomResponsable: string;
  numIce: string;
  raisonSociale: string;
  segmentEntreprise: string;
  typeEntreprise: string;
  ville: string;
};

type Operateur = {
  audited: boolean;
  entrepriseDTO: Partial<EntrepriseDTO>;
  firstname: string;
  id: number;
  lastname: string;
  username: string;
};

interface IAuthLogCriteria {
  adresseIp: string;
  dateDebut: string; // ISO 8601 Date-Time string
  dateFin: string; // ISO 8601 Date-Time string
  nomPrenomOperateur: string;
  operateur: Partial<Operateur>;
};


export class AuthLogCriteria  implements IAuthLogCriteria {
  adresseIp: string;
  dateDebut: string;
  dateFin: string;
  nomPrenomOperateur: string;
  operateur: Partial<Operateur>;
  constructor(authLog: Partial<IAuthLogCriteria>) {
    this.dateDebut = authLog.dateDebut;
    this.dateFin = authLog.dateFin;
    this.nomPrenomOperateur = authLog.nomPrenomOperateur;
    this.operateur = authLog.operateur;
    this.adresseIp = authLog.adresseIp;
}
}
