import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import * as firebase from 'firebase/app';
import 'firebase/messaging';

@Injectable({
  providedIn: 'root',
})
export class ServiceFirebaseCloudMessagingService {
  baseUrl: string;

  private token: string;

  constructor(private httpClient: HttpClient, @Inject('ENVIROMENT') env: any) {
    this.baseUrl = env['base_url'];
  }

  forceSetToken() {
    // const messaging = firebase.messaging();
    // messaging.getToken().then(
    //   (token: string) => {
    //     this.token = token;
    //
    //     this.httpClient.post<any>(this.baseUrl + "/api/notif/push/settoken/" + token, { observe: "body" })
    //       .subscribe(data => {
    //
    //       });
    //   });
  }
}
