import { Component, OnD<PERSON>roy, OnInit } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { CellClickEvent, GridDataResult } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { AuthService } from "@wph/core/auth";
import { CommandeCriteria, Offre, OffresService, Pagination } from "@wph/data-access";
import { EnteteCommandeView, EtatCommande, FsCommandesService } from "@wph/federation-syndicats";
import { SocieteType } from "@wph/shared";
import { ExportPdf, ExportPdfService } from "@wph/web/shared";
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map, takeUntil, Subject } from "rxjs";

type StatutCmd = 'traite' | 'nontraite';

@Component({
    selector: 'wph-commandes-labo',
    templateUrl: './commandes-labo.component.html',
    styleUrls: ['./commandes-labo.component.scss']
})
export class CommandesLaboComponent implements OnInit, OnDestroy {
    unsubscribe$: Subject<boolean> = new Subject<boolean>();

    displayFilter: boolean;
    filterForm: FormGroup;
    searchCriteria: CommandeCriteria = new CommandeCriteria();
    searchFilter: FormControl = new FormControl();
    exportPdfRef: ExportPdf;
    commandeSort: SortDescriptor[];
    gridData: GridDataResult;
    pageSizes: number[] = [5, 10, 15, 20];
    startsWith: RegExp = new RegExp('^CD-\\d*$', 'i');

    navigation: Pagination = {
        pageSize: 15,
        skip: 0
    };

    stautsLabelsValues: any[] = [
        { label: 'Annulé', value: 'A,ANNULEE' },
        { label: 'Envoyée', value: 'E,ENVOYEE' },
        { label: 'En Livraison', value: 'EL,EN_LIVRAISON' },
        { label: 'Livrée', value: 'L,LIVREE' }
    ];

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private authService: AuthService,
        private offresService: OffresService,
        private exportPdfServ: ExportPdfService,
        private fsCommandeService: FsCommandesService,
    ) {
        this.initFilterForm();
        this.searchCriteria = new CommandeCriteria({ laboratoire: this.authService.getPrincipal()?.societe });
    }

    ngOnInit(): void {
        this.buildExport();

        this.route.queryParams.subscribe(qParams => {
            const statut: StatutCmd = qParams['status'];

            if (statut) {
                this.searchCriteria = new CommandeCriteria({
                    ...this.searchCriteria,
                    statut: statut === 'traite' ? ['EL', 'L'] : ['E'],
                    etatCommandes: statut === 'traite' ? ['EN_LIVRAISON', 'LIVREE'] : ['ENVOYEE']
                });
            }

            this.searchCommandesLabo();
        });

        this.listenToSearchFilterChanges();
    }

    initFilterForm(): void {
        this.filterForm = this.fb.group({
            offreur: [null],
            distributeur: [null],
            statut: [null],
            client: [null],
            dateDebut: [null],
            dateFin: [null],
        });
    }

    searchCommandesLabo(): void {
        this.fsCommandeService.searchCommandesLabo(this.navigation, this.searchCriteria).subscribe(res => {
            this.gridData = {
                data: res?.content,
                total: res?.totalElements
            };

            this.exportPdfRef.setData(this.gridData.data);
        });
    }

    buildExport(): void {
        this.exportPdfRef = this.exportPdfServ
            .ref<EnteteCommandeView>()
            .setTitle('Liste des Commandes')
            .addColumn('codeCommande', 'Code Cmd')
            .addColumn('*', "Titre de l'offre", {
                transform: (item: EnteteCommandeView) => {
                    return item?.offre?.titre;
                }
            })
            .addColumn('*', "Distributeur", {
                transform: (item: EnteteCommandeView) => {
                    return item?.distributeur?.raisonSociale;
                }
            })
            .addColumn('*', "Client", {
                transform: (item: EnteteCommandeView) => {
                    return item?.client?.raisonSociale;
                }
            })
            .addColumn('dateCreation', "Date Création", { type: 'date' })
            .addColumn('valeurCmdBruteTtc', "Montant Brut (Dh)", { type: 'decimal' })
            .addColumn('statut', 'Statut', {
                transform: (value: string) => {
                    switch (value) {
                        case 'E':
                            return 'Envoyée';
                        case 'A':
                            return 'Annulé';
                        case 'EL':
                            return 'En Livraison';
                        case 'L':
                            return 'Livrée';
                        case 'S':
                            return 'Supprimé';
                        default:
                            return value;
                    }
                }
            })
            .setData([]);
    }


    appliquerFiltre(): void {
        const payload = this.filterForm?.getRawValue();
        const [statut, etatCommandes] = (payload?.statut as string)?.split(',');

        this.searchCriteria = new CommandeCriteria({
            ...this.searchCriteria,
            fournisseur: payload?.distributeur,
            laboratoire: payload?.offreur,
            client: payload?.client,
            dateDebutCommande: payload?.dateDebut,
            dateFinCommande: payload?.dateFin,
            statut: payload?.statut ? [statut] : null,
            etatCommandes: payload?.statut ? [etatCommandes as EtatCommande] : null,
        });

        this.navigation.skip = 0;

        this.searchCommandesLabo();
    }

    viderFiltre(): void {
        this.filterForm.reset();
        this.navigation.skip = 0;
        this.searchCriteria = new CommandeCriteria({ laboratoire: this.authService.getPrincipal()?.societe });

        this.searchCommandesLabo();
    }

    consulterCommande(selectedCommande: EnteteCommandeView): void {
        if (selectedCommande?.natureCommande === 'I') {
            this.consulterCommandeIndividuelle(selectedCommande?.offre?.id, selectedCommande?.idCommande);
        } else if (selectedCommande?.natureCommande === 'AG') {
            this.consulterCommandeGroupe(selectedCommande);
        }
    }

    consulterCommandeIndividuelle(offreId: number, cmdId: number): void {
        this.router.navigate(
            [`/achats-groupes/commandes/edit/cmd-individuelle`, cmdId],
            { queryParams: { readOnly: true, offreId } }
        );
    }

    consulterCommandeGroupe(item: EnteteCommandeView, readOnly = false): void {
        this.router.navigate(
            ['/achats-groupes/commandes/edit/cmd-groupe', item?.idCommande],
            { queryParams: { readOnly, offreId: item?.offre?.id } }
        );
    }

    filterList(searchQuery: string, clientOnly = false) {
        return this.offresService.searchSociete({
            raisonSociale: searchQuery,
            typeEntreprises: clientOnly ? [SocieteType.CLIENT] : [SocieteType.FABRIQUANT, SocieteType.GROSSISTE]
        });
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    searchClient = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase(), true);
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    fournisseurFormatter = (result: { raisonSociale: any }) =>
        result ? result.raisonSociale : null;

    cellClickHandler(event: CellClickEvent): void {
        if (event?.column?.title === 'Statut') return;

        this.consulterCommande(event?.dataItem);
    }

    sortChange(sort: SortDescriptor[]): void {
        this.commandeSort = sort;
        if (
            this.commandeSort &&
            this.commandeSort.length > 0 &&
            this.commandeSort[0].dir
        ) {

            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.searchCommandesLabo();
    }

    listenToSearchFilterChanges(): void {
        this.searchFilter.valueChanges
            .pipe(
                takeUntil(this.unsubscribe$),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe((term: string) => {
                this.navigation.skip = 0;
                let criteriaKey: string;
                if (this.startsWith.test(term)) {
                    criteriaKey = 'codeCommande';
                    this.searchCriteria['titre'] = null;
                } else {
                    criteriaKey = 'titre';
                    this.searchCriteria['codeCommande'] = null;
                }

                this.searchCriteria = new CommandeCriteria({ ...this.searchCriteria, [criteriaKey]: term });

                this.searchCommandesLabo();
            });
    }

    OnPageChange(event: number): void {
        this.searchCommandesLabo();
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }
}