import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { DemandeAccesClient, DemandeAccesClientCriteria, Pagination, SearchDemandeAccesClient, TacAssociation, TacClientAssociationCriteria, TacClientAssociationResponse } from "@wph/data-access";
import { Observable } from "rxjs";

@Injectable({
    providedIn: 'root'
})
export class DemandeAccesClientService {
    private base_url: string;

    constructor(@Inject('ENVIROMENT') private env: any, private http: HttpClient) {
        this.base_url = this.env?.base_url;
    }

    saveDemandeAccesClient(payload: DemandeAccesClient) {
        return this.http.post<DemandeAccesClient>(`${this.base_url}/api/v1/demande-acces`, payload);
    }

    searchDemandesAccesClient(searchCriteria: DemandeAccesClientCriteria, pagination: Pagination): Observable<SearchDemandeAccesClient> {
        let params = {
            page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
            size: String(pagination.pageSize),
        }

        if (pagination.sortField) {
            params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
        }

        return this.http.post<SearchDemandeAccesClient>(`${this.base_url}/api/v1/demande-acces/search`, searchCriteria, { params });
    }

    getDemandeAccesById(idDemande: number): Observable<DemandeAccesClient> {
        return this.http.get<DemandeAccesClient>(`${this.base_url}/api/v1/demande-acces/${idDemande}`);
    }

    getDemandeAccessByCodeGroupe(codeSite: number, codeClientGroupe: string): Observable<DemandeAccesClient> {
        return this.http.get<DemandeAccesClient>(`${this.base_url}/api/v1/demande-acces/get-one-by-codegroupe`, { params: { codeClientGroupe, codeSite }});
    }

    accepterDemandeAcces(idDemande: number) {
        return this.http.get(`${this.base_url}/api/v1/demande-acces/${idDemande}/accepter`);
    }

    refuserDemandeAcces(idDemande: number, motifRefus: string) {
        return this.http.get(`${this.base_url}/api/v1/demande-acces/${idDemande}/refuser`, { params: { motifRefus }});
    }

    associerTac(payload: DemandeAccesClient): Observable<TacAssociation> {
        return this.http.post<TacAssociation>(`${this.base_url}/api/v1/tac/associer-one-tac`, payload);
    }

    checkAssociationTac(payload: TacClientAssociationCriteria): Observable<TacClientAssociationResponse[]> {
        return this.http.post<TacClientAssociationResponse[]>(`${this.base_url}/api/v1/tac/check-assoc-client`, payload);
    }

    private getPageNumber(skip: number, pageSize: number) {
        return (skip && pageSize) ? Math.floor(skip / pageSize) : 0;
    }

    adminCreationDemandeAccesClient(payload: DemandeAccesClient): Observable<DemandeAccesClient> {
        return this.http.post<DemandeAccesClient>(`${this.base_url}/api/v1/demande-acces/admin-creation`, payload);
    }
}