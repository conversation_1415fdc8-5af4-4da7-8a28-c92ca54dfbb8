import { FsCommandeCriteria } from './../../../models/fs-commande.model';
import { Commande } from './../../../../../../data-access/src/lib/models/commande.model';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Offre, OffreCriteria, OffresService, Pagination } from '@wph/data-access';
import {
  GridDataResult,
  PageChangeEvent,
  SelectionEvent,
} from '@progress/kendo-angular-grid';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AvisCriteria, AvisDTO, TypeAvis } from '../../../models/avis.model';
import { FsCommandesService } from '../../../services/fs-commandes.service';
import { FederationSyndicatService } from '../../../services/federation-syndicats.service';
import { AuthService } from '@wph/core/auth';
import { FsOffreCriteria } from '../../../models/fs-offre.model';
import { EnteteCommandeConsolideeMarche } from '../../../models/entete-commande.model';
import { FormControl } from '@angular/forms';
import { debounceTime, lastValueFrom } from 'rxjs';
import { UploadFileServiceService } from '@wph/shared';

interface SondageCommande {
  commande: Offre;
  positive: number;
  notInterested: number;
}

interface CacheEntry {
  positive: AvisDTO[];
  negative: AvisDTO[];
}

@Component({
  selector: 'wph-sondages-membres',
  templateUrl: './sondages-liste-membres.component.html',
  styleUrls: ['./sondages-liste-membres.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SondagesMembresListeComponent implements OnInit {
  pageSizes: number[] = [5, 10, 15, 20];
  navigation = {
    pageSize: 15,
    skip: 0,
    sortMethod: 'desc'
  };
  gridData: GridDataResult = { total: 0, data: [] };
  groupeSort: SortDescriptor[] = [];
  monGroupe: any;
  membreId: number; 
  searchCriteria: AvisCriteria = { estResponsable: true };
  cmdSearchCriteria: FsCommandeCriteria ;

  isInterested: boolean = true;
  sondages: SondageCommande[] = [];
  selectedSondage: SondageCommande | null = null;
  searchControl: FormControl = new FormControl('');
  filteredSondages: SondageCommande[] = [];
  uploadedDocUrl: string;
  avisCache: { [commandeId: number]: CacheEntry } = {};
  loadingPercentages: { [commandeId: number]: boolean } = {};


  constructor(
    private commandeService: FsCommandesService,
    private avisService: FsCommandesService,
    private fedSyndicatService: FederationSyndicatService,
    private authService: AuthService,
    private offreService: OffresService,
    private uploadService: UploadFileServiceService
  ) {}

  ngOnInit(): void {
    this.fedSyndicatService.getMyGroupe().then(myGroupe => {
      this.monGroupe = myGroupe;
      this.cmdSearchCriteria = new FsCommandeCriteria({ ...this.cmdSearchCriteria, groupeEntreprise: myGroupe });

      this.searchCommandesGroupe();
  }); 
  this.searchControl.valueChanges.pipe(
    debounceTime(300) 
  ).subscribe(value => {
    this.filteredSondages = this.sondages.filter(sondage =>
      sondage.commande.titre.toLowerCase().includes(value.toLowerCase())
    );
  });

  }

  searchCommandesGroupe(): void {
    const criteria = new FsCommandeCriteria({
      ...this.cmdSearchCriteria ,
      etatCommande: ['ENVOYEE', 'EN_ATTENTE', 'EN_LIVRAISON', 'LIVREE', 'REFUSEE', 'ACCEPTEE', 'CLOTUREE', 'TRANSMIS', 'FIN_SAISIE', 'CONFIRMEE']
  });
    const pagination = { pageSize: 100, skip: 0, sortMethod: 'desc' };
    this.commandeService.searchCommandesConsolidee(pagination, criteria).subscribe(res => {
      this.sondages = res.content.map((commande) => ({
        commande: commande,
        positive: null,
        notInterested: null
      }));
      this.filteredSondages = [...this.sondages]; 

        this.gridData = {
            data: res?.content,
            total: res?.totalElements
        };

    });
}

async loadAvis(commandeId?: number): Promise<void> {
  if (this.avisCache[commandeId]) {
    this.updateGridData(this.avisCache[commandeId]);
    this.calculateInterestPercentage(commandeId);
    return;
  }

  const positiveCriteria: AvisCriteria = {
    ...this.searchCriteria,
    commandeConsolideeId: commandeId,
    typeAvis: TypeAvis.Positive
  };

  const negativeCriteria: AvisCriteria = {
    ...this.searchCriteria,
    commandeConsolideeId: commandeId,
    typeAvis: TypeAvis.Negative
  };

  try {
    const [positiveResponse, negativeResponse] = await Promise.all([
      lastValueFrom(this.avisService.rechercherAvisParCritere(positiveCriteria)),
      lastValueFrom(this.avisService.rechercherAvisParCritere(negativeCriteria))
    ]);

    const positiveAvis = positiveResponse.content;
    const negativeAvis = negativeResponse.content;

    this.avisCache[commandeId] = {
      positive: positiveAvis,
      negative: negativeAvis
    };

    this.updateGridData(this.avisCache[commandeId]);
    this.calculateInterestPercentage(commandeId);
  } catch (error) {
    console.error('Error fetching Avis:', error);
    this.gridData = {
      total: 0,
      data: []
    };
  }
}

updateGridData(cacheEntry: CacheEntry): void {
  const avisData = this.isInterested ? cacheEntry.positive : cacheEntry.negative;
  this.gridData = {
    total: avisData.length,
    data: avisData.slice(this.navigation.skip, this.navigation.skip + this.navigation.pageSize),
  };
}
calculateInterestPercentage(commandeId: number): void {
  const cacheEntry = this.avisCache[commandeId];
  if (cacheEntry) {
    const positiveCount = cacheEntry.positive.length;
    const negativeCount = cacheEntry.negative.length;
    const totalCount = positiveCount + negativeCount;

    this.updateSondageInterest(commandeId, positiveCount, negativeCount, totalCount);
  }
}


updateSondageInterest(commandeId: number, positiveCount: number, negativeCount: number, totalCount: number): void {
  const sondage = this.sondages.find(s => s.commande.enteteCommandeId === commandeId);
  if (sondage) {
    sondage.positive = totalCount > 0 ? parseFloat((positiveCount / totalCount * 100).toFixed(2)) : 0;
    sondage.notInterested = totalCount > 0 ? parseFloat((negativeCount / totalCount * 100).toFixed(2)) : 0;
  } else {
    console.warn(`Sondage for commande ${commandeId} not found.`);
  }
}
 

  onSwitchChange(): void {
    if (this.selectedSondage) {
      this.updateGridData(this.avisCache[this.selectedSondage.commande.enteteCommandeId]);
    }
  }

  pageChange(event: PageChangeEvent): void {
    this.navigation.skip = event.skip;
    this.navigation.pageSize = event.take;
    if (this.selectedSondage) {
      this.updateGridData(this.avisCache[this.selectedSondage.commande.enteteCommandeId]);
    }
  }

  sortChange(sort: SortDescriptor[]): void {
    this.groupeSort = sort;
    if (sort.length) {
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortMethod = null;
    }
    if (this.selectedSondage) {
      this.updateGridData(this.avisCache[this.selectedSondage.commande.enteteCommandeId]);
    }
  }

  selectSondage(sondage: SondageCommande): void {
    this.selectedSondage = this.selectedSondage === sondage ? null : sondage;
    if (this.selectedSondage) {
      this.calculateInterestPercentage(this.selectedSondage.commande.enteteCommandeId);
      this.loadAvis(this.selectedSondage.commande.enteteCommandeId);
    }
  }

  toggleSondage(sondage: SondageCommande): void {
    this.selectedSondage = this.selectedSondage === sondage ? null : sondage;
    if (this.selectedSondage) {
      this.loadAvis(this.selectedSondage.commande.enteteCommandeId);
      this.calculateInterestPercentage(this.selectedSondage.commande.enteteCommandeId);
    }
  }

  OnPageChange(event: number): void {
    if (this.selectedSondage) {
      this.updateGridData(this.avisCache[this.selectedSondage.commande.enteteCommandeId]);
    }
  }

  getImageUrl(sondage: SondageCommande): string | null {
    return sondage.commande?.docImageOffre?.idhash
      ? this.uploadService.fetchUploadedDocument(sondage.commande.docImageOffre.idhash)
      : null;
  }
}





