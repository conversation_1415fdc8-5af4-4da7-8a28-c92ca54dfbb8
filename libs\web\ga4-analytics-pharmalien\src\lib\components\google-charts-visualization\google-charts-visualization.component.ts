import { Component, Input, OnChanges, SimpleChanges, ChangeDetectionStrategy } from '@angular/core';
import { ChartType } from 'angular-google-charts';

interface CompatibleChartType {
  type: ChartType;
  displayName: string;
  iconClass: string;
}

@Component({
  selector: 'wph-web-google-chart-visualization',
  templateUrl: './google-charts-visualization.component.html',
  styleUrls: ['./google-charts-visualization.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GoogleChartVisualizationComponent implements OnChanges {
  @Input() chartType: ChartType = ChartType.ColumnChart;
  @Input() chartData: any[][] = [];
  @Input() chartOptions: any = {};
  @Input('columns') internalChartColumns: string[] | null = null;
  @Input() title: string = ''; 

  public internalChartType: ChartType;
  public internalChartData: any[][];
  public internalChartOptions: any;

  public compatibleChartTypes: CompatibleChartType[] = [
    { type: ChartType.ColumnChart, displayName: 'Colonnes', iconClass: 'bi bi-bar-chart-line-fill' },
    { type: ChartType.BarChart, displayName: 'Barres', iconClass: 'bi bi-bar-chart-steps rotate-90-cw' },
    { type: ChartType.LineChart, displayName: 'Lignes', iconClass: 'bi bi-graph-up' },
    { type: ChartType.AreaChart, displayName: 'Aires', iconClass: 'bi bi-graph-up-arrow' }, 
    { type: ChartType.SteppedAreaChart, displayName: 'Aires (pas)', iconClass: 'bi bi-activity' },    
    { type: ChartType.PieChart, displayName: 'Secteurs', iconClass: 'bi bi-pie-chart' },
  ];

  constructor() {
    this.internalChartType = this.chartType;
    this.internalChartData = [];
    this.internalChartOptions = this.getDefaultOptions();
  }

  selectChartType(newType: ChartType): void {
    if (this.internalChartType !== newType) {
      this.internalChartType = newType;
      this.internalChartOptions = {
        ...this.getDefaultOptions(),
        ...this.chartOptions,
        title: this.title || this.chartOptions?.title || '',
      };
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['chartType']) {
      this.internalChartType = this.chartType || ChartType.ColumnChart;
    }
    if (changes['chartData']) {
      this.internalChartData = (this.chartData && this.chartData.length > 0) ? [...this.chartData] : [];

    }
    if (changes['chartOptions'] || changes['title']) {
      this.internalChartOptions = {
        ...this.getDefaultOptions(),
        ...this.chartOptions,
        title: this.title || this.chartOptions?.title || '', 
      };
    }
  }

  private getDefaultOptions(): any {
    return {
      width: '100%',
      height: '100%', // Ensure it fills the container
      legend: { position: 'top', maxLines: 3 },
      bar: { groupWidth: '75%', isStacked: true },
      isStacked: this.internalChartType === ChartType.BarChart || this.internalChartType === ChartType.ColumnChart,
      chartArea: { width: '90%', height: '75%' }, 
    };
  }
}
