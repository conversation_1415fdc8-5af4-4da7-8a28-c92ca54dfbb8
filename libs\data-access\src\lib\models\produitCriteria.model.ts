import { Fournisseur } from './fournisseur.model';
import { GammeProduit } from './gamme-produit.model';
import { FormeProduit } from './forme-produit.model';
export class ProduitCriteria   {
  libelleProduit?: string;
  fournisseur?: Fournisseur;

  gammeProduit?: GammeProduit;
  formeProduit?: FormeProduit;

  constructor(partialProduitCriteria?: Partial<ProduitCriteria>) {
    Object.assign(this, partialProduitCriteria);
  }
}
