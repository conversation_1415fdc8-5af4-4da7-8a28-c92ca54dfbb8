import { Component, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { ChartType } from 'chart.js';
import { FormControl } from '@angular/forms';
import { BehaviorSubject, forkJoin, Subject, takeUntil } from 'rxjs';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { AlertService } from '@wph/shared';
import { FsStatistiqueService } from '../../../services/fs-statistique.service';
import { GroupByField, StatisticsCriteriaDTO } from '../../../models/statistique.model';
import { AuthService } from '@wph/core/auth';
import * as moment from 'moment';
import { ChartOptions } from '@wph/web/shared';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { Router } from '@angular/router';

@Component({
  selector: 'wph-labo-statistiques',
  templateUrl: './labo-statistiques.component.html',
  styleUrls: ['./labo-statistiques.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class LaboStatistiquesComponent implements OnInit, OnDestroy {
  unsubscribe$ = new Subject<boolean>();
  lineChartData: any[] = [];
  isIndividuelle = false;
  gridData:GridDataResult = {total:0,data:[]};
  pieChartData: any = {
    labels: [],
    datasets: []
  };
  displayedStartDate$ = new BehaviorSubject<Date>(new Date());
  displayedEndDate$ = new BehaviorSubject<Date>(new Date());
  startDate = new FormControl();
  endDate = new FormControl();
  now = new Date();
  datePicker = new FormControl();
  datePicker2 = new FormControl();
  minEndDate: NgbDateStruct | null = null;
  statistiqueCriteriaIndividuelle: StatisticsCriteriaDTO;
  statistiqueCriteriaGroupe : StatisticsCriteriaDTO;
  public lineChartLabels: string[] = []
  chartOptions:ChartOptions;

  public lineChartLegend = true;
  public lineChartType = 'line';
  public pieChartLabels: string[] = ['Consommé', 'Supporté'];
  totalsLabo :any ={};

  public pieChartType: ChartType = 'pie';
  public pieChartOptions: any = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: (tooltipItem: any) => {
            const value = tooltipItem.raw;
            const total = this.pieChartData.datasets[0].data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(2) + '%';
            return `${this.pieChartLabels[tooltipItem.dataIndex]}: ${percentage}`;
          }
        }
      }
    }
  };

  public lineChartOptions = {
    responsive: true,
    scales: {
      x: {
        title: {
          display: true,
          text: 'Mois',
          stacked: true
        }
      },
      y: {
        title: {
          display: true,
          text: 'Valeurs en Dh'
        }
      }
    }
  };

  offreStats: any;

  ngOnInit(): void {
    this.initializeDefaultDates();
    this.setFormControlValues();
    // this.updateChartData(this.displayedStartDate$.value, this.displayedEndDate$.value);
    this.listenToDatePickerChanges();
    this.getMyStatistique();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  initializeDefaultDates(): void {
    const defaultStartDate = new Date();
    defaultStartDate.setMonth(defaultStartDate.getMonth() - 6); // 6 months ago
    const defaultEndDate = new Date();

    this.displayedStartDate$.next(defaultStartDate);
    this.displayedEndDate$.next(defaultEndDate);
  }




  getMyStatistique(): void {

    this.statistiqueCriteriaGroupe = new StatisticsCriteriaDTO({
      listGroupByfields: [GroupByField.OFFREUR,GroupByField.PHARMACIEN,GroupByField.ADMIN],
      idLabo: this.authService.getPrincipal()?.societe?.id,

      // get the first day of the month
      dateDebut: moment(this.displayedStartDate$.value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      dateFin: moment(this.displayedEndDate$.value).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
    });
    this.statistiqueCriteriaIndividuelle = new StatisticsCriteriaDTO({
      listGroupByfields: [GroupByField.OFFREUR,GroupByField.PHARMACIEN,GroupByField.ADMIN],
      dateDebut: moment(this.displayedStartDate$.value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      dateFin: moment(this.displayedEndDate$.value).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      idLabo: this.authService.getPrincipal()?.societe?.id,
    });
  forkJoin([
    this.fsStatistiqueService.getStatictiques(this.statistiqueCriteriaGroupe),
    this.fsStatistiqueService.getStatictiques(this.statistiqueCriteriaIndividuelle,true)
  ]).subscribe(res => {
    console.log('Raw response:', res);

    // Create a map to store stats grouped by period
    // const statsByPeriod = new Map<string, any>();

    const flattenArray  = res.flat();
    const statsLaboGrouped =this.groupByPharmacien(flattenArray);
    console.log('Grouped by pharmacien:', statsLaboGrouped);

    // Process all arrays in the response
    // res.forEach(array => {
    //   array.forEach(item => {
    //     const period = item.periode || 'N/A';
    //     const laboratoire = item.offreur?.raisonSociale || 'Unknown';

    //     if (!statsByPeriod.has(period)) {
    //       statsByPeriod.set(period, {
    //         periode: period,
    //         laboratoire: laboratoire,
    //         montantSupportee: 0,
    //         montantConsomme: 0,
    //         countCommandeSupportee: 0,
    //         countCommandeConsommee: 0,
    //         countCmdPasse: 0,
    //         countClient: 0,
    //         balance: 0
    //       });
    //     }

    //     const periodStats = statsByPeriod.get(period);

    //     // Sum up all numeric fields
    //     periodStats.montantSupportee += item.montantSupportee || 0;
    //     periodStats.montantConsomme += item.montantConsomme || 0;
    //     periodStats.countCommandeSupportee += item.countCommandeSupportee || 0;
    //     periodStats.countCommandeConsommee += item.countCommandeConsommee || 0;
    //     periodStats.countCmdPasse += item.countCmdPasse || 0;
    //     periodStats.countClient += item.countClient || 0;
    //     periodStats.balance += item.balance || 0;
    //   });
    // });

    // Convert map to array and sort by period
    // const statisticsByPeriod = Array.from(statsByPeriod.values())
    //   .sort((a, b) => (b.periode || '').localeCompare(a.periode || ''));
     // add periode
     statsLaboGrouped.forEach(item=>{
          const startDate = moment(this.displayedStartDate$.value).startOf('month');
          const endDate = moment(this.displayedEndDate$.value).endOf('month');
          item['periode'] =  startDate.isSame(endDate, 'month') ? startDate.format('MM/YYYY') : `${startDate.format('MM/YYYY')} - ${endDate.format('MM/YYYY')}`;
     })
    this.gridData.data = statsLaboGrouped;
    this.gridData.total = statsLaboGrouped.length;

    const montantSupporter = statsLaboGrouped.map(item => item.montantSupportee);
    const montantConsomme = statsLaboGrouped.map(item => item.montantConsomme);
    const periods = statsLaboGrouped.map(item => item.pharmacien.nomResponsable);

    const totalMontantSupporte = montantSupporter.reduce((a, b) => a + b, 0);
    const totalMontantConsomme = montantConsomme.reduce((a, b) => a + b, 0);
    const totaleCommandes = statsLaboGrouped.reduce((a, b) => a + b.countCmdPasse, 0);
    const totalRemise =  totalMontantSupporte - totalMontantConsomme;
    this.totalsLabo= {
      totalMontantSupporte,
      totalMontantConsomme,
      totaleCommandes,
      totalRemise,
      periode: statsLaboGrouped[0]?.periode
    }

    this.chartOptions = {

      series:[
        // {
        //   name: "Supporté",
        //   data: montantSupporter,
        //   color: "#dc3545"
        // },
        {
          name: "Mnt Net",
          data: montantConsomme,
          color: "#007bff"
        }
      ],
      chart: {
        type: "bar",
        height: 350,
        foreColor: "#000000",

        toolbar:{
          show:true,
          autoSelected:"zoom",

        },




      },
      plotOptions: {

        bar: {
          horizontal: false,
          columnWidth:  "15%" ,
          borderRadius: 10,



          // endingShape: "rounded"
        }
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: false,
        width: 2,
        colors: ["transparent"]
      },
      xaxis: {
        categories: periods,
        tickPlacement: "between",
        offsetX: 10,



      },
      yaxis: [
{
 tickAmount:8,
          title: {
            text: "Montants DH",
          }}

      ],
      fill: {
        opacity: 1
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return  val + " DH";
          }
        }
      },
      grid:{
       padding: {
         left: 20,
         right: 20,
         top: 10,
         bottom: 10,
     },

      },
       legend:{},
      annotations: {},

    };
  })
 }


 groupByPharmacien(data: any[]): any[] {
  const grouped = data.reduce((acc, item) => {
      const { id, raisonSociale,nomResponsable } = item.pharmacien;
      if (!acc[id]) {
          acc[id] = {
              pharmacien: { id, raisonSociale,nomResponsable },
              montantSupportee: 0,
              montantConsomme: 0,
              countCommandeSupportee: 0,
              countCommandeConsommee: 0,
              countCmdPasse: 0,
              countClient: 0,
          };
      }
      acc[id].montantSupportee += item.montantSupportee;
      acc[id].montantConsomme += item.montantConsomme;
      acc[id].countCommandeSupportee += item.countCommandeSupportee;
      acc[id].countCommandeConsommee += item.countCommandeConsommee;
      acc[id].countCmdPasse += item.countCmdPasse;
      acc[id].countClient += item.countClient;
      return acc;
  }, {} );

  return Object.values(grouped);
}


  setFormControlValues(): void {
    const startDateValue = this.displayedStartDate$.value;
    const endDateValue = this.displayedEndDate$.value;

    this.startDate.setValue({
      year: startDateValue.getFullYear(),
      month: startDateValue.getMonth() + 1,
      day: startDateValue.getDate()
    });

    this.endDate.setValue({
      year: endDateValue.getFullYear(),
      month: endDateValue.getMonth() + 1,
      day: endDateValue.getDate()
    });
  }

  forwardOneMonth(picker: 'start' | 'end'): void {
    if (picker === 'start') {
      const newDate = new Date(this.displayedStartDate$.value);
      newDate.setMonth(newDate.getMonth() + 1);
      this.displayedStartDate$.next(newDate);
      this.setFormControlValues();
    } else if (picker === 'end') {
      const newDate = new Date(this.displayedEndDate$.value);
      newDate.setMonth(newDate.getMonth() + 1);
      this.displayedEndDate$.next(newDate);
      this.setFormControlValues();
    }
 this.updateDataAfterFilter();
  }

  backwardOneMonth(picker: 'start' | 'end'): void {
    if (picker === 'start') {
      const newDate = new Date(this.displayedStartDate$.value);
      newDate.setMonth(newDate.getMonth() - 1);
      this.displayedStartDate$.next(newDate);
      this.setFormControlValues();
    } else if (picker === 'end') {
      const newDate = new Date(this.displayedEndDate$.value);
      newDate.setMonth(newDate.getMonth() - 1);
      this.displayedEndDate$.next(newDate);
      this.setFormControlValues();
    }
    this.updateDataAfterFilter();

  }

  updateDataAfterFilter(){
    this.getMyStatistique();
  }

  constructor(private router:Router,private alertService: AlertService,private authService:AuthService ,private fsStatistiqueService: FsStatistiqueService) {
     if(this.router.url.includes('individuelle')){
      this.isIndividuelle = true;
    }
  }

  validateDatePeriod(): void {
    const startDate = this.startDate.value
      ? new Date(
          this.startDate.value.year,
          this.startDate.value.month - 1,
          this.startDate.value.day
        )
      : null;
    const endDate = this.endDate.value
      ? new Date(
          this.endDate.value.year,
          this.endDate.value.month - 1,
          this.endDate.value.day
        )
      : null;

    if (startDate && endDate && startDate > endDate) {
      this.alertService.error(
        'La date de début ne peut pas être postérieure à la date de fin',
        'MODAL'
      );
      return;
    }

    this.displayedStartDate$.next(startDate);
    this.displayedEndDate$.next(endDate);

    this.updateChartData(startDate, endDate);
  }

  updateChartData(startDate: Date, endDate: Date): void {
    if (!startDate || !endDate) {
      this.lineChartData = [];
      this.pieChartData = { labels: [], datasets: [] };
      return;
    }

    const months = this.getMonthsBetween(startDate, endDate);

    const consomméData = this.generateFakeData(months.length);
    const supportéData = this.generateFakeData(months.length);

    this.lineChartLabels = months;

    this.lineChartData = [
      {
        data: consomméData,
        label: 'Consommé',
        borderColor: 'rgba(105,159,177,1)',
        backgroundColor: 'rgba(105,159,177,0.2)',
        pointBackgroundColor: 'rgba(105,159,177,1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(105,159,177,0.8)'
      },
      {
        data: supportéData,
        label: 'Supporté',
        borderColor: 'rgba(255,206,86,1)',
        backgroundColor: 'rgba(255,206,86,0.2)',
        pointBackgroundColor: 'rgba(255,206,86,1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(255,206,86,0.8)'
      }
    ];

    const totalConsommé = consomméData.reduce((a: number, b: number) => a + b, 0);
    const totalSupporté = supportéData.reduce((a: number, b: number) => a + b, 0);

    this.pieChartData = {
      labels: ['Consommé', 'Supporté'],
      datasets: [{
        data: [totalConsommé, totalSupporté],
        backgroundColor: ['rgba(105,159,177,0.8)', 'rgba(255,206,86,0.8)'],
        hoverBackgroundColor: ['rgba(105,159,177,1)', 'rgba(255,206,86,1)'],
        borderColor: ['rgba(105,159,177,1)', 'rgba(255,206,86,1)'],
        hoverBorderColor: ['rgba(105,159,177,1)', 'rgba(255,206,86,1)']
      }]
    };
  }

  getMonthsBetween(startDate: Date, endDate: Date): string[] {
    const months = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    start.setDate(1);
    end.setDate(1);

    while (start <= end) {
      months.push(start.toLocaleString('default', { month: 'long', year: 'numeric' }));
      start.setMonth(start.getMonth() + 1);
    }

    return months;
  }

  generateFakeData(length: number): number[] {
    return Array.from({ length }, () => Math.floor(Math.random() * 100));
  }

  listenToDatePickerChanges(): void {
    this.datePicker.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(res => {
        const selectedDate = new Date(res);

        if (
          (selectedDate?.getMonth() <= this.now?.getMonth()) ||
          (selectedDate?.getFullYear() !== this.now?.getFullYear())
        ) {
          this.displayedStartDate$.next(selectedDate);
          this.updateChartData(this.displayedStartDate$.value, this.displayedEndDate$.value);
        }
      });

    this.datePicker2.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(res => {
        const selectedDate = new Date(res);

        if (
          (selectedDate?.getMonth() <= this.now?.getMonth()) ||
          (selectedDate?.getFullYear() !== this.now?.getFullYear())
        ) {
          this.displayedEndDate$.next(selectedDate);
          this.updateChartData(this.displayedStartDate$.value, this.displayedEndDate$.value);
        }
      });
  }
}
