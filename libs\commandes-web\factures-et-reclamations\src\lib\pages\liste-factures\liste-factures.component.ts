import { Component, OnInit, TemplateRef } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Grid<PERSON><PERSON>Result } from "@progress/kendo-angular-grid";
import { PageChangeEvent } from "@progress/kendo-angular-pager";
import { SortDescriptor } from "@progress/kendo-data-query";
import { CommandeService } from "@wph/commandes-web/commande";
import { FactureCriteria, Pagination } from "@wph/data-access";
import { getDynamicPageSize } from "@wph/web/shared";

@Component({
    selector: 'liste-factures',
    styleUrls: ['./liste-factures.component.scss'],
    templateUrl: './liste-factures.component.html'
})
export class ListeFacturesComponent implements OnInit {
    gridSort: SortDescriptor[];
    listeFactures: GridDataResult = { data: [], total: 0 };
    navigation: Pagination = { pageSize: 20, skip: 0 };
    pageSizes: number[] = [5, 10, 15, 20];
    factureCriteria: FactureCriteria = {};

    factureFilterForm: FormGroup | null = null;

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private modalService: NgbModal,
        private commandeService: CommandeService
    ) { }

    ngOnInit(): void {
        this.setPageSize();

        this.fetchListeFactures();

        this.factureFilterForm = this.fb.group({
            codeFacture: [null],
            dateFactureDebut: [null],
            dateFactureFin: [null]
        });
    }

    setPageSize(currentHeight?: number): void {
        const dynamicSize = getDynamicPageSize(currentHeight);

        if (dynamicSize !== this.navigation.pageSize) {
            this.navigation.pageSize = dynamicSize;

            this.pageSizes.push(dynamicSize);
            this.pageSizes = this.pageSizes.sort((a, b) => a - b);

            currentHeight && this.fetchListeFactures();
        }
    }

    fetchListeFactures(): void {
        this.commandeService.getListeFactures(this.factureCriteria, this.navigation).subscribe(res => {
            this.listeFactures = {
                data: res?.content,
                total: res?.totalElements
            };
        });
    }

    pageChange(event: PageChangeEvent): void {
        if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.navigation.pageSize = event.take;

            this.fetchListeFactures();
        }
    }

    gridSortChange(sort: SortDescriptor[]): void {
        this.gridSort = sort;

        if (this.gridSort && this.gridSort.length > 0 && this.gridSort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.fetchListeFactures();
    }

    navigateToDetailFacture(idFacture: number): void {
        this.router.navigate(['/commande-web/autres/factures', idFacture]);
    }

    openModal(content: TemplateRef<any>, size = 'md'): void {
        this.modalService.open(content, { size, centered: true });
    }

    vider(): void {
        this.factureFilterForm.reset();
        this.appliquerFiltre();
    }

    appliquerFiltre(): void {
        this.navigation = { pageSize: 20, skip: 0 };
        this.factureCriteria = this.factureFilterForm.value;

        this.fetchListeFactures(), this.modalService.dismissAll();
    }

}