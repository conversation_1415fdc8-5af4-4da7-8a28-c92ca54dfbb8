<ng-container *ngIf="mode === 'DEFAULT'; else: alt">
    <div [id]="showDefault ? 'default-news-card-container' : 'principale-news-card-container'" (click)="link && redirectToUrl(link)" class="card ml-2 my-0 mr-0 layered-element pointer-cus info-card"
        wphTrackInteraction [ngClass]="{'layered-element-height-override': overrideMaxHeight}" [title]="tooltipContent"
        [eventData]="{name: 'POST: ' + titre, type: 'Post', author: currentGrossiste?.raisonSociale ?? currentPlateforme}" #actu>
        <div class="layered-element-content h-100">
            <ng-container *ngIf="!showDefault; else: defaultTemp">
                <div class="row px-3 py-1">
                    <span *ngIf="!imgSrc" class="title col-12 p-0">{{ titre }}</span>

                    <span *ngIf="!imgSrc" class="content col-11 p-0">
                        {{ content }}
                    </span>
                </div>

                <div *ngIf="link" class="row px-3 py-1 d-flex justify-content-start"
                    style="position: absolute; bottom: 10px; left: 10px">
                    <span class="readMore pointer-cus">
                        <u>
                            <span *ngIf="imgSrc && !imgActionLabel" class="mr-1 h5 text-white">Voir la suite</span>
                            <span *ngIf="imgActionLabel" class="mr-1">{{imgActionLabel}}</span>
                            <span *ngIf="!imgSrc && !imgActionLabel" class="mr-1 h5 text-white">En savoir plus</span>
                            <i class="bi bi-box-arrow-up-right text-white" style="font-size: 14px;"></i>
                        </u>
                    </span>
                </div>

                <div *ngIf="videoUrl" class="position-relative my-2">
                    <youtube-player [showBeforeIframeApiLoads]="false" [disableCookies]="true"
                        [playerVars]="youtubePlayerVars" [videoId]="videoUrl"></youtube-player>
                </div>
            </ng-container>

            <ng-template #defaultTemp>
                <div class="h-100 w-100 row d-flex justify-content-center align-items-center">
                    <p class="text-center">
                        <span class="h1" style="color: var(--win-offre-primary);">Bienvenue à</span> <br>
                        <img *jhiHasAnyPlateforme="['DEFAULT', 'COMMANDE_WEB']" src="assets/images/pharmalien_logo_dark.svg"
                            style="height: clamp(60px, 6vw, 80px);" alt="logo_dark">
                        <img *jhiHasAnyPlateforme="['WIN_OFFRE']" src="assets/images/WinOffre+v4.svg"
                            style="height: clamp(80px, 6vw, 100px);" class="mt-2" alt="logo_dark">
                    </p>
                </div>
            </ng-template>
        </div>
    </div>
</ng-container>

<ng-template #alt>
    <div class="card shadow-sm p-1 h-100 card-radius info-card">
        <div class="row pl-3 pr-1 py-1 h-100 alt-actu-container">
            <div class="col-8">
                <div class="row">
                    <span class="societe-label col-12 p-0">{{ subtitle | titlecase }}</span>

                    <span class="title col-12 p-0">{{ titre | titlecase }}</span>

                    <span class="content col-11 p-0">{{ content }}</span>

                    <div *ngIf="link" class="col-11 p-0 w-100 d-flex justify-content-end">
                        <span class="readMore pointer-cus" (click)="redirectToUrl(link)">
                            <u>
                                <span class="mr-1">En savoir plus</span>
                                <i class="bi bi-box-arrow-up-right" style="font-size: 14px;"></i>
                            </u>
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-4 px-0 py-2 d-flex align-items-center">
                <img class="img-actu-alt" [src]="imgSrc ?? 'assets/images/article-placeholder.svg'" />
            </div>
        </div>
    </div>
</ng-template>