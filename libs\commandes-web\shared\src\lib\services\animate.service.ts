import {Injectable} from '@angular/core';

@Injectable({
    providedIn: 'root'
})
export class AnimateService {

    constructor() {
    }

    bounce(id) {
        const target = document.getElementById(id);
        target.classList.add('bounce');
        setTimeout(() => {
            target.classList.remove('bounce');
        }, 1000);
    }

    flash(id) {
        const target = document.getElementById(id);
        target.classList.add('flash');
        setTimeout(() => {
            target.classList.remove('flash');
        }, 1000);
    }

    pulse(id) {
        const target = document.getElementById(id);
        target.classList.add('pulse');
        setTimeout(() => {
            target.classList.remove('pulse');
        }, 1000);
    }

    rubberBand(id) {
        const target = document.getElementById(id);
        target.classList.add('rubberBand');
        setTimeout(() => {
            target.classList.remove('rubberBand');
        }, 1000);
    }

    shake(id) {
        const target = document.getElementById(id);
        target.classList.add('shake');
        setTimeout(() => {
            target.classList.remove('shake');
        }, 1000);
    }

    headShake(id) {
        const target = document.getElementById(id);
        target.classList.add('headShake');
        setTimeout(() => {
            target.classList.remove('headShake');
        }, 1000);
    }

    swing(id) {
        const target = document.getElementById(id);
        target.classList.add('swing');
        setTimeout(() => {
            target.classList.remove('swing');
        }, 1000);
    }

    tada(id) {
        const target = document.getElementById(id);
        target.classList.add('tada');
        setTimeout(() => {
            target.classList.remove('tada');
        }, 1000);
    }

    wobble(id) {
        const target = document.getElementById(id);
        target.classList.add('wobble');
        setTimeout(() => {
            target.classList.remove('wobble');
        }, 1000);
    }

    jello(id) {
        const target = document.getElementById(id);
        target.classList.add('jello');
        setTimeout(() => {
            target.classList.remove('jello');
        }, 1000);
    }

    bounceIn(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceIn');
        setTimeout(() => {
            target.classList.remove('bounceIn');
        }, 1000);
    }

    bounceInDown(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceInDown');
        setTimeout(() => {
            target.classList.remove('bounceInDown');
        }, 1000);
    }

    bounceInLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceInLeft');
        setTimeout(() => {
            target.classList.remove('bounceInLeft');
        }, 1000);
    }

    bounceInRight(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceInRight');
        setTimeout(() => {
            target.classList.remove('bounceInRight');
        }, 1000);
    }

    bounceInUp(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceInUp');
        setTimeout(() => {
            target.classList.remove('bounceInUp');
        }, 1000);
    }

    bounceOut(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceOut');
        setTimeout(() => {
            target.classList.remove('bounceOut');
        }, 1000);
    }

    bounceOutDown(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceOutDown');
        setTimeout(() => {
            target.classList.remove('bounceOutDown');
        }, 1000);
    }

    bounceOutLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceOutLeft');
        setTimeout(() => {
            target.classList.remove('bounceOutLeft');
        }, 1000);
    }

    bounceOutRight(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceOutRight');
        setTimeout(() => {
            target.classList.remove('bounceOutRight');
        }, 1000);
    }

    bounceOutUp(id) {
        const target = document.getElementById(id);
        target.classList.add('bounceOutUp');
        setTimeout(() => {
            target.classList.remove('bounceOutUp');
        }, 1000);
    }

    fadeIn(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeIn');
        setTimeout(() => {
            target.classList.remove('fadeIn');
        }, 1000);
    }

    fadeInDown(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeInDown');
        setTimeout(() => {
            target.classList.remove('fadeInDown');
        }, 1000);
    }

    fadeInDownBig(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeInDownBig');
        setTimeout(() => {
            target.classList.remove('fadeInDownBig');
        }, 1000);
    }

    fadeInLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeInLeft');
        setTimeout(() => {
            target.classList.remove('fadeInLeft');
        }, 1000);
    }

    fadeInLeftBig(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeInLeftBig');
        setTimeout(() => {
            target.classList.remove('fadeInLeftBig');
        }, 1000);
    }

    fadeInRight(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeInRight');
        setTimeout(() => {
            target.classList.remove('fadeInRight');
        }, 1000);
    }

    fadeInRightBig(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeInRightBig');
        setTimeout(() => {
            target.classList.remove('fadeInRightBig');
        }, 1000);
    }

    fadeInUp(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeInUp');
        setTimeout(() => {
            target.classList.remove('fadeInUp');
        }, 1000);
    }

    fadeInUpBig(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeInUpBig');
        setTimeout(() => {
            target.classList.remove('fadeInUpBig');
        }, 1000);
    }

    fadeOut(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeOut');
        setTimeout(() => {
            target.classList.remove('fadeOut');
        }, 1000);
    }

    fadeOutDown(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeOutDown');
        setTimeout(() => {
            target.classList.remove('fadeOutDown');
        }, 1000);
    }

    fadeOutDownBig(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeOutDownBig');
        setTimeout(() => {
            target.classList.remove('fadeOutDownBig');
        }, 1000);
    }

    fadeOutLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeOutLeft');
        setTimeout(() => {
            target.classList.remove('fadeOutLeft');
        }, 1000);
    }

    fadeOutLeftBig(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeOutLeftBig');
        setTimeout(() => {
            target.classList.remove('fadeOutLeftBig');
        }, 1000);
    }

    fadeOutRight(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeOutRight');
        setTimeout(() => {
            target.classList.remove('fadeOutRight');
        }, 1000);
    }

    fadeOutRightBig(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeOutRightBig');
        setTimeout(() => {
            target.classList.remove('fadeOutRightBig');
        }, 1000);
    }

    fadeOutUp(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeOutUp');
        setTimeout(() => {
            target.classList.remove('fadeOutUp');
        }, 1000);
    }

    fadeOutUpBig(id) {
        const target = document.getElementById(id);
        target.classList.add('fadeOutUpBig');
        setTimeout(() => {
            target.classList.remove('fadeOutUpBig');
        }, 1000);
    }

    flipInX(id) {
        const target = document.getElementById(id);
        target.classList.add('flipInX');
        setTimeout(() => {
            target.classList.remove('flipInX');
        }, 1000);
    }

    flipInY(id) {
        const target = document.getElementById(id);
        target.classList.add('flipInY');
        setTimeout(() => {
            target.classList.remove('flipInY');
        }, 1000);
    }

    flipOutX(id) {
        const target = document.getElementById(id);
        target.classList.add('flipOutX');
        setTimeout(() => {
            target.classList.remove('flipOutX');
        }, 1000);
    }

    flipOutY(id) {
        const target = document.getElementById(id);
        target.classList.add('flipOutY');
        setTimeout(() => {
            target.classList.remove('flipOutY');
        }, 1000);
    }

    lightSpeedIn(id) {
        const target = document.getElementById(id);
        target.classList.add('lightSpeedIn');
        setTimeout(() => {
            target.classList.remove('lightSpeedIn');
        }, 1000);
    }

    lightSpeedOut(id) {
        const target = document.getElementById(id);
        target.classList.add('lightSpeedOut');
        setTimeout(() => {
            target.classList.remove('lightSpeedOut');
        }, 1000);
    }

    rotateIn(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateIn');
        setTimeout(() => {
            target.classList.remove('rotateIn');
        }, 1000);
    }

    rotateInDownLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateInDownLeft');
        setTimeout(() => {
            target.classList.remove('rotateInDownLeft');
        }, 1000);
    }

    rotateInDownRight(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateInDownRight');
        setTimeout(() => {
            target.classList.remove('rotateInDownRight');
        }, 1000);
    }

    rotateInUpLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateInUpLeft');
        setTimeout(() => {
            target.classList.remove('rotateInUpLeft');
        }, 1000);
    }

    rotateInUpRight(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateInUpRight');
        setTimeout(() => {
            target.classList.remove('rotateInUpRight');
        }, 1000);
    }

    rotateOut(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateOut');
        setTimeout(() => {
            target.classList.remove('rotateOut');
        }, 1000);
    }

    rotateOutDownLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateOutDownLeft');
        setTimeout(() => {
            target.classList.remove('rotateOutDownLeft');
        }, 1000);
    }

    rotateOutDownRight(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateOutDownRight');
        setTimeout(() => {
            target.classList.remove('rotateOutDownRight');
        }, 1000);
    }

    rotateOutUpLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateOutUpLeft');
        setTimeout(() => {
            target.classList.remove('rotateOutUpLeft');
        }, 1000);
    }

    rotateOutUpRight(id) {
        const target = document.getElementById(id);
        target.classList.add('rotateOutUpRight');
        setTimeout(() => {
            target.classList.remove('rotateOutUpRight');
        }, 1000);
    }

    hinge(id) {
        const target = document.getElementById(id);
        target.classList.add('hinge');
        setTimeout(() => {
            target.classList.remove('hinge');
        }, 1000);
    }

    jackInTheBox(id) {
        const target = document.getElementById(id);
        target.classList.add('jackInTheBox');
        setTimeout(() => {
            target.classList.remove('jackInTheBox');
        }, 1000);
    }

    rollIn(id) {
        const target = document.getElementById(id);
        target.classList.add('rollIn');
        setTimeout(() => {
            target.classList.remove('rollIn');
        }, 1000);
    }

    rollOut(id) {
        const target = document.getElementById(id);
        target.classList.add('rollOut');
        setTimeout(() => {
            target.classList.remove('rollOut');
        }, 1000);
    }

    zoomIn(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomIn');
        setTimeout(() => {
            target.classList.remove('zoomIn');
        }, 1000);
    }

    zoomInDown(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomInDown');
        setTimeout(() => {
            target.classList.remove('zoomInDown');
        }, 1000);
    }

    zoomInLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomInLeft');
        setTimeout(() => {
            target.classList.remove('zoomInLeft');
        }, 1000);
    }

    zoomInRight(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomInRight');
        setTimeout(() => {
            target.classList.remove('zoomInRight');
        }, 1000);
    }

    zoomInUp(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomInUp');
        setTimeout(() => {
            target.classList.remove('zoomInUp');
        }, 1000);
    }

    zoomOut(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomOut');
        setTimeout(() => {
            target.classList.remove('zoomOut');
        }, 1000);
    }

    zoomOutDown(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomOutDown');
        setTimeout(() => {
            target.classList.remove('zoomOutDown');
        }, 1000);
    }

    zoomOutLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomOutLeft');
        setTimeout(() => {
            target.classList.remove('zoomOutLeft');
        }, 1000);
    }

    zoomOutRight(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomOutRight');
        setTimeout(() => {
            target.classList.remove('zoomOutRight');
        }, 1000);
    }

    zoomOutUp(id) {
        const target = document.getElementById(id);
        target.classList.add('zoomOutUp');
        setTimeout(() => {
            target.classList.remove('zoomOutUp');
        }, 1000);
    }

    slideInDown(id) {
        const target = document.getElementById(id);
        target.classList.add('slideInDown');
        setTimeout(() => {
            target.classList.remove('slideInDown');
        }, 1000);
    }

    slideInLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('slideInLeft');
        setTimeout(() => {
            target.classList.remove('slideInLeft');
        }, 1000);
    }

    slideInRight(id) {
        const target = document.getElementById(id);
        target.classList.add('slideInRight');
        setTimeout(() => {
            target.classList.remove('slideInRight');
        }, 1000);
    }

    slideInUp(id) {
        const target = document.getElementById(id);
        target.classList.add('slideInUp');
        setTimeout(() => {
            target.classList.remove('slideInUp');
        }, 1000);
    }

    slideOutDown(id) {
        const target = document.getElementById(id);
        target.classList.add('slideOutDown');
        setTimeout(() => {
            target.classList.remove('slideOutDown');
        }, 1000);
    }

    slideOutLeft(id) {
        const target = document.getElementById(id);
        target.classList.add('slideOutLeft');
        setTimeout(() => {
            target.classList.remove('slideOutLeft');
        }, 1000);
    }

    slideOutRight(id) {
        const target = document.getElementById(id);
        target.classList.add('slideOutRight');
        setTimeout(() => {
            target.classList.remove('slideOutRight');
        }, 1000);
    }

    slideOutUp(id) {
        const target = document.getElementById(id);
        target.classList.add('slideOutUp');
        setTimeout(() => {
            target.classList.remove('slideOutUp');
        }, 1000);
    }

    heartBeat(id) {
        const target = document.getElementById(id);
        target.classList.add('heartBeat');
        setTimeout(() => {
            target.classList.remove('heartBeat');
        }, 1000);
    }

}
