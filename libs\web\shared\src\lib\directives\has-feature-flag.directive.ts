import { Directive, TemplateRef, ViewContainerRef, Input } from "@angular/core";
import { AuthService } from "@wph/core/auth";
import { CanAccessFeatureConfig, FEATURE_KEY_STORAGE } from "@wph/data-access";

@Directive({
    selector: '[jhiHasFeatureFlag]',
    standalone: true
})
export class HasFeatureFlagDirective {
    constructor(
        private authService: AuthService,
        private templateRef: TemplateRef<any>,
        private viewContainerRef: ViewContainerRef
    ) { }

    /**
     * ### @Parameters
     * value.feature: Feature flag key
     * 
     * value.roles: Target user role / roles (array of strings)
     * 
     * value.canAccess: Optional access condition
     */
    @Input() set jhiHasFeatureFlag(value: CanAccessFeatureConfig) {
        this.viewContainerRef.clear();

        if (!value || !value.feature) {
            console.warn(`[HasFeatureFlagDirective] Invalid directive input:`, value);
            return;
        }

        const { feature, roles, canAccess } = value;

        if (!this.authService.canAccessFeature(feature, roles, canAccess, [FEATURE_KEY_STORAGE.ROOT, FEATURE_KEY_STORAGE.GROUP])) return;

        this.viewContainerRef.createEmbeddedView(this.templateRef);
    }
}
