import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { PlateformeSelectionComponent } from "@wph/web/auth";
import { AuthoritiesGuard } from "@wph/web/shared";

const routes: Routes = [
    {
        path: '',
        pathMatch: 'full',
        title: 'Choix de Plateforme',
        component: PlateformeSelectionComponent,
    },
    {
        path: 'account',
        loadChildren: () => import('@wph/web/account/pages')
            .then(m => m.WebAccountPagesModule)
    },
    {
        path: 'pharmacies',
        loadChildren: () => import('@wph/web/pharmacies')
            .then(m => m.WebPharmaciesModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_AGENT_FOURNISSEUR', 'ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'users',
        loadChildren: () => import('@wph/web/users/pages')
            .then(m => m.WebUsersPagesModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_POINT_VENTE'] }
    },
    {
        path: 'gestion-acces',
        loadChildren: () => import('@wph/web/gestion-acces-client')
            .then(m => m.WebGestionAccesClientModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'gestion-poste',
        loadChildren: () => import('@wph/web/gestion-annonces')
            .then(m => m.WebGestionAnnoncesModule)
    },
    {
        path: 'demandes',
        loadChildren: () => import('@wph/web/demandes-incriptions/pages')
            .then(m => m.WebDemandesIncriptionsPagesModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'services-client',
        loadChildren: () => import('@wph/web/gestion-services')
            .then(m => m.WebGestionServicesModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'parametres',
        loadChildren: () => import('@wph/web/gestion-parametres')
            .then(m => m.WebGestionParametresModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'flux-maj',
        loadChildren: () => import('@wph/web/gestion-flux-maj')
            .then(m => m.WebGestionFluxMajModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'autres/guides',
        loadChildren: () => import('@wph/web/guide-pharmalien').then(m => m.WebGuidePharmalienModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_AGENT_FOURNISSEUR'] }
    },
    {
        path: 'dashboard',
        loadChildren: () => import('@wph/web/pharmalien-dashboard')
            .then(m => m.WebPharmalienDashboardModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'gestion-demandes-acces',
        loadChildren: () => import('@wph/web/gestion-demandes-access').then(m => m.WebGestionDemandesAccessModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR'] }
    },
    {
        path: 'gestion-analytics',
        loadChildren: () => import('@wph/web/ga4-analytics-pharmalien').then(m => m.WebGa4AnalyticsPharmalienModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'gestion-domaine',
        loadChildren: () => import('../../../../../../libs/web/gestion-domaine/domaine.module')
            .then(m => m.DomaineModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
      path: 'auth-log',
      loadChildren: () => import('../../../../../../libs/web/auth-log/auth-log.module').then(m => m.AuthLogModule),
      canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }

    },
    {
        path: 'batch-admin',
        loadChildren: () => import('../../../../../../libs/web/admin-batch/admin-batch.module')
            .then(m => m.AdminBatchModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    // ? WILD CARD
    {
        path: '**',
        redirectTo: ''
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class PharmaHubRoutingModule { }
