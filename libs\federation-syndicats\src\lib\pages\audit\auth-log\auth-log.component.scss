
label {
  color: var(--winoffre-text-light-shade);
  font-weight: 700;
}


.picker-input {
  .form-control {
    border-radius: var(--winoffre-base-border-radius) !important;
  }
}

.b-radius {
  border-radius: var(--winoffre-base-border-radius) !important;
}

.card-header {
  .btn {
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--winoffre-base-border-radius) !important;
  }
}

.btn {
  font-size: 1rem;
  font-weight: 600;
}

.input-group {
  .form-control {
    color: rgba(0, 0, 0, 0.432) !important;
    font-weight: 700;
      border-top-left-radius: var(--winoffre-base-border-radius);
      border-bottom-left-radius: var(--winoffre-base-border-radius);

  }
  .btn {
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-right-radius: var(--winoffre-base-border-radius);
  }

}



.btn-success{
background: var(--fs-success) !important;
border-color: var(--fs-success) !important;
}

.btn-danger{
background: var(--fs-danger) !important;
border-color: var(--fs-danger) !important;
}



