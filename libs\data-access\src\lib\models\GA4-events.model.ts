
type InteractionEvent = 'content_click' | 'content_view' | 'content_hover';

export enum GA4_EVENT {
    RECHERCHE_PRODUIT = 'recherche_produit',
    CONTENT_INTERACTION = 'content_interaction',
    PLATFORM_SWITCH = 'platform_portal_switch',
}

export enum GA4_DIMENSION {
    CONTENT_NAME = 'customEvent:content_name',
    CONTENT_AUTHOR = 'customEvent:content_author',
    CONTENT_INTERACTION_TYPE = 'customEvent:content_interaction_type',
    CONTENT_INTERACTION_PLATFORM = 'customEvent:content_platform_portal',
    CONTENT_INTERACTION_USER_ID = 'customEvent:user_id',
}

export enum INTERACTION {
    CLICK = 'content_click',
    VIEW = 'content_view',
    HOVER = 'content_hover',
    TOUCH = 'content_touch',
}

export class ContentInteractionEvent {
    name?: string;
    author?: string;
    type?: string;
    // owner_id?: number;
    user_id?: string;
    eventName?: string;
    platform_portal?: string;
    interaction_type?: InteractionEvent;

    constructor(partialContentInteraction?: Partial<ContentInteractionEvent>) {
        this.name = partialContentInteraction?.name || null;
        this.author = partialContentInteraction?.author || null;
        this.type = partialContentInteraction?.type || null;
        this.user_id = partialContentInteraction?.user_id || null;
        // this.owner_id = partialContentInteraction?.owner_id || null;
        this.eventName = partialContentInteraction?.eventName || null;
        this.platform_portal = partialContentInteraction?.platform_portal || null;
        this.interaction_type = partialContentInteraction?.interaction_type || null;
    }
}

export class EventData {
    label?: string;
    value?: number;
    action?: string;
    category?: string;
    eventName?: string;
    [key: string]: any; // Allow any additional properties

    constructor(partialEventData?: Partial<EventData>) {
        this.label = partialEventData?.label || null;
        this.value = partialEventData?.value || null;
        this.action = partialEventData?.action || null;
        this.category = partialEventData?.category || null;
        this.eventName = partialEventData?.eventName || null;
        Object.assign(this, partialEventData); // Assign any additional properties
    }
}