/* Reset button styles */
button {
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
  outline: inherit;
}

/* Switch icons */
.status-switch-align.k-switch-on .k-switch-thumb {
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-switch-align.k-switch-on .k-switch-thumb::before {
  content: "\F26E";
  font-family: "Bootstrap-icons";
  color: var(--fs-success);
  font-size: 20px;
}

.status-switch-align .k-switch-thumb {
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-switch-align .k-switch-thumb::before {
  content: "\F26E";
  font-family: "Bootstrap-icons";
  color: #DCB544;
  font-size: 20px;
}

.status-switch-align.fs-soundage-switch.k-switch-md {
  width: 110px;
}

.status-switch-align.fs-soundage-switch.k-switch-md .k-switch-track {
  width: 150px !important;
}

.k-switch-off .k-switch-track {
  border-color: #DCB544;
  color: white;
  background-color: #DCB544;
}

/* Container and list styles */
.sondages-container {
  display: flex;
  flex-direction: column;
  //height: calc(100vh - 80px); /* Ensure full height of viewport */
}

.sondages-list {
  flex: 1;
  overflow-y: auto; /* Allow vertical scrolling */
  padding: 1rem;
  margin-bottom: 1rem;
}

.sondage-item {
  margin-bottom: 1rem;
  padding: 1rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/* Ensure card container is displayed correctly */
.card {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Ensure table container takes full width */
.table-container {
  max-height:  100% !important;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* Ensure grid takes available height */
.k-grid {
  flex: 1;
  overflow-y: auto;
}
