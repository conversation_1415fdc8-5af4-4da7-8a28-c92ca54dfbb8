import { NgModule } from "@angular/core";
import { StatistiquesRoutingModule } from "./statistiques-routing.module";
import { WebSharedModule } from "@wph/web/shared";
import { SharedModule } from "@wph/shared";
import { CommonModule } from "@angular/common";
import { GroupesStatistiquesComponent } from "./groupes-statistiques/groupes-statistiques.component";
import { MesStatistiquesComponent } from "./mes-statistiques/mes-statistiques.component";
import { MembresStatistiquesComponent } from "./members-statistique/members-statistiques.component";
import { NgApexchartsModule } from "ng-apexcharts";
import { LaboStatistiquesComponent } from "./labo-statistiques/labo-statistiques.component";

@NgModule({
    declarations: [
        GroupesStatistiquesComponent,
        MembresStatistiquesComponent,
        MesStatistiquesComponent,
        LaboStatistiquesComponent
      ],
    imports: [StatistiquesRoutingModule, WebSharedModule, SharedModule, CommonModule,NgApexchartsModule]

})
export class StatistiquesModule {}
