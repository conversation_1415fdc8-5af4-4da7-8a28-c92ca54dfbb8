import { Role, Societe } from "@wph/shared";
import { EntrepriseDTO } from "libs/federation-syndicats/src/lib/models/entreprise.model";

export interface User{
  id:number;
  actif?: boolean;
  audited?: boolean;
  email?: string;
  gsm?: string;
  firstname?: string;
  societe?:Societe,
  idhash?: string;
  lastname?: string;
  password?: string;
  userModifiable?: boolean;
  username?: string;
  emailVerified?: boolean;
  enabled?: boolean;
  role?: Role;
  entrepriseDTO?: EntrepriseDTO;
}

export type UserAbregeDto  = Pick<User, 'id' | 'firstname' | 'lastname' | 'entrepriseDTO' | 'username' | 'audited'>;
