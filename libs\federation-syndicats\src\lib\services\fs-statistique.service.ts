import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
 import { Observable } from 'rxjs';
import { StatisticsCriteriaDTO, StatistiqueDTO } from '../models/statistique.model';

@Injectable({
  providedIn: 'root'
})


export class FsStatistiqueService {

  constructor(
    @Inject('ENVIROMENT') private env: any,
    private http:HttpClient) { }

    getStatictiques(statistiqueCriteria: StatisticsCriteriaDTO,isIndividuelle:boolean=false) {
      if(isIndividuelle){
        return this.http.post<StatistiqueDTO[]>(`${this.env.base_url}/api/v1/stats-achat-groupe/individuel`,statistiqueCriteria,{ observe: 'body' });
      }
      return this.http.post<StatistiqueDTO[]>(`${this.env.base_url}/api/v1/stats-achat-groupe`,statistiqueCriteria,{ observe: 'body' });
    }


    getStatictiquesExecl(statistiqueCriteria: StatisticsCriteriaDTO) {

      return this.http.post(`${this.env.base_url}/api/v1/stats-achat-groupe/excel`,statistiqueCriteria,{   responseType: 'blob',
        observe: 'response' });
    }


}
