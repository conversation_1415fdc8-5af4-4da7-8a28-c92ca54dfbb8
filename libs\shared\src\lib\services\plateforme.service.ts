import { Injectable } from "@angular/core";
import { AccesClient, Societe } from "@wph/shared";
import { CodePlateforme, SelectedPlateforme } from "@wph/web/layout";
import { BehaviorSubject } from "rxjs";

@Injectable({ providedIn: 'root' })
export class PlateformeService {
    // Plateforme observables
    private plateformeChange: BehaviorSubject<SelectedPlateforme> = new BehaviorSubject<SelectedPlateforme>(null);
    public currentPlateforme$ = this.plateformeChange.asObservable();
    // Selected grossiste observables
    private grossisteChange: BehaviorSubject<Societe> = new BehaviorSubject<Societe>(null);
    public currentGrossiste$ = this.grossisteChange.asObservable();

    constructor() { }

    setCurrentGrossiste(grossiste: Societe): void {
        this.grossisteChange.next(grossiste);
        sessionStorage.setItem('TARGET_GROSSISTE', JSON.stringify(grossiste));
    }

    setCurrentPlateforme(plateforme: SelectedPlateforme): void {
        this.plateformeChange.next(plateforme);
        sessionStorage.setItem('TARGET_PLATEFORME', plateforme);
    }

    clearPlateformeKeys(force = false): void {
        this.grossisteChange.next(null);

        sessionStorage.removeItem('TARGET_GROSSISTE');

        if (force || (!this.isPlateForme('FEDERATION_SYNDICAT') && !this.isPlateForme('WIN_GROUPE'))) {
            this.plateformeChange.next('DEFAULT');
            sessionStorage.removeItem('TARGET_PLATEFORME');
        }
    }

    getCurrentPlateforme(): SelectedPlateforme {

        if (this.plateformeChange.value) {
            return this.plateformeChange.value;
        } else {
            const targetPlateforme = sessionStorage.getItem('TARGET_PLATEFORME') as SelectedPlateforme;
            this.plateformeChange.next(targetPlateforme ?? 'DEFAULT');

            return targetPlateforme;
        }
    }

    getCodeOfCurrentPlateforme(): string {
        const currentPlateforme = this.getCurrentPlateforme();

        switch (currentPlateforme) {
            case "WIN_OFFRE":
                return 'WO';
            case "COMMANDE_WEB":
                return 'CW';
            case 'WIN_GROUPE':
                return 'WF';
            case "FEDERATION_SYNDICAT":
                return 'FS';
            case "DEFAULT":
                return '';
        }
    }

    getPlateformePrefix(plateforme?: SelectedPlateforme): string {
        const currentPlateforme = plateforme || this.getCurrentPlateforme();
        switch (currentPlateforme) {
            case "WIN_OFFRE":
                return 'win-offre';
            case "COMMANDE_WEB":
                return 'commande-web';
            case 'WIN_GROUPE':
            case "FEDERATION_SYNDICAT":
                return 'achats-groupes';
            case "DEFAULT":
                return 'pharma-lien';
        }
    }

    getPlateformeName(plateforme?: SelectedPlateforme): string {
        const currentPlateforme = plateforme || this.getCurrentPlateforme();
        switch (currentPlateforme) {
            case "WIN_OFFRE":
                return 'WinPlus Offre';
            case "COMMANDE_WEB":
                return 'Commande Web';
            case 'WIN_GROUPE':
                return 'WinPlus Groupe';
            case "FEDERATION_SYNDICAT":
                return 'La Centrale Pharma';
            case "DEFAULT":
                return 'PharmaLien';
        }
    }

    getCurrentGrossiste(): Societe {
        if (this.grossisteChange.value) {
            return this.grossisteChange.value;
        } else {
            const currentGrossiste = JSON.parse(sessionStorage.getItem('TARGET_GROSSISTE'));
            this.grossisteChange.next(currentGrossiste);

            return currentGrossiste;
        }
    }

    isPlateForme(plateforme: SelectedPlateforme): boolean {
        return this.getCurrentPlateforme() === plateforme;
    }

    hasServiceWithCode(codeService: CodePlateforme, accesClient: AccesClient): boolean {
        const listeServices = accesClient?.listeServices;
        // const isDeactivated = !!accesClient?.dateDesactivation;

        return !!listeServices?.find(service =>
            (service?.serviceClient?.codeService === codeService) && service?.statut
        );
    }

}