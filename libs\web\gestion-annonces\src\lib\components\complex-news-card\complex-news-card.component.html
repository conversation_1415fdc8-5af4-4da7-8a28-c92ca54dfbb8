<div (click)="redirectToUrl()" class="card shadow-sm blog-post-card" [ngClass]="{'pointer-cus': post?.url || post?.imageUrl}" *ngIf="post">
  <!-- Header with author info and branding -->
  <div class="card-header post-header d-flex justify-content-between align-items-center bg-transparent">
    <div class="d-flex align-items-center">
      <img src="assets/images/pharmalien_logo_only_dark.svg" height="30" width="30">
    </div>

    <div class="post-actions">
      <button class="btn share-button d-flex align-items-center" [class.sharing]="isSharing"
        [disabled]="isSharing || !post?.url && !post?.imageUrl && !post?.videoUrl"
        [class.disabled]="isSharing" (click)="sharePost(); $event.stopPropagation()" title="Partager la publication">
        <i class="mdi mdi-share mdi-16px mr-0 mr-sm-1" [class.mdi-loading]="isSharing"
          [class.mdi-share-variant]="!isSharing"></i>
        <span class="share-text d-none d-sm-inline">{{ isSharing ? 'Partage...' : 'Partager' }}</span>
      </button>
    </div>
  </div>

  <!-- Post title -->
  <div class="post-title px-3 pt-2" *ngIf="post?.titre && !post?.imageUrl && !post?.videoUrl">
    <h5 class="card-title fw-semibold truncate-two-lines">{{ post?.titre }}</h5>
  </div>

  <!-- Post content (text) -->
  <div class="post-content px-3 mb-1" *ngIf="post?.sujet">
    <div class="text-content overflow-hidden">
      <p class="card-text mb-2" [innerHTML]="truncatedText"></p>
    </div>

    <button *ngIf="shouldShowReadMore || shouldShowReadLess"
      class="btn btn-link read-more-button p-0 text-decoration-none" (click)="toggleTextExpansion()">
      {{ isTextExpanded ? 'Voir moins' : 'Voir plus' }}
    </button>
  </div>

  <!-- Media content with Swiper integration -->
  <div class="media-container position-relative" *ngIf="hasAnyContent">
    <div id="pharmalien-swiper" class="media-wrapper position-relative w-100">
      <swiper-container class="w-100 swiper-news-card" *ngIf="post?.contentItems?.length; else: singleContent"
        speed="500" autoplay="true" loop="false" navigation="true" pagination="true" scrollbar="false"
        slidesPerView="1" centeredSlides="true" (pointerenter)="toggleSwiperAutoplay()"
        (pointerleave)="toggleSwiperAutoplay()" #swiperRef>
        <swiper-slide *ngFor="let content of post?.contentItems">
          <!-- Image content -->
          <div *ngIf="isImageContent(content)" class="image-content position-relative">
            <img [src]="content?.url" [alt]="content.caption || 'Image de la publication'"
              (error)="onImageError($event)" class="post-image w-100 img-fluid">
            <div *ngIf="content.caption" class="media-caption position-absolute bottom-0 start-0 end-0 text-white p-3">
              <small>{{ content.caption }}</small>
            </div>
          </div>

          <!-- Video content -->
          <div *ngIf="isVideoContent(content)" class="video-content position-relative">
            <youtube-player [showBeforeIframeApiLoads]="false" [disableCookies]="true" [videoId]="post?.videoUrl"
            [playerVars]="youtubePlayerVars" class="w-100 youtube-player"></youtube-player>
            <!-- <video [src]="content?.url" controls preload="metadata" (error)="onVideoError($event)"
              class="post-video w-100">
              Votre navigateur ne supporte pas la lecture de vidéos.
            </video> -->
          </div>
        </swiper-slide>

        <swiper-button-prev class="nav-button nav-button-prev" title="Diapositive précédente"></swiper-button-prev>
        <swiper-button-next class="nav-button nav-button-next" title="Diapositive suivante"></swiper-button-next>
      </swiper-container>

      <!-- Single content template -->
      <ng-template #singleContent>
        <div *ngIf="post?.imageUrl" class="image-content position-relative">
          <img [src]="post?.imageUrl" [alt]="post?.titre || 'Image de la publication'" (error)="onImageError($event)"
            class="post-image w-100 img-fluid">
        </div>

        <div *ngIf="post?.videoUrl" class="video-content position-relative">
          <youtube-player [showBeforeIframeApiLoads]="false" [disableCookies]="true" [videoId]="post?.videoUrl"
            [playerVars]="youtubePlayerVars" class="w-100 youtube-player"></youtube-player>
        </div>
      </ng-template>
    </div>
  </div>

  <!-- Card footer for author organization, publication date -->
  <div class="card-footer media-caption px-3 py-3 d-flex align-items-center">
    <i class="bi bi-building mr-1" style="font-size: 1.1rem;"></i> <span>{{ getCompanyName() }}, {{ post?.dateDebutVisibilite | timeAgo : 'Africa/Casablanca' : false }}</span>
  </div>
</div>