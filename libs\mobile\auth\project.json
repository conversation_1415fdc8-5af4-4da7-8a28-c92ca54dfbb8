{"name": "mobile-auth", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/mobile/auth/src", "prefix": "wph", "targets": {"test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/mobile/auth"], "options": {"jestConfig": "libs/mobile/auth/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["libs/mobile/auth/**/*.ts", "libs/mobile/auth/**/*.html"]}}}, "tags": []}