.bg-message-cmd-alt {
  position: absolute;
  bottom: 0;
  width: 100%;
}

.text-sub {
  color: #6b727d;
}


.p-badge {
  padding-block: 13px !important;
}

.text-success-alt {
  color: #0f8fa3 !important;
}

.btn-close {
  background-color: #EAEDF5;
  color: #2C3E50;
  border-radius: 50%;
  height: 45px;
  width: 45px;
  border: none;
}

.bg-success-alt {
  color: #fff;
  background: #0f8fa3 !important;
}

.rows-divider> :not(:nth-last-child(-n+2)) {
  border-bottom: 1px solid #d8e1ef;
}

::ng-deep .fs-cstm-modal .modal-content {
  border-radius: 10px;
}

.table-container {
  max-height: 300px;
  overflow-y: auto;

  scrollbar-width: thin !important;
  scrollbar-color: var(--fs-secondary-light) white !important;
  border-radius: var(--winoffre-base-border-radius)
}

.table-row {
  display: flex;
  margin-bottom: 8px;
  cursor: pointer;
  background: var(--fs-member-bg);
  border-radius: 10px;
  align-items: center;
}

.selected-member {
  background: var(--fs-group-bg);

  .table-cell {
    color: #fff;
  }
}

.modal-body textarea {
  -webkit-appearance: none;
  /* Remove default styling on Webkit browsers */
  -moz-appearance: none;
  /* Remove default styling on Firefox */
  appearance: none;
  background-color: #fff;
  display: inline-block;
  position: relative;
}

.satisfaction-option {
  margin-bottom: 20px;
  border-radius: 6px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #696C75;
}

.satisfaction-textarea {
  -webkit-appearance: none;
  /* Remove default styling on Webkit browsers */
  -moz-appearance: none;
  /* Remove default styling on Firefox */
  appearance: none;
  background-color: #fff;
  display: inline-block;
  position: relative;
}

.form-check-input {
  height: 28px;
  width: 28px;
  -webkit-appearance: none;
  /* Remove default styling on Webkit browsers */
  -moz-appearance: none;
  /* Remove default styling on Firefox */
  appearance: none;
  background-color: #fff;
  display: inline-block;
  position: relative;
  border-radius: 50%;
  /* Change to circular shape for radio buttons */
  margin-right: 10px;
}

#WIN_GROUPE-container {
  .table-row {
    background: var(--wf-primary-50) !important;
  }

  .selected-member {
    background: var(--wf-primary-400) !important;
  }

  .form-check-input {
    border: 2px solid var(--wf-primary-300) !important;
  }

  .form-check-input:checked {
    border-color: var(--wf-primary-300) !important;
    background-color: var(--wf-primary-300) !important;
  }

  textarea.form-control {
    border: 2px solid var(--wf-primary-300) !important;
  }

  .satisfaction-textarea {
    border: 2px solid var(--wf-primary-400) !important;
  }
}

#FEDERATION_SYNDICAT-container {
  .table-row {
    background: var(--fs-secondary-tint-2) !important;
  }

  .selected-member {
    background: var(--fs-grid-primary) !important;
  }

  .form-check-input {
    border: 2px solid var(--fs-primary-300) !important;
  }

  .form-check-input:checked {
    border-color: var(--fs-primary-300) !important;
    background-color: var(--fs-primary-300) !important;
  }

  textarea.form-control {
    border: 2px solid var(--fs-primary-300) !important;
  }

  .satisfaction-textarea {
    border: 2px solid var(--fs-primary-400) !important;
  }
}


.table-cell {
  display: flex;
  align-items: center;

  flex: 1;
  padding: 5px 10px;

  color: black;
  font-weight: 600;
  width: 170px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-cell-alt {
  display: flex;
  align-items: center;

  padding: 5px 10px;
  width: 50px;
}

.table-cell:last-child {
  border-bottom: none;
}

.modal-body {
  .form-control {
    font-size: 1rem;
    color: black;
    font-weight: 600;

    border-radius: 10px;
  }

  .btn {
    font-size: 1rem;
    font-weight: 600;
    border-radius: 10px;
  }
}

.btn-close {
  background-color: #EAEDF5;
  color: #2C3E50;
  border-radius: 50%;
  height: 45px;
  width: 45px;
  border: none;
}

.form-check-label {
  font-size: 1rem;
  color: #000 !important;
  font-weight: 600 !important;
}

.form-check-input:checked::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  /* Inner circle to show selected state */
  background-color: #fff;
  transform: translate(-50%, -50%);
}

::ng-deep .custom-rating .ngb-rating .bi {
  font-size: 20px !important;
}

::ng-deep .custom-rating .ngb-rating .bi.bi-star-fill {
  color: #EAB308 !important;
}

.satisfaction-option i {
  font-size: 24px;
}

.satisfaction-option label {
  font-size: 16px;

}

.email-form-labo .form-control {
  color: black;
  font-size: 1rem;
  border-radius: var(--winoffre-base-border-radius);
  font-weight: 600;
}


.email-form-labo .input-group {
  .btn {
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-right-radius: var(--winoffre-base-border-radius);
  }
}

.email-form-labo label {
  color: black;
  font-weight: 600;
}

.email-form-labo .picker-input {
  .form-control {
    border-radius: var(--winoffre-base-border-radius) !important;
  }
}