<div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Filtrer</h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="dismiss()">
        <i class="mdi mdi-close"></i>
    </button>
</div>

<form [formGroup]="filterForm" (ngSubmit)="applyFilter()" wphFocusTrap>
    <div class="p-2">
        <div class="form-group">
            <div class="row p-1">
                <div class="col-sm-6 col-12">
                    <label for="codeGroupe" class="col-sm-6 form-label p-0">Code Pharmacie du Maroc</label>
                    <input type="text" class="form-control form-control-md" id="codeGroupe" formControlName="code">
                </div>

                <div class="col-sm-6 col-12 mt-sm-0 mt-2">
                    <label for="raisonSociale" class="col-sm-6 form-label p-0">Raison Sociale</label>
                    <input type="text" class="form-control form-control-md" id="raisonSociale"
                        formControlName="raisonSociale">
                </div>
            </div>

            <div class="row p-1">
                <div class="col-sm-6 col-12">
                    <label for="nomResponsable" class="form-label">Nom Responsable</label>
                    <input type="text" class="form-control form-control-md" id="nomResponsable"
                        formControlName="nomResponsable">
                </div>

                <div class="col-sm-6 col-12 mt-sm-0 mt-2">
                    <label for="ville" class="col-sm-6 form-label p-0">Ville</label>
                    <input type="text" class="form-control form-control-md" id="ville" formControlName="ville">
                </div>
            </div>

            <div *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" class="col-sm-6 col-12">
                <label class="col-sm-6 form-label p-0" for="selectStatutPasserCmd">Passer Commande</label>
                <select2 id="selectStatutPasserCmd" formControlName="isEnrolled" hideSelectedItems="false"
                    [data]="selectStatutPasserCmd" class="form-control-sm w-100" multiple="false">
                </select2>
            </div>

            <ng-container *ngIf="!isFournisseurLabo">
                <div *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']" class="row p-1">
                    <div class="col-sm-6 col-12">
                        <label class="col-sm-6 form-label p-0" for="selectEstClient">Statut Client</label>
                        <select2 id="selectEstClient" formControlName="statutAccesClient" hideSelectedItems="false"
                            [data]="selectStatuClient" class="form-control-sm w-100" multiple="false">
                        </select2>
                    </div>

                    <div class="col-sm-6 col-12">
                        <label class="col-sm-6 form-label p-0" for="selectStatutEnrolement">Est actif dans le
                            groupe</label>
                        <select2 id="selectStatutEnrolement" formControlName="isEnrolled" hideSelectedItems="false"
                            [data]="selectStatutEnrolement" class="form-control-sm w-100" multiple="false">
                        </select2>
                    </div>
                </div>
            </ng-container>

        </div>
    </div>

    <div class="modal-footer">
        <button type="button" (click)="dismiss()" class="btn btn-light" tabindex="-1">Fermer</button>
        <button type="button" class="btn btn-secondary text-white" (click)="clearFilters()" tabindex="-1">Vider</button>
        <button type="button" type="submit" class="btn btn-primary ml-1 text-white" tabindex="-1">Rechercher</button>
    </div>
</form>