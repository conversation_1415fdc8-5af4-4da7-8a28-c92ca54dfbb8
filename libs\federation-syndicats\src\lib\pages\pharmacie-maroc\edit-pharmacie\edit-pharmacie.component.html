<!-- start page title -->
<div class="rowline mb-0">
    <div class="page-title-box row">

        <ng-container *ngIf="!isReadOnly">
            <div class="d-flex  align-items-center col-4 k-gap-4">
                <button class="actions-icons action-back btn text-white" (click)="back()">
                    <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
                </button>
                <h4 class="page-title fw-4 ps-2 "> {{mode === 'ajouter' ? 'Nouvelle Pharmacie du Maroc' : 'Modifier Pharmacie du Maroc'}}</h4>
            </div>

            <div class="col-8 px-1" *ngIf="!(isInactive$ | async)">
                <div class="row d-flex justify-content-end align-items-center">
                    <button (click)="handleSaveChanges()" style="padding-block: 6px;"
                        *ngIf="!isReadOnly && (mode === 'ajouter' || mode === 'modifier')"
                        class="btn btn-sm btn-success" title="Enregistrer Pharmacie">
                        <i class="bi bi-bookmark-check-fill"></i>
                        Enregistrer
                    </button>

                    <button (click)="back()" type="button" style="padding-block: 6px;"
                        class="btn btn-sm btn-dark text-white m-1">
                        <i class="mdi mdi-close"></i>
                        Quitter
                    </button>
                </div>
            </div>
        </ng-container>

        <ng-container *ngIf="isReadOnly">
            <div class="d-flex  align-items-center col k-gap-4">
                <button class="actions-icons action-back btn text-white" (click)="back()">
                    <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
                </button>
                <h4 class="page-title fw-4 ps-2 col-4">Consultation Pharmacie du Maroc</h4>
            </div>

            <div class="d-flex align-items-center px-0 mx-0 col-auto justify-content-end">
                <button (click)="back()" type="button" style="padding-block: 6px;"
                        class="btn btn-sm btn-dark text-white my-1 mx-0">
                        <i class="mdi mdi-close"></i>
                        Quitter
                    </button>
            </div>
        </ng-container>
    </div>
</div>
<!-- end page title -->

<div class="row mx-2">
    <div class="card bg-transparent my-1 w-100">
        <form [formGroup]="editForm" (ngSubmit)="onSubmit()" class="p-0 m-0" autocomplete="off">
            <ul ngbNav #infoPharamcieNav="ngbNav" [activeId]="activeId" (activeIdChange)="indexChanged($event)"
                class="nav-tabs  pharmacie-tab" style="gap: 0 !important;">
                <li [ngbNavItem]="1">
                    <a ngbNavLink class="w-100" [ngClass]="{'active-tab-alt ': activeId !== 1}">
                        <span class="d-flex row align-items-center px-0 px-sm-2">
                            <b>Informations Générales</b>
                            <i *ngIf="isFormInvalid()"
                                class="bi bi-exclamation-diamond-fill text-danger mx-2 fs-1"></i>
                            <i *ngIf="isFormValid()" class="bi bi-check-circle-fill text-success mx-2 fs-1"></i>
                        </span>
                    </a>

                    <ng-template ngbNavContent>
                        <div class="card-body px-1 bg-white mb-4 mb-sm-0">
                            <div class="form-row mx-0 p-1">

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0 mb-0">
                                        <label for="raisonSociale" class="form-label p-0 col-12">Raison Sociale <span
                                                class="text-danger">*</span></label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="raisonSociale" formControlName="raisonSociale"
                                                class="form-control pl-4" placeholder="Entrez la raison sociale"
                                                [ngClass]="{'is-invalid': f['raisonSociale']?.invalid && (f['raisonSociale']?.dirty || f['raisonSociale']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-info-circle-fill text-dark"></i>
                                            </div>

                                        </div>
                                    </div>

                                    <div *ngIf="f['raisonSociale']?.invalid && (f['raisonSociale']?.dirty || f['raisonSociale']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['raisonSociale']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['raisonSociale']?.errors['maxlength']">
                                            Raison Sociale ne peut pas depasser 80 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0">
                                        <label for="nomResponsable" class="form-label p-0 col-12">Nom Responsable <span
                                                class="text-danger">*</span></label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="nomResponsable" formControlName="nomResponsable"
                                                class="form-control pl-4" placeholder="Entrez le nom responsable"
                                                [ngClass]="{'is-invalid': f['nomResponsable']?.invalid && (f['nomResponsable']?.dirty || f['nomResponsable']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-person-circle text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="f['nomResponsable']?.invalid && (f['nomResponsable']?.dirty || f['nomResponsable']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['nomResponsable']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['nomResponsable']?.errors['maxlength']">
                                            Nom Responsable ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div id="edit-pharmacie-statut" class="col-md-4 px-0 mx-0 my-1">
                                    <div class="form-group mb-0 mx-0 px-0">
                                        <label for="statutJuridique" class="form-label p-0 col-12 ml-2"
                                            style="margin-bottom: 4px;">
                                            Statut Juridique
                                        </label>
                                        <div class="input-group picker-input"
                                            [ngClass]="{'is-invalid': f['statutJuridique']?.invalid && (f['statutJuridique']?.dirty || f['statutJuridique']?.touched || submitted)}">
                                            <select2 id="statutJuridique" [data]="stautsLabelsValues"
                                                formControlName="statutJuridique" hideSelectedItems="false"
                                                class="form-control-sm w-100" placeholder="Entrez Statut Juridique"
                                                multiple="false">
                                            </select2>
                                            <div class="picker-icons picker-icons-alt"
                                                style="position: absolute; left: 20px">
                                                <i class="bi bi-shield-fill-check mt-2"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="f['statutJuridique']?.invalid && (f['statutJuridique']?.dirty || f['statutJuridique']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['statutJuridique']?.errors['maxlength']">
                                            StatutJuridique ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>

                                <!--<div class="col-6 my-1">
                                    <div class="form-group mb-0 mb-0">
                                        <label for="raisonSocialeAr" class="form-label p-0 col-12">Raison Sociale
                                            Ar</label>
                                        <input type="text" id="raisonSocialeAr" dir="rtl"
                                            formControlName="raisonSocialeAr" class="form-control" />
                                    </div>
                                    mdi-home-variant
                                    <div *ngIf="f['raisonSocialeAr']?.invalid && submitted" class="text-danger">
                                        <div *ngIf="f['raisonSocialeAr']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['raisonSocialeAr']?.errors['maxlength']">
                                            Raison Sociale ne peut pas depasser 80 caractére.
                                        </div>
                                    </div>
                                </div>-->

                            </div>

                            <div class="form-row mx-0 p-1">
                                <div class="col-md-3 mt-1 mb-2">
                                    <div class="form-group mb-0">
                                        <label for="adresse1" class="form-label p-0 col-12">Adresse Ligne 1 <span
                                                class="text-danger">*</span></label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="adresse1" formControlName="adresse"
                                                class="form-control pl-4" placeholder="Entrez l'adresse ligne 1"
                                                [ngClass]="{'is-invalid': f['adresse']?.invalid && (f['adresse']?.dirty || f['adresse']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-shop text-dark"></i>

                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['adresse']?.invalid && (f['adresse']?.dirty || f['adresse']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['adresse']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['adresse']?.errors['maxlength']">
                                            Adresse ne peut pas depasser 80 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 mt-1 mb-2">
                                    <div class="form-group mb-0">
                                        <label for="adresse2" class="form-label col-12 p-0">Adresse Ligne 2</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="adresse2" formControlName="adresse2"
                                                class="form-control pl-4" placeholder="Entrez l'adresse ligne 2"
                                                [ngClass]="{'is-invalid': f['adresse2']?.invalid && (f['adresse2']?.dirty || f['adresse2']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-shop text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['adresse2'].invalid && (f['adresse2']?.dirty || f['adresse2']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['adresse2']?.errors['maxlength']">
                                            Adresse ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 my-1">
                                    <div class="form-group mb-0">
                                        <label for="adresseAr1" class="form-label col-12 p-0">Adresse Arab Ligne
                                            1</label>

                                        <div class="input-group picker-input">
                                            <input type="text" dir="rtl" id="adresseAr1" formControlName="adresseAr1"
                                                class="form-control pl-4" placeholder="Entrez l'adresse Arabe ligne 1"
                                                [ngClass]="{'is-invalid': f['adresseAr1']?.invalid && (f['adresseAr1']?.dirty || f['adresseAr1']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-shop text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['adresseAr1'].invalid && (f['adresseAr1']?.dirty || f['adresseAr1']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['adresseAr1']?.errors['maxlength']">
                                            Adresse ne peut pas depasser 80 caractére.
                                        </div>
                                    </div>
                                </div>


                                <div class="col-md-3 my-1">
                                    <div class="form-group mb-0">
                                        <label for="adresseAr2" class="form-label p-0 col-12">Adresse Arab Ligne
                                            2</label>

                                        <div class="input-group picker-input">
                                            <input type="text" dir="rtl" id="adresseAr2" formControlName="adresseAr2"
                                                class="form-control pl-4" placeholder="Entrez l'adresse Arabe ligne 2"
                                                [ngClass]="{'is-invalid': f['adresseAr2']?.invalid && (f['adresseAr2']?.dirty || f['adresseAr2']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-shop text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['adresseAr2']?.invalid && (f['adresseAr2']?.dirty || f['adresseAr2']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['adresseAr2']?.errors['maxlength']">
                                            Adresse ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row mx-0 p-1">
                                <div class="col-md-3 my-1">
                                    <div class="form-group mb-0">
                                        <label for="tel" class="form-label p-0 col-12">Téléphone <span
                                                class="text-danger">*</span></label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="tel" formControlName="telephone"
                                                class="form-control pl-4" placeholder="Ex : 6XXXXXXXX"
                                                [ngClass]="{'is-invalid': f['telephone']?.invalid && (f['telephone']?.dirty || f['telephone']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-phone text-dark"></i>

                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['telephone'].invalid && (f['telephone']?.dirty || f['telephone']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['telephone'].errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['telephone'].errors['invalidPhone']">
                                            Numéro de Telephone invalide
                                        </div>

                                        <div *ngIf="f['telephone'].errors['maxlength']">
                                            Téléphone ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 my-1">
                                    <div class="form-group mb-0">
                                        <label for="gsm" class="form-label p-0 col-12">GSM <span
                                                class="text-danger">*</span></label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="gsm" formControlName="gsm1" class="form-control pl-4"
                                                placeholder="Ex : 6XXXXXXXX"
                                                [ngClass]="{'is-invalid': f['gsm1']?.invalid && (f['gsm1']?.dirty || f['gsm1']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-telephone-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['gsm1'].invalid && (f['gsm1']?.dirty || f['gsm1']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['gsm1'].errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['gsm1'].errors['invalidPhone']">
                                            Numéro de GSM invalide
                                        </div>
                                        <div *ngIf="f['gsm1'].errors['maxlength']">
                                            GSM ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 my-1">
                                    <div class="form-group mb-0">
                                        <label for="Whatsapp" class="form-label p-0 col-12">Whatsapp</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="Whatsapp" formControlName="whatsapp"
                                                class="form-control pl-4" placeholder="Ex : 6XXXXXXXX"
                                                [ngClass]="{'is-invalid': f['whatsapp']?.invalid && (f['whatsapp']?.dirty || f['whatsapp']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-whatsapp text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="f['whatsapp'].invalid && (f['whatsapp']?.dirty || f['whatsapp']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['whatsapp'].errors['invalidPhone']">
                                            Numéro de Whatsapp invalide
                                        </div>
                                        <div *ngIf="f['whatsapp'].errors['maxlength']">
                                            Numéro de Whatsapp ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 my-1">
                                    <div class="form-group mb-0">
                                        <label for="fax" class="form-label p-0 col-12">Fax</label>

                                        <div class="input-group picker-input">
                                            <input type="number" id="fax" formControlName="fax"
                                                class="form-control pl-4" placeholder="Ex : 5XXXXXXXX"
                                                [ngClass]="{'is-invalid': f['fax']?.invalid && (f['fax']?.dirty || f['fax']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-newspaper text-dark"></i>

                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['fax'].invalid && (f['fax']?.dirty || f['fax']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['fax'].errors['maxLength']">
                                            Fax ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row mx-0 p-1">
                                <div class="col-md-6 my-1">
                                    <div class="form-group mb-0">
                                        <label for="email" class="form-label p-0 col-12">
                                            Email <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group picker-input">
                                            <input type="text" id="email" formControlName="email"
                                                class="form-control pl-4" placeholder="Ex: <EMAIL>"
                                                [ngClass]="{'is-invalid': f['email']?.invalid && (f['email']?.dirty || f['email']?.touched || submitted)}" />
                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-envelope-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="f['email'].invalid && (f['email']?.dirty || f['email']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['email'].errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['email'].errors['email']">
                                            Email Invalide.
                                        </div>
                                        <div *ngIf="f['email'].errors['maxlength']">
                                            Email ne peut pas depasser 40 caractére.
                                        </div>
                                        <div *ngIf="f['email'].errors['pattern']">
                                            Email doit être au format: Ex : example&#64;domain.com
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="col-md-6 my-sm-1 d-flex align-items-end justify-content-end responsive-actions">
                                    <button class="btn btn-success ml-auto ml-sm-0" (click)="goNext()">
                                        Suivant
                                        <i class="bi bi-chevron-right"></i>
                                    </button>
                                </div>
                            </div>


                        </div>
                    </ng-template>
                </li>
                <li class="tabs-separate flex-grow-0"></li>
                <li [ngbNavItem]="2">
                    <a ngbNavLink class="w-100" [ngClass]="{'active-tab-alt': activeId !== 2}">
                        <span class="d-flex row align-items-center px-0 px-sm-2">
                            <b>Localisation</b>
                            <i *ngIf="isLocationFormInvalid()"
                                class="bi bi-exclamation-diamond-fill text-danger mx-2 fs-1"></i>
                                <i *ngIf="isLocationFormValid()" class="bi bi-check-circle-fill text-success mx-2 fs-1"></i>
                        </span>
                    </a>

                    <ng-template ngbNavContent>
                        <div class="card-body bg-white w-100 px-1  mb-4 mb-sm-0">
                            <div class="form-row">
                                <div class="col-md-6">
                                    <div class="col-12 my-2">
                                        <div class="form-group mb-0">
                                            <label for="ville" class="form-label p-0 col-12">Ville <span
                                                    class="text-danger">*</span></label>

                                            <div class="input-group picker-input">
                                                <input type="text" class="form-control pl-4" formControlName="ville"
                                                    validateOnBlur [ngbTypeahead]="searchVille"
                                                    autocomplete="new-password" [resultFormatter]="formatter"
                                                    placeholder="Entrez la ville" [inputFormatter]="formatter"
                                                    [editable]='false'
                                                    [ngClass]="{'is-invalid': f['ville']?.invalid && (f['ville']?.dirty || f['ville']?.touched || submitted)}" />

                                                <div class="picker-icons picker-icons-alt">
                                                    <i class="bi bi-geo-alt-fill text-dark"></i>
                                                </div>
                                            </div>
                                        </div>

                                        <div *ngIf="f['ville']?.invalid && (f['ville']?.dirty || f['ville']?.touched || submitted)"
                                            class="text-danger">
                                            <div *ngIf="f['ville']?.errors['required']">
                                                champ obligatoire, veuillez saisir une ville valide
                                            </div>
                                            <div *ngIf="f['ville']?.errors['maxlength']">
                                                Ville ne peut pas depasser 40 caractére.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 my-2">
                                        <div class="form-group mb-0">
                                            <label for="localite" class="form-label p-0 col-12">Localité</label>

                                            <div class="input-group picker-input">
                                                <input type="text" class="form-control pl-4" formControlName="localite"
                                                    placeholder="Entrez la localité" [ngbTypeahead]="searchLocalite"
                                                    [resultFormatter]="localiteFormatter"
                                                    [inputFormatter]="localiteFormatter" />

                                                <div class="picker-icons picker-icons-alt">
                                                    <i class="bi bi-geo-alt-fill text-dark"></i>

                                                </div>
                                            </div>
                                        </div>

                                        <div *ngIf="f['localite'].invalid && submitted" class="text-danger">
                                            <div *ngIf="f['localite'].errors['required']">
                                                champ obligatoire, veuillez saisir une localite valide
                                            </div>
                                            <div *ngIf="f['localite'].errors['maxlength']">
                                                Localité ne peut pas depasser 40 caractére.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 my-2">
                                        <div class="form-group mb-0">
                                            <label for="latitude"> Latitude</label>

                                            <div class="input-group picker-input">
                                                <input type="text" id="latitude" formControlName="latitude"
                                                    placeholder="Latitude" class="form-control pl-4" />

                                                <div class="picker-icons picker-icons-alt">
                                                    <i class="bi bi-geo text-dark"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div *ngIf="f['latitude'].invalid && submitted" class="text-danger">
                                            <div *ngIf="f['latitude'].errors['maxlength']">
                                                latitude ne peut pas depasser 40 caractére.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 my-2">
                                        <div class="form-group mb-0">
                                            <label for="longitude">Longitude</label>

                                            <div class="input-group picker-input">
                                                <input type="text" id="longitude" formControlName="longitude"
                                                    placeholder="Longitude" class="form-control pl-4" />

                                                <div class="picker-icons picker-icons-alt">
                                                    <i class="bi bi-geo text-dark"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div *ngIf="f['longitude'].invalid && submitted" class="text-danger">
                                            <div *ngIf="f['longitude'].errors['maxlength']">
                                                longitude ne peut pas depasser 40 caractére.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 flex-column">
                                    <div class="row w-100 h-100">
                                        <div class="col-12 d-flex justify-content-center align-items-center">
                                            <div class="map-container">
                                                <app-map *ngIf="showMap" (coords)="setCoords($event)"></app-map>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex px-3 responsive-actions">
                                <button class="btn btn-light" (click)="goBack()">
                                    <i class="bi bi-chevron-left"></i>
                                    Précedant</button>
                            </div>
                        </div>
                    </ng-template>

                </li>
            </ul>
        </form>
        <div [ngbNavOutlet]="infoPharamcieNav"></div>
    </div>
</div>