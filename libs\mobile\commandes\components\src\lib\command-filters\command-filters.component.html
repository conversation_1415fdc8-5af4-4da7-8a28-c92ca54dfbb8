<ion-popover 
  [isOpen]="isOpen" 
  id="commande-filter-popup" 
  [backdropDismiss]="true" 
  (didDismiss)="dismissed.emit(true)"
  alignment="center" [trigger]="trigger" triggerAction="click" #popover>
  <ng-template>
    <ion-content id="commande-filter-cstm" class="ion-padding">
      <!--<ion-item lines="full">
        <ion-label class="filter-section-title">Rechercher par</ion-label>
      </ion-item>

      <ion-row class="ion-padding-top ion-padding-bottom" [formGroup]="parentForm">
        <ion-segment mode="ios" formControlName="segment">
          <ion-segment-button *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']" value="client">
            <ion-label>Client</ion-label>
          </ion-segment-button>

          <ion-segment-button *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE']" value="distributeur">
            <ion-label>Distributeur</ion-label>
          </ion-segment-button>
  
          <ion-segment-button value="offreur">
            <ion-label>Offreur</ion-label>
          </ion-segment-button>
        </ion-segment>
      </ion-row>-->

      <ion-item lines="full">
        <ion-label class="filter-section-title">Date Création</ion-label>
      </ion-item>
      <ion-row class="ion-padding-top ion-padding-bottom">
        <ion-col>
          <ion-row class="ion-flex ion-align-items-center ion-justify-content-start">
            <ion-label class="innner-section-label">Du : </ion-label>
            <ion-datetime-button [class]="filterControls['dateDebut']?.dirty ? 'is-dirty' : ''" datetime="dateCreationDebut" color="primary">
              <span *ngIf="!filterControls['dateDebut'].dirty" slot="date-target">jj/mm/aaaa</span>
            </ion-datetime-button>
          </ion-row>
        </ion-col>

        <ion-col>
          <ion-row class="ion-flex ion-align-items-center ion-justify-content-end">
            <ion-label class="innner-section-label">Au : </ion-label>
            <ion-datetime-button [class]="filterControls['dateFin']?.dirty ? 'is-dirty' : ''" datetime="dateCreationFin" color="primary">
              <span *ngIf="!filterControls['dateFin'].dirty" slot="date-target">jj/mm/aaaa</span>
            </ion-datetime-button>
          </ion-row>
        </ion-col>
      </ion-row>

      <ng-container *ngIf="showCommandStatusOptions">
        <ion-item lines="full">
          <ion-label class="filter-section-title ion-no-padding">Filtrer par statut</ion-label>
        </ion-item>

        <ion-row class="ion-padding-top">
          <ion-col class="ion-no-padding ion-no-margin" size="6" *ngFor="let option of filterControls['statusOptions'].value.value; let i = index">
            <ion-item lines="none">
              <ion-checkbox slot="start" [(ngModel)]="option.isChecked"></ion-checkbox>
              <ion-label class="innner-section-label"> {{ option?.text }}</ion-label>
            </ion-item>
          </ion-col>
        </ion-row>
      </ng-container>

      <ion-item class="ion-margin-bottom" lines="full"></ion-item>

      <ion-row class="ion-flex ion-align-items-center ion-justify-content-end">
        <ion-button class="popover-btn" shape="round" (click)="popover.dismiss(); vider()">Vider</ion-button>
        <ion-button class="popover-btn popover-btn-alt" shape="round" (click)="popover.dismiss(); appliquer()">Appliquer</ion-button>
      </ion-row>
    </ion-content>
  </ng-template>
</ion-popover>

<ng-container [formGroup]="parentForm">
  <ion-modal [keepContentsMounted]="true" #dateCreationDebut>
    <ng-template>
      <ion-datetime 
        id="dateCreationDebut"
        [showDefaultButtons]="true" 
        cancelText="Annuler" 
        doneText="Confirmer"
        locale="fr-FR"  
        formControlName="dateDebut"
        presentation="date" 
        [preferWheel]="true">
      </ion-datetime>
    </ng-template>
  </ion-modal>
  
  <ion-modal [keepContentsMounted]="true" #dateCreationFin>
    <ng-template>
      <ion-datetime 
        id="dateCreationFin"
        [showDefaultButtons]="true" 
        cancelText="Annuler" 
        doneText="Confirmer"
        locale="fr-FR"  
        presentation="date" 
        formControlName="dateFin"
        [preferWheel]="true">
      </ion-datetime>
    </ng-template>
  </ion-modal>
</ng-container>
