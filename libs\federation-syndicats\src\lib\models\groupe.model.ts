import { Societe } from "@wph/shared";

export interface FsMembre extends Societe {}

export class FsGroupe {
    id?: number;
    nomGroupe?: string;
    ville?: string;
    localite?: string;
    chefGroupe?: Societe;
    membres?: FsMembre[];

    constructor(partialFsGroupe: Partial<FsGroupe>) {
        this.id = partialFsGroupe?.id || null;
        this.nomGroupe = partialFsGroupe?.nomGroupe || null;
        this.ville = partialFsGroupe?.ville || null;
        this.localite = partialFsGroupe?.localite || null;
        this.chefGroupe = partialFsGroupe?.chefGroupe || null;
        this.membres = partialFsGroupe?.membres || null;
    }
}

export class GroupeCardSelection {
    selected?: boolean;
    value?: number;
}