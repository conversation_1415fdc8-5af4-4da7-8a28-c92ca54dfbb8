<!-- Topbar Start -->
<div class="navbar-custom pl-0"
  *ngIf="((currentPlateforme$ | async) !== 'FEDERATION_SYNDICAT' && (currentPlateforme$ | async) !== 'WIN_GROUPE')"
  [ngClass]="{
  'top-bar-bg-cw': (currentPlateforme$ | async) === 'COMMANDE_WEB',
  'top-bar-bg-win': (currentPlateforme$ | async) === 'WIN_OFFRE' ,
  'top-bar-bg-wo': (currentPlateforme$ | async) === 'DEFAULT',
  'top-bar-bg-wf': (currentPlateforme$ | async) === 'WIN_GROUPE',
  'top-bar-bg-fs': (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT'
}">
  <div class="overflow-hidden position-absolute" style="min-height: 70px; width: 100%;">
    <img alt="header-img" [src]="topbarImgUrl" class="top-bar-img-left">

    <img alt="header-img" [src]="topbarImgUrl" class="top-bar-img-right">
  </div>
  <div class="row">
    <div class="col-auto" [style.width]="(leftSidebarWidth === 'fixed' && currentWidth >= 920) ? '270px' : 'auto'">
      <div class="d-flex justify-content-start align-items-center" style="min-height: 68px;">
        <ng-container *jhiHasAnyPlateforme="['FEDERATION_SYNDICAT', 'WIN_GROUPE']; else: alternateMenuView">
          <div class="mx-3">
            <span class="pointer-cus" (click)="changeLeftSidebarType(); toggleMobileMenu($event)">
              <i class="mdi mdi-menu text-white mdi-24px"></i>
            </span>
          </div>
        </ng-container>

        <ng-template #alternateMenuView>
          <div
            *ngIf="((currentPlateforme$ | async) === 'COMMANDE_WEB' || (currentPlateforme$ | async) === 'WIN_OFFRE'); else: showForFournisseur"
            style="cursor: pointer; z-index: 99;" class="ml-3 mr-2"
            (click)="changeLeftSidebarType(); toggleMobileMenu($event)">
            <span>
              <i class="mdi mdi-menu text-white mdi-24px"></i>
            </span>
          </div>
        </ng-template>

        <ng-template #showForFournisseur>
          <div *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_SUPER_ADMIN', 'ROLE_AGENT_POINT_VENTE']"
            style="cursor: pointer; z-index: 99;" class="ml-3 mr-2"
            (click)="changeLeftSidebarType(); toggleMobileMenu($event)">
            <span>
              <i class="mdi mdi-menu text-white mdi-24px"></i>
            </span>
          </div>
        </ng-template>

        <ng-container *jhiHasTargetPlateformeFromWinplus="['PHARMALIEN', 'WIN_OFFRE']">
          <span class="py-1 px-2 src-winplus-badge d-none d-md-block" style="border-radius: 10px;">SRC: WINPLUS</span>
        </ng-container>
      </div>
    </div>

    <div
      class="{{ ((currentPlateforme$ | async) !== 'FEDERATION_SYNDICAT' && (currentPlateforme$ | async) !== 'WIN_GROUPE') ? 'col' : 'col-0' }} px-0 d-flex justify-content-start">
      <ul class="list-unstyled topbar-left-menu mb-0 w-100">
        <ng-container [ngSwitch]="currentPlateforme$ | async">
          <li *ngSwitchCase="'COMMANDE_WEB'"
            class="notification-list d-flex row mx-0 align-items-center justify-content-start"
            style="min-height: 70px;">
            <span class="plateforme-name p-0 mx-0">
              {{ ((currentGrossiste$ | async)?.raisonSociale || '') | uppercase }}
            </span>
          </li>

          <li *ngSwitchCase="'WIN_OFFRE'"
            class="notification-list d-flex row align-items-center w-100 justify-content-md-center justify-content-start"
            style="min-height: 68px;">
            <div class="col-auto w-100 d-flex align-items-center justify-content-center">
              <img src="assets/images/WinOffre+v4.svg" alt="logo" loading="eager" class="p-1"
                style="height: clamp(50px, 6vw, 75px) !important;">
            </div>
          </li>

          <li *ngSwitchCase="'DEFAULT'"
            class="notification-list d-flex row align-items-center w-100 justify-content-md-center justify-content-start"
            style="min-height: 68px;">
            <img src="assets/images/pharmalien_logo_light.svg" alt="logo" loading="eager"
              style="height: clamp(40px, 4vw, 50px) !important;">
          </li>
        </ng-container>
      </ul>
    </div>

    <div class="pl-0"
      [ngClass]="{'col-auto d-flex justify-content-end': (currentPlateforme$ | async) !== 'FEDERATION_SYNDICAT', 'col-xl-8 col-10': (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT'}">
      <ul class="list-unstyled topbar-right-menu float-right mb-0">
        <li class="notification-list mt-1 d-none d-lg-block">
          <h4 class="bg-role py-1 px-2 mt-2" [ngClass]="{
            'bg-win d-xl-block d-none': (currentPlateforme$ | async) === 'WIN_OFFRE',
            'bg-wo d-xl-block d-none': (currentPlateforme$ | async) === 'DEFAULT',
            'bg-cw d-xl-block d-none': (currentPlateforme$ | async) === 'COMMANDE_WEB',
            'bg-fs d-block': (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT'
          }">{{ currentUserRole }}</h4>
        </li>

        <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']">
          <li *ngIf="(currentPlateforme$ | async) === 'COMMANDE_WEB'" class="dropdown notification-list" ngbDropdown>

            <ng-container *jhiHasAnyServiceOption="['PASSER_COMMANDE']">
              <a class="nav-link dropdown-toggle arrow-none" data-toggle="dropdown" href="javascript: void(0);"
                role="button" aria-haspopup="false" (click)="afficherPanier()" aria-expanded="false" ngbDropdownToggle
                id="notificationDropdown">
                <i class="dripicons-cart noti-icon"></i>
                <span class="noti-icon-badge text-white">{{ panierCount }}</span>
              </a>
            </ng-container>
          </li>

          <li *jhiHasAnyPlateforme="'FEDERATION_SYNDICAT'" id="FS_notification-drop-down"
            class="dropdown notification-list d-block ml-0" ngbDropdown>
            <a class="nav-link dropdown-toggle arrow-none" data-toggle="dropdown" href="javascript: void(0);"
              role="button" aria-haspopup="false" aria-expanded="false" ngbDropdownToggle id="notificationDropdown">
              <i class="mdi mdi-bell mdi-24px noti-icon"></i>
              <span class="noti-icon-badge text-white">1</span>
            </a>

            <div class="dropdown-menu dropdown-menu-right dropdown-menu-animated topbar-dropdown-menu"
              aria-labelledby="notificationDropdown" ngbDropdownMenu>
              <!-- item-->
              <div class="dropdown-header noti-title" ngbDropdownItem>
                <h5 class="text-overflow m-0">Notifications</h5>
              </div>
            </div>
          </li>
        </ng-container>

        <li class="dropdown notification-list" ngbDropdown>
          <a class="nav-link dropdown-toggle nav-user bg-white arrow-none mx-0 border-0" data-toggle="dropdown"
            href="javascript: void(0);" role="button" aria-haspopup="false" aria-expanded="false" ngbDropdownToggle
            id="profileDropdown">
            <span class="account-user-avatar">
              <img [src]="loggedInUser?.logo ?? 'assets/images/user.png'" alt="user-image">
            </span>

            <span class="d-none d-xl-block">
              <span class="account-position"
                [ngClass]="{'extra-padding-top': !loggedInUser?.firstname && !loggedInUser?.lastname, 'win-cstm': (currentPlateforme$ | async) === 'WIN_OFFRE'}">{{
                loggedInUser?.societe?.raisonSociale }}</span>

              <span class="account-user-name"
                [ngClass]="{'win-cstm': (currentPlateforme$ | async) === 'WIN_OFFRE'}"><span
                  *ngIf="loggedInUser?.authorities?.includes('ROLE_AGENT_POINT_VENTE')">Dr.</span>
                {{loggedInUser?.lastname}} {{loggedInUser?.firstname}}</span>
            </span>
          </a>

          <div *jhiHasTargetPlateformeFromWinplus="[null]"
            class="dropdown-menu dropdown-menu-right dropdown-menu-animated topbar-dropdown-menu profile-dropdown"
            aria-labelledby="profileDropdown" ngbDropdownMenu>
            <!-- item-->
            <div class=" dropdown-header noti-title" ngbDropdownItem>
              <h5 class="text-overflow m-0">Bienvenue !</h5>
            </div>

            <!-- item-->
            <a [routerLink]="userAccountRouterLink" target="_self" rel="noopener noreferrer"
              class="dropdown-item notify-item" ngbDropdownItem>
              <i class="mdi mdi-account-circle mr-1 mdi-18px"></i>
              <span class="menu-item-txt">Mon Compte</span>
            </a>

            <!-- item-->
            <a href="javascript:void(0);" class="dropdown-item notify-item" ngbDropdownItem (click)="logout()">
              <i class="mdi mdi-logout mr-1 mdi-18px"></i>
              <span class="menu-item-txt">Se Déconnecter</span>
            </a>
          </div>
        </li>

        <li *ngIf="availablePageOptions?.length"
          class="dropdown notification-list h-100 d-flex d-lg-none align-items-center" ngbDropdown>
          <a class="nav-link dropdown-toggle bg-transparent arrow-none mx-1 my-auto border-0" data-toggle="dropdown"
            href="javascript: void(0);" role="button" aria-haspopup="false" aria-expanded="false" ngbDropdownToggle
            id="extraOptionsDropdown" style="vertical-align: middle;">
            <span class="h-100 d-flex align-items-center">
              <i class="mdi mdi-dots-vertical text-white mdi-24px"></i>
            </span>
          </a>

          <div
            class="dropdown-menu dropdown-menu-right dropdown-menu-animated d-flex flex-wrap topbar-dropdown-menu profile-dropdown mr-2"
            aria-labelledby="extraOptionsDropdown" ngbDropdownMenu style="width: fit-content !important;">

            <ng-container *ngFor="let item of availablePageOptions">
              <ng-container *jhiHasAnyAuthority="item?.targetRoles">
                <span *ngIf="item?.shouldShow" class="dropdown-item notify-item k-gap-2" (click)="item?.action()">
                  <i [class]="item?.iconClass" style="font-size: 1.1rem;"></i>
                  <span class="menu-item-txt ml-2">{{ item?.label }}</span>
                </span>
              </ng-container>
            </ng-container>
          </div>
        </li>

      </ul>
    </div>

  </div>
</div>
<!-- end Topbar -->

<!-- Floating extra menu options toggle btn START -->
<ng-container *jhiHasAnyPlateforme="['FEDERATION_SYNDICAT', 'WIN_GROUPE']">
  <div id="left-sidebar-menu" *ngIf="availablePageOptions?.length" class="position-fixed p-2 d-block d-lg-none" style="z-index: 1030;">
    <div ngbDropdown placement="bottom-right">
      <span class="pointer-cus d-inline-flex align-items-center justify-content-center"
        [style.backgroundColor]="(currentPlateforme$ | async) === 'FEDERATION_SYNDICAT' ? 'var(--fs-secondary)' : 'var(--wf-secondary)'"
        style="height: 30px; width: 30px; border-radius: var(--winoffre-base-border-radius);"
        role="button" aria-haspopup="false" aria-expanded="false" ngbDropdownToggle id="extraOptionsDropdown">
        <i class="mdi mdi-dots-vertical" style="font-size: 20px; color: white"></i>
      </span>
  
      <div
        class="dropdown-menu dropdown-menu-animated d-flex flex-wrap topbar-dropdown-menu profile-dropdown mr-2"
        aria-labelledby="extraOptionsDropdown" ngbDropdownMenu style="width: fit-content !important;">
  
        <ng-container *ngFor="let item of availablePageOptions">
          <ng-container *jhiHasAnyAuthority="item?.targetRoles">
            <span *ngIf="item?.shouldShow" class="dropdown-item notify-item k-gap-2" (click)="item?.action()">
              <i [class]="item?.iconClass" style="font-size: 1.1rem;"></i>
              <span class="menu-item-txt ml-2">{{ item?.label }}</span>
            </span>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </div>
</ng-container>
<!-- Floating extra menu options toggle btn END -->