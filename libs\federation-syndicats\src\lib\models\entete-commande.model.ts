import { BlocOffre, Catalogue, Offre, Pagination } from "@wph/data-access";
import { PharmacieEntreprise } from "./pharmacie-entreprise.model";
import { EntrepriseDTO } from "./entreprise.model";
import { GroupeEntreprise } from "./groupe-entreprise.model";
import { EtatCommande } from "./fs-commande.model";


interface IEnteteCommandeDTO {
  id: number;
  codeCommande: string;
  dateCreation: string;
  dateModification: string;
  dateSuppression: string;
  distributeur: EntrepriseDTO;
  client: PharmacieEntreprise;
  offre: Offre;
  societeCreateur: PharmacieEntreprise;
  createurId: number;
  userTraitementId: number;
  enteteCommandeId: number;
  codeGroupe: string;
  raisonSociale: string;
  nomPharmacien: string;
  ville: string;
  valeurCmdBruteHt: number;
  valeurCmdBruteTtc: number;
  valeurCmdNetHt: number;
  valeurCmdNetTtc: number;
  qteTotale: number;
  qteUg: number;
  tauxRf: number;
  tauxUg: number;
  valeurCmdBrutTva: number;
  remiseTtc: number;
  valeurCmdNetTva: number;
  commentaire: string;
  etatCommande: EtatCommande; // shoul bed deleted
  transporteur: EntrepriseDTO;
  listeDetails: IDetailCommandeDto[];
}

interface IDetailCommandeDto {
  id: number;
  blocOffre: BlocOffre;
  catalogue: Catalogue;
  codeProduitCatalogue: string;
  libelleProduit: string;
  ppv: number;
  tauxTva: number;
  prixVenteHt: number;
  prixVenteTtc: number;
  qteCmd: number;
  tauxRemise: number;
  montantRemise: number;
  tauxUg: number;
  qteUg: number;
  totalBrutHt: number;
  totalBrutTtc: number;
  totalNetHt: number;
  totalNetTtc: number;
  isCadeau: boolean;
  qteReliquatBl: number;
}

interface IDetailCommandeAchatGroupeDTO  extends IDetailCommandeDto {
  enteteCommandeAchatGroupeDTO : IEnteteCommandeAchatGroupeDTO;
}

export interface IEnteteCommandeConsolideeMarcheDTO extends IEnteteCommandeAchatGroupeDTO {
  groupeEntreprise: GroupeEntreprise;
  responsableGroupe: PharmacieEntreprise;
  supporterEntreprise: PharmacieEntreprise;
  prioriteDispatchBl : boolean;
  raisonSocialeTransporteur: string;
}



export interface IEnteteCommandeAchatGroupeDTO extends IEnteteCommandeDTO {
  typeCommande : TypeCommandeAchatGroupe;
  etatCommande: EtatCommande;
  prioriteDispatchBl : boolean;
}

interface EnteteCommandeUnitaireMarcheDTO extends IEnteteCommandeAchatGroupeDTO {
  commandeConsolideeMarcheDTO :IEnteteCommandeConsolideeMarcheDTO;
}

export type TypeCommandeAchatGroupe = 'C' | 'U';




export class EnteteCommandeConsolideeMarche implements IEnteteCommandeConsolideeMarcheDTO {
  id: number;
  codeCommande: string;
  dateCreation: string;
  dateModification: string;
  dateSuppression: string;
  distributeur: EntrepriseDTO;
  client: PharmacieEntreprise;
  offre: Offre;
  societeCreateur: PharmacieEntreprise;
  createurId: number;
  userTraitementId: number;
  codeGroupe: string;
  raisonSociale: string;
  nomPharmacien: string;
  ville: string;
  valeurCmdBruteHt: number;
  transporteur: EntrepriseDTO;
  enteteCommandeId: number;
  valeurCmdBruteTtc: number;
  valeurCmdNetHt: number;
  valeurCmdNetTtc: number;
  qteTotale: number;
  qteUg: number;
  tauxRf: number;
  tauxUg: number;
  valeurCmdBrutTva: number;
  remiseTtc: number;
  valeurCmdNetTva: number;
  commentaire: string;
  etatCommande: EtatCommande;
  typeCommande: TypeCommandeAchatGroupe;
  groupeEntreprise: GroupeEntreprise;
  responsableGroupe: PharmacieEntreprise;
  supporterEntreprise: PharmacieEntreprise;
  listeDetails: IDetailCommandeAchatGroupeDTO[];
  prioriteDispatchBl : boolean;
  raisonSocialeTransporteur : string;

  constructor(partial: Partial<IEnteteCommandeConsolideeMarcheDTO>) {
    Object.assign(this, partial);
  }

}

export class EnteteCommandeUnitaireMarche implements EnteteCommandeUnitaireMarcheDTO {
  id: number;
  codeCommande: string;
  dateCreation: string;
  dateModification: string;
  dateSuppression: string;
  distributeur: EntrepriseDTO;
  client: PharmacieEntreprise;
  offre: Offre;
  societeCreateur: PharmacieEntreprise;
  enteteCommandeId: number;
  createurId: number;
  userTraitementId: number;
  codeGroupe: string;
  raisonSociale: string;
  transporteur: EntrepriseDTO;
  nomPharmacien: string;
  ville: string;
  valeurCmdBruteHt: number;
  valeurCmdBruteTtc: number;
  valeurCmdNetHt: number;
  valeurCmdNetTtc: number;
  qteTotale: number;
  qteUg: number;
  tauxRf: number;
  tauxUg: number;
  valeurCmdBrutTva: number;
  remiseTtc: number;
  valeurCmdNetTva: number;
  commentaire: string;
  etatCommande: EtatCommande;
  typeCommande: TypeCommandeAchatGroupe;
  enteteCommandeAchatGroupeDTO: IEnteteCommandeAchatGroupeDTO;
  commandeConsolideeMarcheDTO: IEnteteCommandeConsolideeMarcheDTO;
  prioriteDispatchBl : boolean;
  listeDetails: IDetailCommandeAchatGroupeDTO[];

  constructor(partial: Partial<EnteteCommandeUnitaireMarcheDTO>) {
    Object.assign(this, partial);
  }
}


export class DetailCommandeDto implements IDetailCommandeDto {
  id: number;
  blocOffre: BlocOffre;
  catalogue: Catalogue;
  codeProduitCatalogue: string;
  libelleProduit: string;
  ppv: number;
  tauxTva: number;
  prixVenteHt: number;
  prixVenteTtc: number;
  qteCmd: number;
  tauxRemise: number;
  montantRemise: number;
  tauxUg: number;
  qteUg: number;
  totalBrutHt: number;
  totalBrutTtc: number;
  totalNetHt: number;
  totalNetTtc: number;
  isCadeau: boolean;
  qteReliquatBl: number;

  constructor (detailDto:Partial<IDetailCommandeDto>) {
    Object.assign(this,detailDto)
  }

}

type NatureCommande = "I" | "AG";

export class EnteteCommandeView {
  idCommande?: number;
  statut?: string;
  etatCommande?: EtatCommande;
  natureCommande?: NatureCommande;
  codeCommande?: string;
  dateCreation?: string;
  dateModification?: string;
  dateSuppression?: string;
  codeGroupe?: string;
  raisonSociale?: string;
  nomPharmacien?: string;
  ville?: string;
  valeurCmdBruteHt?: number;
  valeurCmdBruteTtc?: number;
  valeurCmdNetHt?: number;
  valeurCmdNetTtc?: number;
  qteTotale?: number;
  qteUg?: number;
  tauxRf?: number;
  tauxUg?: number;
  valeurCmdBrutTva?: number;
  remiseTtc?: number;
  valeurCmdNetTva?: number;
  valeurEscompteCmd?: number;
  commentaire?: string;
  dateEnvoiDist?: string;
  dateLivraison?: string;
  emailFournisseur?: string;
  raisonSocialeTransporteur?: string;
  distributeur?: EntrepriseDTO;
  client?: EntrepriseDTO;
  offre?: Offre;
}

export interface SearchEnteteCommandeView extends Pagination {
  content?: EnteteCommandeView[];
}