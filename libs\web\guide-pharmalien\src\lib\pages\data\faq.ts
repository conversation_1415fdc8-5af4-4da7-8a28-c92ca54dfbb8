import { Faq } from "libs/federation-syndicats/src/lib/models/faq.model";

export const  faqs: Faq[] = [
  // {
  //   id: 1,
  //   category: 'General',
  //   answers: [
  //     {
  //       id: 1,
  //       question: 'Demander un accès client sur "PharmaLien" ',
  //       textContent: 'Pour demander un accès client sur <b>"PharmaLien"</b>, suivez ces étapes:',
  //       steps: [
  //         { text: 'Cliquez sur <b>"Onboarding Clients"</b> pour demander un accès client' },
  //         { text: 'Cliquez sur <b>"Filtrer"</b> pour chercher le client souhaité' },
  //         { text: 'Chercher un client par <b>raison sociale, nom responsable, ville ou localité</b>' },
  //         { text: `Aprés l'identification du client souhaité, vous pouvez demander l'accès en cliquant sur le bouton <b>"Demander"</b>` },
  //         { text: 'Chercher les informations du client local, par <b>code local (Sarphix) ou raison sociale</b>' },
  //         { text: '<b>Note:</b> Il est important de bien vérifier que les informations du client <b>"Pharmacie du Maroc"</b> sont cohérentes avec celles du <b>client local</b>' },
  //         { text: `Pour consulter la liste des demandes d'accès, vous pouvez cliquer sur <b>"Demandes d'accès"</b>` },
  //         { text: `<b>Note:</b> Vous pouvez chercher par état de la demande d'accès, par code local, ou par date de la demande` },

  //       ],
  //       images: [
  //         { url: `/pharmalien/guides/videos/demande_acces_pharmalien.mp4`, alt: 'Demande_Acces' }
  //       ],
  //       video: null,
  //       examples: []
  //     },
  //   ]
  // },
  {
    id: 2,
    category: 'General',
    answers: [
      {
        id: 1,
        question: "Comment utiliser l'IA pour identifier les clients ?",
        textContent: 'Utiliser l\'IA pour identifier des clients similaires à "Sarphix" (Pharmacie du Maroc) peut être réalisé en suivant ces étapes:',
        steps: [
          { text: 'Cliquez sur <b>"Clients à traiter"</b> pour commencer' },
          { text: 'Cliquez sur le bouton <b>"Recherche de client à partir d\'un client local"</b>' },
          { text: 'Chercher un client en spécifiant <b>raison sociale, nom responsable, ville</b> du client local' },
          { text: `Aprés l'identification du client souhaité, vous pouvez demander l'accès en cliquant sur le bouton <b>"Demander"</b>` },
          { text: 'Chercher les informations du client local, par <b>code local (Sarphix) ou raison sociale</b>' },
          { text: '<b>Note:</b> Il est important de bien vérifier que les informations du client <b>"Pharmacie du Maroc"</b> sont cohérentes avec celles du <b>client local</b>' },
          { text: `Pour consulter la liste des demandes d'accès, vous pouvez cliquer sur <b>"Demandes d'accès"</b>` },
          { text: `<b>Note:</b> Vous pouvez chercher par état de la demande d'accès, par code local, ou par date de la demande` },
        ],
        images: [
          { url: `/pharmalien/guides/videos/TAC-1.mp4`, alt: 'Identifier_Clients' }
        ],
        video: null,
        examples: []
      },
    ]
  },
  
];
