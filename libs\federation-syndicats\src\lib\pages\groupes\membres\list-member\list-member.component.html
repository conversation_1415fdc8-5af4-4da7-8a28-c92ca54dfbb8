<!-- Start Of Header -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-7">Mon Groupe</h4>

        <div *jhiHasAnyAuthority="['ROLE_RESPONSABLE']" class="col-5 px-1">
            <div *ngIf="!(isInactive$ | async)" class="row justify-content-end align-items-center">
                <button (click)="ajouterMembre()" type="button" class="btn btn-sm btn-primary m-1">
                    <i class="mdi mdi-cogs"></i>
                    Paramétrer Groupe
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="row d-flex m-0 px-1">
    <div class="card m-0 w-100 p-0 bg-white" [style.height]="'calc(100vh - 60px)'">
        <div class="card-header py-1 pl-2 mx-0 bg-white">
            <div class="row d-flex py-0 px-1 flex-wrap justify-content-between">
                <div class="col-auto px-2 d-flex align-items-center">
                    <h4 class="text-dark">{{ (managedGroupe?.raisonSociale) | uppercase }}</h4>
                </div>

                <div class="col-auto p-0 d-flex justify-content-end">
                    <div class="row flex-wrap p-0">
                        <div class="col-autp p-0 m-1">
                            <div class="input-group picker-input">
                                <input [formControl]="filterSearch" type="text" placeholder="Rechercher par nom"
                                    class="form-control form-control-md pl-4" id="groupeCritere" />

                                <div class="picker-icons picker-icons-alt">
                                    <i class="mdi mdi-magnify pointer"></i>
                                </div>
                            </div>
                        </div>

                        <button (click)="displayFilter = !displayFilter" type="button"
                            class="btn btn-sm search-btn m-1">
                            <span *ngIf="!displayFilter">
                                <i class="bi bi-sliders"></i>
                                Recherche Avancée
                            </span>

                            <span *ngIf="displayFilter">
                                <i class="mdi mdi-close"></i>
                                Fermer la recherche
                            </span>

                        </button>

                        <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
                    </div>
                </div>
            </div>

            <form *ngIf="displayFilter" [formGroup]="filterForm" (ngSubmit)="appliquerFiltre($event)">
                <div class="row p-0 m-0 d-flex flex-wrap k-gap-2">
                    <div class="col-lg-auto col-12 p-0">
                        <label for="raisonSociale" class="form-label b-text">Pharmacie</label>

                        <div class="input-group">
                            <input type="text" class="form-control b-radius b-text form-control-md" id="raisonSociale"
                                formControlName="raisonSociale" />
                        </div>
                    </div>

                    <div class="col-lg-auto col-12 p-0">
                        <label for="ville" class="form-label b-text">Ville</label>

                        <div class="input-group picker-input">
                            <input type="text" class="form-control b-radius b-text form-control-md pl-4" id="ville"
                                formControlName="ville" [ngbTypeahead]="searchVilleOuLocalite"
                                [inputFormatter]="villeFormatter" [resultTemplate]="searchVilleTemplate" />

                            <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>

                            <ng-template #searchVilleTemplate let-result="result">
                                <b>{{ result?.labelFr }}</b>
                            </ng-template>
                        </div>
                    </div>

                    <div class="col-lg-auto col-12 p-0">
                        <label for="localite" class="form-label b-text">Localité</label>

                        <div class="input-group">
                            <input type="text" class="form-control b-radius b-text form-control-md" id="localite"
                                formControlName="localite" />
                        </div>
                    </div>

                    <div class="col-lg-3 col-12 m-0 py-1 px-0">
                        <label class="col-12 form-label p-0 m-0 b-text" for="selectstatut" style="margin-bottom: 3px !important;">Statut</label>

                        <div class="input-group">
                            <select2 id="selectstatut" formControlName="statutMembreEntreprise"
                                [data]="stautsLabelsValues" hideSelectedItems="false" class="form-control-sm p-0 w-100"
                                multiple="false"></select2>
                        </div>
                    </div>

                    <div class="col px-1 ml-2 d-flex align-items-end">
                        <div class="row d-flex align-items-end justify-content-center py-0">
                            <button type="button" (click)="viderFiltre()" title="Vider"
                            class="btn btn-sm btn-outline-primary b-radius">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <button type="submit" title="Appliquer filtre" class="btn btn-sm btn-primary b-radius mx-1">
                            <i class="mdi mdi-filter"></i>
                        </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="card-body m-0 p-0 bg-white">
            <kendo-grid [data]="gridData" #grid [pageable]="{
              buttonCount: 5,
              info: true,
              type: 'numeric',
              pageSizes: pageSizes,
              previousNext: true,
              position: 'bottom'
            }" [pageSize]="navigation.pageSize" class="fs-grid fs-listing-grid" (pageChange)="pageChange($event)"
                (pageChange)="pageChange($event)" (sortChange)="sortChange($event)" [sortable]="{ mode: 'single'}"
                [sort]="membreSort" [resizable]="true" [skip]="navigation.skip" style="height: 100%"
                [rowClass]="rowClass">

                <kendo-grid-column field="nomResponsable" class="text-wrap" [width]="180">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        Dr. {{ dataItem?.nomResponsable }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Nom Complet</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="alpha"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="raisonSociale" class="text-wrap" [width]="160">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        {{ dataItem?.raisonSociale }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Pharmacie</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="alpha"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="gsm1" [sortable]="false" title="GSM" [width]="160">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <span *ngIf="!dataItem?.gsm1"
                            class="d-flex align-items-center justify-center k-gap-1 text-warning">
                            <i class="bi bi-shield-fill-exclamation"></i>
                            indisponible
                        </span>
                        {{ dataItem?.gsm1}}
                    </ng-template>
                </kendo-grid-column>
                
                <kendo-grid-column field="telephone" [sortable]="false" title="Téléphone" [width]="160">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <span *ngIf="!dataItem?.telephone"
                            class="d-flex align-items-center justify-center k-gap-1 text-warning">
                            <i class="bi bi-shield-fill-exclamation"></i>
                            indisponible
                        </span>
                        {{ dataItem?.telephone}}
                    </ng-template>
                </kendo-grid-column>
                
                <kendo-grid-column field="whatsapp" [sortable]="false" title="WhatsApp" [width]="160">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <span *ngIf="!dataItem?.whatsapp"
                            class="d-flex align-items-center justify-center k-gap-1 text-warning">
                            <i class="bi bi-shield-fill-exclamation"></i>
                            indisponible
                        </span>
                        {{ dataItem?.whatsapp}}
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="email" field="email" title="Email" [width]="160">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <span *ngIf="!dataItem?.email"
                            class="d-flex align-items-center justify-center k-gap-1 text-warning">
                            <i class="bi bi-shield-fill-exclamation"></i>
                            indisponible
                        </span>
                        {{ dataItem?.email}}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="alpha"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column [sortable]="false" class="text-wrap" [width]="160">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap">Ville / Localité</span>
                    </ng-template>
                    
                    <ng-template kendoGridCellTemplate let-dataItem>
                        {{ dataItem?.ville ?? dataItem?.localite }}
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="dateAttachementGroupe" [width]="160">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        {{ dataItem?.dateAttachementGroupe | date: 'dd/MM/yyyy HH:mm' }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Date d'Adhésion</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="numeric"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column title="Rôle" [width]="120" [sortable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <button class="btn btn-dark rounded-pill py-1 px-2 btn-block d-flex justify-content-center"
                            disabled *ngIf="rowIndex === 0">
                            Responsable
                        </button>

                        <button (click)="promoteMember(dataItem)"
                            class="btn btn-primary btn-block rounded-pill py-1 px-2 d-flex justify-content-center"
                            *ngIf="rowIndex > 0">
                            <span>Membre</span>

                            <ng-container *ngIf="!(isInactive$ | async)">
                                <i class="bi bi-arrow-left-right mx-1" *jhiHasAnyAuthority="['ROLE_RESPONSABLE']"></i>
                            </ng-container>

                        </button>

                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column title="Statut" [width]="120" [hidden]="(isInactive$ | async) === true"
                    [sortable]="false" *jhiHasAnyAuthority="['ROLE_RESPONSABLE']">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <div (click)="activerOuDesactiverMembre(dataItem)">
                            <kendo-switch [readonly]="true" size="medium" onLabel="Actif"
                                [checked]="dataItem?.statutMembreGroupe" offLabel="Inactif" class="status-switch-align">
                            </kendo-switch>
                        </div>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column *jhiHasAnyAuthority="['ROLE_RESPONSABLE']" [hidden]="(isInactive$ | async) === true"
                    title="Actions" [width]="100">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <div sty class="d-flex justify-content-center k-gap-1">

                            <span (click)="(rowIndex > 0) && suggererPharmacie(dataItem?.id)"
                                class="actions-icons btn-success pointer-cus" placement="left"
                                title="Suggérer des modificaions">
                                <i class="bi bi-pencil-square"></i>
                            </span>

                            <div (click)="(rowIndex > 0 && dataItem?.statutMembreGroupe) && detacherMembre(dataItem)"
                                placement="left" title="Détacher Membre" class="pointer-cus">
                                <span class="actions-icons btn-danger pointer-cus"
                                    [ngClass]="{ 'opacity-light': (rowIndex === 0) || !dataItem?.statutMembreGroupe}">
                                    <i class="bi bi-trash"></i>
                                </span>
                            </div>
                        </div>
                    </ng-template>
                </kendo-grid-column>

                <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
                    let-total="total">
                    <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
                        [navigation]="navigation" style="width: 100%;"
                        (pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
                </ng-template>

                <ng-template kendoGridNoRecordsTemplate>
                    <span>Aucun résultat trouvé.</span>
                </ng-template>
            </kendo-grid>
        </div>
    </div>
</div>


<ng-template #modeEnvoieModal let-modal>
    <div
        class="fs-modal bg-white d-flex align-items-center justify-content-center flex-column p-2 pt-3 position-relative">
        <div class="cross-button">
            <i class="bi bi-x pointer" (click)="modal.dismiss()"></i>
        </div>
        <div class="d-flex align-items-center justify-content-center k-gap-2">
            <p class="success-text text-warning text-center m-0">
                <img src="assets/icons/info-warning.png" height="40" width="40" alt=""> <br>
                <span>Envoyer Identifiants</span>
            </p>
        </div>
        <div class=" mt-2">
            <p class="message-text p-0 text-center">Veuillez sélectionner une méthode d'envoi des identifiants: </p>
        </div>

        <div class="row mb-1">
            <div class="form-group m-1 d-flex align-items-center">
                <input type="radio" id="whatsApp" class="form-control" [(ngModel)]="modeEnvoiIdentifiants"
                    name="identifiants" value="whatsapp" style="width: 25px !important; height: 25px !important;">
                <label for="whatsApp" class="form-label mx-1 mt-1">WhatsApp</label>
            </div>

            <div class="form-group m-1 d-flex align-items-center">
                <input type="radio" id="emailSms" class="form-control" [(ngModel)]="modeEnvoiIdentifiants"
                    name="identifiants" value="mail" style="width: 25px !important; height: 25px !important;"> <label
                    for="emailSms" class="form-label mx-1 mt-1">E-mail et SMS</label>
            </div>
        </div>

        <div class="actions d-flex w-100 justify-content-center align-items-center k-gap-2">
            <button (click)="sendCredentialsGeneric(); modal.dismiss('confirm click')"
                class="btn btn-fs-confirm  btn-block" tabindex="-1"
                [disabled]="!modeEnvoiIdentifiants">Confirmer</button>
            <button class="btn btn-block btn-fs-cancel m-0" tabindex="-1"
                (click)="modal.dismiss('cancel click')">Annuler</button>
        </div>
    </div>

</ng-template>