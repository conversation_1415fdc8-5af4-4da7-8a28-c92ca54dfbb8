import { Component, OnInit } from '@angular/core';
import { forkJoin } from 'rxjs';
import { AdminConfigService, AlertService } from '../../../shared/src';

@Component({
  selector: 'app-config-admin',
  templateUrl: './config-admin.component.html',
  styleUrls: ['./config-admin.component.scss']
})
export class ConfigAdminComponent implements OnInit {

  constructor(private adminConf: AdminConfigService, private alertSrv: AlertService) { }
  zone: number
  submit

  tabs1 = 1;
  messageError: string | null = null

  rateLimitingStatus: boolean = false
  ngOnInit(): void {

    this.fetchConfig()

  }

  fetchConfig() {


    forkJoin([
      this.adminConf.fetchTimeZone(),
      this.adminConf.fetchRateLimitingStatus()

    ]).subscribe(([zone, rateStatus]) => {


      this.zone = zone;
      this.rateLimitingStatus = rateStatus


    })
  }

  save() {
    this.submit = true


    if (this.zone != null)
      forkJoin([
        this.adminConf.setTimeZone(this.zone),
        this.adminConf.editRateLimitingStatus(this.rateLimitingStatus)

      ]).subscribe(([Timezone, rateStatus]) => {

        this.alertSrv.success('Les configurations sont validées avec succès');
        this.zone = Timezone;
        this.rateLimitingStatus = rateStatus
        this.submit = false

      })

    this.tabs1 = 1


    //this.messageError = 'Veuillez sélectionner une zone valide (0 ou 1).'
  }

}
