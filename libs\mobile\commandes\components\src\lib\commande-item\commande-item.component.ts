import { Component, Input, ViewEncapsulation } from "@angular/core";
import { Commande, Fournisseur } from "@wph/data-access";

@Component({
    selector: 'wph-commande-item',
    templateUrl: './commande-item.component.html',
    styleUrls: ['./commande-item.component.scss'],
    encapsulation: ViewEncapsulation.Emulated,
})
export class CommandeItemComponent {
    @Input() item: Commande;
    @Input() societe: Fournisseur;

    constructor() { }
}