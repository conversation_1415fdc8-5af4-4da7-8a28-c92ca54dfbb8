import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AuditRoutingModule } from "./audit-routing.module";
import { AuthLogComponent } from "./auth-log/auth-log.component";
import { SharedModule } from "@wph/shared";
import { WebSharedModule } from "@wph/web/shared";


@NgModule({
  declarations: [
    AuthLogComponent
  ],
  imports: [
    CommonModule,
    AuditRoutingModule,
    SharedModule,
    WebSharedModule
  ]
})
export class AuditModule { }
