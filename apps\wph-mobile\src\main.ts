import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { environment } from './environments/environment';
import { defineCustomElements as pwaElements} from '@ionic/pwa-elements/loader';

if (environment.production) {
  enableProdMode();
}

pwaElements(window);

window.addEventListener('DOMContentLoaded', async () => {
  try {
    platformBrowserDynamic().bootstrapModule(AppModule)
      .catch(err => console.log(err));
  } catch (err) {
    console.log(`Errors: ${err}`);
    throw new Error(`Error: ${err}`)
  }

});
