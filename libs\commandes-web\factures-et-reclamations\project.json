{"name": "commandes-web-factures-et-reclamations", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/commandes-web/factures-et-reclamations/src", "prefix": "wph", "targets": {"test": {"executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/commandes-web/factures-et-reclamations/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["libs/commandes-web/factures-et-reclamations/**/*.ts", "libs/commandes-web/factures-et-reclamations/**/*.html"]}}}, "tags": []}