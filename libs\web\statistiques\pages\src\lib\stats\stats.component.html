<div class="rowline mb-2">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-4">
      Statistiques Marché
    </h4>

    <div class="col-8 px-1">
      <div class="row justify-content-end align-items-center">
        <button type="button" class="btn btn-sm btn-info m-1" (click)="openFilterModal(filtrageModal, 'lg')">
          <i class="mdi mdi-filter-variant"></i>
          Filtrer</button>
      </div>
    </div>
  </div>
</div>

<div class="row m-2">
  <wph-stat class="col-md-4 col-6 mb-3" *ngFor="let stat of stats" [numberStat]="stat"></wph-stat>
</div>

<div class="row w-100 px-2 m-0 d-flex" id="stats-nav-container">
  <ul ngbNav #nav1="ngbNav" [(activeId)]="currentTab" (activeIdChange)="activeTabChange($event)"
    class="nav-tabs bg-white">
    <li [ngbNavItem]="1" fxFlex>
      <a ngbNavLink>
        <i class="uil-server d-md-none d-block"></i>
        <span class="d-none d-md-block">Offre</span>
      </a>
  
      <ng-template ngbNavContent>
        <kendo-grid [data]="offreGridData" [sort]="offreSort" [sortable]="{ mode: 'single'}" [selectable]="true"
          [selectable]="{mode: 'single', checkboxOnly: false}"
          style="min-height:calc(100vh - 430px); border-radius: var(--winoffre-base-border-radius)"
          (sortChange)="sortChange($event, 'offre')" (selectionChange)="chooseOffer($event)" kendoGridSelectBy
          [(selectedKeys)]="selectedRowIndex" class="border-grey m-0">
          <kendo-grid-checkbox-column class="select-clmn" [width]="40"></kendo-grid-checkbox-column>
  
          <kendo-grid-column title="Offre" field="offreDto.titre" class="text-wrap" [width]="160">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.offreDto?.titre}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="nbrCommande" title="Nombre Commandes" [width]="90" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Nombre Commandes</span>
            </ng-template>
            
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.nbrCommande}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="qteVedues" title="Unités {{ hasAgentPointVenteRole ? 'Achetés' : 'Vendus' }}"
            [width]="100" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">
                Unités {{ hasAgentPointVenteRole ? 'Achetés' : 'Vendus' }}
              </span>
            </ng-template>
            
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.qteVedues}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="caBrut" title="CA Brut réalisé" [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">CA Brut réalisé</span>
            </ng-template>
            
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-column="column">
              {{dataItem.caBrut | number: "1.2-2":"fr-FR"}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="caNet" title="CA Net réalisé" [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">CA Net réalisé</span>
            </ng-template>
            
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-column="column">
              {{dataItem.caNet | number: "1.2-2":"fr-FR"}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="qteUG" title="Quantité UG" [width]="80" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Quantité UG</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.qteUG}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="mtRemise" title="Remise" [width]="120" class="text-end">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.mtRemise | number: "1.2-2":"fr-FR"}}
            </ng-template>
          </kendo-grid-column>
  
          <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun résultat trouvé.</span>
          </ng-template>
        </kendo-grid>
      </ng-template>
    </li>
  
    <li [ngbNavItem]="2" fxFlex>
      <a ngbNavLink>
        <i class="mdi mdi-tag-multiple d-md-none d-block"></i>
        <span class="d-none d-md-block">Pack</span>
      </a>
  
      <ng-template ngbNavContent>
        <kendo-grid [data]="packGridData" [sort]="packSort"
          style="min-height:calc(100vh - 430px); border-radius: var(--winoffre-base-border-radius)"
          [sortable]="{ mode: 'single'}" (sortChange)="sortChange($event, 'pack')" class="border-grey m-0">
          <kendo-grid-column title="N° Pack" field="packOffre.id" [width]="120">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.packOffre?.id}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column title="Pack" class="text-wrap" field="packOffre.titre" [width]="160">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.packOffre?.titre}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column title="Description" class="text-wrap" field="packOffre.description" [width]="180">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.packOffre?.description}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="nbrCommande" title="Nombre Commandes" [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Nombre Commandes</span>
            </ng-template>
            
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.nbrCommande}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="qteVedues" title="Unités {{ hasAgentPointVenteRole ? 'Achetés' : 'Vendus' }}"
            [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Unités {{ hasAgentPointVenteRole ? 'Achetés' : 'Vendus' }}</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.qteVedues}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="caBrut" title="CA Brut réalisé" [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">CA Brut réalisé</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-column="column">
              {{dataItem.caBrut | number: "1.2-2":"fr-FR"}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="caNet" title="CA Net réalisé" [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">CA Net réalisé</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-column="column">
              {{dataItem.caNet | number: "1.2-2":"fr-FR"}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="qteUG" title="Quantité UG" [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Quantité UG</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.qteUG}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="mtRemise" title="Remise" [width]="120" class="text-end">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.mtRemise | number: "1.2-2":"fr-FR"}}
            </ng-template>
          </kendo-grid-column>
  
          <ng-template kendoGridNoRecordsTemplate>
            <span *ngIf="offreGridData?.data?.length > 0" id="no-records"
              class="d-flex align-items-center justify-content-center p-2">
              <i class="mdi mdi-information mr-1 my-0"></i>
              <span class="no-records-alt">Veuillez choisir une offre</span>
            </span>
  
            <span *ngIf="offreGridData?.data?.length === 0">Aucun résultat trouvé.</span>
          </ng-template>
        </kendo-grid>
      </ng-template>
    </li>
  
    <li [ngbNavItem]="3" fxFlex>
      <a ngbNavLink>
        <i class="mdi mdi-package-variant-closed d-md-none d-block"></i>
        <span class="d-none d-md-block">Produit</span>
      </a>
  
      <ng-template ngbNavContent>
        <kendo-grid [data]="produitGridData" [resizable]="false" [reorderable]="false" class="w-100 m-0"
          style="min-height:calc(100vh - 430px); border-radius: var(--winoffre-base-border-radius)" [sort]="produitSort"
          [sortable]="{ mode: 'single'}" (sortChange)="sortChange($event, 'produit')">
          <kendo-grid-column title="Produit" class="text-wrap" field="produitDto.libelleProduit" [width]="240">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.produitDto?.libelleProduit}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="nbrCommande" title="Nombre Commandes" [width]="100" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Nombre Commandes</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.nbrCommande}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="qteVedues" title="Unités {{ hasAgentPointVenteRole ? 'Achetés' : 'Vendus' }}"
            [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Unités {{ hasAgentPointVenteRole ? 'Achetés' : 'Vendus' }}</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.qteVedues}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="caBrut" title="CA Brut réalisé" [width]="130" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">CA Brut réalisé</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-column="column">
              {{dataItem.caBrut | number: "1.2-2":"fr-FR"}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="caNet" title="CA Net réalisé" [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">CA Net réalisé</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-column="column">
              {{dataItem.caNet | number: "1.2-2":"fr-FR"}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="qteUG" title="Quantité UG" [width]="120" class="text-end">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Quantité UG</span>
            </ng-template>
  
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.qteUG}}
            </ng-template>
          </kendo-grid-column>
  
          <kendo-grid-column field="mtRemise" title="Remise" [width]="120" class="text-end">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem?.mtRemise | number: "1.2-2":"fr-FR"}}
            </ng-template>
          </kendo-grid-column>
  
          <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun résultat trouvé.</span>
          </ng-template>
        </kendo-grid>
      </ng-template>
    </li>
  </ul>
</div>

<div class="card p-1 bg-transparent px-2">
  <div [ngbNavOutlet]="nav1"></div>
</div>


<ng-template #filtrageModal let-modal>
  <wph-stats-filter-modal [villes]="listeVilles" [fournisseurs]="listeFournisseurs" [formGroup]="searchFormGroup"
    (modalAction)="filterModalAction($event)">
  </wph-stats-filter-modal>
</ng-template>