<!-- Start Of Header -->
<div class="rowline mb-0" style="margin-bottom: 0px !important;">
	<div class="page-title-box row">
		<h4 class="page-title fw-4 ps-2 col-6 col-sm-4">
			<span class=""> Journal de connexion </span>
		</h4>

	</div>
</div>


<!-- END HEADER -->
<div class="row d-flex m-0 px-1">
	<div class="card m-0 w-100 p-0" style="height: calc(100vh - 60px);">
		<div class="card-header py-1 pl-2 bg-white">
			<div class="row p-0">
				<div class="col-12 p-0 d-flex justify-content-end">
					<div class="row p-0 justify-content-center justify-content-sm-end">
						<div class="col-sm p-0 m-1">
							<div class="input-group picker-input">
								<input type="search" placeholder="Code Client" class="form-control form-control-md pl-4"
									id="groupeCritere" [formControl]="codeClientSeach"  />

								<div class="picker-icons picker-icons-alt">
									<i class="mdi mdi-magnify pointer"></i>
								</div>
							</div>
						</div>


						<button type="button" (click)="displayFilter = !displayFilter"
							class="btn btn-sm search-btn m-1">
							<span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
								<i class="bi bi-sliders"></i>
								<span class="mx-1">Recherche Avancé</span>
							</span>

							<ng-template #closeFilter>
								<span class="d-flex align-items-center">
									<i class="mdi mdi-close"></i>
									<span class="mx-1">Fermer la recherche</span>
								</span>
							</ng-template>

						</button>
					</div>
				</div>
			</div>

			<form [formGroup]="authLogForm">
				<div id="advanced-search" *ngIf="displayFilter" class="row p-0 flex-wrap px-1 my-2 gap-1"
					style="gap: 8px">
					<div class="col-sm p-0 m-0">
						<label for="adresseIp" class="col-form-label text-left">Ip Adresse</label>

						<div class="input-group">
							<input type="text" name="adresseIp"
								class="form-control form-control-md b-radius bg-white" id="adresseIp"
								formControlName="adresseIp">
						</div>
					</div>

					<div class="col-sm p-0 m-0">
						<label for="numCommande" class="col-form-label text-left">Nom Prenom Operateur</label>

						<div class="input-group">
							<input type="text" name="nomPrenomOperateur"
								class="form-control form-control-md b-radius bg-white" formControlName="nomPrenomOperateur"
								id="nomPrenomOperateur">
						</div>
					</div>

					<div class="col-sm p-0 m-0">
						<label for="dateDebut" class="col-form-label text-left">Date Début</label>
						<div class="input-group picker-input">
							<input type="text" [readOnly]="true" class="form-control form-control-md bg-white"
								id="dateDebut" ngbDatepicker #drange1="ngbDatepicker" (click)="drange1.toggle()"
								formControlName="dateDebut" placeholder="jj/mm/aaaa">

							<div class="picker-icons text-dark"><i (click)="drange1.toggle()" class="mdi mdi-calendar"></i></div>
						</div>
					</div>

					<div class="col-md p-0 m-0">
						<label for="dateFin" class="col-form-label text-left">Date Fin</label>
						<div class="input-group picker-input">
							<input type="text" [readOnly]="true" class="form-control form-control-md bg-white"
								id="dateFin" ngbDatepicker #drange2="ngbDatepicker" (click)="drange2.toggle()"
								formControlName="dateFin" placeholder="jj/mm/aaaa">
							<div class="picker-icons text-dark"><i (click)="drange2.toggle()" class="mdi mdi-calendar"></i></div>
						</div>
					</div>

          <div class="col d-flex align-items-end py-0 mt-2 mt-md-0">
						<button type="button" class="btn btn-sm btn-outline-primary b-radius" (click)="clearFilterCommandes()">
							<i class="bi bi-arrow-clockwise"></i>
						</button>

						<button type="submit" (click)="searchAuthLog()" class="btn btn-sm btn-primary b-radius mx-1 d-flex flex-shrink-0">
							<i class="mdi mdi-filter"></i>
							<span class="mx-1">Appliquer</span>
						</button>
					</div>


				</div>
			</form>
		</div>
		<div class="card-body m-0 px-1 bg-white pt-1 pb-0">
			<kendo-grid [data]="gridData" [pageable]="{
		buttonCount: 5,
		info: true,
		type: 'numeric',
		pageSizes: pageSizes,
		previousNext: true,
		position: 'bottom'
	  }" [pageSize]="navigation.pageSize" class="fs-grid fs-listing-grid" (pageChange)="pageChange($event)"
				(sortChange)="sortChange($event)" [sortable]="{ mode: 'single'}" [sort]="groupeSort" [resizable]="true"
				[skip]="navigation.skip" style="height: 100%"
				[selectable]="true">
				<kendo-grid-column [sortable]="false" field="user.entrepriseDTO.code" title="Code Client" [width]="150"></kendo-grid-column>
				<kendo-grid-column [sortable]="false" field="user.username" title="Nom d'utilisateur" [width]="150"></kendo-grid-column>
        <kendo-grid-column [sortable]="false" field="user.entrepriseDTO.nomResponsable" title="Nom Responsable" [width]="150" ></kendo-grid-column>
        <kendo-grid-column [sortable]="false" field="user.entrepriseDTO.ville" title="Ville" [width]="150" ></kendo-grid-column>
        <kendo-grid-column field="user" title="Raison Sociale" [width]="150" >
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.user.entrepriseDTO?.raisonSociale }}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="logDate" title="Date d'accès" [width]="150"></kendo-grid-column>
				<!-- <kendo-grid-column field="type" title="Type d'accès" [width]="150"></kendo-grid-column> -->
				<kendo-grid-column field="srcIp" title="Ip Address" [width]="150"></kendo-grid-column>
				<kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
					pagerItemsPerPage="éléments par page"></kendo-grid-messages>

				<ng-template kendoGridNoRecordsTemplate>
					<span>Aucun résultat trouvé.</span>
				</ng-template>
				<ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
					let-total="total">
					<wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
						[navigation]="navigation" style="width: 100%;"
						(pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
				</ng-template>
			</kendo-grid>
		</div>
	</div>
</div>
