<div class="display">
    <div class="grids d-flex align-items-center mx-1">
        <div class="s1" (click)="up()"><i class="mdi mdi-plus"></i></div>
        <div class="s2">
            <input type="number" [id]="targetID" min="1" max="100" [ngStyle]="{'width': width,'height':'27px' , 'text-align': 'center'}"
                   [(ngModel)]="stockNumber" (input)="onSearchChange($any($event.target)?.value)"
                   (keypress)="checkKeys($event)"/>
        </div>
        <div class="s3" (click)="down()"><i class="mdi mdi-minus"></i></div>
    </div>
</div>
