<ng-template #modalProduit let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Recherche de produit</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span>&times;</span>
        </button>
    </div>

    <div [id]="(currentPlateforme$ | async) === 'WIN_OFFRE' ? 'wo-recherche-produit' : 
        (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT' ? 'fs-recherche-produit' : ''" class="modal-body p-1">
        <form id="produitForm" [formGroup]="produitForm" (ngSubmit)="navigation.skip = 0;SearchProduit()">
            <div class="form-group">
                <div class="input-group">
                    <input type="search" class="form-control col mx-1" formControlName="libelleProduit"
                        placeholder="Libellé produit">

                    <input type="text" class="form-control col mx-1" formControlName="fournisseur"
                        placeholder="Laboratoire" name="fournisseur" [ngbTypeahead]="searchlaboratoire"
                        [resultFormatter]="laboFormatter" [inputFormatter]="laboFormatter"
                        (change)="fournisseurchange()" [editable]="false">

                    <div *ngIf="fournisseur.invalid" class="alert alert-danger">
                        <div *ngIf="fournisseur.errors['required']">
                            Veuillez sélectionner un fournisseur valide
                        </div>
                    </div>

                    <select2 class="col mx-1 p-0" [data]="listeGammeAutorises" placeholder="Gamme"
                        formControlName="gamme" [multiple]="false"></select2>

                    <select2 class="col mx-1 p-0" [data]="listeforme" formControlName="forme" placeholder="Forme"
                        [multiple]="false"></select2>

                    <ng-container *ngIf="enableSousListeSearch">
                        <input type="search" class="form-control form-control-md col mx-1"
                            formControlName="nomListeProduits" placeholder="Nom Liste Produits" name="ListeProduits"
                            [ngbTypeahead]="searchListeProduit" [resultFormatter]="listeProduitFormatter"
                            [inputFormatter]="listeProduitFormatter">

                        <input type="search" class="form-control form-control-md col mx-1"
                            formControlName="nomSousListe" placeholder="Nom Sous Liste" name="nomSousListe"
                            [ngbTypeahead]="searchSousListeProduit" [resultFormatter]="sousListeFormatter"
                            [inputFormatter]="sousListeFormatter">
                    </ng-container>

                    <button (click)="viderProduitForm()" type="button"
                        class="btn btn-warning text-white mx-1 prd-btn">Vider</button>
                    <button type="submit" class="btn btn-primary mx-1 text-white prd-btn">Rechercher</button>
                </div>
            </div>
        </form>

        <ng-container [ngTemplateOutlet]="produitTemplate" [ngTemplateOutletContext]="{height: 445}"></ng-container>
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-success mx-1" (click)="ajouterProduitsSelectionnes()">Ajouter
            Sélection</button>
        <button type="button" class="btn btn-outline-dark mx-1" (click)="modal.dismiss('Cross click')">Fermer</button>
    </div>
</ng-template>

<!-- Produit grid-template start -->
<ng-template #produitTemplate let-height="height">
    <div class="p-0 m-0"
        [id]="(currentPlateforme$ | async) === 'WIN_OFFRE' ? 'WIN_OFFRE-container' : (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT' ? 'FEDERATION_SYNDICAT-container' : 'WIN_GROUPE-container'">
        <kendo-grid class="fs-grid fs-listing-grid" [data]="produitsView" [pageable]="true"
            [pageSize]="!produitsListeSelectionne ? navigation.pageSize :( produitsView?.total || 20)"
            [skip]="!produitsListeSelectionne ? navigation.skip : 0" [height]="height" [sortable]="{ mode: 'single'}"
            [sort]="produitSort" (cellClick)="onCellClick($event)" (sortChange)="produitSortChange($event)"
            (pageChange)="pageProduitChange($event)" [resizable]="true"
            [selectable]="{mode: 'multiple', checkboxOnly: true}" kendoGridSelectBy="id"
            [selectedKeys]="selectedProduitIds" (selectionChange)="chooseProduit($event)">
            <kendo-grid-checkbox-column class="no-ellipsis" headerClass="no-ellipsis" [width]="40"
                [showSelectAll]="true"></kendo-grid-checkbox-column>

            <kendo-grid-column [width]="120" field="codeProduitCatalogue" title="Code">
                <ng-template kendoGridHeaderTemplate let-column>
                    <app-grid-sort-header [direction]="navigation.sortMethod!"
                        [active]="navigation.sortField === column.field" [title]="column.title"
                        type="numeric"></app-grid-sort-header>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="350" field="libelleProduit" class="text-wrap" title="Libellé">
                <ng-template kendoGridHeaderTemplate let-column>
                    <app-grid-sort-header [direction]="navigation.sortMethod!"
                        [active]="navigation.sortField === column.field" [title]="column.title"
                        type="alpha"></app-grid-sort-header>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="150" title="Laboratoire" class="text-wrap">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.fournisseur?.libelle }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="120" field="ppv" title="PPV" class="text-end">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    {{dataItem.ppv | number:'1.2-2':'fr-FR'}}
                </ng-template>

                <ng-template kendoGridHeaderTemplate let-column>
                    <app-grid-sort-header [direction]="navigation.sortMethod!"
                        [active]="navigation.sortField === column.field" [title]="column.title"
                        type="numeric"></app-grid-sort-header>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="120" field="prixVenteTtc" title="PPH" class="text-end">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    {{dataItem.prixVenteTtc | number:'1.2-2':'fr-FR'}}
                </ng-template>

                <ng-template kendoGridHeaderTemplate let-column>
                    <app-grid-sort-header [direction]="navigation.sortMethod!"
                        [active]="navigation.sortField === column.field" [title]="column.title"
                        type="numeric"></app-grid-sort-header>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
                pagerItemsPerPage="éléments par page"></kendo-grid-messages>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>

            <ng-container *ngIf="(currentPlateforme$ | async) === 'WIN_OFFRE'">
                <kendo-grid-messages pagerItems="lignes" pagerOf="de"
                    pagerItemsPerPage="éléments par page"></kendo-grid-messages>
            </ng-container>

            <ng-container *ngIf="(currentPlateforme$ | async) !== 'WIN_OFFRE'">
                <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
                    let-total="total">
                    <wph-grid-custom-pager [totalElements]="total"
                        [totalPages]="!produitsListeSelectionne ? totalPages : 1" [currentPage]="currentPage"
                        [navigation]="!produitsListeSelectionne ? navigation : {pageSize: produitsView?.total || 20, skip: 0}"
                        style="width: 100%;" (pageChange)="pageProduitChange($event)"></wph-grid-custom-pager>
                </ng-template>
            </ng-container>
        </kendo-grid>
    </div>
</ng-template>
<!-- Produit grid-template end -->