import { ScrollCheckDirective } from './directives/scroll-check.directive';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { CommonModule, DatePipe, DecimalPipe } from "@angular/common";
import { ReactiveFormsModule, FormsModule } from "@angular/forms";


import { SharedModule } from "@wph/shared";
import { NgbNavModule, NgbToastModule } from "@ng-bootstrap/ng-bootstrap";
import { HasAnyAuthorityDirective } from "./directives/has-any-authority.directive";
import { InputReaderComponent } from "./components/input-reader/input-reader.component";
import { ConfirmComponent } from "./components/confirm/confirm.component";
import { PdfViewerComponent } from "./components/pdf-viewer/pdf-viewer.component";
import { ElementStatusComponent } from "./components/element-status/element-status.component";

import {
  NgbTooltipModule,
  NgbA<PERSON>rdionModule,
  NgbDropdownModule,
  NgbModalModule,
  NgbProgressbarModule,
  NgbCarouselModule,
  NgbTypeaheadModule,
  NgbDatepickerModule,
  NgbPopoverModule
} from "@ng-bootstrap/ng-bootstrap";
import { GridModule } from "@progress/kendo-angular-grid";
import { WidgetModule } from "./widget/widget.module";
import { ArticleCarouselComponent } from "./components/article-carousel/article-carousel.component";
import { BlocOffreConditionsComponent } from "./components/bloc-offre-conditions/bloc-offre-conditions.component";
import { PaliersViewComponent } from "./components/paliers-view/paliers-view.component";
import { TypeaheadComponent } from "./components/typeahead/typeahead.component";
import { StatComponent } from "./components/stat/stat.component";
import { FlexModule } from "@angular/flex-layout";
import { FilterValuesPipe } from "./pipes/filterValues.pipe";
import { Select2Module } from "ng-select2-component";
import { DatePickerComponent } from "./components/date-picker/date-picker.component";
import { FormControlErrorDirective, ValidatorErrorPipe } from "./directives/form-control-error.directive";
import { UserRolePipe } from "./pipes/user-role.pipe";
import { FluxPipe } from "./pipes/flux.pipe";
import { HasAnyServiceOptionDirective } from "./directives/has-any-service-option.directive";
import { AbsPipe } from "./pipes/abs.pipe";
import { ScrollListenerDirective } from "./directives/scroll-listener.directive";
import { IsCurrentPlatformDirective } from "./directives/is-current-platform.directive";
import { InputsModule } from '@progress/kendo-angular-inputs';
import { SwitchComponent } from "./components/switch/switch.component";
import { ExportPdfModalComponent } from "./components/export-pdf-modal/export-pdf-modal.component";
import { ExportPdfComponent } from "./components/export-pdf/export-pdf.component";
import { WidgetsOffresComponent } from "./components/widgets-offres/widgets-offres.component";
import { AutoFocusDirective } from "./directives/auto-focus.directive";
import { FocusTrapDirective } from "./directives/focus-trap.directive";
import { AjouterCadeauComponent } from "./components/ajouter-cadeau/ajouter-cadeau.component";
import { FocusTrapPrefixedDirective } from "./directives/focus-trap-with-prefix.directive";
import { ConditionsDynamiquesComponent } from "./components/conditions-dynamiques/conditions-dynamiques.component";
import { NumberInputSubmitEventTriggerDirective } from "./directives/number-input-submit-event-trigger.directive";
import { AllowOnlyNumbersDirective } from './directives/number-validator.directive';
import { PackComponent } from './components/gestion-offre/pack/pack.component';
import { BlocProduitComponent } from './components/gestion-offre/bloc-produits/bloc-produit.component';
import { PacksManagerComponent } from './components/gestion-offre/packs-manager/packs-manager.component';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { OffreHeaderComponent } from './components/gestion-offre/offre-header/offre-header.component';
import { EditBlocOffreComponent } from './components/gestion-offre/edit-bloc-offre/edit-bloc-offre.component';
import { EditOffresComponent } from './components/gestion-offre/edit/edit-offres.component';
import { ConditionsSupporteurComponent } from './components/gestion-offre/conditions-supporteur/conditions-supporteur.component';
import { GroupeCardComponent } from './components/groupe-card/groupe-card.component';
import { ListeGroupeComponent } from './components/liste/liste-groupe.component';
import { ListeAnnoncesComponent } from './components/liste-annonces/liste-annonces.component';
import { IntersectionObserverDirective } from './directives/is-intersecting.directive';
import { MomentTimezonePipe } from './pipes/moment-timezone.pipe';
import { TimeAgoPipe } from './pipes/timeAgo.pipe';
import { ElapsedTimePipe } from './pipes/elapsed-time.pipe';
import { UnwrapModePaiementPipe } from './pipes/unwrap-mode-paiement.pipe';
import { CountDownTimerComponent } from './components/count-down-timer/count-down-time.component';
import { RechercheProduitGlobalComponent } from './components/recherche-produit-global/recherche-produit-global.component';
import { OrigineCommandePipe } from './pipes/origine-commande.pipe';
import { OutOfRangePipe } from './pipes/in-range.pipe';
import { FluxRangePipe } from './pipes/flux-range.pipe';
import { ElapsedTimeOutOfRange } from './pipes/elapsed-time-out-of-range.pipe';
import { TypeAssociationPipe } from './pipes/assoc.pipe';
import { ListeProduitsOcrComponent } from './components/gestion-offre/liste-produits-ocr/liste-produits-ocr.component';
import { EditListeProduitsComponent } from './components/gestion-offre/edit-liste-produits/edit-liste-produits.component';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { TargetPlateformeFromWinplusDirective } from './directives/winplus-target-plateforme.directive';
import { GridCellNavigationDirective } from './directives/grid-cell-navigation.directive';
import { AddEditPostModalComponent } from './components/add-edit-post-modal/add-edit-post-modal.component';
import { ListOrValueComponent } from './components/list-or-value/list-or-value.component';
import { OffreSliderComponent } from './components/offre-slider/offre-slider.component';
import { GoogleAnalyticsDataModule } from '@sophatel/google-analytics-data';
import { HasFeatureFlagDirective } from './directives/has-feature-flag.directive';
import { FeatureFlagDescriptionPipe } from './pipes/feature-flag-description.pipe';
import { PopoverTemplateComponent } from './components/popover-template/popover-template.component';
import { RechercheProduitComponent } from './components/gestion-offre/recherche-produit/recherche-produit.component';

@NgModule({
  declarations: [
    UserRolePipe, TypeAssociationPipe, GridCellNavigationDirective, OffreSliderComponent,
    InputReaderComponent, TimeAgoPipe, ElapsedTimeOutOfRange, EditListeProduitsComponent,
    ConfirmComponent, MomentTimezonePipe, UnwrapModePaiementPipe, ListeProduitsOcrComponent,
    PdfViewerComponent, AddEditPostModalComponent, RechercheProduitGlobalComponent,
    ConditionsDynamiquesComponent, ListeGroupeComponent, CountDownTimerComponent,
    SwitchComponent, EditBlocOffreComponent, EditOffresComponent, OrigineCommandePipe,
    ScrollListenerDirective, OffreHeaderComponent, GroupeCardComponent, OutOfRangePipe,
    ExportPdfModalComponent, PacksManagerComponent, ConditionsSupporteurComponent,
    ExportPdfComponent, PackComponent, BlocProduitComponent, ListeAnnoncesComponent, RechercheProduitComponent,
    WidgetsOffresComponent,NumberInputSubmitEventTriggerDirective, ElapsedTimePipe, FluxRangePipe,
    ArticleCarouselComponent, DatePickerComponent, AbsPipe, IntersectionObserverDirective, FeatureFlagDescriptionPipe,
    BlocOffreConditionsComponent,ListOrValueComponent,FormControlErrorDirective,ValidatorErrorPipe,
    BlocOffreConditionsComponent, PaliersViewComponent, TypeaheadComponent, StatComponent, ElementStatusComponent, FilterValuesPipe, FluxPipe,
    AutoFocusDirective,FocusTrapDirective,FocusTrapPrefixedDirective,ScrollCheckDirective,
    AjouterCadeauComponent,AllowOnlyNumbersDirective, PopoverTemplateComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    GridModule,
    SharedModule,
    NgbToastModule, HasFeatureFlagDirective,
    HasAnyAuthorityDirective, TargetPlateformeFromWinplusDirective,
    HasAnyServiceOptionDirective, DropDownsModule,
    IsCurrentPlatformDirective, NgbNavModule, GoogleAnalyticsDataModule,
    WidgetModule,Select2Module, InputsModule, NgxSliderModule,
    NgbTooltipModule, NgbAccordionModule, NgbDropdownModule, NgbModalModule, NgbProgressbarModule, NgbCarouselModule, NgbTypeaheadModule, NgbDatepickerModule, NgbPopoverModule, FlexModule
  ],
  exports: [
    UserRolePipe,
    ArticleCarouselComponent,
    GridModule, GridCellNavigationDirective,
    ReactiveFormsModule, TargetPlateformeFromWinplusDirective,
    HasAnyAuthorityDirective, NgxSliderModule, OffreSliderComponent,
    IsCurrentPlatformDirective, EditListeProduitsComponent, PopoverTemplateComponent,
    HasAnyServiceOptionDirective, ListeProduitsOcrComponent, FeatureFlagDescriptionPipe,
    SwitchComponent, ListeGroupeComponent, TypeAssociationPipe, NgbNavModule,
    ExportPdfModalComponent, GroupeCardComponent, ElapsedTimeOutOfRange, HasFeatureFlagDirective,
    PdfViewerComponent, ConditionsSupporteurComponent, RechercheProduitGlobalComponent,
    ExportPdfComponent, EditOffresComponent, ListeAnnoncesComponent, OutOfRangePipe,
    WidgetsOffresComponent, EditBlocOffreComponent, MomentTimezonePipe, OrigineCommandePipe,
    ScrollListenerDirective, PacksManagerComponent, AddEditPostModalComponent, CountDownTimerComponent,
    AjouterCadeauComponent,NumberInputSubmitEventTriggerDirective, TimeAgoPipe, UnwrapModePaiementPipe,
    ConditionsDynamiquesComponent, PackComponent, BlocProduitComponent, IntersectionObserverDirective, RechercheProduitComponent,
    BlocOffreConditionsComponent, AbsPipe, InputsModule, OffreHeaderComponent, ElapsedTimePipe, FluxRangePipe, GoogleAnalyticsDataModule,
    BlocOffreConditionsComponent, PaliersViewComponent,ListOrValueComponent,DatePickerComponent,FormControlErrorDirective,ValidatorErrorPipe,
    NgbTooltipModule, NgbAccordionModule, NgbDropdownModule, NgbModalModule, NgbProgressbarModule, NgbCarouselModule, NgbTypeaheadModule, NgbDatepickerModule, NgbPopoverModule,
    TypeaheadComponent, StatComponent, ElementStatusComponent, FilterValuesPipe, Select2Module, FluxPipe,AutoFocusDirective,FocusTrapDirective,FocusTrapPrefixedDirective,ScrollCheckDirective,
    AllowOnlyNumbersDirective

  ],
  providers: [
    ValidatorErrorPipe,
    DecimalPipe,
    DatePipe
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class WebSharedModule {
}
