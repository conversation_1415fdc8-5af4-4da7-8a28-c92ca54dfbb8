.b-radius {
    border-radius: 10px;
}

.opacity-light-cstm {
    opacity: 0.6;
    pointer-events: none !important;
}

.input-group-append {
    button {
        border-top-right-radius: 10px !important;
        border-bottom-right-radius: 10px !important;
    }
}

.opacity-light {
    opacity: 0.4;
    pointer-events: none !important;
}

#modRow, #modRowLocal {
    ::ng-deep #client-picker-input {
        .dropdown-menu {
            width: calc(100% + 5px) !important;
        }
    }
}

.bg-even {
    background: #ffeac4 !important;
}

.bg-odd {
    background: #f0a10f84 !important;
}

::ng-deep #DEFAULT-container {
    #client-groupe-grid .k-grid-toolbar {
        background-color: #facc77 !important;
        overflow: visible !important;
    }

    #client-local-grid .k-grid-toolbar {
        background-color: #9dc39f !important;
        overflow: visible !important;
        border-top-left-radius: 10px !important;
        border-top-right-radius: 10px !important;
    }
} 