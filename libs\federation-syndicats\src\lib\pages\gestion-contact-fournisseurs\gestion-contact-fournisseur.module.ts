import { CUSTOM_ELEMENTS_SCHEMA, NgModule, NO_ERRORS_SCHEMA } from "@angular/core";
import { GestionContactFournisseurRoutingModule } from "./gestion-contact-fournisseur-routing.module";
import { CommonModule } from "@angular/common";
import { NgbNavModule } from "@ng-bootstrap/ng-bootstrap";
import { WebSharedModule } from "@wph/web/shared";
import { ListeContactFournisseurComponent } from "./liste/liste-contact-fournisseur.component";
import { EditContactFournisseurComponent } from "./edit/edit-contact-fournisseur.component";
import { SharedModule } from "@wph/shared";

@NgModule({
    schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
    declarations: [ListeContactFournisseurComponent, EditContactFournisseurComponent],
    imports: [WebSharedModule, GestionContactFournisseurRoutingModule, CommonModule, SharedModule, NgbNavModule],
})
export class GestionContactFournisseurModule { }