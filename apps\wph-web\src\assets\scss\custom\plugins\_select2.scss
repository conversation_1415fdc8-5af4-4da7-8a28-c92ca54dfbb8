//
// Select 2
//

.select2-selection {
    border: $input-border-width solid $input-border-color !important;
    height: $input-height !important;
    background-color: $input-bg !important;
    outline: none !important;

    .select2-selection__rendered {
        line-height: 36px !important;
        padding-left: 12px !important;
        color: black !important;
    }

    .select2-selection__arrow {
        height: 34px !important;
        width: 34px !important;
        right: 3px !important;
        b {
            border-color: $gray-600 transparent transparent transparent !important;
            border-width: 6px 6px 0 6px !important;
        }
    }
}

.select2-focused {
    box-shadow: none !important;
    outline: none !important;
}

.select2-dropdown {
    border: $dropdown-border-width solid $dropdown-border-color !important;
    border-top: none !important;
    box-shadow: $shadow !important;
    background-color: $dropdown-bg !important;
}

.select2-container--default {
    .select2-search--dropdown {
        padding: 10px !important;
        background-color: lighten($dropdown-bg,2.5%) !important;
        .select2-search__field {
            outline: none !important;
            border: 1px solid $input-border-color !important;
            background-color: $input-bg !important;
            color: $input-color !important;
        }
    }
    .select2-results__option--highlighted[aria-selected] {
        background-color: $primary !important;
    }
    .select2-results__option[aria-selected=true] {
        background-color: lighten($dropdown-bg,2.5%) !important;
        color: $dropdown-link-active-color !important;
        &:hover {
            background-color: $primary !important;
            color: $white !important;
        }
    }
}

.select2-container {
    width: 100% !important;
    .select2-selection--multiple {
        min-height: $input-height !important;
        height: auto !important;
        border-radius: 10px !important;
        border: $input-border-width solid $input-border-color !important;
        background-color: $input-bg !important;

        .select2-selection__rendered {
            padding: 1px 10px !important;
        }
        .select2-search__field {
            border: 0 !important;
            color: $input-color !important;
        }
        .select2-selection__choice {
            background-color: $primary !important;
            border: none !important;
            color: $white !important;
            border-radius: 3px !important;
            padding: 0 7px !important;
            margin-top: 6px !important;
            line-height: 21px !important;
        }
        .select2-selection__choice__remove {
            color: $white !important;
            margin-right: 5px !important;
            &:hover {
                color: $white !important;
            }
        }
    }
    .select2-search--inline {
        .select2-search__field {
            margin-top: 7px;
        }
    }
}