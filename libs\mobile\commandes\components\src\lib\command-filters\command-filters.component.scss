

.content-container {
  padding: 5px;
  margin: 5px;
  height: 49%;
  position: relative;

  .button-container {
    position: absolute;
    bottom: 0;
    width: calc(100% - 10px);
  }
}

ion-popover {
  --width: calc(100vw - 20px);
  --top: calc(100vh - 300px) !important;
}

ion-segment-button::part(indicator-background) {
  background: #3DA5D9;
}

/* iOS styles */
ion-segment-button.ios::part(native) {
  color: #3DA5D9;
  font-weight: 500 !important;
}

.segment-button-checked.ios::part(native) {
  color: #fff;
}


ion-segment-button.ios::part(indicator-background) {
  border-radius: 20px;
}

.popover-btn {
  font-size: 12px; 
  font-weight: 500; 
  padding:2px; 
  color: #3DA5D9 !important;
}

.popover-btn-alt {
  color: #fff !important;
  --background: #3DA5D9 !important;
}

ion-datetime-button::part(native) {
  color: #3DA5D9;
  font-weight: 500; 
  background: #3da5d911;
  font-size: 14px;
}

ion-datetime-button.is-dirty::part(native) {
  color: #fff !important;
  font-weight: 500; 
  background: #3DA5D9 !important;
  font-size: 14px;
}

.filter-section-title {
  color: #767575 !important;
  font-size: 14px;
  font-weight: 600 !important;
}

.innner-section-label {
  color: #8c8e90 !important; 
  margin-right: 5px !important; 
  font-size: 14px !important;
}

.chip-default {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #3DA5D9;
  font-weight: 300;
  border: 1px solid #3DA5D9;
  --background: #3da5d911;
  padding: 5px 10px !important;

  span {
    min-height: 15px;
    min-width: 15px;
    border-radius: 40px;
    margin-right: 5px;
  }

  span.warning {
    background: rgba(232, 144, 61, 0.5);
  }

  span.info {
    background: rgba(63, 166, 217, 0.5);
  }

  span.danger {
    background: rgba(231, 101, 101, 0.5);
  }

  span.success {
    background: rgba(93, 182, 127, 0.5);
  }
}

.chip-checked {
  font-weight: 500;
  color: darken(#3da5d9, 20%);
  border: 1px solid #3da5d9;
  --background: #3da5d958;

  span.warning {
    background: #e78e3a;
  }

  span.info {
    background: #3da5d9;
  }

  span.danger {
    background: #e76565;
  }

  span.success {
    background: #5db67f;
  }
}

ion-item {
  --min-height:22px !important;
  --padding-end: 8px;
  --inner-padding-end: 0px;
  --padding-start:0px !important;
  --border-color: #d8d8d8 !important;
  ion-input{
    padding-right:10px !important;
  }
}

ion-datetime {
  color: rgba(61, 165, 217, 0.4) !important;
}