import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FederationSyndicatsRoutingModule } from './federation-syndicats-routing.module';
import { WebSharedModule } from '@wph/web/shared';
import { SimpleStatCardComponent } from './components/simple-stat-card/simple-stat-card.component';
import { AccueilComponent } from './pages/accueil/accueil.component';
import { LinkCardComponent } from './components/link-card/link-card.component';
import { NewsCardComponent } from './components/news-card/news-card.component';
import { ActualitesComponent } from './pages/actualites/actualites.component';
import { ListePostesComponent } from '@wph/web/gestion-annonces';
import { NgbRatingModule } from '@ng-bootstrap/ng-bootstrap';
import { NgChartsModule } from 'ng2-charts';
import { YouTubePlayerModule } from '@angular/youtube-player';


@NgModule({
  declarations: [
    SimpleStatCardComponent,
    AccueilComponent,
    ActualitesComponent,
    NewsCardComponent,
    LinkCardComponent,
  ],
  imports: [
    CommonModule,
    FederationSyndicatsRoutingModule,
    WebSharedModule,
    ListePostesComponent,
    NgChartsModule,
    NgbRatingModule,
    YouTubePlayerModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class FederationSyndicatsModule {}
