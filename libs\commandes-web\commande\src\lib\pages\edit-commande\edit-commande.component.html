<!-- Start Of Header -->
<div class="rowline" style="margin-bottom: 2px;">
  <div class="page-title-box row d-lg-flex d-none">
    <div class="col-auto">
      <div class="row px-1 px-lg-2 py-2 py-lg-0 justify-content-start align-items-center">
        <h4 class="page-title fw-4 ps-2 d-md-block d-none">
          {{ pageType | pagetype }}
        </h4>
      </div>
    </div>

    <div class="col-12 col-md-auto px-0 py-1 mt-md-0 py-md-0 d-lg-flex d-none">
      <div class="row px-1 px-lg-2 py-0 justify-content-end align-items-center">

        <button [disabled]="!gridData?.total" (click)="saveCommande()"
          *ngIf="commande?.statut === 'BROUILLON' && pageType !== 'panier'"
          class="btn btn-sm btn-primary rounded-pharma mx-1 my-md-0 my-2 d-flex row" title="enregistrer">
          <i class="mdi mdi-content-save mr-lg-1 mr-0"></i>
          <span class="d-lg-block d-none">Enregistrer</span>
        </button>

        <button [disabled]="!gridData?.total" (click)="goValiderCommande()" *ngIf="commande?.statut === 'BROUILLON'"
          class="btn btn-sm btn-info rounded-pharma mx-1 my-md-0 my-2 d-flex row" title="valider commande">
          <i class="mdi mdi-plus mr-lg-1 mr-0 d-lg-none d-block"></i>
          <span class="d-lg-block d-none">Valider Commande</span>
        </button>

        <button *ngIf="commande?.isPanier" [disabled]="!gridData?.total" title="Vider Panier" type="button"
          (click)="deleteAllCommande()" class="btn btn-sm btn-danger rounded-pharma mx-1 my-md-0 my-2">
          <i class="mdi mdi-cart-off mdi-18px text-white d-lg-none d-block"></i>
          <span class="d-lg-block d-none">Vider Panier</span>
        </button>

        <button (click)="goBack()" class="btn btn-sm btn-dark rounded-pharma mx-1 my-md-0 my-2">
          <i class="mdi mdi-close"></i>
          Quitter
        </button>
      </div>
    </div>
  </div>
</div>
<!-- END HEADER -->

<div id="edit-commande" class="mx-0 mb-0 mt-lg-0 mt-2 p-0">
  <div class="card border-0 p-0 safe-height">
    <div class="card-header row justify-content-between pt-2 pb-0 pt-lg-0 px-0 mx-0 pb-1">
      <div class="d-flex col px-2">
        <div class="row w-100 flex-wrap mx-0" style="gap: 1.2rem" wphFocusTrap>
          <div class="animated mx-0 px-0 my-1 col-md-5 col-12 input-group picker-input" id="productContainerIpt">
            <input type="text" id="iptEditCmd" placeholder="Nouvelle ligne" class="form-control pl-4"
              [(ngModel)]="model" [ngbTypeahead]="$any(search)" (selectItem)="selectedProduct($any($event))"
              [resultFormatter]="formatter" [inputFormatter]="formatter" [editable]="false" appTypeaheadScrollFix
              ngbTooltip="Rechercher par désignation, prix, ou code barre de produit" triggers="manual" container="body"
              [autoClose]="false" container="body" (focus)="myTooltip.open(); myTooltip.placement = 'top-right'"
              (blur)="myTooltip.close()" tooltipClass="custom-tooltip-style" #myTooltip="ngbTooltip" />

            <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
          </div>

          <div class="mx-0 px-0 my-1 col-auto">
            <up-down-input (value)="searchAddQteCmd($event)" (sub)="subs($event)" targetID="searchAdd"
              [start]="addQteCmd">
            </up-down-input>
          </div>

          <div class="mx-0 px-0 my-1 col-auto">
            <button (click)="updateCommandesLocal()" class="btn btn-light rounded-pharma">
              Ajouter à la commande
            </button>
          </div>
        </div>

      </div>
    </div>

    <!-- START CARD BODY -->
    <div class="card-body p-0">
      <kendo-grid id="edit-commande-grid" [data]="gridData" [resizable]="true" [selectable]="false" scrollable="scrollable" [pageable]="false"
        [pageSize]="gridData?.total || 0">

        <ng-template kendoGridToolbarTemplate>
          <div class="d-flex row w-100 mx-0 p-0 justify-content-end align-items-center k-gap-2">
            <button (click)="fetchDisponibiliteProduitCommandeDansOffresDisponibles()" class="btn b-radius shadow-sm d-none d-lg-inline winoffre-btn-container">
              <i class="bi bi-patch-check-fill mr-1" style="font-size: 20px; line-height: 1; vertical-align: middle;"></i>
              Check disponibilité dans les offres
            </button>

            <button (click)="gridData?.total && checkDispoMultiLigne()"
              class="btn btn-warning check-dispo rounded-pharma shadow-sm d-none d-lg-inline m-0">
              <span class="mdi mdi-check mdi-18px"></span> Check Disponibilité Produits Commande
            </button>
          </div>
        </ng-template>

        <kendo-grid-column media="(max-width: 768px)" [title]="pageType | pagetype">

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <!---  Mobile Column Template  --->
            <dl>
              <dt class="my-2 limited-width">Produit: <span>{{ dataItem?.libelleProduit }}</span></dt>

              <dt class="my-2 limited-width">Quantité:
                <span class="ml-2">
                  <up-down-input [start]="dataItem?.qteCmd" [targetID]="dataItem?.codeProduitCatalogue"
                    (value)="getCommandeNumber($event)">
                  </up-down-input>
                </span>
              </dt>

              <dt class="my-2 limited-width">PPV: <span>{{ dataItem?.ppv | number: '1.2-2': 'fr-FR' }}</span></dt>

              <dt class="my-2 limited-width">PPH: <span>{{ dataItem?.prixVenteTtc | number: '1.2-2': 'fr-FR' }}</span>
              </dt>

              <dt class="my-2 limited-width">Taxe(%): <span>{{ dataItem?.tauxTva | number: '1.2-2': 'fr-FR' }}</span>
              </dt>

              <dt class="my-2 limited-width">Total PPH Net TTC: <span>{{ dataItem?.totalNetTtc | number: '1.2-2':
                  'fr-FR'
                  }}</span></dt>
              <dt class="my-2 limited-width">
                Disponibilité: <span *ngIf="dataItem?.disponibiliteCode" class="mdi mdi-circle mdi-18px {{
                  dataItem?.disponibiliteCode[0] === 'A'
                      ? 'text-success'
                      : dataItem?.disponibiliteCode[0] === 'B'
                      ? 'text-warning'
                      : dataItem?.disponibiliteCode[0] === 'C'
                      ? 'text-warning'
                      : 'text-danger'
                  }}">
                </span>
                <span *ngIf="dataItem?.disponibiliteCode" class="ml-1">
                  {{ dataItem?.disponibiliteLibelle || (dataItem?.disponibiliteCode | produitDispo) }}
                </span>
              </dt>

              <dt class="action-btns">
                <div class="d-flex row mx-0 justify-content-start">
                  <button (click)="checkDispoAtIndex(dataItem, rowIndex); $event.stopPropagation()"
                    class="circle circle-alt btn btn-success text-white" title="Disponibilité de la quantité">
                    <i class="mdi mdi-magnify mdi-12px pointer"></i>
                  </button>

                  <button class="circle circle-alt btn btn-danger text-white mt-2" title="Supprimer"
                    (click)="deleteCommande(dataItem?.codeProduitCatalogue); $event.stopPropagation()">
                    <i class="mdi mdi-delete-forever mdi-12px pointer"></i>
                  </button>
                </div>
              </dt>
            </dl>
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" title="Produit" class="text-left text-wrap" [width]="250">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.libelleProduit }}
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" title="Qté" class="text-center" [width]="150">
          <ng-template kendoGridCellTemplate let-dataItem>
            <up-down-input [start]="dataItem?.qteCmd" [targetID]="dataItem?.codeProduitCatalogue"
              (value)="getCommandeNumber($event)">
            </up-down-input>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" title="PPV" field="ppv" class="text-right" [width]="150">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.ppv | number: '1.2-2': 'fr-FR' }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" title="PPH" field="prixVenteTtc" class="text-right" [width]="150">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.prixVenteTtc | number : '1.2-2' : 'fr-FR' }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" title="Taxe(%)" field="tauxTva" class="text-right" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.tauxTva | number: '1.2-2' : 'fr-FR' }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" class="text-right" [width]="150">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-right">Total PPH Net TTC</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.totalNetTtc | number: '1.2-2' : 'fr-FR' }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" title="Disponibilité" class="text-left" [width]="180">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span class="d-flex align-items-center" style="gap: 5px">
              <span *ngIf="dataItem?.disponibiliteCode" class="mdi mdi-circle mdi-18px {{
                dataItem?.disponibiliteCode[0] === 'A'
                    ? 'text-success'
                    : dataItem?.disponibiliteCode[0] === 'B'
                    ? 'text-warning'
                    : dataItem?.disponibiliteCode[0] === 'C'
                    ? 'text-warning'
                    : 'text-danger'
                }}">
              </span>
              <span *ngIf="dataItem?.disponibiliteCode" class="ml-1 text-wrap">
                {{ dataItem?.disponibiliteLibelle || (dataItem?.disponibiliteCode | produitDispo) }}
              </span>
            </span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" title="Action" class="text-center" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <button (click)="checkDispoAtIndex(dataItem, rowIndex); $event.stopPropagation()"
              class="circle btn btn-success text-white mx-1" title="Disponibilité de la quantité">
              <i class="mdi mdi-magnify"></i>
            </button>

            <button class="circle btn btn-danger text-white" title="Supprimer"
              (click)="deleteCommande(dataItem?.codeProduitCatalogue); $event.stopPropagation()">
              <i class="mdi mdi-delete-forever mdi-12px pointer"></i>
            </button>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

        <ng-template kendoGridNoRecordsTemplate>
          <span>Il n'y a pas d'éléments à afficher.</span>
        </ng-template>
      </kendo-grid>
    </div>
    <!-- END CARD BODY -->

    <div class="card-footer my-0 mx-0 px-0 py-1 gap-2 h4 row d-flex justify-content-end">
      <span class="col-lg-auto col text-right text-dark py-1 px-2 m-1 synthese-container">
        <span>Qté Totale:</span> {{ qteTotale | number: '1.0-0' : 'fr-FR' }}
      </span>

      <span class="col-lg-auto col text-right text-dark py-1 px-2 m-1 synthese-container">
        <span>Total PPH Net TTC:</span> {{ CmdNetTtc ? (CmdNetTtc | number: '1.2-2' : 'fr-FR') + ' DH' : 0 }}
      </span>
    </div>

  </div>
</div>

<ng-template #disponibiliteProduitModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title text-dark">{{ 'Disponibilité des produits dans les offres disponibles' | uppercase }}</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body overflow-y-auto">
    <div class="card shadow-sm b-radius p-1 mb-2" *ngFor="let offre of uniqueOffres; let i = index">
      <div class="row d-flex justify-content-between w-100 mx-0">
        <div class="col-auto p-0 d-flex align-items-start">
          <span class="img-wrapper p-2 b-radius d-flex align-items-center justify-content-center">
            <img height="50" width="50" src="assets/images/winplus_offre_mini.svg" />
          </span>
        </div>

        <div class="col d-flex align-items-center">
          <p class="p-0">
            <span class="badge b-radius badge-alt h6 mt-0 badge-warning px-2 py-1">
              <i class="mdi mdi-tag mr-1"></i>
              Remise allant jusqu'à {{ offre?.maxRemiseFinancier | number: '1.0-0' : 'fr-FR' }}%
            </span> <br>

            <span class="h4 w-100 my-0 text-wrap text-dark truncate-two-lines">
              {{ offre?.titre | uppercase }}
            </span>
          </p>
        </div>

        <div class="col-auto m-0 d-flex align-items-center justify-content-end px-0">
          <button (click)="modal.dismiss(); navigateToOffre(offre?.id)" class="btn btn-rounded btn-warning h6 text-white">
            <i class="mdi mdi-cart mr-1"></i> <span>Commander</span>
          </button>
        </div>
      </div>

      <div class="row d-flex justify-content-between mb-1 mt-2 mx-0 p-0">
        <span class="text-dark m-0 h6">OFFRE VALABLE JUSQU'AU: <u>{{ $any(offre?.dateFin) | date: 'dd/MM/yyyy' }}</u></span>
        <span class="text-dark m-0 h6">OFFERTE PAR: <u>{{ offre?.offreur?.raisonSociale }}</u></span>
      </div>

      <hr class="w-100 mt-0 mb-1">

      <div class="row d-flex flex-wrap w-100 mx-0 p-0 mb-1 k-gap-2">
        <span class="badge badge-primary badge-cstm py-1 px-2" *ngFor="let codeProduit of offreIdToCodeProduitMap[offre?.id]">{{
          gridProduitMap[codeProduit]?.libelleProduit }}</span>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Fermer</button>
  </div>
</ng-template>