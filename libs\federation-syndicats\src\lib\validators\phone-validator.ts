import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function phoneValidator(isOptional=false): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (isOptional && !control.value) {
      return null;
    }
    const phonePattern = /^(0[5678])\d{8}$/;
    const valid = phonePattern.test(control.value);
    return valid ? null : { invalidPhone: true };
  };
}

