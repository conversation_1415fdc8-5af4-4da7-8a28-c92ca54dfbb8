import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Inject, Injectable, NgZone } from '@angular/core';
import { AuthService } from './auth.service';
import { inscirptionRoutes } from 'libs/mobile/inscription/utils/inscirption.routes';
import { UserRole } from '@wph/shared';
import { Location } from '@angular/common';

export const VERIFICATION_ID = 'verificationId';

@Injectable({
  providedIn: 'root',
})
export class AuthGuardService implements CanActivate {
  constructor(
    private router: Router,
    public zone: NgZone,
    private location: Location,
    private authService: AuthService,
    @Inject('ENVIROMENT') private envir: any
  ) { }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    let authorities = route.data['authorities'] as Array<UserRole>;

    let ret = false; // no access by default
    let userMessage = '';

    const isVerifying = this.isVerifying();
    ret = this.authService.isAuthenticated();

    if (isVerifying && this.envir?.platform === 'MOBILE') {
      this.router.navigateByUrl(
        '/inscription/' + inscirptionRoutes.authorizationWait
      );
      return null;
    }

    if (!ret) {
      // not authenticated
      console.warn('you are not authenticated!');
      userMessage = 'Please Sign In!';
      const exitedWinPlusSession = sessionStorage.getItem('exitedWinPlusSession');

      if (!!exitedWinPlusSession) {
        sessionStorage.removeItem('exitedWinPlusSession'), this.location.back();
      } else {
        this.router.navigateByUrl('/auth/login');
      }
    } else if (authorities) {
      //  check if there is authorities data
      ret = this.authService.hasAnyAuthority(authorities);
      if (!ret) {
        console.warn('you dont have authorities = [' + authorities + ']  !');
        userMessage =
          'Your permissions do not allow you to access that resource.';
        this.router.navigateByUrl('/page/error/page-not-found');
      }
    }

    return ret;
  }

  getResolvedUrl(route: ActivatedRouteSnapshot): string {
    return route.pathFromRoot
      .map((v) => v.url.map((segment) => segment.toString()).join('/'))
      .join('/');
  }

  isVerifying() {
    return localStorage.getItem(VERIFICATION_ID) != null;
  }
}
