export interface Step {
  text: string;
  substeps?: Step[];
}

export interface Image {
  url: string;
  alt: string;
  caption?: string;
}

export interface Video {
  youtubeId: string;
  title: string;
}

export interface Example {
  title: string;
  content: string;
}

export interface FaqAnswer {
  id: number;
  question: string;
  textContent?: string;
  steps?: Step[];
  images?: Image[];
  video?: Video;
  examples?: Example[];
}

export interface Faq {
  id: number;
  category: string;
  answers: FaqAnswer[];
}
