{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["plugin:@nrwl/nx/angular", "plugin:@angular-eslint/template/process-inline-templates"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "wph", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "wph", "style": "kebab-case"}], "@angular-eslint/component-class-suffix": ["error", {"suffixes": ["Page", "Component"]}], "@angular-eslint/no-empty-lifecycle-method": 0, "@typescript-eslint/no-empty-function": 0}}, {"files": ["*.html"], "extends": ["plugin:@nrwl/nx/angular-template"], "rules": {}}]}