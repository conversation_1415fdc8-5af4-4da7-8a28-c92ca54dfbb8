import { NgModule } from "@angular/core";
import { LivraisonRoutingModule } from "./livraison-routing.module";
import { WebSharedModule } from "@wph/web/shared";
 import { SharedModule } from "@wph/shared";
import { CommonModule } from "@angular/common";
import { ListeBonsLivraisonComponent } from "./liste/liste-bons-livraison.component";
import { EditBonLivraisonComponent } from "./edit/edit-bon-livraison.component";
import { NgbNavModule } from "@ng-bootstrap/ng-bootstrap";
import { BLDispatchComponent } from "./dispatch/bl-dispatch.component";
import { NgxMaskModule } from "ngx-mask";
import { CommandeListBlsComponent } from "./commande-list-bls/commande-list-bls.component";

@NgModule({
    declarations: [ListeBonsLivraisonComponent, EditBonLivraisonComponent,BLDispatchComponent,CommandeListBlsComponent],
    imports: [LivraisonRoutingModule, WebSharedModule, SharedModule, CommonModule,NgbNavModule,NgxMaskModule.forRoot(),]
})
export class LivraisonModule {}
