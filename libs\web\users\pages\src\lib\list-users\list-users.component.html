<!-- Start Of Header -->
<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-2">Utilisateurs</h4>

    <div class="col-10 px-1">
      <div class="row justify-content-end align-items-center">
        <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
          <app-button-upload-file style="visibility: hidden;" [fileOnly]="true"
            fileAccept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            (results)="getCsvFile($event)" title="Importer Utilisateurs" [isMulti]="false" subject="logo-poste"
            #uploadFileBtn></app-button-upload-file>

          <button type="button" class="btn  btn-sm btn-success ml-1" (click)="uploadFileBtn.UploadFile()">
            <i class="mdi mdi-cloud-upload mr-1"></i>Importer Utilisateurs
          </button>
        </ng-container>

        <button *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_SUPER_ADMIN', 'ROLE_AGENT_POINT_VENTE']"
          type="button" class="btn  btn-sm btn-primary-offre ml-1" (click)="(selectedUser = null); openEditUserModal()">
          <i class="mdi mdi-plus mr-1"></i>Ajouter
        </button>

        <button type="button" class="btn  btn-sm btn-info mx-1" (click)="openFilterModal(filterModal, 'lg')">
          <i class="mdi mdi-filter-variant mr-1"></i>Filtrer
        </button>
      </div>
    </div>
  </div>
</div>
<!-- END HEADER -->


<div class="container-fluid m-0 p-0">
  <kendo-grid class="border-grey px-0 card" style="min-height: calc(100vh - 123px);" [data]="gridData"
    [sortable]="{ mode: 'single' }" [sort]="userSort" (sortChange)="userSortChange($event)"
    (pageChange)="pageChange($event)" [pageSize]="navigation.pageSize" [skip]="navigation.skip" [resizable]="true"
    [pageable]="{
      buttonCount: 5,
      info: true,
      type: 'numeric',
      pageSizes: pageSizes,
      previousNext: true,
      position: 'bottom'
    }">
    <kendo-grid-column [width]="150" field="username" title="Username" class="text-wrap"></kendo-grid-column>

    <kendo-grid-column [width]="150" class="text-wrap" field="lastname" title="Nom"></kendo-grid-column>

    <kendo-grid-column [width]="150" class="text-wrap" field="firstname" title="Prénom"></kendo-grid-column>

    <kendo-grid-column [width]="180" class="text-wrap" field="email" title="Email"></kendo-grid-column>

    <kendo-grid-column [width]="180" class="text-wrap" field="societe.raisonSociale" title="Société">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.entrepriseDTO?.raisonSociale }}
      </ng-template>

    </kendo-grid-column>

    <kendo-grid-column [width]="180" title="Role Utilisateur" [sortable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.entrepriseDTO?.typeEntreprise | userRole : dataItem?.role?.label }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column [width]="100" [sortable]="false" field="enabed" title="Statut" class="text-center no-ellipsis">
      <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
        <app-element-status [state]="dataItem.enabled"></app-element-status>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column [width]="80" [sortable]="false" title="Action" class="text-center no-ellipsis">
      <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
        <div class="d-flex justify-content-center">
          <span class="circle btn-primary user-select-none" (click)="(selectedUser = dataItem); openEditUserModal()">
            <i class="mdi mdi-pencil pointer-cus"></i>
          </span>

          <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']"
            [ngTemplateOutlet]="activateDeactivate"></ng-container>

          <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_POINT_VENTE']">
            <ng-container
              *ngIf="dataItem?.role?.label !== 'ROLE_AGENT_FOURNISSEUR' && dataItem?.role?.label !== 'ROLE_AGENT_POINT_VENTE'"
              [ngTemplateOutlet]="activateDeactivate"></ng-container>
          </ng-container>

          <ng-template #activateDeactivate>
            <span class="circle user-select-none"
              [ngClass]="{'btn-danger': dataItem.enabled, 'btn-success': !dataItem.enabled}"
              (click)="changeUserStatut(dataItem)">
              <ng-container *ngIf="!dataItem.enabled">
                <i class="mdi mdi-account-check pointer-cus" placement="bottom" ngbTooltip="Activer"></i>
              </ng-container>

              <ng-container *ngIf="dataItem.enabled">
                <i class="mdi mdi-account-alert pointer-cus" placement="bottom" ngbTooltip="Désactiver"></i>
              </ng-container>
            </span>
          </ng-template>
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-messages pagerItems="lignes" pagerOf="de" pagerItemsPerPage="éléments par page"></kendo-grid-messages>

    <ng-template kendoGridNoRecordsTemplate>
      <span>Aucun résultat trouvé.</span>
    </ng-template>
  </kendo-grid>
</div>


<ng-template #filterModal let-modal>
  <wph-user-filter-modal [formGroup]="filterForm" (modalAction)="filterModalAction($event)"></wph-user-filter-modal>
</ng-template>

<ng-template #editUserModal let-modal>
  <app-add-user [user]="selectedUser"></app-add-user>
</ng-template>