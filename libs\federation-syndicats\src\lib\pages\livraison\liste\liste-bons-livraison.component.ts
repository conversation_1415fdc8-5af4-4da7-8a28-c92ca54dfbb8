import { Component } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ExportPdf,ExportPdfService,UserInputService } from "@wph/web/shared";
import { AlertService} from "@wph/shared";
import { GridDataResult, PageChangeEvent, SelectionEvent } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { Pagination } from "@wph/data-access";
import { FormControl, FormGroup } from "@angular/forms";
import { BLCriteria } from "../../../models/bl-criteria";
import { FsBLService } from "../../../services/fs-bl.service";
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from "rxjs";
import { AuthService } from "@wph/core/auth";
import { FederationSyndicatService } from "../../../services/federation-syndicats.service";
import { EnteteBlConsolideeMarcheDTO, EtatBlAchatGroupeEnum } from "../../../models/bl.model";

@Component({
    selector: 'wph-liste-bons-livraison',
    templateUrl: './liste-bons-livraison.component.html',
    styleUrls: ['./liste-bons-livraison.component.scss']
})
export class ListeBonsLivraisonComponent {

    displayFilter = false;

    BLFilterForm: FormGroup;

   filterList: FormControl = new FormControl();

    exportPdfRef:ExportPdf;

    gridData: GridDataResult = { total: 0, data: [] };

    unsubscribe$: Subject<boolean> = new Subject<boolean>();


    groupeSort: SortDescriptor[];
    pageSizes: number[] = [5, 10, 15, 20];
    navigation: Pagination = { pageSize: 15, skip: 0, sortField: 'dateCreation', sortMethod: 'desc' };

    isUnitaire = false;
    isIndividuelle = false;


    constructor(
        private router: Router,
        private exportPdfServ: ExportPdfService,
        private userInputService: UserInputService,
        private alertService: AlertService,
        private fsBLService:FsBLService,
        private authService: AuthService,
        private fsService:FederationSyndicatService,
    ) {
       if(this.router.url.includes('mes-bons')){
        this.isUnitaire = true;
      }else if (this.router.url.includes('individuelle')){
        this.isIndividuelle = true;
      }

    }

    get isInactive$() {
      return this.fsService.inactiveAccount$;
    }

    OnPageChange(event: number): void {
      if(this.isUnitaire){
        this.searchMyBL();
      }else if(this.isIndividuelle){
        this.searchIndividuelleBL();
      }
      else{
        this.searchBL();
      }

    }

    ngOnInit(): void {
          this.initFilterForm();
          this.buildExport();
          this.listenToSearchFilterChanges();
          if(this.isUnitaire){
            this.searchMyBL();
          }else if(this.isIndividuelle){
            this.searchIndividuelleBL();
          } else {
            this.searchBL();
          }
    }


    ngOnDestroy() {
      this.unsubscribe$.next(true);
      this.unsubscribe$.complete();
    }


    initFilterForm() {
        this.BLFilterForm = new FormGroup({
            numBl: new FormControl(null),
            numcommande: new FormControl(null),
            dateDebut: new FormControl(null),
            dateFin: new FormControl(null),
        });
    }

    searchBL() {
     this.fsBLService.searchBLConsolide(this.navigation,this.BuildFilterModel()).subscribe(res => {
          this.gridData.data = res.content;
          this.gridData.total = res.totalElements;
          this.exportPdfRef.setData(this.gridData.data);
     });
    }

    searchMyBL() {
      this.fsBLService.getMyBLsUnitaire(this.navigation,this.BuildFilterModel()).subscribe(res => {
        this.gridData.data = res.content;
        this.gridData.total = res.totalElements;
        this.exportPdfRef.setData(this.gridData.data);
      });
    }
    searchIndividuelleBL() {
      this.fsBLService.searchMyBLsIndividuel(this.navigation,this.BuildFilterModel()).subscribe(res => {
        this.gridData.data = res.content;
        this.gridData.total = res.totalElements;
        this.exportPdfRef.setData(this.gridData.data);
      });

    }

    listenToSearchFilterChanges(): void {
      this.filterList.valueChanges
        .pipe(
          takeUntil(this.unsubscribe$),
          debounceTime(200),
          distinctUntilChanged()
        )
        .subscribe((res: string) => {
          console.log("res", res);

          if (res?.length) {
            this.BLFilterForm.patchValue({ numBl: res });
             this.isUnitaire ? this.searchMyBL() : this.isIndividuelle ? this.searchIndividuelleBL() : this.searchBL();
          } else {
            this.BLFilterForm.patchValue({ numBl: null });
            this.isUnitaire ? this.searchMyBL() : this.isIndividuelle ? this.searchIndividuelleBL() : this.searchBL();
          }
        });
    }





    BuildFilterModel() {
      const CommandeSearchCreteria =  new BLCriteria({
        numeroBl: this.BLFilterForm.get('numBl').value,
        codeCommande: this.BLFilterForm.get('numcommande').value,
        dateReceptionDebut: this.BLFilterForm.get('dateDebut').value,
        dateReceptionFin: this.BLFilterForm.get('dateFin').value,
        supporteurCmdConsolideeId: this.isUnitaire ? undefined : this.authService.getPrincipal().societe?.id,
        etatBlAchatGroupe: this.isUnitaire ?  [EtatBlAchatGroupeEnum.VALIDE] : undefined,
        clientCmdId : this.isIndividuelle ? this.authService.getPrincipal().societe?.id : undefined


      });
      return CommandeSearchCreteria;

    }



    async AnullerBl(dataItem:EnteteBlConsolideeMarcheDTO,event:any) {
      event.stopPropagation();
      const groupe= await this.fsService.getMyGroupe()
      const groupeId = groupe.id;

      this.userInputService
      .confirmAlt('Confirmation', `Voulez-vous vraiment Supprimer Le BL ${dataItem.numeroBl} `)
      .then((confirmed) => {
        if (confirmed) {
          this.fsBLService.anuulerBLConsolide({blConsolideId:`${dataItem.id}`,groupeId}).subscribe(res=>{
            this.alertService.successAlt(`Le BL ${dataItem.numeroBl}  a été supprimé avec succès!`, 'Suppression de  BL', 'MODAL');
            this.gridData.data.forEach((item:any)=>{
              if(item.id === dataItem.id){
                item.etatBl = 'BANNULE';
              }
            })
          })
          this.searchBL();
        }
      });
    }


    AnullerBlIndividuel(dataItem:any,event: Event) {
      event.stopPropagation();

      this.userInputService
      .confirmAlt('Confirmation', `Voulez-vous vraiment Supprimer Le BL ${dataItem.numeroBl} `)
      .then((confirmed) => {
        if (confirmed) {
          this.fsBLService.anuulerBLIndividuel({blIndividuelId:`${dataItem.id}`}).subscribe(res=>{
            this.alertService.successAlt(`Le BL ${dataItem.numeroBl}  a été supprimé avec succès!`, 'Suppression de  BL', 'MODAL');
            this.gridData.data.forEach((item:any)=>{
              if(item.id === dataItem.id){
                item.etatBl = 'BANNULE';
              }
            })
          })
          this.searchIndividuelleBL();
        }
      });
    }


    filterCommandes() {
      this.isUnitaire ? this.searchMyBL() : this.isIndividuelle ? this.searchIndividuelleBL() : this.searchBL();
    }

    clearFilterCommandes() {
      this.BLFilterForm.reset();
      this.isUnitaire ? this.searchMyBL() : this.isIndividuelle ? this.searchIndividuelleBL() : this.searchBL();
    }



    sortChange(sort: SortDescriptor[]): void {
        this.groupeSort = sort;

        if (this.groupeSort && this.groupeSort.length > 0 && this.groupeSort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }
        if(this.isUnitaire){
          this.searchMyBL();
        }else if(this.isIndividuelle){
          this.searchIndividuelleBL();
        }else{
          this.searchBL();
        }
    }

    pageChange(event: PageChangeEvent): void {
        if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.navigation.pageSize = event.take;
        }
    }

    ajouterBonLivraison() {
      if(this.isIndividuelle){
        this.router.navigateByUrl('/achats-groupes/bons-livraison/individuelle/edit');
      }else{
        this.router.navigateByUrl('/achats-groupes/bons-livraison/edit');
      }
    }

    modifierBonLivraison(dataItem,event: Event) {
      event.stopPropagation();
      const numeroBL = dataItem?.id;
      const numeroCommande = this.isIndividuelle ? dataItem?.enteteCommandeIndividuelleDTO.id : dataItem?.enteteCommandeAchatGroupe.id;
      if(this.isIndividuelle){
        this.router.navigateByUrl(`/achats-groupes/bons-livraison/individuelle/${numeroCommande}/edit/${numeroBL}`);

      }else{
        this.router.navigateByUrl(`/achats-groupes/bons-livraison/${numeroCommande}/edit/${numeroBL}`);
      }
  }

    onRowClick(event: SelectionEvent) {

        const numeroBL = event.selectedRows[0]?.dataItem?.id;
        const numeroCommande = this.isIndividuelle ? event.selectedRows[0]?.dataItem?.enteteCommandeIndividuelleDTO.id : event.selectedRows[0]?.dataItem?.enteteCommandeAchatGroupe.id;

        if(this.isUnitaire){
          this.router.navigateByUrl(`/achats-groupes/bons-livraison/edit/${numeroBL}/unitaire?from=ls-sortie`);
        }else if(this.isIndividuelle){
          this.router.navigateByUrl(`/achats-groupes/bons-livraison/individuelle/${numeroCommande}/edit/${numeroBL}?from=ls-sortie&readOnly=true`);
        }
        else{
          this.router.navigateByUrl(`/achats-groupes/bons-livraison/${numeroCommande}/edit/${numeroBL}?readOnly=true`);
        }


    }



    deleteBonLivraison(dataItem: any,event: Event) {
        event.stopPropagation();
        this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir supprimer cette BL ?`)
        .then(
            () => {

                    this.alertService.successAlt(`Le BL BL555 a été supprimé avec succès!`, 'Suppression du Groupe', 'MODAL');

            },
            () => null
        );

    }


    buildExport(): void {
        this.exportPdfRef = this.exportPdfServ.ref()
        .setTitle('Liste des Bons de Livraison')
        .addColumn('numeroBl', 'N° BL',{
          transform: (value) => {
            return `BL-${value}`;
          }
        })
        .addColumn('codeCommande', 'N° Commande',{})
        .addColumn('dateCommande', 'Date Commande',{
            type: 'date'
        })
        .addColumn('dateReceptionBl', 'Date BL',{  type: 'date'})
        .addColumn('dateCreation', 'Date Création',{  type: 'date'})
        .addColumn('montantSaisi', 'Montant BL',{type:"decimal"})
        .addColumn('fournisseur', 'Distributeur',{keyAccessor:"raisonSociale"})
        .addColumn('etatBl', 'Etat',{})

        .setData(this.gridData.data)
    }
}
