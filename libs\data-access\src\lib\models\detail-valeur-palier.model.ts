import { DomainEnumeration } from "@wph/shared";
import { PalierValeurCadeau } from "./palier-valeur-cadeau.model";
import { TypeSelectionRfUgEnum } from "./type-selection-rf-ug.enum";



export class DetailValeurPalier {

    id?: number;


    
	// param condition
    valueRelatifPack?: boolean;     // default local (false)     or else packe (true) TODO: AGA take this for the UI


    
	// conditions
    valeurMin?: number;

    valeurMax?: number;
    valeurNette?: number;

    isValeurNet?: boolean;    // TODO: AGA  in UI/logiq

    qteMin?: number;

    qteMax?: number;

    nbrObjFilsMin?: number;

    nbrObjFilsMax?: number;

    nombreProduitsMin?: number;

    nombreProduitsMax?: number;

    reste?: string; // U: unique   T:tout
    multiple?: string; // O : oui multiple   C: oui multiple et cumuler  N:pas de multiple

    delaiPaiement?: DomainEnumeration;



    

	// valeur resultat si condition
    tauxRf?: number;

    tauxUg?: number;

    ratioUg?: string;

    
    typeSelectionRfUg?: TypeSelectionRfUgEnum;



    tauxEscompte?: number;    // TODO: AGA  take this in UI


    listCadeaux?: PalierValeurCadeau[];    // TODO: AGA  take this in UI




    // other fields
    selected?: boolean;

}
