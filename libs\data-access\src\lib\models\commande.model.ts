import { BlocOffre } from './bloc-offre.model';
import { Moment } from 'moment';
import { Fournisseur } from './fournisseur.model';
import { Offre } from './offre.model';
import { EtatCommande } from '@wph/federation-syndicats';

export type NatureCommande = 'GROUPEE' | 'INDIVIDUELLE';

export class Commande {

    id: number;
    codeCommande: string;
    dateDerogation: Moment;
    dateAnnulation: Moment;
    dateEnvoiDist: Moment;
    dateCommande: Moment;
    dateCreation: Moment;
    dateConfirmation?: Moment;
    dateSuppression?: Moment;
    dateAcceptation?: Moment;
    dateRefus?: Moment;
    enteteCommandeId?: number;
    dateLivraison?: Moment;
    dateValidation: Moment;
    isDerogation: string;
    listeDetails: BlocOffre[];
    accepterPalierInvalide?: string;
    lockedForNonBo?: string;
    motifAnnulation?: string;
    offre?: Offre;
    distributeur?: Fournisseur;
    qteTotale?: number;
    qteUg?: number;
    statut?: string;
    tauxRf?: number;
    tauxUg?: number;
    valeurCmdBruteHt?: number;
    valeurCmdBruteTtc?: number;
    valeurCmdBruteTva?: number;
    valeurCmdNetHt?: number;
    valeurCmdNetTtc?: number;
    valeurCmdNetTva?: number;
    remisettc: number;
    societeCreateur?: Fournisseur;
    annuleePar?: Fournisseur;
    client?: Fournisseur;
}

export class CommandeCriteria {
    id?: number;
    codeCommande?: string;
    titre?: string;
    dateDebutCommande?: Moment;
    dateFinCommande?: Moment;
    dateValidationDebut?: Moment;
    dateValidationFin?: Moment;
    fournisseur?: Fournisseur;
    laboratoire?: Fournisseur;
    statut?: string[];
    etatCommandes?: EtatCommande[];
    venteDirecteLabo?: string;
    nonExpireesUniquement?: string;
    raisonSocialClient?: string;
    client?: Fournisseur;
    raisonSocialTarget?: string;
    cmdFilterTarget?: string;
    createurId?: string;
    natureCommande?: string;
    codeSite?: number;
    codeOrRaisonSocialOfDis?: string;
    offreId?: number;

    constructor(partialCommandeCriteria?: Partial<CommandeCriteria>) {
        this.id = partialCommandeCriteria?.id || null;
        this.codeCommande = partialCommandeCriteria?.codeCommande || null;
        this.titre = partialCommandeCriteria?.titre || null;
        this.dateDebutCommande = partialCommandeCriteria?.dateDebutCommande || null;
        this.dateFinCommande = partialCommandeCriteria?.dateFinCommande || null;
        this.dateValidationDebut = partialCommandeCriteria?.dateValidationDebut || null;
        this.dateValidationFin = partialCommandeCriteria?.dateValidationFin || null;
        this.fournisseur = partialCommandeCriteria?.fournisseur || null;
        this.laboratoire = partialCommandeCriteria?.laboratoire || null;
        this.statut = partialCommandeCriteria?.statut || null;
        this.venteDirecteLabo = partialCommandeCriteria?.venteDirecteLabo || null;
        this.nonExpireesUniquement = partialCommandeCriteria?.nonExpireesUniquement || null;
        this.raisonSocialClient = partialCommandeCriteria?.raisonSocialClient || null;
        this.client = partialCommandeCriteria?.client || null;
        this.raisonSocialTarget = partialCommandeCriteria?.raisonSocialTarget || null;
        this.cmdFilterTarget = partialCommandeCriteria?.cmdFilterTarget || null;
        this.createurId = partialCommandeCriteria?.createurId || null;
        this.codeSite = partialCommandeCriteria?.codeSite || null;
        this.etatCommandes = partialCommandeCriteria?.etatCommandes || null;
        this.offreId = partialCommandeCriteria?.offreId || null;
        this.natureCommande = partialCommandeCriteria?.natureCommande || null;
        this.codeOrRaisonSocialOfDis = partialCommandeCriteria?.codeOrRaisonSocialOfDis || null;
    }
}

export type CommandeType =  'cmd-unitaire' | 'cmd-groupe' | 'cmd-individuelle';

export enum TYPE_CMD {
    GROUPE = 'cmd-groupe',
    UNITAIRE = 'cmd-unitaire',
    INDIVIDUELLE = 'cmd-individuelle'
  }

export class SelectedCommande {
    statut?: string;
    code?: string;
    date?: Moment;
    grossiste?: string;
    laboratoire?: string;
    readOnly?: boolean;
    isClientCommande?: boolean;

    constructor(commande: Commande) {
        this.code = commande?.codeCommande;
        this.date = commande?.dateCreation;
        this.statut = commande?.statut;
        this.grossiste = commande?.distributeur?.raisonSociale;
        this.laboratoire = commande?.offre?.laboratoire?.raisonSociale;
        this.isClientCommande = true;
    }
}

export class CommandeNormaleCriteria {
    clientId?: number;
    codeClientLocal?: string;
    codeSite?: number;
    dateDebut?: Moment;
    dateFin?: Moment;
    statuts?: string[];
    statutTraiteBl?: string[];
    nonEnvoyes?: boolean | null;
    dateValidationDebut?: Moment;
    dateValidationFin?: Moment;

    constructor(partialCommandeNormaleCriteria?: Partial<CommandeNormaleCriteria>) {
        this.codeSite = partialCommandeNormaleCriteria?.codeSite || null;
        this.dateDebut = partialCommandeNormaleCriteria?.dateDebut || null;
        this.dateFin = partialCommandeNormaleCriteria?.dateFin || null;
        this.dateValidationDebut = partialCommandeNormaleCriteria?.dateValidationDebut || null;
        this.dateValidationFin = partialCommandeNormaleCriteria?.dateValidationFin || null;
        this.clientId = partialCommandeNormaleCriteria?.clientId || null;
        this.statuts = partialCommandeNormaleCriteria?.statuts || null;
        this.codeClientLocal = partialCommandeNormaleCriteria?.codeClientLocal || null;
        this.statutTraiteBl = partialCommandeNormaleCriteria?.statutTraiteBl || null;
        partialCommandeNormaleCriteria?.nonEnvoyes && (this.nonEnvoyes = true);
    }
}

export class CheckProduitInOffreResponseDto  {
    codeProduitFournisseur: string;
    listOffre: Partial<Offre>[] | null = null;
}
