import { EnteteBl } from "./EnteteBl";

export class ListCommandesCriteria {
    content: Content[];
    pageable: Pageable;
    last: boolean;
    totalElements: number;
    totalPages: number;
    size: number;
    number: number;
    sort: Sort;
    numberOfElements: number;
    first: boolean;
    empty: boolean;
}

export interface Pageable {
    sort: Sort;
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
}

export interface Sort {
    sorted: boolean;
    unsorted: boolean;
    empty: boolean;
}

export interface Content {
    id: number;
    bl?: EnteteBl | null;
    societeId: number;
    idhash: string;
    isPanier: string;
    dateCommande: string;
    codeCommande: string;
    type?: any;
    statut?: string;
    dateValidation?: any;
    dateAnnulation?: any;
    motifAnnulation?: any;
    distributeur: Distributeur;
    clientId?: any;
    codeClientCatalogue?: any;
    raisonSociale?: any;
    nomPharmacien?: any;
    ville?: any;
    userCreateur: UserCreateur;
    userTraitement: UserCreateur;
    valeurCmdBruteHt?: any;
    valeurCmdBruteTtc?: any;
    valeurCmdNetHt?: any;
    valeurCmdNetTtc?: any;
    qteTotale: number;
    qteUg: number;
    tauxRf?: any;
    tauxUg?: any;
    commentaire?: any;
    lignes: Ligne[];
}

export interface Ligne {
    id: number;
    produitId: number;
    codeProduitCatalogue: string;
    libelleProduit: string;
    ppv: number;
    tauxTva: number;
    prixVenteHt: number;
    prixVenteTtc: number;
    qteCmd: number;
    tauxRemise: number;
    montantRemise: number;
    tauxUg: number;
    qteUg: number;
    totalBrutHt: number;
    totalBrutTtc: number;
    totalNetHt: number;
    totalNetTtc: number;
}

export interface UserCreateur {
    audited: boolean;
    idhash: string;
    firstname: string;
    lastname: string;
    city: City;
}

export interface City {
    audited: boolean;
    id: number;
    country: Country;
    labelEn: string;
    labelFr: string;
}

export interface Country {
    audited: boolean;
    id: number;
    labelEn: string;
    labelFr: string;
}

export interface Distributeur {
    id: number;
    nom: string;
}
