import { Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, CanActivateChild, Router, UrlTree } from "@angular/router";
import { AuthService } from "@wph/core/auth";
import { PlateformeService } from "@wph/shared";
import { DynamicColorPaletteService, BaseColorFournisseur, CodeSiteFournisseur } from "@wph/web/shared";

@Injectable({ providedIn: 'root' })
export class CanActivateCommandeWeb implements CanActivateChild {
    constructor(
        private router: Router, 
        private authService: AuthService, 
        private plateformeService: PlateformeService,
        private colorPaletteService: DynamicColorPaletteService,
    ) { }

    canActivateChild(route: ActivatedRouteSnapshot): boolean | UrlTree {
        const url = route['_routerState'].url;

        if (this.plateformeService.isPlateForme('COMMANDE_WEB') &&
            !!this.plateformeService.getCurrentGrossiste() ||

            (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']) &&
                this.plateformeService.isPlateForme('WIN_OFFRE') &&
                url.includes('bon-commande'))
        ) {
            const currentGrossiste = this.plateformeService.getCurrentGrossiste();
            if (BaseColorFournisseur[currentGrossiste?.noeud?.codeSite]) {
                this.colorPaletteService.setBaseColor(BaseColorFournisseur[currentGrossiste?.noeud?.codeSite]);
            } else {
                this.colorPaletteService.setBaseColor(BaseColorFournisseur[CodeSiteFournisseur.DEFAULT]);
            }
            
            return true;
        }

        return this.router.createUrlTree(['/pharma-lien']);
    }

}
