@import url('https://fonts.googleapis.com/css?family=Roboto');

/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";
@import "~leaflet/dist/leaflet.css";

.first-load-container {
  text-align: center;
  margin-top: 2em;
}
.error-ls {
  border: 2px red solid !important;
}
.matt {
  border: 2px green solid !important;
}

.matt-warning {
  border: 2px #F1A621 solid !important;
}

.cancel-event {
  color: red !important;
}

.ion-page {
  width: 100%;
  height: 100%;

}

.err-pb {
  padding-bottom: 42px;
}


.small-modal {
  --height: 200px;
  --width: 90%;
  --margin: 0 auto !important;
  --border-radius: 16px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.small-modal::part(backdrop) {
  background: rgba(209, 213, 219);
  opacity: 1;
}

.small-modal ion-toolbar {
  --background: rgb(14 116 144);
  --color: white;
}
.cancel-event {
  * {
    color: #a71410;
    --color: #a71410;
  }
}
.edit-event {
  * {
    color: black;
    --color: black;
  }
}
.cancel-action {
  *{
    color: #f62222 !important;
    --color: #f62222 !important;
    --ionicon-stroke-width: 40px !important;
    font-weight: 400;
  }
}

.custom-page-title {
  text-align: left;
  font: normal normal bold 28px/33px Arial;
  letter-spacing: 0px;
  color: #3DA5D9;
  opacity: 1;
  margin-top: 50px;
}

.ion-item-no-end-padding{
  --inner-padding-end:0px !important;
}

.select-holder {
  &ion-alert .alert-wrapper .alert-radio-group [aria-checked=true] .alert-radio-label {
    --color: #40a2d6 !important;
  }
}

.confirm-cmd {
  .alert-wrapper {
    .alert-head {
        // header styling here eg. background
        .alert-title {
          font-size:19px !important;
        }
        .alert-message{
          font-size:20px !important;
        }
    }
  }
}

.link-icon {
  width: 1.5rem !important;
  height: 0.8rem !important;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-image: url('./assets/images/link.svg');
  mask-image: url('./assets/images/link.svg');
  background: #000000;
}

.link-icon-alt {
  width: 1.5rem !important;
  height: 0.8rem !important;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-image: url('./assets/images/link.svg');
  mask-image: url('./assets/images/link.svg');
  background: #615A19;
}

.link-icon-invert {
  width: 1.5rem !important;
  height: 0.8rem !important;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-image: url('./assets/images/link.svg');
  mask-image: url('./assets/images/link.svg');
  background: #ffffff !important;
}

.grossiste-label > span {
  color: #F1A621 !important;
  font-weight: 500;
}
.labo-label > span {
  color: #1498da !important;
  font-weight: 500;
}

.client-label > span {
  color: #000000 !important;
  font-weight: 500;
}

iframe {
  width: -webkit-fill-available !important;
}
