import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from "@angular/core";
import { FormGroup, FormControl, FormBuilder } from "@angular/forms";
import { Router, ActivatedRoute } from "@angular/router";
import { GridDataResult, CellClickEvent } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { AuthService } from "@wph/core/auth";
import { CommandeCriteria, Pagination, OffresService } from "@wph/data-access";
import { SocieteType } from "@wph/shared";
import { ExportPdf, ExportPdfService } from "@wph/web/shared";
import { Subject, Observable, debounceTime, distinctUntilChanged, switchMap, of, map, takeUntil } from "rxjs";
import { EnteteCommandeView } from "../../../models/entete-commande.model";
import { EtatCommande } from "../../../models/fs-commande.model";
import { FsCommandesService } from "../../../services/fs-commandes.service";

@Component({
    selector: 'wph-toutes-commandes-admin',
    templateUrl: './toutes-commandes-admin.component.html',
    styleUrls: ['./toutes-commandes-admin.component.scss']
})
export class ToutesCommandesAdminComponent implements OnInit, OnDestroy {
    unsubscribe$: Subject<boolean> = new Subject<boolean>();

    displayFilter: boolean;
    filterForm: FormGroup;
    searchCriteria: CommandeCriteria = new CommandeCriteria();
    searchFilter: FormControl = new FormControl();
    exportPdfRef: ExportPdf;
    commandeSort: SortDescriptor[];
    gridData: GridDataResult;
    pageSizes: number[] = [5, 10, 15, 20];
    startsWith: RegExp = new RegExp('^CD-\\d*$', 'i');

    navigation: Pagination = {
        pageSize: 15,
        skip: 0
    };

    stautsLabelsValues: any[] = [
        { label: "Ouverte", value: " ,ACCEPTEE" },
        { label: 'Annulé', value: 'A,ANNULEE' },
        { label: 'Brouillon', value: 'B, ' },
        { label: "Cloturée", value: " ,CLOTUREE" },
        { label: "En Attente", value: " ,EN_ATTENTE" },
        { label: 'Envoyée', value: 'E,ENVOYEE' },
        { label: 'En Livraison', value: 'EL,EN_LIVRAISON' },
        { label: "Fin de saisie", value: " ,FIN_SAISIE" },
        { label: 'Livrée', value: 'L,LIVREE' },
        { label: "Réfusée", value: "REFUSEE" },
        { label: 'Supprimé', value: 'S, ' },
        { label: "Commandé", value: " ,VALIDEE" },
    ];

    natureCmdLabelsValues: any[] = [
        { label: 'Tous', value: null },
        { label: 'Groupe', value: 'AG' },
        { label: 'Individuelle', value: 'I' }
    ];

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private authService: AuthService,
        private offresService: OffresService,
        private exportPdfServ: ExportPdfService,
        private fsCommandeService: FsCommandesService,
    ) {
        this.initFilterForm();
    }

    ngOnInit(): void {
        this.buildExport();

        this.route.queryParams.subscribe(qParams => {
            if (qParams['idLabo'] || qParams['natureCommande'] || qParams['dateDebut'] || qParams['dateFin']) {
                console.log(qParams['idLabo'])
                this.searchCriteria = {
                    dateFinCommande: qParams['dateFin'] || null,
                    dateDebutCommande: qParams['dateDebut'] || null,
                    natureCommande: qParams['natureCommande'] || null,
                    laboratoire: qParams['idLabo'] ? { id: +qParams['idLabo'] } : null,
                }
            }
            this.searchCommandesLabo();
        });


        this.listenToSearchFilterChanges();
    }

    initFilterForm(): void {
        this.filterForm = this.fb.group({
            offreur: [null],
            distributeur: [null],
            statut: [null],
            client: [null],
            natureCommande: [null],
            dateDebut: [null],
            dateFin: [null],
        });
    }

    searchCommandesLabo(): void {
        this.fsCommandeService.searchCommandesLabo(this.navigation, this.searchCriteria).subscribe(res => {
            this.gridData = {
                data: res?.content,
                total: res?.totalElements
            };

            this.exportPdfRef.setData(this.gridData.data);
        });
    }

    buildExport(): void {
        this.exportPdfRef = this.exportPdfServ
            .ref<EnteteCommandeView>()
            .setTitle('Liste des Commandes Groupes & Individuelles')
            .addColumn('codeCommande', 'Code Cmd', { width: 60})
            .addColumn('*', "Titre de l'offre", {
                transform: (item: EnteteCommandeView) => {
                    return item?.offre?.titre;
                }
            })
            .addColumn('*', "Offreur", {
                transform: (item: EnteteCommandeView) => {
                    return item?.offre?.offreur?.raisonSociale;
                }
            }).addColumn('*', "Distributeur", {
                transform: (item: EnteteCommandeView) => {
                    return item?.distributeur?.raisonSociale;
                }
            })
            .addColumn('*', "Client", {
                transform: (item: EnteteCommandeView) => {
                    return item?.client?.raisonSociale;
                }
            })
            .addColumn('*', "Nature Cmd", { width: 60,
                transform: (item: EnteteCommandeView) => {
                    return item?.natureCommande === 'I' ? 'Individuelle' : 'Groupe';
                }
            })
            .addColumn('dateCreation', "Date Création", { type: 'date', width: 60 })
            .addColumn('valeurCmdBruteTtc', "Montant Brut (Dh)", { type: 'decimal', width: 60 })
            .addColumn('statut', 'Statut', { width: 80,
                transform: (value: string) => {
                    switch (value) {
                        case 'E':
                            return 'Envoyée';
                        case 'A':
                            return 'Annulé';
                        case 'EL':
                            return 'En Livraison';
                        case 'L':
                            return 'Livrée';
                        case 'S':
                            return 'Supprimé';
                        default:
                            return value;
                    }
                }
            })
            .setData([]);
    }


    appliquerFiltre(): void {
        let statut: string;
        let etatCommandes: string;
        const payload = this.filterForm?.getRawValue();

        if (payload?.statut) {
            [statut, etatCommandes] = (payload?.statut as string)?.split(',');
        }

        this.searchCriteria = new CommandeCriteria({
            ...this.searchCriteria,
            fournisseur: payload?.distributeur,
            laboratoire: payload?.offreur,
            client: payload?.client,
            dateDebutCommande: payload?.dateDebut,
            dateFinCommande: payload?.dateFin,
            natureCommande: payload?.natureCommande,
            statut: statut?.trim()?.length ? [statut] : null,
            etatCommandes: etatCommandes?.trim()?.length ? [etatCommandes as EtatCommande] : null,
        });

        this.navigation.skip = 0;

        this.searchCommandesLabo();
    }

    viderFiltre(): void {
        this.filterForm.reset();
        this.navigation.skip = 0;

        this.searchCriteria = new CommandeCriteria();
        this.router.navigate(['.'], { relativeTo: this.route });

        this.searchCommandesLabo();
    }

    consulterCommande(selectedCommande: EnteteCommandeView): void {
        if (selectedCommande?.natureCommande === 'I') {
            this.consulterCommandeIndividuelle(selectedCommande?.offre?.id, selectedCommande?.idCommande);
        } else if (selectedCommande?.natureCommande === 'AG') {
            this.consulterCommandeGroupe(selectedCommande);
        }
    }

    consulterCommandeIndividuelle(offreId: number, cmdId: number): void {
        this.router.navigate(
            [`/achats-groupes/commandes/edit/cmd-individuelle`, cmdId],
            { queryParams: { readOnly: true, offreId, from: 'cmds-admin' } }
        );
    }

    consulterCommandeGroupe(item: EnteteCommandeView, readOnly = false): void {
        this.router.navigate(
            ['/achats-groupes/commandes/edit/cmd-groupe', item?.idCommande],
            { queryParams: { readOnly, offreId: item?.offre?.id, from: 'cmds-admin' } }
        );
    }

    filterList(searchQuery: string, clientOnly = false) {
        return this.offresService.searchSociete({
            raisonSociale: searchQuery,
            typeEntreprises: clientOnly ? [SocieteType.CLIENT] : [SocieteType.FABRIQUANT, SocieteType.GROSSISTE]
        });
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    searchClient = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase(), true);
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    fournisseurFormatter = (result: { raisonSociale: any }) =>
        result ? result.raisonSociale : null;

    cellClickHandler(event: CellClickEvent): void {
        if (event?.column?.title === 'Statut') return;

        this.consulterCommande(event?.dataItem);
    }

    sortChange(sort: SortDescriptor[]): void {
        this.commandeSort = sort;
        if (
            this.commandeSort &&
            this.commandeSort.length > 0 &&
            this.commandeSort[0].dir
        ) {

            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.searchCommandesLabo();
    }

    listenToSearchFilterChanges(): void {
        this.searchFilter.valueChanges
            .pipe(
                takeUntil(this.unsubscribe$),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe((term: string) => {
                this.navigation.skip = 0;
                let criteriaKey: string = 'titre';
                
                this.searchCriteria['codeCommande'] = null;

                this.searchCriteria = new CommandeCriteria({ ...this.searchCriteria, [criteriaKey]: term });

                this.searchCommandesLabo();
            });
    }

    OnPageChange(event: number): void {
        this.searchCommandesLabo();
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }
}