#fiche-produit {
  margin-top: 12px;

  .box {


    .title {
      text-align: center;
      padding: 20px 0;
      font-size: 20px;
      margin: auto;
    }

    .detail {
      display: flex;
      flex-direction: column;

    }

    .data {
      display: inline-block;
      // color: #74cd8f;
      font-weight: bold;
    }
  }
}

.no-border {
  td  {
    border-top: none !important;
    padding-top: 0px !important;
  }
}

td {
  color: black !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
}

.card-header > .h4 {
  color: black;
}

.card-body {
  span, .btn {
    color: black;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--winoffre-base-border-radius);
  }
}

.card-footer {
  .btn {
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--winoffre-base-border-radius);
  }
}