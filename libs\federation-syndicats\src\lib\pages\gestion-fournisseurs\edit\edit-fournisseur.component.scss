.form-control {
    color: black;
    font-size: 1rem;
    border-radius: var(--winoffre-base-border-radius);
    font-weight: 600;
}

.btn {
    font-size: 1rem;
    border-radius: var(--winoffre-base-border-radius);
}

.input-group {
    .btn {
        border-top-right-radius: var(--winoffre-base-border-radius);
        border-bottom-right-radius: var(--winoffre-base-border-radius);
    }
}

label {
    color: black;
    font-weight: 600;
}

.picker-input {
    .form-control {
        border-radius: var(--winoffre-base-border-radius) !important;
    }
}

.card {
    border-radius: var(--winoffre-base-border-radius) !important;
}

.card-body {
    border-top-left-radius: 0;
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-left-radius:  var(--winoffre-base-border-radius);
    border-bottom-right-radius:  var(--winoffre-base-border-radius);
}

.map-container {
    position: relative;
    z-index: 0;
    height: 350px;
    width: 500px;
}


.btn-success{
  background: var(--fs-success) !important;
  border-color: var(--fs-success) !important;
}


.actions-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-inline: 10px;
  padding-block: 8px;
  border-radius: 11px;


  i {
    font-size: 18px;
    line-height: 1;
    cursor: pointer;
  }
}



.action-back{
  background: var(--wf-primary-400);
}



.tabs-separate{
  background: #f0f2f5;
  width: 20px;
  /* height: 100%; */
  flex: none !important;
  @media screen and (max-width: 768px) {
    display: none;

  }
}



.responsive-actions{


  @media screen and (max-width: 768px) {
    display: flex;
    justify-content: space-between !important;
    align-items: center;
    padding-inline: 10px;
    padding-block: 8px;
    background: white;
    border-top: #ccc 1px solid;

    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    margin: 0;

  }
}
