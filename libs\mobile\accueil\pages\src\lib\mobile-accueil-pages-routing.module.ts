import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AccueilPage } from "./accueil/accueil.page";
import { ActualitesPage } from "./actualites/actualites.page";
import { DetailActualitePage } from './detail-actualite/detail-actualite.page';

const routes: Routes = [
  {
    path: '',
    component: AccueilPage
  },
  {
    path: 'actualites',
    children:[
      {
        path:'',
        component: ActualitesPage,
      },
      {
        path:':id',
        component: DetailActualitePage
      }
    ]
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MobileAccueilPagesRoutingModule { }
