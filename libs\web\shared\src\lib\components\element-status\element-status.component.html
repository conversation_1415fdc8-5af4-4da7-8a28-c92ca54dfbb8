<div *ngIf="statut  " class="d-flex align-items-center justify-content-center">
  <span *ngIf="statut ==='N' || statut === 'NOUVELLE'" class="badge badge-info fixed-width-badge rounded-pill py-1 px-2">
    {{'Confirmé' | uppercase}}
  </span>
  <span *ngIf="statut === 'CLOTURE' || statut === 'CLOTUREE'" class="badge badge-warning rounded-pill fixed-width-badge py-1 px-2" style="background-color: #EE8245;">
    {{'Cloturée' | uppercase}}
  </span>

  <span *ngIf="statut ==='T' || statut === 'EN_ATTENTE'" class="badge rounded-pill fixed-width-badge py-1 px-2" style="background-color: #d7a934;">
    {{'En attente' | uppercase}}
  </span>
  <span *ngIf="statut ==='S' || statut === 'SUPPRIMER'" class="badge badge-danger rounded-pill fixed-width-badge py-1 px-2">
    {{'Supprimée' | uppercase}}
  </span>
  <span *ngIf="statut ==='r' || statut === 'REFUSE'" class="badge badge-danger rounded-pill fixed-width-badge py-1 px-2"  >
    {{'Refusé' | uppercase}}
  </span>
  <span *ngIf=" statut === 'TRANSMIS'" class="badge badge-primary rounded-pill fixed-width-badge py-1 px-2" style="background-color: #477DE7;">
    {{'Transmise' | uppercase}}
  </span>
  <span *ngIf="statut === 'FIN_SAISIE'" class="badge badge-primary rounded-pill fixed-width-badge py-1 px-2" style="background-color: #477DE7;">
    {{'Fin Saisie' | uppercase}}
  </span>
  <span *ngIf="statut === 'E' || statut === 'ENVOYEE'" class="badge badge-success rounded-pill fixed-width-badge py-1 px-2" style="background-color: #5E9E71;">
    {{'Envoyée' | uppercase}}
  </span>

  <span *ngIf="statut === 'EN_LIVRAISON' || statut === 'EL'" class="badge badge-success rounded-pill fixed-width-badge p-1" style="background-color: #5E9E71;">
    {{'En Livraison' | uppercase}}
  </span>

  <span *ngIf="statut === 'LIVREE' || statut === 'L'" class="badge badge-success rounded-pill fixed-width-badge py-1 px-2" style="background-color: #5E9E71;">
    {{'Livré(e)' | uppercase}}
  </span>
  <span *ngIf="statut === 'NP'" class="badge badge-grey rounded-pill fixed-width-badge py-1 px-2">
    Nouvelle
  </span>
  <span *ngIf="statut ==='B' || statut === 'BROUILLON'" class="badge badge-grey rounded-pill fixed-width-badge py-1 px-2">
    {{'Brouillon' | uppercase}}
  </span>
  <span *ngIf="statut ==='AC' || statut === 'ACCEPTER'" class="badge badge-success  rounded-pill fixed-width-badge py-1 px-2">
    {{'Acceptée' | uppercase}}
  </span>
  <span *ngIf="statut === 'ACCEPTEE'" class="badge badge-success  rounded-pill fixed-width-badge py-1 px-2" style="background-color: #477956;">
    {{'Ouverte' | uppercase}}
  </span>
  <span *ngIf="statut === 'VALIDEE' || statut === 'VALIDE'"  class="badge  rounded-pill py-1  fixed-width-badge" style="background-color: #477DE7;">
    {{'Commandée' | uppercase}}
  </span>
  <span *ngIf="statut === 'V'"  class="badge  rounded-pill py-1  fixed-width-badge" style="background-color: #477DE7;">
    {{'Validée' | uppercase}}
  </span>
  <span *ngIf="statut ==='R' || statut === 'REJETEE'" class="badge badge-success  rounded-pill fixed-width-badge py-1 px-2"  style="background-color: #A93535 ;" >
    {{'Rejetée' | uppercase}}
  </span>
  <span *ngIf="statut ==='REFUSEE'" class="badge badge-success  rounded-pill fixed-width-badge py-1 px-2"  style="background-color: #D49F00 ;" >
    {{'Réfusée' | uppercase}}
  </span>
  <span *ngIf="statut ==='P' || statut === 'PUBLIER'" class="badge badge-success rounded-pill fixed-width-badge py-1 px-2">
    {{'Publiée' | uppercase}}
  </span>
  <span *ngIf="statut ==='A' || statut === 'ANNULEE' || statut === 'ANNULER' || statut === 'ANNULE' " class="badge rounded-pill py-1 fixed-width-badge px-2" style="background-color: #A93535;">
    {{'Annulée' | uppercase}}
  </span>
  <span *ngIf="statut === 'EXPIREE'" class="badge rounded-pill py-1 fixed-width-badge px-2" style="background-color: #A93535;">
    {{'Expirée' | uppercase}}
  </span>
  <span *ngIf="statut ==='BANNULE'" class="badge rounded-pill py-1 fixed-width-badge px-2" style="background-color: #8F9BB3;">
    {{'Supprimé' | uppercase}}
  </span>
  <span *ngIf="statut ==='REPARTI'" class="badge rounded-pill py-1 fixed-width-badge px-2" style="background-color: #5E9E71;">
    {{'Réparti' | uppercase}}
  </span>
  <span *ngIf="statut ==='BVALIDE'" class="badge rounded-pill py-1 fixed-width-badge px-2" style="background-color: #5E9E71;">
    {{'Validé' | uppercase}}
  </span>

  <span *ngIf="statut === 'OFFRE_INDIVIDUELLE'"  class="badge  rounded-pill py-1 px-2" style="background-color: #393c9b;">
    {{'individuelle' | uppercase}}
  </span>
  <ng-container *jhiHasAnyPlateforme="'WIN_GROUPE'">
    <span *ngIf="statut === 'OFFRE_GROUPE'"  class="badge  rounded-pill py-1  fixed-width-badge" style="background-color: var(--wf-primary-400);">
      {{'groupe' | uppercase}}
    </span>
  </ng-container>

  <ng-container *jhiHasAnyPlateforme="'FEDERATION_SYNDICAT'">
    <span *ngIf="statut === 'OFFRE_GROUPE'"  class="badge  rounded-pill py-1  fixed-width-badge" style="background-color: var(--fs-primary-600);">
      {{'groupe' | uppercase}}
    </span>
  </ng-container>
  
</div>


<div *ngIf="!statut && !objectStatut" class="d-flex align-items-center justify-content-center">
  <span *ngIf="isActive === true" class="badge badge-success rounded-pill fixed-width-badge py-1 px-2">
    Actif
  </span>
  <span *ngIf="isActive === false " class="badge badge-danger rounded-pill fixed-width-badge py-1 px-2">
    Inactif
  </span>
</div>

<div *ngIf="type" >
  <span *ngIf="type === 'C'" >
    Création
  </span>
  <span *ngIf="type === 'M'" >
    Modification
  </span>
</div>

<div *ngIf="!statut && objectStatut">
  <span *ngIf="objectStatut.value == true" class="badge badge-success rounded-pill py-1 px-2">
    {{objectStatut.label}}
  </span>
  <span *ngIf="objectStatut.value == false " class="badge badge-danger rounded-pill py-1 px-2">
    {{objectStatut.label}}
  </span>
</div>
