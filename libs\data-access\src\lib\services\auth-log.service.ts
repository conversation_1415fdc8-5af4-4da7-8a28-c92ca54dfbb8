import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { AuthLogCriteria } from "libs/federation-syndicats/src/lib/models/auth-log.model";
import { Page } from "../models/page.model";
import { AuthLog } from "../models/auth-log.model";
import { Pagination } from "../models/PaginationDTO.ts";
@Injectable({
  providedIn: 'root'
})
export class AuthLogService {

  constructor(private http:HttpClient,@Inject('ENVIROMENT') private environment: any) { }

  search(searchCriteria: Partial<AuthLogCriteria>,pagination:Pagination) {
    const searchParams = {
      page: pagination.skip / pagination.pageSize,
      size: pagination.pageSize
    }
    return this.http.post<Page<AuthLog>> (`${this.environment.base_url}/api/conlog/searchpage`, searchCriteria,{params: searchParams});
  }
}
