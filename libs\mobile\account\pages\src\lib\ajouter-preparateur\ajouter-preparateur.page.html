<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="account"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Ajouter Préparateur'}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
    <ion-grid class="ion-padding" *ngIf="viewEntered">
      <ion-row class="ion-no-padding">
         <!-- Full Name -->
         <ion-col class="ion-no-padding" size="12">
           <ion-label>Nom:</ion-label>
           <ion-item class="ion-margin-bottom ion-no-padding" lines="none" input>
               <ion-input type="text"></ion-input>
            </ion-item>
            <!-- Last Name -->
            <ion-label>Prénom:</ion-label>
            <ion-item class="ion-margin-bottom ion-no-padding" lines="none" input>
               <ion-input type="text"></ion-input>
            </ion-item>
            <!-- MAIL -->
            <ion-label>Mail:</ion-label>
            <ion-item class="ion-margin-bottom ion-no-padding" lines="none" input>
               <ion-input type="email"></ion-input>
            </ion-item>
            <!-- PHONE  -->
            <ion-label>N° GSM</ion-label>
            <ion-item class="ion-margin-bottom ion-no-padding" lines="none" input>
               <ion-input type="number"></ion-input>
            </ion-item>
         </ion-col>
         <!-- Button SAVE -->
         <ion-col size="12" class="ion-no-padding ion-flex ion-justify-content-around ion-align-items-center">
           <ion-button class="btn-actions return-btn">
            <ion-icon name="close-outline" class="mx-lg"></ion-icon>
            Quitter</ion-button>
            <ion-button class="btn-actions valider-btn">
              <ion-icon name="checkmark-done-outline" class="mx-lg"></ion-icon>
              Ajouter</ion-button>
         </ion-col>
      </ion-row>
   </ion-grid>
</ion-content>
