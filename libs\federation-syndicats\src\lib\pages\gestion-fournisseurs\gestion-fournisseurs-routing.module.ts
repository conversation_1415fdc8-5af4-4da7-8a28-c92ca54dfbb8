import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ListeFournisseursComponent } from "./liste/liste-fournisseurs.component";
import { EditFournisseurComponent } from "./edit/edit-fournisseur.component";

const routes: Routes = [
    {
        path: '',
        pathMatch: 'full',
        redirectTo: 'liste'
    },
    {
        path: 'liste',
        title: 'Liste des Fournisseurs',
        component: ListeFournisseursComponent
    },
    {
        path: 'edit',
        title: 'Ajouter Fournisseur',
        component: EditFournisseurComponent
    },
    {
        path: 'edit/:id',
        title: 'Modifier Fournisseur',
        component: EditFournisseurComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class GestionFournisseursRoutingModule {}