import { BlocOffre } from './bloc-offre.model';
import { Moment } from 'moment';
import { Fournisseur } from './fournisseur.model';
import { DetailValeurPalier } from './detail-valeur-palier.model';
import { ClientFournisseur, DocMetaDataDto, DomainEnumeration, IUserAccount } from '@wph/shared';
import { ConditionBlocOffreCommandeConsolidee, EtatCommande, PharmacieEntreprise } from '@wph/federation-syndicats';
import { EntrepriseDTO } from 'libs/federation-syndicats/src/lib/models/entreprise.model';
import { SelectedPlateforme } from '@wph/web/layout';

export type NatureOffre = 'GROUPEE' | 'INDIVIDUELLE';
export type NatureOffreEnum = 'I' | 'G';
export class Offre {

  id: number;

  numeroOffre: string;

  statutOffre?: boolean;
  compteurCommandes?: number;

  titre: string;
  description: string;

  dateDebut: Moment | string;

  dateFin: Moment | string;
  enteteCommandeId?: number;

  views?: number;

  commandStatut?: string;

  natureOffre?: NatureOffreEnum | boolean;

  codeCommande?: string;

  listeConditionsBlocOffreCmd?: ConditionBlocOffreCommandeConsolidee[];

  client?: Fournisseur | PharmacieEntreprise;

  etatCommandeAchatGroupe?: EtatCommande;

  codeClientLocal?: string;

  clientLocal?: ClientFournisseur | string;

  dateCreationCmd?: string;

  nombreProduitsProposes?: number;

  commentaire?: string;
  commentaireOffre?: string;

  pourcentageEcoulement?: number;

  dateConfirmation?: string;

  dateSuppressionCmd?: string;

  dateAnnulationCmd?: string;

  maxRemiseFinancier?: number;

  nombresJoursRestants?: number;

  supporterEntreprise?: PharmacieEntreprise;
  supporterEntrepriseTemp?: PharmacieEntreprise; // Transient

  societeCreateurCmd?: Fournisseur;

  datePublication?: string; // TODO: Added
  reference?: string;
  image?: any;
  imageType?: any;   // TODO: Added
  selectedfile?: any;

  modalites: string;

  dateLivraisonPrevue?: Moment;
  delaiLivraison?: number;

  listeBlocs: BlocOffre[];

  listePaliersRemisesAdditionnels: DetailValeurPalier[];
  listePaliers: DetailValeurPalier[];

  palierAdditionTestBrut?: string;

  accepterPalierInvalide?: string;

  daysLeft: any;

  palierTestValeurBrut?: string;     // TODO: AGK   re-add this into saisie offre
  dateAcceptation?: Moment;
  dateRefus?: Moment;
  utiliserValeurHt?: string;     // TODO: AGK   re-add this into saisie offre

  offreur?: Fournisseur;
  distributeur?: Fournisseur;
  laboratoire?: Fournisseur;
  annuleePar?: Fournisseur

  distributeurs?: Fournisseur[];

  transporteurCommande?: string;
  transporteur?: EntrepriseDTO;
  transporteurs?: EntrepriseDTO[];

  cmdsUnitairesModifie?: BlocOffre[];

  modePaiement?: DomainEnumeration[];
  modePaiementValuePair?: { label: string, value: DomainEnumeration }[];

  typeOffre?: boolean; // transient

  typePack?: boolean; // transient

  offreVisibility?: 'PRIVATE' | 'PUBLIC' = 'PUBLIC';

  montantDuPack?: number;
  
  offreExportSrc?: SelectedPlateforme;

  etat?: string;
  etatProposant?: string;

  nombrePacksProposes?: number;

  userCreateurCommande?: IUserAccount;

  motifRefus?: string;

  coffretEnabled?: boolean;    // transient   not send from backend

  canPlaceOrder?: boolean // FE use only

  venteDirecteLabo?: boolean // Transient

  listeDistributeursString?: string; // Transient

  raisonSocialeTransporteur?: string;

  offreImageUrl?: string;

  // champs calculés
  mapBlocsById?: any;

  totalQteCmd?: number;
  totalQteUg?: number;
  TotalUgSaisie?: number;


  totalValeurBruteCmd?: number;   // may be ttc or ht   selon config offre
  totalValeurNetteCmd?: number;   // may be ttc or ht   selon config offre


  totalValeurBruteCmdTtc?: number;   // transient (used in frontend only)
  totalValeurNetteCmdTtc?: number;   // transient (used in frontend only)
  totalValeurBruteCmdHt?: number;   // transient (used in frontend only)
  totalValeurNetteCmdHt?: number;   // transient (used in frontend only)


  NetteAvantRemiseAdd?: number;

  docImageOffre?: DocMetaDataDto;

  attachments?: DocMetaDataDto[];

  // ? Conditions sur l'offre
  qteMin?: number;
  qteMax?: number;
  valeurMin?: number;
  valeurMax?: number;


  delaiPaiement?: DomainEnumeration;   // TODO: AGA add this in UI ?
  valeurEscompteCmd?: number;   // TODO: AGA add this in UI + CMD ?

  listDelaiPaiements?: DomainEnumeration[];

  dynamicScriptCondition?: string;



  etatCmdOffre?: string;      // TODO: AGA  'N' pour nonsaisi, 'V' pour valide, 'I' pour invalide
  messageEtatCmdOffre?: string;       //TODO: AGA  utilisé principalement pour afficher msg d'erreur quand etat='I'
}

export class OffreCriteria {
  id?: number;
  numero?: string;
  titre?: string;
  titreLaboratoire?: string;
  typeFrn?: string;
  dateDebut?: Moment;
  dateFin?: Moment;
  natureOffre?: string;
  dateLivraisonDebut?: Moment;
  dateLivraisonFin?: Moment;
  fournisseur?: Fournisseur;
  laboratoire?: Fournisseur;
  offreur?: Fournisseur;
  distributeur?: Fournisseur;
  offreurDistributeur?: Fournisseur;
  statut?: string[];
  venteDirecteLabo?: string;
  nonExpireesUniquement?: string;
  typeOffreur?: string; 
  idOffre?: string;

  categoriePrdId?: number;
  libelleProduit?: string;

  constructor(criteria?: Partial<OffreCriteria>) {
    Object.assign(this, criteria);
  }

}
