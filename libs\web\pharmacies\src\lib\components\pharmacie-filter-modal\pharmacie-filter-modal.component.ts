import { Component, EventEmitter, OnInit, Output } from "@angular/core";
import { <PERSON><PERSON><PERSON>r, FormBuilder, FormGroup } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthService } from "@wph/core/auth";
import { FilterOutput } from "@wph/data-access";
import { SocieteType } from "@wph/shared";

@Component({
    selector: 'wph-pharmacie-filter-modal',
    styleUrls: ['./pharmacie-filter-modal.component.scss'],
    templateUrl: './pharmacie-filter-modal.component.html'
})
export class PharmacieFilterModalComponent implements OnInit {
    filterForm: FormGroup;

    selectStatuClient = [
        { label: 'Tout', value: null },
        { label: 'Mes Clients Seulement', value: true },
        { label: 'Non-Client Seulement', value: false }
    ];

    selectStatutEnrolement = [
        { label: 'Tout', value: null },
        { label: 'Actif Seulement', value: true },
        { label: 'Inactif Seulement', value: false }
    ];
    
    selectStatutPasserCmd = [
        { label: 'Tout', value: null },
        { label: 'OUI', value: true },
        { label: 'NON', value: false }
    ];

    isFournisseurLabo: boolean;

    @Output() modalAction: EventEmitter<FilterOutput> = new EventEmitter<FilterOutput>();

    constructor(
        private fb: FormBuilder,
        private modalService: NgbModal,
        private authService: AuthService,
        private controlContainer: ControlContainer
    ) { }

    ngOnInit(): void {
        this.filterForm = this.fb.group({
            adresse: [null],
            code: [null],
            codeLocal: [null],
            dateActivation: [null],
            dateDebutCreation: [null],
            dateDesactivation: [null],
            dateFinCreation: [null],
            fournisseurId: [null],
            localite: [null],
            nomResponsable: [null],
            raisonSociale: [null],
            segmentEntreprise: [null],
            ville: [null],
            statutAccesClient: [null],
            isEnrolled: [null]
        });

        this.filterForm.setValue(this.controlContainer.control.value);

        this.isFournisseurLabo = this.authService.getPrincipal()?.societe?.typeEntreprise === SocieteType.FABRIQUANT;
    }

    applyFilter(): void {
        this.applyChanges();
        this.modalAction.emit({ filter: true });
    }

    clearFilters(): void {
        this.applyChanges();
        this.modalAction.emit({ clear: true });
    }

    applyChanges(): void {
        this.controlContainer.control.setValue(this.filterForm.value);

        this.filterForm.dirty && this.controlContainer.control.markAsDirty();
        this.filterForm.touched && this.controlContainer.control.markAsTouched();
    }

    dismiss(): void {
        this.modalService.dismissAll();
    }
}