import { SocieteType } from "@wph/shared"
import { GroupeEntreprise } from "./groupe-entreprise.model";

export class PharmacieEntrepriseCriteria {
    code?: string;
    fournisseurId?: number;
    gsm?: string;
    id?: number;
    deletedOnly?: boolean;
    isEnrolled?: boolean;
    nomResponsable?: string;
    raisonSociale?: string;
    segmentEntreprise?: string;
    groupeEntrepriseDTO?: GroupeEntreprise;
    statutMembreEntreprise?: boolean;
    statutMembreGroupe?: boolean;
    statutAccesClient?: boolean;
    typeEntreprise?: SocieteType[];
    typeEntreprises?: SocieteType[];
    responsablesGroupe?: any[];
    statutEntreprise?: boolean;
    isMembreGroupe?: boolean;
    ville?: string;
    villes?: string[];
    localite?: string[];

    constructor(criteria: Partial<PharmacieEntrepriseCriteria>) {
        this.code = criteria?.code || null;
        this.fournisseurId = criteria?.fournisseurId || null;
        this.gsm = criteria?.gsm || null;
        this.id = criteria?.id || null;
        this.isEnrolled = criteria?.isEnrolled;
        this.statutMembreEntreprise = criteria?.statutMembreEntreprise;
        this.nomResponsable = criteria?.nomResponsable || null;
        this.raisonSociale = criteria?.raisonSociale || null;
        this.segmentEntreprise = criteria?.segmentEntreprise || null;
        this.statutAccesClient = criteria?.statutAccesClient;
        this.typeEntreprise = criteria?.typeEntreprise || null;
        this.typeEntreprises = criteria?.typeEntreprises || null;
        this.ville = criteria?.ville || null;
        this.villes = criteria?.villes || null;
        this.responsablesGroupe = criteria?.responsablesGroupe || null;
        this.localite = criteria?.localite || null;
        this.statutEntreprise  = criteria?.statutEntreprise;
        this.statutMembreEntreprise = criteria?.statutMembreEntreprise;
        this.statutMembreGroupe = criteria?.statutMembreGroupe;
        this.isMembreGroupe = criteria?.isMembreGroupe;
        this.groupeEntrepriseDTO = criteria?.groupeEntrepriseDTO || null;
    }
}