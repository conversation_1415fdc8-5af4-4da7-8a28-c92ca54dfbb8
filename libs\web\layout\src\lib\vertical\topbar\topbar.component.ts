import { Component, OnInit, Output, <PERSON><PERSON><PERSON>ter, <PERSON><PERSON><PERSON><PERSON>, HostListener } from "@angular/core";
import { NotificationItem } from '../../shared/models/notification.model';
import { Language } from '../../shared/models/language.model';
import { Router } from '@angular/router';
import { AuthService } from "@wph/core/auth";
import { EventService } from "../../shared/services/event.service";
import { OffresService, PageOptions } from "@wph/data-access";
import { PlateformeService, Principal, Societe } from "@wph/shared";
import { Observable, Subject, takeUntil } from "rxjs";
import { CommandeService } from "@wph/commandes-web/commande";
import { SelectedPlateforme } from "@wph/web/layout";
import { DeferredActionButtonsService } from "@wph/web/shared";


@Component({
  selector: 'wph-vertical-topbar',
  templateUrl: './topbar.component.html',
  styleUrls: ['./topbar.component.scss']
})
export class TopbarComponent implements On<PERSON><PERSON>t, OnDestroy {
  notificationItems: Array<NotificationItem>;
  languages: Array<Language>;
  selectedLanguage: Language;


  openMobileMenu: boolean;
  etatBar: string;
  loggedInUser: Principal;
  leftSidebarWidth: string;

  panierCount = 0;
  currentWidth: number;

  isMobileView: boolean;
  availablePageOptions: PageOptions[] = [];
  topbarImgUrl: string = 'https://cdn.sophatel.com/pharmalien/images/quebg.svg';

  unsubscribe$: Subject<boolean> = new Subject();

  @Output() settingsButtonClicked = new EventEmitter();
  @Output() mobileMenuButtonClicked = new EventEmitter();


  private _isSuperAdmin: boolean = null;

  userAccountRouterLink: string;

  currentUser: Principal;
  currentUserRole: string;

  currentGrossiste$: Observable<Societe> = this.plateformeService.currentGrossiste$;
  currentPlateforme$: Observable<SelectedPlateforme>;

  constructor(
    private router: Router,
    private offreService: OffresService,
    private authService: AuthService,
    private eventService: EventService,
    private commandeService: CommandeService,
    private plateformeService: PlateformeService,
    private deferredActionBtnService: DeferredActionButtonsService
  ) {
    this.currentWidth = window.innerWidth;
    this.currentPlateforme$ = this.plateformeService.currentPlateforme$;

    this.currentUser = this.authService.getPrincipal();
    this.currentUserRole = authService.getUserRole(this.currentUser);

    // Set "Commande dans panier" count
    this.commandeService.panier$.pipe(
      takeUntil(this.unsubscribe$)
    ).subscribe(panier => {
      this.panierCount = panier?.lignes?.length || 0;
    });

    // Set dynamic user account router link
    this.setUserAccountRouterLink();
  }
  @HostListener('window:resize', ['$event']) onResize(event: any) {
    this.currentWidth = window.innerWidth;
    this.checkMobileView();
  }

  ngOnInit() {
    this.checkMobileView();
    // Get panier
    (
      (this.plateformeService.getCurrentPlateforme() === 'COMMANDE_WEB') &&
      this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])
    ) && this.getPanier();

    // get the notifications
    this._fetchNotifications();

    // get the language
    this._fetchLanguages();
    this.offreService.currentEtatBar.subscribe(etat => this.etatBar = etat);
    this.openMobileMenu = false;

    // logged in user
    this.loggedInUser = this.authService.currentUser();

    // set leftbar
    this.offreService.currentEtatBar
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(etat => {
        this.leftSidebarWidth = etat;

        setTimeout(() => {
          this.eventService.broadcast('changeLeftSidebarType', this.leftSidebarWidth);
        }, 10);
      });

    this.eventService.subscribe('refreshSideBar', (refresh) => {
      if (refresh) {
        this.currentUser = this.authService.getPrincipal();
        this.loggedInUser = this.authService.currentUser();
        this.currentUserRole = this.authService.getUserRole(this.currentUser);
      }
    });

    this.listenToMobileMenuOptionsChange();
  }

  getPanier() {
    const codeSite = this.plateformeService.getCurrentGrossiste()?.noeud?.codeSite;

    if (codeSite) {
      this.commandeService.getPanier().subscribe(panier => {
        this.panierCount = panier?.lignes?.length || 0;
      });
    }
  }

  checkMobileView() {
    this.isMobileView = window.innerWidth < 1000;
  }

  listenToMobileMenuOptionsChange() {
    this.deferredActionBtnService.mobileMenuOptions$.pipe
      (takeUntil(this.unsubscribe$))
      .subscribe((options: PageOptions[]) => {
        this.availablePageOptions = options;
      });
  }

  setUserAccountRouterLink(): void {
    this.currentPlateforme$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(current => {
        switch (current) {
          case 'WIN_OFFRE':
            this.userAccountRouterLink = '/win-offre/account';
            break;
          case 'COMMANDE_WEB':
            this.userAccountRouterLink = '/commande-web/account';
            break;
          case 'WIN_GROUPE':
          case 'FEDERATION_SYNDICAT':
            this.userAccountRouterLink = '/achats-groupes/account';
            break;
          case 'DEFAULT':
          default:
            this.userAccountRouterLink = '/pharma-lien/account';
            break;
        }
      });
  }

  /**
   * Fetches the supported languages
   */
  _fetchLanguages() {
    this.languages = [{
      id: 1,
      name: 'English',
      flag: 'assets/images/flags/us.jpg',
    },
    {
      id: 2,
      name: 'German',
      flag: 'assets/images/flags/germany.jpg',
    },
    {
      id: 3,
      name: 'Italian',
      flag: 'assets/images/flags/italy.jpg',
    },
    {
      id: 4,
      name: 'Spanish',
      flag: 'assets/images/flags/spain.jpg',
    },
    {
      id: 5,
      name: 'Russian',
      flag: 'assets/images/flags/russia.jpg',
    }];

    this.selectedLanguage = this.languages[0];
  }

  /**
   * Fetches the notification
   * Note: For now returns the hard coded notifications
   */
  _fetchNotifications() {
    this.notificationItems = [{
      text: 'Caleb Flakelar commented on Admin',
      subText: '1 min ago',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'primary',
      redirectTo: '/notification/1'
    },
    {
      text: 'New user registered.',
      subText: '5 min ago',
      icon: 'mdi mdi-account-plus',
      bgColor: 'info',
      redirectTo: '/notification/2'
    },
    {
      text: 'Cristina Pride',
      subText: 'Hi, How are you? What about our next meeting',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'success',
      redirectTo: '/notification/3'
    },
    {
      text: 'Caleb Flakelar commented on Admin',
      subText: '2 days ago',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'danger',
      redirectTo: '/notification/4'
    },
    {
      text: 'Caleb Flakelar commented on Admin',
      subText: '1 min ago',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'primary',
      redirectTo: '/notification/5'
    },
    {
      text: 'New user registered.',
      subText: '5 min ago',
      icon: 'mdi mdi-account-plus',
      bgColor: 'info',
      redirectTo: '/notification/6'
    },
    {
      text: 'Cristina Pride',
      subText: 'Hi, How are you? What about our next meeting',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'success',
      redirectTo: '/notification/7'
    },
    {
      text: 'Caleb Flakelar commented on Admin',
      subText: '2 days ago',
      icon: 'mdi mdi-comment-account-outline',
      bgColor: 'danger',
      redirectTo: '/notification/8'
    }];
  }

  /**
   * Change the language
   * @param language
   */
  changeLanguage(language: Language) {
    this.selectedLanguage = language;
  }

  /**
   * Toggles the right sidebar
   */
  toggleRightSidebar() {
    this.settingsButtonClicked.emit();
  }

  /**
   * Toggle the menu bar when having mobile screen
   */
  toggleMobileMenu(event: any) {
    event.preventDefault();
    this.eventService.displayMobileSidebar$.next(!this.eventService.displayMobileSidebar$.value);
  }

  /**
   * Logout the user
   */
  logout() {
    this.router.navigate(['/auth/login']).then((success) => {
      if (success) {
        this._isSuperAdmin = null;
        this.authService.logout();
      }
    });
  }

  get isSuperAdmin() {
    if (this._isSuperAdmin === null) {
      console.log('Header isAdmin call');
      this._isSuperAdmin = this.authService.isAuthenticated() ? this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']) : null;
    }

    return this._isSuperAdmin;
  }


  isAuthenticated() {
    return this.authService.isAuthenticated();
  }


  /**
     * Change the side bar width
     * @param type type of sidebar
     */
  public changeLeftSidebarType() {
    if (window.innerWidth >= 920) {
      if (this.leftSidebarWidth === 'condensed') {
        this.offreService.changeEtatBar('fixed');
      } else {
        this.offreService.changeEtatBar('condensed');
      }
    } else {
      this.offreService.changeEtatBar('fixed');
    }
  }

  afficherPanier() {
    this.router.navigateByUrl('commande-web/edit/panier')
  }

  navigateToPlateformeSelection(): void {
    this.router.navigateByUrl('/pharma-lien');
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

}
