import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FsBLService } from '../../../services/fs-bl.service';
import { DetailBlAchatGroupeDTO, EnteteBlConsolideeMarcheDTO } from '../../../models/bl.model';
import { ActivatedRoute, Router } from '@angular/router';
import { RowClassArgs } from '@progress/kendo-angular-grid';
import { NgbNav } from '@ng-bootstrap/ng-bootstrap';
import { SelectedPlateforme } from '@wph/web/layout';
import { PlateformeService } from '@wph/shared';

@Component({
  selector: 'app-commande-list-bls',
  templateUrl: './commande-list-bls.component.html',
  styleUrls: ['./commande-list-bls.component.scss']
})
export class CommandeListBlsComponent implements OnInit {

  listEnteteBl: EnteteBlConsolideeMarcheDTO[] = [];
  currentBL: EnteteBlConsolideeMarcheDTO;
  filteredLignes: DetailBlAchatGroupeDTO[] = [];
  commandeId = null;
  activeId = 1;

  filter = '';
  isUnitaire: boolean;
  isIndividuelle: boolean;
  loading = true;

  isLeftDisabled = true;
  isRightDisabled = true;
  currentPlateforme: SelectedPlateforme;

  synthese: { qLivree: number; mntBrut: number; mntNet: number; mntRemise: number; };

  @ViewChild('blScroller') blsList: ElementRef<HTMLElement>;
  @ViewChild('infoPharamcieNav') infoPharamcieNav: NgbNav;


  constructor(private fsBlService: FsBLService, private route: ActivatedRoute, private router: Router, private plateformeService: PlateformeService) {
    this.currentPlateforme = this.plateformeService.getCurrentPlateforme();
    
    if(this.router.url.includes('unitaire')){
      this.isUnitaire = true;
    }else if(this.router.url.includes('individuelle')){
      this.isIndividuelle = true;
    }
    this.route.params.subscribe(params => {
      this.commandeId = params['cmd'];
      this.isUnitaire ? this.getListUnitaire() : this.isIndividuelle ? this.getListIndividuelle() : this.getListConsolide();
    })
  }

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.checkScrollPosition();
      this.blsList?.nativeElement.addEventListener('scroll', this.checkScrollPosition.bind(this));
    }, 1000);
  }

  checkScrollPosition() {
    if(!this.blsList) return;
    const element = this.blsList.nativeElement;
    this.isLeftDisabled = element.scrollLeft === 0;
    this.isRightDisabled = element.scrollLeft + element.clientWidth >= element.scrollWidth;
  }

  scrollToDirection(direction: 'left' | 'right') {
    if (direction === 'left') {
      this.blsList.nativeElement.scrollBy({ left: -300, behavior: 'smooth' });
    } else {
      this.blsList.nativeElement.scrollBy({ left: 300, behavior: 'smooth' });
    }
  }

  onFilterBLProduit(filter: string) {
    this.filter = filter.toLowerCase().trim();
    this.applyFilter();
  }

  applyFilter() {
    if (!this.currentBL) return;

    if (!this.filter) {
      this.filteredLignes = this.currentBL.lignes;
    } else {
      this.filteredLignes = this.currentBL.lignes.filter(ligne =>
        ligne.designation.toLowerCase().includes(this.filter)
      );
    }
    this.generateSynthese(this.filteredLignes);
  }

  tabChange(activeId: number) {
    this.activeId = activeId;
    this.currentBL = this.listEnteteBl[activeId - 1];
    this.filter = '';
    this.applyFilter();    setTimeout(() => {
      this.scrollToActiveTab();
    });
  }


  scrollToActiveTab() {
    if (!this.blsList || !this.infoPharamcieNav) return;

    const tabElement = this.infoPharamcieNav.items.get(this.activeId - 1)?.elementRef.nativeElement;
    if (!tabElement) return;

    const scroller = this.blsList.nativeElement;
    const scrollerRect = scroller.getBoundingClientRect();
    const tabRect = tabElement.getBoundingClientRect();

    // Check if the tab is partially or fully out of view
    if (tabRect.left < scrollerRect.left || tabRect.right > scrollerRect.right) {
      const scrollLeft = tabElement.offsetLeft - (scroller.clientWidth - tabElement.offsetWidth) / 2;
      scroller.scrollTo({
        left: scrollLeft,
        behavior: 'smooth'
      });
    }
  }

  getListConsolide() {
    this.fsBlService.getBLsConsolideByCommandeId({cmdConsolideeId: this.commandeId}).subscribe({
      next: (res) => {
        this.listEnteteBl = res;
        this.loading = false;
        if (this.listEnteteBl.length > 0) {
          this.currentBL = this.listEnteteBl[0];
          this.applyFilter();
        }
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  getListUnitaire() {
    this.fsBlService.getBLsUnitaireByCommandeId({cmdUnitaireId: this.commandeId}).subscribe({
      next: (res) => {
        this.listEnteteBl = res;
        this.loading = false;
        if (this.listEnteteBl.length > 0) {
          this.currentBL = this.listEnteteBl[0];
          this.applyFilter();
        }
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  getListIndividuelle() {
    this.fsBlService.getBLsIndividuelByCommandeId(this.commandeId).subscribe({
      next: (res) => {
        this.listEnteteBl = res
        this.loading = false;
        if (this.listEnteteBl.length > 0) {
          this.currentBL = this.listEnteteBl[0];
          this.applyFilter();
        }
      },
      complete: () => {
        this.loading = false;
      }
    });
  }


  generateSynthese(lignes: DetailBlAchatGroupeDTO[]) {
    console.log("lignes",lignes)

    const field = {
      qLivree: 0,
      mntBrut: 0,
      mntNet: 0,
      mntRemise: 0,
    };

    field.qLivree = lignes.reduce((acc, curr) => acc + curr.quantiteLivree, 0);
    field.mntBrut = lignes.reduce((acc, curr) => acc + (curr.pph * curr.quantiteLivree), 0);
    field.mntNet = lignes.reduce((acc, curr) => acc + ((curr.pphRemise ?? curr.pph) * curr.quantiteLivree), 0);
    field.mntRemise = lignes.reduce((acc, curr) =>
      acc + (curr.pph * curr.quantiteLivree) - ((curr.pphRemise ?? curr.pph) * curr.quantiteLivree), 0);

    this.synthese = field;
  }

  choseRowClass(row: RowClassArgs) {
    if(row.dataItem.isCadeau) {
      return 'cadeau-row';
    }
    return '';
  }

  back() {
    window.history.back();
  }
}
