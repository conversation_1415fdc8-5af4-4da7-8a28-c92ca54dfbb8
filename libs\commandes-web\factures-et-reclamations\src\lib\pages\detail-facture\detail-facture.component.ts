import { Component, HostListener, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { GridDataResult, PageChangeEvent } from "@progress/kendo-angular-grid";
import { CommandeService } from "@wph/commandes-web/commande";
import { DetailFacture, EnteteFacture, Pagination } from "@wph/data-access";
import { getDynamicPageSize } from "@wph/web/shared";

@Component({
    selector: 'detailFacture',
    templateUrl: './detail-facture.component.html',
    styleUrls: ['./detail-facture.component.scss']
})
export class DetailFactureComponent implements OnInit {
    navigation: Pagination = { skip: 0, pageSize: 20 }
    enteteFacture: EnteteFacture;
    initialDetailFacture: DetailFacture[];
    detailFactureData: GridDataResult = { data: [], total: 0 };

    factureId: number;

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private commandeService: CommandeService
    ) { }

    ngOnInit(): void {
        this.setPageSize();

        this.route.params.subscribe(params => {
            this.factureId = params['id'];

            this.getDetailsFacture();
        });
    }

    getDetailsFacture(): void {
        this.commandeService.getFactureById(this.factureId).subscribe(res => {
            this.enteteFacture = res;

            this.initialDetailFacture = this.enteteFacture?.lignes;

            this.detailFactureData = {
                data: this.initialDetailFacture.slice(0, this.navigation.pageSize),
                total: this.initialDetailFacture.length
            };
        });
    }

    setPageSize(currentHeight?: number): void {
        const dynamicSize = getDynamicPageSize(currentHeight, 48);
        
        if (dynamicSize !== this.navigation.pageSize) {
            this.navigation.pageSize = dynamicSize;

            if (currentHeight) {
                this.navigation.skip = 0;
                this.getDetailsFacture();
            }
        }
    }

    back(): void {
        this.router.navigateByUrl('commande-web/autres/factures');
    }

    pageChange(event: PageChangeEvent): void {
        if (event.skip !== this.navigation.skip) {
            this.navigation.skip = event.skip;

            this.detailFactureData.data =
                this.initialDetailFacture
                    .slice(this.navigation.skip, (this.navigation.skip + this.navigation.pageSize));

        }
    }
}