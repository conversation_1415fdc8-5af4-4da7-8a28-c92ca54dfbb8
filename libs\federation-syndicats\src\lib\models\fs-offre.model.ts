import { Moment } from "moment";
import { EntrepriseDTO } from "./entreprise.model";
import { GroupeEntreprise } from "./groupe-entreprise.model";
import { NatureOffreEnum, Offre, Pagination } from "@wph/data-access";

export class FsOffreCriteria {
    id?: number;
    numero?: string;
    titre?: string;
    titreLaboratoire?: string;
    dateDebut?: Moment;
    dateFin?: Moment;
    dateLivraisonDebut?: Moment;
    dateLivraisonFin?: Moment;
    fournisseur?: EntrepriseDTO;
    laboratoire?: EntrepriseDTO;
    offreur?: EntrepriseDTO;
    distributeur?: EntrepriseDTO;
    offreurDistributeur?: EntrepriseDTO;
    etatProposant?: string[];
    venteDirecteLabo?: string;
    nonExpireesUniquement?: string;
    typeOffreur?: string;
    statut?: string[];
    natureOffre?: NatureOffreEnum;

    etatCommandeAchatGroupesExclus?: string[];
    etatCommandeAchatGroupesInclus?: string[];
    groupeOfCommandeConsolideeDTO?: GroupeEntreprise;
    groupeConcerne?: GroupeEntreprise;

    constructor(criteria?: Partial<FsOffreCriteria>) {
        this.id = criteria?.id || null;
        this.numero = criteria?.numero || null;
        this.titre = criteria?.titre || null,
        this.titreLaboratoire = criteria?.titreLaboratoire || null;
        this.dateDebut = criteria?.dateDebut || null;
        this.dateFin = criteria?.dateFin || null;
        this.dateLivraisonDebut = criteria?.dateLivraisonDebut || null;
        this.dateLivraisonFin = criteria?.dateLivraisonFin || null;
        this.fournisseur = criteria?.fournisseur || null;
        this.laboratoire = criteria?.laboratoire || null;
        this.offreur = criteria?.offreur || null;
        this.distributeur = criteria?.distributeur || null;
        this.offreurDistributeur = criteria?.offreurDistributeur || null;
        this.etatProposant = criteria?.etatProposant || null;
        this.venteDirecteLabo = criteria?.venteDirecteLabo;
        this.nonExpireesUniquement = criteria?.nonExpireesUniquement;
        this.typeOffreur = criteria?.typeOffreur || null;
        this.statut = criteria?.statut || null;
        this.natureOffre = criteria?.natureOffre || null;
        this.groupeConcerne = criteria?.groupeConcerne || null;
        this.etatCommandeAchatGroupesExclus = criteria?.etatCommandeAchatGroupesExclus || null;
        this.etatCommandeAchatGroupesInclus = criteria?.etatCommandeAchatGroupesInclus || null;
        this.groupeOfCommandeConsolideeDTO = criteria?.groupeOfCommandeConsolideeDTO || null;
    }
}

export interface SearchOffre extends Pagination {
    content: Offre[];
}