<!-- Start Of Header -->
<div class="row rowline mb-2">
    <div class="page-title-box  d-flex gap-2">
        <h4 class="page-title fw-4 ps-2">Configuration admin</h4>
        <div class="d-flex gap-2">


            <button type="button" class="btn btn-primary" (click)="save()">
                Enregistrer</button>
        </div>
    </div>
</div>
<!-- END HEADER -->
<div class="page-content">
    <div class="card">
        <ul ngbNav #nav1="ngbNav" [(activeId)]="tabs1" class="nav-tabs bg-nav-pills mb-2 bg-white">
            <li [ngbNavItem]="1">
                <a ngbNavLink>
                    <i class="mdi mdi-home-variant d-md-none d-block fs-4"></i>
                    <span class="d-none d-md-block px-2">Configuration du timezone</span>
                </a>
                <ng-template ngbNavContent>
                    <ng-container *ngTemplateOutlet="Tab1"></ng-container>
                </ng-template>
            </li>

            <li [ngbNavItem]="2">
                <a ngbNavLink>
                    <i class="mdi mdi-home-variant d-md-none d-block fs-4"></i>
                    <span class="d-none d-md-block px-2">Configuration Rate Limiting</span>
                </a>
                <ng-template ngbNavContent>
                    <ng-container *ngTemplateOutlet="Tab2"></ng-container>
                </ng-template>
            </li>

        </ul>
        <div class="col-12" style="min-height:65vh;">
            <div [ngbNavOutlet]="nav1"></div>
        </div>
    </div>
</div>



<ng-template #Tab1>
    <div class="row d-flex justify-content-left ps-3">

        <div class="container mt-1 gu-container p-2">

            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="form-check d-flex align-items-start">
                        <div class="flex-shrink-0">
                            <label for="colFormLabelSm" class="col-form-label col-form-label-sm me-1">
                                timezone<span class="text-danger">*</span>
                            </label>
                        </div>
                        <div class="col-6">
                            <input type="number" id="zone" name="zone" [(ngModel)]="zone"
                                class="form-control text-end  form-control-sm ps-1 fc-p1  bg-white" autocomplete="off">
                            <small class="text-danger" *ngIf=" this.submit  && (zone==null)">Ce
                                champs est obligatoire</small> <br>
                            <small class="text-danger" *ngIf="messageError">{{messageError}}</small>
                        </div>
                    </div>
                </div>
            </div>




        </div>
    </div>
</ng-template>

<ng-template #Tab2>
    <div class="row d-flex justify-content-left ps-3">

        <div class="container mt-1 gu-container p-2">

            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="form-check d-flex align-items-start">
                        <input class="form-check-input me-2 custom-checkbox" type="checkbox"
                            [(ngModel)]="rateLimitingStatus" />
                        <label class="form-check-label user-select-none fw-semibold" for="RateLimiting">
                            Activer Rate Limiting
                        </label>
                    </div>
                </div>
            </div>




        </div>
    </div>
</ng-template>