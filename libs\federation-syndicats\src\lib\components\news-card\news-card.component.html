<ng-container *ngIf="mode === 'DEFAULT'; else: alt">
    <div class="card ml-2 my-0 mr-0 layered-element info-card" #actu>
        <div class="layered-element-content">
            <div class="row px-3 py-1">
                <span class="title col-12 p-0">{{ titre }}</span>

                <span class="content col-11 p-0">
                    {{ content }}
                </span>
            </div>

            <div *ngIf="link" class="row px-3 py-1 d-flex justify-content-end">
                <span class="readMore pointer-cus" (click)="redirectToUrl(link)">
                    <u>
                        <span *ngIf="imgSrc && !imgActionLabel" class="mr-1">Ouvrir l'image dans un nouvel onglet</span>
                        <span *ngIf="imgActionLabel" class="mr-1">{{imgActionLabel}}</span>
                        <span *ngIf="!imgSrc && !imgActionLabel" class="mr-1">En savoir plus</span>
                        <i class="bi bi-box-arrow-up-right" style="font-size: 14px;"></i>
                    </u>
                </span>
            </div>

            <div *ngIf="videoUrl" class="position-relative my-2">
                <youtube-player [showBeforeIframeApiLoads]="false" [disableCookies]="true" [playerVars]="youtubePlayerVars"
                  [videoId]="videoUrl"></youtube-player>
              </div>
        </div>
    </div>
</ng-container>

<ng-template #alt>
    <div class="card shadow-sm p-1 h-100 card-radius info-card">
        <div class="row pl-3 pr-1 py-1 h-100 alt-actu-container">
            <div class="col-8">
                <div class="row">
                    <span class="societe-label col-12 p-0">{{ subtitle | titlecase }}</span>

                    <span class="title col-12 p-0">{{ titre | titlecase }}</span>

                    <span class="content col-11 p-0">{{ content }}</span>

                    <div *ngIf="link" class="col-11 p-0 w-100 d-flex justify-content-end">
                        <span class="readMore pointer-cus" (click)="redirectToUrl(link)">
                            <u>
                                <span class="mr-1">En savoir plus</span>
                                <i class="bi bi-box-arrow-up-right" style="font-size: 14px;"></i>
                            </u>
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-4 px-0 py-2 d-flex align-items-center">
                <img class="img-actu-alt" [src]="imgSrc ?? 'assets/images/article-placeholder.svg'" />
            </div>
        </div>
    </div>
</ng-template>