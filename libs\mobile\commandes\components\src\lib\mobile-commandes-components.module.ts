import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CommandEditComponent } from './command-edit/command-edit.component';
import { MobileSharedModule } from '@wph/mobile/shared';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommandFiltersComponent } from './command-filters/command-filters.component';
import { ClientTypeaheadComponent } from './client-typeahead/client-typeahead.component';
import { CommandeItemComponent } from './commande-item/commande-item.component';

@NgModule({
  imports: [
    CommonModule,
    MobileSharedModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  declarations: [
    CommandEditComponent,
    CommandFiltersComponent,
    ClientTypeaheadComponent,
    CommandeItemComponent,
  ],
  exports: [
    CommandEditComponent,
    ClientTypeaheadComponent,
    CommandFiltersComponent,
    CommandeItemComponent,
  ],
})
export class MobileCommandesComponentsModule { }
