  .back {
    // width: calc(100vw - 30%);
    text-align: right;
    margin: 10px auto 0 auto;
  }

  .box {
    // width: calc(100vw - 30%);
    background-color: #fafafa;
    border: 1px solid;
    margin: 25px auto;
    border-radius: 6px;
    border-width: 0;
    box-shadow: rgba(25, 25, 25, 0.04) 0 0 1px 0, rgba(0, 0, 0, 0.1) 0 3px 4px 0;
    font-family: Arial, sans-serif;
    font-size: 1em;
    padding: 10px 25px;
    transition: all 200ms;

    .parent {
      display: grid;
      gap: 0.5rem;
      grid-template-columns:
        auto max-content;
      grid-template-areas: "left right";
      height: 57px;
    }

    .left {
      grid-area: left;

      h2 {
        color: #8bc350;
      }
    }

    .right {
      grid-area: right;

      .date {
        transform: translateY(9px);
      }
    }

    .table_data {
      table {
        width: 100%;
      }

      .total {
        font-size: 20px;
        color: #8bc34a;
      }
    }
  }

  .card-header {
    span {
      font-size: 1rem;
      font-weight: 600;
      color: black;
    }
  }
  