import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { BlocOffre, Fournisseur, Offre, OffresService } from "@wph/data-access";
import { ActivatedRoute, Router } from "@angular/router";
import { Observable, Subject, filter, finalize, iif } from "rxjs";
import { DeferredActionButtonsService, UserInputService } from '@wph/web/shared';
import { AuthService } from '@wph/core/auth';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AccesClient, AccesClientService, AlertService, ClientFournisseur, ClientView, HasAccessService, SocieteType } from '@wph/shared';
import { GestionServicesClient } from '@wph/web/gestion-services';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { cloneDeep, isEqual } from 'lodash';
import { EventService } from '@wph/web/layout';

@Component({
  selector: 'wph-edit-commande',
  templateUrl: './edit-commande-wo.component.html',
  styleUrls: ['./edit-commande-wo.component.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class EditCommandeComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  submitted: boolean;
  selectedOffre: Offre;
  initialSelectedOffreObj: Offre | null = null;
  id: string;
  validCommande = true;
  isLoading = false;
  readOnly = false;
  offre = false;
  etatBar: string;
  printing: boolean;
  blobUrl: string;
  motifDeRefus: string = '';
  societe: Fournisseur;
  pageMode: string;
  assistantForm: FormGroup;
  offreForm: FormGroup;
  handleRechercheProduit: boolean;
  selectPack: Array<{ label: string; value: string; }>;

  @ViewChild('resetModal', { static: true }) resetModal: TemplateRef<any>;
  @ViewChild('rechercherProduitRef', { static: true }) rechercherProduitRef: any;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private alertServ: AlertService,
    private authService: AuthService,
    private offresService: OffresService,
    private hasAccessService: HasAccessService,
    private userInputService: UserInputService,
    private accesClientService: AccesClientService,
    private gestionServiceClient: GestionServicesClient,
    private deferredActionBtnService: DeferredActionButtonsService,
  ) {}

  ngOnInit() {
    this.offresService.getValidCommandeObservable().subscribe(() => this.checkifvalid());
    this.offresService.currentEtatBar.subscribe(etat => this.etatBar = etat);

    this.route.queryParams.subscribe(params => {
      this.offre = params['offre'];
      this.pageMode = params['mode'];
    });

    this.route.queryParams
      .pipe(filter((params: any) => params.readOnly))
      .subscribe(params => {
        this.readOnly = params.readOnly;
      });

    this.route.paramMap.subscribe(params => {
      this.id = params.get('id');
      if (this.id) {
        if (this.offre) {
          const incrementViewCount = history.state?.['incr'] || false;

          this.offresService.getOffreById(+this.id, incrementViewCount).subscribe((data => {
            this.selectedOffre = data;
            this.offresService.initialiserEtatOffre(this.selectedOffre);
            this.offresService.palierOffreTraitement(this.selectedOffre);

            this.setPackTitles();

            // ? Fetch liste des code produits for consultation stock
            this.getProduitStockForOffre(this.selectedOffre);

            this.setSousBlocCollapseState(true)
            this.setConditionsEtRemisesDisplayState(true);

            // STORE DEEP COPY OF THE INITIAL SELECTED OFFRE
            this.initialSelectedOffreObj = cloneDeep(data);

            this.pushMobilePageOptions();
          }));

        } else {
          this.offresService.getCommandesById(+this.id).subscribe(
            (data) => {
              this.selectedOffre = data;
              this.readOnly = this.readOnly || (this.selectedOffre?.commandStatut !== 'BROUILLON')
              this.offresService.initialiserEtatOffre(this.selectedOffre);
              this.offresService.palierOffreTraitement(this.selectedOffre);

              // ? Fetch liste des code produits for consultation stock
              this.getProduitStockForOffre(this.selectedOffre);

              this.setPackTitles();

              this.setSousBlocCollapseState(true)
              this.setConditionsEtRemisesDisplayState(true);

              // STORE DEEP COPY OF THE INITIAL SELECTED OFFRE
              this.initialSelectedOffreObj = cloneDeep(data);

              this.pushMobilePageOptions();
            }
          );
        }

      }
    });

    this.societe = this.authService.getPrincipal()?.societe;

    /*******************Assistant de Commande******************/
    this.assistantForm = this.fb.group({
      client: [null, [Validators.required]],
      dateDebut: [null, [Validators.required]],
      dateFin: [null, [Validators.required]]
    });

    if (this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])) {
      this.assistantForm.patchValue({
        client: this.authService.currentUser()?.societe
      });
    }

    this.offreForm = this.fb.group({
      resetOption: ['O'],
      pack: [null]
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();

    this.deferredActionBtnService.pushPageOptions([]);
    this.offresService.produitsAvecStockInsuffisant$.next(null);
  }

  setPackTitles(): void {
    this.selectPack = this.selectedOffre?.listeBlocs?.map(bloc => {
      return { label: bloc.titre, value: bloc.id.toString() };
    });
  }

  applyOffreReset(): void {
    const values = this.offreForm.value;

    if (values['resetOption'] === 'O') {
      this.offresService.reinitialiserEtatCommande(this.selectedOffre);
    } else if (values['resetOption'] === 'P') {
      const targetIndex = this.selectedOffre?.listeBlocs?.findIndex(bloc => bloc?.id === +values['pack']);

      if (targetIndex > -1) {
        this.offresService.reinitialiserEtatCommandeBlocOffre(this.selectedOffre.listeBlocs[targetIndex]);
      }
    }

    this.modalService.dismissAll();
  }

  get resetCtrls() {
    return this.offreForm.controls;
  }

  public checkifvalid() {
    if (this.selectedOffre?.listeBlocs && this.selectedOffre?.listeBlocs?.length) {
      for (const iterator of this.selectedOffre.listeBlocs) {
        if (iterator.etat === 'I' || this.selectedOffre?.etatCmdOffre === 'I') {
          this.validCommande = false;
          break;
        }
        this.validCommande = true;
      }
    }
  }

  save() {
    this.isLoading = true;
    let requiredFieldsMsg: string = '';

    if (!this.selectedOffre?.distributeur) {
      requiredFieldsMsg += `<li>Sélectionner un distributeur.</li>\n`;
    }

    if (!this.selectedOffre?.client && this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL'])) {
      requiredFieldsMsg += `<li>Sélectionner un client pour cette commande.</li>\n`;
    }

    if (this.selectedOffre?.raisonSocialeTransporteur && !this.selectedOffre?.transporteurCommande) {
      requiredFieldsMsg += `<li>Sélectionner un transporteur pour cette commande.</li>\n`;
    }

    if (this.selectedOffre?.listDelaiPaiements?.length && !this.selectedOffre?.delaiPaiement) {
      requiredFieldsMsg += `<li>Sélectionner un mode de paiement.</li>\n`;
    }

    if (!requiredFieldsMsg) {
      if (!!this.selectedOffre?.clientLocal) {
        this.selectedOffre['codeClientLocal'] =
          (this.selectedOffre?.clientLocal instanceof Object) ?
            this.selectedOffre?.clientLocal?.code :
            this.selectedOffre?.clientLocal;

        this.excludeClientLocal();
      }

      this.offresService.createCommande(this.selectedOffre)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(() => {
          this.submitted = true;

          this.router.navigateByUrl('win-offre/commandes/list', { state: { modified: true } });
          this.isLoading = false;
        });
    } else {
      this.isLoading = false;
      this.alertServ.error(`Veuillez compléter les éléments obligatoires suivants: <ul>${requiredFieldsMsg}</ul>`, 'MODAL');
    }

  }

  accepterCmd(params?: { forceAccept?: boolean; codeClientLocalCmd?: string }) {
    return this.offresService.accepterCommande(this.selectedOffre.enteteCommandeId, params?.codeClientLocalCmd, params?.forceAccept);
  }

  async accepterCommande() {
    if (this.societe?.typeEntreprise === SocieteType.GROSSISTE) {
      if (this.selectedOffre?.clientLocal) {
        const clientGroupe: ClientView = await this.hasAccessService.getClientByCodeGroupe(this.selectedOffre?.client?.code);

        this.userInputService.confirm('Confirmation', 'Êtes vous sûr de vouloir accepter cette commande ?').then(
          async () => {
            if (!!clientGroupe?.accesClient) {

              if (!clientGroupe?.accesClient?.dateDesactivation) {
                this.excludeClientLocal(), this.finalizeAccepterCmd();
              } else {
                this.alertServ.error("L’accès du client sélectionné est actuellement désactivé, veuillez activer l’accès pour pouvoir accepter la commande.", 'MODAL');
              }
            } else {

              if (this.selectedOffre?.clientLocal instanceof Object) {

                (await this.activerAcces()).subscribe(_res => {
                  this.excludeClientLocal(), this.finalizeAccepterCmd();
                });
              } else {
                this.finalizeAccepterCmd(this.selectedOffre?.clientLocal);
              }
            }

          }, () => null);

      } else {
        this.alertServ.error('Veuillez sélectionner un client local pour cette commande', 'MODAL');
      }
    } else {
      this.finalizeAccepterCmd();
    }

  }

  excludeClientLocal(): void {
    this.selectedOffre = this.selectedOffre as Omit<Offre, 'clientLocal'>;
  }

  finalizeAccepterCmd(codeClientLocalCmd?: string): void {
    if ((this.isVenteDirecte())) {
      if (this.selectedOffre?.distributeur?.email) {
        this.processAccepterCmd();
      } else {
        this.alertServ.error(`Votre adresse email n'a pas encore été renseignée. Merci de la renseigner afin de pouvoir accepter des commandes.`, 'MODAL');
      }
    } else {
      this.processAccepterCmd(codeClientLocalCmd);
    }
  }

  processAccepterCmd(codeClientLocalCmd?: string): void {
    this.accepterCmd({ codeClientLocalCmd }).subscribe((res) => {
      if (!res?.length) {
        this.envoyerOuAccepterCmd();
      } else {
        this.alertServ.error(`Impossible d'accepter la commande, car certains produits sont en rupture de stock ou en quantité insuffisante.`, 'MODAL');

        this.offresService.produitsAvecStockInsuffisant$.next(
          res.map(prod => {
            const match = this.selectedOffre?.listeBlocs.filter(
              bloc => this.getParentBlocId(bloc, prod.codeProduitCatalogue)
            ).pop();

            return { blocId: match?.id, code: prod.codeProduitCatalogue };
          })
        );
      }
    });
  }

  private envoyerOuAccepterCmd() {
    if (this.isVenteDirecte()) {
      this.envoyerCommande();
    } else {
      //? COMMANDE ALREADY ACCEPTED, DISPLAY SUCCESS MESSAGE & RETURN TO LISTE COMMANDES 
      this.back();
      this.alertServ.success(`La commande: <b>${this.selectedOffre?.codeCommande}</b> sur offre: <b>${this.selectedOffre?.titre}</b> a été acceptée avec succès.`, 'MODAL');
    }
  }

  envoyerCommandeWithPrompt(): void {
    this.userInputService.confirm('Confirmation', `Êtes vous sûr de vouloir envoyer cette commande ?`).then(
      () => {
        this.envoyerCommande();
      },
      () => null
    );
  }

  envoyerCommande(): void {
    this.offresService.envoyerCommandeIndividuelle(
      this.selectedOffre?.enteteCommandeId, this.selectedOffre?.distributeur?.email
    ).subscribe(_res => {
      this.back(true);
      this.alertServ.success(`La commande: <b>${this.selectedOffre?.codeCommande}</b> sur offre: <b>${this.selectedOffre?.titre}</b> a été envoyée avec succès.`, 'MODAL');
    });
  }

  async activerAcces(): Promise<Observable<Object>> {
    const servicesClient = await this.gestionServiceClient.getListeServices();

    const accesClient = new AccesClient({
      listeServices: servicesClient,
      clientGroupe: this.selectedOffre?.client,
      codeClientGroupe: this.selectedOffre?.client?.code,
      codeClientLocal: ((this.selectedOffre?.clientLocal as ClientFournisseur)?.code as string)?.trim(),
      fournisseur: this.authService.getPrincipal()?.societe
    });

    return this.accesClientService.activerAccess(accesClient, true);
  }

  getParentBlocId(blocOffre: BlocOffre, codeProduit: string) {
    for (let fils of blocOffre?.listeFils) {
      if ((fils.codeProduitCatalogue === codeProduit) && fils.qteCmd > 0) {
        return true;
      }

      if (fils?.listeFils.length > 0) {
        return this.getParentBlocId(fils, codeProduit);
      }
    }

    return false;
  }

  refuserCommande() {
    this.userInputService.confirm('Confirmation', 'Êtes vous sûr de vouloir refuser cette commande ?').then(
      () => {
        this.offresService.refuserCommande(this.selectedOffre.enteteCommandeId, this.motifDeRefus).subscribe(() => {
          this.back();
        });
      }, () => null).then(() => this.modalService.dismissAll());
  }

  validerCommande() {
    this.userInputService.confirm('Confirmation', 'Êtes vous sûr de vouloir confirmer cette commande ?').then(
      (result) => {
        if (result) {
          this.offresService.valdierCommandeById(this.selectedOffre.enteteCommandeId).subscribe((res: Offre) => {
            this.back();
            this.alertServ.success(`Votre commande sur offre: <b>${this.selectedOffre?.titre}</b> a été confirmée avec succès.`, 'MODAL');
          });
        }

      }, () => null);
  }

  annulerCommande() {
    this.userInputService.confirm('Confirmation', 'Êtes vous sûr de vouloir annuler cette commande ?').then(
      (result) => {
        result && iif(
          () => this.selectedOffre?.commandStatut === 'BROUILLON',
          this.offresService.supprimerCommandeById(this.selectedOffre.enteteCommandeId),
          this.offresService.annulerCommandeById(this.selectedOffre.enteteCommandeId)
        ).subscribe(() => {
          this.back();
        });
      }, () => null);
  }

  back(modified = true) {
    this.submitted = modified;

    if (modified) {
      this.router.navigateByUrl('win-offre/commandes/list', { state: { modified: true } });
    } else {
      this.pageMode ?
        this.router.navigate(['win-offre/offres/list'], { queryParams: { mode: this.pageMode } }) :
        this.router.navigateByUrl('win-offre/commandes/list');
    }
  }

  openModal(modalContent: any, size = 'md', resetModal = false) {
    if (
      resetModal && (
        this.selectedOffre?.listeBlocs?.length === 1 &&
        this.selectedOffre?.listeBlocs[0]?.listeFils?.length === 1
      )) {
      this.offresService.reinitialiserEtatCommande(this.selectedOffre);
    } else {
      this.modalService
        .open(modalContent, {
          ariaLabelledBy: 'modal-basic-title',
          size,
          backdrop: 'static',
          windowClass: 'fs-radius-modal',
          modalDialogClass: 'fs-radius-modal',
          centered: true
        })
        .result.then(
          (result) => {
            console.log(result);
          },
          (reason) => {
            console.log('Err!', reason);
          }
        );
    }

  }

  validerAssistantCommande(applyChanges: boolean): void {
    if (applyChanges) {
      console.log("assistant form: ", this.assistantForm.value);
    }
  }

  isVenteDirecte(): boolean {
    return (this.societe?.typeEntreprise === SocieteType.FABRIQUANT) &&
      (this.selectedOffre?.distributeur?.typeEntreprise === SocieteType.FABRIQUANT);
  }

  imprimerCommandeIndividuelle(): void {
    this.blobUrl = null;
    this.offresService.imprimerCommandeIndividuelle(this.selectedOffre?.enteteCommandeId).subscribe(res => {
      this.blobUrl = res;
    });
  }

  hasUnsaved(): boolean {
    if (!this.readOnly) {
      if (this.isQuantityChanged() || this.hasClientOrClientLocal() || this.isDistributeurChanged() ||
        this.isComplexDistributeurCase() || this.isTransporteurChanged() || this.isModePaiementChanged() ||
        (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']) && this.isClientChanged())
      ) {
        return true;
      }
    }

    return false;
  }

  setSousBlocCollapseState(collapse: boolean) {
    this.offresService.setCollapseStateOnOffre(this.selectedOffre, collapse);
  }

  setConditionsEtRemisesDisplayState(show: boolean) {
    this.offresService.setConditionsAndPaliersRowDisplayState(this.selectedOffre, show);
  }

  private isQuantityChanged(): boolean {
    return this.selectedOffre?.totalQteCmd > this.initialSelectedOffreObj?.totalQteCmd;
  }

  private isClientChanged(): boolean {
    return !isEqual(this.selectedOffre?.client, this.initialSelectedOffreObj?.client);
  }

  private isDistributeurChanged(): boolean {
    return !isEqual(this.selectedOffre?.distributeur, this.initialSelectedOffreObj?.distributeur);
  }

  private hasClientOrClientLocal(): boolean {
    return (
      this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']) &&
      (!!this.selectedOffre?.client || !!this.selectedOffre?.clientLocal)
    );
  }

  private isComplexDistributeurCase(): boolean {
    return (
      !!this.selectedOffre?.distributeur &&
      this.selectedOffre?.distributeurs?.length > 1 &&
      !this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL'])
    );
  }

  private isTransporteurChanged(): boolean {
    return this.selectedOffre?.raisonSocialeTransporteur && !isEqual(this.selectedOffre?.transporteurCommande, this.initialSelectedOffreObj?.transporteurCommande);
  }

  private isModePaiementChanged(): boolean {
    return this.selectedOffre?.listDelaiPaiements?.length && !isEqual(this.selectedOffre?.delaiPaiement, this.initialSelectedOffreObj?.delaiPaiement);
  }

  private getProduitStockForOffre(offre: Offre) {
    if (this.authService.getNavigationSourceAndTarget() === 'WIN_OFFRE') {
      const listeCodeProduits = this.offresService.getListeCodeProduitForOffre(offre);

      if (listeCodeProduits && listeCodeProduits?.length) {
        this.offresService.fetchIndicateurStockWinPlus(listeCodeProduits).subscribe(indicateurStockResponse => {
          if (indicateurStockResponse && indicateurStockResponse?.length) {
            this.offresService.assignStockProduitWinplusToProduitOffre(offre, indicateurStockResponse);
          }
        });
      }
    }
  }

  pushMobilePageOptions() {
    this.deferredActionBtnService.pushPageOptions([
      {
        label: 'Enregistrer',
        iconClass: 'mdi mdi-content-save',
        shouldShow: !this.readOnly && (!this.selectedOffre?.commandStatut || this.selectedOffre?.commandStatut === 'NOUVELLE' || this.selectedOffre?.commandStatut === 'ACCEPTER'),
        action: () => this.save(),
      },
      {
        label: 'Réinitialiser',
        iconClass: 'mdi mdi-refresh',
        shouldShow: !this.readOnly,
        targetRoles: ['ROLE_AGENT_COMMERCIAL', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'],
        action: () => (this.offreForm.reset({ resetOption: 'O' }), this.openModal(this.resetModal, 'md', true)),
      },
      {
        label: 'Imprimer',
        iconClass: 'mdi mdi-printer',
        shouldShow: this.selectedOffre?.commandStatut === 'NOUVELLE' || this.selectedOffre?.commandStatut === 'ACCEPTER',
        action: () => this.imprimerCommandeIndividuelle(),
      },
      {
        label: 'Envoyer',
        iconClass: 'bi bi-send-fill',
        targetRoles: ['ROLE_AGENT_FOURNISSEUR'],
        shouldShow: this.readOnly && (this.selectedOffre?.commandStatut === 'ACCEPTER') && (this.selectedOffre?.distributeur?.typeEntreprise === 'FABRIQUANT') && ((this.societe?.typeEntreprise === 'GROSSISTE') || ((this.societe?.typeEntreprise === 'FABRIQUANT') && (this.selectedOffre?.distributeur?.typeEntreprise === 'FABRIQUANT'))),
        action: () => this.envoyerCommandeWithPrompt(),
      },
      {
        label: 'Accepter',
        iconClass: 'mdi mdi-check-all',
        targetRoles: ['ROLE_AGENT_FOURNISSEUR'],
        shouldShow: this.readOnly && (this.societe?.typeEntreprise === 'GROSSISTE') || ((this.societe?.typeEntreprise === 'FABRIQUANT') && (this.selectedOffre?.distributeur?.typeEntreprise === 'FABRIQUANT')),
        action: () => this.accepterCommande(),
      },
      {
        label: 'Refuser',
        iconClass: 'mdi mdi-cancel',
        targetRoles: ['ROLE_AGENT_FOURNISSEUR'],
        shouldShow: this.readOnly && (this.societe?.typeEntreprise === 'GROSSISTE') || ((this.societe?.typeEntreprise === 'FABRIQUANT') && (this.selectedOffre?.distributeur?.typeEntreprise === 'FABRIQUANT')),
        action: () => this.refuserCommande(),
      },
      {
        label: 'Confirmer',
        iconClass: 'mdi mdi-check',
        targetRoles: ['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_POINT_VENTE', 'ROLE_AGENT_COMMERCIAL', 'ROLE_ASSISTANT'],
        shouldShow: this.readOnly && this.selectedOffre?.commandStatut === 'BROUILLON',
        action: () => this.validerCommande(),
      },
      {
        label: this.selectedOffre?.commandStatut === 'BROUILLON' ? 'Annuler' : 'Annuler commande',
        iconClass: this.selectedOffre?.commandStatut === 'BROUILLON' ? 'mdi mdi-delete-forever' : 'mdi mdi-cancel',
        shouldShow: this.readOnly && (this.selectedOffre?.commandStatut === 'BROUILLON' || this.selectedOffre?.commandStatut === 'NOUVELLE') && (this.selectedOffre?.societeCreateurCmd?.id === this.societe?.id),
        targetRoles: ['ROLE_AGENT_POINT_VENTE', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL', 'ROLE_ASSISTANT'],
        action: () => this.annulerCommande(),
      },
      {
        label: 'Rechercher produit',
        iconClass: 'mdi mdi-magnify',
        shouldShow: true,
        action: () => this.rechercherProduitRef?.openSearchModal(),
      },
      {
        label: 'Quitter',
        iconClass: 'mdi mdi-close',
        shouldShow: true,
        action: () => this.back(),
      },
    ])
  }

  hasUnsavedData(): boolean {
    return !this.submitted && this.hasUnsaved();
  }
}
