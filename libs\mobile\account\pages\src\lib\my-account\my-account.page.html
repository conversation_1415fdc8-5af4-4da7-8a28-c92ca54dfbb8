<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/"></ion-back-button>
    </ion-buttons>
    <ion-title class="ion-text-start">{{'Mon Compte'}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-card class="acc-card">
    <ion-grid>
      <ion-row>
        <ion-col size="12">
          <ion-list lines="none">
            <ion-item class="padding-start-list ion-no-padding">
              <ion-avatar class="ion-text-center ion-flex item-with-icon cus-avatar">
                <ion-icon name="person"></ion-icon>
              </ion-avatar>

              <ion-label class="ion-text-wrap ion-padding-start"
                *ngIf="(accountInfo?.lastname || accountInfo?.firstname); else: aucune">{{(accountInfo?.lastname || '')
                + ' ' + (accountInfo?.firstname || '') | uppercase}}</ion-label>

              <ion-badge *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE']" class="ion-float-end cus-badge">
                Pharmacien(ne)
              </ion-badge>

              <ion-badge *jhiHasAnyAuthority="['ROLE_ASSISTANT']" class="ion-float-end cus-badge">
                Préparateur
              </ion-badge>

              <ion-badge *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']" class="ion-float-end cus-badge">
                Manager
              </ion-badge>

              <ion-badge *jhiHasAnyAuthority="['ROLE_AGENT_COMMERCIAL']" class="ion-float-end cus-badge">
                Commercial(e)
              </ion-badge>
            </ion-item>

            <ion-item class="padding-start-list ion-no-padding">
              <ion-avatar class="ion-text-center ion-flex item-with-icon cus-avatar">
                <ion-icon name="phone-portrait"></ion-icon>
              </ion-avatar>
              <ion-label class="ion-text-wrap ion-padding-start"
                *ngIf="accountInfo?.gsm; else aucune">{{accountInfo?.gsm}}</ion-label>
            </ion-item>

            <ion-item class="padding-start-list ion-no-padding">
              <ion-avatar class="ion-text-center ion-flex item-with-icon cus-avatar">
                <ion-icon name="mail"></ion-icon>
              </ion-avatar>
              <ion-label class="ion-text-wrap ion-padding-start"
                *ngIf="accountInfo?.email; else aucune">{{accountInfo?.email}}</ion-label>
            </ion-item>
          </ion-list>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col size="12">
          <ion-label class="label-title-acc">{{societe?.raisonSociale}}</ion-label>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="ion-padding-top" size="12">
          <ion-list lines="none" *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']">

            <ion-item class="padding-start-list ion-no-padding">
              <ion-avatar class="ion-text-center ion-flex item-with-icon cus-avatar">
                <ion-icon name="person"></ion-icon>
              </ion-avatar>

              <ion-label *ngIf="societe?.nomResponsable" class="ion-text-wrap ion-padding-start">
                {{ societe?.nomResponsable | uppercase }}
              </ion-label>

              <ion-label *ngIf="!societe?.nomResponsable" class="ion-text-wrap ion-padding-start">
                <i class="aucune">AUCUNE</i>
              </ion-label>

              <ion-badge class="ion-float-end cus-badge">
                Responsable
              </ion-badge>
            </ion-item>

            <ion-item class="padding-start-list ion-no-padding">
              <ion-avatar class="ion-text-center ion-flex item-with-icon cus-avatar">
                <ion-icon name="call"></ion-icon>
              </ion-avatar>
              <ion-label class="ion-text-wrap ion-padding-start"
                *ngIf="accountInfo?.tel; else aucune">{{accountInfo?.tel}}</ion-label>
            </ion-item>

            <ion-item class="padding-start-list ion-no-padding">
              <ion-avatar class="ion-text-center ion-flex item-with-icon cus-avatar">
                <ion-icon name="location"></ion-icon>
              </ion-avatar>

              <ion-label *ngIf="societe?.adresse && societe?.ville" class="ion-text-wrap ion-padding-start">
                {{societe?.adresse + ', ' + societe?.ville}}
              </ion-label>

              <ion-label *ngIf="!societe?.adresse && societe?.ville" class="ion-text-wrap ion-padding-start">
                {{societe?.ville}}
              </ion-label>

              <ion-label *ngIf="societe?.adresse && !societe?.ville" class="ion-text-wrap ion-padding-start">
                {{societe?.adresse}}
              </ion-label>

              <ion-label *ngIf="!societe?.adresse && !societe?.ville" class="ion-text-wrap ion-padding-start">
                <i class="aucune">AUCUNE</i>
              </ion-label>
            </ion-item>
            
            <ion-item-divider class="ion-no-padding ion-margin-bottom"></ion-item-divider>

            <ion-item lines="none" class="padding-start-list ion-no-padding ion-margin-bottom ion-margin-top ion-activatable" id="delete-acc-btn">
              <ion-avatar class="ion-text-center ion-flex item-with-icon-alt cus-avatar">
                <ion-icon name="trash"></ion-icon>
              </ion-avatar>

              <ion-label class="ion-text-wrap ion-padding-start">{{ 'Supprimer Mon Compte' }}</ion-label>
              <ion-icon name="arrow-forward" size="small" mode="ios" class="ion-float-end"></ion-icon>

              <ion-ripple-effect class="ripple"></ion-ripple-effect>
            </ion-item>

          </ion-list>

          <!--<div class="googlemap">
            <div id="mapId">
            </div>
          </div> -->

        </ion-col>
      </ion-row>
      
      <!--<ion-row>
        <ion-col size="12">
          <ion-label class="label-title-acc ion-text-center">
            Utilisateurs -->
            <!--<ion-icon name="add-circle" class="ion-float-end add-icon"
              (click)="openPage('account/ajouter-preparateur')"></ion-icon> -->
          <!--</ion-label>
        </ion-col>
      </ion-row> -->

      <!--<ion-row>
        <ion-col size="12">
          <ion-list lines="none">
            <ion-item class="padding-start-list ion-no-padding">
              <ion-avatar class="ion-text-center ion-flex item-with-icon cus-avatar">
                <span>{{'1'}}</span>
              </ion-avatar>

              <ion-label class="ion-text-wrap ion-padding-start">Abdellah Foutih</ion-label>
              
              <ion-badge class="ion-float-end cus-badge">
                Préparateur
              </ion-badge>
            </ion-item>
          </ion-list>
        </ion-col>
      </ion-row>-->

      <ion-row class="logout-row">
        <ion-button class="w-100 btn-logout" color="danger" (click)="logout()">Se déconnecter</ion-button>
      </ion-row>
    </ion-grid>
  </ion-card>
</ion-content>

<ng-template #aucune>
  <ion-label class="ion-text-wrap ion-padding-start"><i class="aucune">AUCUNE</i></ion-label>
</ng-template>

<ion-modal trigger="delete-acc-btn" #modal>
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-icon size="large" name="close" class="ion-no-padding" (click)="modal.dismiss()"></ion-icon>
        </ion-buttons>
        <ion-title class="ion-text-start">{{'Supprimer Mon Compte'}}</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content [fullscreen]="true" class="ion-padding">
      <p class="ion-text-center" style="padding-top: 2rem">
        <b style="font-size: large;">Êtes-vous sûr de vouloir supprimer votre compte?</b> <br>
        <span>
          Cette action est irréversible et entraînera les conséquences suivantes :
          <ul class="ion-text-start">
            <li>Vous perdrez toutes les données qui étaient enregistrées sur votre compte, telles que votre profil, vos préférences, votre historique, vos signets, etc.</li>
            <li>Vous ne pourrez pas récupérer votre compte ou vos données à l’avenir, même si vous réinstallez l’application ou utilisez un autre appareil.</li>
          </ul> <br>
          Si vous souhaitez toujours supprimer votre compte, veuillez appuyer sur le bouton “<b>Confirmer</b>”. Sinon, appuyez sur le texte “<b>Annuler</b>” pour revenir à l’application.
        </span>
      </p>

      <ion-item lines="none">
        <ion-button (click)="confirmAccountDeletion()" color="danger" class="w-100 ion-padding-top ion-padding-bottom ion-margin-bottom" expand="block">Confirmer</ion-button>
      </ion-item>

      <ion-item (click)="modal.dismiss()" lines="none" class="ion-text-center ion-margin-top">
        <ion-label>
          <u>Annuler</u> 
        </ion-label>
      </ion-item>
    </ion-content>
  </ng-template>
</ion-modal>
