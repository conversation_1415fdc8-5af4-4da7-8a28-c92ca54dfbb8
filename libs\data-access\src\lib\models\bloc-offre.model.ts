import { DetailValeurPalier } from './detail-valeur-palier.model';
import { Catalogue } from './catalogue.model';
import { Offre } from './offre.model';
import { SelectedTypeRemiseEnum } from './selected-type-remise.enum';
import { ConditionBlocOffreCommandeConsolidee } from '@wph/federation-syndicats';
import { DocMetaDataDto } from '@wph/shared';

export class BlocOffre {

    id?: number;

    titre?: string;

    description?: string;

    typeBloc: string;

    listeFils: BlocOffre[];

    blocOrder?: number;

    blocObligatoire?: string;
    hash?:string;

    docImageBlocOffre?: DocMetaDataDto;


    // conditions
    valeurMin?: number;

    valeurMax?: number;

    qteMin?: number;

    qteMax?: number;

    nbrObjFilsMin?: number;

    nbrObjFilsMax?: number;

    nombreProduitsMin?: number;

    nombreProduitsMax?: number;

    conditionsString?: string;

    dynamicScriptCondition?: string;     // TODO: AGA  take this into UI

    ratioUg?: string;
    // fin conditions


    typeRemise?: string;    // non used ???


    coffretEnabled?: boolean;    
    qteFixePrdInCoffret?: number;   

    designationProduit?: string // FE use only


    listOfBlocOffresPrerequis?: number[];  // liste des id

    listOfBlocOffresExclus?: number[];  // liste des ids

    listePaliers: DetailValeurPalier[];

    detailCommandeId?: number;

    // cas produit:
    catalogue?: Catalogue;

    hasStockProduitWinplus?: boolean;
    stockProduitWinplus?: number;
    codeProduitCatalogue?: string;

    libelleProduit?: string;

    prixVenteHt?: number;

    prixVenteTtc?: number;

    ppv?: number;

    plafondRemiseSpeciale?: number;

    plafondUgSpeciale?: number;

    multipleQtePrdCmd?: number;

    onlyMultipleQteCmd?: string;
    // fin cas produit


    colisage?: number;

    displayConditionsEtRemisesRow?: boolean = false; // transient

    displayDetailsSousBloc?: boolean | null = true; // transient

    // saisie cmd ligne produit
    qteCmd?: number;
    // fin saisie ligne prd

    selectedTypeRemiseEnum?: SelectedTypeRemiseEnum;

    enteteCommandeId?: number

    // champs calculés
    parent?: BlocOffre; // rempli après obtention de toute l'arborescence
    offre?: Offre; // rempli après obtention de toute l'arborescence

    totalQteCmd?: number;
    totalValeurBruteCmd?: number;   // may be ttc or ht   selon config offre
    totalValeurNetteCmd?: number;   // may be ttc or ht   selon config offre

    totalValeurBruteCmdTtc?: number;   // transient (used in frontend only)
    totalValeurNetteCmdTtc?: number;   // transient (used in frontend only)
    totalValeurBruteCmdHt?: number;   // transient (used in frontend only)
    totalValeurNetteCmdHt?: number;   // transient (used in frontend only)

    totalNbrObjFils?: number;
    totalNombreProduits?: number;
    totalNbrPackCmd?: number;
    etat?: string;      // 'N' pour nonsaisi, 'V' pour valide, 'I' pour invalide
    messageEtat?: string;       // utilisé principalement pour afficher msg d'erreur quand etat='I'

    mapBlocsByMemberId?: IMapBlockMembre;
    mapNbrByMemberId?: any; 

    tauxRemise?: number;
    tauxUg?: number;
    totalQteUg?: number;
    qteUgSaisie?: number;
    // fin champs calculés

    toJSON?: any;


    codeExclusion?: string;    //   used only in edit listeExclusion  TODO: AGA  take this into UI/UX

    
    selectedPalier?: DetailValeurPalier;


    totalQtePrdFixe?: number;

    conditionCmdUnitaireSpecGroup?: ConditionBlocOffreCommandeConsolidee;


    subscribers?: BlocOffre[];   // transient


    
	currentSelectedPalier: DetailValeurPalier;   // transient
	previousSelectedPalier: DetailValeurPalier;   // transient
}

export interface IMapBlockMembre {
  [index: number]: BlocOffre;
}



export function toJSON() {
    const obj: any = {};
    const keysToOmit = ['parent', 'offre', 'subscribers'];
    Object.keys(this).forEach(function(key) {
    if (keysToOmit.indexOf(key) === -1) {
    obj[key] = this[key];
    }
}, this);
    return obj;
}

