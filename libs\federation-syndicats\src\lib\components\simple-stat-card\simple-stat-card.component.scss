::ng-deep #WIN_GROUPE-container {
    .card-icon-container {
        width: 35px;
        height: 35px;
        color: #fff;
        border-radius: var(--winoffre-base-border-radius);
        background: var(--wf-primary-300);
    }
}

::ng-deep #FEDERATION_SYNDICAT-container {
    .card-icon-container {
        width: 35px;
        height: 35px;
        color: #fff;
        border-radius: var(--winoffre-base-border-radius);
        background: var(--fs-primary-400);
    }
}

.card-radius {
    width: 100%;
    height: fit-content;
    //max-width: 400px !important;
    border-radius: var(--winoffre-base-border-radius); 
}

.card-labels {
    .label {
        font-size: .85rem;
        font-weight: 600;
        color: #3e3e3e;
    }

    .value {
        font-size: 1.3rem;
        font-weight: 700;
        color: black;

        .pos-growth {
            font-size: 1rem !important;
            color: var(--fs-success) !important;
        }

        .neg-growth {
            font-size: 1rem !important;
            color: var(--fs-danger) !important;
        }
    }
    
    .value-alt {
        font-size: 1.05rem;
        font-weight: 700;
        color: black;
    }

    .single-layer {
        padding: 10px 5px;
    }
}
