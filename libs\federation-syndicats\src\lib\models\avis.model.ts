import { EnteteCommandeConsolideeMarche } from "./entete-commande.model";
import { EntrepriseDTO } from "./entreprise.model";
import { GroupeEntreprise } from "./groupe-entreprise.model";
import { PharmacieEntreprise } from "./pharmacie-entreprise.model";

export enum TypeAvis {
    Positive = 'P',
    Negative = 'N'
  }

export interface Avis {
    audited?: boolean;
    commentaire?: string;
    dateCreation?: string;
    enteteCommandeConsolideeMarche?: EnteteCommandeConsolideeMarche;
    entCmdUnitaireMarcheId?: number;
    estResponsable?: boolean;
    groupeEntreprise?: GroupeEntreprise;
    id?: number;
    laboratoire?: number;
    livraison?: number;
    paiement?: number;
    qualite?: number;
    raison?: string;
    reduction?: number;
    sondeurEntreprise?: PharmacieEntreprise;
    typeAvis?: TypeAvis;
  }

  export interface AvisDTO {
    id?: number;
    typeAvis?: TypeAvis;
    commentaire?: string;
    sondeurEntreprise?: PharmacieEntreprise;
    groupeEntreprise?: GroupeEntreprise;
    estResponsable?: boolean;
    dateCreation?: string; 
    enteteCommandeConsolideeMarche?: EnteteCommandeConsolideeMarche;
    qualite?: number;
    reduction?: number;
    paiement?: number;
    livraison?: number;
    laboratoire?: number;
    raison?: string;
  }

  export interface AvisCriteria {
    commandeConsolideeId?: number;
    dateDebut?: string; 
    dateFin?: string; 
    estResponsable?: boolean;
    groupeEntrepriseId?: number;
    offreCmdId?: number;
    id?: number;
    sondeurId?: number;
    typeAvis?: TypeAvis;
  }