<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-4">Demandes d'accès</h4>

        <div class="col-8 px-1">
            <div class="row justify-content-end align-items-center">
                <button *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']"
                    (click)="openCreationDemandeAccesClientModal(creationDemandeAccessAdmin)"
                    class="btn btn-sm text-white btn-warning m-1 d-flex align-items-center k-gap-1" type="button">
                    <i class="mdi mdi-plus"></i>
                    <span class="d-lg-none d-block">Accès</span>
                    <span class="d-lg-block d-none">Créer un accès Client</span>
                </button>

                <button (click)="openFilterModal(filterModal)" type="button" class="btn btn-sm btn-info m-1">
                    <i class="mdi mdi-filter-variant"></i>
                    Filtrer
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="card">
    <kendo-grid [data]="demandesAccesData" [pageable]="{
        buttonCount: 5,
        info: true,
        type: 'numeric',
        pageSizes: pageSizes,
        previousNext: true,
        position: 'bottom'
      }" [pageSize]="navigation.pageSize" [skip]="navigation.skip" style="min-height: calc(100vh - 123px)"
        (pageChange)="pageChange($event)" [sortable]="{ mode: 'single'}" [sort]="sort" [resizable]="true"
        (sortChange)="sortChange($event)">

        <kendo-grid-column *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" [width]="180" field="fournisseur.raisonSociale"
            class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap d-flex align-items-center">Fournisseur</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.fournisseur?.raisonSociale }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="180" field="raisonSocialeLocal" class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap d-flex align-items-center">Raison Sociale</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.raisonSocialeLocal }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="160" field="nomPharmacienLocal" class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap d-flex align-items-center">Nom Responsable</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.nomPharmacienLocal }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="180" field="villeLocal" class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap d-flex align-items-center">Ville</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.villeLocal }}
            </ng-template>

        </kendo-grid-column>

        <kendo-grid-column *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" [width]="80" field="classification"
            class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap d-flex align-items-center">Class</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.classification }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="120" field="dateCreation" class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap d-flex align-items-center">Date Création</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.dateCreation | momentTimezone: 'yyyy-MM-DD HH:mm' : 'Africa/Casablanca' }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="120" field="dateTraitement" class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap d-flex align-items-center">Date Traitement</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.dateTraitement | momentTimezone: 'yyyy-MM-DD HH:mm' : 'Africa/Casablanca' }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="130" [sortable]="false" class="no-ellipsis">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap d-flex align-items-center justify-content-center w-100">État Demande</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                <div class="d-flex align-items-center justify-content-center">
                    <span *ngIf="dataItem?.etatDemandeAcces === 'A'"
                        class="badge badge-success rounded-pill py-1 fixed-width-badge px-2">
                        {{'Acceptée' | uppercase}}
                    </span>

                    <span *ngIf="dataItem?.etatDemandeAcces === 'R'"
                        class="badge badge-danger rounded-pill py-1 fixed-width-badge px-2">
                        {{'Refusée' | uppercase}}
                    </span>

                    <span *ngIf="dataItem?.etatDemandeAcces === 'E'"
                        class="badge badge-light rounded-pill py-1 fixed-width-badge px-2">
                        {{'En Attente' | uppercase}}
                    </span>
                </div>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="65" [sortable]="false" class="text-start no-ellipsis">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-center">Action</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                <div class="d-flex justify-content-center k-gap-1">
                    <span (click)="consulterDemandeAcces(activationAccesModal, dataItem)"
                        class="circle circle-btn btn-warning pointer-cus" title="Consulter Demande">
                        <i class="mdi mdi-eye text-white"></i>
                    </span>

                    <span [ngClass]="{'opacity-light': dataItem?.etatDemandeAcces !== 'A'}"
                        (click)="consulterDemandeAcces(credentialsModal, dataItem, 'lg', true)"
                        class="circle circle-btn btn-info pointer-cus" title="Consulter Identifiants">
                        <i class="mdi mdi-lock-open text-white"></i>
                    </span>
                </div>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-messages pagerItems="lignes" pagerOf="de"
            pagerItemsPerPage="éléments par page"></kendo-grid-messages>

        <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun résultat trouvé.</span>
        </ng-template>
    </kendo-grid>
</div>


<ng-template #filterModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title text-dark">Filtrer</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss()">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <form [formGroup]="filterForm" (ngSubmit)="appliquerFiltre(); modal.dismiss()" wphFocusTrap>
        <div class="modal-body">
            <div class="d-flex row justify-content-start p-0 m-0 flex-wrap w-100">
                <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                    <div class="col-lg-6 col-12">
                        <label for="raisonSociale" class="col-form-label text-dark">Fournisseur</label>
                        <div id="client-picker-input" class="input-group picker-input">
                            <input id="raisonSociale" [ngbTypeahead]="searchFournisseur"
                                [inputFormatter]="fournisseurFormatter" [resultFormatter]="fournisseurFormatter"
                                [resultTemplate]="clientSearchTemplate" type="text" formControlName="fournisseurId"
                                class="form-control form-control-md text-dark pl-4 b-radius"
                                style="border-radius: 10px !important;">

                            <ng-template #clientSearchTemplate let-result="result">
                                <div>
                                    <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                                </div>
                                <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                            </ng-template>

                            <div class="picker-icons picker-icons-alt">
                                <i class="mdi mdi-magnify"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <label for="codeClientGroupe" class="col-form-label text-dark">Code Client Groupe</label>
                        <div class="input-group">
                            <input id="codeClientGroupe" type="text" formControlName="codeClientGroupe"
                                class="form-control form-control-md text-dark  b-radius">
                        </div>
                    </div>

                    <div class="col-lg-6 col-12">
                        <label for="classification" class="col-form-label text-dark">Classification</label>
                        <div class="input-group">
                            <input id="classification" type="text" formControlName="classification"
                                class="form-control form-control-md text-dark  b-radius">
                        </div>
                    </div>
                </ng-container>

                <div class="col-lg-6 col-12">
                    <label for="codeClientLocal" class="col-form-label text-dark">Code Client Local</label>
                    <div class="input-group">
                        <input id="codeClientLocal" type="text" formControlName="codeClientLocal"
                            class="form-control form-control-md text-dark  b-radius">
                    </div>
                </div>

                <div class="col-lg-6 col-12">
                    <label for="etatDemande" class="col-form-label text-dark">État Demande</label>
                    <div class="input-group">
                        <select2 formControlName="etatsDemande" [multiple]="true" [data]="selectStatutData"
                            class="form-control-md p-0 w-100"></select2>
                    </div>
                </div>
            </div>

            <hr *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" class="w-100">

            <div class="d-flex row justify-content-start p-0 m-0 flex-wrap w-100">
                <div class="col-lg-6 col-12">
                    <label for="dateCreationDebut" class="col-form-label text-dark">Date Création Début</label>
                    <app-date-picker formControlName="dateCreationFrom"></app-date-picker>
                </div>

                <div class="col-lg-6 col-12">
                    <label for="dateCreationFin" class="col-form-label text-dark">Date Création Fin</label>
                    <app-date-picker formControlName="dateCreationTo"></app-date-picker>
                </div>

                <div class="col-lg-6 col-12">
                    <label for="dateTraitementFrom" class="col-form-label text-dark">Date Traitement Début</label>
                    <app-date-picker formControlName="dateTraitementFrom"></app-date-picker>
                </div>

                <div class="col-lg-6 col-12">
                    <label for="dateTraitementTo" class="col-form-label text-dark">Date Traitement Fin</label>
                    <app-date-picker formControlName="dateTraitementTo"></app-date-picker>
                </div>
            </div>

        </div>

        <div class="modal-footer">
            <button type="button" (click)="modal.dismiss()" class="btn btn-light" tabindex="-1">Fermer</button>
            <button type="button" (click)="vider(); modal.dismiss()" class="btn btn-secondary text-white"
                tabindex="-1">Vider</button>
            <button type="submit" class="btn btn-primary ml-1 text-white" tabindex="-1">Appliquer</button>
        </div>
    </form>
</ng-template>

<ng-template #motifDeRefusModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Veuillez spécifier le motif de refus</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <div class="row p-2">
        <div class="col-sm-12">
            <label for="motif" class="form-label text-dark">Motif de refus <span class="text-danger"
                    style="font-size: 0.75rem;">(obligatoire*)</span></label>
            <textarea type="text" [(ngModel)]="motifDeRefus" rows="3" class="form-control form-control-md b-radius"
                id="motif"></textarea>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light">Fermer</button>
        <button type="button" [disabled]="!motifDeRefus?.length" type="submit"
            (click)="confirmerRefusDemandeAcces(modal)" class="btn btn-danger ml-1">Refuser</button>
    </div>
</ng-template>

<ng-template #credentialsModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">CONSULTER IDENTIFIANTS</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <div class="modal-body px-lg-0 px-2 py-2 m-0">
        <div class="row d-flex flex-wrap justify-content-start w-100 m-0">
            <div class="col-lg-6 col-12">
                <label for="raisonSociale" class="col-form-label text-dark">Raison Sociale</label>
                <div class="input-group">
                    <input id="raisonSociale" type="text" [readonly]="true" name="raisonSocialeGroupe"
                        [value]="selectedDemande?.raisonSocialeGroupe ?? ''"
                        class="form-control form-control-md text-dark  b-radius">
                </div>
            </div>

            <div class="col-lg-6 col-12">
                <label for="nomResponsable" class="col-form-label text-dark">Nom Responsable</label>
                <div class="input-group">
                    <input id="nomResponsable" type="text" [readonly]="true" name="nomPharmacienGroupe"
                        [value]="selectedDemande?.nomPharmacienGroupe ?? ''"
                        class="form-control form-control-md text-dark  b-radius">
                </div>
            </div>

            <div class="col-lg-6 col-12">
                <label for="codeClientGroupe" class="col-form-label text-dark">Identifiant Utilisateur</label>
                <div class="input-group">
                    <input id="codeClientGroupe" type="text" [readonly]="true" name="codeClientGroupe"
                        [value]="selectedDemande?.codeClientGroupe ?? ''"
                        class="form-control form-control-md text-dark  b-radius">

                    <div class="input-group-append"
                        style="border-top-right-radius: 10px; border-bottom-right-radius: 10px;">
                        <button class="btn btn-md btn-dark text-white" type="button"
                            title="Copier l'identifiant utilisateur"
                            (click)="copyToClipboard(selectedDemande?.codeClientGroupe, 'code')">
                            <i class="mdi mdi-content-copy"
                                [ngClass]="{'mdi-content-copy': !copyCode, 'mdi-check': copyCode}"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-12">
                <label for="mdp" class="col-form-label text-dark">Mot de passe par défaut</label>
                <div class="input-group">
                    <input id="mdp" type="text" [readonly]="true" name="nomPharmacienGroupe"
                        [value]="selectedDemande?.codeClientGroupe + '2024'"
                        class="form-control form-control-md text-dark  b-radius">

                    <div class="input-group-append">
                        <button class="btn btn-md btn-dark text-white" type="button" title="Copier le mot de passe"
                            (click)="copyToClipboard(selectedDemande?.codeClientGroupe + '2024', 'mdp')">
                            <i class="mdi mdi-content-copy"
                                [ngClass]="{'mdi-content-copy': !copyMdp, 'mdi-check': copyMdp}"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light">Fermer</button>
    </div>
</ng-template>


<ng-template #activationAccesModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title text-dark">CONSULTER DEMANDE D'ACCÈS</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss()">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <form>
        <div id="DEFAULT-container" class="modal-body p-2 m-0" style="padding-bottom: 130px !important">
            <div id="modRow" class="row d-flex flex-wrap justify-content-start w-100 mx-0 my-2">
                <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                    <ng-container *ngIf="isEditing">
                        <div class="col-lg-5 col-12">
                            <label for="clientGroupeModCode" class="col-form-label text-dark">Rechercher par
                                code</label>

                            <div id="client-picker-input" class="input-group picker-input">
                                <input id="clientGroupeModCode" (focus)="searchClientIptCode.select()"
                                    [ngbTypeahead]="searchClientByCode" [(ngModel)]="selectedClientGroupeToModifyCode"
                                    [inputFormatter]="clientFormatter" [resultFormatter]="clientFormatter"
                                    [title]="searchClientIptCode?.value" [resultTemplate]="clientSearchTemplate"
                                    type="search" name="clientGroupeMod"
                                    class="form-control form-control-md text-dark pl-4 b-radius"
                                    style="border-radius: 10px !important;" #searchClientIptCode>

                                <ng-template #clientSearchTemplate let-result="result">
                                    <div class="d-flex w-100 px-2 align-items-center k-gap-2">
                                        <ngb-highlight class="col-4 p-0 m-0 text-wrap h5"
                                            [result]="result?.raisonSociale"></ngb-highlight>

                                        <span
                                            class="col-4 m-0 px-1 py-0 d-flex justify-content-start align-items-center justify-content-start">
                                            <i class="bi bi-person-circle mr-1"></i>
                                            <span class="h6 text-wrap">{{ result?.nomResponsable }}</span>
                                        </span>

                                        <span
                                            class="col-4 m-0 px-1 py-0 d-flex justify-content-start align-items-center">
                                            <i class="bi bi-geo-alt-fill mr-1"></i>
                                            <span class="h6 text-wrap">{{ result?.ville }}</span>
                                        </span>
                                    </div>
                                </ng-template>

                                <div class="picker-icons picker-icons-alt">
                                    <i class="mdi mdi-magnify"></i>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-5 col-12">
                            <label for="clientGroupeMod" class="col-form-label text-dark">Rechercher par raison
                                sociale</label>

                            <div id="client-picker-input" class="input-group picker-input">
                                <input id="clientGroupeMod" (focus)="searchClientIpt.select()"
                                    [ngbTypeahead]="searchClient" [(ngModel)]="selectedClientGroupeToModify"
                                    [inputFormatter]="clientFormatter" [resultFormatter]="clientFormatter"
                                    [title]="searchClientIpt?.value" [resultTemplate]="clientSearchTemplate"
                                    type="search" name="clientGroupeMod"
                                    class="form-control form-control-md text-dark pl-4 b-radius"
                                    style="border-radius: 10px !important;" #searchClientIpt>

                                <ng-template #clientSearchTemplate let-result="result">
                                    <div class="d-flex w-100 px-2 align-items-center k-gap-2">
                                        <ngb-highlight class="col-4 p-0 m-0 text-wrap h5"
                                            [result]="result?.raisonSociale"></ngb-highlight>

                                        <span
                                            class="col-4 badge badge-light m-0 p-1 d-flex justify-content-start align-items-center justify-content-start text-wrap">
                                            <i class="bi bi-person-circle mr-1"></i>
                                            <span class="h6">{{ result?.nomResponsable }}</span>
                                        </span>

                                        <span
                                            class="col-4 badge badge-dark m-0 p-1 d-flex justify-content-start align-items-center text-wrap">
                                            <i class="bi bi-geo-alt-fill mr-1"></i>
                                            <span class="h6">{{ result?.ville }}</span>
                                        </span>
                                    </div>
                                </ng-template>

                                <div class="picker-icons picker-icons-alt">
                                    <i class="mdi mdi-magnify"></i>
                                </div>
                            </div>
                        </div>

                        <div class="col py-1 d-flex k-gap-2 align-items-end">
                            <span (click)="appliquerModificationClientGroupe()"
                                class="actions-icons btn-success pointer-cus" title="Appliquer les modifications">
                                <i class="bi bi-check-lg"></i>
                            </span>

                            <span (click)="annulerModificationClientGroupe()"
                                class="actions-icons btn-danger pointer-cus" title="Annuler les modifications">
                                <i class="bi bi-x"></i>
                            </span>
                        </div>
                    </ng-container>
                </ng-container>
            </div>

            <kendo-grid id="client-groupe-grid" [data]="{data: [selectedDemande], total: 1}"
                class="fs-grid fs-grid-white mb-4" [pageable]="false" [pageSize]="navigation.pageSize"
                [skip]="navigation.skip" (pageChange)="pageChange($event)" [sortable]="false" [sort]="sort"
                [resizable]="true">

                <ng-template kendoGridToolbarTemplate>
                    <div class="d-flex row p-0 my-0 mx-0 w-100 align-items-center k-gap-3 grid-top-radius">
                        <span class="h4 text-dark">{{'informations pharmacie du maroc' | uppercase }}</span>

                        <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                            <span *ngIf="!isEditing && selectedDemande?.etatDemandeAcces === 'E'"
                                (click)="isEditing = !isEditing" class="actions-icons text-dark pointer-cus"
                                title="Modifier Pharmacie du Maroc">
                                <i class="bi bi-pencil-square"></i>
                            </span>
                        </ng-container>
                    </div>
                </ng-template>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Raison Sociale</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.raisonSocialeGroupe ?? ''}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Nom Responsable</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{ dataItem?.nomPharmacienGroupe ?? '' }}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Ville</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.villeGroupe ?? ''}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Localité</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.localiteGroupe ?? ''}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Adresse</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.clientGroupe?.adresse ?? ''}}</span>
                    </ng-template>
                </kendo-grid-column>
            </kendo-grid>

            <kendo-grid id="client-local-grid" [data]="{data: [selectedDemande], total: 1}"
                class="fs-grid fs-grid-white" [pageable]="false" [pageSize]="navigation.pageSize"
                [skip]="navigation.skip" (pageChange)="pageChange($event)" [sortable]="false" [sort]="sort"
                [resizable]="true" style="overflow: visible">

                <ng-template kendoGridToolbarTemplate>
                    <div
                        class="d-flex row p-0 my-0 mx-0 w-100 align-items-center justify-content-between grid-top-radius">
                        <div class="col m-0 p-0 d-flex justify-content-start">
                            <span class="h4 text-dark">{{ 'informations Client Local' | uppercase }}</span>
                        </div>

                        <div class="col m-0 p-0 d-flex justify-content-start">
                            <span class="h4 text-dark">{{ selectedDemande?.fournisseur?.raisonSociale }} | CODE LOCAL :
                                {{ selectedDemande?.codeClientLocal }}</span>
                        </div>
                    </div>
                </ng-template>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Raison Sociale</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.raisonSocialeLocal ?? '---'}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Nom Responsable</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{ dataItem?.nomPharmacienLocal ?? '---' }}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Ville</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.villeLocal ?? '---'}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Localité</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.localiteLocal ?? '---'}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Adresse</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.adresseLocal ?? '---'}}</span>
                    </ng-template>
                </kendo-grid-column>
            </kendo-grid>

            <hr class="w-100 mt-4">

            <div class="row d-flex w-100 mx-0 my-2 justify-content-end">
                <div class="col-lg-6 col-12">
                    <label for="gsmPharmacieLocal" class="col-form-label text-dark">GSM</label>
                    <div class="input-group">
                        <input id="gsmPharmacieLocal" [readOnly]="true" [value]="selectedDemande?.gsm ?? ''" type="text"
                            name="gsm" class="form-control form-control-md text-dark  b-radius">
                    </div>
                </div>

                <div class="col-lg-6 col-12">
                    <label for="emailPharmacieLocal" class="col-form-label text-dark">E-mail</label>
                    <div class="input-group">
                        <input id="emailPharmacieLocal" [readOnly]="true" [value]="selectedDemande?.email ?? ''"
                            type="text" class="form-control form-control-md text-dark  b-radius">
                    </div>
                </div>

                <div *ngIf="selectedDemande?.motifRefus && selectedDemande?.etatDemandeAcces === 'R'" class="col-12">
                    <label for="emailPharmacieLocal" class="col-form-label text-dark">Motif de refus</label>
                    <div class="input-group">
                        <textarea type="text" [value]="selectedDemande?.motifRefus" rows="2" [readOnly]="true"
                            class="form-control form-control-md b-radius" id="motif"></textarea>
                    </div>
                </div>
            </div>

        </div>

        <div class="modal-footer d-flex justify-content-between">
            <button type="button" (click)="modal.dismiss()" class="btn btn-light" tabindex="-1">Fermer</button>

            <div class="col p-0 m-0 k-gap-2 d-flex justify-content-end">
                <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                    <button type="button" (click)="consulterClientFournisseur(selectedDemande)"
                        class="btn btn-warning text-white" tabindex="-1">Consulter Assoc.</button>

                    <ng-container *ngIf="selectedDemande?.etatDemandeAcces === 'E'">
                        <button type="button" (click)="openRefuserDemandeAccesModal(motifDeRefusModal, selectedDemande)"
                            class="btn btn-danger" tabindex="-1">Refuser <span class="d-none d-lg-inline">la
                                demande</span></button>
                        <button type="button" (click)="accepterDemandeAcces(selectedDemande)" class="btn btn-success"
                            tabindex="-1">Accepter <span class="d-none d-lg-inline">la demande</span></button>
                    </ng-container>
                </ng-container>

                <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']">
                    <ng-container *ngIf="selectedDemande?.etatDemandeAcces === 'R'">
                        <button (click)="renvoyerDemandeAccess(modal)" type="button" class="btn btn-primary"
                            tabindex="-1">Renvoyer <span class="d-none d-lg-inline">la demande</span></button>
                    </ng-container>
                </ng-container>
            </div>
        </div>
    </form>
</ng-template>

<ng-template #associationClientFournModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">CONSULTATION DES ASSOCIATIONS DU CLIENT</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <div id="DEFAULT-container" class="modal-body px-lg-0 px-2 py-2 m-0">
        <kendo-grid [data]="assocClientFounisseurData" [pageable]="true"
            [pageSize]="assocClientFounisseurData?.total ?? 20" [skip]="0" [sortable]="false"
            class="fs-grid fs-grid-white">
            <kendo-grid-column [width]="80">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Code Local</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                    {{ dataItem?.code }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="180">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Fournisseur</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.noeud?.nom }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="180" class="text-wrap">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Raison Sociale</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.raisonSociale }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="180" class="text-wrap">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Nom Responsable</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.nomPharmacien }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="150" class="text-wrap">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Ville</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.ville }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="150" class="text-wrap">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Localité</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.localite }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="180" class="text-wrap">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Adresse</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.adresse }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="100" class="text-center no-ellipsis">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Dispose Accès</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    <span *ngIf="!dataItem?.hasAccess" class="badge badge-light rounded-pill py-1 px-2">Non</span>
                    <span *ngIf="dataItem?.hasAccess" class="badge badge-success rounded-pill py-1 px-2">Oui</span>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="100" class="text-center no-ellipsis">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Est Primaire</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    <span *ngIf="!dataItem?.estPrimaire" class="badge badge-light rounded-pill py-1 px-2">Non</span>
                    <span *ngIf="dataItem?.estPrimaire" class="badge badge-success rounded-pill py-1 px-2">Oui</span>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-messages pagerItems="lignes" pagerOf="de"
                pagerItemsPerPage="éléments par page"></kendo-grid-messages>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>
        </kendo-grid>
    </div>

    <div class="modal-footer d-flex justify-content-end">
        <button class="btn btn-light" type="button" (click)="modal.dismiss()">Fermer</button>
    </div>
</ng-template>

<ng-template #creationDemandeAccessAdmin let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">CRÉATION D'UN ACCÈS CLIENT</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <form>
        <div id="DEFAULT-container" class="modal-body p-2 m-0" style="padding-bottom: 130px !important">
            <form id="modRow" (ngSubmit)="appliquerModificationClientGroupeAdmin()" wphFocusTrap
                class="row d-flex flex-wrap justify-content-start w-100 mx-0 my-2">
                <div class="col-lg-5 col-12">
                    <label for="clientGroupeModCode" class="col-form-label text-dark">Code client groupe</label>

                    <div id="client-picker-input" class="input-group picker-input">
                        <input id="clientGroupeModCode" (focus)="searchClientIptCode.select()"
                            [ngbTypeahead]="searchClientByCode" [(ngModel)]="selectedClientGroupeToModifyCode"
                            [inputFormatter]="clientFormatter" [resultFormatter]="clientFormatter"
                            [title]="searchClientIptCode?.value" [resultTemplate]="clientSearchTemplate" type="search"
                            name="clientGroupeMod" class="form-control form-control-md text-dark pl-4 b-radius"
                            style="border-radius: 10px !important;" #searchClientIptCode>

                        <ng-template #clientSearchTemplate let-result="result">
                            <div class="d-flex w-100 px-2 align-items-center k-gap-2">
                                <ngb-highlight class="col-4 p-0 m-0 text-wrap h5"
                                    [result]="result?.raisonSociale"></ngb-highlight>

                                <span
                                    class="col-4 m-0 px-1 py-0 d-flex justify-content-start align-items-center justify-content-start">
                                    <i class="bi bi-person-circle mr-1"></i>
                                    <span class="h6 text-wrap">{{ result?.nomResponsable }}</span>
                                </span>

                                <span class="col-4 m-0 px-1 py-0 d-flex justify-content-start align-items-center">
                                    <i class="bi bi-geo-alt-fill mr-1"></i>
                                    <span class="h6 text-wrap">{{ result?.ville }}</span>
                                </span>
                            </div>
                        </ng-template>

                        <div class="picker-icons picker-icons-alt">
                            <i class="mdi mdi-magnify"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-5 col-12">
                    <label for="clientGroupeMod" class="col-form-label text-dark">Raison sociale groupe</label>

                    <div id="client-picker-input" class="input-group picker-input">
                        <input id="clientGroupeMod" (focus)="searchClientIpt.select()" [ngbTypeahead]="searchClient"
                            [(ngModel)]="selectedClientGroupeToModify" [inputFormatter]="clientFormatter"
                            [resultFormatter]="clientFormatter" [title]="searchClientIpt?.value"
                            [resultTemplate]="clientSearchTemplate" type="search" name="clientGroupeMod"
                            class="form-control form-control-md text-dark pl-4 b-radius"
                            style="border-radius: 10px !important;" #searchClientIpt>

                        <ng-template #clientSearchTemplate let-result="result">
                            <div class="d-flex w-100 px-2 align-items-center k-gap-2">
                                <ngb-highlight class="col-4 p-0 m-0 text-wrap h5"
                                    [result]="result?.raisonSociale"></ngb-highlight>

                                <span
                                    class="col-4 badge badge-light m-0 p-1 d-flex justify-content-start align-items-center justify-content-start text-wrap">
                                    <i class="bi bi-person-circle mr-1"></i>
                                    <span class="h6">{{ result?.nomResponsable }}</span>
                                </span>

                                <span
                                    class="col-4 badge badge-dark m-0 p-1 d-flex justify-content-start align-items-center text-wrap">
                                    <i class="bi bi-geo-alt-fill mr-1"></i>
                                    <span class="h6">{{ result?.ville }}</span>
                                </span>
                            </div>
                        </ng-template>

                        <div class="picker-icons picker-icons-alt">
                            <i class="mdi mdi-magnify"></i>
                        </div>
                    </div>
                </div>

                <div class="col py-1 d-flex k-gap-2 align-items-end">
                    <button type="submit" (click)="appliquerModificationClientGroupeAdmin()" tabindex="-1"
                        class="actions-icons btn btn-success btn-sm pointer-cus" title="Appliquer les modifications">
                        <i class="bi bi-check-lg"></i>
                    </button>

                    <button (click)="annulerModificationClientGroupe()" type="button" tabindex="-1"
                        class="actions-icons btn btn-danger btn-sm pointer-cus" title="Annuler les modifications">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </form>

            <kendo-grid id="client-groupe-grid" [data]="{data: [selectedDemande], total: 1}"
                class="fs-grid fs-grid-white mb-4" [pageable]="false" [pageSize]="navigation.pageSize"
                [skip]="navigation.skip" (pageChange)="pageChange($event)" [sortable]="false" [sort]="sort"
                [resizable]="true">

                <ng-template kendoGridToolbarTemplate>
                    <div class="d-flex row p-0 my-0 mx-0 w-100 align-items-center k-gap-3 grid-top-radius">
                        <span class="h4 text-dark">{{'informations pharmacie du maroc' | uppercase }}</span>

                        <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                            <span *ngIf="!isEditing && selectedDemande?.etatDemandeAcces === 'E'"
                                (click)="isEditing = !isEditing" class="actions-icons text-dark pointer-cus"
                                title="Modifier Pharmacie du Maroc">
                                <i class="bi bi-pencil-square"></i>
                            </span>
                        </ng-container>
                    </div>
                </ng-template>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Raison Sociale</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.raisonSocialeGroupe ?? '---'}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Nom Responsable</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{ dataItem?.nomPharmacienGroupe ?? '---' }}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Ville</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{ dataItem?.villeGroupe ?? '---' }}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Localité</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{ dataItem?.localiteGroupe ?? '---' }}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-even" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Adresse</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{ dataItem?.clientGroupe?.adresse ?? '---' }}</span>
                    </ng-template>
                </kendo-grid-column>
            </kendo-grid>

            <form id="modRowLocal" (ngSubmit)="appliquerModificationClientLocalAdmin()"
                class="row d-flex flex-wrap justify-content-start w-100 mx-0 my-2" wphFocusTrap>
                <div class="col-lg-5 col-12">
                    <label for="raisonSocialeFourn" class="col-form-label text-dark">Fournisseur <span class="text-danger"
                            style="font-size: .75rem;">(Obligatoire*)</span></label>
                            
                    <div id="client-picker-input" class="input-group picker-input">
                        <input id="raisonSocialeFourn" [ngbTypeahead]="searchFournisseur"
                            [ngModelOptions]="{standalone: true}" [(ngModel)]="selectedDemande.fournisseur"
                            [inputFormatter]="fournisseurFormatter" [resultFormatter]="fournisseurFormatter"
                            [resultTemplate]="clientSearchTemplateFourn" type="text"
                            class="form-control form-control-md text-dark pl-4 b-radius"
                            style="border-radius: 10px !important;">

                        <ng-template #clientSearchTemplateFourn let-result="result">
                            <div>
                                <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                            </div>
                            <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                        </ng-template>

                        <div class="picker-icons picker-icons-alt">
                            <i class="mdi mdi-magnify"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-5 col-12">
                    <label for="clientLocalMod" class="col-form-label text-dark">Client local</label>

                    <div id="client-picker-input" class="input-group picker-input">
                        <input id="clientLocalMod" (focus)="searchClientLocalIpt.select()" [ngbTypeahead]="searchClientLocal"
                            [inputFormatter]="clientLocalFormatter" [(ngModel)]="selectedClientLocalToModify"
                            [resultFormatter]="clientLocalFormatter" [title]="searchClientLocalIpt?.value"
                            [readOnly]="!selectedDemande?.fournisseur" [resultTemplate]="clientSearchTemplateAlt"
                            type="search" name="clientGroupeMod" placeholder="Rechercher par raison sociale ou code local"
                            class="form-control form-control-md text-dark pl-4 b-radius"
                            style="border-radius: 10px !important;" #searchClientLocalIpt>

                        <ng-template #clientSearchTemplateAlt let-result="result">
                            <div class="d-flex w-100 px-2 align-items-center k-gap-2">
                                <ngb-highlight class="col p-0 m-0 text-wrap h5"
                                    [result]="result?.raisonSociale"></ngb-highlight>

                                <span
                                    class="col badge badge-light m-0 p-1 d-flex justify-content-start align-items-center justify-content-start text-wrap">
                                    <i class="bi bi-person-circle mr-1"></i>
                                    <span class="h6">{{ result?.nomPharmacien }}</span>
                                </span>

                                <span
                                    class="col badge badge-dark m-0 p-1 d-flex justify-content-start align-items-center text-wrap">
                                    <i class="bi bi-geo-alt-fill mr-1"></i>
                                    <span class="h6">{{ result?.ville }}</span>
                                </span>
                            </div>
                        </ng-template>

                        <div class="picker-icons picker-icons-alt">
                            <i class="mdi mdi-magnify"></i>
                        </div>
                    </div>
                </div>

                <div class="col py-1 d-flex k-gap-2 align-items-end">
                    <button type="submit" (click)="appliquerModificationClientLocalAdmin()" tabindex="-1"
                        class="actions-icons btn btn-success btn-sm pointer-cus" title="Appliquer les modifications">
                        <i class="bi bi-check-lg"></i>
                    </button>

                    <button (click)="annulerModificationClientLocal()" type="button" tabindex="-1"
                        class="actions-icons btn btn-danger btn-sm pointer-cus" title="Annuler les modifications">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </form>

            <kendo-grid id="client-local-grid" [data]="{data: [selectedDemande], total: 1}"
                class="fs-grid fs-grid-white" [pageable]="false" [pageSize]="navigation.pageSize"
                [skip]="navigation.skip" (pageChange)="pageChange($event)" [sortable]="false" [sort]="sort"
                [resizable]="true" style="overflow: visible">

                <ng-template kendoGridToolbarTemplate>
                    <div
                        class="d-flex row p-0 my-0 mx-0 w-100 align-items-center justify-content-between grid-top-radius">
                        <div class="col m-0 p-0 d-flex justify-content-start">
                            <span class="h4 text-dark">{{ 'informations Client Local' | uppercase }}</span>
                        </div>
                    </div>
                </ng-template>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Raison Sociale</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.raisonSocialeLocal ?? '---'}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Nom Responsable</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{ dataItem?.nomPharmacienLocal ?? '---' }}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Ville</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.villeLocal ?? '---'}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Localité</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.localiteLocal ?? '---'}}</span>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column headerClass="bg-odd" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap text-dark">Adresse</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                        <span class="text-wrap">{{dataItem?.adresseLocal ?? '---'}}</span>
                    </ng-template>
                </kendo-grid-column>
            </kendo-grid>

            <hr class="w-100 mt-4">

            <div class="row d-flex w-100 mx-0 my-2 justify-content-end">
                <div class="col-lg-6 col-12">
                    <label for="gsmPharmacieLocal" class="col-form-label text-dark">GSM <span class="text-danger"
                            style="font-size: .75rem;">(Obligatoire*)</span></label>
                    <div class="input-group">
                        <input id="gsmPharmacieLocal" [(ngModel)]="selectedDemande.gsm" [ngModelOptions]="{standalone: true}" type="text" name="gsm"
                            class="form-control form-control-md text-dark  b-radius">
                    </div>
                </div>

                <div class="col-lg-6 col-12">
                    <label for="emailPharmacieLocal" class="col-form-label text-dark">E-mail <span class="text-muted"
                            style="font-size: .75rem;">(Facultatif)</span></label>
                    <div class="input-group">
                        <input id="emailPharmacieLocal" [(ngModel)]="selectedDemande.email" [ngModelOptions]="{standalone: true}" type="text"
                            class="form-control form-control-md text-dark  b-radius">
                    </div>
                </div>
            </div>

        </div>

        <div class="modal-footer d-flex justify-content-between">
            <button type="button" (click)="modal.dismiss()" class="btn btn-light" tabindex="-1">Fermer</button>

            <div class="col p-0 m-0 k-gap-2 d-flex justify-content-end">
                <button (click)="enoyerDemandeAccesClientAdmin(modal)" type="button" class="btn btn-primary" tabindex="-1">Envoyer la demande</button>
            </div>
        </div>
    </form>
</ng-template>