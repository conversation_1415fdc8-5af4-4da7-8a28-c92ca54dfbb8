import { DetailActualitePage } from './detail-actualite/detail-actualite.page';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccueilPage } from './accueil/accueil.page';
import { ActualitesPage } from './actualites/actualites.page';
import { MobileAccueilComponentsModule } from '@wph/mobile/accueil/components';
import { MobileAccueilPagesRoutingModule } from './mobile-accueil-pages-routing.module';
import { MobileSharedModule } from '@wph/mobile/shared';
import { YouTubePlayerModule } from '@angular/youtube-player';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
  imports: [
    CommonModule,
    MobileAccueilComponentsModule,
    MobileAccueilPagesRoutingModule,
    MobileSharedModule,
    YouTubePlayerModule,
    ReactiveFormsModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [AccueilPage, ActualitesPage, DetailActualitePage],
})
export class MobileAccueilPagesModule {}
