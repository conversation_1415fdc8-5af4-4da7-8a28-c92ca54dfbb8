import { Noeud, SocieteType } from "@wph/shared";
import { GroupeEntreprise } from "./groupe-entreprise.model";
import { PharmacieEntreprise } from "./pharmacie-entreprise.model";
import { Pagination } from "@wph/data-access";

export class EntrepriseDTO {
  id?: number;
  code?: string;
  raisonSociale?: string;
  nomResponsable?: string;
  typeEntreprise?: string;
  adresse?: string;
  adresse2?: string;
  localite?: string;
  ville?: string;
  email?: string;
  gsm1?: string;
  telephone?: string;
  catalogue?: string;
  noeud?: Noeud;
  numIce?: number;
  segmentEntreprise?: string;
  isEnrolled?: boolean;

  constructor(item?: Partial<EntrepriseDTO>) {
    this.id = item?.id || null;
    this.code = item?.code || null;
    this.raisonSociale = item?.raisonSociale || null;
    this.nomResponsable = item?.nomResponsable || null;
    this.typeEntreprise = item?.typeEntreprise || null;
    this.adresse = item?.adresse || null;
    this.adresse2 = item?.adresse2 || null;
    this.localite = item?.localite || null;
    this.ville = item?.ville || null;
    this.email = item?.email || null;
    this.gsm1 = item?.gsm1 || null;
    this.telephone = item?.telephone || null;
    this.catalogue = item?.catalogue || null;
    this.noeud = item?.noeud || null;
    this.numIce = item?.numIce || null;
    this.segmentEntreprise = item?.segmentEntreprise || null;
    this.isEnrolled = (typeof item?.isEnrolled === 'boolean') ? item?.isEnrolled : null;
  }
}

export class EntrepriseCriteriaDto {
  id: number;
  code: string;
  raisonSociale: string;
  nomResponsable: string;
  typeEntreprise: SocieteType;
  typeEntreprises: SocieteType[];
  responsablesGroupe: PharmacieEntreprise[];
  fournisseurHasOffre: boolean;
  localite: string;
  ville: string;
  villes: string[];
  catalogue: string;
  noeud: string;
  segmentEntreprise: string;
  isEnrolled: boolean;
  groupeEntrepriseDTO: GroupeEntreprise;

  constructor(criteria?: Partial<EntrepriseCriteriaDto>) {
    Object.assign(this, criteria);
  }
}

export interface SearchEntrepriseDto extends Pagination {
  content: EntrepriseDTO[];
}

export class ClientSiteDto {
  clientGroupe?: EntrepriseDTO;
  codeClientSite?: string;
  codeSite?: number;
  codeLocal?: string // transient
  gsm?: string;
  localite?: string;
  nomPharmacien?: string;
  raisonSociale?: string;
  ville?: string;
}