//
// page-head.scss
//

.page-title-box {
  .page-title {
    font-size: 18px;
    margin: 0;
    line-height: 50px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: $page-title-color;
  }

  .page-title-right {
    float: right;
    margin-top: 20px;
  }

  .breadcrumb {
    padding-top: 8px;
  }
}

.page-title-box-sm {
  .page-title {
    line-height: 1 !important;
    margin-bottom: 25px;
  }

  .page-title-right {
    float: right;
    margin-top: 0;
  }

  .breadcrumb {
    padding-top: 0;
    margin-top: -3px !important;
  }
}


.text-title {
  color: $text-title-color;

  &:hover {
    color: $text-title-color;
  }
}

@include media-breakpoint-down(sm) {
  .page-title-box {
    .page-title {
      display: block;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      line-height: 70px;
    }

    .breadcrumb {
      display: none;
    }

    .page-title-right {
      display: none;
    }
  }
}

/*@media (max-width: 419px) {
  .page-title-box .breadcrumb {
    display: none;
  }
}*/

@media (min-width: 768px) and (max-width: 991px) {
  .page-title-box {
    padding-top: 12px;
  }

  .federation-syndicat-content{
    .page-title-box {
      padding-top: 0px;
    }
  }
}

@media screen and (min-width: 992px) {
  body[data-leftbar-compact-mode="fixed"] {
    .rowline {
      width: calc(100% - 260px) !important;
    }

    .inactive-banner {
      left: 260px !important;
      width: calc(100vw - 260px) !important;
    }
  }

  body[data-leftbar-compact-mode="condensed"] {
    .rowline {
      width: calc(100% - 70px) !important;
    }

    .inactive-banner {
      left: 70px !important;
      width: calc(100vw - 70px) !important;
    }
  }

  .rowline {
    position: fixed !important;
    z-index: 4;
    top: 70px;
  }

  .federation-syndicat-content .rowline{
    top: 0px !important;

  }

  .rowline+* {
    margin-top: 52px !important;
  }
}

@media screen and (max-width: 991px) {
  .rowline {
    position: relative !important;
    width: 100% !important;
  }

  .inactive-banner {
    left: 0 !important;
    width: 100vw !important;
  }
}

.rowline {

  &:before {
    content: "";
    top: 0;
    left: -260px;
    width: calc(120% + 260px);
    margin: 0 auto;
    height: 96%;
    position: absolute;
    background: white;
    z-index: -1;
    border-bottom: 1px solid #ddd !important;
  }

  .page-title-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 12px;

    .page-title-right {
      order: 5;
      margin-top: 0;

      .breadcrumb {
        padding: 0 !important;
      }
    }

  }
 
}
@media screen and (min-width: 1000px) {
  .federation-syndicat-content {
    .rowline{
      .page-title-box{
        padding-left: 30px !important;
      }
    }
   
  } 
}

@media screen and (max-width: 1000px) {
  .federation-syndicat-content {
    .rowline{
      .page-title-box{
        margin-left: 20px !important;
      }
    }
   
  } 
}