import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { WebSharedModule } from "@wph/web/shared";
import { GestionFournisseursRoutingModule } from "./gestion-fournisseurs-routing.module";
import { ListeFournisseursComponent } from "./liste/liste-fournisseurs.component";
import { CommonModule } from "@angular/common";
import { SharedModule } from "@wph/shared";
import { EditFournisseurComponent } from "./edit/edit-fournisseur.component";
import { NgbNavModule } from "@ng-bootstrap/ng-bootstrap";


@NgModule({
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    declarations: [ListeFournisseursComponent, EditFournisseurComponent],
    imports: [WebSharedModule, GestionFournisseursRoutingModule, CommonModule, SharedModule, NgbNavModule],
})
export class GestionFournisseursModule {}