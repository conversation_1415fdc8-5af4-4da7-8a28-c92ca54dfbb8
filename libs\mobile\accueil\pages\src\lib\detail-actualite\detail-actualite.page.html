<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="accueil/actualites"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ postItem?.title }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content parallax-header [scrollEvents]="true">
  <!-- Parallax Big Image -->
  <div class="header-image background-size" [ngStyle]="{'background-image': 'url(' + postItem.img + ')'}">
    <!-- <ion-item class="transparent" lines="none">
      <ion-button slot="end" class="ion-margin-top button-action-shit" (click)="presentActionSheet(data, $event)">
        <ion-icon class="icon-font-md icon-color-light" slot="icon-only" [name]="data.shareIcon"></ion-icon>
      </ion-button>
    </ion-item> -->
  </div>
  <!--Content-->
  <ion-grid class="ion-no-padding" *ngIf="postItem !== null">
    <ion-row>
      <ion-col class="ion-no-padding background-primary" size="12">
        <ion-item class="transparent" lines="none">
          <ion-label>
            <span class="ion-margin-bottom text-size-xxs text-color-accent font-bold ion-text-uppercase">{{postItem?.time}}</span>
            <h1 class="ion-text-wrap text-size-xl text-color-primary font-bold">{{postItem?.title}}</h1>
          </ion-label>
          <ion-label slot="end" class="ion-align-self-end">
            <ion-badge>{{postItem?.time}}</ion-badge>
          </ion-label>
        </ion-item>
        <ion-item class="transparent">
          <h2 class="ion-no-margin ion-padding-bottom text-size-xs text-color-secondary font-regular">{{postItem?.desc}}</h2>
        </ion-item>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
