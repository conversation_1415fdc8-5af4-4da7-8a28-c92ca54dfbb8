{"extends": "../../tsconfig.base.json", "files": [], "include": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}, {"path": "./tsconfig.editor.json"}], "compilerOptions": {"target": "es2015", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}