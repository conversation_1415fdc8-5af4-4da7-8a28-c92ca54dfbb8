{"name": "wph-mobile-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/wph-mobile-e2e/src", "projectType": "application", "targets": {"e2e": {"executor": "@nrwl/cypress:cypress", "options": {"cypressConfig": "apps/wph-mobile-e2e/cypress.config.ts", "devServerTarget": "wph-mobile:serve:development", "testingType": "e2e"}, "configurations": {"production": {"devServerTarget": "wph-mobile:serve:production"}}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/wph-mobile-e2e/**/*.{js,ts}"]}}}, "tags": [], "implicitDependencies": ["wph-mobile"]}