import { Fournisseur } from "./fournisseur.model";
import { Pagination } from "./PaginationDTO.ts";

export type EtatDemandeAcces = 'A' | 'R' | 'E';
export class DemandeAccesClient {
    id?: number;
    fournisseur?: Fournisseur;
    clientGroupe?: Fournisseur;
    codeSite?: number;
    dateCreation?: string;
    dateTraitement?: string;
    codeClientGroupe?: string;
    raisonSocialeGroupe?: string;
    nomPharmacienGroupe?: string;
    adresseGroupe?: string;
    adresseLocal?: string;
    villeGroupe?: string;
    codeClientLocal?: string;
    raisonSocialeLocal?: string;
    nomPharmacienLocal?: string;
    localiteGroupe?: string;
    localiteLocal?: string;
    villeLocal?: string;
    gsm?: string;
    email?: string;
    motifRefus?: string;
    classification?: string;
    etatDemandeAcces?: EtatDemandeAcces;

    constructor(partialDemandeAccesClient?: Partial<DemandeAccesClient>) {
        this.id = partialDemandeAccesClient?.id || null;
        this.fournisseur = partialDemandeAccesClient?.fournisseur || null;
        this.clientGroupe = partialDemandeAccesClient?.clientGroupe || null;
        this.codeSite = partialDemandeAccesClient?.codeSite || null;
        this.dateCreation = partialDemandeAccesClient?.dateCreation || null;
        this.dateTraitement = partialDemandeAccesClient?.dateTraitement || null;
        this.codeClientGroupe = partialDemandeAccesClient?.codeClientGroupe || null;
        this.codeClientLocal = partialDemandeAccesClient?.codeClientLocal || null;
        this.raisonSocialeGroupe = partialDemandeAccesClient?.raisonSocialeGroupe || null;
        this.raisonSocialeLocal = partialDemandeAccesClient?.raisonSocialeLocal || null;
        this.nomPharmacienGroupe = partialDemandeAccesClient?.nomPharmacienGroupe || null;
        this.nomPharmacienLocal = partialDemandeAccesClient?.nomPharmacienLocal || null;
        this.villeGroupe = partialDemandeAccesClient?.villeGroupe || null;
        this.villeLocal = partialDemandeAccesClient?.villeLocal || null;
        this.localiteGroupe = partialDemandeAccesClient?.localiteGroupe || null;
        this.localiteLocal = partialDemandeAccesClient?.localiteLocal || null;
        this.adresseGroupe = partialDemandeAccesClient?.adresseGroupe || null;
        this.adresseLocal = partialDemandeAccesClient?.adresseLocal || null;
        this.gsm = partialDemandeAccesClient?.gsm || null;
        this.email = partialDemandeAccesClient?.email || null;
        this.motifRefus = partialDemandeAccesClient?.motifRefus || null;
        this.classification = partialDemandeAccesClient?.classification || null;
        this.etatDemandeAcces = partialDemandeAccesClient?.etatDemandeAcces || null;
    }
}


export class DemandeAccesClientCriteria {
    id?: number;
    clientGroupeId?: number;
    codeClientGroupe?: string;
    codeClientLocal?: string;
    codeSite?: number;
    dateCreationFrom?: string;
    dateCreationTo?: string;
    dateTraitementFrom?: string;
    dateTraitementTo?: string;
    fournisseurId?: number;
    classification?: string;
    etatsDemande?: EtatDemandeAcces[];

    constructor(criteria?: Partial<DemandeAccesClientCriteria>) {
        this.id = criteria?.id || null;
        this.clientGroupeId = criteria?.clientGroupeId || null;
        this.codeClientGroupe = criteria?.codeClientGroupe || null;
        this.codeClientLocal = criteria?.codeClientLocal || null;
        this.codeSite = criteria?.codeSite || null;
        this.dateCreationFrom = criteria?.dateCreationFrom || null;
        this.dateCreationTo = criteria?.dateCreationTo || null;
        this.dateTraitementFrom = criteria?.dateTraitementFrom || null;
        this.dateTraitementTo = criteria?.dateTraitementTo || null;
        this.etatsDemande = criteria?.etatsDemande || null;
        this.fournisseurId = criteria?.fournisseurId || null;
        this.classification = criteria?.classification || null;
    }
}

export interface SearchDemandeAccesClient extends Pagination {
    content?: DemandeAccesClient[];
}

export class TacAssociation {
    id?: string;
    match?: number; // ? 1 / 0
    score?: number; // ? percentage 0% -> 100%
}

export class TacClientAssociationCriteria {
    code_client?: string;
    code_site?: number;
    nom_pharmacien?: string;
    raison_sociale?: string;
    ville?: string;

    constructor(criteria?: Partial<TacClientAssociationCriteria>) {
        this.code_client = criteria?.code_client || null;
        this.code_site = criteria?.code_site || null;
        this.nom_pharmacien = criteria?.nom_pharmacien || null;
        this.raison_sociale = criteria?.raison_sociale || null;
        this.ville = criteria?.ville || null;
    }
}

export class TacClientDto extends TacClientAssociationCriteria {
    constructor(criteria?: Partial<TacClientAssociationCriteria>) {
        super(criteria);
    }
}

export class TacClientAssociationMatches {
    adresse1?: string;
    adresse2?: string;
    code_client_groupe?: string;
    ice?: string;
    localite?: string;
    nom_pharmacien?: string;
    nom_pharmacien_normalized?: string;
    patente?: string;
    raison_sociale?: string;
    raison_sociale_normalized?: string;
    tag?: string;
    telephone?: string;
    type?: string;
    ville?: string;

    constructor(matches?: Partial<TacClientAssociationMatches>) {
        this.adresse1 = matches?.adresse1 || null;
        this.adresse2 = matches?.adresse2 || null;
        this.code_client_groupe = matches?.code_client_groupe || null;
        this.ice = matches?.ice || null;
        this.localite = matches?.localite || null;
        this.nom_pharmacien = matches?.nom_pharmacien || null;
        this.nom_pharmacien_normalized = matches?.nom_pharmacien_normalized || null;
        this.patente = matches?.patente || null;
        this.raison_sociale = matches?.raison_sociale || null;
        this.raison_sociale_normalized = matches?.raison_sociale_normalized || null;
        this.tag = matches?.tag || null;
        this.telephone = matches?.telephone || null;
        this.type = matches?.type || null;
        this.ville = matches?.ville || null;
    }
}

export class TacClientAssociationResponse {
    type?: string;
    client?: TacClientDto;
    matches?: TacClientAssociationMatches[];
}
