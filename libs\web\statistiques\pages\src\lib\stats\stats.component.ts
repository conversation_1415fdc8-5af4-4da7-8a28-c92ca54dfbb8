import { Component, OnInit, ViewEncapsulation } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { FilterOutput, Fournisseur, OffresService, StatsSearch } from "@wph/data-access";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SortDescriptor, orderBy } from "@progress/kendo-data-query";
import { GridDataResult, SelectionEvent } from "@progress/kendo-angular-grid";
import { City } from "@wph/shared";
import { InscriptionService } from "@wph/mobile/shared";
import { ProduitDto, StatsData } from "libs/data-access/src/lib/models/stats.model";
import { StatConfig } from "libs/web/shared/src/lib/components/stat/stat.component";

@Component({
  selector: "wph-stats",
  templateUrl: "./stats.component.html",
  styleUrls: ["./stats.component.scss"],
  encapsulation: ViewEncapsulation.Emulated
})
export class StatsComponent implements OnInit {

  searchFormGroup: FormGroup;
  currentTab: number = 1;
  listeFournisseurs: Fournisseur[] | null = null;
  listeVilles: City[] | null = null;

  stats: StatConfig[];
  statsData: StatsData;

  produitSort: SortDescriptor[];
  packSort: SortDescriptor[];
  offreSort: SortDescriptor[];

  hasAgentPointVenteRole: boolean;

  selectedOffreId: number | null = null;
  selectedRowIndex: number[] | null = null;
  offrePackFirstLoad: boolean = true;

  initialProductList: ProduitDto[];

  navigation = {
    skip: 0,
    pageSize: 20,
  };

  produitGridData: GridDataResult = {
    data: [],
    total: 0
  }
  packGridData: GridDataResult = {
    data: [],
    total: 0
  }
  offreGridData: GridDataResult = {
    data: [],
    total: 0
  }

  constructor(
    private formBuilder: FormBuilder,
    private offresService: OffresService,
    private modalService: NgbModal,
    private inscriptionService: InscriptionService
  ) {}
  
  ngOnInit(): void {
    this.initForm();
    this.checkCurrentUserRole();
    this.setInitialStatsData();
    
    this.getData();
  }

  checkCurrentUserRole() {
    const principal = JSON.parse(localStorage.getItem('principal'));
    this.hasAgentPointVenteRole = principal?.authorities.includes('ROLE_AGENT_POINT_VENTE');
  }


  getFournisseur() {
    this.offresService.getListeFournisseur().subscribe(
      (data) => {
        this.listeFournisseurs = data;
      }
    );
  }

  getListeVilles() {
    this.inscriptionService.getCities().subscribe((villes: City[]) => {
      this.listeVilles = villes;
    });
  }

  initForm() {
    this.searchFormGroup = this.formBuilder.group<StatsSearch | any>({
      codeClient: [null],
      commercial: [null], // Added
      commercialId: [null],
      dateAcceptationDebut: [null],
      dateAcceptationFin: [null],
      distributeur: [null], // Added
      distributeurId: [null],
      localite: [null],
      client: [null],
      nomPharmacie: [null],
      numeroCommande: [null],
      offre: [null], // Added
      offreId: [null],
      offreur: [null], // Added
      offreurId: [null],
      raisonSociale: [null],
      statut: [null],
      ville: [null]
    });
  }

  getData() {
    this.modalService.dismissAll();

    this.selectedOffreId = null;
    this.selectedRowIndex = null;

    const { offreur, distributeur, offre, ville, client, ...formValues } = (this.searchFormGroup.value as StatsSearch);

    !offre && (formValues['offreId'] = null);

    offreur && (formValues['offreurId'] = offreur?.id);
    distributeur && (formValues['distributeurId'] = distributeur?.id);
    ville && (formValues['ville'] = (ville as City)?.labelFr);
    client && (formValues['codeClient'] = client?.code);

    this.offresService.getStats(formValues).subscribe(data => {
      this.statsData = data;
      this.initialProductList = data?.produitDtos;

      this.stats[0].value = data?.nbrCommande;
      this.stats[1].value = data?.qteVedues;
      this.stats[2].value = data?.qteUG;
      this.stats[3].value = data?.caBrut;
      this.stats[4].value = data?.caNet;
      this.stats[5].value = data?.mtRemise;

      this.setGridData('offre');
      this.setGridData('produit');
      (data?.offreDtos?.length === 1) && this.setGridData('pack');
    });
  }

  setGridData(gridName: string) {
    if (gridName === 'offre') {
      this.offreGridData = {
        data: this.statsData?.offreDtos,
        total: this.statsData?.offreDtos?.length
      }
    } else if (gridName === 'pack') {
      this.packGridData = {
        data: this.statsData?.packOffres,
        total: this.statsData?.packOffres?.length
      }
    } else {
      this.produitGridData = {
        data: this.statsData?.produitDtos,
        total: this.statsData?.produitDtos?.length
      }
    }
  }

  vider() {
    this.searchFormGroup.reset(), this.packGridData = null, this.getData();
  }

  openFilterModal(modal: any, size: string) {
    if (!this.listeFournisseurs || !this.listeVilles) {
      // this.getFournisseur();
      // this.getListeVilles();
    }

    this.modalService
      .open(modal, { ariaLabelledBy: 'modal-basic-title', size, backdrop: 'static', centered: true, modalDialogClass: 'fs-radius-modal' })
      .result.then(
        result => {
          console.log(result);
        },
        reason => {
          console.log('Err!', reason);
        }
      );
  }

  sortChange(sort: SortDescriptor[], GridSorted: any): void {
    if (GridSorted === 'produit') {
      this.produitSort = sort;
      this.statsData.produitDtos = orderBy(this.statsData.produitDtos, this.produitSort);

    } else if (GridSorted === 'pack') {
      this.packSort = sort;
      this.statsData.packOffres = orderBy(this.statsData.packOffres, this.packSort);

    } else {
      this.offreSort = sort;
      this.statsData.offreDtos = orderBy(this.statsData.offreDtos, this.offreSort);
    }

    this.setGridData(GridSorted)
  }

  chooseOffer(event: SelectionEvent) {
    if (event.selectedRows?.length > 0) {
      this.selectedRowIndex = [event.selectedRows[0].index];
      this.statsData?.offreDtos?.length > 1 && (this.offrePackFirstLoad = true);

      this.selectedOffreId = event.selectedRows[0].dataItem?.offreDto?.id;

      this.searchFormGroup.get('offreId').setValue(this.selectedOffreId);
    } else if (event.deselectedRows?.length > 0) {
      this.selectedOffreId = null;
      this.selectedRowIndex = null;

      this.statsData['packOffres'] = [];
      this.statsData['produitDtos'] = [...this.initialProductList];

      this.setGridData('pack');
      this.setGridData('produit');
    }
  }

  activeTabChange(tabIndex: number) {
    if ((tabIndex > 1) && this.offrePackFirstLoad &&
      (this.statsData?.offreDtos?.length === 1 || this.selectedOffreId)
    ) {
      this.offresService.getStats({ offreId: this.selectedOffreId ?? this.statsData?.offreDtos[0]?.offreDto.id })
        .subscribe(res => {
          this.statsData['packOffres'] = res.packOffres;
          this.statsData['produitDtos'] = res.produitDtos;

          this.setGridData('pack');
          this.setGridData('produit');
          this.offrePackFirstLoad = false;
        })
    }
  }

  setInitialStatsData() {
    this.stats = [
      {
        title: "Nbr CMD",
        value: 0,
        icon: "open_book",
        background: 'var(--wo-primary-500)'
      },
      {
        title: this.hasAgentPointVenteRole ? 'Qte Achetées' : 'Qte Vendues',
        value: 0,
        icon: "open_book",
        background: 'var(--wo-primary-500)'
      },
      {
        title: "Qte UG",
        value: 0,
        icon: "open_book",
        background: 'var(--wo-primary-500)'
      },
      {
        title: "Ca Brut",
        value: 0,
        icon: "open_book",
        isFloat: true,
        background: '#0d788b'
      },
      {
        title: "CA Net",
        value: 0,
        icon: "open_book",
        isFloat: true,
        background: '#0d788b'
      },
      {
        title: "Mt Remise",
        value: 0,
        icon: "open_book",
        isFloat: true,
        background: '#0d788b'
      }
    ];
  }

  filterModalAction(filterAction: FilterOutput): void {
    filterAction?.clear && this.vider();
    filterAction?.filter && this.getData();
  }

}
