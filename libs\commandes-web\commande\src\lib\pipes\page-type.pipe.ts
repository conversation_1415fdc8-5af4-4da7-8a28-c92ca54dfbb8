import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
    pure: true,
    name: 'pagetype'
})
export class PageTypePipe implements PipeTransform {

    transform(value: string): string {
        switch(value) {
            case 'new': 
                return 'Nouvelle Commande';
            case 'panier':
                return 'Panier';
            default:
                return 'Modifier Commande';    
        }
    }
   
}