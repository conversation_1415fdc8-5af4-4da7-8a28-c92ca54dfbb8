.b-radius {
    border-radius: 10px;
}

.input-border {
    border-color: rgb(122, 122, 122);
}

.card-header {
    border-radius: 10px;
}

.bg-even {
    background: #ffeac4 !important;
}

.bg-odd {
    background: #f0a10f84 !important;
}

.search-btn {
    font-size: 1rem;
    border-radius: 8px !important;

    color: var(--win-offre-primary) !important;
    border-color: var(--win-offre-primary) !important;
}

.search-btn:hover,
.search-btn:focus,
.search-btn:active {
  background-color: var(--win-offre-primary) !important;
  border-color: var(--win-offre-primary) !important;
  color: #fff !important;
}


.search-btn-green {
    font-size: 1rem;
    border-radius: 8px !important;

    color: #56a65a !important;
    border-color: #56a65a !important;

    .sparkle-icon {
        background-size: 100% 100%;
        background-repeat: no-repeat;
        outline: none;
        width: 1.1rem;
        height: 1.1rem;
        background-image: var(--magic-wand-icon-green);
    }

    transition: all 2s ease-in-out !important;
}

.search-btn-green:hover,
.search-btn-green:focus,
.search-btn-green:active {
    background-color: transparent !important;
  background: linear-gradient(90deg, #EC9F15, #4b934f) !important;
  border-color: #56a65a !important;
  i {
      color: #fff !important;
  }

  .sparkle-icon {
        background-image: var(--magic-wand-icon-white) !important;
    }
  
  .gradient-text {
      background: linear-gradient(90deg, #fef7e9, #eefff0);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
  }
}

::ng-deep #DEFAULT-container {
    #client-groupe-grid .k-grid-toolbar {
        background-color: #facc77 !important;
        overflow: visible !important;
    }

    #client-local-grid .k-grid-toolbar {
        background-color: #9dc39f !important;
        overflow: visible !important;
        border-top-left-radius: 10px !important;
        border-top-right-radius: 10px !important;
    }
}

@media (max-width: 768px) {
    #recherche-client-local-grid {
        min-height: 280px;
    }
}

@media (min-width: 769px) {
    #recherche-client-local-grid {
        min-height: 410px;
    }
}

.gradient-text {
    font-weight: 700;
    color: #56a65a !important;
}