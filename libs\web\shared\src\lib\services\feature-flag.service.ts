import { Injectable } from "@angular/core";
import { BaseFeatureFlagMap, FEATURE_KEY_STORAGE, FeatureFlagMap, FeatureFlagMapValue } from "@wph/data-access";

@Injectable({
    providedIn: 'root'
})
export class FeatureFlagService {
    private featureFlags: FeatureFlagMap;
    private enabledFeatureFlagKeys: string[];

    constructor() { }

    public loadFeatureFlags(featureFlags: BaseFeatureFlagMap[]): void {
        const formattedFeatureFlags = this.formatFeatureFlags(featureFlags);

        this.setFeatureFlags(formattedFeatureFlags);
    }

    private formatFeatureFlags(featureFlags: BaseFeatureFlagMap[]): FeatureFlagMap {
        let formattedFeatureFlags: FeatureFlagMap = {};

        for (const paramDto of featureFlags) {
            Object.entries(paramDto).forEach(paramItem => {
                const [key, value] = paramItem;
                formattedFeatureFlags[key] = { enabled: value };
            });
        }

        return formattedFeatureFlags;
    }

    public setFeatureFlags(featureFlags: FeatureFlagMap, storageKey?: FEATURE_KEY_STORAGE): void {
        localStorage.setItem(storageKey || FEATURE_KEY_STORAGE.ROOT, JSON.stringify(featureFlags));

        this.featureFlags = featureFlags;
        this.enabledFeatureFlagKeys = Object.keys(featureFlags).filter(flag => (featureFlags[flag] as FeatureFlagMapValue).enabled);
    }

    public isFeatureEnabled(featureName: string, storageKeys?: FEATURE_KEY_STORAGE[]): boolean {
        if (storageKeys && storageKeys.length > 0) {
            return storageKeys.every(storageKey => {
                const featureFlag = this.getFeatureFlag(featureName, storageKey);
                return featureFlag ? featureFlag.enabled : true; //? Default to true if feature flag is not found
            });
        }

        const featureFlag = this.getFeatureFlag(featureName);
        return featureFlag ? featureFlag.enabled : true; //? Default to true if feature flag is not found
    }

    public getFeatureFlagMap(storageKey?: FEATURE_KEY_STORAGE): FeatureFlagMap {
        if (!this.featureFlags) {
            const storedFeatureFlags = localStorage.getItem(storageKey || FEATURE_KEY_STORAGE.ROOT);
            if (storedFeatureFlags) {
                this.featureFlags = JSON.parse(storedFeatureFlags);
            } else {
                this.featureFlags = {};
            }
        }

        return this.featureFlags;
    }

    public toGridList(): { data: any[], total: number } {
        if (!this.featureFlags) {
            this.getFeatureFlagMap();
        }

        const data = Object.entries(this.featureFlags).map(([key, value]) => {
            return { libelle: key, ...value };
        });

        return { data, total: data?.length };
    }

    public getFeatureFlag(featureName: string, storageKey?: FEATURE_KEY_STORAGE): FeatureFlagMapValue | undefined {
        if (!this.featureFlags) {
            const storedFeatureFlags = localStorage.getItem(storageKey || FEATURE_KEY_STORAGE.ROOT);
            if (storedFeatureFlags) {
                this.featureFlags = JSON.parse(storedFeatureFlags);
            }
        }

        return this.featureFlags[featureName] as FeatureFlagMapValue | undefined;
    }

    public availableFeatureFlags(): string[] {
        return this.enabledFeatureFlagKeys;
    }
}
