<ng-container *ngIf="offre">
  <div class="row mx-1 mb-2" #offreHeader>
    <div class="container-fluid"
      [style.margin-bottom]="(offre?.nombreProduitsProposes === 1) ? (fixedPackSyntheseHeight ? (fixedPackSyntheseHeight + 'px') : '120px') : '0px'">
      <div id="offre-header-container" class="card border-0 shadow-md bg-transparent w-100">
        <ul ngbNav #blocOffreHeaderNav="ngbNav" class="nav-tabs mb-0">
          <li [ngbNavItem]="1">
            <a ngbNavLink class="d-flex justify-content-center" [ngClass]="{
            'pack-error': (offre?.etatCmdOffre === 'I') || 
              ((offre?.nombreProduitsProposes === 1) && offre?.totalQteCmd > 0 && monoProduitBlocOffre?.etat === 'I'),
            'pack-success': ((offre?.nombreProduitsProposes === 1) && offre?.totalQteCmd > 0 && monoProduitBlocOffre?.etat !== 'I') 
            }">
              <div class="col-auto d-flex align-items-center">
                <i class="mdi mdi-package-variant-closed mdi-18px mr-1"></i>
                <b *ngIf="offre?.nombreProduitsProposes > 1">{{'Informations Générales' | uppercase }}</b>
                <b *ngIf="offre?.nombreProduitsProposes === 1">{{ offre?.titre| uppercase }}</b>
              </div>

              <div *ngIf="offre.etatCmdOffre === 'I'" class="col-auto d-flex align-items-center">
                <button [id]="'popover-container-' + offre?.id" type="button" (click)="fireEvent($event)" popoverClass="increase-popover-width "
                  placement="left" [ngbPopover]="offre?.messageEtatCmdOffre ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover">
                  <i class="bi bi-patch-exclamation-fill"
                    style="font-size: 20px; line-height: 1; vertical-align: middle;"></i>
                </button>

                <ng-template #cstmPopoverTemplate>
                  <wph-popover-template [id]="'popover-' + offre?.id" [popoverRef]="ngbPopover" [popoverContent]="offre?.messageEtatCmdOffre"></wph-popover-template>
                </ng-template>
              </div>

              <div *ngIf="(offre?.nombreProduitsProposes === 1) && monoProduitBlocOffre?.etat === 'I'"
                class="col-auto d-flex align-items-center">
                <button type="button" (click)="fireEvent($event)" popoverClass="increase-popover-width "
                  placement="left" [ngbPopover]="monoProduitBlocOffre?.messageEtat ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover">
                  <i class="bi bi-patch-exclamation-fill"
                    style="font-size: 20px; line-height: 1; vertical-align: middle;"></i>
                </button>

                <ng-template #cstmPopoverTemplate>
                  <wph-popover-template [popoverRef]="ngbPopover" [popoverContent]="monoProduitBlocOffre?.messageEtat"></wph-popover-template>
                </ng-template>
              </div>
            </a>

            <ng-template ngbNavContent>
              <div class="card-body p-1 w-100 mt-0 bg-white card-btm-radius" #firstCard>
                <div class="row">
                  <div class="col-xl-4 col-12 d-flex justify-content-start">
                    <div class="row w-100 mx-0 px-0 d-flex justify-content-center">
                      <div class="col-auto mx-auto">
                        <img
                          (click)="offreImageUrl && openLargImageModal(largeImgModal, getBlocImage(offre?.docImageOffre?.idhash))"
                          class="img-fluid-header rounded-2 pointer-cus"
                          [src]="offreImageUrl || '/assets/images/default-img.png'" alt="offreImage">

                        <ng-container *ngIf="!readOnly">
                          <wph-countdown-timer [offre]="offre" [separator]="false" mode="D" gap="5px"
                            class="row mx-0 my-2 px-2 w-100 d-flex justify-content-between align-items-center"></wph-countdown-timer>
                        </ng-container>
                      </div>

                      <ng-container *ngIf="offre?.nombreProduitsProposes === 1"
                        [ngTemplateOutlet]="badgeBlocConditionsTemplate"
                        [ngTemplateOutletContext]="{bloc: monoProduitBlocOffre}"></ng-container>
                    </div>
                  </div>

                  <div class="col py-1">
                    <div class="row row-gap-2 rows-divider m-0" wphIntersectionObserver
                      [options]="{ rootMargin: '0px', threshold: 0.4 }"
                      (isIntersecting)="isIntersecting($event, rowsDivider)" #rowsDivider>

                      <ng-container *ngIf="offre?.nombreProduitsProposes === 1" [ngTemplateOutlet]="monoProduitTemplate"
                        [ngTemplateOutletContext]="{produit: monoProduitBlocOffre}"></ng-container>

                      <ng-container *ngIf="offre?.nombreProduitsProposes > 1"
                        [ngTemplateOutlet]="multiProduitTemplate"></ng-container>

                      <ng-template #multiProduitTemplate>
                        <div class="col-md-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{"Titre de l'offre" | uppercase}}</h5>
                          <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ offre?.titre | uppercase}}</b></p>
                        </div>

                        <div class="col-md-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{'Offert par' | uppercase}}</h5>
                          <p class="mb-0 fs-5 h4 fw-semibold" [ngClass]="{
                            'labo-label': offre?.offreur?.typeEntreprise === 'FABRIQUANT',
                            'grossiste-label':offre?.offreur?.typeEntreprise === 'GROSSISTE'
                          }">
                            <b>{{ offre?.offreur?.raisonSociale | uppercase }}</b>
                          </p>
                        </div>

                        <div class="col-md-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{'Date Début' | uppercase}}</h5>
                          <p class="mb-0 fs-5 h4 text-dark fw-semibold "><b>{{ $any(offre.dateDebut) | date:'dd/MM/yyyy'
                              }}</b></p>
                        </div>

                        <div *ngIf="readOnly" class="col-md-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{'Date Fin' | uppercase}}</h5>
                          <p class="mb-0 fs-5 h4 text-dark fw-semibold "><b>{{ $any(offre.dateFin) | date:'dd/MM/yyyy'
                              }}</b>
                          </p>
                        </div>

                        <div class="col-md-6 col-12" *ngIf="offre?.offreur?.typeEntreprise !== 'FABRIQUANT'">
                          <h5 class="mb-0 fs-5 text-sub">{{'Laboratoire' | uppercase}}</h5>
                          <p class="mb-0 fs-5 h4 fw-semibold" [ngClass]="{
                            'labo-label': offre?.laboratoire?.typeEntreprise === 'FABRIQUANT',
                            'grossiste-label':offre?.laboratoire?.typeEntreprise === 'GROSSISTE'
                          }">
                            <b>{{ offre.laboratoire?.raisonSociale | uppercase }}</b>
                          </p>
                        </div>

                        <div *ngIf="!readOnly" class="col-md-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{'Date Fin' | uppercase}}</h5>
                          <p class="mb-0 fs-5 h4 text-dark fw-semibold "><b>{{ $any(offre.dateFin) | date:'dd/MM/yyyy'
                              }}</b>
                          </p>
                        </div>

                        <ng-container *ngIf="readOnly">
                          <div class="col-md-6 col-12">
                            <h5 class="mb-0 fs-5 text-sub">{{'Fournisseur' | uppercase}}</h5>
                            <p class="mb-0 fs-5 h4 fw-semibold" [ngClass]="{
                              'labo-label': offre?.distributeur?.typeEntreprise === 'FABRIQUANT',
                              'grossiste-label':offre?.distributeur?.typeEntreprise === 'GROSSISTE'
                            }">
                              <b> {{ offre.distributeur?.raisonSociale | uppercase }} </b>
                            </p>
                          </div>

                          <ng-container
                            *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL', 'ROLE_SUPER_ADMIN']">
                            <div class="col-md-6 col-12">
                              <h5 class="mb-0 fs-5 text-sub">{{'Client' | uppercase}}</h5>
                              <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                                <b>{{ offre?.client?.raisonSociale ?? 'Non spécifié' | uppercase }}</b>
                              </p>
                            </div>

                            <div class="col-md-6 col-12" *ngIf="
                              (offre?.societeCreateurCmd?.typeEntreprise === 'GROSSISTE') || 
                              (offre?.societeCreateurCmd?.typeEntreprise === 'FABRIQUANT') ||
                              (offre?.societeCreateurCmd?.id !== currentUser?.societe?.id)
                            ">
                              <h5 class="mb-0 fs-5 text-sub">{{'Saisie Par' | uppercase}}</h5>
                              <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                                <b>
                                  {{
                                  offre?.userCreateurCommande?.id + ' / ' +
                                  (offre?.societeCreateurCmd?.raisonSociale || '')
                                  }}
                                </b>
                              </p>
                            </div>

                            <div class="col-md-6 col-12"
                              *ngIf="offre?.commandStatut === 'ANNULER' && offre?.annuleePar">
                              <h5 class="mb-0 fs-5 text-sub">{{'Annulée par' | uppercase}}</h5>
                              <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                                <b>{{ offre?.annuleePar?.raisonSociale }}</b>
                              </p>
                            </div>

                            <ng-container
                              *ngIf="((offre?.commandStatut === 'NOUVELLE') || (offre?.commandStatut === 'BROUILLON')) && currentUser?.societe?.typeEntreprise !== 'FABRIQUANT'">
                              <div id="client-picker-input" class="col-md-6 col-12"
                                *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']">
                                <label class="form-label d-flex row align-items-center m-0">
                                  <b class="p-0 mb-0 h6 text-sub"> {{'Code client chez le grossiste' | uppercase}} </b>
                                  <span *ngIf="showCodeLocalWarning" class="pointer-cus p-0 ml-1"
                                    style="margin-top: -3px;" popoverClass="increase-popover-width-warning"
                                    placement="top" container="body" [ngbPopover]="popoverContent">
                                    <i class="mdi mdi-alert-octagon mdi-24px text-warning"></i>
                                  </span>
                                </label>

                                <div class="w-100 d-flex row mx-0 px-0 my-1">
                                  <div class="input-group picker-input search-input-picker">
                                    <input type="text" class="custom-search-input form-control form-control-md"
                                      [editable]="true" [(ngModel)]="selectedClientLocal"
                                      [readOnly]="isClientLocalReadOnly || readOnly"
                                      (ngModelChange)="offre.clientLocal = selectedClientLocal"
                                      [ngbTypeahead]="searchClientLocal" [resultTemplate]="clientLocalResTemplate"
                                      [inputFormatter]="clientLocalFormatter" [resultFormatter]="clientLocalFormatter">

                                    <ng-template #clientLocalResTemplate let-result="result">
                                      <div>
                                        <span class="badge badge-info mr-2">{{result?.code}}</span>

                                        <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                                      </div>

                                      <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                                    </ng-template>


                                    <div class="picker-icons"><i class="mdi mdi-magnify pointer"></i></div>
                                  </div>

                                </div>
                              </div>

                            </ng-container>

                          </ng-container>

                          <div class="col-md-6 col-12" *ngIf="offre?.transporteurCommande">
                            <h5 class="mb-0 fs-5 text-sub">{{'Transporteur' | uppercase}}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ offre?.transporteurCommande
                                }}</b>
                            </p>
                          </div>

                          <div class="col-md-6 col-12" *ngIf="offre?.delaiPaiement">
                            <h5 class="mb-0 fs-5 text-sub">{{'Mode de paiement' | uppercase}}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ offre?.delaiPaiement?.label
                                }}</b>
                            </p>
                          </div>

                          <div class="col-md-6 col-12"
                            *ngIf="offre?.dateConfirmation && offre?.commandStatut === 'NOUVELLE'">
                            <h5 class="mb-0 fs-5 text-sub">{{'Date de Confirmation' | uppercase}}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ offre?.dateConfirmation |
                                date:'dd/MM/yyyy'
                                }}</b>
                            </p>
                          </div>

                          <div class="col-md-6 col-12"
                            *ngIf="offre?.dateAcceptation && !offre?.dateAnnulationCmd && offre?.commandStatut === 'ACCEPTER'">
                            <h5 class="mb-0 fs-5 text-sub">{{"Date d'acceptation" | uppercase}}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ $any(offre?.dateAcceptation) |
                                date:'dd/MM/yyyy' }}</b>
                            </p>
                          </div>

                          <div class="col-md-6 col-12" *ngIf="offre?.dateAnnulationCmd">
                            <h5 class="mb-0 fs-5 text-sub">{{"Date d'annulation" | uppercase}}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ $any(offre?.dateAnnulationCmd) |
                                date:'dd/MM/yyyy' }}</b>
                            </p>
                          </div>

                          <div class="col-md-6 col-12" *ngIf="offre?.dateSuppressionCmd">
                            <h5 class="mb-0 fs-5 text-sub">{{'Date Suppression' | uppercase}}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ $any(offre?.dateSuppressionCmd) |
                                date:'dd/MM/yyyy' }}</b>
                            </p>
                          </div>

                          <div class="col-md-6 col-12" *ngIf="offre?.dateRefus">
                            <h5 class="mb-0 fs-5 text-sub">{{'Date Refus' | uppercase}}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ $any(offre?.dateRefus) |
                                date:'dd/MM/yyyy' }}</b>
                            </p>
                          </div>

                          <div class="col-md-6 col-12" *ngIf="offre?.commandStatut === 'REFUSE'">
                            <h5 class="mb-0 fs-5 text-sub">
                              {{'Motif de refus' | uppercase}}
                            </h5>
                            <p class="mb-1 fs-5 h4 text-dark text-wrap fw-semibold motif-refus-container"><b>
                                <i class="bi bi-exclamation-triangle-fill"></i>
                                {{
                                !!$any(offre?.motifRefus)
                                ? $any(offre?.motifRefus) : 'Pas de motif de
                                refus pour cette commande'
                                }}</b>
                            </p>
                          </div>
                        </ng-container>

                        <ng-container *ngIf="!readOnly">
                          <div class="col-md-6 col-12">
                            <h5 class="mb-0 fs-5 text-sub">{{'Distributeur' | uppercase}} <span
                                *ngIf="(offre?.distributeurs?.length > 1) && !forceSelectDistributeur"
                                class="text-danger">*</span></h5>
                            <p *ngIf="(offre?.distributeurs?.length === 1) || forceSelectDistributeur"
                              class="mb-0 fs-5 h4 fw-semibold" [ngClass]="{
                                'labo-label': offre?.distributeur?.typeEntreprise === 'FABRIQUANT',
                                'grossiste-label':offre?.distributeur?.typeEntreprise === 'GROSSISTE'
                              }">
                              <b>{{ offre?.distributeur?.raisonSociale }}</b>
                            </p>

                            <p *ngIf="!offre?.distributeurs?.length && !forceSelectDistributeur"
                              class="mb-0 fs-5 h5 fw-semibold text-warning ">
                              <i class="mdi mdi-information mdi-16px mr-1"></i>
                              Vous n'avez aucun service actif chez un fournisseur.
                            </p>

                            <ng-container *ngIf="offre?.distributeurs?.length > 1 && !forceSelectDistributeur">
                              <div class="d-flex row mx-0 mt-1 px-0 pt-0 pb-1">

                                <div class="col-md-9 col-12 mx-0 px-0">
                                  <div class="input-group picker-input">
                                    <select [(ngModel)]="offre.distributeur" id="offreDistributeur"
                                      [disabled]="offre?.distributeurs?.length === 1 || forceSelectDistributeur"
                                      class="custom-select" [compareWith]="compareFn"
                                      style="border-radius: 10px; font-size: 1rem; color: black; font-weight: 600;">
                                      <option *ngFor="let item of offre.distributeurs" [ngValue]="item"
                                        [selected]="item?.id === offre.distributeur?.id">{{item.raisonSociale}}</option>
                                    </select>
                                  </div>
                                </div>
                              </div>
                            </ng-container>

                          </div>

                          <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
                            <div id="client-picker-input" class="col-md-6 col-12">
                              <h5 class="mb-0 fs-5 text-sub">
                                <span *ngIf="currentUser?.societe?.typeEntreprise !== 'FABRIQUANT'; else: fabClient">
                                  {{'Code national de la pharmacie' | uppercase}}
                                </span>
                                <ng-template #fabClient>
                                  {{'Client' | uppercase}} <span class="text-danger">*</span>
                                </ng-template>
                              </h5>

                              <div class="input-group picker-input search-input-picker mt-2 mb-1 w-100">
                                <input type="text" class="custom-search-input form-control form-control-md"
                                  [editable]="true" [(ngModel)]="selectedSociete"
                                  (ngModelChange)="(offre.client = selectedSociete)" [ngbTypeahead]="searchSociete"
                                  [resultTemplate]="searchResultTemplate" [inputFormatter]="societeFormatter"
                                  [resultFormatter]="societeFormatter">

                                <ng-template #searchResultTemplate let-result="result">
                                  <div>
                                    <span class="badge badge-info mr-2">{{result?.code}}</span>

                                    <b><ngb-highlight [result]="result?.raisonSociale"></ngb-highlight></b>
                                  </div>

                                  <span class="badge badge-light mx-1 p-1">Dr. {{ result?.nomResponsable }}</span>
                                  <span class="badge badge-dark mx-1 p-1">{{result?.ville}}</span>
                                </ng-template>


                                <div class="picker-icons"><i class="mdi mdi-magnify pointer"></i></div>
                              </div>
                            </div>

                            <div id="client-picker-input" class="col-md-6 col-12"
                              *ngIf="currentUser?.societe?.typeEntreprise !== 'FABRIQUANT'">
                              <h5 class="mb-0 fs-5 text-sub">{{'Code client chez le grossiste' | uppercase}}</h5>

                              <div class="w-100 d-flex row mx-0 px-0 mt-2 mb-1">
                                <div class="input-group picker-input search-input-picker">
                                  <input type="text" class="custom-search-input form-control form-control-md"
                                    [editable]="true" [(ngModel)]="selectedClientLocal"
                                    [readOnly]="isClientLocalReadOnly"
                                    (ngModelChange)="offre.clientLocal = selectedClientLocal"
                                    [ngbTypeahead]="searchClientLocal" [resultTemplate]="clientLocalResTemplate"
                                    [inputFormatter]="clientLocalFormatter" [resultFormatter]="clientLocalFormatter">

                                  <ng-template #clientLocalResTemplate let-result="result">
                                    <div>
                                      <span class="badge badge-info mr-2">{{result?.code}}</span>

                                      <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                                    </div>

                                    <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                                  </ng-template>


                                  <div class="picker-icons"><i class="mdi mdi-magnify pointer"></i></div>
                                </div>

                              </div>
                            </div>

                          </ng-container>

                          <div *ngIf="offre.raisonSocialeTransporteur" class="col-md-6 col-12 pb-1">
                            <label for="raisonSocialeTransporteur"
                              class="form-label h5 text-sub d-flex row align-items-center mx-0 mt-1">{{'Transporteur' |
                              uppercase}}
                              <span class="text-danger mx-1">*</span></label>

                            <select2 [(ngModel)]="offre.transporteurCommande" [data]="transporteurValuePair"
                              class="form-control-md px-0" [readonly]="readOnly" listPosition="above"
                              [hideSelectedItems]="false" [multiple]="false">
                            </select2>
                          </div>

                          <div *ngIf="offre?.listDelaiPaiements?.length" class="col-md-6 col-12 mt-1 pb-1">
                            <label for="modePaiement"
                              class="form-label h5 text-sub d-flex row align-items-center mx-0 mt-1">{{'Mode de
                              paiement' | uppercase}}
                              <span class="text-danger mx-1">*</span></label>

                            <select2 [ngModel]="offre.delaiPaiement" (ngModelChange)="modePaiementValueChange($event)"
                              [data]="modePaiementValuePair" class="form-control-md col-8 px-0" [readonly]="readOnly"
                              listPosition="above" [hideSelectedItems]="false" [multiple]="false"></select2>

                          </div>
                        </ng-container>

                        <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                          <div *ngIf="offre?.dynamicScriptCondition"
                            class="col-auto d-flex align-items-end pr-0 my-1 mx-0">
                            <wph-conditions-dynamiques [readOnly]="true"
                              [ngModel]="offre.dynamicScriptCondition"></wph-conditions-dynamiques>
                          </div>
                        </ng-container>

                        <div class="col-sm-6 col-12 pb-1">
                          <div class="form-group m-0 d-flex align-items-center">
                            <input id="utiliserValeurHt" type="checkbox" [disabled]="true"
                              [checked]="offre.utiliserValeurHt === 'O'" style="width: 20px; height: 20px">
                            <label for="utiliserValeurHt" class="col-form-label fs-5 text-dark ml-1"><b>Utiliser Valeur
                                HT</b></label>
                          </div>

                          <div class="form-group m-0 d-flex align-items-center">
                            <input id="palierTestValeurBrut" type="checkbox" [disabled]="true"
                              [checked]="offre.palierTestValeurBrut === 'O'" style="width: 20px; height: 20px">
                            <label for="palierTestValeurBrut" class="col-form-label fs-5 text-dark ml-1"><b>Condition
                                sur
                                Brut</b></label>
                          </div>
                        </div>

                        <div class="col-sm-6 col-12 pb-1" *ngIf="offre?.commentaireOffre">
                          <h5 class="mb-0 fs-5 text-sub">{{ 'Commentaire' | uppercase }}</h5>
                          <p class="mb-0 fs-5 h5 text-dark fw-semibold">{{offre?.commentaireOffre}}
                          </p>
                        </div>

                        <div *ngIf="hasOffreConditions(offre)"
                          class="col-auto mb-0 mt-1 mx-1 h-100 rounded-lg d-flex justify-content-start p-1"
                          [style.background-color]="'var(--wo-primary-100)'">
                          <div class="col-auto px-2">
                            <p class="text-start mb-0 h5 text-dark">Conditions de l'offre</p>
                            <div class="d-flex gap-2 flex-wrap px-1 m-0 w-100">
                              <div class="conditions-row-wo">
                                <button *ngIf="offre?.qteMin"
                                  class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                                  <span>Qté Min</span>
                                  &nbsp;

                                  <i class="mdi mdi-arrow-right mdi-16px"></i>
                                  &nbsp;

                                  <span>{{ offre?.qteMin }}</span>
                                </button>

                                <button *ngIf="offre?.qteMax"
                                  class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                                  <span>Qté Max</span>
                                  &nbsp;

                                  <i class="mdi mdi-arrow-right mdi-16px"></i>
                                  &nbsp;

                                  <span>{{ offre?.qteMax }}</span>
                                </button>

                                <button *ngIf="offre?.valeurMin"
                                  class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                                  <span>Valeur Min</span>
                                  &nbsp;

                                  <i class="mdi mdi-arrow-right mdi-16px"></i>
                                  &nbsp;

                                  <span>{{ offre?.valeurMin | number: '1.2-2' }}</span>
                                </button>

                                <button *ngIf="offre?.valeurMax"
                                  class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                                  <span>Valeur Max</span>
                                  &nbsp;

                                  <i class="mdi mdi-arrow-right mdi-16px"></i>
                                  &nbsp;

                                  <span>{{ offre?.valeurMax | number: '1.2-2' }}</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>

                        <ng-container *ngIf="offre?.listePaliersRemisesAdditionnels?.length">
                          <div class="col-auto mb-0 mt-1 h-100 rounded-lg d-flex justify-content-center p-1"
                            [style.background-color]="'var(--wo-primary-100)'">
                            <div class="col-auto">
                              <p class="h5 mb-0 text-dark">Remises Additionnelles de l'Offre</p>
                              <div class="d-flex gap-2 flex-wrap ">
                                <wph-paliers-view viewMode="badge"
                                  [paliers]="offre.listePaliersRemisesAdditionnels"></wph-paliers-view>
                              </div>
                            </div>
                          </div>

                        </ng-container>

                        <ng-container *ngIf="displayDummy">
                          <div class="col-md-6 col-12"></div>
                        </ng-container>
                      </ng-template>

                      <ng-template #monoProduitTemplate let-produit="produit">
                        <div class="col-sm-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{ 'Qté Cmd' | uppercase }} <span *ngIf="!readOnly"
                              class="text-danger">*</span></h5>

                          <p *ngIf="readOnly" class="mb-0 fs-5 h4 fw-semibold text-dark">
                            <b>{{ (produit?.qteCmd || 0) | number: '1.0-0' }} </b>
                          </p>

                          <div *ngIf="!readOnly" class="d-flex col-sm-6 col-12 mx-0 px-0 my-1">
                            <div class="input-group mt-1">
                              <input type="number" [readOnly]="readOnly" class="form-control form-control-md"
                                [ngModel]="produit.qteCmd"
                                (ngModelChange)="qteCmdChangeOffreMonoProduit(produit, $event)"
                                class="form-control text-right" id="qteCmd" placeholder="Qté Cmd" min="0"
                                (focus)="ngbPopover.open()" [ngbPopover]="produit?.messageEtat" container="body"
                                popoverClass="increase-popover-width" placement="bottom" [ngClass]="{
                                  'column-success': 
                                    (produit?.qteCmd > 0 && produit?.etat !== 'I'),
                                  
                                  'column-error': 
                                    (produit?.qteCmd > 0 && produit?.etat === 'I'),
                        
                                  'column-warning': produit?.qteCmd > 0 && productIds?.includes(produit?.codeProduitCatalogue)
                                }" #ngbPopover="ngbPopover">
                            </div>

                          </div>
                        </div>

                        <div class="col-sm-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{'Taux Remise' | uppercase }}</h5>

                          <div *ngIf="!readOnly; else: txRemiseVal" class="input-group pt-2 d-flex align-items-end">
                            <ng-container [ngTemplateOutlet]="txRemiseVal"></ng-container>
                          </div>

                          <ng-template #txRemiseVal>
                            <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                              <b>{{ (produit?.tauxRemise || 0) | number: '1.0-0' }} <span
                                  *ngIf="produit?.tauxRemise">%</span></b>
                            </p>
                          </ng-template>

                        </div>

                        <div class="col-sm-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">PPV</h5>
                          <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                            <b>{{ produit?.ppv | number: '1.2-2' }}</b>
                          </p>
                        </div>

                        <div class="col-sm-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">PPH</h5>
                          <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                            <b>{{ produit?.prixVenteTtc | number: '1.2-2' }}</b>
                          </p>
                        </div>

                        <div *ngIf="produit?.colisage" class="col-sm-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{ 'Colisage' | uppercase }}</h5>

                          <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                            <b>{{ (produit?.colisage || 0) | number: '1.0-0' }}</b>
                          </p>
                        </div>

                        <div class="col-sm-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{ 'Date Début' | uppercase }}</h5>
                          <p class="mb-0 fs-5 h4 text-dark fw-semibold "><b>{{ $any(offre.dateDebut) | date:'dd/MM/yyyy'
                              }}</b></p>
                        </div>

                        <div class="col-sm-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{ 'Date Fin' | uppercase }}</h5>
                          <p class="mb-0 fs-5 h4 text-dark fw-semibold "><b>{{ $any(offre.dateFin) | date:'dd/MM/yyyy'
                              }}</b>
                          </p>
                        </div>

                        <div class="col-sm-6 col-12">
                          <h5 class="mb-0 fs-5 text-sub">{{'Offert par' | uppercase}}</h5>
                          <p class="mb-0 fs-5 h4 fw-semibold" [ngClass]="{
                            'labo-label': offre?.offreur?.typeEntreprise === 'FABRIQUANT',
                            'grossiste-label':offre?.offreur?.typeEntreprise === 'GROSSISTE'
                          }">
                            <b>{{ offre?.offreur?.raisonSociale | uppercase }}</b>
                          </p>
                        </div>


                        <div class="col-sm-6 col-12" *ngIf="offre?.offreur?.typeEntreprise !== 'FABRIQUANT'">
                          <h5 class="mb-0 fs-5 text-sub">{{ 'Laboratoire' | uppercase }}</h5>
                          <p class="mb-0 fs-5 h4 fw-semibold" [ngClass]="{
                            'labo-label': offre?.laboratoire?.typeEntreprise === 'FABRIQUANT',
                            'grossiste-label':offre?.laboratoire?.typeEntreprise === 'GROSSISTE'
                          }">
                            <b>{{ offre.laboratoire?.raisonSociale | uppercase }}</b>
                          </p>
                        </div>


                        <ng-container *ngIf="readOnly">
                          <div class="col-sm-6 col-12">
                            <h5 class="mb-0 fs-5 text-sub">{{ 'Fournisseur' | uppercase }}</h5>
                            <p class="mb-0 fs-5 h4 fw-semibold" [ngClass]="{
                              'labo-label': offre?.distributeur?.typeEntreprise === 'FABRIQUANT',
                              'grossiste-label':offre?.distributeur?.typeEntreprise === 'GROSSISTE'
                            }">
                              <b> {{ offre.distributeur?.raisonSociale | uppercase }} </b>
                            </p>
                          </div>

                          <ng-container
                            *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL', 'ROLE_SUPER_ADMIN']">
                            <div class="col-sm-6 col-12">
                              <h5 class="mb-0 fs-5 text-sub">{{ 'Client' | uppercase }}</h5>
                              <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                                <b>{{ offre?.client?.raisonSociale ?? 'Non spécifié' | uppercase }}</b>
                              </p>
                            </div>

                            <div class="col-sm-6 col-12"
                              *ngIf="(offre?.societeCreateurCmd?.typeEntreprise === 'GROSSISTE') || (offre?.societeCreateurCmd?.typeEntreprise === 'FABRIQUANT')">
                              <h5 class="mb-0 fs-5 text-sub">{{ 'Saisie Par' | uppercase }}</h5>
                              <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                                <b>
                                  {{
                                  offre?.userCreateurCommande?.id + ' / ' +
                                  (offre?.societeCreateurCmd?.raisonSociale || '')
                                  }}
                                </b>
                              </p>
                            </div>

                            <div class="col-sm-6 col-12"
                              *ngIf="offre?.commandStatut === 'ANNULER' && offre?.annuleePar">
                              <h5 class="mb-0 fs-5 text-sub">{{ 'Annulée par' | uppercase }}</h5>
                              <p class="mb-0 fs-5 h4 fw-semibold text-dark">
                                <b>{{ offre?.annuleePar?.raisonSociale }}</b>
                              </p>
                            </div>

                            <ng-container
                              *ngIf="((offre?.commandStatut === 'NOUVELLE') || (offre?.commandStatut === 'BROUILLON')) && currentUser?.societe?.typeEntreprise !== 'FABRIQUANT'">
                              <div id="client-picker-input" class="col-sm-6 col-12"
                                *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']">
                                <label class="form-label d-flex row align-items-center m-0">
                                  <b class="p-0 mb-0 h6 text-sub"> {{ 'Code client chez le grossiste' | uppercase }}
                                  </b>
                                  <span *ngIf="showCodeLocalWarning" class="pointer-cus p-0 ml-1"
                                    style="margin-top: -3px;" popoverClass="increase-popover-width-warning"
                                    placement="top" container="body" [ngbPopover]="popoverContent">
                                    <i class="mdi mdi-alert-octagon mdi-24px text-warning"></i>
                                  </span>
                                </label>

                                <div class="w-100 d-flex row mx-0 px-0 my-1">
                                  <div class="input-group picker-input search-input-picker">
                                    <input type="text" class="custom-search-input form-control form-control-md"
                                      [editable]="true" [(ngModel)]="selectedClientLocal"
                                      [readOnly]="isClientLocalReadOnly || readOnly"
                                      (ngModelChange)="offre.clientLocal = selectedClientLocal"
                                      [ngbTypeahead]="searchClientLocal" [resultTemplate]="clientLocalResTemplate"
                                      [inputFormatter]="clientLocalFormatter" [resultFormatter]="clientLocalFormatter">

                                    <ng-template #clientLocalResTemplate let-result="result">
                                      <div>
                                        <span class="badge badge-info mr-2">{{result?.code}}</span>

                                        <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                                      </div>

                                      <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                                    </ng-template>


                                    <div class="picker-icons"><i class="mdi mdi-magnify pointer"></i></div>
                                  </div>

                                </div>
                              </div>

                            </ng-container>

                          </ng-container>

                          <div class="col-sm-6 col-12" *ngIf="offre?.transporteurCommande">
                            <h5 class="mb-0 fs-5 text-sub">{{ 'Transporteur' | uppercase }}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ offre?.transporteurCommande
                                }}</b>
                            </p>
                          </div>

                          <div class="col-sm-6 col-12" *ngIf="offre?.delaiPaiement">
                            <h5 class="mb-0 fs-5 text-sub">{{ 'Mode de paiement' | uppercase }}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ offre?.delaiPaiement?.label
                                }}</b>
                            </p>
                          </div>

                          <div class="col-sm-6 col-12"
                            *ngIf="offre?.dateConfirmation && offre?.commandStatut === 'NOUVELLE'">
                            <h5 class="mb-0 fs-5 text-sub">{{ 'Date de Confirmation' | uppercase }}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ offre?.dateConfirmation |
                                date:'dd/MM/yyyy'
                                }}</b>
                            </p>
                          </div>

                          <div class="col-sm-6 col-12"
                            *ngIf="offre?.dateAcceptation && !offre?.dateAnnulationCmd && offre?.commandStatut === 'ACCEPTER'">
                            <h5 class="mb-0 fs-5 text-sub">{{ "Date d'acceptation" | uppercase }}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ $any(offre?.dateAcceptation) |
                                date:'dd/MM/yyyy' }}</b>
                            </p>
                          </div>

                          <div class="col-sm-6 col-12" *ngIf="offre?.dateAnnulationCmd">
                            <h5 class="mb-0 fs-5 text-sub">{{ "Date d'annulation" | uppercase }}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ $any(offre?.dateAnnulationCmd) |
                                date:'dd/MM/yyyy' }}</b>
                            </p>
                          </div>

                          <div class="col-sm-6 col-12" *ngIf="offre?.dateSuppressionCmd">
                            <h5 class="mb-0 fs-5 text-sub">{{ 'Date Suppression' | uppercase }}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ $any(offre?.dateSuppressionCmd) |
                                date:'dd/MM/yyyy' }}</b>
                            </p>
                          </div>

                          <div class="col-sm-6 col-12" *ngIf="offre?.dateRefus">
                            <h5 class="mb-0 fs-5 text-sub">{{ 'Date Refus' | uppercase }}</h5>
                            <p class="mb-0 fs-5 h4 text-dark fw-semibold"><b>{{ $any(offre?.dateRefus) |
                                date:'dd/MM/yyyy' }}</b>
                            </p>
                          </div>

                          <div class="col-sm-6 col-12" *ngIf="offre?.commandStatut === 'REFUSE'">
                            <h5 class="mb-0 fs-5 text-sub">{{ 'Motif de refus' | uppercase }}</h5>
                            <p class="mb-0 fs-5 h4 text-dark text-wrap fw-semibold"><b>{{
                                !!$any(offre?.motifRefus)
                                ? $any(offre?.motifRefus) : 'Pas de motif de
                                refus pour cette commande'}}</b>
                            </p>
                          </div>
                        </ng-container>

                        <ng-container *ngIf="!readOnly">
                          <div class="col-sm-6 col-12">
                            <h5 class="mb-0 fs-5 text-sub">{{ 'Distributeur' | uppercase }} <span
                                *ngIf="(offre?.distributeurs?.length > 1) && !forceSelectDistributeur"
                                class="text-danger">*</span></h5>
                            <p *ngIf="(offre?.distributeurs?.length === 1) || forceSelectDistributeur"
                              class="mb-0 fs-5 h4 fw-semibold" [ngClass]="{
                                'labo-label': offre?.distributeur?.typeEntreprise === 'FABRIQUANT',
                                'grossiste-label':offre?.distributeur?.typeEntreprise === 'GROSSISTE'
                              }">
                              <b>{{ offre?.distributeur?.raisonSociale }}</b>
                            </p>

                            <p *ngIf="!offre?.distributeurs?.length && !forceSelectDistributeur"
                              class="mb-0 fs-5 h5 fw-semibold text-warning ">
                              <i class="mdi mdi-information mdi-16px mr-1"></i>
                              Vous n'avez aucun service actif chez un fournisseur.
                            </p>

                            <ng-container *ngIf="offre?.distributeurs?.length > 1 && !forceSelectDistributeur">
                              <div class="d-flex row mx-0 my-2">

                                <div class="col-md-9 col-12 mx-0 px-0">
                                  <div class="input-group picker-input">
                                    <select [(ngModel)]="offre.distributeur" id="offreDistributeur"
                                      [disabled]="offre?.distributeurs?.length === 1 || forceSelectDistributeur"
                                      class="custom-select" [compareWith]="compareFn"
                                      style="border-radius: 10px; font-size: 1rem; color: black; font-weight: 600;">
                                      <option *ngFor="let item of offre.distributeurs" [ngValue]="item"
                                        [selected]="item?.id === offre.distributeur?.id">{{item.raisonSociale}}</option>
                                    </select>
                                  </div>
                                </div>
                              </div>
                            </ng-container>

                          </div>

                          <div *ngIf="canSelectTypeRfOuUg" class="col-sm-6 col-12 mt-1 pb-1">
                            <label for="selectTypeRfOuUg"
                              class="form-label h5 text-sub d-flex row align-items-center mx-0 mt-1">{{ 'Type Remise' |
                              uppercase }}</label>

                            <span class="d-block w-100">
                              <label for="RF{{produit?.id}}" class="text-dark"> <b>RF</b> </label> <input
                                id="RF{{produit?.id}}" type="radio" name="selectedTypeRemiseEnum{{produit?.id}}"
                                value="RF" [ngModel]="produit.selectedTypeRemiseEnum" class="mx-1"
                                (ngModelChange)="selectedRemiseChange($event, produit)"
                                [disabled]="produit?.selectedPalier?.typeSelectionRfUg !== 'OR'">

                              <label for="UG{{produit?.id}}" class="text-dark"> <b>UG</b> </label> <input
                                id="UG{{produit?.id}}" type="radio" name="selectedTypeRemiseEnum{{produit?.id}}"
                                value="UG" [ngModel]="produit.selectedTypeRemiseEnum" class="mx-1"
                                [disabled]="produit?.selectedPalier?.typeSelectionRfUg !== 'OR'"
                                (ngModelChange)="selectedRemiseChange($event, produit)">
                            </span>
                          </div>

                          <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
                            <div id="client-picker-input" class="col-sm-6 col-12">
                              <h5 class="mb-0 fs-5 text-sub">
                                <span *ngIf="currentUser?.societe?.typeEntreprise !== 'FABRIQUANT'; else: fabClient">
                                  {{ 'Code national de la pharmacie' | uppercase }}
                                </span>
                                <ng-template #fabClient>
                                  {{'Client' | uppercase}} <span class="text-danger">*</span>
                                </ng-template>
                              </h5>

                              <div class="input-group picker-input search-input-picker mt-2 mb-1 w-100">
                                <input type="text" class="custom-search-input form-control form-control-md"
                                  [editable]="true" [(ngModel)]="selectedSociete"
                                  (ngModelChange)="(offre.client = selectedSociete)" [ngbTypeahead]="searchSociete"
                                  [resultTemplate]="searchResultTemplate" [inputFormatter]="societeFormatter"
                                  [resultFormatter]="societeFormatter">

                                <ng-template #searchResultTemplate let-result="result">
                                  <div>
                                    <span class="badge badge-info mr-2">{{result?.code}}</span>

                                    <b><ngb-highlight [result]="result?.raisonSociale"></ngb-highlight></b>
                                  </div>

                                  <span class="badge badge-light mx-1 p-1">Dr. {{ result?.nomResponsable }}</span>
                                  <span class="badge badge-dark mx-1 p-1">{{result?.ville}}</span>
                                </ng-template>


                                <div class="picker-icons"><i class="mdi mdi-magnify pointer"></i></div>
                              </div>
                            </div>

                            <div id="client-picker-input" class="col-sm-6 col-12"
                              *ngIf="currentUser?.societe?.typeEntreprise !== 'FABRIQUANT'">
                              <h5 class="mb-0 fs-5 text-sub">{{ 'Code client chez le grossiste' | uppercase }}</h5>

                              <div class="w-100 d-flex row mx-0 px-0 mt-2 mb-1">
                                <div class="input-group picker-input search-input-picker">
                                  <input type="text" class="custom-search-input form-control form-control-md"
                                    [editable]="true" [(ngModel)]="selectedClientLocal"
                                    [readOnly]="isClientLocalReadOnly"
                                    (ngModelChange)="offre.clientLocal = selectedClientLocal"
                                    [ngbTypeahead]="searchClientLocal" [resultTemplate]="clientLocalResTemplate"
                                    [inputFormatter]="clientLocalFormatter" [resultFormatter]="clientLocalFormatter">

                                  <ng-template #clientLocalResTemplate let-result="result">
                                    <div>
                                      <span class="badge badge-info mr-2">{{result?.code}}</span>

                                      <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                                    </div>

                                    <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                                  </ng-template>


                                  <div class="picker-icons"><i class="mdi mdi-magnify pointer"></i></div>
                                </div>

                              </div>
                            </div>

                          </ng-container>

                          <div *ngIf="offre.raisonSocialeTransporteur" class="col-sm-6 col-12 mt-1 pb-1">
                            <label for="raisonSocialeTransporteur"
                              class="form-label h5 text-sub d-flex row align-items-center mx-0 mt-1">{{ 'Transporteur' |
                              uppercase }}
                              <span class="text-danger mx-1">*</span></label>

                            <select2 [(ngModel)]="offre.transporteurCommande" [data]="transporteurValuePair"
                              class="form-control-md px-0" [readonly]="readOnly" listPosition="above"
                              [hideSelectedItems]="false" [multiple]="false">
                            </select2>
                          </div>

                          <div *ngIf="offre?.listDelaiPaiements?.length" class="col-sm-6 col-12 mt-1 pb-1">
                            <label for="modePaiement"
                              class="form-label h5 text-sub d-flex row align-items-center mx-0 mt-1">{{ 'Mode de
                              paiement' | uppercase }}
                              <span class="text-danger mx-1">*</span></label>

                            <select2 [ngModel]="offre.delaiPaiement" (ngModelChange)="modePaiementValueChange($event)"
                              [data]="modePaiementValuePair" class="form-control-md col-8 px-0" [readonly]="readOnly"
                              listPosition="above" [hideSelectedItems]="false" [multiple]="false"></select2>
                          </div>
                        </ng-container>

                        <div class="col-sm-6 col-12 pb-1">
                          <div class="form-group m-0 d-flex align-items-center">
                            <input id="utiliserValeurHt" type="checkbox" [disabled]="true"
                              [checked]="offre.utiliserValeurHt === 'O'" style="width: 20px; height: 20px">
                            <label for="utiliserValeurHt" class="col-form-label fs-5 text-dark ml-1"><b>Utiliser Valeur
                                HT</b></label>
                          </div>

                          <div class="form-group m-0 d-flex align-items-center">
                            <input id="palierTestValeurBrut" type="checkbox" [disabled]="true"
                              [checked]="offre.palierTestValeurBrut === 'O'" style="width: 20px; height: 20px">
                            <label for="palierTestValeurBrut" class="col-form-label fs-5 text-dark ml-1"><b>Condition
                                sur
                                Brut</b></label>
                          </div>
                        </div>

                        <div class="col-sm-6 col-12 pb-1" *ngIf="offre?.commentaireOffre">
                          <h5 class="mb-0 fs-5 text-sub">{{ 'Commentaire' | uppercase }}</h5>
                          <p class="mb-0 fs-5 h5 text-dark fw-semibold">{{offre?.commentaireOffre}}
                          </p>
                        </div>

                        <ng-container *ngIf="displayDummy">
                          <div class="col-sm-6 col-12"></div>
                        </ng-container>

                        <div class="row w-100 mx-0 my-1 p-1 d-xl-none d-flex">
                          <ng-container [ngTemplateOutlet]="badgeBlocConditionsTemplate"
                            [ngTemplateOutletContext]="{bloc: monoProduitBlocOffre, inline: true}"></ng-container>
                        </div>
                      </ng-template>

                    </div>
                  </div>
                </div>
              </div>
            </ng-template>

          </li>
        </ul>

        <div [ngbNavOutlet]="blocOffreHeaderNav"></div>

      </div>

    </div>
  </div>

  <ng-container *ngIf="offre?.nombreProduitsProposes === 1" [ngTemplateOutlet]="fixedPackSyntheseTemplate"
    [ngTemplateOutletContext]="{ offre: offre, bloc: monoProduitBlocOffre }"></ng-container>

  <div *ngIf="offre?.nombreProduitsProposes > 1" class="row px-2 w-100 mx-0"
    [style.margin-bottom]="fixedPackSyntheseHeight ? (fixedPackSyntheseHeight + 'px') : '120px'">
    <div id="pack-tab-nav-commande" class="card card-radius card-border-cstm-wo bg-transparent w-100">
      <ul ngbNav #packNav="ngbNav" [(activeId)]="activePackIndex" class="nav-tabs mb-1">
        <li [ngbNavItem]="ib + 1" *ngFor="let bloc of offre.listeBlocs; let ib=index">
          <a ngbNavLink [ngClass]="{
            'pack-error': bloc.etat === 'I', 
            'pack-success': bloc.etat === 'V', 
            'pack-warning': blocIds?.includes(bloc.id)
          }" class="d-flex row align-items-center" style="gap: 8px; min-width: 250px;">
            <div class="col w-100 p-0 m-0 d-flex align-items-center" style="min-width: 150px;">
              <span class="truncate-two-lines"
                [title]="bloc?.titre + (bloc?.blocObligatoire === 'O' ? '(Obligatoire**)' : '')">{{ bloc?.titre }} <b
                  *ngIf="bloc?.blocObligatoire === 'O'">(Obligatoire**)</b></span>
            </div>

            <div class="d-block">
              <button *ngIf="bloc.etat === 'I' " type="button" (click)="fireEvent($event);" [id]="'popover-container-' + bloc?.id"
                popoverClass="increase-popover-width" placement="left" [ngbPopover]="bloc?.messageEtat ? cstmPopoverTemplate : null"
                container="body" #ngbPopover="ngbPopover">
                <i class="bi bi-patch-exclamation-fill"
                  style="font-size: 20px; line-height: 1; vertical-align: middle;"></i>

                <ng-template #cstmPopoverTemplate>
                  <wph-popover-template [id]="'popover-' + bloc?.id" [popoverRef]="ngbPopover" [popoverContent]="bloc?.messageEtat"></wph-popover-template>
                </ng-template>
              </button>

              <button *ngIf="bloc?.etat === 'V'" type="button" (click)="fireEvent($event)">
                <i class="bi bi-patch-check-fill " style="font-size: 20px; line-height: 1; vertical-align: middle;"></i>
              </button>
            </div>

            <span *ngIf="(bloc?.listeFils[0]?.typeBloc === 'G' || bloc?.listeFils[0]?.typeBloc === 'R')"
              (click)="(bloc.displayDetailsSousBloc = !bloc.displayDetailsSousBloc); forceExpandOrCollapseAllSousBlocs(bloc, bloc.displayDetailsSousBloc)"
              [title]="(bloc.displayDetailsSousBloc ? 'Cacher' : 'Afficher') + ' les détails des produits'"
              class="collapse-expand-icon mx-1 d-flex align-items-center">
              <i *ngIf="!bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-right"></i>
              <i *ngIf="bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-down"></i>
            </span>
          </a>

          <ng-template ngbNavContent>
            <ngb-accordion #b="ngbAccordion" activeIds="custom-panel-{{ib}}" class="bg-white">
              <ngb-panel id="custom-panel-{{ib}}"
                cardClass="{{ bloc.etat === 'V' ? 'border border-success' : (bloc.etat === 'I' ? 'border border-danger' : '') }}">
                <ng-template ngbPanelHeader let-opened="opened">
                  <div *ngIf="(hasConditions(bloc) || bloc?.listePaliers?.length); else: noConditions"
                    class="row px-2 w-100 m-0 py-0">
                    <button (click)="bloc.displayConditionsEtRemisesRow = !bloc.displayConditionsEtRemisesRow"
                      *ngIf="!bloc?.displayConditionsEtRemisesRow" class="btn bg-cstm-info-wo">
                      <span>
                        Voir Remises & Conditions <i class="bi bi-arrows-angle-expand"></i>
                      </span>
                    </button>

                    <div [ngClass]="{'hidden-container': !bloc.displayConditionsEtRemisesRow}"
                      [style.background-color]="'var(--wo-primary-100)'"
                      class="row w-100 mx-0 mb-1 mt-2 p-1 d-flex flex-wrap position-relative"
                      style="border-radius: 10px !important;">
                      <span (click)="bloc.displayConditionsEtRemisesRow = false" class="remises-row-close"
                        style="top: -5px !important; right: 0 !important">
                        <i class="mdi mdi-close"></i> Fermer
                      </span>

                      <ng-container *ngIf="hasConditions(bloc)" [ngTemplateOutlet]="sousBlocConditionsBadge"
                        [ngTemplateOutletContext]="{bloc: bloc}"></ng-container>

                      <div *ngIf="bloc.listePaliers?.length"
                        class="col-12 w-100 m-0 rounded-lg d-flex align-items-center justify-content-start p-1"
                        [style.background-color]="'var(--wo-primary-100)'">
                        <div class="col-auto p-0">
                          <div class="d-flex align-items-center gap-2 flex-wrap">
                            <p class="h5 m-0 text-dark">Remises Financières</p>
                            <wph-paliers-view viewMode="badge" [paliers]="bloc.listePaliers"></wph-paliers-view>
                          </div>
                        </div>
                      </div>

                      <span class="d-flex col-12 align-items-center p-1 m-0">
                        <span class="actions-icons actions-icons-ov text-white pointer-cus bg-cstm-info">
                          <i class="bi bi-arrows-angle-expand text-white"></i>
                        </span>
                        <span class="text-dark mx-1">Conditions ou remises appliquées sur la ligne de produit</span>
                      </span>
                    </div>
                  </div>

                  <ng-template #noConditions>
                    <div class="col-auto p-0 m-0 mt-1"></div>
                  </ng-template>
                </ng-template>

                <ng-template ngbPanelContent>
                  <div id="panelContentRow" class="row w-100 mx-0 p-0">
                    <div id="produits-container" class="col-12 mx-0 p-0 bg-white">
                      <ng-container *ngIf="existeFilsBlocsProduits(bloc)" [ngTemplateOutlet]="blocProduits"
                        [ngTemplateOutletContext]="{listeProduits: getListeFilsBlocsProduits(bloc), parentOfProduits: bloc}">
                      </ng-container>

                      <div class="row mx-0 mt-0 mb-2 w-100"
                        *ngFor="let blocFils of getListeFilsBlocsNonProduits(bloc); let blocGroupeIndex=index">
                        <ng-container [ngTemplateOutlet]="blocGroupeTemplate"
                          [ngTemplateOutletContext]="{bloc: blocFils, blocGroupeIndex: blocGroupeIndex}">
                        </ng-container>
                      </div>
                    </div>
                  </div>

                  <ng-container [ngTemplateOutlet]="fixedPackSyntheseTemplate"
                    [ngTemplateOutletContext]="{ bloc: bloc, offre: offre }"></ng-container>
                </ng-template>
              </ngb-panel>


            </ngb-accordion>
          </ng-template>

        </li>
      </ul>

      <div [ngbNavOutlet]="packNav" class="px-0 pb-1 pt-0 bg-white card-border-alt mx-0 w-100"></div>

    </div>
  </div>
</ng-container>

<!-- fixed-pack-synthese template start -->
<ng-template #fixedPackSyntheseTemplate let-offre="offre" let-bloc="bloc">
  <div class="col-12 pt-0 pb-2 px-2 bg-white rounded shadow-lg fixed-synthese" [style.left]="fixedSyntheseLeft"
    [style.width]="fixedSyntheseWidth" wphIntersectionObserver [options]="{ rootMargin: '0px', threshold: 0.4 }"
    (isIntersecting)="isFixedPackSyntheseIntersecting($event)" #fixedPackSynthese>
    <div (click)="afficherSynthese = !afficherSynthese" class="row d-flex d-lg-none w-100 m-0">
      <div class="col-12 d-flex justify-content-center align-items-center">
        <i class="bi bi-chevron-compact-{{afficherSynthese ? 'down' : 'up'}} h3 m-0 icon-bounce"></i>
      </div>

      <div class="col-12 m-0 p-0 d-flex justify-content-center align-items-start">
        <h4 class="m-0 fs-5 text-center">{{ afficherSynthese ? 'Cacher Synthèse Commande' : 'Afficher Synthèse
          Commande'}}</h4>
      </div>
    </div>

    <div *ngIf="afficherSynthese" class="row d-flex flex-wrap mx-0 synthese-transition">
      <div *ngIf="offre?.listeBlocs?.length > 1 && contentContainerWidth > 1000"
        class="col bg-cstm-info-synthese col-cstm-radius m-1">
        <h4 class="mb-0 fs-5 text-center">Qte Cmd Pack</h4>
        <p class="mb-0 fs-5 h4 fw-semibold  text-center">
          {{ bloc?.totalQteCmd | number : '1.0-0'}}
        </p>
      </div>

      <div *ngIf="offre?.listeBlocs?.length > 1 && contentContainerWidth > 1000"
        class="col bg-cstm-info-synthese col-cstm-radius m-1">
        <h4 class="mb-0 fs-5 text-center">Montant Brut Pack</h4>
        <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{bloc?.totalValeurBruteCmd | number
          :
          '1.2-2'}} DH</p>
      </div>

      <div *ngIf="offre?.listeBlocs?.length > 1 && contentContainerWidth > 1000"
        class="col bg-cstm-info-synthese col-cstm-radius m-1">
        <h4 class="mb-0 fs-5 text-center">Montant Net Pack</h4>
        <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{bloc?.totalValeurNetteCmd | number
          :
          '1.2-2'}} DH</p>
      </div>

      <div class="col bg-cstm-info-synthese col-cstm-radius m-1">
        <h4 class="mb-0 fs-5 text-center">Total Qte Cmd</h4>
        <p class="mb-0 fs-5 h4 fw-semibold  text-center">
          {{offre?.totalQteCmd | number : '1.0-0'}}
        </p>
      </div>

      <div class="col bg-cstm-info-synthese col-cstm-radius m-1">
        <h4 class="mb-0 fs-5  text-center">Total Qte UG</h4>
        <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre?.totalQteUg | number :
          '1.0-0'}}</p>
      </div>

      <div class="col bg-cstm-info-synthese col-cstm-radius m-1">
        <h4 class="mb-0 fs-5 text-center">Total Montant Brut</h4>
        <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre?.totalValeurBruteCmd | number
          :
          '1.2-2'}} DH</p>
      </div>

      <div class="col bg-cstm-info-synthese col-cstm-radius m-1">
        <h4 class="mb-0 fs-5  text-center">Total Réduction</h4>
        <p class="mb-0 fs-5 h4 fw-semibold text-center">{{(offre?.totalValeurBruteCmd -
          offre?.totalValeurNetteCmd) | number : '1.2-2'}} DH</p>
      </div>

      <div class="col bg-cstm-info-synthese col-cstm-radius m-1">
        <h4 class="mb-0 fs-5 text-center">Total Montant Net</h4>
        <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre?.totalValeurNetteCmd | number
          :
          '1.2-2'}} DH</p>
      </div>
    </div>
  </div>
</ng-template>
<!-- fixed-pack-synthese template end -->

<!-- bloc-groupe template start -->
<ng-template #blocGroupeTemplate let-bloc="bloc" let-blocGroupeIndex="blocGroupeIndex">
  <div id="pack-tab-nav-commande"
    class="card bloc-card card-border-cstm-wo card-border card-radius bg-transparent w-100">
    <ul ngbNav #blocGroupeNav="ngbNav" class="nav-tabs">
      <li [ngbNavItem]="blocGroupeIndex">
        <a ngbNavLink class="d-flex row align-items-center" style="gap: 8px; min-width: 250px;" [ngClass]="{
          'pack-error': bloc?.etat === 'I',
          'pack-success': bloc?.etat === 'V',
          'pack-warning': blocIds?.includes(bloc.id)
        }">
          <div class="col w-100 p-0 m-0 d-flex align-items-center justify-content-between" style="min-width: 150px;">
            <span class="truncate-two-lines"
              [title]="bloc?.titre + (bloc?.blocObligatoire === 'O' ? '(Obligatoire**)' : '')">{{ bloc?.titre }} <b
                *ngIf="bloc?.blocObligatoire === 'O'">(Obligatoire**)</b></span>
          </div>

          <div class="d-block">
            <button *ngIf="bloc?.etat === 'I' " type="button" (click)="fireEvent($event)"
              popoverClass="increase-popover-width" placement="left" [ngbPopover]="bloc?.messageEtat ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover">
              <i class="bi bi-patch-exclamation-fill"
                style="font-size: 20px; line-height: 1; vertical-align: middle;"></i>

              <ng-template #cstmPopoverTemplate>
                <wph-popover-template [popoverRef]="ngbPopover" [popoverContent]="bloc?.messageEtat"></wph-popover-template>
              </ng-template>
            </button>

            <button *ngIf="bloc?.etat == 'V'" type="button" (click)="fireEvent($event)">
              <i class="bi bi-patch-check-fill " style="font-size: 20px; line-height: 1; vertical-align: middle;"></i>
            </button>
          </div>

          <span
            (click)="(bloc.displayDetailsSousBloc = !bloc.displayDetailsSousBloc); forceExpandOrCollapseAllSousBlocs(bloc, bloc.displayDetailsSousBloc)"
            [title]="(bloc.displayDetailsSousBloc ? 'Cacher' : 'Afficher') + ' les détails des produits'"
            class="collapse-expand-icon mx-1 d-flex align-items-center">
            <i *ngIf="!bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-right"></i>
            <i *ngIf="bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-down"></i>
          </span>
        </a>

        <ng-template ngbNavContent>
          <div *ngIf="bloc.displayDetailsSousBloc" class="row mx-0 w-100">
            <div *ngIf="(hasConditions(bloc) || bloc?.listePaliers?.length); else: noConditions"
              class="row w-100 m-0 p-0">
              <button (click)="bloc.displayConditionsEtRemisesRow = !bloc.displayConditionsEtRemisesRow"
                *ngIf="!bloc?.displayConditionsEtRemisesRow" class="btn bg-cstm-info-wo">
                <span>
                  Voir Remises & Conditions <i class="bi bi-arrows-angle-expand"></i>
                </span>
              </button>

              <div [ngClass]="{'hidden-container': !bloc.displayConditionsEtRemisesRow}"
                [style.background-color]="'var(--wo-primary-100)'"
                class="row w-100 mx-0 mb-1 mt-2 p-1 d-flex flex-wrap position-relative"
                style="border-radius: 10px !important;">
                <span (click)="bloc.displayConditionsEtRemisesRow = false" class="remises-row-close"
                  style="top: -5px !important; right: 0 !important">
                  <i class="mdi mdi-close"></i> Fermer
                </span>

                <ng-container *ngIf="hasConditions(bloc)" [ngTemplateOutlet]="sousBlocConditionsBadge"
                  [ngTemplateOutletContext]="{bloc: bloc}"></ng-container>

                <div *ngIf="bloc.listePaliers?.length"
                  class="col-12 w-100 m-0 rounded-lg d-flex align-items-center justify-content-start p-1"
                  [style.background-color]="'var(--wo-primary-100)'">
                  <ng-container>
                    <div class="col-auto p-0">
                      <div class="d-flex align-items-center gap-2 flex-wrap">
                        <p class="h5 m-0 text-dark">Remises Financières</p>
                        <wph-paliers-view viewMode="badge" [paliers]="bloc.listePaliers"></wph-paliers-view>
                      </div>
                    </div>

                  </ng-container>
                </div>

                <span class="d-flex col-12 align-items-center p-1 m-0">
                  <span class="actions-icons actions-icons-ov text-white pointer-cus bg-cstm-info">
                    <i class="bi bi-arrows-angle-expand text-white"></i>
                  </span>
                  <span class="text-dark mx-1">Conditions ou remises appliquées sur la ligne de produit</span>
                </span>

              </div>
            </div>

            <ng-template #noConditions>
              <div class="col-auto m-0 p-0 mt-1"></div>
            </ng-template>

            <ng-container *ngIf="existeFilsBlocsProduits(bloc)" [ngTemplateOutlet]="blocProduits"
              [ngTemplateOutletContext]="{listeProduits: getListeFilsBlocsProduits(bloc), parentOfProduits: bloc}">
            </ng-container>

            <div class="row mx-0 my-2 w-100"
              *ngFor="let blocFils of getListeFilsBlocsNonProduits(bloc); let subGroupIndex=index">
              <ng-container [ngTemplateOutlet]="blocGroupeTemplate"
                [ngTemplateOutletContext]="{bloc: blocFils, blocGroupeIndex: subGroupIndex}">
              </ng-container>
            </div>

          </div>
        </ng-template>
      </li>
    </ul>

    <div [ngbNavOutlet]="blocGroupeNav" class="px-1 pb-1 pt-0 bg-white card-border-alt mx-0 w-100"></div>
  </div>
</ng-template>
<!-- bloc-groupe template end -->

<!-- bloc-produits template start -->
<ng-template #blocProduits let-listeProduits="listeProduits" let-parentOfProduits="parentOfProduits">
  <div id="wo-bloc-produit" class="p-0 m-0" style="overflow-x: auto;">
    <div class="card card-border-bg-primary w-100">
      <div id="produitsCommande" class="card-body" style="padding: 0 !important;">
        <kendo-grid [data]="{data: listeProduits, total: listeProduits?.length}" [resizable]="true"
          class="w-100 h-100 produits-card fs-grid fs-grid-white" (cellClick)="cellClickHandler($event)"
          (cellClose)="cellCloseHandler($event)" [rowClass]="rowClassProduitUnitaire" #produitsGridTemp>

          <ng-template kendoGridToolbarTemplate>
            <div class="d-flex row mx-0 w-100 align-items-center justify-content-between grid-top-radius">
              <div class="col-auto p-0 m-0 d-flex align-items-center">
                <span class="h4 text-dark">Liste des produits</span>
              </div>

              <div class="col-auto p-0 m-0 d-flex justify-content-end">
                <div class="row mx-0 d-flex align-items-center">
                  <div *ngIf="!coffretEnabled && !readOnly" class="col-auto p-0 mx-2 my-0">
                    <span class="accelerator-container d-flex align-items-center">
                      <input type="number" [readOnly]="readOnly" placeholder="0" min="0" [value]="0"
                        (focus)="accQteCmd.select()" autocomplete="off"
                        (keydown.enter)="appliquerLaSaisieAccelere(accQteCmd, parentOfProduits, 'groupe')"
                        class="form-control form-control-md text-center" style="width: 60px;" #accQteCmd>

                      <span (click)="!readOnly && appliquerLaSaisieAccelere(accQteCmd, parentOfProduits, 'groupe')"
                        class="px-2 py-1 pointer-cus d-flex align-items-center text-white">
                        <i class="bi bi-save" style="font-size: 16px;"></i>
                      </span>
                    </span>
                  </div>

                  <div *ngIf="coffretEnabled" class="col-auto p-0 mx-2 my-0">
                    <span class="coffret-qte-container d-flex align-items-center">
                      <span (click)="!readOnly && qteCmdPackCoffretValueChange(null, false, true)"
                        class="px-2 py-1 pointer-cus d-flex align-items-center text-white">
                        <i class="mdi mdi-minus"></i>
                      </span>

                      <input type="text" [readOnly]="readOnly" (focus)="!readOnly && qteCoffret?.select()"
                        (ngModelChange)="qteCmdPackCoffretValueChange($event)" [ngModel]="qteCmdPackCoffret"
                        class="form-control form-control-sm text-center" wphAllowOnlyNumbers style="width: 60px;"
                        (keydown.arrowup)="!readOnly && qteCmdPackCoffretValueChange(null, true)" autocomplete="off"
                        (keydown.arrowdown)="!readOnly && qteCmdPackCoffretValueChange(null, false, true)" #qteCoffret>

                      <span (click)="!readOnly && qteCmdPackCoffretValueChange(null, true)"
                        class="px-2 py-1 pointer-cus d-flex align-items-center text-white">
                        <i class="mdi mdi-plus"></i>
                      </span>
                    </span>
                  </div>

                  <div class="col-auto p-0 m-0">
                    <div class="input-group picker-input">
                      <input type="search" placeholder="Rechercher par Designation"
                        [(ngModel)]="parentOfProduits.designationProduit"
                        class="form-control form-control-md pl-4 bl-input-search" id="groupeCritere"
                        style="width: 270px" autocomplete="off" />

                      <div class="picker-icons picker-icons-alt">
                        <i class="mdi mdi-magnify pointer"></i>
                      </div>
                    </div>
                  </div>

                </div>

              </div>
            </div>
          </ng-template>

          <kendo-grid-column [footerStyle]="{'white-space': 'normal'}" class="text-wrap" [footerClass]="'text-right'"
            [style]="{'white-space': 'normal'}" field="libelleProduit" title="Libellé produit" [width]="400">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              <div class="d-flex text-wrap align-items-center" [id]="parentOfProduits?.id + '-' + dataItem?.id">
                <img
                  (click)="dataItem?.docImageBlocOffre && openLargImageModal(largeImgModal, getBlocImage(dataItem?.docImageBlocOffre?.idhash))"
                  [src]="dataItem?.docImageBlocOffre ? getBlocImage(dataItem?.docImageBlocOffre?.idhash) : 'assets/images/default-img-alt.png'"
                  class="img-fluid-dataItem mx-1">
                <span class="mx-1">{{dataItem.libelleProduit}} <span *ngIf="dataItem?.blocObligatoire === 'O'"
                    class="text-danger">(Obligatoire**)</span> </span>
              </div>
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column [footerClass]="'text-left'" class="text-left" field="ppv" title="PPV" [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem.ppv | number:'1.2-2':'fr-FR'}}
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column [footerClass]="'text-left'" class="text-left" field="prixVenteTtc" title="PPH"
            [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem.prixVenteTtc | number:'1.2-2':'fr-FR'}}
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column *ngIf="!coffretEnabled" [footerClass]="'text-center'" class="text-center" field="colisage"
            title="Colisage" [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{dataItem.colisage | number:'1.0-0':'fr-FR'}}
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column *ngIf="!coffretEnabled" [footerClass]="'text-center'" class="text-center" title="Qté Min"
            [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              {{ dataItem?.qteMin | number: '1.0-0' }}
            </ng-template>
          </kendo-grid-column>

          <ng-container *ngIf="!coffretEnabled">

            <kendo-grid-column [editable]="!readOnly" [footerClass]="'text-center'" class="text-center" field="qteCmd"
              title="Qté Cmd" [width]="120">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="d-flex justify-content-center"
                  [attr.data-prefix]="'qteCMD' + parentOfProduits?.id + '-' + rowIndex">
                  <span class="bg-white px-2 py-1 text-right columnQte" (click)="readOnly && fireEvent($event)"
                    popoverClass="increase-popover-width" placement="left" [ngbPopover]="dataItem?.messageEtat ? cstmPopoverTemplate : null"
                    container="body" [ngClass]="{
                      'column-success': 
                        (dataItem.qteCmd > 0 && dataItem?.etat !== 'I'),
                      
                      'column-error': 
                        ((dataItem.qteCmd > 0 || dataItem?.blocObligatoire === 'O') && dataItem?.etat === 'I'),
            
                      'column-warning': dataItem.qteCmd > 0 && productIds?.includes(dataItem?.codeProduitCatalogue)
                    }" style="border-radius: 10px; width: 120px; border: 1px solid #ccc" #ngbPopover="ngbPopover">
                    {{(dataItem.qteCmd ?? 0) | number:'1.0-0':'fr-FR'}}

                    <ng-template #cstmPopoverTemplate>
                      <wph-popover-template [popoverRef]="ngbPopover" [popoverContent]="dataItem?.messageEtat"></wph-popover-template>
                    </ng-template>
                  </span>
                </div>
              </ng-template>

              <ng-template kendoGridEditTemplate let-dataItem="dataItem" let-column="column" let-formGroup="formGroup"
                let-rowIndex="rowIndex">
                <div class="d-flex justify-content-center mx-1">
                  <input [formControl]="formGroup.get('qteCmd')" wphAllowOnlyNumbers
                    (keydown)="onKeyDown($event, produitsGridTemp,false,'qteCMD' + parentOfProduits?.id)" type="number"
                    class="form-control text-right" id="qteCmd" placeholder="Qté Cmd" (focus)="ngbPopover.open()"
                    data-toggle="input-mask" mask="separator.0" thousandSeparator=" " autocomplete="off"
                    (blur)="produitsGridTemp?.closeCell()" popoverClass="increase-popover-width" placement="left"
                    [ngbPopover]="dataItem?.messageEtat ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover"
                    [attr.data-prefix]="'qteCMD' + parentOfProduits?.id + '-' + rowIndex" data-reverse="true"
                    [readOnly]="readOnly" style="max-width: 120px;">

                    <ng-template #cstmPopoverTemplate>
                      <wph-popover-template [popoverRef]="ngbPopover" [popoverContent]="dataItem?.messageEtat"></wph-popover-template>
                    </ng-template>
                </div>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column editor="numeric" [editable]="!readOnly"
              *ngIf="hasRatioUg(parentOfProduits?.listePaliers)" [footerClass]="'text-right'" class="text-right"
              field="qteUgSaisie" title="Qté UG Saisie" [width]="130">

              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="d-flex justify-content-center align-items-center"
                  [attr.data-prefix]="'qteUGSaisie' + parentOfProduits?.id + '-' + rowIndex">
                  <span [ngClass]="{
                    'column-success': 
                      (
                        (dataItem.qteUgSaisie > 0) && 
                        dataItem?.offre?.accepterPalierInvalide === 'N' && 
                        dataItem.parent.ratioUg && (dataItem.parent.qteUgSaisie > 0) && 
                        (dataItem.parent.qteUgSaisie === dataItem.parent.totalQteUg)
                      ),
                    'column-error': 
                      (
                        (dataItem.qteUgSaisie > 0) &&  
                        dataItem?.offre?.accepterPalierInvalide === 'N' && 
                        dataItem?.parent?.listePaliers.length && 
                        !dataItem.tauxRemise && dataItem.parent.ratioUg && 
                        (!dataItem.parent.qteUgSaisie || dataItem.parent.qteUgSaisie > 0) && 
                        (dataItem.parent.qteUgSaisie !== dataItem.parent.totalQteUg)
                      )
                  }" class="bg-white px-2 py-1 text-right columnQte"
                    style="border-radius: 10px; width: 120px; border: 1px solid #ccc"> {{ dataItem.qteUgSaisie ?
                    (dataItem.qteUgSaisie | number:'1.0-0':'fr-FR') : 'Saisir' }} </span>
                </div>
              </ng-template>

              <ng-template kendoGridEditTemplate let-rowIndex="rowIndex" let-dataItem="dataItem" let-column="column"
                let-formGroup="formGroup">
                <div class="d-flex justify-content-center mx-1">
                  <input [readonly]="readOnly"
                    (keydown)="onKeyDown($event, produitsGridTemp, false, 'qteUGSaisie' + parentOfProduits?.id)"
                    [formControl]="formGroup.get('qteUgSaisie')" type="text" class="form-control text-right"
                    autocomplete="off" (blur)="produitsGridTemp?.closeCell()" id="qteUgSaisie" placeholder="Qté UG"
                    data-toggle="input-mask" mask="separator.0" thousandSeparator=" " data-reverse="true"
                    style="max-width: 120px;"
                    [attr.data-prefix]="'qteUGSaisie' + parentOfProduits?.id + '-' + rowIndex">
                </div>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column *jhiHasTargetPlateformeFromWinplus="['WIN_OFFRE']" [width]="60" title="Stat"
              class="text-center">
              <ng-template kendoGridCellTemplate let-dataItem>
                <div class="d-flex justify-content-center align-items-center w-100">
                  <button (click)="openProduitStatistiques(produitStatistiquesTemplate, 'xl', dataItem)"
                    class="btn text-white btn-statistiques" title="Statistiques de consommation">S</button>
                </div>
              </ng-template>
            </kendo-grid-column>

            <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE']">
              <kendo-grid-column *jhiHasTargetPlateformeFromWinplus="[null]" [width]="60" title="Stat"
                class="text-center"
                [hidden]="offre?.distributeurs?.length === 1 && offre?.distributeurs[0]?.typeEntreprise !== 'GROSSISTE'">
                <ng-template kendoGridCellTemplate let-dataItem>
                  <div class="d-flex justify-content-center align-items-center w-100">
                    <button (click)="openProduitStatistiques(produitStatistiquesTemplate, 'xl', dataItem)"
                      class="btn text-white btn-statistiques" title="Statistiques de consommation">S</button>
                  </div>
                </ng-template>
              </kendo-grid-column>
            </ng-container>

            <kendo-grid-column *jhiHasTargetPlateformeFromWinplus="['WIN_OFFRE']" [hidden]="readOnly" title="Stock"
              [width]="75" class="text-center">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.stockProduitWinplus }}
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [footerClass]="'text-center'" class="text-center" field="tauxRemise" title="Taux Remise"
              [width]="120">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ (dataItem.qteCmd && dataItem.tauxRemise) ? ((dataItem.tauxRemise |
                number:'1.0-0':'fr-FR') + '%' ): ' '}}
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [footerClass]="'text-center'" class="text-center" field="totalQteUg" title="Qté UG"
              [width]="100">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ dataItem.totalQteUg ? (dataItem.totalQteUg | number:'1.0-0':'fr-FR') : ''}}
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column title="Type Remise" [width]="120"
              [hidden]="!canSelectTypeRfUg(listeProduits) || readOnly">
              <ng-template kendoGridCellTemplate let-dataItem>
                <span class="d-block w-100" *ngIf="(dataItem?.selectedPalier?.typeSelectionRfUg === 'OR')">
                  <label for="RF{{dataItem.id}}"> RF </label> <input id="RF{{dataItem.id}}" type="radio"
                    name="selectedTypeRemiseEnum{{dataItem.id}}" value="RF" [ngModel]="dataItem.selectedTypeRemiseEnum"
                    class="mx-1" (ngModelChange)="selectedRemiseChange($event, dataItem)">

                  <label for="UG{{dataItem.id}}"> UG </label> <input id="UG{{dataItem.id}}" type="radio"
                    name="selectedTypeRemiseEnum{{dataItem.id}}" value="UG" [ngModel]="dataItem.selectedTypeRemiseEnum"
                    class="mx-1" (ngModelChange)="selectedRemiseChange($event, dataItem)">
                </span>
              </ng-template>
            </kendo-grid-column>
          </ng-container>

          <kendo-grid-column *ngIf="coffretEnabled" [footerClass]="'text-left'" class="text-left"
            field="qteFixePrdInCoffret" title="Qté Fixe" [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              <div class="d-flex justify-content-start">
                <span class="bg-white px-2 py-1 text-right"
                  style="border-radius: 10px; width: 120px; border: 1px solid #ccc">
                  {{dataItem.qteFixePrdInCoffret | number:'1.0-0':'fr-FR'}}
                </span>
              </div>
            </ng-template>
          </kendo-grid-column>

          <ng-container *ngIf="!coffretEnabled">
            <kendo-grid-column [footerClass]="'text-left'" class="text-left" title="Paliers" [width]="100">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="row d-flex justify-content-center">
                  <span (click)="dataItem?.listePaliers?.length && open(popPaliers)"
                    class="actions-icons text-white pointer-cus"
                    [ngClass]="{'bg-cstm-info-alt': !dataItem?.listePaliers?.length, 'bg-only-cstm-info': dataItem?.listePaliers?.length}"
                    title="Afficher Paliers">
                    <i class="bi bi-arrows-angle-expand"
                      [ngClass]="{'opacity-light': !dataItem?.listePaliers?.length }"></i>
                  </span>
                </div>

                <ng-template #popPaliers let-modal>
                  <div class="modal-header">
                    <h4 class="modal-title" id="modal-basic-title">Paliers du produit</h4>

                    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                      <span>&times;</span>
                    </button>
                  </div>

                  <div class="modal-body">
                    <div class="row d-flex justify-content-between p-1 mx-0 w-100 align-items-center"
                      style="border-radius: 5px" [style.background-color]="'var(--wo-primary-100)'">
                      <span class="h5 text-dark">Remises</span>

                      <div class="col-12 d-flex gap-2 flex-wrap justify-content-center align-items-center">
                        <wph-paliers-view [viewMode]="'badge'" [bloc]="dataItem" #palierViewBloc></wph-paliers-view>
                      </div>
                    </div>

                  </div>

                  <div class="modal-footer">
                    <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">
                      Fermer
                    </button>
                  </div>
                </ng-template>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [hidden]="!hasAnyProduitsConditions(listeProduits)" [footerClass]="'text-center'"
              class="text-center" [width]="50">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="row d-flex justify-content-center" *ngIf="hasConditions(dataItem)">
                  <span class="actions-icons text-white bg-cstm-info pointer-cus" popoverClass="info-popover-container"
                    placement="left" container="body" [ngbPopover]="dataItem?.conditionsString">
                    <i class="bi bi-info-lg"></i>
                  </span>
                </div>
              </ng-template>
            </kendo-grid-column>
          </ng-container>

        </kendo-grid>
      </div>
    </div>
  </div>
</ng-template>
<!-- bloc-produits template end -->

<!-- Extra-items-modal template start -->
<ng-template #modalExtraItems let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">{{'Toutes les conditions : ' + extraConditionsTargetBloc?.titre |
      uppercase}}</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span>&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <div class="d-flex gap-2 flex-wrap conditions-row-wo">
      <button *ngIf="extraConditionsTargetBloc?.qteMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Qté Min</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.qteMin }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.qteMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Qté Max</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.qteMax }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.valeurMin"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Valeur Min</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.valeurMin | number: '1.2-2' }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.valeurMax"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Valeur Max</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.valeurMax | number: '1.2-2' }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.nbrObjFilsMin"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Sous blocs CMD Min</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.nbrObjFilsMin }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.nbrObjFilsMax"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Sous blocs CMD Max</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.nbrObjFilsMax }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.nombreProduitsMin"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Nbr Prd CMD Min</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.nombreProduitsMin }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.nombreProduitsMax"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Nbr Prd CMD Max</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.nombreProduitsMax }}</span>
      </button>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-outline-dark ml-2" (click)="modal.dismiss('Cross click')">Fermer</button>
  </div>
</ng-template>
<!-- Extra-items-modal template end -->

<!-- STATISTIQUES PRODUITS MODAL TEMPLATE START -->
<ng-template #produitStatistiquesTemplate let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">STATISTIQUES PRODUIT</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span class="h2">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <div id="wo-bloc-produit" class="p-0 m-0 d-flex w-100 row justify-content-between">

      <div id="produitsCommande" class="col-xl-3 col-lg-5 col-12 p-0 m-0"
        style="margin-right: 5px !important; margin-bottom: 10px !important">
        <kendo-grid [data]="statsProduitData1" class="w-100 h-100 produits-card fs-grid fs-grid-white">
          <kendo-grid-column [width]="40" field="libellePeriode" title="Mois / Année">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Mois / Année</span>
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column field="consommation" [width]="30" class="text-center" title="Consommation">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Consommation</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
              {{ dataItem?.consommation || 0 }}
            </ng-template>
          </kendo-grid-column>

          <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun résultat trouvé.</span>
          </ng-template>
        </kendo-grid>
      </div>

      <div id="produitsCommande" class="col-xl-3 col-lg-5 col-12 p-0 m-0" style="margin-bottom: 10px !important">
        <kendo-grid [data]="statsProduitData2" class="w-100 h-100 produits-card fs-grid fs-grid-white">
          <kendo-grid-column [width]="40" field="libellePeriode" title="Mois / Année">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Mois / Année</span>
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column [width]="30" field="consommation" class="text-center" title="Consommation">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Consommation</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
              {{ dataItem?.consommation || 0 }}
            </ng-template>
          </kendo-grid-column>

          <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun résultat trouvé.</span>
          </ng-template>
        </kendo-grid>
      </div>

      <div *ngIf="chartOptions" class="col h-100 w-100 m-0 d-xl-flex d-none justify-content-center align-items-center"
        style="height: 250px">
        <apx-chart [options]="chartOptions" [series]="chartOptions?.series" [chart]="chartOptions?.chart"
          [dataLabels]="chartOptions?.dataLabels" [plotOptions]="chartOptions?.plotOptions"
          [yaxis]="chartOptions?.yaxis" [legend]="chartOptions?.legend" [fill]="chartOptions?.fill"
          [annotations]="chartOptions?.annotations" [stroke]="chartOptions?.stroke" [tooltip]="chartOptions?.tooltip"
          [xaxis]="chartOptions?.xaxis"></apx-chart>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-outline-dark ml-2" (click)="modal.dismiss('Cross click')">Fermer</button>
  </div>
</ng-template>
<!-- STATISTIQUES PRODUITS MODAL TEMPLATE END -->

<!-- large-img-modal template start -->
<ng-template #largeImgModal let-modal>
  <img *ngIf="largeImageTargetUrl" [src]="largeImageTargetUrl" style="width: auto; height: auto;"
    alt="Selected post image">
</ng-template>
<!-- large-img-modal template end -->

<!-- badge-bloc-conditions template start -->
<ng-template #badgeBlocConditionsTemplate let-bloc="bloc" let-inline="inline">
  <div *ngIf="hasConditions(bloc)" class="rounded-lg justify-content-start m-1 p-1"
    [ngClass]="{'col-12 d-xl-flex d-none': !inline, 'col-auto d-flex': inline}"
    style="background-color: var(--wo-primary-100); height: fit-content">
    <div class="col-auto px-1">
      <p class="text-start text-dark mb-0 h5">
        Conditions de
        <span *ngIf="bloc?.typeBloc === 'P'">Pack</span>
        <span *ngIf="bloc?.typeBloc === 'G'">Groupe</span>
        <span *ngIf="bloc?.typeBloc === 'R'">Référence</span>
        <span *ngIf="bloc?.typeBloc === 'F'">Produit</span>
        <span *ngIf="!bloc?.typeBloc">l'offre</span>
      </p>

      <div class="d-flex gap-2 flex-wrap py-0 px-0 m-0 w-100">
        <div class="conditions-row">
          <button *ngIf="bloc?.qteMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Qté Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.qteMin }}</span>
          </button>

          <button *ngIf="bloc?.qteMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Qté Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.qteMax }}</span>
          </button>

          <button *ngIf="bloc?.valeurMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Valeur Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.valeurMin | number: '1.2-2' }}</span>
          </button>

          <button *ngIf="bloc?.valeurMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Valeur Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.valeurMax | number: '1.2-2' }}</span>
          </button>

          <button *ngIf="bloc?.nbrObjFilsMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Sous blocs CMD Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nbrObjFilsMin }}</span>
          </button>

          <button *ngIf="bloc?.nbrObjFilsMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Sous blocs CMD Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nbrObjFilsMax }}</span>
          </button>

          <button *ngIf="bloc?.nombreProduitsMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Nbr Prd CMD Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nombreProduitsMin }}</span>
          </button>

          <button *ngIf="bloc?.nombreProduitsMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Nbr Prd CMD Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nombreProduitsMax }}</span>
          </button>

        </div>
      </div>
    </div>

  </div>

  <div *ngIf="bloc.listePaliers?.length || bloc?.listePaliersRemisesAdditionnels?.length"
    class="m-1 rounded-lg justify-content-center p-1"
    [ngClass]="{'col-12 d-xl-flex d-none': !inline, 'col-auto d-flex': inline}"
    style="background-color: var(--wo-primary-100); height: fit-content">
    <div class="col-auto px-0">
      <p class="mb-0 h5 text-dark mx-1">{{ bloc.listePaliers?.length ? 'Remises Financières' : "Remises d'Offre" }}</p>
      <div class="d-flex gap-2 flex-wrap px-0 m-0">
        <wph-paliers-view viewMode="badge" [paliers]="bloc.listePaliers"></wph-paliers-view>
      </div>
    </div>
  </div>
</ng-template>
<!-- badge-bloc-conditions template end -->

<!-- Sous-bloc Badge-template Start -->
<ng-template #sousBlocConditionsBadge let-bloc="bloc">
  <div class="col-12 w-100 m-0 rounded-lg d-flex justify-content-start p-1"
    [style.background-color]="'var(--wo-primary-100)'">
    <div class="col-auto px-0">
      <div class="d-flex align-items-center gap-2 flex-wrap px-0 m-0 w-100">
        <p class="text-start m-0 mr-1 h5 text-dark">Conditions de {{bloc?.typeBloc === 'P' ? 'Pack' : bloc?.typeBloc ===
          'G' ? 'groupe' :
          'référence'}}</p>

        <div class="slice conditions-row-wo">
          <button *ngIf="bloc?.qteMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Qté Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.qteMin }}</span>
          </button>

          <button *ngIf="bloc?.qteMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Qté Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.qteMax }}</span>
          </button>

          <button *ngIf="bloc?.valeurMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Valeur Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.valeurMin | number: '1.2-2' }}</span>
          </button>

          <button *ngIf="bloc?.valeurMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Valeur Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.valeurMax | number: '1.2-2' }}</span>
          </button>

          <button *ngIf="bloc?.nbrObjFilsMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Sous blocs CMD Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nbrObjFilsMin }}</span>
          </button>

          <button *ngIf="bloc?.nbrObjFilsMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Sous blocs CMD Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nbrObjFilsMax }}</span>
          </button>

          <button *ngIf="bloc?.nombreProduitsMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Nbr Prd CMD Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nombreProduitsMin }}</span>
          </button>

          <button *ngIf="bloc?.nombreProduitsMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Nbr Prd CMD Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nombreProduitsMax }}</span>
          </button>
        </div>

        <button *ngIf="hasManyConditions(bloc)" (click)="openExtraConditionsModal(modalExtraItems, bloc)"
          class="btn btn-sm my-1 px-2 py-0 conditions-row-wo">
          <span></span>
          <i class="bi bi-plus"></i>
        </button>
      </div>
    </div>

  </div>
</ng-template>

<!-- Sous-bloc Badge-template End -->