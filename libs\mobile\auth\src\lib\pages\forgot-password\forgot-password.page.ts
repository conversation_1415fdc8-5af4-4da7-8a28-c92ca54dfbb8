import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from "@angular/forms";
import {ActivatedRoute} from "@angular/router";
import {Loading<PERSON>ontroller, ModalController, NavController} from "@ionic/angular";
import {AuthService} from "@wph/core/auth";
import {first} from "rxjs/operators";
import {StateModalComponent} from "../../../../../shared/src/lib/state-modal/state-modal.component";

@Component({
  selector: 'wph-forgot-password',
  templateUrl: './forgot-password.page.html',
  styleUrls: ['./forgot-password.page.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class ForgotPasswordPage implements OnInit {

  data = {
    'headerTitle': 'Login Platform des offres',
    "forgotPassword": "", // TODO: Mot de passe oublier?
    "subtitle": "Welcome",
    "labelUsername": "USERNAME",
    "labelPassword": "PASSWORD",
    "title": "Login to your account",
    "username": "Entrer le nom d'utilisateur",
    "password": "Entrer le mot de passe",
    "register": "", // TODO: Register
    "login": "Connexion",
    "subscribe": "Inscrire",
    "skip": "Skip",
    "facebook": "Facebook",
    "twitter": "Twitter",
    "google": "Google",
    "pinterest": "Pinterest",
    "description": "", // TODO: Don't have account?
    "logo": "assets/images/logos/logo.png",
  };
  loading = false;
  returnUrl = '/';

  codeForm!: FormGroup<{code: FormControl<string>}>;
  formSubmitted = false;
  error: any;

  showPassword = false;

  constructor(
    private route: ActivatedRoute,
    private navController: NavController,
    private authenticationService: AuthService,
    private fb: FormBuilder,
    private loadingCtrl: LoadingController,
    private modalController: ModalController
  ) {}

  ngOnInit(): void {
    this.codeForm = this.fb.group({
      code: ['', [Validators.required]]
    });


  }

  /**
   * convenience getter for easy access to form fields
   */
  get formValues() {
    return this.codeForm.controls;
  }

  /**
   * On submit form
   */
  async onSubmit() {
    const loading = await this.loadingCtrl.create({
      message: 'Chargement...',
      duration: 3000
    });

    loading.present();
    this.formSubmitted = true;
    if (this.codeForm.valid) {
      this.loading = true;
      this.authenticationService
        .resetPassword(
          this.formValues['code']?.value
        )
        .pipe(first())
        .subscribe(
          (data: any) => {
            loading.dismiss();
            this.loading = false;
            this.presentMessageModal('Un courriel de réinitialisation a été envoyé à votre adresse électronique.', 'success').then((success) => {
            });
            this.navController.pop();
          },
          (error: any) => {
            this.error = error;
            this.loading = false;
            loading.dismiss();
          }
        );
    }
  }

  onForgotPasswordFunc($event: MouseEvent) {

  }



  onRegisterFunc() {

  }

  get errMessage() {
    switch (this.error.error.status) {
      case 'UNAUTHORIZED':
        return 'Code incorrect';
      default:
        return 'Une erreur est survenue';
    }
  }

  async presentMessageModal(message: string, type: string) {
    const alert = await this.modalController.create({
      cssClass: 'small-modal',
      component: StateModalComponent,
      componentProps: {
        message: message,
        type: type
      }
    });

    await alert.present();
  }

  pop() {
    this.navController.pop();
  }
}
