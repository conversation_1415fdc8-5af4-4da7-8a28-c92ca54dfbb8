import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CommandFiltersComponent } from './command-filters.component';

describe('CommandFiltersComponent', () => {
  let component: CommandFiltersComponent;
  let fixture: ComponentFixture<CommandFiltersComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CommandFiltersComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(CommandFiltersComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
