import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { DemandesInscription } from '../models/demandes-incriptions.model';
import { Pagination } from '../models/PaginationDTO.ts';
import { DemandeInscriptionCriteria } from '../models/demadeInscriptionCriteria.model';

@Injectable({
  providedIn: 'root'
})
export class DemandesInscriptionsService {

  baseUrl: string;

  constructor(private httpClient: HttpClient,  @Inject('ENVIROMENT') private environment: any) {
    this.baseUrl = this.environment.base_url;
  }

  getListDemandes(criteria: DemandeInscriptionCriteria, pagination:Pagination=null){
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
      // sort: pagination.sortField ? pagination.sortField+','+pagination.sortMethod : null,
    }

    return this.httpClient.post<any>(this.baseUrl + `/api/v1/users/searchdemande`, criteria, { observe: 'body', params })
  }

  accepterDemande(societe:any, demande:any){
    return this.httpClient.post<any>(this.baseUrl + `/api/v1/users/accepterdemande/${societe?.id}`, demande , { observe: 'body' });
  }

  refuserDemande(demande: DemandesInscription){
    return this.httpClient.post<any>(this.baseUrl + `/api/v1/users/refuserdemande/` , demande, { observe: 'body', });
  }

  loadListSocieteClient(){
    return this.httpClient.get<any>(this.baseUrl + `/api/v1/entreprise/societes-pointvente` , { observe: 'body' });;
  }

  getPageNumber(skip: number, pageSize: number) {
    return (skip && pageSize) ? Math.floor(skip / pageSize) : 0;
  }


}
