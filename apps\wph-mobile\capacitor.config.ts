/// <reference types="@capacitor/push-notifications" />

import { CapacitorConfig } from "@capacitor/cli";

const config: CapacitorConfig = {
  appId: "com.sophatel.winoffre",
  appName: "Winoffre",
  webDir: "../../dist/apps/wph-mobile",
  bundledWebRuntime: false,
  loggingBehavior: "debug",
  android: {
    allowMixedContent: true
  },
  plugins: {
    "SplashScreen": {
      "launchShowDuration": 1000,
      "launchAutoHide": false,
      "backgroundColor": "#ffffffff",
      "androidSplashResourceName": "splash",
      "androidScaleType": "CENTER_CROP",
      "showSpinner": true,
      "androidSpinnerStyle": "horizontal",
      "iosSpinnerStyle": "small",
      "spinnerColor": "#999999",
      "splashFullScreen": true,
      "splashImmersive": false,
      "layoutName": "launch_screen",
      "useDialog": false
    },
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"]
    }
  }
};

export default config;
