import { NgModule } from "@angular/core";
import { RouterModule, Routes } from '@angular/router';
import { DemandesAccesComponent } from "./pages/demandes-acces/demandes-acces.component";
import { SuiviActivationAccesComponent } from "./pages/suivi-activation-acces/suivi-activation-acces.component";
import { AuthoritiesGuard } from "@wph/web/shared";
import { MesClientsComponent } from "./pages/mes-clients/mes-clients.component";

const routes: Routes = [
    { 
        path: 'liste',
        title: 'Clients à traiter',
        canActivate: [AuthoritiesGuard],
        component: DemandesAccesComponent
    },
    {
        path: 'demandes/liste',
        title: 'Liste des demandes d\'accès',
        component: SuiviActivationAccesComponent
    },
    {
        path: 'mes-clients',
        title: 'Mes Clients',
        component: MesClientsComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class WebGestionDemandesAccessRoutingModule {}