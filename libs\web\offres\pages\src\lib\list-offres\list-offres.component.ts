import { Component, ElementRef, On<PERSON><PERSON>roy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FilterOutput, Fournisseur, Offre, OffreCriteria, OffresService, Pagination, Produit } from "@wph/data-access";
import { ActivatedRoute, Params, Router } from "@angular/router";
import { ScrollService, UserInputService, getDynamicPageSize } from "@wph/web/shared";
import { FormGroup, FormControl } from "@angular/forms";
import { CellClickEvent, FilterService, GridDataResult, PageChangeEvent, SelectionEvent } from "@progress/kendo-angular-grid";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SortDescriptor } from '@progress/kendo-data-query';
import * as moment from 'moment';
import { AlertService, HasAccessService, Principal, SocieteType } from '@wph/shared';
import { AuthService } from '@wph/core/auth';
import { Subject, debounceTime, distinctUntilChanged, filter, takeUntil } from 'rxjs';
import { GridLine } from 'libs/web/shared/src/lib/components/list-or-value/list-or-value.component';

@Component({
  selector: 'wph-list-offres',
  templateUrl: './list-offres.component.html',
  styleUrls: ['./list-offres.component.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class ListOffresComponent implements OnInit, OnDestroy {

  unsubscribe$: Subject<boolean> = new Subject<boolean>();

  selectedFounrisseurs: Fournisseur[];
  offreForm: FormGroup;
  offreCriteria: OffreCriteria;
  offres: Offre[];
  originalListOffres: Offre[];
  selectedOffre: Offre;
  gridData: GridDataResult;
  offreSort: SortDescriptor[];
  objOffres: any;
  navigation = {
    skip: 0,
    pageSize: 10,
    sortField: null,
    sortMethod: null
  };
  cardViewNavigation: Pagination = {
    skip: 0,
    pageSize: 10
  };

  isSeleceted = false;
  onlyPublier = false;
  etatBar: string;
  pageParams: Params;
  isAdmin: boolean;
  currentUser: Principal;
  hasMore: boolean;
  forceCloseDropdowns: boolean = false;

  searchControl = new FormControl();
  displayFilter: boolean;

  cardViewData: Offre[] = [];
  offresMonoProduit: Offre[] = [];
  offresMultiProduit: Offre[] = [];

  isListView: boolean = false;

  pageSizes: number[] = [5, 10, 15, 20];

  selectedTypeOffre: string | null = null;

  stautsLabelsValues: any[] = [
    { label: 'Brouillon', value: 'B' },
    { label: 'Annulé', value: 'A' },
    { label: 'Publié', value: 'P' },
    { label: 'Cloturé', value: 'C' }
  ];

  listOrValue_config = {
    searchFields: ['code', 'raisonSociale'],
    displayFields: 'raisonSociale',
    tooltipField: 'code',
    grid: {
      lines: [
        new GridLine('code', 'Code Fournisseur', 'text', 40, 'end'),
        new GridLine('raisonSociale', 'Raison Sociale', 'text', 40, 'start'),
      ],
    },
    hiddenItems: []
  }
  categorieProduits: any[] = [];

  @ViewChild('cardViewContainer', { static: true }) cardViewContainer: ElementRef<any>;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private authService: AuthService,
    private alertService: AlertService,
    private offresService: OffresService,
    private scrollService: ScrollService,
    private hasAccesService: HasAccessService,
    private userInputService: UserInputService
  ) { }

  ngOnInit() {
    // this.setPageSize();
    this.listenToScrollPostion();
    this.listenToSearchControlChanges();

    this.fetchListeCategoriesProduits();

    this.currentUser = this.authService.currentUser();
    this.isAdmin = this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']);

    this.offresService.currentEtatBar.subscribe(etat => this.etatBar = etat);
    this.initFormmessage();

    if (!this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
      this.offreForm?.get('statut').setValue(['P']);
    }

    this.route.queryParams
      .subscribe((params: Params) => {
        this.pageParams = params;
        const mode: string = params['mode'];
        this.onlyPublier = params['onlyPublier'];

        if (params['dateDebut'] && params['dateFin']) {
          this.offreForm.get('dateDebut').setValue(moment(params['dateDebut']));
          this.offreForm.get('dateFin').setValue(moment(params['dateFin']));
        }

        if (params['categorie']) this.offreForm.get('categoriePrdId').setValue(+params['categorie']);

        if (params['libelleProduit']) this.offreForm.get('libelleProduit').setValue(params['libelleProduit']);

        if (params['laboratoire']) this.offreForm.get('laboratoire').setValue(JSON.parse(params['laboratoire']));

        if (this.onlyPublier) {
          this.offreForm.value.statut = 'P';
          this.SearchOffres();
        }

        if (mode) {
          this.isListView = (mode === 'liste');
        }

        this.updatePageParams();

      });

    if (!this.onlyPublier) {
      this.SearchOffres();
    }

  }

  setPageSize(currentHeight?: number): void {
    const dynamicSize = getDynamicPageSize(currentHeight, 49);

    if (dynamicSize !== this.navigation.pageSize) {
      this.navigation.pageSize = this.cardViewNavigation.pageSize = dynamicSize;

      this.pageSizes.push(dynamicSize);
      this.pageSizes = this.pageSizes.sort((a, b) => a - b);

      if (currentHeight) {
        this.cardViewData = [], this.SearchOffres();
      }
    }
  }

  listenToScrollPostion(): void {
    this.scrollService.reachedBottom$
      .pipe(
        takeUntil(this.unsubscribe$),
        filter(state => !!state)
      )
      .subscribe(_state => {

        if (this.hasMore) {
          this.isSeleceted = false;
          this.selectedOffre = null;

          if (this.cardViewNavigation.skip !== (this.cardViewNavigation.skip + this.cardViewNavigation.pageSize)) {
            this.cardViewNavigation.skip += this.cardViewNavigation.pageSize;

            this.SearchOffres(false, true);
          }
        }
      });
  }

  listenToSearchControlChanges(): void {
    this.searchControl.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe((searchQuery: string) => {
        this.navigation.skip = this.cardViewNavigation.skip = 0;
        this.offreCriteria = new OffreCriteria({ ...this.offreCriteria, titre: searchQuery });

        this.SearchOffres(true);
      });
  }

  private initFormmessage() {

    this.offreForm = new FormGroup({
      numero: new FormControl(null, []),
      titre: new FormControl(null, []),
      dateLivraisonDebut: new FormControl(null, []),
      dateLivraisonFin: new FormControl(null, []),
      fournisseur: new FormControl(null, []),
      laboratoire: new FormControl(null, []),
      dateDebut: new FormControl(null, []),
      dateFin: new FormControl(null, []),
      venteDirecteLabo: new FormControl(null, []),
      statut: new FormControl(null, []),
      libelleProduit: new FormControl(null, []),
      categoriePrdId: new FormControl(null, []),
      nonExpireesUniquement: new FormControl(!this.isAdmin, []),
    });

  }

  SearchOffres(isFilter = false, infiniteScrollTriggered = false) {
    if (this.offreForm.valid) {
      this.offreCriteria = new OffreCriteria(this.offreCriteria);
      this.offreCriteria.dateDebut = this.offreForm.value.dateDebut;
      this.offreCriteria.dateFin = this.offreForm.value.dateFin;
      this.offreCriteria.numero = this.offreForm.value.numero;
      this.offreCriteria.dateLivraisonDebut = this.offreForm.value.dateLivraisonDebut;
      this.offreCriteria.dateLivraisonFin = this.offreForm.value.dateLivraisonFin;
      this.offreCriteria.nonExpireesUniquement = this.offreForm.value.nonExpireesUniquement ? 'O' : null;
      this.offreCriteria.distributeur = this.offreForm.value.fournisseur;
      this.offreCriteria.offreur = this.offreForm.value.laboratoire;
      this.offreCriteria.venteDirecteLabo = this.offreForm.value.venteDirecteLabo ? 'O' : null;
      this.offreCriteria.statut = this.offreForm.value.statut?.length ? this.offreForm.value.statut : null;
      this.offreCriteria.categoriePrdId = this.offreForm.value?.categoriePrdId;
      this.offreCriteria.libelleProduit = this.offreForm.value?.libelleProduit ? (
        (typeof this.offreForm.value?.libelleProduit === 'string') ?
          this.offreForm.value?.libelleProduit : (this.offreForm.value?.libelleProduit as Produit)?.libelleProduit
      ) : null;

      if (isFilter) {
        this.selectedOffre = null;
        this.isSeleceted = false;
        this.navigation.skip = this.cardViewNavigation.skip = 0;
      }

      this.offresService.searchOffres(infiniteScrollTriggered ? this.cardViewNavigation : this.navigation, this.offreCriteria).subscribe(
        (data: any) => {
          isFilter && this.modalService.dismissAll();

          if (this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])) {
            const fournAvecAccesIds = this.hasAccesService.getListeFournisseurAvecAcces()?.map(fourn => {
              return fourn.hasAccess && fourn.fournisseurId;
            });

            if (fournAvecAccesIds?.length) {
              const filteredOffreList = data?.content?.map((offre: Offre) => {
                return {
                  ...offre,
                  distributeurs: offre?.distributeurs?.
                    filter(distributeur => fournAvecAccesIds?.includes(distributeur.id) || (distributeur?.typeEntreprise === SocieteType.FABRIQUANT))
                }
              });

              this.offres = filteredOffreList;
              this.objOffres = { ...data, content: filteredOffreList };
            } else {
              this.objOffres = data;
              this.offres = data['content'];
            }
          } else {
            this.objOffres = data;
            this.offres = data['content'];
          }

          this.hasMore = !data?.last;

          isFilter && (this.cardViewData = this.offresMonoProduit = this.offresMultiProduit = []);

          this.loadItems(infiniteScrollTriggered);
        }
      );
    }

  }

  private loadItems(infiniteScrollTriggered?: boolean): void {
    if (infiniteScrollTriggered) {
      this.cardViewData = this.cardViewData.concat(...this.offres);
    } else {
      this.gridData = {
        data: this.objOffres['content'],
        total: this.objOffres.totalElements,
      };

      this.cardViewData = this.gridData?.data;
    }

    this.offresMonoProduit = this.cardViewData?.filter(item => item?.nombreProduitsProposes === 1);
    this.offresMultiProduit = this.cardViewData?.filter(item => item?.nombreProduitsProposes > 1);

    !this.selectedTypeOffre && this.updateSelectedTypeOffre('multi');
  }

  public pageoffresChange(event: PageChangeEvent): void {

    if ((event.skip !== this.navigation.skip) || (event?.take !== this.navigation.pageSize)) {
      const pageSizeChanged: boolean = event?.take !== this.navigation.pageSize;

      this.isSeleceted = false;
      this.selectedOffre = null;

      this.navigation.skip = event.skip;
      this.navigation.pageSize = event?.take;

      this.SearchOffres(pageSizeChanged);
    }
  }

  updatePageParams() {
    let mode: string;

    mode = (this.isListView || this.isAdmin) ? 'liste' : 'cartes';

    this.router.navigate([], { queryParams: { ...this.pageParams, mode }, queryParamsHandling: 'merge' });
  }

  chooseOffre(item: SelectionEvent) {
    if (item.selectedRows && item.selectedRows.length) {
      this.selectedOffre = this.offres.find((ofr: { id: any; }) => ofr.id === item.selectedRows[0].dataItem.id);
      this.selectedOffre = { ...this.selectedOffre, canPlaceOrder: this.canPlaceOrder() };
      this.isSeleceted = true;
    } else {
      this.isSeleceted = false;
    }

  }

  offreEdit(offre?: Offre) {
    const mode = this.isListView ? 'liste' : 'cartes';
    this.router.navigate(
      ['win-offre/offres/saisie/' + offre?.id],
      {
        queryParams: {
          mode,
          readOnly: false,
          natureOffre: offre?.natureOffre,
          enteteCommandeId: offre?.enteteCommandeId,
          etatCommandeAchatGroupe: offre?.etatCommandeAchatGroupe
        }
      });
  }

  offreConsulter(offre?: Offre) {
    const mode = this.isListView ? 'liste' : 'cartes';
    this.router.navigate(
      ['win-offre/offres/edit/' + offre?.id],
      {
        state: { incr: true },
        queryParams: {
          mode,
          readOnly: true,
          natureOffre: offre?.natureOffre,
          enteteCommandeId: offre?.enteteCommandeId,
          etatCommandeAchatGroupe: offre?.etatCommandeAchatGroupe
        }
      });
  }

  offrePublier(offre?: Offre) {
    this.userInputService.confirm('Confirmation', 'Êtes vous sûr de vouloir publier cet offre ?').then(
      (result) => {
        this.offresService.publierOffreById(offre?.id).subscribe(data => {
          this.SearchOffres();
        });
      }, () => null);
  }

  offreCardsView() {
    this.router.navigateByUrl('win-offre/offres/widget');
  }

  offreAnnuler(offre?: Offre) {
    this.userInputService.confirm('Confirmation', 'Êtes vous sûr de vouloir annuler cet offre ?').then(
      (result) => {
        this.offresService.annulerrOffreById(offre?.id).subscribe(data => {
          this.SearchOffres();
        });
      }, () => null);
  }

  offreNouvelle() {
    const mode = this.isListView ? 'liste' : 'cartes';
    this.router.navigate(['win-offre/offres/saisie'], { queryParams: { mode } });
  }

  offreDupliquer(offre?: Offre) {
    const mode = this.isListView ? 'liste' : 'cartes';
    this.router.navigate(['win-offre/offres/saisie/' + offre?.id], { queryParams: { cloner: 'true', mode } });
  }

  CommandeSurOffre() {
    if (moment().isBefore(moment(this.selectedOffre.dateFin))) {
      const mode = this.isListView ? 'liste' : 'cartes';
      this.router.navigate(['win-offre/commandes/edit/' + this.selectedOffre.id], { queryParams: { offre: 'true', mode } });
    }

  }

  openDistributeurs(fournisseurs: Fournisseur[], content) {
    this.selectedFounrisseurs = fournisseurs;
    this.modalService.open(content, { ariaLabelledBy: "modal-basic-title", centered: true }).result.then((result) => {
      console.log(`Closed with: ${result}`);
    }, (reason) => {
      console.log(`Dismissed ${reason}`);
    });
  }

  cloturerOffre(offre?: Offre) {
    if (offre?.etatProposant === 'BROUILLON') {
      this.offreAnnuler(offre);
    } else {
      if (moment().isAfter(moment(offre?.dateFin))) {
        this.alertService.error("Cette offre est déjà terminée", 'MODAL');
      } else {
        this.userInputService.confirm('Confirmation', 'Êtes vous sûr de vouloir cloturer cet offre ?').then(
          (result) => {
            this.offresService.clotureOffre(offre?.id).subscribe(data => {
              this.SearchOffres();
            });
          }, () => null);
      }
    }
  }

  vider() {
    if (this.offreForm.dirty && this.offreForm.touched) {
      this.offreForm.reset({ nonExpireesUniquement: !this.isAdmin, categoriePrdId: null });

      this.selectedOffre = null;
      this.isSeleceted = false;
      this.cardViewData = this.offresMonoProduit = this.offresMultiProduit = [];

      this.SearchOffres();
    }
    this.modalService.dismissAll();
  }

  openFilterModal(modalContent: any, size: string) {
    this.modalService
      .open(modalContent, { ariaLabelledBy: 'modal-basic-title', size, backdrop: 'static' })
      .result.then(
        result => {
          console.log(result);
        },
        reason => {
          console.log('Err!', reason);
        }
      );
  }

  offreSortChange(sort: SortDescriptor[]): void {
    this.offreSort = sort;
    if (this.offreSort && this.offreSort.length > 0 && this.offreSort[0].dir) {
      this.navigation.sortField = sort[0].field;
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
    }

    this.SearchOffres(false);
  }

  SelectChanges(values: string[], filterService: FilterService, field: string) {
    filterService.filter({
      filters: values.map(value => ({
        field: field,
        operator: 'eq',
        value
      })),
      logic: 'or'
    });
  }

  filterModalAction(filterAction: FilterOutput): void {
    if (filterAction?.clear) {
      const qParams = this.route.snapshot.queryParams;
      this.navigation.skip = this.cardViewNavigation.skip = 0;

      this.vider();
      this.router.navigate(['.'], { relativeTo: this.route, queryParams: { mode: qParams['mode'] } });
    }

    filterAction?.filter && this.SearchOffres(true);
  }

  canPlaceOrder(): boolean {
    if (this.currentUser?.societe?.typeEntreprise === 'FABRIQUANT') {
      const matched = this.selectedOffre?.distributeurs?.filter(dist => dist?.id === this.currentUser?.societe?.id);
      return !!matched?.length;
    }

    return true;
  }

  reload() {
    this.isSeleceted = false;
    this.selectedOffre = null;
    this.SearchOffres();
  }

  cellClickHandler(event: CellClickEvent) {
    if (event?.column?.title !== 'Actions') {
      this.offreConsulter(event.dataItem);
    }
  }

  fetchListeCategoriesProduits() {
    this.offresService.getListeCategories().subscribe((data) => {
      data?.forEach((cat) => {
        this.categorieProduits.push({ label: cat.libelle, value: cat.id });
      });

      if (this.categorieProduits?.length) {
        this.categorieProduits.sort((a, b) => a.label.localeCompare(b.label));
      }
    });
  }

  updateSelectedTypeOffre(type: string): void {
    this.selectedTypeOffre = type;
    const el = this.cardViewContainer.nativeElement as HTMLDivElement;

    el && el.scrollTo({ top: 0, behavior: 'smooth' });
  }

  closeDropdowns() {
    this.forceCloseDropdowns = true;
    setTimeout(() => {
      this.forceCloseDropdowns = false;
    }, 200);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();

    this.scrollService.updateScrollState(false);
  }

}
