import { GroupeEntreprise } from "./groupe-entreprise.model";
import { PharmacieEntreprise } from "./pharmacie-entreprise.model";

export class BalanceAchatGroupeDto {
    id?: number;
    groupeAchatGroupe?: GroupeEntreprise;
    membreGroupe?: PharmacieEntreprise;
    montantConsomme?: number;
    montantSupportee?: number;
    nbrCmdConsommee?: number;
    nbrCmdSupportee?: number;

    constructor(balanceAchatGroupeDto: Partial<BalanceAchatGroupeDto>) {
        Object.assign(this, balanceAchatGroupeDto);
    }
}