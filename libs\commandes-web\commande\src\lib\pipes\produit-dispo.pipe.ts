import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
    name: 'produitDispo',
    pure: true
})
export class ProduitDispoPipe implements PipeTransform {
    
    transform(code: string) {
        switch (code) {
            case 'A01': 
              return 'Disponible';
            case 'A02': 
                return 'Disponible Moins';
            case 'B01': 
                return 'Non Disponible';
            case 'B02':
                return 'Rupture';
            case 'C01':
                return 'Non existant';
            case 'D01': 
                return 'Mauvaise QTE_MIN';
            case 'D02': 
            default:
                return 'Erreur serveur';
        }    
    }

}