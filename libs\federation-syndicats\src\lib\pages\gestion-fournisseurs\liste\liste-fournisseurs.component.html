<!-- start page title -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-5">Liste des Fournisseurs</h4>

        <div class="col-7 px-1">
            <div class="row justify-content-end align-items-center">
                <button (click)="ajouterFournisseur()" type="button" class="btn btn-sm btn-primary m-1"
                    style="padding-block: 6px;">
                    <i class="bi bi-plus-circle-fill"></i>
                    Ajouter Fournisseur
                </button>
            </div>
        </div>
    </div>
</div>
<!-- end page title -->

<div class="row d-flex m-0 px-1">
    <div class="card m-0 w-100 p-0" style="height: calc(100vh - 60px);">
        <div class="card-header py-1 pl-2 bg-white">
            <div class="row p-0">
                <div class="col-12 p-0 d-flex justify-content-end">
                    <div class="row p-0 justify-content-center justify-content-sm-end">
                        <div class="col-sm p-0 m-1">
                            <div class="input-group picker-input">
                                <input type="search" placeholder="Raison sociale" [formControl]="filterList"
                                    class="form-control form-control-md pl-4" id="groupeCritere" />

                                <div class="picker-icons picker-icons-alt">
                                    <i class="mdi mdi-magnify pointer"></i>
                                </div>
                            </div>
                        </div>


                        <button type="button" (click)="displayFilter = !displayFilter"
                            class="btn btn-sm search-btn m-1">
                            <span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
                                <i class="bi bi-sliders"></i>
                                <span class="mx-1">Recherche Avancé</span>
                            </span>

                            <ng-template #closeFilter>
                                <span class="d-flex align-items-center">
                                    <i class="mdi mdi-close"></i>
                                    <span class="mx-1">Fermer la recherche</span>
                                </span>
                            </ng-template>

                        </button>

                        <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
                    </div>
                </div>
            </div>
        </div>

        <form [formGroup]="filterForm" (ngSubmit)="appliquerFiltre()" wphFocusTrap>
            <div id="advanced-search" *ngIf="displayFilter" class="row p-0 flex-wrap px-1 my-2 gap-1">
                <div class="col-auto p-0 m-0">
                    <label for="code" class="col-12 py-0 col-form-label text-left">Code</label>

                    <div class="col-12 input-group">
                        <input type="text" name="code" formControlName="code"
                            class="form-control form-control-md b-radius bg-white" id="code">
                    </div>
                </div>

                <div class="col-auto p-0 m-0">
                    <label for="ville" class="col-12 py-0 col-form-label text-left">Ville</label>

                    <div class="col-12 input-group">
                        <input type="text" name="ville" formControlName="ville"
                            class="form-control form-control-md b-radius bg-white" id="ville">
                    </div>
                </div>

                <div class="col-auto p-0 m-0">
                    <label for="localite" class="col-12 py-0 col-form-label text-left">Localité</label>

                    <div class="col-12 input-group">
                        <input type="text" name="localite" formControlName="localite"
                            class="form-control form-control-md b-radius bg-white" id="localite">
                    </div>
                </div>

                <div class="col-auto p-0 m-0">
                    <label for="email" class="col-12 py-0 col-form-label text-left">E-mail</label>

                    <div class="col-12 input-group">
                        <input type="text" name="localite" formControlName="email"
                            class="form-control form-control-md b-radius bg-white" id="email">
                    </div>
                </div>

                <div class="col col-sm d-flex align-items-end p-0">
                    <button type="button" class="btn btn-sm btn-outline-primary b-radius" (click)="vider()">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                    <button type="submit" class="btn btn-sm btn-primary b-radius mx-1">
                        <i class="mdi mdi-filter"></i>
                    </button>
                </div>
            </div>
        </form>

        <div class="card-body m-0 p-0 bg-white mt-1" style="height:calc(100vh - 125px)">
            <kendo-grid class="fs-grid fs-listing-grid" [data]="gridView" [pageSize]="navigation.pageSize"
                [skip]="navigation.skip" [pageable]="true" [sortable]="{ mode: 'single' }" [sort]="pharmacieSort"
                (sortChange)="pharmacieGridSort($event)" style="height: 100%" (cellClick)="cellClickHandler($event)">

                <kendo-grid-column field="code" [width]="60" class="text-wrap">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        {{ dataItem?.code }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Code</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'numeric'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="raisonSociale" [width]="150" class="text-wrap">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        {{ dataItem?.raisonSociale }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Raison sociale</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'alpha'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="ville" [width]="140" [resizable]="false"
                    class="text-wrap">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        {{ dataItem?.ville ?? dataItem?.localite }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Ville / Localité</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'alpha'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="email" title="E-mail" [width]="120" [resizable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <span *ngIf="!dataItem?.email"
                            class="d-flex align-items-center justify-center k-gap-1 text-warning">
                            <i class="bi bi-shield-fill-exclamation"></i>
                            Indisponible
                        </span>
                        {{ dataItem?.email }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'alpha'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>
                
                <kendo-grid-column field="telephone" title="Téléphone" [width]="120" [resizable]="false" [sortable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <span *ngIf="!dataItem?.telephone"
                            class="d-flex align-items-center justify-center k-gap-1 text-warning">
                            <i class="bi bi-shield-fill-exclamation"></i>
                            Indisponible
                        </span>
                        {{ dataItem?.telephone }}
                    </ng-template>
                </kendo-grid-column>


                <kendo-grid-column field="typeEntreprise" [width]="100" [resizable]="false">
                    <ng-template kendoGridHeaderTemplate>
                        <span class="text-wrap">Type Entreprise</span>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        {{ dataItem?.typeEntreprise }}
                    </ng-template>
                </kendo-grid-column>


                <kendo-grid-column title="Action" [width]="50" class="no-ellipsis">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="d-flex justify-content-center">
                            <span (click)="consulterFournisseur(dataItem?.id, false)"
                                class="actions-icons btn-success pointer-cus" title="Modifier Fournisseur">
                                <i class="bi bi-pencil-square"></i>
                            </span>
                        </div>
                    </ng-template>
                </kendo-grid-column>

                <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
                    let-total="total">
                    <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
                        [navigation]="navigation" style="width: 100%;"
                        (pageChange)="onPageChange($event)"></wph-grid-custom-pager>
                </ng-template>

                <ng-template kendoGridNoRecordsTemplate>
                    <span>Aucun résultat trouvé.</span>
                </ng-template>
            </kendo-grid>
            <ng-template #emptyDate>
                <span>--/--/----</span>
            </ng-template>
        </div>
    </div>

</div>