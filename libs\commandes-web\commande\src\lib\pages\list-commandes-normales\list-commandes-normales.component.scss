#no-records {
    background: rgba(247, 162, 15, 0.075);
    i, .no-records-alt {
      font-size: clamp(0.6rem, 2vw, 1.1rem);
      font-weight: 500;
      color: rgb(247, 162, 15);
    }
  }
  
  .form-control {
    color: black;
    font-size: 1rem;
    border-radius: var(--winoffre-base-border-radius);
    font-weight: 600;
  }
  
  .modal-footer {
    .btn {
      color: black;
      font-size: 1rem;
      border-radius: var(--winoffre-base-border-radius);
      font-weight: 600;
    }
  }
  
  .input-group {
    .btn {
      border-top-right-radius: var(--winoffre-base-border-radius);
      border-bottom-right-radius: var(--winoffre-base-border-radius);
    }
  }
  
  label {
    color: var(--winoffre-text-light-shade);
    font-weight: 600;
  }
  
  .picker-input {
    .form-control {
      border-radius: var(--winoffre-base-border-radius) !important;
    }
  }