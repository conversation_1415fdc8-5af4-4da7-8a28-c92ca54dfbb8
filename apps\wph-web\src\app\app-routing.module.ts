import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CanActivateCommandeWeb } from '@wph/commandes-web/commande';
import { AppHostnameGuard, AuthGuardService, ChildAuthGuard } from '@wph/core/auth';
import { AchatsGroupesGuard } from '@wph/federation-syndicats';
import { LayoutContainerComponent } from "@wph/web/layout";
import { WildCardComponent } from './components/wild-card/wild-card.component';

const routes: Routes = [
  {
    path: 'auth',
    canActivate: [AppHostnameGuard],
    loadChildren: () => import('@wph/web/auth').then(m => m.WebAuthModule),
  },
  {
    path: '',
    component: LayoutContainerComponent,
    canActivate: [AuthGuardService, AppHostnameGuard],
    canActivateChild: [ChildAuthGuard],
    children: [
      // ? COMMANDES_WEB ROUTES
      {
        path: 'commande-web',
        canActivateChild: [CanActivateCommandeWeb],
        loadChildren: () => import('@wph/commandes-web/commande')
          .then(m => m.CommandeModule)
      },
      // ? PHARMA_HUB ROUTES
      {
        path: 'pharma-lien',
        loadChildren: () => import('./modules/pharma-hub/pharma-hub.module').then(m => m.PharmaHubModule)
      },
      // ? FEDERATION_SYNDICATS ROUTES
      {
        path: 'achats-groupes',
        canActivateChild: [AchatsGroupesGuard],
        loadChildren: () => import('./modules/federation-syndicats/federation-syndicats.module').then(m => m.FederationSyndicatsModule)
      },
      // ? WIN_OFFRE ROUTES
      {
        path: 'win-offre',
        loadChildren: () => import('./modules/win-offre/win-offre.module').then(m => m.WinOffreModule)
      },
      // ? WILD_CARD 
      {
        path: '**',
        component: WildCardComponent
      }
    ]
  }
];
@NgModule({
  imports: [RouterModule.forRoot(routes, { onSameUrlNavigation: 'reload' })],
  exports: [RouterModule],
})
export class AppRoutingModule { }
