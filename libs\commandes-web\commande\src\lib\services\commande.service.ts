import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { ProduitFournisseurCriteria } from '../models/produit-fournisseur-criteria';
import { BehaviorSubject, Observable } from 'rxjs';
import * as moment from 'moment';
import { AlertService, PlateformeService } from '@wph/shared';
import { CommandeNormaleCriteria, EnteteFacture, EnteteFacturePageable, Pagination } from '@wph/data-access';
import { EnteteBl } from '../models/EnteteBl';
import { CommandeDto } from '../models/CommandeDto';
import { PanierCriteria } from '../models/panier-criteria.dto';
import { ListProduitCriteria, ProduitDTO } from '../models/produitDTO';
import { ProduitCriteria } from '../models/produit-criteria';
import { ListCommandesCriteria } from '../models/listCommandesCriteria';
import { CheckDispoItem, CheckDispoResponse } from '../models/check-dispo.model';
import { AlertProduitDto } from '../models/alert-produit.model';
import { AlertProduitCriteria } from '../models/AlertProduitCriteria';

@Injectable({
    providedIn: 'root'
})
export class CommandeService {
    baseUrl: string | null = null;

    /* -------------------------------------------------------------------------- */
    /*                                Update panier                               */
    /* -------------------------------------------------------------------------- */
    private updatePanier = new BehaviorSubject<CommandeDto>(null);
    panier$ = this.updatePanier.asObservable();

    panierChanged(newCommande: CommandeDto) {
        this.updatePanier.next(newCommande);
    }

    constructor(
        @Inject('ENVIROMENT') private env: any,
        private httpClient: HttpClient,
        private srvAlert: AlertService,
        private plateformeService: PlateformeService
    ) {
        this.baseUrl = env.base_url;
    }

    /* -------------------------------------------------------------------------- */
    /*                             Catalogue Produits                             */

    /* -------------------------------------------------------------------------- */
    getCatalogueProduits(body: ProduitFournisseurCriteria, pagination?: Pagination) {
        const sort = (pagination?.sortField && pagination?.sortMethod) ?
            pagination?.sortField + ',' + pagination?.sortMethod :
            null;

        const pageParams = { page: pagination?.skip, size: pagination?.pageSize };
        sort && (pageParams['sort'] = sort);

        return this.httpClient.post<any>(`${this.baseUrl}/api/produit-fournisseur/search`, body, { observe: 'body', params: pageParams });
    }

    getCatalogueProduitByCode(id: number): Observable<ProduitDTO> {
        return this.httpClient.get<ProduitDTO>(`${this.baseUrl}/api/produit-fournisseur/${id}`, { observe: 'body' });
    }

    /* -------------------------------------------------------------------------- */
    /*                             Liste Nouveaux produits                        */

    /* -------------------------------------------------------------------------- */
    getListeNouveauxProduits(payload: ProduitCriteria, pagination?: Pagination): Observable<ListProduitCriteria> {
        const sort = (pagination?.sortField && pagination?.sortMethod) ?
            pagination?.sortField + ',' + pagination?.sortMethod :
            null;

        const params = { page: pagination?.skip, size: pagination?.pageSize };
        sort && (params['sort'] = sort);

        return this.httpClient.post<ListProduitCriteria>(`${this.baseUrl}/api/v1/management-produit/search-produit`, payload, { observe: 'body', params });
    }

    getProduitGeneralById(produitId: number): Observable<ProduitDTO> {
        return this.httpClient.get<ProduitDTO>(`${this.baseUrl}/api/v1/management-produit/${produitId}`, { observe: 'body' });
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Panier                                   */

    /* -------------------------------------------------------------------------- */
    getPanier(): Observable<CommandeDto> {
        const params = { codeSite: this.plateformeService.getCurrentGrossiste()?.noeud?.codeSite };
        return this.httpClient.get<CommandeDto>(`${this.baseUrl}/api/commande-normale/getpanier`, { observe: 'body', params });
    }

    dansPanier(payload: Partial<PanierCriteria>): Observable<CommandeDto> {
        return this.httpClient.post<CommandeDto>(`${this.baseUrl}/api/commande-normale/danspanier`, payload, { observe: 'body' });
    }

    setlocalPanier(nb: number) {
        localStorage.setItem('qtePanier', String(nb));
    }

    createBlFromCommande(idhash: string): Observable<any> {
        return this.httpClient.get<any>(`${this.baseUrl}/api/commande-normale/create-bl-from-cmd/${idhash}`, { observe: 'body' });
    }

    /* -------------------------------------------------------------------------- */
    /*                                  Commandes                                 */

    /* -------------------------------------------------------------------------- */
    saveCommandes(body: CommandeDto): Observable<CommandeDto> {
        return this.httpClient.post<CommandeDto>(`${this.baseUrl}/api/commande-normale/edit`, body, { observe: 'body' });
    }

    getListCommandes(body: CommandeNormaleCriteria, pagination?: Pagination): Observable<ListCommandesCriteria> {
        const sort = (pagination?.sortField && pagination?.sortMethod) ?
            pagination?.sortField + ',' + pagination?.sortMethod :
            null;

        const pageParams = { page: pagination.skip, size: pagination.pageSize, sort };
        return this.httpClient.post<ListCommandesCriteria>(`${this.baseUrl}/api/commande-normale/search`, body, { observe: 'body', params: pageParams });
    }

    getBonCommande(idhash: string): Observable<CommandeDto> {
        return this.httpClient.get<CommandeDto>(`${this.baseUrl}/api/commande-normale/${idhash}`, { observe: 'body' });
    }

    getBLCommande(idhash: number): Observable<EnteteBl> {
        return this.httpClient.get<EnteteBl>(`${this.baseUrl}/api/commande-normale/${idhash}/bl`, { observe: 'body' });
    }

    deleteCommande(idhash: string) {
        return this.httpClient.delete<any>(`${this.baseUrl}/api/commande-normale/${idhash}/annuler`, { observe: 'body' });
    }

    validerCommande(idhash: string) {
        return this.httpClient.get<any>(`${this.baseUrl}/api/commande-normale/${idhash}/valider`, { observe: 'body' });
    }

    checkDispo({ codeProduit, qteCmd }): Observable<CheckDispoResponse[]> {
        const body = {
            listeDemandes: [
                {
                    codeProduit,
                    quantiteMin: qteCmd,
                    typeCode: 2
                }
            ]
        };

        return this.httpClient.post<CheckDispoResponse[]>(`${this.baseUrl}/api/produit-fournisseur/checkdispo`, body, { observe: 'body' });
    }

    checkDispoMultiProduit(payload: CheckDispoItem[]): Observable<CheckDispoResponse[]> {
        const body = {
            listeDemandes: payload
        };

        return this.httpClient.post<CheckDispoResponse[]>(`${this.baseUrl}/api/produit-fournisseur/checkdispo`, body, { observe: 'body' });
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Factures                                */

    /* -------------------------------------------------------------------------- */

    getListeFactures(criteria: any, pagination: Pagination): Observable<EnteteFacturePageable> {
        const sort = (pagination?.sortField && pagination?.sortMethod) ?
            pagination?.sortField + ',' + pagination?.sortMethod :
            null;

        const params = { page: Math.floor(pagination?.skip / pagination?.pageSize), size: pagination?.pageSize };
        sort && (params['sort'] = sort);

        return this.httpClient.post<EnteteFacturePageable>(this.baseUrl + '/api/facture_client/search', criteria, { observe: 'body', params });
    }

    getFactureById(idFacture: number): Observable<EnteteFacture> {
        return this.httpClient.get<EnteteFacture>(this.baseUrl + '/api/facture_client/' + idFacture, { observe: 'body' });
    }

    /* -------------------------------------------------------------------------- */
    /*                                   alert produit                                  */

    /* -------------------------------------------------------------------------- */
    addAlertProduit(alertProduitDto: AlertProduitDto): Observable<AlertProduitDto> {
        return this.httpClient.post<AlertProduitDto>(`${this.baseUrl}/api/conso_ext/alertproduit/addalert`, alertProduitDto, { observe: 'body' });
      }
      searchAlertProduit(alertCriteria: AlertProduitCriteria): Observable<AlertProduitDto[]> {
        return this.httpClient.post<AlertProduitDto[]>(`${this.baseUrl}/api/conso_ext/alertproduit/search`, alertCriteria, { observe: 'body' });
      }

      deleteAlertProduit(id: number): Observable<AlertProduitDto> {
        return this.httpClient.delete<AlertProduitDto>(`${this.baseUrl}/api/conso_ext/alertproduit/delete/${id}`, { observe: 'body' });
      }
    /* -------------------------------------------------------------------------- */
    /*                                   helpers                                  */

    /* -------------------------------------------------------------------------- */
    getLast30Days() {
        const d = new Date();
        d.setDate(d.getDate() - 30); // 30 jours
        return moment(new Date(d));
    }

    showToast(code: any) {
        switch (code) {
            case 'A01': {
                this.srvAlert.info('Disponible', 'TOAST');
            }
                break;
            case 'A02': {
                this.srvAlert.warning('DisponibleMoins', 'TOAST');
            }
                break;
            case 'B01': {
                this.srvAlert.warning('Non Disponible', 'TOAST');
            }
                break;
            case 'B02': {
                this.srvAlert.warning('Rupture', 'TOAST');
            }
                break;
            case 'C01': {
                this.srvAlert.warning('Non existant', 'TOAST');
            }
                break;
            case 'D01': {
                this.srvAlert.warning('BAD_QTE_MIN', 'TOAST');
            }
                break;
            case 'D02': {
                this.srvAlert.warning('BAD_TYPECODE', 'TOAST');
            }
                break;
            default:
                this.srvAlert.warning('Error Server ', 'TOAST');
                break;
        }
    }

}
