
.card, .card-header, .card-body {
    border-radius: var(--winoffre-base-border-radius) !important;
}

.b-radius {
    border-radius: var(--winoffre-base-border-radius) !important;
}
.btn {
    font-size: 1rem;
    font-weight: 600;
}

.input-group {
    .form-control {
      color: black;
      font-weight: 700;
        border-top-left-radius: var(--winoffre-base-border-radius);
        border-bottom-left-radius: var(--winoffre-base-border-radius);

    }
    .btn {
      border-top-right-radius: var(--winoffre-base-border-radius);
      border-bottom-right-radius: var(--winoffre-base-border-radius);
    }
  }

label {
    color: black !important;
    font-weight: 700 !important;
}

.picker-input {
    .form-control{
        border-radius: var(--winoffre-base-border-radius) !important;
    }
}

.selected-switch {
    color: #f3f3f3;
    background: var(--wf-primary-500);
}

.card-view {
    height: calc(100vh - 190px);
    overflow-x: hidden;
    overflow-y: auto;
}

.opacity-light {
  opacity: 0.6 !important;
}


.btn-success{
  background: var(--fs-success) !important;
  border-color: var(--fs-success) !important;
}

.btn-danger{
  background: var(--fs-danger) !important;
  border-color: var(--fs-danger) !important;
}

::ng-deep .status-switch-align .k-switch-label-on{
  margin-top: 2px;
  margin-left: 3px;
}

::ng-deep .status-switch-align .k-switch-label-off{
  margin-top: 2px;
  margin-right: 3px;
}
.btn-close {
  background-color: #EAEDF5;
  color: #2C3E50;
  border-radius: 50%;
  height: 45px;
  width: 45px;
  border: none;
}


.satisfaction-option {
  background-color: var(--wf-primary-50);
  margin-bottom: 20px;
  border-radius: 6px;
  padding: 10px;
  display: flex;
align-items: center;
justify-content: space-between;
color: #696C75;
}

::ng-deep .custom-rating .ngb-rating .bi {
  font-size: 20px !important;
}

::ng-deep .custom-rating .ngb-rating .bi.bi-star-fill {
  color: #EAB308 !important;
}

.satisfaction-option i {
font-size: 24px;
}
.satisfaction-option label {
font-size: 16px;

}

.satisfaction-textarea {
border: 2px solid var(--wf-primary-400);
-webkit-appearance: none; /* Remove default styling on Webkit browsers */
-moz-appearance: none; /* Remove default styling on Firefox */
appearance: none;
background-color: #fff;
display: inline-block;
position: relative;
}
