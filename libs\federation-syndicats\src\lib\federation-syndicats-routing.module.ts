import { NgModule } from "@angular/core";
import { RouterModule, Routes } from '@angular/router';
import { AccueilComponent } from "./pages/accueil/accueil.component";
import { ActualitesComponent } from "./pages/actualites/actualites.component";
import { AuthoritiesGuard } from "@wph/web/shared";

const routes: Routes = [
    {
        path: '',
        pathMatch: 'full',
        redirectTo: 'accueil'
    },
    {
        path: 'accueil',
        title: 'Accueil',
        component: AccueilComponent
    },
    {
        path: 'actualites',
        title: 'Actualités',
        component: ActualitesComponent
    },
    {
      path: 'auth-log',
      title: 'Journal de connexion',
      loadChildren: () => import('./pages/audit/audit.module').then(m => m.AuditModule)
    },
    {
        path: 'pharmacies',
        loadChildren: () => import('./pages/pharmacies/pharmacies.module').then(m => m.PharmaciesModule)
    },
    {
        path: 'pharmacie-maroc',
        loadChildren: () => import('./pages/pharmacie-maroc/pharmacie-maroc.module').then(m => m.PharmacieMarocModule)
    },
    {
        path: 'gestion-fournisseurs',
        loadChildren: () => import('./pages/gestion-fournisseurs/gestion-fournisseurs.module').then(m => m.GestionFournisseursModule)
    },
    {
        path: 'gestion-contact-fournisseurs',
        loadChildren: () => import('./pages/gestion-contact-fournisseurs/gestion-contact-fournisseur.module').then(m => m.GestionContactFournisseurModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_RESPONSABLE', 'ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'groupes',
        loadChildren: () => import('./pages/groupes/groupes.module').then(m => m.GroupeModule)
    },
    {
        path: 'offres',
        loadChildren: () => import('./pages/offres/offres.module').then(m => m.OffresModule)
    },
    {
        path: 'commandes',
        loadChildren: () => import('./pages/commandes/commandes.module').then(m => m.CommandesModule)
    },
    {
        path: 'bons-livraison',
        loadChildren: () => import('./pages/livraison/livraison.module').then(m => m.LivraisonModule)
    },
    {
        path: 'sondages',
        loadChildren: () => import('./pages/sondages/sondages.module').then(m => m.SondagesModule)
    },
    {
        path: 'statistiques',
        loadChildren: () => import('./pages/statistiques/statistiques.module').then(m => m.StatistiquesModule)
    },
    {
        path: 'domaine',
        loadChildren: () => import('../../../web/gestion-domaine/domaine.module').then(m => m.DomaineModule)
    },
    {
        path: 'actualites',
        loadChildren: () => import('./pages/actualites/actualites.module').then(m => m.ActualitesModule),
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'guides',
        loadChildren: () => import('./pages/guides/guides.module').then(m => m.GuidesModule)
    },
    {
        path: 'config-parametre',
        loadChildren: () => import('@wph/web/gestion-config-parametre').then(m => m.GestionConfigParametreModule)
    },
    {
        path: '**',
        redirectTo: 'accueil'
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class FederationSyndicatsRoutingModule { }
