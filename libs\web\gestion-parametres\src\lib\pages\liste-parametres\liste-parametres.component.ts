import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { OffresService } from "@wph/data-access";
import { AlertService, Parameter, ParameterService, Societe, SocieteType } from "@wph/shared";
import { UserInputService } from "@wph/web/shared";
import { Observable, debounceTime, distinctUntilChanged, map, of, switchMap } from "rxjs";

@Component({
    selector: 'wph-liste-parametres',
    templateUrl: './liste-parametres.component.html'
})
export class ListeParametresComponent implements OnInit {
    editMode: boolean;
    paramForm: FormGroup;
    grid: GridDataResult = { data: [], total: 0 };

    constructor(
        private fb: FormBuilder,
        private modalService: NgbModal,
        private alertService: AlertService,
        private offreService: OffresService,
        private userInputService: UserInputService,
        private parameterService: ParameterService,
    ) { }

    ngOnInit(): void {
        this.paramForm = this.fb.group({
            id: [null],
            audited: [null],
            accepterCmdIndispo: [null],
            codeSite: [null],
            enableCheckQuota: [null],
            maxQteCheckdispo: [null],
            maxQteCmd: [null]
        });

        this.getListeParametres();
    }

    get controls() {
        return this.paramForm.controls;
    }

    getListeParametres(): void {
        this.parameterService.getAllParameters().subscribe(res => {
            this.grid = { data: res, total: res?.length };
        });
    }

    createOrUpdateParameter(): void {
        let payload: Parameter = new Parameter();
        const { codeSite, ...values } = this.paramForm.getRawValue();

        payload['codeSite'] = (codeSite instanceof Object) ? (codeSite as Societe)?.noeud?.codeSite : +codeSite;

        this.parameterService.addOrUpdateParameter({ ...values, ...payload }).subscribe(res => {
            this.getListeParametres();
        });
    }

    patchFormValues(item: Parameter): void {
        this.paramForm.patchValue(item);

        this.controls['id'].disable();
        this.controls['codeSite'].disable();
    }

    resetForm(): void {
        this.paramForm.reset();
        this.controls['codeSite'].enable();
    }

    filterList(searchQuery: string) {
        return this.offreService.searchSociete({
            raisonSociale: searchQuery,
            typeEntreprise: [SocieteType.GROSSISTE, SocieteType.FABRIQUANT]
        });
    }

    societeInputFormatter = (result: Societe) => result ? result?.noeud?.codeSite?.toString() : null;

    societeFormatter = (result: Societe) => result ? result?.raisonSociale : null;

    searchSociete = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase())
                }
                return of({ content: [] });
            }),
            map(res => res.content.slice(0, 5))
        );

    openModal(modalContent: any, size = 'lg'): void {
        this.modalService.open(modalContent, {
            ariaLabelledBy: 'modal-basic-title',
            size,
            backdrop: 'static',
        })
    }

    desactiverBatchDuSite(codeSite: number): void {
        this.userInputService.confirm("Confirmation", `Êtes-vous sûr de vouloir désactiver le batch du site: <b>${codeSite}</b> ?`, "Désactiver", "Annuler")
            .then(_result => {
                this.parameterService.declarerStatutDuBatch(codeSite, false).subscribe(() => {
                    this.alertService.success("Batch désactivé avec succès");
                    this.getListeParametres();
                });
            }, () => null);
    }
}