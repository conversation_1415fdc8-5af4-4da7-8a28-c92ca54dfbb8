import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ListeFacturesComponent } from "./pages/liste-factures/liste-factures.component";
import { ListeReclamationComponent } from "./pages/liste-reclamation/liste-reclamation.component";
import { AuthoritiesGuard, ServiceOptionsGuard } from "@wph/web/shared";
import { DetailFactureComponent } from "./pages/detail-facture/detail-facture.component";
import { ListeGuidesComponent } from "./pages/liste/liste-guides.component";

const routes: Routes = [
    {
        path: '',
        redirectTo: '/commande-web/accueil',
        pathMatch: 'full'
    },
    {
        path: 'factures',
        component: ListeFacturesComponent,
        canActivate: [AuthoritiesGuard, ServiceOptionsGuard],
        data: { 
            serviceOptions: ['FACTURE'],
            authorities: ['ROLE_AGENT_POINT_VENTE'] 
        },
    },
    {
        path: 'factures/:id',
        component: DetailFactureComponent,
        canActivate: [AuthoritiesGuard, ServiceOptionsGuard],
        data: { 
            serviceOptions: ['FACTURE'],
            authorities: ['ROLE_AGENT_POINT_VENTE'] 
        },
    },
    {
        path: 'reclamations',
        component: ListeReclamationComponent,
        canActivate: [AuthoritiesGuard, ServiceOptionsGuard],
        data: { 
            serviceOptions: ['RECLAMATION'],
            authorities: ['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_POINT_VENTE'] 
        },
    },
    {
        path: 'guide',
        component: ListeGuidesComponent,
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
  })
  export class CommandesWebFacturesEtReclamationsRoutingModule {}