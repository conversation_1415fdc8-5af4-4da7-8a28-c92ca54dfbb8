.info-card {
    min-height: 380px;
}

.alt-actu-container {
    max-height: 250px;
    width: calc(100% + 10px);

    .societe-label {
        font-size: clamp(.85rem, 2vw, 1rem);
        font-weight: 500;
        color: black;
    }

    .title {
        font-size: clamp(1.2rem, 2vw, 1.7rem);
        font-weight: 700;
        color: black;
    }

    .readMore {
        font-size: clamp(.85rem, 2vw, 1rem);
        font-weight: 700;
        color: var(--fs-secondary-shade);
    }

    .content {
        font-size: clamp(.85rem, 2vw, 1rem);
        font-weight: 500;
        color: black;
        display: inline-block;

        max-height: 100px;

        overflow: hidden;
        text-overflow: ellipsis;
    }

    .img-actu-alt {
        width: 100%;
        height: auto;
        object-fit: cover;
        object-position: center;
    }
}

.layered-element {
    height: 100%;
    border-radius: var(--winoffre-base-border-radius);
    width: calc(100% - 10px);
    position: relative;
    background-image: var(--fs-actu-default-img);
    background-size: cover;
    object-fit: cover;
    object-position: center;

    .title {
      font-size: clamp(1.2rem, 2vw, 1.7rem);
      font-weight: 700;
      color: #f3f3f3;
    }

    .content {
      font-size: clamp(.85rem, 2vw, 1rem);
      font-weight: 500;
      color: #f3f3f3;
      display: inline-block;

      //max-height: 100px;

      overflow-y: hidden;
      text-overflow: ellipsis;
    }

    .readMore {
      font-size: clamp(.85rem, 2vw, 1rem);
      font-weight: 600;
      color: #f3f3f3;
    }
  }

  .layered-element::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: var(--winoffre-base-border-radius);
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.2));
  }

  .layered-element-content {
    z-index: 10;
    color: #fff;

    .video-container {
      z-index: 25 !important;
    }
  }
