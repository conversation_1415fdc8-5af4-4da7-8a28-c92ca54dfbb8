import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>ni<PERSON> } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { ContactFournisseurGroupe, OffresService, Pagination, SearchContactFournCriteria } from "@wph/data-access";
import { ExportPdf, ExportPdfService, UserInputService } from "@wph/web/shared";
import { FederationSyndicatService } from "../../../services/federation-syndicats.service";
import { GroupeEntreprise } from "../../../models/groupe-entreprise.model";
import { CellClickEvent, GridDataResult } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { Router } from "@angular/router";
import { SocieteType } from "@wph/shared";
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map, Subject, takeUntil } from "rxjs";
import { EntrepriseCriteriaDto, EntrepriseDTO } from "../../../models/entreprise.model";
import { GroupeEntrepriseCriteria } from "../../../models/groupe-entreprise-criteria.model";
import { AuthService } from "@wph/core/auth";

@Component({
    selector: 'wph-liste-contact-fournisseur',
    templateUrl: './liste-contact-fournisseur.component.html',
    styleUrls: ['./liste-contact-fournisseur.component.scss']
})
export class ListeContactFournisseurComponent implements OnInit, OnDestroy {
    unsubscribe$: Subject<boolean> = new Subject<boolean>();
    displayFilter: boolean;
    exportPdfRef: ExportPdf;
    contactFournisseurSort: SortDescriptor[];
    filterFourn: FormControl = new FormControl();
    searchCriteria: SearchContactFournCriteria;

    filterForm: FormGroup;

    contactFournisseurData: GridDataResult = { data: [], total: 0 };
    monGroupe: GroupeEntreprise;
    navigation: Pagination = { pageSize: 20, skip: 0 };

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private authService: AuthService,
        private offreService: OffresService,
        private exportPdfServ: ExportPdfService,
        private userInputService: UserInputService,
        private fedSyndicatService: FederationSyndicatService,
    ) {
        this.initFIlterForm();
    }

    ngOnInit(): void {
        this.buildExport();

        this.fedSyndicatService.getMyGroupe().then(groupe => {
            this.monGroupe = groupe;
            this.searchCriteria = new SearchContactFournCriteria({ groupe: this.monGroupe });
            this.searchContactFournisseurGroupe();
        });

        this.listenToSearchFournisseurChanges();
    }

    searchContactFournisseurGroupe() {
        this.fedSyndicatService.searchContactsFournisseurGroupe(this.navigation, this.searchCriteria).subscribe(res => {
            this.exportPdfRef.setData(res?.content);
            this.contactFournisseurData = { data: res?.content, total: res?.totalElements };
        });
    }

    initFIlterForm(): void {
        this.filterForm = this.fb.group<SearchContactFournCriteria>(new SearchContactFournCriteria());
    }

    onPageChange(_event: any): void {
        this.searchContactFournisseurGroupe();
    }

    listenToSearchFournisseurChanges(): void {
        this.filterFourn.valueChanges
            .pipe(
                debounceTime(400),
                takeUntil(this.unsubscribe$)
            )
            .subscribe(value => {
                if (typeof value === 'object') {
                    const targetFournisseur = value as EntrepriseDTO;
                    this.searchCriteria['fournisseur'] = targetFournisseur;

                    this.searchContactFournisseurGroupe();
                } else if (!value || !value?.length) {
                    this.searchCriteria['fournisseur'] = null;

                    this.searchContactFournisseurGroupe();
                }

            });
    }

    buildExport() {
        this.exportPdfRef = this.exportPdfServ
            .ref<ContactFournisseurGroupe>()
            .setTitle('Liste des Contacts Fournisseur')
            .addColumn('*', 'Code', {
                transform: (value: ContactFournisseurGroupe) => {
                    return value?.fournisseur?.code;
                }
            })
            .addColumn('*', 'Raison Sociale', {
                transform: (value: ContactFournisseurGroupe) => {
                    return value?.fournisseur?.raisonSociale;
                }
            })

        this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']) && this.exportPdfRef.addColumn('*', 'Groupe', {
            transform: (value: ContactFournisseurGroupe) => {
                return value?.groupe?.raisonSociale;
            }
        });

        this.exportPdfRef.addColumn('*', 'Ville / Localité', {
            transform: (value: ContactFournisseurGroupe) => {
                return value?.fournisseur?.ville ?? value?.fournisseur?.localite;
            }
        })
            .addColumn('emailFournisseur', 'E-mail')
            .addColumn('gsmFournisseur', 'Téléphone')
            .addColumn('*', 'Type Entreprise', {
                transform: (value: ContactFournisseurGroupe) => {
                    return value?.fournisseur?.typeEntreprise;
                }
            })

        this.exportPdfRef.setData(this.contactFournisseurData.data);
    }

    contactFournisseurGridSortChange(sort: SortDescriptor[]) {
        this.contactFournisseurSort = sort;
        if (
            this.contactFournisseurSort &&
            this.contactFournisseurSort.length > 0 &&
            this.contactFournisseurSort[0].dir
        ) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }
        this.searchContactFournisseurGroupe();
    }

    onCellClick(clickEvent: CellClickEvent) {
        if ((clickEvent.column?.title as string)?.includes('Actions')) return;

        const fournisseurId = clickEvent?.dataItem?.id;

        this.consulterContactFournisseur(fournisseurId);
    }

    consulterContactFournisseur(contactId: number, readOnly = true) {
        this.router.navigate([`/achats-groupes/gestion-contact-fournisseurs/edit${contactId ? '/' + contactId : ''}`], { queryParams: { readOnly } });
    }

    deleteContact(item: ContactFournisseurGroupe) {
        this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir supprimer ce contact du fournisseur : <b>${item?.fournisseur?.raisonSociale}</b> ?`)
            .then(
                () => {
                    this.fedSyndicatService.deleteContactFournisseurGroupeById(item?.id).subscribe(_res => {
                        this.searchContactFournisseurGroupe();
                    });
                },
                () => null
            );
    }

    filterListe(searchQuery: string) {
        const criteria: EntrepriseCriteriaDto = new EntrepriseCriteriaDto({
            raisonSociale: searchQuery,
            fournisseurHasOffre: true,
            typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE],
        });

        return this.offreService.searchSociete(criteria);
    }

    filterGroupe(searchQuery: string) {
        const criteria: GroupeEntrepriseCriteria = new GroupeEntrepriseCriteria({ raisonSociale: searchQuery, typeEntreprises: [SocieteType.GROUPE_CLIENT] });

        return this.fedSyndicatService.searchGroupeEntreprise({ pageSize: 10, skip: 0 }, criteria);
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterListe(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    fournisseurFormatter = (result: { raisonSociale: any }) =>
        result ? result.raisonSociale : null;


    searchGroupeEntreprise = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term?.length > 1) {
                    return this.filterGroupe(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content)
        );

    groupeEntrepriseFormatter = (result: GroupeEntreprise) => result ? result?.raisonSociale : null;

    appliquerFiltre(): void {
        const payload: SearchContactFournCriteria = this.filterForm.getRawValue();

        this.searchCriteria = new SearchContactFournCriteria({
            ...payload,
            groupe: typeof payload?.groupe === 'object' ? payload?.groupe : null
        });

        this.searchContactFournisseurGroupe();
    }

    vider(): void {
        this.searchCriteria = new SearchContactFournCriteria();
        this.filterForm.reset();

        this.searchContactFournisseurGroupe();
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }
}