import { Component, Inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '@wph/core/auth';
import { faqs } from './data/faq';

@Component({
  selector: 'wph-liste-guides',
  templateUrl: './liste-guides.component.html',
  styleUrls: ['./liste-guides.component.scss'],
})
export class ListeGuidesPharmalienComponent implements OnInit {

  activatedQuestion: number;
  filteredQuestions: any[] = [];  
  questions: any[] = [
    // {
    //   question: "Comment demander un accès client ?",
    //   authorities: ['ROLE_AGENT_FOURNISSEUR'],
    //   id: 1,
    //  },
     {
      question: "Comment utiliser l'IA pour identifier les clients ?",
      authorities: ['ROLE_AGENT_FOURNISSEUR'],
      id: 2,
     },
  ];

  cdnBaseUrl: string;
  private faqs: any[] = faqs;

  constructor(
    private router: Router,   
    private route: ActivatedRoute, 
    private authService: AuthService,
    @Inject('ENVIROMENT') private environment: any
  ) { 
    this.cdnBaseUrl = environment.cdn_base_url;
   }

  
  getCurrentQuestion(): any | undefined {
    return this.faqs.find(faq => faq.id == this.activatedQuestion);
  }


  questionidChanged(questionId: number) {
    this.activatedQuestion = questionId;
    this.router.navigate(['/pharma-lien/autres/guides'], { queryParams: { questionId } });
  }

  removeAnimation() { 
    const target = document.querySelector('.side-nav-item.guide-plateforme');
    if (target && target.classList.contains('animated')) {
      target.classList.remove('animated');
    }
  }

  ngOnInit(): void {
    localStorage.setItem('guideVisited', 'true');
    this.removeAnimation();

    this.route.queryParams.subscribe(params => {
      if (params['questionId']) {
        this.activatedQuestion = +params['questionId'];
      } else {
        this.activatedQuestion = this.questions[0].id; // Default to the first question if none is selected
      }
    });
    this.filteredQuestions = this.questions.filter(q => 
      !q.authorities || this.authService.hasAnyAuthority(q.authorities)
    );       
  }

}
