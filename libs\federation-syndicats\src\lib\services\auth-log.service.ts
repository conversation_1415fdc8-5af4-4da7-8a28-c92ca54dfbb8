import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Page, Pagination } from '@wph/data-access';
import { AuthLogCriteria } from '../models/auth-log.model';

@Injectable({
  providedIn: 'root'
})

export class AuthLogService {

  constructor(@Inject('ENVIROMENT') private env: any,private http: HttpClient) { }


  getPageNumber(skip: number, pageSize: number) {
    return (skip && pageSize) ? Math.floor(skip / pageSize) : 0;
}

  buildNavigationParams(pagination: Pagination) {
    const params = {
        page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
        size: String(pagination.pageSize) ?? 20,
    }

    if (pagination.sortField) {
        params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return params;
}




  searchAuthLog(authLogCriteria: any, navigation: any) {
    const params = this.buildNavigationParams(navigation);
    return this.http.post<Page<any>>(`${this.env.base_url}/api/conlog/searchpage`, authLogCriteria,{ params });
  }

}
