<!-- start page title -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <div class="d-flex align-items-center col-auto k-gap-4">
            <h4 class="page-title fw-4 ps-2">Liste des Contacts Fournisseur</h4>
        </div>

        <div class="col px-1">
            <div class="row justify-content-end align-items-center">
                <button (click)="consulterContactFournisseur(null, false)" type="button"
                    class="btn btn-sm btn-primary m-1" style="padding-block: 6px;">
                    <i class="bi bi-plus-circle-fill"></i>
                    Ajouter Contact
                </button>
            </div>
        </div>
    </div>
</div>
<!-- end page title -->

<div class="row d-flex m-0 px-1">
    <div class="card m-0 w-100 p-0" style="height: calc(100vh - 60px);">
        <div class="card-header py-1 pl-2 bg-white">
            <div class="row p-0">
                <div class="col-12 p-0 d-flex justify-content-end">
                    <div class="row p-0 justify-content-center justify-content-sm-end">
                        <div class="col-sm p-0 m-1">
                            <div class="input-group picker-input">
                                <input type="search" placeholder="Raison sociale" [formControl]="filterFourn"
                                    class="form-control form-control-md pl-4" id="groupeCritere"
                                    [ngbTypeahead]="searchFournisseur" [inputFormatter]="fournisseurFormatter"
                                    [resultFormatter]="fournisseurFormatter" style="border-radius: 10px;" />

                                <div class="picker-icons picker-icons-alt">
                                    <i class="mdi mdi-magnify pointer"></i>
                                </div>
                            </div>
                        </div>

                        <button type="button" *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']"
                            (click)="displayFilter = !displayFilter" class="btn btn-sm search-btn m-1">
                            <span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
                                <i class="bi bi-sliders"></i>
                                <span class="mx-1">Recherche Avancé</span>
                            </span>

                            <ng-template #closeFilter>
                                <span class="d-flex align-items-center">
                                    <i class="mdi mdi-close"></i>
                                    <span class="mx-1">Fermer la recherche</span>
                                </span>
                            </ng-template>
                        </button>

                        <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
                    </div>
                </div>
            </div>

            <form *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" [formGroup]="filterForm" (ngSubmit)="appliquerFiltre()" wphFocusTrap>
                <div id="advanced-search" *ngIf="displayFilter" class="row p-0 flex-wrap px-1 my-2 k-gap-4">
                    <div class="col-auto p-0 m-0">
                        <label for="groupe" class="col-12 p-0 col-form-label text-left">Groupe</label>

                        <div class="col-12 p-0 input-group picker-input">
                            <input type="text" name="groupe" formControlName="groupe" [ngbTypeahead]="searchGroupeEntreprise"
                                [inputFormatter]="groupeEntrepriseFormatter" [resultFormatter]="groupeEntrepriseFormatter"
                                class="form-control form-control-md b-radius bg-white pl-4" id="groupe"  style="border-radius: 10px;">

                            <div class="picker-icons picker-icons-alt">
                                <i class="mdi mdi-magnify pointer"></i>
                            </div>
                        </div>
                    </div>

                    <app-date-picker formControlName="dateDebut" label="Date Début"></app-date-picker>

                    <app-date-picker formControlName="dateFin" label="Date Fin"></app-date-picker>

                    <div class="col-auto p-0 m-0">
                        <label for="emailFournisseur" class="col-12 p-0 col-form-label text-left">Email Fournisseur</label>

                        <div class="col-12 p-0 input-group">
                            <input type="text" name="emailFournisseur" formControlName="emailFournisseur"
                                class="form-control form-control-md b-radius bg-white" id="emailFournisseur">
                        </div>
                    </div>
                    
                    <div class="col-auto p-0 m-0">
                        <label for="gsmFournisseur" class="col-12 p-0 col-form-label text-left">GSM Fournisseur</label>

                        <div class="col-12 p-0 input-group">
                            <input type="text" name="gsmFournisseur" formControlName="gsmFournisseur"
                                class="form-control form-control-md b-radius bg-white" id="gsmFournisseur">
                        </div>
                    </div>

                    <div class="col-auto p-0 m-0">
                        <label for="nomDelegue" class="col-12 p-0 col-form-label text-left">Nom Délégué</label>

                        <div class="col-12 p-0 input-group">
                            <input type="text" name="nomDelegue" formControlName="nomDelegue"
                                class="form-control form-control-md b-radius bg-white" id="nomDelegue">
                        </div>
                    </div>

                    <div class="col-auto p-0 m-0">
                        <label for="prenomDelegue" class="col-12 p-0 col-form-label text-left">Prénom Délégué</label>

                        <div class="col-12 p-0 input-group">
                            <input type="text" name="prenomDelegue" formControlName="prenomDelegue"
                                class="form-control form-control-md b-radius bg-white" id="prenomDelegue">
                        </div>
                    </div>

                    <div class="col col-sm d-flex align-items-end py-1">
                        <button type="button" class="btn btn-sm btn-outline-primary b-radius" (click)="vider()" style="border-radius: 10px;">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <button type="submit" class="btn btn-sm btn-primary b-radius mx-1" style="border-radius: 10px;">
                            <i class="mdi mdi-filter"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="card-body m-0 p-0 bg-white mt-1" style="height:calc(100vh - 125px)">
            <kendo-grid class="fs-grid fs-listing-grid" [data]="contactFournisseurData" [pageSize]="navigation.pageSize"
                [sort]="contactFournisseurSort" (sortChange)="contactFournisseurGridSortChange($event)"
                (cellClick)="onCellClick($event)" [skip]="navigation.skip" [pageable]="true"
                [sortable]="{ mode: 'single' }" style="height: 100%">

                <kendo-grid-column field="fournisseur.code" [width]="60" class="text-wrap">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        {{ dataItem?.fournisseur?.code }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Code</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'numeric'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="fournisseur.raisonSociale" [width]="150" class="text-wrap">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        {{ dataItem?.fournisseur?.raisonSociale }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Raison Sociale</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'alpha'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" field="groupe.raisonSociale" [width]="130" [resizable]="false" class="text-wrap">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        {{ dataItem?.groupe?.raisonSociale}}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Groupe</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'alpha'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="fournisseur.ville" [width]="130" [resizable]="false" class="text-wrap">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        {{ dataItem?.fournisseur?.ville ?? dataItem?.fournisseur?.localite }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Ville / Localité</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'alpha'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="emailFournisseur" title="E-mail" [width]="140" [resizable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <span *ngIf="!dataItem?.emailFournisseur"
                            class="d-flex align-items-center justify-center k-gap-1 text-warning">
                            <i class="bi bi-shield-fill-exclamation"></i>
                            Indisponible
                        </span>
                        {{ dataItem?.emailFournisseur }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'alpha'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="gsmFournisseur" title="Téléphone" [width]="90" [resizable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <span *ngIf="!dataItem?.gsmFournisseur"
                            class="d-flex align-items-center justify-center k-gap-1 text-warning">
                            <i class="bi bi-shield-fill-exclamation"></i>
                            Indisponible
                        </span>
                        {{ dataItem?.gsmFournisseur }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'numeric'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>


                <kendo-grid-column field="fournisseur.typeEntreprise" [width]="100" [resizable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        {{ dataItem?.fournisseur?.typeEntreprise }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Type Entreprise</span>

                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            [type]="'alpha'"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>


                <kendo-grid-column title="Actions" [width]="70" class="no-ellipsis">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="d-flex justify-content-center" style="gap: 8px;">
                            <span (click)="consulterContactFournisseur(dataItem?.id, false)"
                                class="actions-icons btn-success pointer-cus" title="Modifier Contact Fournisseur">
                                <i class="bi bi-pencil-square"></i>
                            </span>

                            <span (click)="deleteContact(dataItem)" class="actions-icons btn-danger pointer-cus"
                                title="Modifier Contact Fournisseur">
                                <i class="bi bi-trash"></i>
                            </span>
                        </div>
                    </ng-template>
                </kendo-grid-column>

                <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
                    let-total="total">
                    <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
                        [navigation]="navigation" style="width: 100%;"
                        (pageChange)="onPageChange($event)"></wph-grid-custom-pager>
                </ng-template>

                <ng-template kendoGridNoRecordsTemplate>
                    <span>Aucun résultat trouvé.</span>
                </ng-template>
            </kendo-grid>
            <ng-template #emptyDate>
                <span>--/--/----</span>
            </ng-template>
        </div>
    </div>
</div>