import { HttpClient, HttpParams } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

import { Observable, of } from "rxjs";
import { catchError } from "rxjs/operators";
import { BatchAdmin } from "../models/batch-admin.model";


@Injectable({
    providedIn: 'root'
})

export class AdminConfigService {

    baseUrl = this.env.base_url;

    constructor(@Inject('ENVIROMENT') private env: any, private httpClient: HttpClient, private modalService: NgbModal) { }

    saveAdminParametresSecurite(password) {
        const parametresSecuriteAdmin = {
            "passcodeImport": password
        }
        return this.httpClient.post<any>(this.baseUrl + '/api/winpharm/config/parameter/admin/securite/edit', parametresSecuriteAdmin);

    }


    listAvailableBatches(): Observable<BatchAdmin[]> {
        return this.httpClient.get<BatchAdmin[]>(this.baseUrl + '/api/batch/list');

    }

    executeBatch(data) {
        return this.httpClient.post<any>(this.baseUrl + '/api/batch/execute', { codeBatch: data.codeBatch })
    }


    checkPasscode(password) {
        const params = new HttpParams().append('password', password);
        return this.httpClient.get<number>(this.baseUrl + '/api/winpharm/import/checkpasscode', { params: params }).pipe(
            catchError((error) => {
                return of(-1);
            })
        );
    }

    setTimeZone(zone: number) {
        return this.httpClient.post<number>(this.baseUrl + '/api/params/timezone/edit', zone)

    }

    fetchTimeZone() {
        return this.httpClient.get<number>(this.baseUrl + '/api/params/timezone/fetch')

    }


    editRateLimitingStatus(rateLimitingStatus: boolean) {
        return this.httpClient.post<boolean>(this.baseUrl + '/api/params/ratelimiterstatus/edit', rateLimitingStatus)


    }


    fetchRateLimitingStatus() {
        return this.httpClient.get<boolean>(this.baseUrl + '/api/params/ratelimiterstatus/fetch')

    }
}


