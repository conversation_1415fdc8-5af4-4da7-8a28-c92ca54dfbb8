import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AuthListenerService {
  private stackOfAsking: any[] = [];

  authQueriesSubject: Subject<any> = new Subject<any>();
  private authResponsesSubject: Subject<any> = new Subject<any>();

  busy = false;

  constructor() {}

  // async askAndWait() {
  //   this.authQueriesSubject.next(0);

  //   console.log('wait for authResponsesSubject');
  //   let ret = await this.authResponsesSubject.toPromise();
  //   console.log('result returned for authResponsesSubject', ret);

  //   return ret;
  // }

  ask() {
    let level = this.stackOfAsking.pop();

    if (!level) {
      level = 0;
    }

    this.authQueriesSubject.next(level);

    return this.authResponsesSubject.asObservable();
  }

  askNextHttpRequest(level: any) {
    this.stackOfAsking.push(level);
  }

  doesNextHttpRequestNeedAskAuth() {
    return this.stackOfAsking.length > 0;
  }

  endWaitingForAuthResponse(result: any) {
    this.authResponsesSubject.next(result);
    this.authResponsesSubject.complete();

    this.authResponsesSubject = new Subject<any>();
  }
}
