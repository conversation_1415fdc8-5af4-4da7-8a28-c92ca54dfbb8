.k-checkbox:indeterminate,
.k-checkbox.k-state-indeterminate,
.k-checkbox.k-indeterminate {
  color: #1bbb9a !important;
}

.k-pager-numbers {
  color: white !important;
  border-color: #1bbb9a !important;
}

.k-pager-numbers .k-link .k-state-selected,
.k-pager-numbers .k-link.k-selected {
  background-color: #bfdfd8ba;
}

.k-pager-numbers .k-link:hover,
.k-pager-numbers .k-link.k-state-hover {
  background-color: #bfdfd8ba;
}

.k-pager-numbers .k-link:focus,
.k-pager-numbers .k-link.k-state-focus {
  box-shadow: inset 0 0 0 2px rgba(107, 255, 88, 0.12);
}

.k-pager-numbers .k-link {
  color: #1bbb9a !important;
}

.k-checkbox:checked {
  background-color: #1bbb9a !important;
  color: white !important;
  border-color: #1bbb9a !important;
}

.k-checkbox:checked:focus {
  box-shadow: 0px 0px 0px 2px rgba(58, 218, 130, 0.6);
}

.no-header-grid {
  &.k-grid div.k-grid-header {
    display: none !important;
  }
}

// GRID HEADER
@media (pointer: fine) and (pointer: fine) {
  .k-grid-header {
    padding: 0px !important;
  }
}

// MOZILA
@-moz-document url-prefix() {
  .k-grid-header {
    padding: 0px !important;
  }
}

//Grid actions button
.circle {
  border-radius: 50%;
  display: inline-block;
  padding: 3px 5px;
  text-align: center !important;
  margin: auto 2px;
  line-height: 20px;
  font-size: 16px;
}

.circle-alt {
  width: 35px !important;
  line-height: 30px !important;
}

.k-grid th {
  font-size: 1rem;
  vertical-align: middle;
  font-weight: bold;
}

//CHANGE SELECTION COLOR
.k-grid td.k-state-selected,
.k-grid tr.k-state-selected>td {
  background-color: lighten(#2d8bcd, 35) !important;
  //color: #424242;
}

.k-grid td.k-selected,
.k-grid tr.k-selected>td {
  background-color: #a2cbe184;
  transition: all .2s cubic-bezier(0.6, 0.41, 0.51, 0.61);
  //color: #00527d;
}

//CHANGE BACKGROUND ROW ON HOVER
.k-grid tbody tr:hover,
.k-grid tbody tr.k-state-hover {
  background-color: lighten(#2d8bcd, 45) !important;
}

.k-grid td {
  border-bottom: 0.6px solid #42424224 !important;
  overflow: hidden; // ANCHOR: visisblity over overlayed content
  padding: 4px 12px !important;
  text-overflow: ellipsis;
  color: black;
  font-size: 1rem;
  font-weight: 600;
}

.k-grid td.select-clmn:first-child {
  overflow: visible;
  text-align: center;
}

#COMMANDE_WEB-container {

  //CHANGE BACKGROUND OF SECOND ROW ELEMENT
  .k-grid tr.k-alt {
    background-color: var(--cw-primary-50) !important;
  }

  .k-grid th {
    color: #fff;
    vertical-align: middle;
    background: var(--cw-primary-800);
  }

  //CHANGE BACKGROUND ROW ON HOVER
  .k-grid tbody tr:hover,
  .k-grid tbody tr.k-state-hover {
    background-color: var(--cw-primary-50) !important;
  }

  //GRID SCROLL BAR
  .k-grid-content {
    scrollbar-width: thin !important;
    //FOR MOZILLA BROWSER
    scrollbar-color: var(--cw-primary-600) white !important;
    overflow-y: auto !important;
    border-radius: var(--winoffre-base-border-radius);
  }

  /* Chromium-based browsers */
  .k-grid-content::-webkit-scrollbar {
    width: 5px;
  }

  .k-grid-content::-webkit-scrollbar-track {
    background: white;
  }

  .k-grid-content::-webkit-scrollbar-thumb {
    background-color: var(--cw-primary-600);
    border-radius: 10px;
    border: 2px solid white;
  }
}

#WIN_OFFRE-container {

  //CHANGE BACKGROUND OF SECOND ROW ELEMENT
  .k-grid tr.k-alt {
    background-color: var(--wo-primary-50);
  }

  .k-grid th {
    color: #fff;
    vertical-align: middle;
    background: var(--wo-primary-500);
  }

  //CHANGE BACKGROUND ROW ON HOVER
  .k-grid tbody tr:hover,
  .k-grid tbody tr.k-state-hover {
    background-color: var(--wo-primary-50) !important;
  }

  //GRID SCROLL BAR
  .k-grid-content {
    scrollbar-width: thin !important;
    //FOR MOZILLA BROWSER
    scrollbar-color: var(--wo-primary-300) white !important;
    overflow-y: auto !important;
    border-radius: var(--winoffre-base-border-radius);
  }

  /* Chromium-based browsers */
  .k-grid-content::-webkit-scrollbar {
    width: 5px;
  }

  .k-grid-content::-webkit-scrollbar-track {
    background: white;
  }

  .k-grid-content::-webkit-scrollbar-thumb {
    background-color: var(--wo-primary-300);
    border-radius: 10px;
    border: 2px solid white;
  }
}

#DEFAULT-container {

  //CHANGE BACKGROUND OF SECOND ROW ELEMENT
  .k-grid tr.k-alt {
    background-color: var(--win-offre-bg-light-2) !important;
  }

  .k-grid th {
    color: #fff;
    background: var(--win-offre-bg-action-shade-1);
    vertical-align: middle;
  }

  .k-grid-header .k-i-sort-asc-sm,
  .k-grid-header .k-i-sort-desc-sm,
  .k-grid-header .k-i-sort-asc-small,
  .k-grid-header .k-i-sort-desc-small,
  .k-grid-header .k-sort-order {
    color: #fff;
  }

  //CHANGE BACKGROUND ROW ON HOVER
  .k-grid tbody tr:hover,
  .k-grid tbody tr.k-state-hover {
    background-color: var(--win-offre-bg-light) !important;
  }

  //GRID SCROLL BAR
  .k-grid-content {
    scrollbar-width: thin !important;
    //FOR MOZILLA BROWSER
    scrollbar-color: var(--win-offre-primary) white !important;
    overflow-y: auto !important;
    border-radius: var(--winoffre-base-border-radius);
  }

  /* Chromium-based browsers */
  .k-grid-content::-webkit-scrollbar {
    width: 5px;
  }

  .k-grid-content::-webkit-scrollbar-track {
    background: white;
  }

  .k-grid-content::-webkit-scrollbar-thumb {
    background-color: var(--win-offre-primary);
    border-radius: 10px;
    border: 2px solid white;
  }

  .k-pager-numbers .k-link .k-state-selected,
  .k-pager-numbers .k-link.k-selected {
    background-color: var(--win-offre-bg-light) !important;
  }

  .k-grid td.k-selected,
  .k-grid tr.k-selected>td {
    background-color: var(--win-offre-bg-light) !important;
    transition: all .2s cubic-bezier(0.6, 0.41, 0.51, 0.61);
  }

  .k-checkbox:checked {
    background-color: var(--win-offre-primary-tint) !important;
    color: white !important;
    border-color: var(--win-offre-primary) !important;
  }
}

//CHANGE BACKGROUND OF SECOND ROW ELEMENT
.k-grid tr.k-alt {
  background-color: lighten(#2d8bcd, 48);
}

//GRID SCROLL BAR
.k-grid-content {
  scrollbar-width: thin !important;
  //FOR MOZILLA BROWSER
  scrollbar-color: #2b89bbe7 white;
  overflow-y: auto !important;
  border-radius: var(--winoffre-base-border-radius);
}

.k-grid {

  *::-webkit-scrollbar,
  *::-webkit-scrollbar-thumb {
    width: 6.5px;
    height: 8.5px;
    border-radius: 5px;
    background-clip: padding-box;
    border: 1px solid transparent;
    background-color: transparent !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
  }

  *::-webkit-scrollbar-corner {
    background-color: unset !important;
  }

  *::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 0 15px #2b89bbe7;
  }
}

.grid-edit-input {
  // margin: -2px -3px -2px -6px !important;
  padding: 0 3px !important;
  outline: none !important;
  min-width: 35px !important;
  width: 98% !important;
  border: 0.5px solid rgba(53, 53, 53, 0.363) !important;
}

// when selected
.k-grid-edit-row {
  td {
    margin: 0 12px !important;
    padding: 0 3px !important;
  }
}

.k-grid tr.diffrence {
  background-color: #f8eaea !important;
}

.k-pager-wrap:focus,
.k-pager-wrap.k-focus,
.k-pager:focus,
.k-pager.k-focus {
  box-shadow: none !important;
}

.k-grid-header .k-grid-header-menu.k-active {
  background-color: #efefef !important;
  border-radius: 5px !important;
}

.k-icon.k-i-filter {
  color: #656565;
}

.k-grid .k-grid-filter {
  color: #fff;
}

.table-details th {
  padding: 5px !important;
  border-color: #eef2f7;
}

.k-grid .k-hierarchy-cell>.k-icon {
  padding: 0px !important;
  font-size: 18px !important;
}

.k-detail-row tr:hover {
  background: none;
}

.k-detail-row tr.k-alt:hover {
  background: none;
}

.k-grid .k-hierarchy-col {
  width: 15px;
}

.k-column-title {
  font-size: 1rem;
}

.k-grid-footer {
  padding: 0 !important;
}

#WIN_OFFRE-container, #FEDERATION_SYNDICAT-container, #WIN_GROUPE-container {
  .k-grid tr {
    cursor: pointer;
  }
}
