import { MenuItem } from '../models/menu.model';

export const MENU: MenuItem[] = [
  {
    targetPlateforme: 'WIN_OFFRE',
    label: 'Accueil',
    icon: ' dripicons-home',
    link: '/win-offre/accueil',
    style: 'test',
    collapsed: true,
    parentKey: null,
  },

  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Accueil',
    icon: ' dripicons-home',
    link: '/commande-web/accueil',
    style: 'test',
    collapsed: true,
    parentKey: null,
  },

  {
    targetPlateforme: 'WIN_OFFRE',
    authorities: ['ROLE_SUPER_ADMIN'],
    label: 'Offre',
    icon: 'uil-server',
    style: '',
    collapsed: true,
    parentKey: null,
    children: [
      {
        label: 'Créer une Offre',
        link: '/win-offre/offres/saisie',
      },
      {
        label: 'Gérer produits',
        link: '/win-offre/offres/produits'
      },
      {
        label: 'Liste des Offres',
        link: '/win-offre/offres/list',
      },
    ],
  },

  {
    targetPlateforme: 'WIN_OFFRE',
    label: 'Liste des Offres',
    icon: 'uil-server',
    style: 'test',
    link: '/win-offre/offres/list',
    parentKey: null,
    authorities: [
      'ROLE_AGENT_POINT_VENTE',
      'ROLE_AGENT_FOURNISSEUR',
      'ROLE_AGENT_COMMERCIAL',
      'ROLE_ASSISTANT'
    ],
  },

  {
    targetPlateforme: 'WIN_OFFRE',
    label: 'Liste des Commandes',
    icon: 'mdi mdi-cart-outline',
    link: '/win-offre/commandes/list',
    style: '',
    parentKey: null
  },

  {
    targetPlateforme: 'WIN_OFFRE',
    label: 'Statistiques',
    icon: 'mdi mdi-chart-areaspline',
    link: '/win-offre/statistiques',
    style: 'test',
    parentKey: null,
    authorities: [
      'ROLE_SUPER_ADMIN',
      'ROLE_AGENT_FOURNISSEUR',
      'ROLE_AGENT_COMMERCIAL',
      'ROLE_AGENT_POINT_VENTE'
    ]
  },
  {
    targetPlateforme: 'WIN_OFFRE',
    label: 'Guide Utilisation',
    icon: 'mdi mdi-information-outline',
    style: 'test',
    link: '/win-offre/guide',
    parentKey: null,
    authorities: [
      'ROLE_AGENT_POINT_VENTE',
      'ROLE_AGENT_FOURNISSEUR',
      'ROLE_AGENT_COMMERCIAL',
      'ROLE_ASSISTANT'
    ],
  },
  {
    targetPlateforme: 'WIN_OFFRE',
    label: 'Changer la plateforme',
    icon: 'mdi mdi-cogs',
    link: '/pharma-lien',
    style: 'change-plateforme',
    collapsed: true,
    parentKey: null,
    excludeWinplusSource: true,
  },

  {
    targetPlateforme: 'DEFAULT',
    label: 'Se Déconnecter',
    icon: 'mdi mdi-power',
    link: '/auth/login',
    style: 'test logout-link',
    collapsed: true,
    parentKey: null,
    replaceLink: true
  },

  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Se Déconnecter',
    icon: 'mdi mdi-power',
    link: '/auth/login',
    style: 'logout-link',
    collapsed: true,
    parentKey: null,
    replaceLink: true
  },
  {
    targetPlateforme: 'WIN_OFFRE',
    label: 'Se Déconnecter',
    icon: 'mdi mdi-power',
    link: '/auth/login',
    style: 'logout-link',
    collapsed: true,
    parentKey: null,
    replaceLink: true
  },


  /********************** Commande web menu items ****************************/
  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Catalogue Produits',
    icon: 'mdi mdi-package-variant-closed',
    link: '/commande-web/catalogue-produit/',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']
  },
  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Nouveaux Produits',
    icon: 'mdi mdi-package-variant',
    link: '/commande-web/list-nouveaux-produits/',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']
  },
  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Alerte Produit',
    icon: 'mdi mdi-bell-outline',
    link: '/commande-web/Alerte-produit/',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_ACHAT', 'ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']
  },
  {
    targetPlateforme: 'COMMANDE_WEB',
    icon: 'mdi mdi-cart-outline',
    style: 'test',
    label: 'Commandes',
    collapsed: false,
    parentKey: null,
    serviceClientOptions: ['PASSER_COMMANDE'],
    authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'],
    children: [
      {
        label: 'Nouvelle Commande',
        link: '/commande-web/add-commande/new',
      },
      {
        label: 'Liste des Commandes',
        link: '/commande-web/list-commandes',
      }
    ]
  },
  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Liste Commandes Normales',
    link: '/commande-web/list/normales',
    icon: ' mdi mdi-cart-outline',
    style: 'test',
    parentKey: null,
    collapsed: true,
    authorities: ['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']
  },
  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Réclamations',
    icon: 'mdi mdi-clipboard-alert-outline',
    link: '/commande-web/reclamation/',
    style: 'test',
    collapsed: true,
    parentKey: null,
    // serviceClientOptions: ['RECLAMATION'],
    authorities: ['ROLE_AGENT_POINT_VENTE'], // , 'ROLE_ASSISTANT'
    badge: {
      variant: 'danger',
      text: '0'
    }

  },

  // c'est pour le ROLE_AGENT_FOURNISSEUR
  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Réclamations',
    icon: 'mdi mdi-clipboard-alert-outline',
    link: '/commande-web/reclamation/fournisseur/',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_FOURNISSEUR'],
    badge: {
      variant: 'danger',
      text: '0'
    }

  },
  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Factures',
    icon: 'mdi mdi-clipboard-text-outline',
    link: '/commande-web/autres/factures',
    style: 'test',
    collapsed: true,
    parentKey: null,
    serviceClientOptions: ['FACTURE'],
    authorities: ['ROLE_AGENT_POINT_VENTE']
  },

  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Guide',
    icon: 'mdi mdi-help-circle',
    link: '/commande-web/autres/guide',
    style: 'guide-link',
    collapsed: true,
    parentKey: null,
  },

  {
    targetPlateforme: 'COMMANDE_WEB',
    label: 'Changer la plateforme',
    icon: 'mdi mdi-cogs',
    link: '/pharma-lien',
    style: 'change-plateforme',
    collapsed: true,
    parentKey: null
  },

  /*************** PHARMAHUB: FOURNISSEUR MENU ITEMS ****************/
  {
    targetPlateforme: 'DEFAULT',
    label: 'Accueil',
    icon: ' dripicons-home',
    link: '/pharma-lien',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_FOURNISSEUR', 'ROLE_SUPER_ADMIN', 'ROLE_AGENT_POINT_VENTE', 'ROLE_AGENT_ACHAT']
  },
  {
    targetPlateforme: 'DEFAULT',
    label: 'Tableau de bord',
    link: '/pharma-lien/dashboard',
    icon: 'bi bi-clipboard-data',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN']
  },
  {
    targetPlateforme: 'DEFAULT',
    label: 'Pharmacies du Maroc',
    link: '/pharma-lien/pharmacies/list',
    icon: 'mdi mdi-pharmacy',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_FOURNISSEUR', 'ROLE_SUPER_ADMIN']
  },
  {
    targetPlateforme: 'DEFAULT',
    label: "Clients à traiter",
    link: '/pharma-lien/gestion-demandes-acces/liste',
    icon: 'bi bi-person-fill-check',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_FOURNISSEUR']
  },
  {
    targetPlateforme: 'DEFAULT',
    label: "Demandes d'accès",
    link: '/pharma-lien/gestion-demandes-acces/demandes/liste',
    icon: 'bi bi-person-lines-fill',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_FOURNISSEUR', 'ROLE_SUPER_ADMIN']
  },
   {
    targetPlateforme: 'DEFAULT',
    label: "Utilisateurs",
    icon: 'bi bi-people-fill',
    style: 'test',
    collapsed: false,
    parentKey: null,
    authorities: ['ROLE_AGENT_FOURNISSEUR'],
    children: [
      {
        label: 'Mes Clients',
        link: '/pharma-lien/gestion-demandes-acces/mes-clients'
      },
      { 
        label: 'Mes Utilisateurs',
        link: '/pharma-lien/users/list' 
      },
    ]
  },
  {
    targetPlateforme: 'DEFAULT',
    label: 'Utilisateurs',
    link: '/pharma-lien/users/list',
    icon: 'mdi mdi-account-multiple',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_POINT_VENTE']
  },
  {
    targetPlateforme: 'DEFAULT',
    label: 'Actualités & Annonces',
    link: '/pharma-lien/gestion-poste',
    icon: 'mdi mdi-newspaper',
    style: 'test',
    collapsed: true,
    parentKey: null,
    excludeTypeSociete: ['FABRIQUANT'],
    authorities: ['ROLE_AGENT_FOURNISSEUR']
  },

  {
    targetPlateforme: 'DEFAULT',
    label: 'Guide',
    icon: 'mdi mdi-help-circle',
    link: '/pharma-lien/autres/guides',
    style: 'guide-plateforme animated',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_FOURNISSEUR']
  },


  /** ADMIN MENU ITEMS */
  {
    targetPlateforme: 'DEFAULT',
    label: 'Utilisateurs',
    link: '/pharma-lien/users/list',
    icon: 'mdi mdi-account-multiple',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },
  {
    targetPlateforme: ['DEFAULT'],
    label: "Journal de connexion ",
    link: 'pharma-lien/auth-log',
    // history icon
    icon: 'bi bi-clock-history',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },
  {
    targetPlateforme: 'DEFAULT',
    label: 'Gérer Accès Client',
    link: '/pharma-lien/gestion-acces',
    icon: 'bi bi-person-fill-gear',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },

  {
    targetPlateforme: 'DEFAULT',
    label: 'Inscriptions',
    link: '/pharma-lien/demandes',
    icon: 'mdi mdi-account-card-details',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },

  {
    targetPlateforme: 'DEFAULT',
    label: 'Google Analytics',
    link: '/pharma-lien/gestion-analytics',
    icon: 'bi bi-graph-up',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },

  {
    targetPlateforme: 'DEFAULT',
    label: 'Actualités & Annonces',
    link: '/pharma-lien/gestion-poste',
    icon: 'mdi mdi-newspaper',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },
  
  {
    targetPlateforme: 'DEFAULT',
    label: 'Batch Admin',
    link: '/pharma-lien/batch-admin/execution-batch',
    icon: 'mdi mdi-flash',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },

  {
    targetPlateforme: 'DEFAULT',
    label: 'Config',
    icon: 'mdi mdi-settings',
    style: 'test',
    collapsed: false,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
    children: [
      {
        label: 'Gérer Domaines',
        link: '/pharma-lien/gestion-domaine/list',
      },
      {
        label: 'Config Service',
        link: '/pharma-lien/services-client/list',
      },
      {
        label: 'Config Catégorie Post',
        link: '/pharma-lien/gestion-poste/categorie',
      },
      {
        label: 'Config Paramètres',
        link: '/pharma-lien/parametres',
      },
      {
        label: 'Config Flux Maj',
        link: '/pharma-lien/flux-maj'
      }
    ]
  },

  /********************** PLATEFORME FEDERATION_SYNDICAT ****************************/
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Accueil',
    link: '/achats-groupes/accueil',
    icon: 'dripicons-home',
    style: 'test',
    collapsed: true,
    parentKey: null
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Gestion des Offres',
    icon: 'mdi mdi-clipboard-check',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'],
    children: [
      {
        label: 'Créer une offre',
        link: '/achats-groupes/offres/saisie',
        icon: 'bi bi-caret-right-fill'
      },
      {
        label: 'Gérer produits',
        link: '/achats-groupes/offres/produits',
        icon: 'bi bi-caret-right-fill',
        exclude: ['ROLE_NATIONAL']
      },
      {
        label: 'Liste des Offres',
        link: '/achats-groupes/offres/liste',
        icon: 'bi bi-caret-right-fill',
      },
    ]
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Gérer Commandes',
    icon: 'mdi mdi-cart',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
    children: [
      {
        label: 'Commandes Groupe',
        link: '/achats-groupes/commandes/liste/groupe/admin',
        icon: 'bi bi-caret-right-fill'
      },
      {
        label: 'Commandes Individuelles',
        link: '/achats-groupes/commandes/liste/individuelle/admin',
        icon: 'bi bi-caret-right-fill',
      },
      {
        label: 'Toutes les commandes',
        link: '/achats-groupes/commandes/liste/commandes/admin',
        icon: 'bi bi-caret-right-fill',
      }
    ]
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Liste des Offres',
    link: '/achats-groupes/offres/liste',
    icon: 'mdi mdi-clipboard-check',
    style: 'test',
    collapsed: true,
    parentKey: null,
    exclude: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN']
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Liste des Commandes',
    link: '/achats-groupes/commandes/liste/laboratoire',
    icon: 'mdi mdi-cart',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_FOURNISSEUR']
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Commandes Groupe',
    icon: 'mdi mdi-cart',
    style: 'test',
    collapsed: true,
    parentKey: null,
    exclude: ['ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR'],
    children: [
      {
        label: 'Commandes Groupe',
        link: '/achats-groupes/commandes/groupe',
        icon: 'bi bi-caret-right-fill'
      },
      {
        label: 'Mes Commandes',
        link: '/achats-groupes/commandes/liste',
        icon: 'bi bi-caret-right-fill'
      },
    ]
  },

  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Gérer Commandes',
    icon: 'bi bi-cart4',
    style: 'test',
    link: '/achats-groupes/commandes/liste/commandes/admin',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_NATIONAL'],
  },

  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Bons de Livraison',
    icon: 'mdi mdi-truck',
    style: 'test',
    collapsed: true,
    parentKey: null,
    exclude: ['ROLE_SUPER_ADMIN'],
    children: [
      {
        label: 'Saisir bon de livraison',
        link: '/achats-groupes/bons-livraison/edit',
        icon: 'bi bi-caret-right-fill',
        isActionLink: true
      },
      {
        label: 'Liste bons de livraison',
        link: '/achats-groupes/bons-livraison/liste',
        icon: 'bi bi-caret-right-fill',
      },
      {
        label: 'Mes bons de sortie',
        link: '/achats-groupes/bons-livraison/mes-bons',
        icon: 'bi bi-caret-right-fill',
      }
    ],
    authorities: ['ROLE_NATIONAL', 'ROLE_RESPONSABLE', "ROLE_AGENT_POINT_VENTE"]
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Mes Statistiques',
    icon: 'mdi mdi-chart-pie',
    style: 'test',
    collapsed: true,
    link: '/achats-groupes/statistiques/labo-statistiques',
    authorities: ['ROLE_AGENT_FOURNISSEUR']
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Statistiques Groupe',
    icon: 'mdi mdi-chart-pie',
    style: 'test',
    collapsed: true,
    parentKey: null,
    children: [
      {
        label: 'Statistiques Groupes',
        link: '/achats-groupes/statistiques/groupes',
        icon: 'bi bi-caret-right-fill',
        authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN']
      },
      {
        label: 'Statistiques Membres',
        link: '/achats-groupes/statistiques/membres',
        icon: 'bi bi-caret-right-fill',
        authorities: ['ROLE_RESPONSABLE']
      },
      {
        label: 'Mes Statistiques',
        link: '/achats-groupes/statistiques/mes-statistiques',
        icon: 'bi bi-caret-right-fill',
        authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_NATIONAL']
      }
    ],
    authorities: ['ROLE_NATIONAL', 'ROLE_RESPONSABLE', "ROLE_AGENT_POINT_VENTE", 'ROLE_SUPER_ADMIN']
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Commandes Individuelles',
    icon: 'bi bi-basket-fill',
    style: 'cmd-individuelle',
    collapsed: true,
    parentKey: null,
    exclude: ['ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR'],
    children: [
      {
        label: 'Mes Commandes',
        link: '/achats-groupes/commandes/liste/individuelle',
        icon: 'bi bi-caret-right-fill'
      },
      {
        label: 'Mes Bons de Livraison',
        link: '/achats-groupes/bons-livraison/liste/individuelle',
        icon: 'bi bi-caret-right-fill'
      },
      {
        label: 'Mes Statistiques',
        link: '/achats-groupes/statistiques/mes-statistiques/individuelle',
        icon: 'bi bi-caret-right-fill'
      },
    ]
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Sondages",
    icon: 'mdi mdi-message-text',
    style: 'test',
    collapsed: true,
    parentKey: null,
    children: [
      {
        label: 'Sondage Membres',
        link: '/achats-groupes/sondages/membres-liste',
        icon: 'bi bi-caret-right-fill',
        authorities: ['ROLE_RESPONSABLE']
      },
      {
        label: 'Sondages groupes',
        link: '/achats-groupes/sondages/liste',
        icon: 'bi bi-caret-right-fill',
        authorities: ['ROLE_NATIONAL']
      },
      {
        label: 'Mes Sondages',
        link: '/achats-groupes/sondages/ma-liste',
        icon: 'bi bi-caret-right-fill',
      }
    ],
    authorities: ['ROLE_NATIONAL', 'ROLE_RESPONSABLE', "ROLE_AGENT_POINT_VENTE"]
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Gestion des Groupes',
    icon: 'mdi mdi-account-multiple',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'],
    children: [
      {
        label: 'Ajouter Groupe',
        link: '/achats-groupes/groupes/saisie',
        icon: 'bi bi-caret-right-fill',
      },
      {
        label: 'Liste des Groupes',
        link: '/achats-groupes/groupes/liste',
        icon: 'bi bi-caret-right-fill',

      },
    ]
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Suggérer Pharmacie",
    link: '/achats-groupes/pharmacies/liste',
    icon: 'mdi mdi-pharmacy',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_NATIONAL', 'ROLE_RESPONSABLE'],
    children: [
      {
        label: ' Ajouter Suggestion',
        link: '/achats-groupes/pharmacies/suggerer',
        icon: 'bi bi-caret-right-fill',
        isActionLink: true
      },
      {
        label: 'Mes suggestions',
        link: '/achats-groupes/pharmacies/liste',
        icon: 'bi bi-caret-right-fill',
      },
    ]
  },

  {
    targetPlateforme: ['WIN_GROUPE'],
    label: "Contact Fournisseur",
    link: '/achats-groupes/pharmacies/liste',
    icon: 'bi bi-building-fill',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_RESPONSABLE', 'ROLE_SUPER_ADMIN'],
    children: [
      {
        label: ' Ajouter Contact',
        link: '/achats-groupes/gestion-contact-fournisseurs/edit',
        icon: 'bi bi-caret-right-fill',
        isActionLink: true
      },
      {
        label: 'Liste Contact Fournisseur',
        link: '/achats-groupes/gestion-contact-fournisseurs/liste',
        icon: 'bi bi-caret-right-fill',
      },
    ]
  },

  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Pharmacies Maroc",
    link: '/achats-groupes/pharmacie-maroc/liste',
    icon: 'bi bi-shop',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
    children: [
      {
        label: 'Ajouter Pharmacie',
        link: '/achats-groupes/pharmacie-maroc/suggerer',
        icon: 'bi bi-caret-right-fill',
        isActionLink: true
      },
      {
        label: 'Liste Pharmacies Maroc',
        link: '/achats-groupes/pharmacie-maroc/liste',
        icon: 'bi bi-caret-right-fill',
      }
    ]
  },

  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: 'Gérer Sondages',
    icon: 'mdi mdi-message-text',
    style: 'test',
    collapsed: true,
    link: '/achats-groupes/sondages/liste',
    authorities: ['ROLE_SUPER_ADMIN']
  },


  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Gérer Fournisseurs",
    link: '/achats-groupes/gestion-fournisseurs/liste',
    icon: 'bi bi-building-fill',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Gérer Suggestions",
    link: '/achats-groupes/pharmacies/liste',
    icon: 'mdi mdi-pharmacy',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Gérer Domaines",
    link: 'achats-groupes/domaine/list',
    icon: 'bi bi-shield-fill-check',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },
  
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Gérer Paramètres",
    link: 'achats-groupes/config-parametre',
    icon: 'bi bi-gear-wide-connected',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Journal de connexion ",
    link: 'achats-groupes/auth-log',
    // history icon
    icon: 'bi bi-clock-history',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_SUPER_ADMIN'],
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Gérer Actualités",
    link: 'achats-groupes/actualites/gestion-actu',
    icon: 'bi bi-newspaper',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'],
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT', 'WIN_GROUPE'],
    label: "Mon Groupe",
    link: '/achats-groupes/groupes/membres',
    icon: 'mdi mdi-account-multiple',
    style: 'test',
    collapsed: true,
    parentKey: null,
    authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_NATIONAL']
  },
  {
    targetPlateforme: ['FEDERATION_SYNDICAT'],
    label: "Guide",
    link: '/achats-groupes/guides/liste',
    icon: 'mdi mdi-help-circle',
    style: 'test',
    collapsed: true,
    parentKey: null,
    exclude: ['ROLE_AGENT_FOURNISSEUR']
  },
];
