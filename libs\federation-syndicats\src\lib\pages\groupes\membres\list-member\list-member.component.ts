import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, TemplateRef, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CellClickEvent, GridComponent, GridDataResult, PageChangeEvent, RowClassArgs, SelectableSettings, SelectionEvent } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { Pagination } from "@wph/data-access";
import { FederationSyndicatService, PharmacieEntreprise } from "@wph/federation-syndicats";
import { AlertService, ICity, SocieteType, StaticDataService } from "@wph/shared";
import { GroupeEntreprise } from '../../../../models/groupe-entreprise.model';
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map, iif, Subject, takeUntil } from "rxjs";
import { FormB<PERSON>er, FormControl, FormGroup } from "@angular/forms";
import { PharmacieEntrepriseCriteria } from "libs/federation-syndicats/src/lib/models/pharmacie-entreprise-criteria.model";
import { ExportPdf, ExportPdfService, UserInputService } from "@wph/web/shared";
import { AuthService } from "@wph/core/auth";
import { DatePipe } from "@angular/common";
import { FsStatistiqueService } from 'libs/federation-syndicats/src/lib/services/fs-statistique.service';
import { StatisticsCriteriaDTO } from 'libs/federation-syndicats/src/lib/models/statistique.model';

@Component({
  selector: 'app-list-member',
  templateUrl: './list-member.component.html',
  styleUrls: ['./list-member.component.scss']
})
export class ListMemberComponent implements OnInit, OnDestroy {
  membreSort: SortDescriptor[];
  pageSizes: number[] = [5, 10, 15, 20];
  selectedKeys: number[] = [];
  navigation: Pagination = { pageSize: 20, skip: 0 };
  gridData: GridDataResult = { total: 0, data: [] };

  @ViewChild('grid') grid: GridComponent;

  modeEnvoiIdentifiants: string;
  targetPharmacie: PharmacieEntreprise;

  initialGridData: PharmacieEntreprise[];

  criteria: PharmacieEntrepriseCriteria;

  unsubscribe$: Subject<boolean> = new Subject<boolean>();

  filterSearch: FormControl = new FormControl();
  filterForm: FormGroup;
  displayFilter: boolean;
  gridSelectable: boolean | SelectableSettings = { mode: 'multiple', checkboxOnly: true };

  villes: ICity[] = [];
  stautsLabelsValues: any[] = [
    { label: 'Tout', value: null },
    { label: 'Actif', value: true },
    { label: 'Inactif', value: false }
  ];
  exportPdfRef: ExportPdf;

  managedGroupe: GroupeEntreprise | null = null;

  constructor(
    private router: Router,
    private fb: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private alertService: AlertService,
    private authService: AuthService,
    private exportPdfServ: ExportPdfService,
    private userInputService: UserInputService,
    private staticDataService: StaticDataService,
    private fsStatistiqueService: FsStatistiqueService,
    private federationSyndicatService: FederationSyndicatService,
  ) {
    this.initFilterForm();

    this.federationSyndicatService.inactiveAccount$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(res => {
        if (res === true) {
          this.gridSelectable = false;
        }
      });
  }

  get isInactive$() {
    return this.federationSyndicatService.inactiveAccount$;
  }

  OnPageChange(event: number): void {
    this.fetchMembersOfCurrentGroupe()

  }

  ngOnInit(): void {
    this.buildExport();

    this.federationSyndicatService.getMyGroupe().then(myGroupe => {
      this.managedGroupe = myGroupe;

      this.criteria = new PharmacieEntrepriseCriteria({ groupeEntrepriseDTO: this.managedGroupe, typeEntreprises: [SocieteType.CLIENT] });

      if (this.managedGroupe) {
        this.fetchMembersOfCurrentGroupe();
      }
    });

    this.getListeVilles();
    this.listenToFilterSearchChanges();
  }

  initFilterForm(): void {
    this.filterForm = this.fb.group({
      raisonSociale: new FormControl(null),
      ville: new FormControl(null),
      localite: new FormControl(null),
      nomResponsable: new FormControl(null),
      statutMembreEntreprise: new FormControl(null),
    });
  }

  buildExport(): void {
    this.exportPdfRef = this.exportPdfServ
      .ref<PharmacieEntreprise>()
      .setTitle('Liste des Membres')
      .addColumn('nomResponsable', 'Nom Complet', {
        width: 110, transform: (value) => {
          return `Dr. ${value}`;
        }
      })
      .addColumn('raisonSociale', 'Pharmacie', { width: 110 })
      .addColumn('gsm1', 'GSM', {
        width: 100, transform: (value) => {
          return value || 'GSM indisponible';
        }
      })
      .addColumn('telephone', 'Téléphone', {
        width: 100, transform: (value) => {
          return value || 'Tél indisponible';
        }
      })
      .addColumn('email', 'Email', {
        width: 100, transform: (value) => {
          return value || 'Email indisponible';
        }
      })
      .addColumn('*', 'Ville / Localité', {
        width: 100, transform: (value) => {
          return value?.ville || value?.localite;
        }
      })
      .addColumn('dateAttachementGroupe', "Date d'Adhésion", {
        width: 100, transform: (value) => {
          return this.datePipe.transform(value, 'dd/MM/yyyy HH:mm')
        }
      })
      .addColumn('*', 'Rôle', {
        width: 60, transform: (value) => {
          return (value?.id === this.managedGroupe?.responsablesGroupe[0]?.id) ? 'Chef' : 'Membre';
        }
      })
      .addColumn('statutMembreGroupe', 'Statut', {
        width: 60, transform: (value) => {
          return value ? 'Actif' : 'Inactif';
        }
      })
      .setData([]);
  }



  fetchMembersOfCurrentGroupe(): void {
    this.federationSyndicatService.searchPharmacieEntreprise(this.navigation, this.criteria).subscribe(res => {
      const members = res.content?.filter(mem => mem?.id !== this.managedGroupe?.responsablesGroupe[0]?.id);

      !this.navigation.skip && members.unshift(this.managedGroupe?.responsablesGroupe[0]);

      this.gridData = {
        data: members,
        total: res.totalElements
      };

      this.exportPdfRef.setData(this.gridData.data);
      this.exportPdfRef.setHeaderInfo([{
        title: 'Nom du groupe',
        value: this.managedGroupe?.raisonSociale,
      },
      {
        title: 'Nombre de membres',
        value: this.managedGroupe?.nbrMembres.toString(),
      },
      {
        title: "Ville",
        value: `${this.managedGroupe?.ville} ${this.managedGroupe?.localite !== null ? `, ${this.managedGroupe?.localite}` : ''}`,
      }
      ]);
    });
  }

  listenToFilterSearchChanges(): void {
    this.filterSearch.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(200),
        distinctUntilChanged()
      )
      .subscribe((term: string) => {
        this.navigation.skip = 0;
        this.criteria = new PharmacieEntrepriseCriteria({ nomResponsable: term, groupeEntrepriseDTO: this.managedGroupe, typeEntreprises: [SocieteType.CLIENT] });

        this.fetchMembersOfCurrentGroupe();
      });
  }

  rowClass(row: RowClassArgs): string {
    return (row.index === 0) ? 'k-disabled' : '';
  }

  pageChange(event: PageChangeEvent): void {
    if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
      this.navigation.skip = event.skip;
      this.navigation.pageSize = event.take;

      this.fetchMembersOfCurrentGroupe();
    }
  }


  selectionChange(event: SelectionEvent): void {
    // if (this.authService.hasAnyAuthority(['ROLE_RESPONSABLE'])) {
    // Filter out rows that should not be selectable
    const filteredSelectedRows = event.selectedRows.filter(row => (row?.index > 0 && (true)));
    const filteredDeselectedRows = event.deselectedRows.filter(row => (row?.index > 0 && (true)));


    // Update selected keys by adding non-owner selected rows
    filteredSelectedRows.forEach(row => {
      if (!row.dataItem.groupeEntreprise) {
        return;
      }
      if (!this.selectedKeys.includes(row.dataItem.id)) {
        this.selectedKeys.push(row.dataItem.id);
      }
    });

    // Update selected keys by removing non-owner deselected rows
    filteredDeselectedRows.forEach(row => {
      if (!row.dataItem.groupeEntreprise) {
        return;
      }
      const index = this.selectedKeys.indexOf(row.dataItem.id);
      if (index >= 0) {
        this.selectedKeys.splice(index, 1);
      }
    });

    this.unselectCell(0, 0);
  }

  onCellClick(event: CellClickEvent): void {
    if (event.columnIndex === 0 || event?.column?.title === 'Rôle' || event?.column?.title === 'Statut' || event?.column?.title === 'Actions') {
      return;
    }

    const clickedItem = event.dataItem;
    if (!this.selectedKeys.includes(clickedItem.id)) {

      this.selectedKeys = [...this.selectedKeys, clickedItem.id];
    } else {
      const targetIndex = this.selectedKeys?.indexOf(clickedItem?.id);

      if (targetIndex > -1) {
        this.selectedKeys?.splice(targetIndex, 1);

        this.selectedKeys = [...this.selectedKeys];
      }
    }
  }

  unselectCell(rowIndex: number, columnIndex: number) {
    const cell = this.grid.wrapper.nativeElement.querySelector(
      `tr[data-kendo-grid-item-index="${rowIndex}"] td:nth-child(${columnIndex + 1})`
    );

    if (cell) {
      cell.classList.remove('k-state-selected');
      cell.setAttribute('aria-selected', 'false');
      (cell.querySelector('input') as HTMLInputElement)?.remove();

    }
  }

  envoyerMultipleIdentifiants(toWhatsapp = false): void {
    const selectedPharmacies = this.gridData?.data?.filter((pharmacie: PharmacieEntreprise) => this.selectedKeys?.includes(pharmacie?.id));
    const hasMissingCredentials = selectedPharmacies.filter((pharm: PharmacieEntreprise) => (!pharm?.email && !pharm?.gsm1) || (toWhatsapp && pharm?.whatsapp))?.length > 0;

    if (hasMissingCredentials) {
      this.alertService.error(`Vous avez sélectionné des pharmacies avec des identifiants manquants. Veuillez choisir des pharmacies disposant ${toWhatsapp ? "d'un numéro de <b>Whatsapp</b> valide." : "d'un numéro de <b>téléphone</b> ou d'une <b>adresse e-mail</b> valide."}`, 'MODAL');
    } else {
      this.federationSyndicatService.envoyerIdentifiantsParMail(this.selectedKeys).subscribe(res => {
        this.alertService.mailSent(`Les identifiants ont été envoyés aux pharmacies sélectionnées avec succès.`, 'Identifiants Envoyés');
      });
    }

  }


  promoteMemberToChef(member: PharmacieEntreprise): void {
    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir assigner le rôle responsable du groupe à la pharmacie <b>${member?.raisonSociale}</b> ?`).then(
      () => {
        // ? Seconde factor confirmation
        this.userInputService.confirmAlt(
          'Confirmation',
          `En confirmant cette action, vous transférerez tous les privilèges actuels de l’application à la pharmacie <b>${member?.raisonSociale}</b>. Veuillez vérifier et confirmer votre choix.`
        ).then(
          () => {
            this.federationSyndicatService.promoteMembreDuGroupeEntreprise(this.managedGroupe?.id, member?.id).subscribe(res => {
              this.authService.logout();
              this.router.navigateByUrl('/auth/login');
            });
          },
          () => null
        );
      },
      () => null
    );
  }

  promoteMember(member: PharmacieEntreprise) {
    if (this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']) && !this.isInactive$.value) {
      this.promoteMemberToChef(member);
    }
  }

  sortChange(sort: SortDescriptor[]): void {
    this.membreSort = sort;

    if (this.membreSort && this.membreSort.length > 0 && this.membreSort[0].dir) {
      this.navigation.sortField = sort[0].field;
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
    }

    this.fetchMembersOfCurrentGroupe();
  }

  sendMailToUser(item: PharmacieEntreprise) {
    if (!item?.gsm1 && !item?.email) {
      this.alertService.identifiantsManquants("Les données de l'utilisateur sélectionné sont incomplètes.", 'Données Indisponible', item?.id);
    } else {
      this.federationSyndicatService.envoyerIdentifiantsParMail([item.id]).subscribe(res => {
        this.alertService.mailSent(`Un message avec login et mot de passe est envoyer à la pharmacie <b>${item?.raisonSociale}</b>`, 'Identifiants Envoyés');
      });
    }
  }

  sendWhatsappToUser(item: PharmacieEntreprise) {
    if (!item?.whatsapp) {
      this.alertService.identifiantsManquants("Le numéro WhatsApp de l'utilisateur sélectionné est inexistant.", 'Données Indisponible', item?.id);
    } else {
      this.federationSyndicatService.envoyerIdentifiantsParWhatsapp([item.id]).subscribe(res => {
        this.alertService.mailSent(`Un message avec login et mot de passe est envoyer à la pharmacie <b>${item?.raisonSociale}</b>`, 'Identifiants Envoyés');
      });
    }
  }

  suggererPharmacie(id?: number): void {
    this.router.navigate([`/achats-groupes/pharmacies/edit/${id}`], { queryParams: { suggerer: true, from: 'mon-gr' } });
  }

  ajouterMembre(): void {
    this.router.navigate(['..', 'membres-saisie'], { relativeTo: this.route });
  }

  activerOuDesactiverMembre(membre: PharmacieEntreprise): void {
    if (this.authService.hasAnyAuthority(['ROLE_RESPONSABLE'])) {
      const action = membre?.statutMembreGroupe ? 'désactiver' : 'activer';

      this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir ${action} la pharmacie: <b>${membre?.raisonSociale ?? ''}</b> ?`).then(
        () => {
          iif(
            () => membre?.statutMembreGroupe,
            this.federationSyndicatService.desactiverMembreGroupeEntreprise(this.managedGroupe?.id, membre?.id),
            this.federationSyndicatService.activerMembreGroupeEntreprise(this.managedGroupe?.id, membre?.id)
          )
            .subscribe(_res => {
              this.fetchMembersOfCurrentGroupe();
              this.alertService.successAlt(`La pharmacie <b>${membre?.raisonSociale}</b> a été ${membre?.statutMembreGroupe ? 'désactivée' : 'activée'} avec succès!`, `${membre?.statutMembreGroupe ? 'Membre Désactivé' : 'Membre Activé'}`, 'MODAL');
            })
        },
        () => null
      );
    }
  }

  openModal(content: TemplateRef<any>, item?: PharmacieEntreprise, size = 'md') {
    this.modeEnvoiIdentifiants = null, this.targetPharmacie = item;
    this.modalService.open(content, { size, centered: true }).result.then(
      result => {        
       this.targetPharmacie = null;
      },
      reason => {        
        this.targetPharmacie = null;
      }
    );
  }

  sendCredentialsGeneric() {
    if (this.modeEnvoiIdentifiants === 'whatsapp') {
      this.targetPharmacie ? this.sendWhatsappToUser(this.targetPharmacie) : this.envoyerMultipleIdentifiants(true);
    } else {
      this.targetPharmacie ? this.sendMailToUser(this.targetPharmacie) : this.envoyerMultipleIdentifiants();
    }
  }

  attacherMembre(membre: PharmacieEntreprise): void {
    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir attacher la pharmacie: <b>${membre?.raisonSociale}</b> comme membre du groupe ?`).then(
      () => {
        this.modalService.dismissAll();

        this.federationSyndicatService.ajouterMembreAuGroupeEntreprise(this.managedGroupe?.id, membre?.id).subscribe(res => {
          this.alertService.successAlt(`La pharmacie: <b>${membre?.raisonSociale}</b> a été attachée comme membre du groupe avec succès.`, 'Membre Attaché', 'MODAL');
        });
      },
      () => null
    );
  }

  detacherMembre(membre: PharmacieEntreprise): void {
    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir détacher la pharmacie <b>${membre?.raisonSociale}</b> du groupe ?`).then(
      () => {

        const statsCriteria = new StatisticsCriteriaDTO({ idMembre: membre?.id, idGroupe: this.managedGroupe?.id });

        this.fsStatistiqueService.getStatictiques(statsCriteria).subscribe(res => {
          if (res?.length) {
            const statsMembre = res[0];

            if (statsMembre?.balance === 0) {
              this.federationSyndicatService.retirerMembreDuGroupeEntreprise(membre?.id, this.managedGroupe?.id).subscribe(res => {
                this.fetchMembersOfCurrentGroupe();
                this.alertService.successAlt(`La pharmacie <b>${membre?.raisonSociale}</b> a été détachée du groupe avec succès.`, 'Membre Detaché', 'MODAL');
              });
            } else {
              this.alertService.error(`Impossible de détacher le pharmacien <b>Dr. ${membre?.nomResponsable}</b> car sa balance n'est pas soldée.`, 'MODAL');
            }
          }
        });

      },
      () => null
    );
  }

  appliquerFiltre(event: Event): void {
    event.preventDefault();
    if (this.filterForm.valid) {
      const formValue = this.filterForm.getRawValue();

      // Clean up form values by removing null or undefined values
      const cleanFormValue = Object.fromEntries(
        Object.entries(formValue).filter(([key, v]) => v !== null && v !== undefined)
      ) as { [key: string]: any };

      // Check if 'ville' is an object, and extract 'labelFr'; otherwise, use it as is
      const villeValue = cleanFormValue['ville'] && typeof cleanFormValue['ville'] === 'object'
        ? cleanFormValue['ville'].labelFr
        : cleanFormValue['ville'];
      // Update the search criteria
      this.criteria = new PharmacieEntrepriseCriteria({
        ...this.criteria,
        nomResponsable: cleanFormValue['nomResponsable'] || null,
        raisonSociale: cleanFormValue['raisonSociale'] || null,
        localite: cleanFormValue['localite'] ? [cleanFormValue['localite']] : null,
        ville: villeValue || null,
        statutMembreEntreprise: cleanFormValue['statutMembreEntreprise'],
      });

      // Fetch members based on the updated criteria
      this.fetchMembersOfCurrentGroupe();
    }
  }

  viderFiltre(): void {
    this.filterForm.reset();

    this.navigation.skip = 0;
    this.criteria = new PharmacieEntrepriseCriteria({ groupeEntrepriseDTO: this.managedGroupe, typeEntreprises: [SocieteType.CLIENT] });

    this.fetchMembersOfCurrentGroupe();
  }

  getListeVilles(): void {
    this.staticDataService.getListCitiesByCountryId(1)
      .subscribe(res => {
        this.villes = res;
      })
  }

  searchVilleOuLocalite = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap(term => {
        return term?.length < 1 ? of([]) :
          of(this.villes?.filter(ville => ville?.labelFr?.toLowerCase()?.includes(term?.toLowerCase())));
      }),
      map(res => res?.slice(0, 10))
    );

  villeFormatter = (result: ICity) => result?.labelFr;

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

}
