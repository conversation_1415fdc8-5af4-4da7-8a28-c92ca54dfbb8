import { Commande, Produit } from "@wph/data-access";
import { EntrepriseDTO } from "./entreprise.model";
import { GroupeEntreprise } from "./groupe-entreprise.model";
import { PharmacieEntreprise } from "./pharmacie-entreprise.model";
import {  IEnteteCommandeConsolideeMarcheDTO } from "./entete-commande.model";

export type EtatBlAchatGroupe = 'BROUILLON'| 'VALIDE'| 'ANNULE' | 'REPARTI';

export enum EtatBlAchatGroupeEnum {
    BROUILLON = 'BROUILLON',
    VALIDE = 'VALIDE',
    ANNULE = 'ANNULE',
    REPARTI = 'REPARTI',
}



interface IEnteteBlAchatGroupeDTO {
  id: number;
  fournisseur: EntrepriseDTO;
  transporteur: EntrepriseDTO;
  enteteCommandeAchatGroupe: IEnteteCommandeConsolideeMarcheDTO;
  montantCalcule: number;
  tauxRf: number;
  quantiteUg: number;
  montantSaisi: number;
  cumulBl: number;
  axeAnalytique: string;
  depot: string;
  typeCommande: string;
  dateCreation: string;
  dateReceptionBl: string;
  dateCommande: string;
  codeCommande: string;
  numeroBl: string;
  lignes: IDetailBlAchatGroupeDTO[];
  etatBl: EtatBlAchatGroupe;
  qteTotaleLivree: number;
  raisonSocialeTransporteur: string;
  montantCalculeOriginal: number;
  qteTotaleLivreeOriginal: number;
}


interface IDetailBlAchatGroupeDTO {
  id: number;
  enteteBl: IEnteteBlAchatGroupeDTO;
  produit: Produit;
  codeBarre: string;
  codeProduitGroupe: string;
  ppv: number;
  pph: number;
  tva: number;
  montant: number;
  designation: string;
  quantiteCommandee: number;
  quantiteLivree: number;
  tauxRf: number;
  quantiteUg: number;
  quantiteUgCmd: number;
  blocOffreId: number;
  datePeremption: Date;
  pphRemise: number;
  isCadeau: boolean;
  qteFixePrdInCoffret: number;
  qteReliquatBl: number;
  oldPphRemise: number;
  oldTauxRf: number;
  isExtraLine: boolean;
  montantOriginal: number;
  quantiteLivreeOriginal: number;
}

interface IEnteteBlUnitaireMarcheDTO extends IEnteteBlAchatGroupeDTO {
  groupeEntreprise: GroupeEntreprise;
  responsableGroupe: PharmacieEntreprise;
  enteteBlConsolideeMarche: IEnteteBlConsolideeMarcheDTO;
}


interface IEnteteBlConsolideeMarcheDTO extends IEnteteBlAchatGroupeDTO {
  groupeEntreprise: GroupeEntreprise;
  responsableGroupe: PharmacieEntreprise;
}


//  Implementations

export class EnteteBlAchatGroupeDTO implements IEnteteBlAchatGroupeDTO {
  id: number;
  fournisseur: EntrepriseDTO;
  transporteur: EntrepriseDTO;
  enteteCommandeAchatGroupe: IEnteteCommandeConsolideeMarcheDTO;
  montantCalcule: number;
  tauxRf: number;
  quantiteUg: number;
  montantSaisi: number;
  cumulBl: number;
  axeAnalytique: string;
  depot: string;
  typeCommande: string;
  dateCreation: string;
  dateReceptionBl: string;
  dateCommande: string;
  codeCommande: string;
  numeroBl: string;
  lignes: IDetailBlAchatGroupeDTO[];
  etatBl: EtatBlAchatGroupe;
  qteTotaleLivree: number;
  raisonSocialeTransporteur: string;
  montantCalculeOriginal: number;
  qteTotaleLivreeOriginal: number;

  constructor(partialEnteteBlAchatGroupeDTO: Partial<IEnteteBlAchatGroupeDTO>) {
    this.id = partialEnteteBlAchatGroupeDTO?.id ?? undefined;
    this.fournisseur = partialEnteteBlAchatGroupeDTO?.fournisseur ?? undefined;
    this.enteteCommandeAchatGroupe = partialEnteteBlAchatGroupeDTO?.enteteCommandeAchatGroupe ?? undefined;
    this.montantCalcule = partialEnteteBlAchatGroupeDTO?.montantCalcule ?? undefined;
    this.tauxRf = partialEnteteBlAchatGroupeDTO?.tauxRf ?? undefined;
    this.quantiteUg = partialEnteteBlAchatGroupeDTO?.quantiteUg ?? undefined;
    this.montantSaisi = partialEnteteBlAchatGroupeDTO?.montantSaisi ?? undefined;
    this.cumulBl = partialEnteteBlAchatGroupeDTO?.cumulBl ?? undefined;
    this.axeAnalytique = partialEnteteBlAchatGroupeDTO?.axeAnalytique ?? undefined;
    this.depot = partialEnteteBlAchatGroupeDTO?.depot ?? undefined;
    this.typeCommande = partialEnteteBlAchatGroupeDTO?.typeCommande ?? undefined;
    this.dateCreation = partialEnteteBlAchatGroupeDTO?.dateCreation ?? undefined;
    this.dateReceptionBl = partialEnteteBlAchatGroupeDTO?.dateReceptionBl ?? undefined;
    this.dateCommande = partialEnteteBlAchatGroupeDTO?.dateCommande ?? undefined;
    this.codeCommande = partialEnteteBlAchatGroupeDTO?.codeCommande ?? undefined;
    this.numeroBl = partialEnteteBlAchatGroupeDTO?.numeroBl ?? undefined;
    this.lignes = partialEnteteBlAchatGroupeDTO?.lignes ?? undefined;
    this.etatBl = partialEnteteBlAchatGroupeDTO?.etatBl ?? undefined;
    this.transporteur = partialEnteteBlAchatGroupeDTO?.transporteur ?? undefined;
    this.qteTotaleLivree = partialEnteteBlAchatGroupeDTO?.qteTotaleLivree ?? undefined;
    this.raisonSocialeTransporteur = partialEnteteBlAchatGroupeDTO?.raisonSocialeTransporteur ?? undefined;
    this.montantCalculeOriginal = partialEnteteBlAchatGroupeDTO?.montantCalculeOriginal ?? undefined;
    this.qteTotaleLivreeOriginal = partialEnteteBlAchatGroupeDTO?.qteTotaleLivreeOriginal ?? undefined;
  }
}


export class DetailBlAchatGroupeDTO implements IDetailBlAchatGroupeDTO {
  id: number;
  enteteBl: IEnteteBlAchatGroupeDTO;
  produit: Produit;
  codeBarre: string;
  codeProduitGroupe: string;
  ppv: number;
  pph: number;
  tva: number;
  montant: number;
  designation: string;
  quantiteCommandee: number;
  quantiteLivree: number;
  tauxRf: number;
  quantiteUg: number;
  quantiteUgCmd: number;
  datePeremption: Date;
  blocOffreId: number;
  pphRemise: number;
  isCadeau: boolean;
  qteFixePrdInCoffret: number;
  qteReliquatBl: number;
  oldPphRemise: number;
  oldTauxRf: number;
  isExtraLine: boolean;
  montantOriginal: number;
  quantiteLivreeOriginal: number;

  constructor(partialDetailBlAchatGroupeDTO: Partial<IDetailBlAchatGroupeDTO>) {
    this.id = partialDetailBlAchatGroupeDTO?.id ?? undefined;
    this.enteteBl = partialDetailBlAchatGroupeDTO?.enteteBl ?? undefined;
    this.produit = partialDetailBlAchatGroupeDTO?.produit ?? undefined;
    this.codeBarre = partialDetailBlAchatGroupeDTO?.codeBarre ?? undefined;
    this.codeProduitGroupe = partialDetailBlAchatGroupeDTO?.codeProduitGroupe ?? undefined;
    this.ppv = partialDetailBlAchatGroupeDTO?.ppv ?? undefined;
    this.pph = partialDetailBlAchatGroupeDTO?.pph ?? undefined;
    this.tva = partialDetailBlAchatGroupeDTO?.tva ?? undefined;
    this.montant = partialDetailBlAchatGroupeDTO?.montant ?? undefined;
    this.designation = partialDetailBlAchatGroupeDTO?.designation ?? undefined;
    this.quantiteCommandee = partialDetailBlAchatGroupeDTO?.quantiteCommandee ?? undefined;
    this.quantiteLivree = partialDetailBlAchatGroupeDTO?.quantiteLivree ?? undefined;
    this.tauxRf = partialDetailBlAchatGroupeDTO?.tauxRf ?? undefined;
    this.quantiteUg = partialDetailBlAchatGroupeDTO?.quantiteUg ?? undefined;
    this.datePeremption = partialDetailBlAchatGroupeDTO?.datePeremption ?? undefined;
    this.quantiteUgCmd = partialDetailBlAchatGroupeDTO?.quantiteUgCmd ?? undefined;
    this.blocOffreId = partialDetailBlAchatGroupeDTO?.blocOffreId ?? undefined;
    this.pphRemise = partialDetailBlAchatGroupeDTO?.pphRemise ?? undefined;
    this.isCadeau = partialDetailBlAchatGroupeDTO?.isCadeau ?? undefined;
    this.qteFixePrdInCoffret = partialDetailBlAchatGroupeDTO?.qteFixePrdInCoffret ?? undefined;
    this.qteReliquatBl = partialDetailBlAchatGroupeDTO?.qteReliquatBl ?? undefined;
    this.oldPphRemise = partialDetailBlAchatGroupeDTO?.oldPphRemise ?? undefined;
    this.oldTauxRf = partialDetailBlAchatGroupeDTO?.oldTauxRf ?? undefined;
    this.isExtraLine = partialDetailBlAchatGroupeDTO?.isExtraLine ?? undefined;
    this.montantOriginal = partialDetailBlAchatGroupeDTO?.montantOriginal ?? undefined;
    this.quantiteLivreeOriginal = partialDetailBlAchatGroupeDTO?.quantiteLivreeOriginal ?? undefined;
  }
}


export class EnteteBlUnitaireMarcheDTO extends EnteteBlAchatGroupeDTO implements IEnteteBlUnitaireMarcheDTO {
  groupeEntreprise: GroupeEntreprise;
  responsableGroupe: PharmacieEntreprise;
  enteteBlConsolideeMarche:EnteteBlConsolideeMarcheDTO;

  constructor(partialEnteteBlUnitaireMarcheDTO: Partial<IEnteteBlUnitaireMarcheDTO>) {
    super(partialEnteteBlUnitaireMarcheDTO);
    this.groupeEntreprise = partialEnteteBlUnitaireMarcheDTO?.groupeEntreprise ?? undefined;
    this.responsableGroupe = partialEnteteBlUnitaireMarcheDTO?.responsableGroupe ?? undefined;
    this.enteteBlConsolideeMarche = partialEnteteBlUnitaireMarcheDTO?.enteteBlConsolideeMarche ?? undefined;
  }
}


export class EnteteBlConsolideeMarcheDTO extends EnteteBlAchatGroupeDTO implements IEnteteBlConsolideeMarcheDTO {
  groupeEntreprise: GroupeEntreprise;
  responsableGroupe: PharmacieEntreprise;

  constructor(partialEnteteBlConsolideeMarcheDTO: Partial<IEnteteBlConsolideeMarcheDTO>) {
    super(partialEnteteBlConsolideeMarcheDTO);
    this.groupeEntreprise = partialEnteteBlConsolideeMarcheDTO?.groupeEntreprise ?? undefined;
    this.responsableGroupe = partialEnteteBlConsolideeMarcheDTO?.responsableGroupe ?? undefined;
  }
}


export class DetailBlIndividuelDTO implements IDetailBlAchatGroupeDTO {
  id: number;
  enteteBl: IEnteteBlAchatGroupeDTO;
  produit: Produit;
  codeBarre: string;
  codeProduitGroupe: string;
  ppv: number;
  pph: number;
  tva: number;
  montant: number;
  designation: string;
  quantiteCommandee: number;
  quantiteLivree: number;
  tauxRf: number;
  quantiteUg: number;
  quantiteUgCmd: number;
  blocOffreId: number;
  datePeremption: Date;
  pphRemise: number;
  isCadeau: boolean;
  qteFixePrdInCoffret: number;
  qteReliquatBl: number;
  enteteBlIndividuel: EnteteBlIndividuel;
  oldPphRemise: number;
  oldTauxRf: number;
  isExtraLine: boolean;
  montantOriginal: number;
  quantiteLivreeOriginal: number;

  constructor(partialDetailBlIndividuelDTO: Partial<IDetailBlAchatGroupeDTO>) {
    Object.assign(this, partialDetailBlIndividuelDTO);
  }

}

export class EnteteBlIndividuel  implements IEnteteBlAchatGroupeDTO {
  id: number;
  fournisseur: EntrepriseDTO;
  transporteur: EntrepriseDTO;
  enteteCommandeAchatGroupe: IEnteteCommandeConsolideeMarcheDTO;
  montantCalcule: number;
  tauxRf: number;
  quantiteUg: number;
  montantSaisi: number;
  cumulBl: number;
  axeAnalytique: string;
  depot: string;
  typeCommande: string;
  dateCreation: string;
  dateReceptionBl: string;
  dateCommande: string;
  codeCommande: string;
  numeroBl: string;
  lignes: IDetailBlAchatGroupeDTO[];
  etatBl: EtatBlAchatGroupe;
  qteTotaleLivree: number;
  raisonSocialeTransporteur: string;
  lignesIndividuel: DetailBlIndividuelDTO[];
  enteteCommandeIndividuelleDTO: Commande;
  montantCalculeOriginal: number;
  qteTotaleLivreeOriginal: number;
  constructor(partialEnteteBlIndividuel: Partial<EnteteBlIndividuel>) {
    Object.assign(this, partialEnteteBlIndividuel);
  }

}
