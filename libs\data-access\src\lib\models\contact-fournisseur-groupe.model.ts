import { GroupeEntreprise } from "@wph/federation-syndicats";
import { EntrepriseDTO } from "libs/federation-syndicats/src/lib/models/entreprise.model";
import { Pagination } from "./PaginationDTO.ts";

export class ContactFournisseurGroupe {
    id?: number;
    emailFournisseur?: string;
    fournisseur?: EntrepriseDTO;
    groupe?: GroupeEntreprise;
    gsmFournisseur?: string;
    nomDelegue?: string;
    prenomDelegue?: string;

    constructor(contact: ContactFournisseurGroupe) {
        this.id = contact?.id || null;
        this.emailFournisseur = contact?.emailFournisseur || null;
        this.fournisseur = contact?.fournisseur || null;
        this.groupe = contact?.groupe || null;
        this.gsmFournisseur = contact?.gsmFournisseur || null;
    }
}

export interface SearchContactFournisseurGroupe extends Pagination {
    content?: ContactFournisseurGroupe[];
}

export class SearchContactFournCriteria {
    dateDebut?: string;
    dateFin?: string;
    emailFournisseur?: string;
    fournisseur?: EntrepriseDTO;
    groupe?: GroupeEntreprise;
    gsmFournisseur?: string;
    id?: number;
    nomDelegue?: string;
    prenomDelegue?: string;

    constructor(criteria?: Partial<SearchContactFournCriteria>) {
        this.dateDebut = criteria?.dateDebut || null;
        this.dateFin = criteria?.dateFin || null;
        this.emailFournisseur = criteria?.emailFournisseur || null;
        this.fournisseur = criteria?.fournisseur || null;
        this.groupe = criteria?.groupe || null;
        this.gsmFournisseur = criteria?.gsmFournisseur || null;
        this.id = criteria?.id || null;
        this.nomDelegue = criteria?.nomDelegue || null;
        this.prenomDelegue = criteria?.prenomDelegue || null;
    }
}