import { DocMetaDataDto } from '@wph/shared';
import { Catalogue } from './catalogue.model';
import { Pagination } from './PaginationDTO.ts';
import { Fournisseur } from './fournisseur.model';
import { Offre } from '@wph/data-access';



export class Produit {

  id?: number;

  catalogue?: Catalogue;
  codeBarre?: string;

  codeProduitCatalogue?: string;

  libelleProduit?: string;

  tauxTva?: number;

  colisage?: number;

  docImageBlocOffre?: DocMetaDataDto;

  prixVenteHt?: number;

  prixVenteTtc?: number;

  ppv?: number;

}

export interface SearchProduit extends Pagination {
  content: Produit[];
}

export class CheckTranscoDto {
  codeProduitCatalogue?: string;
  codeProduitSite?: string;
  codeSite?: number;
  libelleProduitGroupe?: string;
  libelleProduitOffre?: string;
  libelleProduitSite?: string;
}

export class CheckTranscoPrixDto {
  distributeur?: Fournisseur;
  message?: string;
  prixFournisseur?: number;
  prixGroupe?: number;
  produit?: Produit;
}

export interface CheckTranscoResponse {
  [index: string]: CheckTranscoDto[];
}

export interface CheckTranscoPrixResponse {
  [index: string]: CheckTranscoPrixDto;
}

export class CheckTranscoPrdResponseDTO {
  produit?: Produit;
  message?: string;
  distributeur?: Fournisseur;
}

export class CheckPrixRequestDTO {
  offreDto?: Offre;
  seuil?: number;
}
