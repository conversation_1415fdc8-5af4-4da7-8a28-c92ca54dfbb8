import { AnalyticsResponse } from "@wph/data-access";

export function buildSeriesByDimension(
    response: AnalyticsResponse,
    groupByDimension: string,
    splitByDimension: string,
    metricName: string
) {
    const groupIndex = response.dimensionHeaders.findIndex(d => d.name === groupByDimension);
    const splitIndex = response.dimensionHeaders.findIndex(d => d.name === splitByDimension);
    const metricIndex = response.metricHeaders.findIndex(m => m.name === metricName);

    if (groupIndex === -1 || splitIndex === -1 || metricIndex === -1) {
        throw new Error('Invalid dimension or metric provided.');
    }

    const groupedData: Record<string, Record<string, number>> = {};

    response.rows.forEach(row => {
        const groupKey = row.dimensionValues[groupIndex]?.value || 'unknown';
        const splitKey = row.dimensionValues[splitIndex]?.value || 'unknown';
        const metricValue = Number(row.metricValues[metricIndex]?.value || 0);

        if (!groupedData[splitKey]) {
            groupedData[splitKey] = {};
        }
        groupedData[splitKey][groupKey] = (groupedData[splitKey][groupKey] || 0) + metricValue;
    });

    const series = Object.entries(groupedData).filter(item => !item?.includes('(not set)') && !item?.includes('unknown')).map(([name, dataMap]) => ({
        name,
        data: Object.values(dataMap)
    }));

    const categories = Array.from(
        new Set(response.rows
            .filter(row => (
                !row.dimensionValues[groupIndex]?.value?.includes('(not set)') &&
                !row.dimensionValues[groupIndex]?.value?.includes('unknown') &&
                !row.dimensionValues[groupIndex]?.value?.includes('POST: Bienvenue à Pharmalien !') &&
                !!row.dimensionValues[groupIndex]?.value
            ))
            .map(row => row.dimensionValues[groupIndex]?.value || 'unknown'))
    );

    return { series, categories };
}

/**
    * Prepares data in the format required by Google Charts.
    * @param categories Array of category names (e.g., content names for the X-axis).
    * @param translatedSeries Array of series objects, each with a name (e.g., 'Vue', 'Clic') and data array.
    * @param categoryAxisLabel The label for the first column in the Google Chart header (the category axis).
    * @returns Data formatted as an array of arrays for Google Charts.
    */
export function prepareGoogleChartData(
    categories: string[],
    translatedSeries: { name: string; data: (number | null)[] }[],
    categoryAxisLabel: string = 'Category',
    sortByValue: boolean = true
): any[][] {
    if (!categories || categories.length === 0 || !translatedSeries || translatedSeries.length === 0) {
        return [['No Data'], [0]]; // Return a minimal structure for Google Charts to not error out
    }

    const googleChartDataArray: any[][] = [];
    const headerRow: string[] = [categoryAxisLabel, ...translatedSeries.map(s => s.name)];
    googleChartDataArray.push(headerRow);

    if (sortByValue) {
        const sortedValues = translatedSeries[0].data.sort((a, b) => (b || 0) - (a || 0));
        
        categories.forEach((category, index) => {
            googleChartDataArray.push([category, sortedValues[index]]);
        });
    } else {
        categories.forEach((category, catIndex) => {
            const dataRow: any[] = [category, ...translatedSeries.map(serie => {
                const value = serie.data.length > catIndex ? serie.data[catIndex] : null;
                if (value === null || typeof value === 'undefined') { // Check for null or undefined explicitly
                    return 0; // Default for missing data
                }
                const num = Number(value); // Attempt conversion to number
                return isNaN(num) ? 0 : num; // If conversion results in NaN, use 0, otherwise use the number
            })];
            googleChartDataArray.push(dataRow);
        });
    }

    return googleChartDataArray;
}
