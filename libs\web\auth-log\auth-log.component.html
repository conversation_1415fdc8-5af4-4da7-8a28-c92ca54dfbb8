<!-- Start Of Header -->
<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col">Journal de Connection</h4>

    <div class="col px-1">
      <div class="row justify-content-end align-items-center">


        <button type="button" class="btn  btn-sm btn-info mx-1"  (click)="openFilterModal(filterModal)">
          <i class="mdi mdi-filter-variant mr-1"></i>Filtrer
        </button>
      </div>
    </div>
  </div>
</div>
<!-- END HEADER -->


<div class="container-fluid m-0 p-0">
  <kendo-grid class="border-grey px-0 card" style="height: calc(100vh - 123px);" [data]="authLogs" [pageable]="true" [pageSize]="navigation.pageSize" [skip]="navigation.skip"
  (pageChange)="onPageChange($event)"

  >
    <kendo-grid-column field="user" title="Code Client" [width]="50" [sortable]="true">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem.user.entrepriseDTO?.code }}
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="user.username" title="Nom d'utilisateur" [width]="50" [sortable]="true"></kendo-grid-column>
    <kendo-grid-column field="user" title="Nom Responsable" [width]="50" [sortable]="true">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem.user?.firstname }} {{ dataItem.user?.lastname }}
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="user.entrepriseDTO.ville" title="Ville" [width]="50" [sortable]="true"></kendo-grid-column>
    <kendo-grid-column field="logDate" title="Date d'accès" [width]="50" [sortable]="true"></kendo-grid-column>
    <kendo-grid-column field="srcIp" title="Ip Address" [width]="50" [sortable]="true"></kendo-grid-column>
    <ng-template kendoGridNoRecordsTemplate>
      <span>Aucun résultat trouvé.</span>
    </ng-template>
  </kendo-grid>
</div>


<ng-template #filterModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Filtrer</h4>

    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('cross click')">
        <i class="mdi mdi-close"></i>
    </button>
</div>
  <form [formGroup]="authLogFilterForm" (ngSubmit)="filterAuthLog()">
  <div class="modal-body">
      <div class="row">
        <!-- Left Column -->
        <div class="col-md-6">
          <div class="mb-1">
            <label for="dateDebut" class="form-label">Date début</label>
            <div class="input-group">
              <input
                id="dateDebut"
                class="form-control"
                placeholder="yyyy-mm-dd"
                formControlName="dateDebut"
                ngbDatepicker
                #dateDebutPicker="ngbDatepicker">
              <button class="btn btn-outline-secondary calendar" (click)="dateDebutPicker.toggle()" type="button">
                <i class="mdi mdi-calendar"></i>
              </button>
            </div>
          </div>
          </div>
          <div class="col-md-6">
            <div class="mb-1">
            <label for="dateFin">Date Fin</label>
            <div class="input-group">
             <input #dateFinPicker="ngbDatepicker" id="dateFin"   formControlName="dateFin" class="form-control" placeholder="yyyy-mm-dd"  ngbDatepicker>
             <button class="btn btn-outline-secondary calendar" (click)="dateFinPicker.toggle()" type="button">
             <i class="mdi mdi-calendar"></i>
             </button>
            </div>
          </div>
           </div>
          <div class="col-md-6">
            <div class="mb-1">
              <label for="nomPrenomOperateur" class="form-label">Nom et prénom opérateur</label>
              <input type="text" class="form-control" id="nomPrenomOperateur" formControlName="nomPrenomOperateur">
            </div>
          </div>

            <!-- <div class="col-md-6">
            <label for="operateur" class="form-label">Opérateur</label>
            <input type="text" class="form-control" id="operateur" formControlName="operateur">
            </div> -->

            <div class="col-md-6">
            <label for="adresseIp" class="form-label">Adresse IP</label>
            <input type="text" class="form-control" id="adresseIp" formControlName="adresseIp">
            </div>


      </div>



    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="resetFilter()">Réinitialiser</button>
      <button type="submit" class="btn btn-primary">Appliquer</button>
    </div>
  </form>
  </ng-template>
