import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>er, LoadingController, ModalController, NavController, Platform} from '@ionic/angular';
import * as Leaflet from 'leaflet';
import { AuthService } from "@wph/core/auth";
import { IUserAccount } from "@wph/shared";
import { Fournisseur } from '@wph/data-access';
import { Subscription } from 'rxjs';
@Component({
  selector: 'wph-my-account',
  templateUrl: './my-account.page.html',
  styleUrls: ['./my-account.page.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class MyAccountPage implements OnInit, OnDestroy {
  societe: Fournisseur;
  accountInfo: IUserAccount;
  backBtnSubscription: Subscription | null = null;
  map: Leaflet.Map = {
      lat: 40.712562,
      lng: -74.005911,
      zoom: 15,
      mapTypeControl: true,
      streetViewControl: true
  };

  constructor(
    private platform: Platform,
    private authService: AuthService,
    private modalCtrl: ModalController,
    private alertCtrl: AlertController,
    private navController: NavController, 
    private loadingCtrl: LoadingController
  ) {
    this.loadModel();
  }

  ngOnInit(): void {
    this.societe = this.authService.currentUser()?.societe;

    this.backBtnSubscription = this.platform.backButton.subscribeWithPriority(10, (processNextHandler) => {
      this.navController.navigateBack('/accueil'), processNextHandler();
    });
  }


  ionViewDidEnter() { 
    //this.leafletMap(); 
  }


  private loadModel() {
    this.authService.getMyAccount( )
      .subscribe(
        (data: any) => {
          this.accountInfo = data;
        }
      );
  }

  leafletMap() {
    this.map = Leaflet.map('mapId', {
      'attributionControl': false
  }).setView([35.768122, -5.8249], 16);
    Leaflet.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png',).addTo(this.map);

    Leaflet.marker([35.768122, -5.8249] , {
      icon: this.generateMarker('assets/icon/marker-icon-2x-red.png')
    }).addTo(this.map).bindPopup()
    // Leaflet.marker([34, 77]).addTo(this.map).bindPopup('Leh').openPopup();

    // antPath([[28.644800, 77.216721], [34.1526, 77.5771]],
    //   { color: '#FF0000', weight: 5, opacity: 0.6 })
    //   .addTo(this.map);
  }

  generateMarker(imag: string): Leaflet.Icon<Leaflet.IconOptions> {
    const icon = new Leaflet.Icon({
      iconUrl: imag,
      shadowUrl: 'assets/icon/marker-shadow.png',
      iconSize: [19, 29],
      iconAnchor: [12, 41],
      popupAnchor: [-2, -30],
      shadowSize: [19, 29]
    });
    return icon;
  }

  openPage(page:string){
    this.navController.navigateRoot([page], {});
  }

  async logout() {
    const loading = await this.loadingCtrl.create({
      message: 'Chargement...',
      duration: 3000
    });
    loading.present();
    this.authService.unsetToken().subscribe((data: any) => {
      loading.dismiss();
      this.authService.logout();
      this.navController.navigateRoot(['/auth/login']);
    });
  }

  async confirmAccountDeletion(): Promise<void> {
    const alert = await this.alertCtrl.create({
      header: 'Confirmer',
      message: "En confirmant, une demande de suppression de compte sera envoyée à l’administrateur. Vous pourrez toujours accéder à l’application avant que la demande ne soit approuvée par l’administrateur. Si vous souhaitez toujours conserver votre compte, veuillez contacter le support dès que possible.",
      cssClass: 'confirm-cmd',
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel'
        },
        {
          text: 'Confirmer',
          role: 'confirm',
          handler: async() => {
            this.modalCtrl.dismiss(); 
            this.authService.requestDeactivation().subscribe(_ => {
              this.logout();
              localStorage.clear();
            });
          }
        }
      ]
    });

    await alert.present();
  }

  ngOnDestroy(): void {
    this.backBtnSubscription.unsubscribe();
  }
}
