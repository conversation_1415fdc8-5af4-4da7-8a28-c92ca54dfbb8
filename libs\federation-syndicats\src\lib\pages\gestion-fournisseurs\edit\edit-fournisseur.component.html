<!-- start page title -->
<div class="rowline mb-0">
    <div class="page-title-box row">

        <ng-container *ngIf="!isReadOnly">
            <div class="d-flex  align-items-center col-4 k-gap-4">
                <button class="actions-icons action-back btn text-white" (click)="back()">
                    <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
                </button>
                <h4 class="page-title fw-4 ps-2">{{ fournisseurId ? 'Modifier Fournisseur' : 'Ajouter Fournisseur' }}
                </h4>
            </div>

            <div class="col-8 px-1">
                <div class="row d-flex justify-content-end align-items-center">
                    <button (click)="saveChanges()" style="padding-block: 6px;" class="btn btn-sm btn-success"
                        title="Enregistrer Pharmacie">
                        <i class="bi bi-bookmark-check-fill"></i>
                        Enregistrer
                    </button>

                    <button (click)="back()" type="button" style="padding-block: 6px;"
                        class="btn btn-sm btn-dark text-white m-1">
                        <i class="mdi mdi-close"></i>
                        Quitter
                    </button>
                </div>
            </div>
        </ng-container>

        <ng-container *ngIf="isReadOnly">
            <div class="d-flex  align-items-center col-auto k-gap-4">
                <button class="actions-icons action-back btn text-white" (click)="back()">
                    <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
                </button>
                <h4 class="page-title fw-4 ps-2 col-12">Consultation Fournisseur</h4>
            </div>

            <div class="col-auto px-1">
                <div class="row d-flex justify-content-end align-items-center">
                    <button (click)="back()" type="button" style="padding-block: 6px;"
                        class="btn btn-sm btn-dark text-white m-1">
                        <i class="mdi mdi-close"></i>
                        Quitter
                    </button>
                </div>
            </div>
        </ng-container>
    </div>
</div>
<!-- end page title -->

<div class="row mx-2">
    <div class="card bg-transparent my-1 w-100">
        <form [formGroup]="editForm" class="p-0 m-0" autocomplete="off">
            <ul ngbNav #infoPharamcieNav="ngbNav" class="nav-tabs  pharmacie-tab" style="gap: 0 !important;">
                <li [ngbNavItem]="1">
                    <a ngbNavLink class="w-100">
                        <span class="d-flex row align-items-center px-0 px-sm-2">
                            <b>Informations Générales</b>
                            <i *ngIf="submitted && editForm.invalid"
                                class="bi bi-exclamation-diamond-fill text-danger mx-2 fs-1"></i>
                            <i *ngIf="editForm.valid" class="bi bi-check-circle-fill text-success mx-2 fs-1"></i>
                        </span>
                    </a>

                    <ng-template ngbNavContent>
                        <div class="card-body px-1 bg-white mb-4 mb-sm-0">
                            <div class="form-row mx-0 p-1">
                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0 mb-0">
                                        <label for="codeFourn" class="form-label p-0 col-12">Code Fournisseur <span
                                                *ngIf="!isReadOnly" class="text-danger">*</span></label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="codeFourn" formControlName="code"
                                                class="form-control pl-4" placeholder="Entrez le code fournisseur"
                                                [readonly]="isReadOnly"
                                                [ngClass]="{'is-invalid': f['code']?.invalid && (f['code']?.dirty || f['code']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-info-circle-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['code']?.invalid && (f['code']?.dirty || f['code']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['code']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['code']?.errors['maxlength']">
                                            Code fournisseur ne peut pas depasser 10 caractères.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0 mb-0">
                                        <label for="numIce" class="form-label p-0 col-12">Numéro ICE</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="numIce" formControlName="numIce"
                                                [readonly]="isReadOnly" class="form-control pl-4"
                                                placeholder="Entrez le numéro ICE"
                                                [ngClass]="{'is-invalid': f['numIce']?.invalid && (f['numIce']?.dirty || f['numIce']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-shield-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['numIce']?.invalid && (f['numIce']?.dirty || f['numIce']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['numIce']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                    </div>
                                </div>


                                <div class="col-md-4 my-1">
                                    <div id="edit-pharmacie-statut">
                                        <div class="form-group mb-0">
                                            <label for="typeEntreprise" class="form-label p-0 col-12">Type Entreprise
                                                <span *ngIf="!isReadOnly" class="text-danger">*</span></label>

                                            <div class="input-group picker-input">
                                                <select2 *ngIf="!isReadOnly" id="typeEntreprise"
                                                    [data]="typeEntrepriseValues" [readonly]="isReadOnly"
                                                    placeholder="Sélectionnez le type entreprise"
                                                    formControlName="typeEntreprise"
                                                    [ngClass]="{'is-invalid': f['typeEntreprise']?.invalid && (f['typeEntreprise']?.dirty || f['typeEntreprise']?.touched || submitted)}"
                                                    class="form-control-md w-100"></select2>

                                                <input *ngIf="isReadOnly" type="text" id="typeEntreprise"
                                                    formControlName="typeEntreprise" class="form-control pl-4"
                                                    [readonly]="isReadOnly" />

                                                <div class="picker-icons picker-icons-alt">
                                                    <i class="bi bi-building-fill text-dark"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div *ngIf="f['typeEntreprise']?.invalid && (f['typeEntreprise']?.dirty || f['typeEntreprise']?.touched || submitted)"
                                            class="text-danger">
                                            <div *ngIf="f['typeEntreprise']?.errors['required']">
                                                champ obligatoire
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="form-row mx-0 p-1">

                                <div class="col-md-6 my-1">
                                    <div class="form-group mb-0 mb-0">
                                        <label for="raisonSociale" class="form-label p-0 col-12">Raison Sociale <span
                                                *ngIf="!isReadOnly" class="text-danger">*</span></label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="raisonSociale" formControlName="raisonSociale"
                                                class="form-control pl-4" placeholder="Entrez la raison sociale"
                                                [readonly]="isReadOnly"
                                                [ngClass]="{'is-invalid': f['raisonSociale']?.invalid && (f['raisonSociale']?.dirty || f['raisonSociale']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-info-circle-fill text-dark"></i>
                                            </div>

                                        </div>
                                    </div>

                                    <div *ngIf="f['raisonSociale']?.invalid && (f['raisonSociale']?.dirty || f['raisonSociale']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['raisonSociale']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['raisonSociale']?.errors['maxlength']">
                                            Raison Sociale ne peut pas depasser 80 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 my-1">
                                    <div class="form-group mb-0">
                                        <label for="nomResponsable" class="form-label p-0 col-12">Nom
                                            Responsable</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="nomResponsable" formControlName="nomResponsable"
                                                class="form-control pl-4" placeholder="Entrez le nom responsable"
                                                [readonly]="isReadOnly"
                                                [ngClass]="{'is-invalid': f['nomResponsable']?.invalid && (f['nomResponsable']?.dirty || f['nomResponsable']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-person-circle text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="f['nomResponsable']?.invalid && (f['nomResponsable']?.dirty || f['nomResponsable']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['nomResponsable']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['nomResponsable']?.errors['maxlength']">
                                            Nom Responsable ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="form-row mx-0 p-1">
                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0">
                                        <label for="gsm1" class="form-label p-0 col-12">
                                            GSM
                                        </label>
                                        <div class="input-group picker-input">
                                            <input type="text" id="gsm1" formControlName="gsm1" [readonly]="isReadOnly"
                                                class="form-control pl-4" placeholder="Ex : 06XXXXXXXX" maxlength="10"
                                                [ngClass]="{'is-invalid': f['gsm1']?.invalid && (f['gsm1']?.dirty || f['gsm1']?.touched || submitted)}" />
                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-phone-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="f['gsm1'].invalid && (f['gsm1']?.dirty || f['gsm1']?.touched || submitted)"
                                        class="text-danger">
                                        <span>GSM Invalide.</span>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0">
                                        <label for="gsm1" class="form-label p-0 col-12">
                                            Téléphone
                                        </label>
                                        <div class="input-group picker-input">
                                            <input type="text" id="telephone" formControlName="telephone"
                                                [readonly]="isReadOnly" class="form-control pl-4"
                                                placeholder="Ex : 05XXXXXXXX" maxlength="10"
                                                [ngClass]="{'is-invalid': f['telephone']?.invalid && (f['telephone']?.dirty || f['telephone']?.touched || submitted)}" />
                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-telephone-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="f['telephone'].invalid && (f['telephone']?.dirty || f['telephone']?.touched || submitted)"
                                        class="text-danger">
                                        <span>Téléphone Invalide.</span>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0">
                                        <label for="email" class="form-label p-0 col-12">
                                            Email <span style="color: #b4b4b4; font-size: 0.85rem">Plusieurs emails
                                                séparés par un point-virgule (;)</span>
                                        </label>
                                        <div class="input-group picker-input">
                                            <input type="text" id="email" formControlName="email"
                                                [readonly]="isReadOnly" class="form-control pl-4"
                                                placeholder="Ex: <EMAIL>;<EMAIL>"
                                                [ngClass]="{'is-invalid': f['email']?.invalid && (f['email']?.dirty || f['email']?.touched || submitted)}" />
                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-envelope-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="f['email'].invalid && (f['email']?.dirty || f['email']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['email'].errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['email'].errors['email']">
                                            Email Invalide.
                                        </div>
                                        <div *ngIf="f['email'].errors['maxlength']">
                                            Email ne peut pas depasser 40 caractére.
                                        </div>
                                        <div *ngIf="f['email'].errors['pattern']">
                                            Email doit être au format: Ex : example&#64;domain.com
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row mx-0 p-1">
                                <div class="col-md-6 mt-1 mb-2">
                                    <div class="form-group mb-0">
                                        <label for="adresse1" class="form-label p-0 col-12">Adresse Ligne 1</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="adresse1" formControlName="adresse"
                                                [readonly]="isReadOnly" class="form-control pl-4"
                                                placeholder="Entrez l'adresse ligne 1"
                                                [ngClass]="{'is-invalid': f['adresse']?.invalid && (f['adresse']?.dirty || f['adresse']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-shop text-dark"></i>

                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['adresse']?.invalid && (f['adresse']?.dirty || f['adresse']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['adresse']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['adresse']?.errors['maxlength']">
                                            Adresse ne peut pas depasser 80 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mt-1 mb-2">
                                    <div class="form-group mb-0">
                                        <label for="adresse2" class="form-label col-12 p-0">Adresse Ligne 2</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="adresse2" formControlName="adresse2"
                                                [readonly]="isReadOnly" class="form-control pl-4"
                                                placeholder="Entrez l'adresse ligne 2"
                                                [ngClass]="{'is-invalid': f['adresse2']?.invalid && (f['adresse2']?.dirty || f['adresse2']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-shop text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['adresse2'].invalid && (f['adresse2']?.dirty || f['adresse2']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['adresse2']?.errors['maxlength']">
                                            Adresse ne peut pas depasser 40 caractére.
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mt-1 mb-2">
                                    <div class="form-group mb-0">
                                        <label for="ville" class="form-label col-12 p-0">Ville</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="ville" formControlName="ville"
                                                [ngbTypeahead]="searchVille" [inputFormatter]="villeFormatter"
                                                [resultFormatter]="villeFormatter" class="form-control pl-4"
                                                placeholder="Entrez la ville" [readonly]="isReadOnly"
                                                [ngClass]="{'is-invalid': f['ville']?.invalid && (f['ville']?.dirty || f['ville']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-geo-alt text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['ville'].invalid && (f['ville']?.dirty || f['ville']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['ville']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mt-1 mb-2">
                                    <div class="form-group mb-0">
                                        <label for="localite" class="form-label col-12 p-0">Localité </label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="localite" formControlName="localite"
                                                class="form-control pl-4" placeholder="Entrez la localité"
                                                [readonly]="isReadOnly" [ngbTypeahead]="searchLocalite"
                                                [inputFormatter]="localiteFormatter"
                                                [resultFormatter]="localiteFormatter"
                                                [ngClass]="{'is-invalid': f['localite']?.invalid && (f['localite']?.dirty || f['localite']?.touched || submitted)}" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-geo-alt text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['localite'].invalid && (f['localite']?.dirty || f['localite']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['localite']?.errors['required']">
                                            champ obligatoire
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </ng-template>
                </li>
            </ul>
        </form>
        <div [ngbNavOutlet]="infoPharamcieNav"></div>
    </div>
</div>