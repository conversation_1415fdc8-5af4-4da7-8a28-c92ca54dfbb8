<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-6 col-sm-4">Liste des Sondages</h4>

    <div class="col-6 col-sm-8 px-1">
      <div class="row justify-content-end align-items-center">
        <!-- add button if needed -->
      </div>
    </div>
  </div>
</div>
<!-- END HEADER -->

<div class="row d-flex m-0 px-1">
  <div class="card m-0 w-100 p-0 bg-white" [style.max-height]="'calc(100vh - 60px)'"
    *jhiHasAnyAuthority="['ROLE_NATIONAL', 'ROLE_RESPONSABLE']">
    <div class="card-header py-1 pl-2 mx-0 bg-white">
      <div class="row p-0 d-flex justify-content-end">
        <div class="col-8 p-0 ">
          <div class="col-12 col-md-8  col-lg-6 p-0 m-1 ml-auto">
            <div class="input-group picker-input">
              <input type="text" [formControl]="searchControl" placeholder="Chercher par titre offre"
                class="form-control form-control-md pl-4 rounded-lg" id="groupeCritere" style="border-radius: 10px !important;" />

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sondages-container">
      <div class="sondages-list m-0 p-2 bg-white card-view">
        <div *ngIf="filteredSondages.length === 0 " class="w-100 d-flex justify-content-center align-items-center">
          Aucun résultat trouvé. </div>
        <div *ngFor="let sondage of filteredSondages"
          class="sondage-item w-100 py-2 px-3 d-flex flex-column justify-content-between align-items-start mb-2"
          [ngClass]="{'expanded': sondage === selectedSondage}"
          style="background-color: #F6F6F6; border-radius: 12px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); transition: all 0.3s ease;">
          <!-- Header Section -->
          <div class="d-flex w-100 justify-content-between align-items-center" style="cursor: pointer;"
            (click)="toggleSondage(sondage)">
            <div class="d-flex align-items-center ">
              <div class="image-container"
                [ngStyle]="{'background-image': getImageUrl(sondage) ? 'url(' + getImageUrl(sondage) + ')' : 'var(--fs-actu-default-img)', 'background-size': 'cover', 'background-position': 'center', 'height': '50px', 'width': '50px', 'margin-right': '8px', 'border-radius': '12px'}">
              </div>
              <div class=" d-flex ">
                <p style="margin-right: 5px; font-weight: 600;"><span class="offre-title"
                    style="font-size: 18px; color: #696C75; font-weight: 700;">Commande : </span>{{
                  sondage.commande.codeCommande }}</p>
                <p style="margin-right: 5px; font-weight: 600;"><span class="offre-title"
                    style="font-size: 18px; color: #696C75; font-weight: 700; ">Du:</span>{{ sondage.commande.titre }}
                </p>
              </div>


            </div>
            <div class="d-flex align-items-center">
              <div *ngIf="sondage.positive !== null && sondage.notInterested !== null" class="d-flex flex-column" style="font-weight: 700; margin-right: 15px;">
                <p class="mb-0 interet">
                  <span style="color: #5E9E71 !important; margin-right: 5px;">
                    {{ (sondage.positive | number: '1.0-2') + '%' }}
                  </span>
                  Intéressé
                </p>
                <p class="mb-0 interet">
                  <span style="color: #DCB544 !important; margin-right: 5px;">
                    {{ (sondage.notInterested | number: '1.0-2') + '%' }}
                  </span>
                  Non Intéressé
                </p>
              </div>
              <i [ngClass]="{'bi-chevron-double-down': sondage !== selectedSondage, 'bi-chevron-double-up': sondage === selectedSondage}"
                class="transition-icon" style="font-size: 28px; color: #696C75;"></i>
            </div>
          </div>
          <!-- Table Section -->
          <div *ngIf="sondage === selectedSondage" class="table-container soundage-list-table mt-2 w-100">
            <div class=" px-1">
              <kendo-grid [data]="gridData" class="fs-grid fs-grid-white" [sortable]="{ mode: 'single'}" [pageable]="{
                    buttonCount: 5,
                    info: true,
                    type: 'numeric',
                    pageSizes: pageSizes,
                    previousNext: true,
                    position: 'bottom'
                  }" [pageSize]="navigation.pageSize" (pageChange)="pageChange($event)"
                (sortChange)="sortChange($event)" [resizable]="true" [skip]="navigation.skip">
                <ng-template kendoGridToolbarTemplate>
                  <div class="d-flex justify-content-between align-items-center w-100">
                    <div class="header-switch d-flex justify-content-start align-items-center">
                      <h2 class="table-header" style="font-size: 20px; margin-right: 10px; color: white;">Liste des
                        avis</h2>
                      <kendo-switch [(ngModel)]="isInterested" [readonly]="false" size="medium" onLabel="Intéressé"
                        offLabel="Non Intéressé" class="status-switch-align fs-soundage-switch"
                        (ngModelChange)="onSwitchChange()"></kendo-switch>
                    </div>
                  </div>
                </ng-template>

                <kendo-grid-column field="nomResponsable" title="Nom Membre" [width]="150"> 
                  <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Nom Membre</span>
                  </ng-template>
                  
                  <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem?.sondeurEntreprise?.nomResponsable}}
                  </ng-template>
                </kendo-grid-column>

                <ng-container *ngIf="isInterested">
                  <kendo-grid-column field="qualite" title="Qualité" [width]="80">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      {{dataItem.qualite}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column field="reduction" title="Réduc." [width]="80">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      {{dataItem.reduction}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column field="paiement" title="Paiement" [width]="80">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      {{dataItem.paiement}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column field="livraison" title="Livraison" [width]="80">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      {{dataItem.livraison}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column field="laboratoire" title="Lab." [width]="80">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      {{dataItem.laboratoire}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column field="commentaire" class="text-wrap" title="Commentaires" [width]="150"></kendo-grid-column>
                </ng-container>

                <ng-container *ngIf="!isInterested">
                  <kendo-grid-column field="raison" class="text-wrap" title="Raison du Refus" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                      <span class="text-wrap">Raison du Refus</span>
                    </ng-template>                    
                  </kendo-grid-column>

                  <kendo-grid-column field="commentaire" title="Commentaires" [width]="150"></kendo-grid-column>
                </ng-container>

                <kendo-grid-column field="dateCreation" title="Date Soumission" [width]="150">
                  <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap">Date Soumission</span>
                  </ng-template>
                  
                  <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.dateCreation | date: 'dd/MM/yyyy - HH:MM'}}
                  </ng-template>
                </kendo-grid-column>

                <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
                  let-total="total">
                  <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
                    [navigation]="navigation" style="width: 100%;"
                    (pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
                </ng-template>
                <ng-template kendoGridNoRecordsTemplate>
                  <span>Aucun résultat trouvé.</span>
                </ng-template>
              </kendo-grid>
            </div>
          </div>
        </div>
      </div>
    </div>



  </div>
</div>
