import { SocieteType, User } from "@wph/shared";
import { PharmacieEntreprise } from "./pharmacie-entreprise.model";
import { Pagination } from "@wph/data-access";

export class GroupeEntreprise {
    id?: number;
    code?: string;
    typeEntreprise?: SocieteType;
    raisonSociale?: string;
    ville?: string;
    localite?: string;
    groupeEntreprise?: GroupeEntreprise;
    dateAttachementGroupe?: Date;
    dateDetachementGroupe?: Date;
    responsablesGroupe?: PharmacieEntreprise[];
    dateActivation?: Date;
    dateDesactivation?: Date;
    statutEntreprise?: boolean;
    createdAt?: Date;
    createdById?: User;
    updatedAt?: Date;
    updatedById?: User;
    deletedAt?: Date;
    deletedById?: User;
    nbrMembres?: number;
}

export interface SearchGroupeEntreprise extends Pagination {
    content: GroupeEntreprise[];
}