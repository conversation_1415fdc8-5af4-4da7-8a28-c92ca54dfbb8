import { FsStatistiqueService } from './../../services/fs-statistique.service';
import {
  AfterViewInit,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Pagination } from '@wph/data-access';
import {
  BlogPostCriteria,
  BlogPostDto,
  PosteService,
} from '@wph/web/gestion-annonces';
import { Swiper } from 'swiper';

import { FederationSyndicatService } from '../../services/federation-syndicats.service';
import {
  FsCommandeCriteria,
  GroupeEntreprise,
  PharmacieEntreprise,
} from '@wph/federation-syndicats';
import { AuthService } from '@wph/core/auth';
import { FormControl } from '@angular/forms';
import { Observable, Subject, takeUntil } from 'rxjs';
import { FsOffreCriteria } from '../../models/fs-offre.model';
import { DashboardData, LaborationDashboardData } from '../../models/dashboard-data';
import { GroupByField, StatisticsCriteriaDTO } from '../../models/statistique.model';
import * as moment from 'moment';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { KeyboardShortcutService } from '@wph/web/shared';
import { SelectedPlateforme } from '@wph/web/layout';
import { PlateformeService } from '@wph/shared';

@Component({
  selector: 'wph-fed-syndicat-accueil',
  templateUrl: './accueil.component.html',
  styleUrls: ['./accueil.component.scss'],
})
export class AccueilComponent implements AfterViewInit, OnInit, OnDestroy {
  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  totalOffres: number = 0;
  badgeValue: number = 3;
  searchCriteria: FsOffreCriteria = new FsOffreCriteria();
  public cmdSearchCriteria: FsCommandeCriteria = new FsCommandeCriteria();
  public livSearchCriteria: FsCommandeCriteria = new FsCommandeCriteria();

  public totalCmdEnCours: number = 0;
  public totalLivEnAttente: number = 0;
  public totalCmdSupporte: number = 0;
  public totalNouvellesOffres: number = 0;
  public totalOffresActives: number = 0;
  public totalCmdConsome: number = 0;
  public totalActiveGroups: number = 0;
  public totalActiveMembers: number = 0;
  public totalActiveMembersByGroupe: number = 0;

  private monGroupe: GroupeEntreprise;
  private membreId: number;

  swiper: Swiper;
  listePostes: BlogPostDto[] = [];
  paginationParams: Pagination = {
    skip: 0,
    pageSize: 3,
    sortMethod: 'desc',
    sortField: 'dateDebutVisibilite',
  };

  filterCriteria = {
    visible: true,
    categorie: null,
    dateDebutVisibilite: null,
    dateFinVisibilite: null,
    fournisseursId: null,
    type: null,
    scopes: null,
    typeActualite: null,
  };

  now = new Date();
  displayedDate = new Date();

  datePicker = new FormControl();

  rolePharmacienOnly: boolean;
  roleResponsableOnly: boolean;

  roleLAbortoireOnly: boolean;

  shouldDisplayStats: boolean;

  autoPlaySlider: string = 'false';

  laboratioreDashboardData: LaborationDashboardData;
  keyboardShortcuts: { desc: string, combo: string[] }[] | null = null;

  pharmaEntreprise: PharmacieEntreprise;
  currentPlateforme$: Observable<SelectedPlateforme>;

  @ViewChild('swiperRef', { static: false }) swiperRef: ElementRef;

  enteteStatistique: { montantConsome: number, montantSupporte: number, montantBalance: number };

  constructor(
    private modalService: NgbModal,
    private authService: AuthService,
    private posteService: PosteService,
    private plateformeService: PlateformeService,
    private fsStatistiqueService: FsStatistiqueService,
    private keyboardShortcutService: KeyboardShortcutService,
    private federationSyndicatService: FederationSyndicatService,
  ) {
    this.currentPlateforme$ = this.plateformeService.currentPlateforme$;
    
    this.rolePharmacienOnly =
      this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE']) &&
      !this.authService.hasAnyAuthority(['ROLE_NATIONAL', 'ROLE_RESPONSABLE']);

    this.roleResponsableOnly = this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']) && !this.authService.hasAnyAuthority(['ROLE_NATIONAL']);
    this.roleLAbortoireOnly = this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']) && !this.authService.hasAnyAuthority(['ROLE_NATIONAL']);
    this.membreId = this.authService.getPrincipal()?.societe?.id;


    this.laboratioreDashboardData = {
      offresActives: 0,
      nouvellesOffres: 0,
      commandesEnCoursDeSaisie: 0,
      commandesEnAttenteDeLivraison: 0,
      commandesSupportees: 0,
      commandesConsommees: 0,
      nbrCmdTraiteParLabo: 0,
      nbrCmdNonTraiteParLabo: 0,
      chiffreDaffaireLaboCmdHt: 0,
      chiffreDaffaireLaboCmdTtc: 0,
      nbreClientLabo: 0,
    }
  }

  ngOnInit(): void {
    this.shouldDisplayStats = this.authService.hasAnyAuthority(['ROLE_NATIONAL', 'ROLE_RESPONSABLE', 'ROLE_SUPER_ADMIN', 'ROLE_AGENT_POINT_VENTE']);

    this.getListePostes();
    this.initializeComponent();
    this.getMyStatistique();
    this.getTableauDeBordLaboratoire();

    this.listenToDatePickerChanges();

    // this.keyboardShortcuts = this.keyboardShortcutService.getRegisteredShortcuts(); // TODO: AGK-> uncomment when help-modal content is approved
  }


  getMyStatistique(): void {
    const groupeId = this.authService.getPrincipal()?.groupe?.id;
    const membreId = this.authService.getPrincipal()?.societe?.id;
    const statistiqueCriteria = new StatisticsCriteriaDTO({
      dateDebut: moment(this.displayedDate).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      dateFin: moment(this.displayedDate).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      idGroupe: groupeId,
      idMembre: membreId,
      listGroupByfields: [GroupByField.YEAR_MONTH, GroupByField.PHARMACIEN],
    });
    this.fsStatistiqueService.getStatictiques(statistiqueCriteria).subscribe(res => {
      this.enteteStatistique = {
        montantBalance: res?.[0]?.balance ?? 0,
        montantConsome: res?.[0]?.montantConsomme ?? 0,
        montantSupporte: res?.[0]?.montantSupportee ?? 0,
      }
    })
  }

  openModal(content: TemplateRef<any>, size = 'lg'): void {
    this.modalService.open(content, { size, modalDialogClass: 'help-modal-container', windowClass: 'help-modal-window' }).result.then(
      () => null,
      () => null
    );
  }


  getTableauDeBordLaboratoire(): void {
    if(!this.roleLAbortoireOnly){
      return;
    }
    this.federationSyndicatService.getTableauDeBordLaboratoire().subscribe({
      next: (res) => {
        this.laboratioreDashboardData = res;
      },
      error: (error) => console.error('Failed to get tableau de bord', error)
    });
  }

  fetchTotals(): void {
    this.federationSyndicatService.getTotalActiveGroups().subscribe({
      next: (totalGroups) => this.totalActiveGroups = totalGroups,
      error: (error) => console.error('Failed to get total active groups', error)
    });

    this.federationSyndicatService.getTotalActiveMembers().subscribe({
      next: (totalMembers) => this.totalActiveMembers = totalMembers,
      error: (error) => console.error('Failed to get total active members', error)
    });
  }

  initializeComponent(): void {
    this.federationSyndicatService
      .getMyGroupe()
      .then((myGroupe) => {
        this.monGroupe = myGroupe;
        if (this.monGroupe) {
          this.fetchDashboardData();
          this.fetchTotals();
          this.fetchActiveMembersByGroupe(this.monGroupe.id);
        } else {
          console.error('monGroupe is null');
        }
      })
      .catch((error) => {
        console.error('Error initializing monGroupe:', error);
      });
  }

  fetchDashboardData(): void {
    if (!this.monGroupe || !this.monGroupe.id) {
      console.error('monGroupe is not initialized or id is missing');
      return;
    }

    if (!this.membreId) {
      console.error('membreId is not initialized');
      return;
    }

    this.federationSyndicatService
      .obtenirTableauDeBord(this.monGroupe?.id, this.membreId)
      .subscribe(
        (data: DashboardData) => {
          this.totalCmdEnCours = data.commandesEnCoursDeSaisie;
          this.totalLivEnAttente = data.commandesEnAttenteDeLivraison;
          this.totalCmdSupporte = data.commandesSupportees;
          this.totalNouvellesOffres = data.nouvellesOffres;
          this.totalOffresActives = data.offresActives;
          this.totalCmdConsome = data.commandesConsommees;
        },
        (error) => {
          console.error('Error fetching dashboard data', error);
        }
      );
  }


  fetchActiveMembersByGroupe(groupeId: number): void {
    this.federationSyndicatService.getTotalActiveMembersByGroupe(groupeId).subscribe({
      next: (totalMembers) => this.totalActiveMembersByGroupe = totalMembers,
      error: (error) => console.error('Failed to get total active members for groupe', error)
    });
  }

  ngAfterViewInit(): void {
    this.swiper = new Swiper(this.swiperRef?.nativeElement?.el, {
      slidesPerView: 1,
      loop: false,
      navigation: false,
      pagination: true,
      scrollbar: false,
      centeredSlides: true,
      lazyPreloadPrevNext: 2,
      autoplay: { delay: 1500, pauseOnMouseEnter: true },
    });
  }

  toggleSwiperAutoplay() {
    const swiperInstance: Swiper = this.swiperRef?.nativeElement?.swiper;

    if (swiperInstance) {
      if (swiperInstance?.autoplay?.running) {
        swiperInstance?.autoplay?.stop();
      } else {
        swiperInstance?.autoplay?.start();
      }
    }
  }

  getListePostes(): void {
    this.posteService.listePlateformes().subscribe(res => {
      const criteria: BlogPostCriteria = {
        ...this.filterCriteria,
        scopes: ['A'],
        plateformePostDtos: [res?.find(item => item?.id === 5)],
      };

      this.posteService
        .searchPosts(criteria, this.paginationParams)
        .subscribe((res) => {
          this.listePostes = this.posteService.addReadMoreAndImageUrl(res);
        });

    })

  }

  listenToDatePickerChanges(): void {
    this.datePicker.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((res) => {
        const selectedDate = new Date(res);

        if (
          selectedDate?.getMonth() <= this.now?.getMonth() ||
          selectedDate?.getFullYear() !== this.now?.getFullYear()
        ) {
          this.displayedDate = selectedDate;
          this.getMyStatistique();
        }
      });
  }

  forwardOneMonth(): void {
    if (
      this.displayedDate?.getMonth() + 1 <= this.now?.getMonth() ||
      this.displayedDate?.getFullYear() !== this.now?.getFullYear()
    ) {
      this.displayedDate.setMonth(this.displayedDate?.getMonth() + 1);
      this.datePicker.setValue(this.getMomentDate(this.displayedDate));
    }
  }

  backwardOneMonth(): void {
    this.displayedDate.setMonth(this.displayedDate?.getMonth() - 1);
    this.datePicker.setValue(this.getMomentDate(this.displayedDate));
  }

  getMomentDate(newDate: Date) {
    return moment(newDate.getFullYear() + '-' + (newDate.getMonth() + 1) + '-' + 1, 'YYYY-MM-DD');
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
