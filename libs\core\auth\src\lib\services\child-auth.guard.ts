import { Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, CanActivateChild, RouterStateSnapshot } from "@angular/router";
import { AuthService } from "./auth.service";
import { HasAccessService, PlateformeService } from "@wph/shared";

@Injectable({ providedIn: 'root' })
export class ChildAuthGuard implements CanActivateChild {
	constructor(
		private authService: AuthService,
		private hasAccesClient: HasAccessService,
		private plateformeService: PlateformeService
	) { }

	canActivateChild(childRoute: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
		if (this.authService.isAuthenticated()) {
			if (this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])) {
				const fournisseursAvecAcces = this.hasAccesClient.getListeFournisseurAvecAcces();

				!fournisseursAvecAcces && this.hasAccesClient.setListeFournisseurAvecAcces();
			}

			if (this.plateformeService.getCurrentPlateforme() !== 'WIN_OFFRE' &&
				childRoute?.url[0]?.path?.includes(this.plateformeService.getPlateformePrefix('WIN_OFFRE'))
			) {
				this.plateformeService.setCurrentPlateforme('WIN_OFFRE');
			}

			return true;
		}
		return false;
	}

}