import { NgModule } from "@angular/core";
import { GroupesRoutingModule } from "./groupes-routing.module";
import { WebSharedModule } from "@wph/web/shared";
import { CommonModule } from "@angular/common";
import { SaisieGroupeComponent } from "./saisie/saisie-groupe.component";
import { ListMemberComponent } from "./membres/list-member/list-member.component";
import { SaisieMemberComponent } from "./membres/saisie-member/saisie-member.component";
import { SharedModule } from "@wph/shared";

@NgModule({
  declarations: [
    ListMemberComponent,
    SaisieGroupeComponent,
    SaisieMemberComponent,
  ],
  imports: [
    GroupesRoutingModule,
    WebSharedModule,
    CommonModule,
    SharedModule,
  ],
})
export class GroupeModule { }
