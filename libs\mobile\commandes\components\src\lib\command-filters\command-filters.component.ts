import {Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation, forwardRef} from '@angular/core';
import { Control<PERSON>ontainer, ControlValueAccessor, FormArray, FormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';
@Component({
  selector: 'wph-command-filters',
  templateUrl: './command-filters.component.html',
  styleUrls: ['./command-filters.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CommandFiltersComponent),
      multi: true
    }
  ],
  encapsulation: ViewEncapsulation.Emulated
})
export class CommandFiltersComponent implements OnInit, ControlValueAccessor {  
  @Input() trigger: string;

  @Input() showCommandStatusOptions: boolean;

  @Output() filterApplied: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Output() dismissed: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input('isOpen') set isOpen(isOpen: boolean) {
    this._isOpen = isOpen;
  }

  get filterControls () {
    return this.parentForm?.controls;
  }

  get isOpen() {
    return this._isOpen;
  }

  _isOpen: boolean;

  parentForm: FormGroup;
  onChange = (_value: any) => {};

  constructor(private controlContainer: ControlContainer) { }

  ngOnInit(): void {
    this.parentForm = this.controlContainer.control as FormGroup;
  }

  writeValue(obj: any): void {}

  registerOnChange(fn: any): void { this.onChange = fn;}

  registerOnTouched(fn: any): void {}

  setDisabledState?(isDisabled: boolean): void {}

  updateControlAtIndex(index: number) {
    const controlAtIndex = (this.filterControls['statusOptions'].value as FormArray).controls[index];
    
    controlAtIndex && controlAtIndex.patchValue({ isChecked: !controlAtIndex?.value?.isChecked });    
  }

  resetStatusCheckedState(): void {
    const commandStatusArray = (this.filterControls['statusOptions'].value as FormArray).controls;

    commandStatusArray && commandStatusArray.map(c => c.patchValue({ isChecked: true }));
  }

  appliquer() {
    this.filterApplied.emit(true);
  }

  vider() {
    this.showCommandStatusOptions && this.resetStatusCheckedState();

    this.parentForm.patchValue({ 
      segment: null,
      dateDebut: new Date().toISOString(), 
      dateFin: new Date().toISOString() 
    });

    this.parentForm.markAsPristine();
    this.filterApplied.emit(false);
  }

}
