import { ProduitDTO } from "libs/commandes-web/commande/src/lib/models/produitDTO";
import { EntrepriseDTO } from "./entreprise.model";
import { Produit } from "@wph/data-access";
import { ProduitDto } from "libs/data-access/src/lib/models/stats.model";


interface IEchangeAchatGroupeDTO {
  id: number;
  clientDonneur : EntrepriseDTO;
  clientReceveur : EntrepriseDTO;
  entBlConsolideId;
  dateCreation: string;
  lignes: IDetailEchangeAchatGroupeDTO[];
}

interface IDetailEchangeAchatGroupeDTO {
  id : number;
  blocOffreId: number;
  produitDto: ProduitDto;
  quantiteEchangee: number;
  qteDonneurAvant: number;
  qteReceveurApres: number;
}


export class EchangeAchatGroupeDTO implements IEchangeAchatGroupeDTO {
  id: number;
  clientDonneur : EntrepriseDTO;
  clientReceveur : EntrepriseDTO;
  entBlConsolideId;
  dateCreation: string;
  lignes: IDetailEchangeAchatGroupeDTO[];
  constructor(partial: Partial<IEchangeAchatGroupeDTO>) {
    Object.assign(this, partial);
  }
}



export class DetailEchangeAchatGroupeDTO implements IDetailEchangeAchatGroupeDTO {
  id : number;
  blocOffreId: number;
  produitDto: ProduitDto;
  quantiteEchangee: number;
  qteDonneurAvant: number;
  qteReceveurApres: number;
  constructor(partial: Partial<IDetailEchangeAchatGroupeDTO>) {
    Object.assign(this, partial);
  }
}
