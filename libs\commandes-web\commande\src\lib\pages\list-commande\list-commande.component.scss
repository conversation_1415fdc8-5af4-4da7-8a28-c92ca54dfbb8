// #bon_commande_list {
//   padding: 10px;
//   .form-inline {
//     width: fit-content;
//   }

//   .actions {
//     display: grid;
//     grid-template-columns: auto max-content;
//     grid-template-rows: 1fr;
//     gap: 5px 5px;
//     grid-template-areas: "s1 s2";
//     .s1 {
//       grid-area: s1;
//       button {
//         transform: translateY(7px);
//       }
//     }
//     .s2 {
//       grid-area: s2;
//     }
//   }

//   .gridbtn {
//     display: flex;
//   }

//   @media only screen and (max-width: 992px) {
//     .actions {
//       display: grid;
//       grid-template-columns: max-content max-content;
//       grid-template-rows: 2fr;
//       gap: 5px 5px;
//       grid-template-areas:
//         "s1"
//         "s2";
//       .s1 {
//         grid-area: s1;
//         button {
//           transform: translateY(7px);
//         }
//       }
//       .s2 {
//         grid-area: s2;
//       }
//     }
//   }

//   @media (max-width: 576px) {
//     .mr-2 {
//       margin-right: unset !important;
//     }
//     .form-group {
//       width: 100% !important;
//     }
//   }
// }

.form-control {
    color: black;
    font-size: 1rem;
    border-radius: var(--winoffre-base-border-radius);
    font-weight: 600;
  }
  
  .modal-footer {
    .btn {
      color: black;
      font-size: 1rem;
      border-radius: var(--winoffre-base-border-radius);
      font-weight: 600;
    }
  }
  
  .input-group {
    .btn {
      border-top-right-radius: var(--winoffre-base-border-radius);
      border-bottom-right-radius: var(--winoffre-base-border-radius);
    }
  }
  
  label {
    color: var(--winoffre-text-light-shade);
    font-weight: 600;
  }
  
  .picker-input {
    .form-control {
      border-radius: var(--winoffre-base-border-radius) !important;
    }
  }
