import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Offre, OffreCriteria, OffresService, Pagination } from '@wph/data-access';
import {
  GridDataResult,
  PageChangeEvent,
  SelectionEvent,
} from '@progress/kendo-angular-grid';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AvisCriteria, AvisDTO, TypeAvis } from '../../../models/avis.model';
import { FsCommandesService } from '../../../services/fs-commandes.service';
import { FederationSyndicatService } from '../../../services/federation-syndicats.service';
import { AuthService } from '@wph/core/auth';
import { FsOffreCriteria } from '../../../models/fs-offre.model';
import { FormControl } from '@angular/forms';
import { debounceTime, lastValueFrom } from 'rxjs';
import { PlateformeService, UploadFileServiceService } from '@wph/shared';
import { SelectedPlateforme } from '@wph/web/layout';

interface SondageOffre {
  offre: Offre;
  positive: number;
  notInterested: number;
}


interface CacheEntry {
  positive: AvisDTO[];
  negative: AvisDTO[];
}

@Component({
  selector: 'wph-sondages-liste',
  templateUrl: './sondages-liste-groupes.component.html',
  styleUrls: ['./sondages-liste-groupes.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SondagesGroupesListeComponent implements OnInit {
  defaultImageUrl = 'path/to/default-image.jpg';
  pageSizes: number[] = [5, 10, 15, 20];
  navigation = {
    pageSize: 15,
    skip: 0,
    sortMethod: 'desc'
  };
  gridData: GridDataResult = { total: 0, data: [] };
  groupeSort: SortDescriptor[] = [];
  monGroupe: any;
  membreId: number; 
  searchCriteria: AvisCriteria = { estResponsable: true };
  isInterested: boolean = true;
  sondages: SondageOffre[] = [];
  selectedSondage: SondageOffre | null = null;
  searchControl: FormControl = new FormControl('');
  filteredSondages: SondageOffre[] = [];
  avisCache: { [offreId: number]: CacheEntry } = {};
  loadingPercentages: { [offreId: number]: boolean } = {};
  currentPlateforme: SelectedPlateforme;

  constructor(
    private authService: AuthService,
    private offreService: OffresService,
    private avisService: FsCommandesService,
    private plateformeService: PlateformeService,
    private uploadService: UploadFileServiceService,
    private fedSyndicatService: FederationSyndicatService,
  ) {
    this.currentPlateforme = this.plateformeService.getCurrentPlateforme();
  }

  ngOnInit(): void {
    this.membreId = this.authService.getPrincipal()?.societe?.id;
    this.searchOffres();

    this.searchControl.valueChanges.pipe(
      debounceTime(300) 
    ).subscribe(value => {
      this.filteredSondages = this.sondages.filter(sondage =>
        sondage.offre.titre.toLowerCase().includes(value.toLowerCase())
      );
    });
  }

  searchOffres(): void {
    const criteria = new OffreCriteria();
    criteria.statut = ['P'];
    
    const pagination = { pageSize: 100, skip: 0, sortMethod: 'desc' };
    
    this.offreService.searchOffres(pagination, criteria).subscribe((response) => {
      this.sondages = response.content.map(offre => ({
        offre: offre,
        positive: null,
        notInterested: null
      }));
      this.filteredSondages = [...this.sondages]; 
    
    });
  }

  async loadAvis(offreCmdId?: number): Promise<void> {
    if (this.avisCache[offreCmdId]) {
      this.updateGridData(this.avisCache[offreCmdId]);
      this.calculateInterestPercentage(offreCmdId); // Ensure percentage calculation is called
      return;
    }

    const positiveCriteria: AvisCriteria = {
      ...this.searchCriteria,
      offreCmdId: offreCmdId,
      typeAvis: TypeAvis.Positive
    };

    const negativeCriteria: AvisCriteria = {
      ...this.searchCriteria,
      offreCmdId: offreCmdId,
      typeAvis: TypeAvis.Negative
    };

    try {
      const [positiveResponse, negativeResponse] = await Promise.all([
        lastValueFrom(this.avisService.rechercherAvisParCritere(positiveCriteria)),
        lastValueFrom(this.avisService.rechercherAvisParCritere(negativeCriteria))
      ]);

      const positiveAvis = positiveResponse.content;
      const negativeAvis = negativeResponse.content;

      this.avisCache[offreCmdId] = {
        positive: positiveAvis,
        negative: negativeAvis
      };

      this.updateGridData(this.avisCache[offreCmdId]);
      this.calculateInterestPercentage(offreCmdId);
    } catch (error) {
      console.error('Error fetching Avis:', error);
      this.gridData = {
        total: 0,
        data: []
      };
    }
  }

  updateGridData(cacheEntry: CacheEntry): void {
    const avisData = this.isInterested ? cacheEntry.positive : cacheEntry.negative;
    this.gridData = {
      total: avisData.length,
      data: avisData.slice(this.navigation.skip, this.navigation.skip + this.navigation.pageSize),
    };
  }


  onSwitchChange(): void {
    if (this.selectedSondage) {
      this.updateGridData(this.avisCache[this.selectedSondage.offre.id]);
    }
  }

  pageChange(event: PageChangeEvent): void {
    this.navigation.skip = event.skip;
    this.navigation.pageSize = event.take;
    if (this.selectedSondage) {
      this.updateGridData(this.avisCache[this.selectedSondage.offre.id]);
    }
  }

  sortChange(sort: SortDescriptor[]): void {
    this.groupeSort = sort;
    if (sort.length) {
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortMethod = null;
    }
    if (this.selectedSondage) {
      this.updateGridData(this.avisCache[this.selectedSondage.offre.id]);
    }
  }

  selectSondage(sondage: SondageOffre): void {
    this.selectedSondage = this.selectedSondage === sondage ? null : sondage;
    if (this.selectedSondage) {
      this.calculateInterestPercentage(this.selectedSondage.offre.id);
      this.loadAvis(this.selectedSondage.offre.id);
    }
  }

  toggleSondage(sondage: SondageOffre): void {
    this.selectedSondage = this.selectedSondage === sondage ? null : sondage;
    if (this.selectedSondage) {
      this.calculateInterestPercentage(this.selectedSondage.offre.id);
      this.loadAvis(this.selectedSondage.offre.id);
    }
  }

 
  calculateInterestPercentage(offreCmdId: number): void {
    const cacheEntry = this.avisCache[offreCmdId];
    if (cacheEntry) {
      const positiveCount = cacheEntry.positive.length;
      const negativeCount = cacheEntry.negative.length;
      const totalCount = positiveCount + negativeCount;
  
      this.updateSondageInterest(offreCmdId, positiveCount, negativeCount, totalCount);
    }
  }
  updateSondageInterest(offreId: number, positiveCount: number, negativeCount: number, totalCount: number): void {
    const sondage = this.sondages.find(s => s.offre.id === offreId);
    if (sondage) {
      sondage.positive = totalCount > 0 ? parseFloat((positiveCount / totalCount * 100).toFixed(2)) : 0;
      sondage.notInterested = totalCount > 0 ? parseFloat((negativeCount / totalCount * 100).toFixed(2)) : 0;
    } else {
      console.warn(`Sondage for offre ${offreId} not found.`);
    }
  }
  OnPageChange(event: number): void {
    if (this.selectedSondage) {
      this.updateGridData(this.avisCache[this.selectedSondage.offre.id]);
    }
  }

  getImageUrl(sondage: SondageOffre): string | null {
    return sondage.offre?.docImageOffre?.idhash
      ? this.uploadService.fetchUploadedDocument(sondage.offre.docImageOffre.idhash)
      : null;
  }
}






