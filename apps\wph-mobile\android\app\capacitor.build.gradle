// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_11
      targetCompatibility JavaVersion.VERSION_11
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-push-notifications')
    implementation project(':capacitor-app')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-browser')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
