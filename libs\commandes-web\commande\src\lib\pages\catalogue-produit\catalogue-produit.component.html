<div id="catalogue" class="p-0">
    <!-- start page title -->
    <div class="row rowline mb-0 bg-transparent">
        <div class="page-title-box row px-3">
            <h4 class="page-title fw-4 ps-2 col-12">Catalogue Produits</h4>
        </div>
    </div>
    <!-- end page title -->

    <div style="z-index: 1">
        <div class="card m-0">
            <div class="card-header">
                <div class="left">
                    <div class="search input-group picker-input">
                        <input type="text" id="goSearchCatalogue"
                            placeholder="Rechercher par désignation, prix, ou code barre de produit"
                            class="form-control b-radius pl-4" [formControl]="searchData" (keydown.enter)="goSearch()"
                            ngbTooltip="Rechercher par désignation, prix, ou code barre de produit" triggers="manual"
                            [autoClose]="false" (focus)="myTooltip.open()" (blur)="myTooltip.close()"
                            placement="bottom-left" tooltipClass="custom-tooltip-style" #myTooltip="ngbTooltip" />

                        <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
                    </div>
                </div>
            </div>
            <kendo-grid [data]="gridView" (pageChange)="pageChange($event)" [pageSize]="navigation.pageSize"
                [skip]="navigation.skip" [pageable]="{
                    buttonCount: 5,
                    info: true,
                    type: 'numeric',
                    pageSizes: pageSizes,
                    previousNext: true,
                    position: 'bottom'
                  }" [sortable]="{mode: 'single'}" [sort]="gridSort" (sortChange)="gridSortChange($event)"
                [groupable]="false" [reorderable]="true" class="grid-height" [resizable]="true">

                <kendo-grid-column media="(max-width: 768px)" title="Catalogue Produits">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <!---  Mobile Column Template  --->
                        <dl>
                            <dt class="my-2 limited-width">Code Produit: <span>{{ dataItem?.codeProduit }}</span></dt>

                            <dt class="my-2 limited-width">Libellé: <span>{{ dataItem?.libelle }}</span></dt>

                            <dt class="my-2 limited-width">PPH: <span>{{ dataItem?.pph | number: '1.2-2':'fr-FR'
                                    }}</span></dt>

                            <dt class="my-2 limited-width">PPV: <span>{{ dataItem?.ppv | number: '1.2-2':'fr-FR'
                                    }}</span></dt>

                            <dt class="my-2 limited-width">Fabriquant: <span>{{ dataItem?.libelleLabo }}</span></dt>

                            <dt class="my-2 limited-width">Quantité:
                                <span class="ml-2">
                                    <up-down-input [start]="dataItem?.qteCmd" [targetID]="dataItem?.codeProduit"
                                        (value)="getCommandeNumber($event)">
                                    </up-down-input>
                                </span>
                            </dt>

                            <dt class="my-2 limited-width">Disponibilité:
                                <span>
                                    <span *ngIf="dataItem?.disponibiliteCode" class="ml-2 mdi mdi-circle mdi-14px {{
                                        dataItem?.disponibiliteCode[0] === 'A'
                                            ? 'text-success'
                                            : dataItem?.disponibiliteCode[0] === 'B'
                                            ? 'text-warning'
                                            : dataItem?.disponibiliteCode[0] === 'C'
                                            ? 'text-warning'
                                            : 'text-danger'
                                        }}"></span>
                                    <span *ngIf="dataItem?.disponibiliteCode" class="ml-1">
                                        {{ dataItem?.disponibiliteLibelle || (dataItem?.disponibiliteCode |
                                        produitDispo)
                                        }}</span>
                                </span>
                            </dt>

                            <dt class="action-btns">
                                <div class="d-flex row mx-0 justify-content-start">
                                    <button class="circle circle-alt btn btn-success text-white"
                                        (click)="dispoCheck(dataItem)" title="Disponibilité de la quantité">
                                        <i class="mdi mdi-magnify"></i>
                                    </button>

                                    <button *jhiHasAnyServiceOption="['PASSER_COMMANDE']"
                                        (click)="commanderProduit(dataItem)" title="Ajouter au panier"
                                        class="circle circle-alt btn btn-primary text-white mt-2">
                                        <i class="mdi mdi-cart-plus"></i>
                                    </button>

                                    <button (click)="navige(dataItem?.id)" title="Détails"
                                        class="circle circle-alt btn btn-warning text-white mt-2">
                                        <i class="mdi mdi-eye"></i>
                                    </button>
                                </div>
                            </dt>

                        </dl>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column media="(min-width: 769px)" field="code" title="Code Produit" [width]="120">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <div class="text-left">
                            {{ dataItem?.codeProduit }}
                        </div>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column media="(min-width: 769px)" class="text-wrap" field="libelle" title="Libellé"
                    [width]="300"></kendo-grid-column>

                <kendo-grid-column media="(min-width: 769px)" field="pph" title="PPH" [width]="120"
                    [class]="{ 'text-center': true }" [resizable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <div class="text-right">
                            {{ dataItem?.pph | number: '1.2-2':'fr-FR' }}
                        </div>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column media="(min-width: 769px)" field="ppv" title="PPV" [width]="120"
                    [class]="{ 'text-center': true }" [resizable]="false" filter="boolean">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <div class="text-right">
                            {{ dataItem?.ppv | number: '1.2-2':'fr-FR' }}
                        </div>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column media="(min-width: 769px)" class="text-wrap" field="fournisseur.frn_rai_soc"
                    title="Fabriquant" [width]="180" [resizable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        {{ dataItem?.libelleLabo | uppercase }}
                    </ng-template>

                </kendo-grid-column>

                <kendo-grid-column media="(min-width: 769px)" title="Action" [width]="400" [sortable]="false"
                    [resizable]="false">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <div class="gridbtn d-flex align-items-center">
                            <up-down-input [start]="dataItem?.qteCmd" [targetID]="dataItem?.codeProduit"
                                (value)="getCommandeNumber($event)">
                            </up-down-input>

                            <button class="circle btn btn-success text-white mx-1" (click)="dispoCheck(dataItem)"
                                title="Disponibilité de la quantité">
                                <i class="mdi mdi-magnify"></i>
                            </button>

                            <button *jhiHasAnyServiceOption="['PASSER_COMMANDE']" (click)="commanderProduit(dataItem)"
                                title="Ajouter au panier" class="circle btn btn-primary text-white mx-1">
                                <i class="mdi mdi-cart-plus"></i>
                            </button>

                            <button (click)="navige(dataItem?.id)" title="Détails"
                                class="circle btn btn-warning text-white mx-1">
                                <i class="mdi mdi-eye"></i>
                            </button>

                            <span *ngIf="dataItem?.disponibiliteCode" class="ml-2 mdi mdi-circle mdi-14px {{
                                dataItem?.disponibiliteCode[0] === 'A'
                                    ? 'text-success'
                                    : dataItem?.disponibiliteCode[0] === 'B'
                                    ? 'text-warning'
                                    : dataItem?.disponibiliteCode[0] === 'C'
                                    ? 'text-warning'
                                    : 'text-danger'
                                }}"></span>
                            <span *ngIf="dataItem?.disponibiliteCode" class="ml-2 text-wrap">
                                {{ dataItem?.disponibiliteLibelle || (dataItem?.disponibiliteCode | produitDispo)
                                }}</span>
                        </div>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
                    pagerItemsPerPage="éléments par page"></kendo-grid-messages>

                <ng-template kendoGridNoRecordsTemplate>
                    <span>Aucun résultat trouvé.</span>
                </ng-template>
            </kendo-grid>
        </div>
    </div>
</div>