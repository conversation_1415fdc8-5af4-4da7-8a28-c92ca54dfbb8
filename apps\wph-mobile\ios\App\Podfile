platform :ios, '13.0'
use_frameworks!

# workaround to avoid Xcode caching of Pods that requires
# Product -> Clean Build Folder after new Cordova plugins installed
# Requires CocoaPods 1.6 or newer
install! 'cocoapods', :disable_input_output_paths => true

def capacitor_pods
  pod 'Capacitor', :path => '../../../../node_modules/@capacitor/ios'
  pod 'CapacitorCordova', :path => '../../../../node_modules/@capacitor/ios'
  pod 'CapacitorPushNotifications', :path => '../../../../node_modules/@capacitor/push-notifications'
  pod 'CapacitorApp', :path => '../../../../node_modules/@capacitor/app'
  pod 'CapacitorSplashScreen', :path => '../../../../node_modules/@capacitor/splash-screen'
  pod 'CapacitorBrowser', :path => '../../../../node_modules/@capacitor/browser'
  pod 'CordovaPlugins', :path => '../capacitor-cordova-ios-plugins'
end

target 'App' do
  capacitor_pods
  # Add your Pods here
end
