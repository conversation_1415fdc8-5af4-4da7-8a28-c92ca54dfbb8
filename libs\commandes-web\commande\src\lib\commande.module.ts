import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CatalogueProduitComponent } from './pages/catalogue-produit/catalogue-produit.component';
import { SharedModule } from '@wph/commandes-web/shared';
import { ListCommandeComponent } from './pages/list-commande/list-commande.component';
import { ListNouveauxProduitsComponent } from './pages/list-nouveaux-produits/list-nouveaux-produits.component';
import { CommandeRoutingModule } from './commande-routing.module';
import { EditCommandeComponent } from './pages/edit-commande/edit-commande.component';
import { FicheProduitComponent } from './pages/fiche-produit/fiche-produit.component';
import { BonCommandeComponent } from './pages/bon-commande/bon-commande.component';
import { BlCommandeComponent } from './pages/bl-commande/bl-commande.component';
import { ProduitDispoPipe } from './pipes/produit-dispo.pipe';
import { Select2Module } from 'ng-select2-component';
import { ListCommandesNormalesComponent } from './pages/list-commandes-normales/list-commandes-normales.component';
import { HasAnyAuthorityDirective, HasAnyServiceOptionDirective, WebSharedModule } from '@wph/web/shared';
import { PageTypePipe } from './pipes/page-type.pipe';
import { TraitementBlPipe } from './pipes/traitement-bl.pipe';
import { AlerteProduitsComponent } from './pages/alerte-produit/alerte-produits.component';
import { ReclamationsComponent } from './pages/reclamation/reclamations.component';

@NgModule({
  declarations: [
    CatalogueProduitComponent,
    ListCommandeComponent,
    ListNouveauxProduitsComponent,
    AlerteProduitsComponent,
    EditCommandeComponent,
    FicheProduitComponent,
    ProduitDispoPipe,
    ListCommandesNormalesComponent,
    BonCommandeComponent,
    BlCommandeComponent,
    PageTypePipe,
    TraitementBlPipe,
    ReclamationsComponent
  ],
  imports: [
    HasAnyAuthorityDirective,
    HasAnyServiceOptionDirective,
    CommonModule,
    SharedModule,
    CommandeRoutingModule,
    Select2Module,
    WebSharedModule
  ],
})
export class CommandeModule {}
