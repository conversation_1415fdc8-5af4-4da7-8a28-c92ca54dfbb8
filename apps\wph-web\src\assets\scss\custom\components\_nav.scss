//
// tabs.scss
//

#DEFAULT-container {

    .nav-tabs {
        width: 100%;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }

    .nav-tabs li{
      flex: 1;
    }

    .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
        background: var(--win-offre-bg-light-2) !important;
        color: var(--win-offre-primary) !important;
        border-color: var(--win-offre-primary-tint) !important;
        border-bottom-width: 2px;
        font-weight: 600;
        height: 100%;
        margin: 0 !important;
        border-top-left-radius: var(--winoffre-base-border-radius);
        border-top-right-radius: var(--winoffre-base-border-radius);
    }

    .nav-tabs,
    .nav-pills,
    .nav-bordered {
        >li {
            >a {
                color: black;
                height: 100%;
                font-weight: $font-weight-semibold;
            }
        }
    }
}

#COMMANDE_WEB-container {
    .nav-tabs {
        width: 100%;
        border-top-left-radius: var(--winoffre-base-border-radius);
        border-top-right-radius: var(--winoffre-base-border-radius);
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        gap: 20px;
    }

    .nav-tabs li{
      flex: 1;
    }
    
    .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
        background: var(--cw-primary-100) !important;
        color: var(--cw-primary-800) !important;
        border-color: var(--cw-primary-800) !important;
        border-bottom-width: 2px;
        font-weight: 600;
        border-top-left-radius: var(--winoffre-base-border-radius);
        border-top-right-radius: var(--winoffre-base-border-radius);
    }

    .nav-tabs,
    .nav-pills,
    .nav-bordered {
        >li {
            >a {
                color: black;
                font-weight: $font-weight-semibold;
            }
        }
    }
}

#WIN_OFFRE-container {
    .nav-tabs {
        width: 100%;
        border-top-left-radius: var(--winoffre-base-border-radius);
        border-top-right-radius: var(--winoffre-base-border-radius);
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        gap: 20px;
    }

    .nav-tabs li{
      flex: 1;
    }

    .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
        background: var(--wo-primary-100) !important;
        color: var(--wo-primary-800) !important;
        border-color: var(--wo-primary-800) !important;
        border-bottom-width: 2px;
        font-weight: 600;
        margin: 0 !important;
        border-top-left-radius: var(--winoffre-base-border-radius);
        border-top-right-radius: var(--winoffre-base-border-radius);
    }

    .nav-tabs,
    .nav-pills,
    .nav-bordered {
        >li {
            >a {
                color: black;
                font-weight: $font-weight-semibold;
            }
        }
    }
}

#FEDERATION_SYNDICAT-container, #WIN_GROUPE-container {
    .nav-tabs {
        width: 100%;

        border-top-left-radius: var(--winoffre-base-border-radius);
        border-top-right-radius: var(--winoffre-base-border-radius);
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        gap: 20px;
    }

    .nav-tabs li{
      flex: 1;
    }


    .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
        background: transparent !important;
        color: black !important;
        border-color: transparent !important;
        // border-left: 1px solid #dcdcdc !important;
        // border-right: 1px solid #dcdcdc !important;
        font-weight: 600;
        margin: 0 !important;
    }
    .nav-tabs .nav-link.active-tab{
        background:rgba(142, 182, 101, 0.37) !important;
        color: #fff !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    .nav-tabs .nav-link.active-tab-alt {
        color: rgb(233, 171, 0);
      background: rgba(233, 171, 0, 0.47);
      color: #000000a8 !important;
      margin: 0 !important;
      height: 100%;
  }

    .nav-tabs,
    .nav-pills,
    .nav-bordered {
        >li {
            >a {
                color: black;
                font-weight: 600;
                height: 100%;
            }
        }
    }
}

#FEDERATION_SYNDICAT-container .nav-tabs .nav-link.active-tab-alt {
 color: #fff !important;
  background: rgba(103, 105, 178, 0.47);
  margin: 0 !important;
  height: 100%;
}

.nav-tabs,
.nav-pills,
.nav-bordered {
    >li {
        >a {
            color: $gray-700;
            font-weight: $font-weight-semibold;
        }
    }
}

.nav-pills {
    >a {
        color: $gray-700;
        font-weight: $font-weight-semibold;
    }
}

.bg-nav-pills {
    background-color: $nav-pills-bg;
}

//
// nav-bordered
//

.nav-bordered {
    border-bottom: 2px solid rgba($gray-600, 0.2);

    .nav-item {
        margin-bottom: -2px;
    }

    li {
        a {
            border: 0;
            padding: 0.625rem 1.25rem;

            &.disabled {
                color: $nav-link-disabled-color;
                background-color: transparent;
                border-color: transparent;
            }

            &.active {
                border-bottom: 2px solid $primary;
                color: $nav-tabs-link-active-color;
                background-color: $nav-tabs-link-active-bg;
            }
        }
    }
}
