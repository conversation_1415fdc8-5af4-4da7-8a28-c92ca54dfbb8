.isWinOffre {
  width: 100%;
  height: auto;
  overflow-y: scroll;
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.image-container {

  div {
    bottom: 6px;
    width: 98.5%;
    left: 7px;
    height: 13%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 25px;
  }

  img {
    overflow: hidden;
    position: relative;
    width: 100%;
    height: auto;
    max-height: 380px;
    object-fit: contain;
  }

}

.b-radius {
  border-radius: 10px;
}

.filter-card {
  width: auto;
  margin: 0 auto;
}

.poste-card, .filter-card {
  // max-width: 650px;
  margin: 0 auto;
}

.logo-img-container {
  max-height: 60px;
  width: 100%;
  object-fit: contain;
}

.title-cstm {
    font-size: 1.2rem;
    font-weight: 900;
    color: #c08911;
  }

.info-card {
  min-height: 220px;
  max-height: 450px;
  overflow: hidden;
  border-radius: 10px;
  background: #fff;

  .title {
    font-size: 1.2rem;
    font-weight: 900;
    color: #c08911;
  }
  
  .icon-filter {
    color: #c08911;
  }

  transition: min-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-card-height-override {
  max-height: calc((100vh - 190px) * 2/3) !important;
}

.info-card-alt {
  min-height: 220px;
  overflow: hidden;
  border-radius: 10px;
  background: #fff;

  .title {
    font-size: 1.3rem;
    font-weight: 900;
    color: #c08911;
  }
  
  .icon-filter {
    color: #c08911;
  }
}

.sw-container {
  width: calc(100% + 10px);
}

.border-left-cstm {
  border-left: 3px solid var(--win-offre-primary);
}

@media (max-width: 991px) {
  .info-card-height-override {
    max-height: none !important;
  }
}

@media (min-width: 992px) and (max-width: 1550px) {
  .info-card-height-override {
    max-height: 510px !important;
  }
}

.carousel-container {
  height: auto;
  width: 100%;
  max-height: 380px;
}

.pin-icon {
  transform: rotate(45deg) !important;
  margin-top: -5px;
}

.message-text {
  font-size: 1.1rem;
  color: #333;
  line-height: 1.5;
}

.text-only-template {
  h5.title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
  }

  p.message-text {
    font-size: 1rem;
    color: #333;
  }
}

.force-auto-height {
  height: auto !important;
  min-height: 0 !important;
}