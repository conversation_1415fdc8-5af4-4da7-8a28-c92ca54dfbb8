import { Component, HostListener, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import {
  CellClickEvent,
  GridDataResult,
  PageChangeEvent,
  SelectionEvent,
} from '@progress/kendo-angular-grid';
import { debounceTime, distinctUntilChanged, finalize, Subject, takeUntil } from 'rxjs';
import {
  Commande,
  CommandeCriteria,
  FilterOutput,
  Fournisseur,
  OffresService,
  Pagination,
} from '@wph/data-access';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AuthService } from '@wph/core/auth';
import { getDynamicPageSize } from '@wph/web/shared';

@Component({
  selector: 'wph-list-commandes',
  templateUrl: './list-commandes.component.html',
  styleUrls: ['./list-commandes.component.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class ListCommandesComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  commandeForm: FormGroup;
  commandeCriteria: CommandeCriteria;
  ObjCommandes: any;
  listCommandes: Commande[];
  commandeSort: SortDescriptor[];
  selectedcommande: Commande;
  selectedIdsCommandes: number[] = [];
  gridData: GridDataResult;
  listeClients: Fournisseur[] | null = null;
  isSeleceted = false;
  printing: boolean;
  blobUrl: string;
  forceCloseDropdowns: boolean = false;
  societe: Fournisseur;

  defaultData: Commande[];
  displayFilter: boolean;
  isNotRoleAgentPointVente: boolean = false;

  searchControl = new FormControl();
  pageSizes: number[] = [5, 10, 15, 20];

  navigation: Pagination = {
    skip: 0,
    pageSize: 20,
    sortField: null,
    sortMethod: null,
  };

  constructor(
    private router: Router,
    private modalService: NgbModal,
    private authService: AuthService,
    private offresService: OffresService
  ) { }

  ngOnInit() {
    this.setPageSize();

    this.initFormmessage();

    this.SearchCommandes();

    this.listenToSearchControlChanges();

    this.societe = this.authService.getPrincipal()?.societe;
  }

  setPageSize(currentHeight?: number): void {
    const dynamicSize = getDynamicPageSize(currentHeight, 49);

    if (dynamicSize !== this.navigation.pageSize) {
      this.navigation.pageSize = dynamicSize;

      this.pageSizes.push(dynamicSize);
      this.pageSizes = this.pageSizes.sort((a, b) => a - b);

      currentHeight && this.SearchCommandes();
    }
  }


  loadListCommandes() {
    this.offresService.getListeCommandes(this.navigation).subscribe((res) => {
      this.ObjCommandes = res;
      this.listCommandes = res['content'];

      this.loadItems();
    });
  }

  listenToSearchControlChanges(): void {
    this.searchControl.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe((searchQuery: string) => {
        this.navigation.skip = 0;
        this.commandeCriteria = new CommandeCriteria({ ...this.commandeCriteria, titre: searchQuery });

        this.SearchCommandes(true);
      });
  }

  private initFormmessage() {
    this.commandeForm = new FormGroup({
      codeCommande: new FormControl(null, []),
      titre: new FormControl(null, []),
      dateValidationDebut: new FormControl(null, []),
      dateValidationFin: new FormControl(null, []),
      fournisseur: new FormControl(null, []),
      laboratoire: new FormControl(null, []),
      dateDebutCommande: new FormControl(null, []),
      dateFinCommande: new FormControl(null, []),
      venteDirecteLabo: new FormControl(null, []),
      statut: new FormControl(null, []),
      client: new FormControl(null, []),
      nonExpireesUniquement: new FormControl(false, []),
    });

  }

  SearchCommandes(isFilter: boolean = false) {
    if (this.commandeForm.valid) {
      this.commandeCriteria = new CommandeCriteria(this.commandeCriteria);
      this.commandeCriteria.dateDebutCommande = this.commandeForm.value.dateDebutCommande;
      this.commandeCriteria.dateFinCommande = this.commandeForm.value.dateFinCommande;
      this.commandeCriteria.codeCommande = this.commandeForm.value.codeCommande;
      this.commandeCriteria.dateValidationDebut = this.commandeForm.value.dateValidationDebut;
      this.commandeCriteria.dateValidationFin = this.commandeForm.value.dateValidationFin;
      this.commandeCriteria.nonExpireesUniquement = this.commandeForm.value.nonExpireesUniquement ? 'O' : null;
      this.commandeCriteria.fournisseur = this.commandeForm.value.fournisseur;
      this.commandeCriteria.laboratoire = this.commandeForm.value.laboratoire;
      this.commandeCriteria.venteDirecteLabo = this.commandeForm.value.venteDirecteLabo ? 'O' : null;
      this.commandeCriteria.statut = this.commandeForm.value.statut;
      this.commandeCriteria.client = this.commandeForm.value.client;

      if (isFilter) {
        this.navigation.skip = 0;
      }

      this.offresService.searchCommandes(this.commandeCriteria, this.navigation)
        .pipe(finalize(() => {
          this.isSeleceted = false;
          this.selectedcommande = null;
        }))
        .subscribe(
          (data: any) => {
            isFilter && this.modalService?.dismissAll();

            this.ObjCommandes = data;
            this.listCommandes = data['content']

            this.loadItems();
          }
        );
    }
  }

  pageChange(event: PageChangeEvent): void {
    if (event.skip !== this.navigation.skip || event?.take !== this.navigation.pageSize) {
      this.isSeleceted = false;
      this.selectedcommande = null;

      this.navigation.skip = event.skip;
      this.navigation.pageSize = event?.take
      this.SearchCommandes();
    }
  }

  private loadItems(): void {
    this.gridData = {
      data: this.ObjCommandes['content'],
      total: this.ObjCommandes.totalElements
    };
  }

  chooseCommandes(item: SelectionEvent) {
    //////
    if (item.selectedRows && item.selectedRows.length) {
      if (item.selectedRows && item.selectedRows.length) {
        this.selectedcommande = this.ObjCommandes.content.find(
          (prd) => prd.id === item.selectedRows[0].dataItem.id
        );

        this.isSeleceted = true;
        this.selectedIdsCommandes.push(this.selectedcommande.id);
      }

      if (item.deselectedRows && item.deselectedRows.length) {
        const indexToRemove: number = this.selectedIdsCommandes.findIndex(
          (prd) => prd === item.deselectedRows[0].dataItem.id
        );

        this.selectedIdsCommandes.splice(indexToRemove, 1);
      }
    } else {
      this.isSeleceted = false;
    }
  }

  Print() {
    this.offresService
      .printCommandes(this.selectedIdsCommandes)
      .subscribe((data) => {
        //const blobUrrl = URL.createObjectURL(data);
        this.blobUrl = data;
        //const iframe = document.createElement('iframe');
        //iframe.style.display = 'none';
        //iframe.src = blobUrl;
        //document.body.appendChild(iframe);
        //iframe.contentWindow.print();
        this.printing = true;
      });
  }

  editCommande(commande?: Commande) {
    this.router.navigateByUrl('/win-offre/commandes/edit/' + commande?.id);
  }

  consulterCommande(commande?: Commande) {
    this.router.navigate(['/win-offre/commandes/edit/' + commande?.id], {
      queryParams: { readOnly: 'true' },
    });
  }

  vider() {
    if (this.commandeForm.dirty && this.commandeForm.touched) {
      this.commandeForm.reset({ client: null, nonExpireesUniquement: false });

      this.commandeSort = null;
      this.isSeleceted = false;

      this.SearchCommandes();
    }
    this.modalService.dismissAll();
  }

  openFilterModal(modalContent: any, size: string) {
    this.modalService
      .open(modalContent, {
        ariaLabelledBy: 'modal-basic-title',
        size,
        backdrop: 'static',
      })
      .result.then(
        (result) => {
          console.log(result);
        }, () => null
      );
  }

  commandeSortChange(sort: SortDescriptor[]): void {
    this.commandeSort = sort;
    if (
      this.commandeSort &&
      this.commandeSort.length > 0 &&
      this.commandeSort[0].dir
    ) {
      this.navigation.sortField = sort[0].field;
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
    }

    this.SearchCommandes();
  }

  filterModalAction(filterAction: FilterOutput): void {
    filterAction?.clear && this.vider();
    filterAction?.filter && this.SearchCommandes(true);
  }

  cellClick(event: CellClickEvent): void {
    if (event?.column?.title !== 'Actions') {
      this.consulterCommande(event?.dataItem);
    }
  }

  closeDropdowns() {
    this.forceCloseDropdowns = true;
    setTimeout(() => {
      this.forceCloseDropdowns = false;
    }, 200);
  }

  reload() {
    this.isSeleceted = false;
    this.selectedcommande = null;
    this.SearchCommandes();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
