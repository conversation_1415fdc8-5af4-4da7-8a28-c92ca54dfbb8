<ng-container *ngTemplateOutlet="pages;context:{$implicit: item}"></ng-container>

<!-- MODAL -->
<ion-modal id="example-modal" #modal>
  <ng-template>
    <div class="wrapper">
      <ion-list class="bg-transparent" line="none">
        <ion-item *ngFor="let palier of paliersData; let i = index" class="ion-padding-end palier-item-height">
          <span class="ion-padding-start ion-flex palier-item w-100">
            <span>Palier {{i + 1}}: </span>
            <span *ngIf="palier.qteMin"> {{palier.qteMin}}</span>
            <span *ngIf="palier.valeurMin"> {{palier.valeurMin | number: '1.2-2'}}</span>
            <span> {{palier?.tauxRf }}%</span>
          </span>
        </ion-item>
      </ion-list>
    </div>
  </ng-template>
</ion-modal>


<ng-template #pages let-dataItem>

  <!-- FIRST PAGE  -->
  <div class="page-wrapper w-100" *ngIf="!dataItem.id">

    <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
      <div class="warning-container"
        *ngIf="(!offre?.commandStatut || offre?.commandStatut === 'BROUILLON') && !selectedClient">
        <span>Veuillez selectionner le client destiné de cette commande</span>
      </div>
    </ng-container>

    <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
      <div class="warning-container"
        *ngIf="(!offre?.commandStatut || offre?.commandStatut === 'BROUILLON') && selectedClient && !selectedClientLocal">
        <span>Veuillez selectionner le client local destiné de cette commande</span>
      </div>
    </ng-container>

    <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']">
      <div class="warning-container" *ngIf="!hasAccess">
        <ion-row>
          <ion-col size="12">
            <ion-icon name="warning" color="light" size="small"></ion-icon>
          </ion-col>

          <ion-col size="12">
            <span>Vous n'avez pas l'accès au fournisseur spécifié. Veuillez sélectionner un fournisseur auquel vous avez
              accès.</span>
          </ion-col>
        </ion-row>
      </div>
    </ng-container>


    <!-- GEENRAL PACK (G) TITLE & EXTRA INFOS -->
    <!--<ion-list id="general-info-list" lines="none" class="w-100 mt-list">
      <ion-item class="padding-start-list">
        <div class="title-wrapper ion-padding-horizontal">
          <ion-label class="ion-text-wrap"><h2 class="font-bold pack-title">{{ dataItem.title }}</h2></ion-label>
          <ion-badge
            [ngClass]="{'via-direct': dataItem.distributeur,'via-special': !dataItem.distributeur }"
            class="ion-float-start badge-option-lg ">{{ dataItem.via }}</ion-badge>
          <ion-badge [ngClass]="{'type-gold':dataItem.type === 'RF','type-noraml':dataItem.type === 'UG' }"
                     class="ion-float-end badge-option-xsm">{{ dataItem.type }}</ion-badge>
        </div>
      </ion-item>
    </ion-list> -->


    <!-- GEENRAL PACK (G) LIST OF PACKS -->
    <div class="details-wrapper">
      <ion-list lines="none" class="w-100 scrollable-list" [ngClass]="{'padding-lg': readOnly && offre?.commandStatut}">
        <!-- COMMANDE DETAILS -->
        <ion-item lines="none" class="cmd-details" *ngIf="readOnly && offre?.commandStatut">
          <div class="wrapper-item ion-padding-horizontal">
            <div class="inner-wrapper-item">
              <span class="date-wrapper-child">N° : {{ offre?.codeCommande }}</span>
              <span class="badge-cmd">
                <ion-badge [class]="offre?.commandStatut | statutBadgeColor">
                  {{ offre?.commandStatut | commandeStatut }}
                </ion-badge>
              </span>
            </div>

            <div class="inner-wrapper-item">
              <span class="date-wrapper-child">Créée le : {{ $any(offre?.dateCreationCmd) | date: 'dd/MM/yyyy HH:mm'
                }}</span>

              <span class="date-wrapper-child">
                {{
                offre?.commandStatut | commandeStatut }} le : {{ offre?.commandStatut === 'NOUVELLE' ?
                ($any(offre?.dateConfirmation ) | date: 'dd/MM/yyyy HH:mm') :
                ($any(offre?.dateAcceptation) || $any(offre?.dateRefus )|| $any(offre?.dateSuppressionCmd) ||
                $any(offre?.dateAnnulationCmd)) | date: 'dd/MM/yyyy HH:mm'
                }}
              </span>
            </div>

            <div class="inner-wrapper-item">
              <span class="wrapper-child {{offre?.offreur | societeType}}">Offre de : <span> {{
                  offre?.offreur?.raisonSociale | uppercase }}</span></span>

              <span style="text-align: end !important;" *ngIf="!((offre?.societeCreateurCmd?.id !== societe?.id && offre?.commandStatut === 'NOUVELLE') ||
              (offre?.annuleePar && offre?.annuleePar?.id !== societe?.id && offre?.commandStatut === 'ANNULER'))"
                class="wrapper-child {{offre?.distributeur | societeType}}">Distribuée par : <span> {{
                  offre?.distributeur?.raisonSociale | uppercase }}</span></span>
            </div>

            <div class="inner-wrapper-item" *ngIf="(offre?.societeCreateurCmd?.id !== societe?.id && offre?.commandStatut === 'NOUVELLE') ||
              (offre?.annuleePar && offre?.annuleePar?.id !== societe?.id && offre?.commandStatut === 'ANNULER')">
              <span class="wrapper-child {{(offre?.annuleePar ?? offre?.societeCreateurCmd) | societeType}}">
                {{ offre?.commandStatut === 'NOUVELLE' ? 'Saisie' : offre?.commandStatut | commandeStatut }} par : <b>
                  {{ offre?.userCreateurCommande?.id }} / </b>
                <span>{{ (offre?.annuleePar ??
                  offre?.societeCreateurCmd)?.raisonSociale }} </span>
              </span>

              <span style="text-align: end !important;"
                class="wrapper-child {{offre?.distributeur | societeType}}">Distribuée par : <span> {{
                  offre?.distributeur?.raisonSociale | uppercase }}</span></span>
            </div>

          </div>
        </ion-item>

        <ion-item class="motif-refus-container" *ngIf="offre?.commandStatut && offre?.commandStatut === 'REFUSE'">
          <ion-label class="ion-text-wrap"><span>Motif de refus : </span> {{offre?.motifRefus || 'Pas de motif de refus
            pour cette commande'}} </ion-label>
        </ion-item>

        <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_COMMERCIAL', 'ROLE_AGENT_FOURNISSEUR']">
          <ion-item style="margin-bottom: 5px;"
            *ngIf="!selectedClient && (!offre?.commandStatut || offre?.commandStatut === 'BROUILLON')">
            <button class="client-btn ion-activatable"
              (click)="openClientModal.emit({ open: true, isClientLocal: false })">
              <ion-icon name="add"></ion-icon>
              {{ 'choisir un client' | uppercase }}

              <ion-ripple-effect class="ripple"></ion-ripple-effect>
            </button>
          </ion-item>
        </ng-container>

        <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
          <ion-item *ngIf="selectedClient" class="w-100" style="--padding-start: 12px;">
            <div class="pack-wrapper pack-wrapper-alt w-100">
              <ion-label class="ion-flex ion-justify-content-between">
                <b class="ion-text-start ion-text-wrap">{{ (selectedClient ?? offre?.client)?.raisonSociale }}</b>
                <ion-icon *ngIf="!offre.commandStatut || offre?.commandStatut === 'BROUILLON'" size="large"
                  name="ellipsis-horizontal" (click)="ajouterClient()"></ion-icon>
              </ion-label>

              <ion-list>
                <ion-item>
                  <span class="ion-flex palier-item w-100">
                    <ion-badge class="client-code-badge">G{{ (selectedClient ?? offre?.client)?.code }}</ion-badge>
                    <ion-badge class="ion-float-end" color="dark" style="font-size: 12px">{{ (selectedClient ??
                      offre?.client)?.ville }}</ion-badge>
                  </span>
                </ion-item>
              </ion-list>
            </div>
          </ion-item>
        </ng-container>

        <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
          <ng-container *ngIf="(!offre.commandStatut || offre?.commandStatut === 'BROUILLON')">
            <ion-item
              *ngIf="(isSelectedClientLocalObject === true) && selectedClientLocal && $any(selectedClientLocal)?.id"
              class="w-100" style="--padding-start: 12px;">
              <div class="pack-wrapper pack-wrapper-primary w-100">
                <ion-label class="ion-flex ion-justify-content-between">
                  <b class="ion-text-start ion-text-wrap">
                    {{ $any(selectedClientLocal)?.raisonSociale?.trim() }}
                    <span style="font-weight: 200 !important;">(client local)</span>
                  </b>
                  <ion-icon *ngIf="!$any(selectedClientLocal)?.estClient" size="large" name="ellipsis-horizontal"
                    (click)="ajouterClient(true)"></ion-icon>
                </ion-label>

                <ion-list>
                  <ion-item>
                    <span class="ion-flex palier-item w-100">
                      <ion-badge class="client-code-badge-primary">{{ $any(selectedClientLocal)?.code }}</ion-badge>
                      <ion-badge class="ion-float-end" color="dark" style="font-size: 12px">{{
                        $any(selectedClientLocal)?.ville
                        }}</ion-badge>
                    </span>
                  </ion-item>
                </ion-list>
              </div>
            </ion-item>

            <ng-container
              *ngIf="!!isSelectedClientLocalObject && selectedClientLocal && !$any(selectedClientLocal)?.id">
              <ion-item class="w-100" style="--padding-start: 12px;">
                <div class="pack-wrapper pack-wrapper-primary w-100">
                  <ion-label class="ion-flex ion-justify-content-start">
                    <ion-badge class="client-code-badge-primary">{{ $any(selectedClientLocal)?.code }}</ion-badge>

                    <span style="font-weight: 200 !important; margin-left: 10px">(client local)</span>
                  </ion-label>
                </div>
              </ion-item>
            </ng-container>

            <ng-container *ngIf="isSelectedClientLocalObject === false">
              <ion-item class="w-100" style="--padding-start: 12px;">
                <div class="pack-wrapper pack-wrapper-primary w-100">
                  <ion-label class="ion-flex ion-justify-content-between">
                    <ion-badge class="client-code-badge-primary">{{ selectedClientLocal }}</ion-badge>

                    <span style="font-weight: 200 !important">(client local)</span>

                    <ion-icon size="large" name="ellipsis-horizontal" (click)="ajouterClient(true)"></ion-icon>
                  </ion-label>
                </div>
              </ion-item>
            </ng-container>


            <ng-container *ngIf="(isSelectedClientLocalObject === null) && selectedClient && !selectedClientLocal">
              <ion-item style="margin-bottom: 5px;">
                <button class="client-btn ion-activatable"
                  (click)="openClientModal.emit({ open: true, isClientLocal: true })">
                  <ion-icon name="add"></ion-icon>
                  {{ 'choisir un client local' | uppercase }}

                  <ion-ripple-effect class="ripple"></ion-ripple-effect>
                </button>
              </ion-item>
            </ng-container>

          </ng-container>
        </ng-container>

        <ion-item *ngFor="let pack of offre.listeBlocs; let i = index" class="w-100" style="--padding-start: 12px;">
          <div class="pack-wrapper w-100">
            <ion-label class="ion-text-wrap  ion-padding-start">
              <h5 class="normal-title"> Pack n°: {{ i + 1 }}
                <span class="ion-text-center ion-padding-start">{{pack.titre}}</span>
              </h5>
            </ion-label>
            <ion-list class="bg-transparent">
              <ion-item class="item-palier ion-padding-end" *ngFor="let palier of pack.listePaliers;let j = index"
                style="--min-height: 30px !important;">
                <span class="ion-padding-start ion-flex palier-item w-100">
                  <span>Palier {{ j + 1 }} :</span>
                  <span *ngIf="palier.qteMin"> {{palier.qteMin}}</span>
                  <span *ngIf="palier.valeurMin"> {{palier.valeurMin | number: '1.2-2'}}</span>
                  <span *ngIf="palier.tauxRf"> {{palier.tauxRf}}%</span>
                  <span *ngIf="palier.tauxUg"> {{palier.tauxUg}}%</span>
                  <span *ngIf="palier.ratioUg"> {{palier.ratioUg}}</span>
                  <i *ngIf="!palier.tauxRf && !palier.tauxUg && !palier.ratioUg" class="text-color-light">N</i>
                </span>
              </ion-item>
            </ion-list>
          </div>
        </ion-item>

        <ion-item>
          <div class="special-conditions ">
            <ion-label>
              <ion-icon name="information-circle-outline"></ion-icon>
              Conditions Générales
            </ion-label>
            <ion-list>
              <ion-item>
                <h5>1- Livraison via grossiste, au choix.</h5>
              </ion-item>
              <ion-item>
                <h5>2- Le pharmacien a le choix entre les deux packs (pas d'obligation de perdre tous packs).</h5>
              </ion-item>
            </ion-list>
          </div>
        </ion-item>
      </ion-list>
    </div>
  </div>


  <!-- SECOND PAGE -->
  <div class="w-100" *ngIf="dataItem.id">
    <div class="error-container" *ngIf="dataItem?.etat === 'I'">{{dataItem?.messageEtat}}</div>
    <!--    <div class="error-container" *ngIf="dataItem?.etat === 'I'">-->
    <!--      <span *ngIf="dataItem.qteMin && dataItem.qteMin > dataItem.totalQteCmd">min: {{dataItem.qteMin}}</span>-->
    <!--      <span *ngIf="dataItem.qteMax"> max {{dataItem.qteMax}}</span>-->
    <!--      <span *ngIf="dataItem.valeurMin"> vMin {{dataItem.valeurMin}}</span>-->
    <!--      <span *ngIf="dataItem.valeurMax"> vMax {{dataItem.valeurMax}}</span>-->
    <!--      <span *ngIf="dataItem.nbrObjFilsMin"> nbrSousBlockMin {{dataItem.nbrObjFilsMin}}</span>-->
    <!--      <span *ngIf="dataItem.nbrObjFilsMax"> nbrSousBlockMax {{dataItem.nbrObjFilsMax}}</span>-->
    <!--      <span *ngIf="dataItem.nombreProduitsMin"> nbrPrdMin {{dataItem.nombreProduitsMin}}</span>-->
    <!--      <span *ngIf="dataItem.nombreProduitsMax"> nbrPrdMax {{dataItem.nombreProduitsMax}}</span>-->
    <!--    </div>-->
    <div class="error-container" *ngIf="dataItem.conditionErrors?.length > 0">
      <span *ngFor="let condition of dataItem.conditionErrors; let i = index">{{condition.name}} {{condition.value}} {{i
        === dataItem.conditionErrors .length - 1 ? '' : ' - '}}</span>
    </div>

    <div class="inner-cmd-wrapper" [ngClass]="{'err-pb': dataItem?.etat === 'I'}">
      <ion-list lines="none" class="w-100">
        <ion-item class="padding-start-list ion-no-padding ion-item-no-end-padding">
          <div class="item-panel ion-padding-top ion-flex ion-column w-100">
            <div class="title-wrapper">
              <ion-label class="ion-text-wrap">
                <h2 class="ion-text-uppercase font-bold pack-title ion-padding-start">
                  Pack : {{ dataItem.titre }}</h2>
              </ion-label>
              <div *ngIf="dataItem.listePaliers.length" style="padding-right: 5px;" class="ion-float-end">
                <ion-badge class="type-gold remise-badge">RF</ion-badge>

                <ion-badge [ngClass]="{
                  'type-noraml': palier.selected, 
                  'palier-error': (
                    ip === 0 && 
                    offre?.accepterPalierInvalide === 'N' && 
                    dataItem?.etat === 'I')
                  }" class="remise-badge" *ngFor="let palier of dataItem.listePaliers; let ip = index"
                  (click)="openIonModal(modal, dataItem.listePaliers)">
                  {{palier?.tauxRf}}%
                </ion-badge>

                <ion-badge class="remise-badge info-badge" (click)="openIonModal(modal, dataItem.listePaliers)">
                  <img class="link-icon-alt" />
                </ion-badge>
              </div>
            </div>
            <div class="item-panel ion-flex ion-justify-content-start ion-align-items-center w-100">
              <div class="ion-flex">
                <h2 class="ion-text-uppercase pack-title ion-padding-start font-bold ex-m" style="margin-right:6px;">
                  Total Pack :</h2>
                <ion-item class="ion-no-padding badge-input w-input-total" lines="none" input>
                  <ion-input [value]="dataItem.totalValeurNetteCmd | number: '1.2-2':'fr-FR'" readonly
                    disabled></ion-input>
                </ion-item>
              </div>
              <div class="ion-flex">
                <h2 class="ion-text-uppercase pack-title ion-padding-start font-bold ex-m">Total QT. </h2>
                <ion-item class="ion-no-padding badge-input w-input-qt" lines="none" input>
                  <ion-input [value]="dataItem.totalQteCmd | number: '1.0-0':'fr-FR'" readonly disabled></ion-input>
                </ion-item>
              </div>
            </div>
          </div>
        </ion-item>
      </ion-list>

      <!--      <ng-container *ngIf="existeFilsBlocsProduits(bloc)">-->
      <!--        <wph-bloc-offre [readOnly]="readOnly"-->
      <!--                        [listeBlocsOffreProduits]="getListeFilsBlocsProduits(bloc)"></wph-bloc-offre>-->
      <!--      </ng-container>-->
      <!-- PRODUCT SINGLE CONTAINER -->

      <ion-list *ngIf="existeFilsBlocsProduits(dataItem)" lines="none" class="w-100"
        [ngClass]="{'padding-md': offre?.commandStatut === 'BROUILLON' || !offre?.commandStatut}">
        <ng-container [ngTemplateOutlet]="products"
          [ngTemplateOutletContext]="{products:getListeFilsBlocsProduits(dataItem)}"></ng-container>
      </ion-list>

      <ion-list lines="none" class="w-100 padding-md">
        <ng-container *ngFor="let blocFils of getListeFilsBlocsNonProduits(dataItem); let ib2=index"
          [ngTemplateOutlet]="productPack" [ngTemplateOutletContext]="{pack:blocFils}">
        </ng-container>
      </ion-list>
    </div>

    <!-- FOOTER WITH ACTION BUTTONS -->
    <div class="cmd-footer">
      <ion-grid>
        <ion-row class="footer-wrapper">
          <ion-col size="7" class="ion-flex ion-justify-content-between ion-align-items-center">
            <ion-label class="font-bold montant-total-net ion-text-wrap footer-label-lg">Montant Total Net</ion-label>
            <ion-input type="number" value="178600.00" class="footer-inp-lg"></ion-input>
          </ion-col>
          <ion-col size="5" class="ion-flex ion-justify-content-between ion-align-items-center">
            <ion-label class="font-bold montant-total-net ion-text-wrap footer-label-sm">Soit une remise</ion-label>
            <ion-input type="number" value="100.00" class="footer-inp-sm"></ion-input>
          </ion-col>
        </ion-row>
      </ion-grid>

      <div class="ion-flex ion-justify-content-between ion-p-action">
        <ion-button class="btn-actions cmd-btns" (click)="openPage('/commandes')">
          <ion-icon name="close-outline" class="mx-sm"></ion-icon>
          Quitter
        </ion-button>
        <ion-button class="btn-actions cmd-btns">
          <ion-icon name="save-outline" class="mx-sm"></ion-icon>
          Enregister
        </ion-button>
        <ion-button class="btn-actions cmd-btns" (click)="openPage('commandes/confirmation')">
          <ion-icon name="cart-outline" class="mx-sm"></ion-icon>
          Commander
        </ion-button>
      </div>
    </div>
  </div>


</ng-template>

<ng-template #products let-products="products">
  <div *ngFor="let product of products" class="p-product">
    <div class="product-wrapper">
      <ion-row>
        <ion-col class="m-product-left">
          <ion-text class="product-title">
            <h5>{{product.libelleProduit}} <span *ngIf="product.blocObligatoire === 'O'" class="font-bold"
                [ngClass]="{'text-danger': (product.blocObligatoire === 'O' && (!product.qteCmd || product.qteCmd === 0) )}">
                (**)</span></h5>
          </ion-text>
          <ion-row class="ion-flex ion-justify-content-start ion-align-items-stretch w-100">
            <ion-item class="ion-no-padding badge-input w-product-option" lines="none" input>
              <ion-input class="fw-bold" [value]="product?.prixVenteHt | number: '1.2-2':'fr-FR'" readonly></ion-input>
            </ion-item>
            <ion-item class="ion-no-padding badge-input w-product-option" lines="none" input>
              <ion-input class="fw-bold" [value]="product?.prixVenteTtc | number: '1.2-2':'fr-FR'" readonly></ion-input>
            </ion-item>
          </ion-row>
        </ion-col>
        <ion-col>
          <ion-row>
            <ion-col size="4">
              <div class="product-conditions">
                <span *ngIf="product.qteMax" class="ion-text-nowrap"
                  [ngClass]="{'text-danger': (product.qteMax && product.qteCmd > product.qteMax)}">max :
                  {{product.qteMax}}</span>
                <span *ngIf="product.qteMin" class="ion-text-nowrap"
                  [ngClass]="{'text-danger': (product.qteMin && product.qteCmd < product.qteMin)}">min :
                  {{product.qteMin}}</span>
              </div>
            </ion-col>
            <ion-col size="8">
              <div class="ion-flex">
                <div class="i-product-wrapper" lines="none" input>
                  <ion-button *ngIf="!readOnly" class="ion-shadow-none btn-product btn-left"
                    (click)="product.qteCmd > 0 ? cellCloseHandler(product.qteCmd - 1, product) : null">
                    <ion-icon name="remove-outline"></ion-icon>
                  </ion-button>

                  <ion-input type="number" [value]="product.qteCmd" [min]="0" [readonly]="readOnly"
                    (ionChange)="cellCloseHandler($any($event).target.value, product)" [ngClass]="{
                      'valid-input': 
                        (product.qteCmd > 0 && product?.offre?.accepterPalierInvalide === 'N' && product.tauxRemise) || 

                        (product.qteCmd > 0 && product?.offre?.accepterPalierInvalide === 'N' &&  
                          (+product?.parent?.qteMin > 0 && (product?.parent?.totalQteCmd >= +product?.parent?.qteMin)) ||
                          +product?.parent?.qteMax > 0 && (product?.parent?.totalQteCmd <= +product?.parent?.qteMax) ||
                          +product?.parent?.valeurMin > 0 && (product?.parent?.totalQteCmd >= +product?.parent?.valeurMin) ||
                          +product?.parent?.valeurMax > 0 && (product?.parent?.totalQteCmd <= +product?.parent?.valeurMax)) || 

                        (product.qteCmd > 0 && !product?.parent?.listePaliers.length && 
                          !product?.parent?.qteMin && !product?.parent?.qteMax && !product?.parent?.valeurMin && !product?.parent?.valeurMax) || 
                        
                        (product.qteCmd > 0 && product?.offre?.accepterPalierInvalide === 'O'),
                      
                      'error-input': 
                        (product.qteCmd > 0 && product?.offre?.accepterPalierInvalide === 'N' && 
                          (product?.parent?.totalQteCmd < +product?.parent?.qteMin || (+product?.parent?.qteMax > 0 && (product?.parent?.totalQteCmd > +product?.parent?.qteMax)))) ||

                        (product.qteCmd > 0 && product?.offre?.accepterPalierInvalide === 'N' && 
                          (product?.parent?.totalValeurBruteCmd < +product?.parent?.valeurMin || (+product?.parent?.valeurMax > 0 && (product?.parent?.totalValeurBruteCmd > +product?.parent?.valeurMax)))) ||  
                        
                        (product.qteCmd > 0 && product?.offre?.accepterPalierInvalide === 'N' && product?.parent?.listePaliers.length && !product.tauxRemise)
                    }">
                  </ion-input>
                  <ion-button *ngIf="!readOnly" class="ion-shadow-none btn-product btn-right"
                    (click)="cellCloseHandler(product.qteCmd + 1, product)">
                    <ion-icon name="add-outline"></ion-icon>
                  </ion-button>
                </div>
                <span class="product-remise">{{validCommande && product.qteCmd && product.tauxRemise ?
                  (product.tauxRemise + '%') : ' '}}</span>
              </div>
            </ion-col>
          </ion-row>
        </ion-col>
      </ion-row>
    </div>
  </div>
</ng-template>
<ng-template #productPack let-pack="pack">

  <div *ngIf="pack?.titre" class="p-product-pack">
    <div class="product-pack">
      <h6>{{pack?.titre}}</h6>
      <span class="pack-condition" [ngClass]="{'pack-success': pack.etat === 'V', 'pack-error': pack.etat === 'I'}">
        {{pack.qteMin ? 'min ' + pack.qteMin : ''}}{{pack.qteMax ? ', max ' + pack.qteMax : ''}}
        {{pack.valMin ? ', vMin ' + pack.valMin : ''}}{{pack.valMax ? ', vMax ' + pack.valMax : ''}}
        {{pack.nbrObjFilsMin ? ', fMin ' + pack.nbrObjFilsMin : ''}}{{pack.nbrObjFilsMax ? ', fMax ' +
        pack.nbrObjFilsMax : ''}}
        {{pack.nombreProduitsMin ? ', pMin ' + pack.nombreProduitsMin : ''}}{{pack.nombreProduitsMax ? ', pMax ' +
        pack.nombreProduitsMax : ''}}
      </span>
      <div>

        <ion-badge [ngClass]="{
          'type-noraml': palier.selected, 
          'palier-error': (
            ip === 0 && 
            offre?.accepterPalierInvalide === 'N' &&
            pack?.etat === 'I')
          }" class="ion-activatable remise-badge" *ngFor="let palier of pack.listePaliers; let ip = index"
          (click)="openIonModal(modal, pack.listePaliers)">
          <img style="margin-right: 8px;" class="link-icon" [ngClass]="{'link-icon-invert': palier.selected || (
              ip === 0 && 
              offre?.accepterPalierInvalide === 'N' &&
              pack?.etat === 'I')}" />
          {{palier?.tauxRf}}%

          <ion-ripple-effect class="ripple"></ion-ripple-effect>
        </ion-badge>
        <!--                <ion-badge class="remise-badge" (click)="openIonModal(modal, 6)">6%</ion-badge>-->
        <!--                <ion-badge class="remise-badge" (click)="openIonModal(modal, 15)">15%</ion-badge>-->
        <!--                <ion-badge class="remise-badge" (click)="openIonModal(modal, 20)">20%</ion-badge>-->
      </div>
      <ng-container *ngIf="existeFilsBlocsProduits(pack)" [ngTemplateOutlet]="products"
        [ngTemplateOutletContext]="{products:getListeFilsBlocsProduits(pack)}">
      </ng-container>
      <ng-container *ngFor="let blocFils of getListeFilsBlocsNonProduits(pack); let ib2=index"
        [ngTemplateOutlet]="productPack" [ngTemplateOutletContext]="{pack:blocFils}">
      </ng-container>

    </div>
  </div>
</ng-template>