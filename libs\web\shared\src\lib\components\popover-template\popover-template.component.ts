import { Component, Input } from "@angular/core";
import { NgbPopover } from "@ng-bootstrap/ng-bootstrap";

@Component({
    selector: 'wph-popover-template',
    templateUrl: './popover-template.component.html',
    styleUrls: ['./popover-template.component.scss'],
})
export class PopoverTemplateComponent {
    @Input() id: string;
    @Input() popoverRef: NgbPopover;

    @Input() set popoverContent(value: string) {
        this._popoverContent = value;
    }
    get popoverContent(): string {
        return this._popoverContent;
    }

    _popoverContent: string;

    constructor() { }
}