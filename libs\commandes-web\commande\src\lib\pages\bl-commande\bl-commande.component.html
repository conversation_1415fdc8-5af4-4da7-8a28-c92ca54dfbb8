<!-- Start Of Header -->
<div class="rowline mb-0" id="bl-header">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col"><PERSON> l<PERSON>raison</h4>
        <div class="col px-0">
            <div class="row justify-content-end align-items-center">
                <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>

                <button (click)="goBack()" class="btn btn-sm btn-dark rounded-pharma m-1">
                    <i class="mdi mdi-close"></i>
                    Quitter
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="card" id="bl-card" style="height: calc(100vh - 123px)">
    <div class="card-header px-1">
        <div class="d-flex row justify-content-between px-0 mx-0">
            <div class="mx-1 py-1 d-flex row">
                <span><b class="mr-1">Date du BL: </b>{{ gridData?.dateBl | momentTimezone: "yyyy-MM-DD" : "Africa/Casablanca" }}</span>
            </div>

            <div class="mx-1 py-1 d-flex row" >
                <span><b class="mr-1">Code Client: </b>{{ gridData?.codeClientSite }}</span>
            </div>

            <div *ngIf="client" class="mx-1 py-1 d-flex row">
                <span><b class="mr-1">Client: </b>{{ client | titlecase }}</span>
            </div>

            <div *ngIf="ville" class="mx-1 py-1 d-flex row">
                <span><b class="mr-1">Ville: </b>{{ ville | titlecase }}</span>
            </div>

            <div class="mx-1 py-1 d-flex row">
                <span><b class="mr-1">Numéro du BL: </b>{{ gridData?.numeroBl }}</span>
            </div>
        </div>
    </div>

    <div class="card-body px-0 py-0" style="overflow-y: auto;">
        <kendo-grid [data]="grid" [sortable]="false" [resizable]="true" [pageable]="false" [scrollable]="scrollable">
            <kendo-grid-column media="(max-width: 768px)" title="Détails BL">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <!---  Mobile Column Template  --->
                    <dl>
                        <dt class="my-2 ml-2">Produit: <span>{{ dataItem?.designation }}</span></dt>

                        <dt class="my-2 ml-2">Qté Commandée: <span>{{ dataItem?.qtiteCmde }}</span></dt>

                        <dt class="my-2 ml-2">Qté Livrée: <span>{{ dataItem?.qtiteLivr }}</span></dt>

                        <dt class="my-2 ml-2">PPV: <span>{{ dataItem?.ppv | number: '1.2-2': 'fr-FR' }}</span></dt>

                        <dt class="my-2 ml-2">PPH: <span>{{ dataItem?.pph | number: '1.2-2': 'fr-FR' }}</span></dt>

                        <dt class="my-2 ml-2">Taxe (%): <span>{{ dataItem?.tva | number: '1.2-2': 'fr-FR' }}</span>
                        </dt>

                        <dt class="my-2 ml-2">Total: <span>{{ (dataItem?.pph && dataItem?.qtiteLivr ? dataItem?.pph
                                * dataItem?.qtiteLivr : 0) | number:
                                '1.2-2': 'fr-FR' }}</span>
                        </dt>
                    </dl>
                </ng-template>

            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="designation" title="Produit" class="text-left text-wrap"
                [width]="320"></kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="qtiteCmde" class="text-left" title="Qté Commandée">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.qtiteCmde }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="qtiteLivr" class="text-left" title="Qté Livrée">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.qtiteLivr }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="ppv" class="text-right" title="PPV">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.ppv | number: '1.2-2': 'fr-FR' }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="pph" title="PPH" class="text-right">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.pph | number: '1.2-2': 'fr-FR' }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="tva" title="Taxe (%)" class="text-right">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.tva | number: '1.2-2': 'fr-FR' }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" title="Total" class="text-right">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ (dataItem?.pph && dataItem?.qtiteLivr ? dataItem?.pph * dataItem?.qtiteLivr : 0) | number:
                    '1.2-2': 'fr-FR' }}
                </ng-template>
            </kendo-grid-column>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>
        </kendo-grid>
    </div>

    <div class="card-footer">
        <div class="total h3 float-right">
            Total PPH Net: {{ total | number: "1.2-2":"fr-FR" }} DH<br />
        </div>
    </div>
</div>