export interface Catalogue {
  audited: boolean;
  code: string;
  id: number;
  nom: string;
  typeCatalogue: string;
}

export interface Client {
  adresse: string;
  catalogue: Catalogue;
  code: string;
  id: number;
  localite: string;
  nomResponsable: string;
  raisonSociale: string;
  societeType: string;
  ville: string;
}

export interface Catalogue2 {
  audited: boolean;
  code: string;
  id: number;
  nom: string;
  typeCatalogue: string;
}

export interface Distributeur {
  adresse: string;
  catalogue: Catalogue2;
  code: string;
  id: number;
  localite: string;
  nomResponsable: string;
  raisonSociale: string;
  societeType: string;
  ville: string;
}

export interface Catalogue3 {
  audited: boolean;
  code: string;
  id: number;
  nom: string;
  typeCatalogue: string;
}

export interface Distributeur2 {
  adresse: string;
  catalogue: Catalogue3;
  code: string;
  id: number;
  localite: string;
  nomResponsable: string;
  raisonSociale: string;
  societeType: string;
  ville: string;
}

export interface Catalogue4 {
  audited: boolean;
  code: string;
  id: number;
  nom: string;
  typeCatalogue: string;
}

export interface ListePalier {
  cadeau: string;
  id: number;
  multiple: string;
  nbrObjFilsMax: number;
  nbrObjFilsMin: number;
  nombreProduitsMax: number;
  nombreProduitsMin: number;
  qteMax: number;
  qteMin: number;
  ratioUg: string;
  reste: string;
  tauxRf: number;
  tauxUg: number;
  valeurMax: number;
  valeurMin: number;
}

export interface ListeBloc {
  blocObligatoire: string;
  blocOrder: number;
  catalogue: Catalogue4;
  codeProduitCatalogue: string;
  description: string;
  id: number;
  libelleProduit: string;
  listOfBlocOffresExclus: number[];
  listOfBlocOffresPrerequis: number[];
  listeFils: any[];
  listePaliers: ListePalier[];
  multipleQtePrdCmd: number;
  nbrObjFilsMax: number;
  nbrObjFilsMin: number;
  nombreProduitsMax: number;
  nombreProduitsMin: number;
  onlyMultipleQteCmd: string;
  plafondRemiseSpeciale: number;
  plafondUgSpeciale: number;
  ppv: number;
  prixVenteHt: number;
  prixVenteTtc: number;
  qteCmd: number;
  qteMax: number;
  qteMin: number;
  qteUgSaisie: number;
  ratioUg: string;
  titre: string;
  totalQteUg: number;
  typeBloc: string;
  typeRemise: string;
  valeurMax: number;
  valeurMin: number;
}

export interface ListePaliersRemisesAdditionnel {
  cadeau: string;
  id: number;
  multiple: string;
  nbrObjFilsMax: number;
  nbrObjFilsMin: number;
  nombreProduitsMax: number;
  nombreProduitsMin: number;
  qteMax: number;
  qteMin: number;
  ratioUg: string;
  reste: string;
  tauxRf: number;
  tauxUg: number;
  valeurMax: number;
  valeurMin: number;
}

export interface Catalogue5 {
  audited: boolean;
  code: string;
  id: number;
  nom: string;
  typeCatalogue: string;
}

export interface Offreur {
  adresse: string;
  catalogue: Catalogue5;
  code: string;
  id: number;
  localite: string;
  nomResponsable: string;
  raisonSociale: string;
  societeType: string;
  typeEntreprise?: string;
  ville: string;
}

export interface OffreDto2 {
  accepterPalierInvalide: string;
  client: Client;
  commandStatut: string;
  dateDebut: Date;
  dateFin: Date;
  dateLivraisonPrevue: Date;
  datePublication: Date;
  description: string;
  distributeur: Distributeur;
  distributeurs: Distributeur2[];
  enteteCommandeId: number;
  etat: string;
  etatProposant: string;
  id: number;
  image: string;
  imageType: string;
  listeBlocs: ListeBloc[];
  listePaliersRemisesAdditionnels: ListePaliersRemisesAdditionnel[];
  maxRemiseFinancier: number;
  modalites: string;
  nombreProduitsProposes: number;
  nombresJoursRestants: number;
  numeroOffre: string;
  offreur: Offreur;
  palierAdditionTestBrut: string;
  palierTestValeurBrut: string;
  pourcentageEcoulement: number;
  resume: string;
  titre: string;
  utiliserValeurHt: string;
}

export interface OffreDto {
  caBrut: number;
  caNet: number;
  mtRemise: number;
  nbrCommande: number;
  offreDto: OffreDto2;
  qteUG: number;
  qteVedues: number;
}

export interface Catalogue6 {
  audited: boolean;
  code: string;
  id: number;
  nom: string;
  typeCatalogue: string;
}

export interface ListePalier2 {
  cadeau: string;
  id: number;
  multiple: string;
  nbrObjFilsMax: number;
  nbrObjFilsMin: number;
  nombreProduitsMax: number;
  nombreProduitsMin: number;
  qteMax: number;
  qteMin: number;
  ratioUg: string;
  reste: string;
  tauxRf: number;
  tauxUg: number;
  valeurMax: number;
  valeurMin: number;
}

export interface PackOffre2 {
  blocObligatoire: string;
  blocOrder: number;
  catalogue: Catalogue6;
  codeProduitCatalogue: string;
  description: string;
  id: number;
  libelleProduit: string;
  listOfBlocOffresExclus: number[];
  listOfBlocOffresPrerequis: number[];
  listeFils: any[];
  listePaliers: ListePalier2[];
  multipleQtePrdCmd: number;
  nbrObjFilsMax: number;
  nbrObjFilsMin: number;
  nombreProduitsMax: number;
  nombreProduitsMin: number;
  onlyMultipleQteCmd: string;
  plafondRemiseSpeciale: number;
  plafondUgSpeciale: number;
  ppv: number;
  prixVenteHt: number;
  prixVenteTtc: number;
  qteCmd: number;
  qteMax: number;
  qteMin: number;
  qteUgSaisie: number;
  ratioUg: string;
  titre: string;
  totalQteUg: number;
  typeBloc: string;
  typeRemise: string;
  valeurMax: number;
  valeurMin: number;
}

export interface PackOffre {
  caBrut: number;
  caNet: number;
  mtRemise: number;
  nbrCommande: number;
  packOffre: PackOffre2;
  qteUG: number;
  qteVedues: number;
}

export interface Catalogue7 {
  audited: boolean;
  code: string;
  id: number;
  nom: string;
  typeCatalogue: string;
}

export interface Catalogue8 {
  audited: boolean;
  code: string;
  id: number;
  nom: string;
  typeCatalogue: string;
}

export interface Fournisseur {
  catalogue: Catalogue8;
  code: string;
  id: number;
  libelle: string;
  typeFrn: string;
}

export interface ProduitDto2 {
  catalogue: Catalogue7;
  codeProduitCatalogue: string;
  fournisseur: Fournisseur;
  id: number;
  libelleProduit: string;
  ppv: number;
  prixVenteHt: number;
  prixVenteTtc: number;
  tauxTva: number;
}

export interface ProduitDto {
  caBrut: number;
  caNet: number;
  mtRemise: number;
  nbrCommande: number;
  produitDto: ProduitDto2;
  qteUG: number;
  qteVedues: number;
}

export interface StatsData {
  caBrut: number;
  caNet: number;
  mtRemise: number;
  nbrCommande: number;
  offreDtos: OffreDto[];
  packOffres: PackOffre[];
  produitDtos: ProduitDto[];
  qteUG: number;
  nmbrPh?: number;
  qteVedues: number;
}

