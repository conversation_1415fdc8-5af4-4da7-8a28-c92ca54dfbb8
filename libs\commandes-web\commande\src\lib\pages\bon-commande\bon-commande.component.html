<div id="bon_commande">
  <!-- Start Of Header -->
  <div class="rowline mb-0" id="bon-cmd-header">
    <div class="page-title-box row">
      <h4 class="page-title fw-4 ps-2 col">Bon de Commande</h4>

      <div class="col px-0">
        <div class="row justify-content-end align-items-center">
          <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>

          <button (click)="goBack()" class="btn btn-sm btn-dark rounded-pharma m-1">
            <i class="mdi mdi-close"></i>
            Quitter
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- END HEADER -->

  <div class="card" style="height: calc(100vh - 123px);">

    <div class="card-header px-1" id="bon-cmd-card">
      <div class="d-flex row justify-content-between px-0 mx-0">
        <div class="py-1 mx-1 d-flex row">
          <span><b class="mr-1">Crée le: </b>{{ gridData?.dateCommande | momentTimezone: 'yyyy-MM-DD HH:mm' :
            "Africa/Casablanca" }}</span>
        </div>

        <div class="py-1 mx-1 d-flex row">
          <span><b class="mr-1">Numéro de commande: </b>{{gridData?.codeCommande}}</span>
        </div>


        <div class="py-1 mx-1 d-flex row">
          <span> <b class="mr-1">Enregistrée le: </b>{{ gridData?.dateValidation | momentTimezone : 'yyyy-MM-DD
            HH:mm' : "Africa/Casablanca" }}</span>
        </div>

        <div class="py-1 mx-1 d-flex row">
          <span> <b class="mr-1">Saisie par: </b> {{ [gridData?.userCreateur?.firstname,
            gridData?.userCreateur?.lastname].join(' ') | titlecase }}</span>
        </div>

        <div *ngIf="!showClientDetails; else: showClient" class="py-1 mx-1 d-flex row">
          <span><b class="mr-1">Fournisseur: </b>{{ gridData?.distributeur?.raisonSociale | titlecase }}</span>
        </div>

        <ng-template #showClient>
          <div class="py-1 mx-1 d-flex row">
            <span><b class="mr-1">Client: </b>{{ gridData?.userCreateur?.entrepriseDTO?.raisonSociale | titlecase
              }}</span>
          </div>
        </ng-template>

        <div class="py-1 mx-1 d-flex row">
          <span><b class="mr-1">Ville: </b>{{ gridData?.ville | titlecase }}</span>
        </div>

      </div>
    </div>

    <div class="card-body py-0 px-0" style="overflow-y: auto;">

      <kendo-grid [data]="grid" [resizable]="true" [scrollable]="scrollable" [sortable]="false" [pageable]="false">
        <kendo-grid-column media="(max-width: 768px)" title="Détails Commande">
          <ng-template kendoGridCellTemplate let-dataItem>
            <!--- Mobile Column Template --->
            <dl>
              <dt class="my-2 ml-2">Produit: <span>{{ dataItem?.libelleProduit }}</span></dt>

              <dt class="my-2 ml-2">Qté Validée: <span>{{ dataItem?.qteCmd }}</span></dt>

              <dt class="my-2 ml-2">PPV: <span>{{ dataItem?.ppv }}</span></dt>

              <dt class="my-2 ml-2">PPH: <span>{{ dataItem?.prixVenteTtc }}</span></dt>

              <dt class="my-2 ml-2">Taxe (%): <span>{{ dataItem?.tauxTva | number: '1.2-2': 'fr-FR' }}</span></dt>

              <dt class="my-2 ml-2">Total PPH Net TTC: <span>{{ dataItem?.totalNetTtc | number: '1.2-2': 'fr-FR'
                  }}</span></dt>
            </dl>
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" class="text-wrap" field="libelleProduit" title="Produit"
          class="text-left" [width]="320"></kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="qteCmd" class="text-left" title="Qté Validée">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.qteCmd }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="ppv" class="text-right" title="PPV">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.ppv | number: '1.2-2': 'fr-FR' }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="prixVenteTtc" title="PPH" class="text-right">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.prixVenteTtc | number: '1.2-2': 'fr-FR' }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="tauxTva" title="Taxe (%)" class="text-right">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.tauxTva | number: '1.2-2': 'fr-FR' }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="totalNetTtc" title="Total" class="text-right">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-right">Total PPH Net TTC</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.totalNetTtc | number: '1.2-2': 'fr-FR' }}
          </ng-template>
        </kendo-grid-column>

        <ng-template kendoGridNoRecordsTemplate>
          <span>Aucun résultat trouvé.</span>
        </ng-template>
      </kendo-grid>

    </div>

    <div class="card-footer">
      <div class="total h3 float-right">
        Total PPH Net TTC: {{total | number:'1.2-2':'fr-FR'}} DH
      </div>
    </div>
  </div>

</div>