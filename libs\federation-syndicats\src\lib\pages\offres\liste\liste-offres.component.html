<!-- Start Of Header -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-8">Liste des Offres</h4>

        <div *jhiHasAnyAuthority="['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN']" class="col-4 px-1">
            <div class="row justify-content-end align-items-center">
                <button (click)="saisirOffre()" type="button" class="btn btn-sm btn-primary m-1">
                    <i class="bi bi-plus-circle-fill"></i>
                    Saisir Offre
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="row d-flex m-0 px-1">
    <div class="card m-0 w-100 p-0" [ngClass]="{'bg-transparent': !isListView}" [style.height]="'calc(100vh - 60px)'">
        <div class="card-header py-1 pl-2 mx-0 bg-white">
            <div class="row p-0">
                <div class="col-3 p-0">
                    <button (click)="(isListView = false); updatePageParams()" title="Cartes"
                        [ngClass]="{ 'selected-switch': !isListView }" class="btn btn-sm py-1 px-2 border m-1 b-radius">
                        <i class="mdi mdi-18px mdi-view-grid"></i>
                    </button>

                    <button (click)="(isListView = true); updatePageParams()" title="Liste"
                        [ngClass]="{ 'selected-switch': isListView }" class="btn btn-sm py-1 px-2  border m-1 b-radius">
                        <i class="mdi mdi-18px mdi-format-list-bulleted"></i>
                    </button>
                </div>

                <div class="col-9 p-0 d-flex justify-content-end">
                    <div class="row p-0">
                        <div class="col-lg col-9 p-0 m-1">
                            <div class="input-group picker-input">
                                <input type="search" placeholder="Titre de l'offre" [formControl]="searchFilter"
                                    class="form-control b-radius form-control-md pl-4" id="titreOffre" />

                                <div class="picker-icons picker-icons-alt">
                                    <i class="mdi mdi-magnify pointer"></i>
                                </div>
                            </div>
                        </div>


                        <button type="button" (click)="displayFilter = !displayFilter"
                            class="btn btn-sm search-btn m-1 b-radius">
                            <span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
                                <i class="bi bi-sliders"></i>
                                <span class="mx-1">Recherche Avancée</span>
                            </span>

                            <ng-template #closeFilter>
                                <span class="d-flex align-items-center">
                                    <i class="mdi mdi-close"></i>
                                    <span class="mx-1">Fermer la recherche</span>
                                </span>
                            </ng-template>

                        </button>

                        <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
                    </div>
                </div>
            </div>

            <form [formGroup]="filterForm" (ngSubmit)="appliquerFiltre()">
                <div id="advanced-search" *ngIf="displayFilter" class="row p-0 flex-wrap px-1 my-2 k-gap-2">
                    <div class="col-lg-auto col-12 p p-0 m-0">
                        <label for="numero" class="col-12 px-0 col-form-label text-left">Numéro</label>

                        <div class="col-12 px-0 input-group">
                            <input type="text" name="numero" id="numero" formControlName="numero"
                                class="form-control form-control-md b-radius bg-white">
                        </div>
                    </div>

                    <div *ngIf="!isLabo" class="col-lg-auto col-12 p-0 m-0">
                        <label for="offreur" class="col-12 px-0 col-form-label text-left">Offreur</label>

                        <div class="col-12 px-0 input-group picker-input">
                            <input type="text" name="offreur" id="offreur" formControlName="offreur"
                                class="form-control pl-4 form-control-md b-radius bg-white"
                                [ngbTypeahead]="searchFournisseur" [resultFormatter]="laboFormatter"
                                [inputFormatter]="laboFormatter">

                            <div class="picker-icons picker-icons-alt">
                                <i class="mdi mdi-magnify pointer"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-auto col-12 p-0 m-0">
                        <label for="distributeur" class="col-12 px-0 col-form-label text-left">Distributeur</label>

                        <div class="col-12 px-0 input-group picker-input">
                            <input type="text" name="distributeur" id="distributeur" formControlName="distributeur"
                                class="form-control pl-4 form-control-md b-radius bg-white"
                                [ngbTypeahead]="searchFournisseur" [resultFormatter]="fournisseurFormatter"
                                [inputFormatter]="fournisseurFormatter">

                            <div class="picker-icons picker-icons-alt">
                                <i class="mdi mdi-magnify pointer"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-12 mt-1 pt-1 pb-0 px-0">
                        <label class="col-12 form-label px-0 m-0" style="margin-bottom: 5px !important;" for="selectNature">Nature
                            de l'offre</label>

                        <div class="col-12 px-0 input-group">
                            <select2 id="selectNature" formControlName="natureOffre" hideSelectedItems="false"
                                [data]="natureOffreSelect" class="form-control-sm p-0 w-100" multiple="false"></select2>

                        </div>
                    </div>

                    <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL', 'ROLE_AGENT_FOURNISSEUR']">
                        <div class="col-lg-2 col-12 mt-1 pt-1 pb-0 px-0">
                            <label class="col-12 form-label px-0 m-0" for="selectstatut"
                                style="margin-bottom: 5px !important;">Non Expirée Uniquement</label>

                            <div class="col-12 px-0 input-group">
                                <select2 id="selectstatut" formControlName="nonExpireesUniquement"
                                    [data]="nonExpireesSelect" hideSelectedItems="false" class="form-control-sm p-0 w-100"
                                    multiple="false"></select2>
                            </div>
                        </div>

                        <div class="col-lg-2 col-12 mt-1 pt-1 pb-0 px-0">
                            <label class="col-12 form-label px-0 m-0" for="selectstatut"
                                style="margin-bottom: 5px !important;">Statut</label>

                            <div class="col-12 px-0 input-group">
                                <select2 id="selectstatut" formControlName="statut" [data]="stautsLabelsValues"
                                    hideSelectedItems="false" class="form-control-sm p-0 w-100" multiple="false"></select2>
                            </div>
                        </div>
                    </ng-container>


                    <div class="col d-flex align-items-end py-lg-0 py-2">
                        <button (click)="viderFiltre()" type="button" class="btn btn-sm btn-outline-primary b-radius">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>

                        <button type="submit" class="btn btn-sm btn-primary b-radius mx-1">
                            <i class="mdi mdi-filter"></i>
                        </button>
                    </div>
                </div>
            </form>

        </div>

        <div *ngIf="isListView; else: cardView" class="card-body m-0 p-0 bg-white mt-1">
            <kendo-grid [data]="gridData" [pageable]="{
                buttonCount: 5,
                info: true,
                type: 'numeric',
                pageSizes: pageSizes,
                previousNext: true,
                position: 'bottom'
              }" [pageSize]="navigation.pageSize" class="fs-grid" (cellClick)="cellClickHandler($event)"
                (pageChange)="pageChange($event)" (sortChange)="sortChange($event)" [sortable]="{ mode: 'single'}"
                [sort]="offreSort" [resizable]="true" [skip]="navigation.skip" style="height: 100%">

                <kendo-grid-column field="numeroOffre" title="Numéro" [width]="100">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        {{ dataItem?.numeroOffre }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="alpha"></app-grid-sort-header>
                    </ng-template>

                </kendo-grid-column>

                <kendo-grid-column class="text-wrap" field="titre" [width]="200">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        {{ dataItem?.titre }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Titre de l'offre</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="alpha"></app-grid-sort-header>
                    </ng-template>

                </kendo-grid-column>

                <kendo-grid-column class="text-wrap" field="offreur.raisonSociale" title="Offreur" [width]="120">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        {{ dataItem?.offreur?.raisonSociale | uppercase }}
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="alpha"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column class="text-wrap" title="Distributeur" [width]="120">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <ng-container *ngIf="(dataItem?.distributeurs?.length === 1); else: seeMore">
                            {{ dataItem?.distributeurs[0]?.raisonSociale }}
                        </ng-container>

                        <ng-template #seeMore>
                            <span [title]="dataItem?.listeDistributeursString ?? ''"><u>Voir Plus...</u></span>
                        </ng-template>

                    </ng-template>

                </kendo-grid-column>

                <kendo-grid-column title="Nature" [width]="100">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        {{ (dataItem?.natureOffre === 'I' ? 'Individuelle' : 'Groupe') | uppercase }}
                    </ng-template>

                </kendo-grid-column>

                <kendo-grid-column field="dateDebut" [width]="150">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <span *ngIf="dataItem?.dateDebut; else: emptyDate">
                            {{ dataItem?.dateDebut | date: 'dd/MM/yyyy' }}
                        </span>
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Date publication</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="numeric"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="dateFin" [width]="120">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <span *ngIf="dataItem?.dateFin; else: emptyDate">
                            {{ dataItem?.dateFin | date: 'dd/MM/yyyy' }}
                        </span>
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Date Limite</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="numeric"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="delaiLivraison" class="text-center" [width]="120">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <span *ngIf="dataItem?.delaiLivraison; else: notSpecified">
                            {{ dataItem?.delaiLivraison | number }} <span>Jour{{ dataItem?.delaiLivraison > 1 ? 's' : ''
                                }}</span>
                        </span>
                        <ng-template #notSpecified>--</ng-template>
                    </ng-template>

                    <ng-template kendoGridHeaderTemplate let-column>
                        <span class="text-wrap">Délai Livraison</span>
                        <app-grid-sort-header [direction]="navigation.sortMethod!"
                            [active]="navigation.sortField === column.field" [title]="column.title"
                            type="numeric"></app-grid-sort-header>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="etatProposant" title="Statut" [width]="100">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <app-element-status *ngIf="dataItem?.etatCommandeAchatGroupe !== 'ACCEPTEE'; else: accepted"
                            [state]="(dataItem?.etat === 'cloturee' && dataItem?.etatProposant !== 'ANNULER') ? 'CLOTURE' : dataItem?.etatProposant"></app-element-status>

                        <ng-template #accepted>
                            <app-element-status [state]="dataItem?.etatCommandeAchatGroupe"></app-element-status>
                        </ng-template>
                    </ng-template>
                </kendo-grid-column>

                <kendo-grid-column *jhiHasAnyAuthority="['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN']" title="Actions"
                    [width]="isAdmin ? 120 : 100">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="d-flex justify-content-center k-gap-2">
                            <ng-container *jhiHasAnyAuthority="['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN']">
                                <span (click)="consulterOffre(dataItem, false)"
                                    *ngIf="(dataItem?.etatProposant === 'BROUILLON') && (dataItem?.etat !== 'cloturee')"
                                    class="actions-icons btn-success pointer-cus" title="Modifier Offre">
                                    <i class="bi bi-pencil-square"></i>
                                </span>

                                <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                                    <span (click)="consulterOffre(dataItem, false)"
                                        *ngIf="(dataItem?.etatProposant === 'PUBLIER') && (dataItem?.etat !== 'cloturee' && dataItem?.etatProposant !== 'CLOTURE' && dataItem?.etatProposant !== 'ANNULER')"
                                        class="actions-icons btn-success pointer-cus" title="Modifier Offre">
                                        <i class="bi bi-pencil-square"></i>
                                    </span>
                                </ng-container>

                                <span *ngIf="dataItem?.etatProposant !== 'BROUILLON' || (dataItem?.etat === 'cloturee')"
                                    (click)="dupliquerOffre(dataItem)" class="actions-icons btn-info pointer-cus"
                                    title="Dupliquer Offre">
                                    <i class="bi bi-files-alt"></i>
                                </span>

                                <span (click)="cloturerOffre(dataItem)"
                                    [ngClass]="{'opacity-light': dataItem?.etatProposant === 'CLOTURE' || dataItem?.etatProposant === 'ANNULER' || dataItem?.etat === 'cloturee'}"
                                    class="actions-icons  btn-danger pointer-cus"
                                    [title]="(dataItem?.etatProposant === 'BROUILLON') ? 'Annuler Offre' : 'Cloturer Offre'">
                                    <i class="bi bi-trash"></i>
                                </span>
                            </ng-container>

                        </div>

                    </ng-template>

                </kendo-grid-column>

                <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
                    let-total="total">
                    <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
                        [navigation]="navigation" style="width: 100%;"
                        (pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
                </ng-template>

                <ng-template kendoGridNoRecordsTemplate>
                    <span>Aucun résultat trouvé.</span>
                </ng-template>
            </kendo-grid>
        </div>

        <ng-template #emptyDate>
            <span>--/--/----</span>
        </ng-template>


        <ng-template #cardView>
            <div class="card-body m-0 p-0 bg-transparent card-view" [style.height]="'calc(100vh - 125px)'"
                scrollListener #cardViewContainer>
                <wph-widgets-offres [listeOffres]="cardData"></wph-widgets-offres>
            </div>
        </ng-template>

    </div>
</div>

<ng-template #popFournisseurs let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">LISTE DES DISTRIBUTEURS</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span>&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <kendo-grid [data]="selectedFournisseurs" [hideHeader]="true">
            <kendo-grid-column [width]="100" field="etatProposant" title="">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    {{dataItem.raisonSociale}}
                </ng-template>
            </kendo-grid-column>
        </kendo-grid>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">Fermer
        </button>
    </div>
</ng-template>