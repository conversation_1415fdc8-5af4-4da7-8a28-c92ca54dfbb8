import { NgModule } from '@angular/core';
import { CommandesRoutingModule } from './commandes-routing.module';
import { ListeCommandesComponent } from './liste/liste-commandes.component';
import { WebSharedModule } from '@wph/web/shared';
import { SharedModule } from '@wph/shared';
import { EditCommandeComponent } from './edit/edit-commande.component';
import { CommonModule } from '@angular/common';
import { ListeCommandeGroupe } from './commande-groupe/liste-commande-groupe.component';
import { BlocOffreComponent } from '../../components/bloc-offre/bloc-offre.component';
import { NgbNavModule, NgbRatingModule } from '@ng-bootstrap/ng-bootstrap';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { ListeCommandesComponentIndividuelle } from './liste-individuelle/liste-commandes-individuelle.component';
import { CommandesLaboComponent } from './commandes-labo/commandes-labo.component';
import { CmdIndividuelleAdminComponent } from './commande-individuelle-admin/commande-individuelle-admin.component';
import { CommandeGroupeAdminComponent } from './commande-groupe-admin/commande-groupe-admin.component';
import { ToutesCommandesAdminComponent } from './toutes-commandes-admin/toutes-commandes-admin.component';

@NgModule({
  declarations: [
    ListeCommandesComponent,
    EditCommandeComponent,
    ListeCommandeGroupe,
    BlocOffreComponent,
    CommandesLaboComponent,
    ToutesCommandesAdminComponent,
    CommandeGroupeAdminComponent,
    CmdIndividuelleAdminComponent,
    ListeCommandesComponentIndividuelle,
  ],
  imports: [
    CommandesRoutingModule,
    WebSharedModule,
    SharedModule,
    CommonModule,
    NgbNavModule,
    DropDownsModule,
    NgbRatingModule,
  ],
})
export class CommandesModule {}
