<!-- Start Of Header -->
<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-12">Liste des Commandes</h4>
  </div>
</div>
<!-- <PERSON><PERSON> HEADER -->

<div class="row d-flex m-0 px-1">
  <div class="card m-0 w-100 p-0 bg-white" [style.height]="'calc(100vh - 60px)'">
    <div class="card-header py-1 pl-2 mx-0 bg-white">
      <div class="row p-0">

        <div class="col-12 p-0 d-flex justify-content-end">
          <div class="row p-0">
            <div class="col p-0 m-1">
              <div class="input-group picker-input">
                <input type="search" [formControl]="searchFilter" placeholder="Code ou Titre de l'offre"
                  class="form-control form-control-md pl-4" id="groupeCritere" />

                <div class="picker-icons picker-icons-alt">
                  <i class="mdi mdi-magnify pointer"></i>
                </div>
              </div>
            </div>

            <button (click)="displayFilter = !displayFilter" type="button" class="btn btn-sm search-btn b-radius m-1">
              <span *ngIf="!displayFilter">
                <i class="bi bi-sliders"></i>
                Recherche Avancée
              </span>

              <span *ngIf="displayFilter">
                <i class="mdi mdi-close"></i>
                Fermer la recherche
              </span>

            </button>

            <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
          </div>
        </div>
      </div>

      <form [formGroup]="filterForm" (ngSubmit)="appliquerFiltre()" *ngIf="displayFilter" wphFocusTrap>
        <div class="row px-1 py-0 flex-wrap k-gap-2">

          <div class="col-sm p-0 m-0">
            <label for="distributeur" class="col-12 px-0 col-form-label text-left">Distributeur</label>

            <div class="col-12 px-0 input-group picker-input">
              <input type="text" name="distributeur" id="distributeur" formControlName="distributeur"
                class="form-control pl-4 form-control-md b-radius bg-white" [ngbTypeahead]="searchFournisseur"
                [resultFormatter]="fournisseurFormatter" [inputFormatter]="fournisseurFormatter">

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>

          <div class="col-sm p-0 m-0">
            <label for="client" class="col-12 px-0 col-form-label text-left">Client</label>

            <div class="col-12 px-0 input-group picker-input">
              <input type="text" name="client" id="client" formControlName="client"
                class="form-control pl-4 form-control-md b-radius bg-white" [ngbTypeahead]="searchClient"
                [resultFormatter]="fournisseurFormatter" [inputFormatter]="fournisseurFormatter">

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>

          <div class="col-sm p-0 m-0">
            <label for="dateDebut" class="col-12 px-0 col-form-label text-left">Date Création Début</label>

            <div class="col-12 px-0 input-group picker-input">
              <input [readOnly]="true" (click)="drange1.toggle()" type="text" name="dateDebut" id="dateDebut"
                formControlName="dateDebut" ngbDatepicker #drange1="ngbDatepicker"
                class="form-control pr-4 form-control-md b-radius bg-white" placeholder="jj/mm/aaaa">

              <div class="picker-icons picker-icons">
                <i (click)="drange1.toggle()" class="mdi mdi-calendar text-dark pointer"></i>
              </div>
            </div>
          </div>

          <div class="col-sm p-0 m-0">
            <label for="dateFin" class="col-12 px-0 col-form-label text-left">Date Création Fin</label>

            <div class="col-12 px-0 input-group picker-input">
              <input [readOnly]="true" (click)="drange2.toggle()" type="text" name="dateFin" id="dateFin"
                formControlName="dateFin" ngbDatepicker #drange2="ngbDatepicker"
                class="form-control pr-4 form-control-md b-radius bg-white" placeholder="jj/mm/aaaa">

              <div class="picker-icons picker-icons">
                <i (click)="drange2.toggle()" class="mdi mdi-calendar text-dark pointer"></i>
              </div>
            </div>
          </div>

          <div class="col-sm mt-1 pt-1 pb-0 px-0">
            <label class="col-12 form-label p-0 ml-2" for="selectstatut" style="margin-bottom: -4px;">Statut</label>

            <div class="col-12 px-0 input-group">
              <select2 id="selectstatut" [data]="stautsLabelsValues" formControlName="statut" hideSelectedItems="false"
                class="form-control-sm w-100" multiple="false"></select2>

            </div>
          </div>

          <div class="col d-flex align-items-end py-0">
            <button (click)="viderFiltre()" type="button" title="Vider" class="btn btn-sm btn-outline-primary b-radius">
              <i class="bi bi-arrow-clockwise"></i>
            </button>

            <button type="submit" title="Appliquer filtre" class="btn btn-sm btn-primary b-radius mx-1">
              <i class="mdi mdi-filter"></i>
            </button>

          </div>
        </div>
      </form>
    </div>

    <div class="card-body m-0 p-0 bg-white">
      <kendo-grid [data]="gridData" [pageable]="true" [pageSize]="navigation.pageSize" class="fs-grid fs-listing-grid"
        (cellClick)="cellClickHandler($event)" (sortChange)="sortChange($event)" [sortable]="{ mode: 'single'}"
        [sort]="commandeSort" [resizable]="true" [skip]="navigation.skip" style="height: 100%">

        <kendo-grid-column field="codeCommande" title="Code Cmd" [width]="120">
          <ng-template kendoGridHeaderTemplate let-column>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="offre.titre" class="text-wrap" title="Titre de l'offre" [width]="200">
          <ng-template kendoGridHeaderTemplate let-column>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'alpha'"></app-grid-sort-header>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.offre?.titre }}
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column field="distributeur.raisonSociale" class="text-wrap" title="Distributeur" [width]="160">
          <ng-template kendoGridCellTemplate let-dataItem>
            <ng-container>
              {{ dataItem?.distributeur?.raisonSociale }}
            </ng-container>
          </ng-template>
          <ng-template kendoGridHeaderTemplate let-column>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'alpha'"></app-grid-sort-header>
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column field="client.raisonSociale" class="text-wrap" title="Client" [width]="160">
          <ng-template kendoGridCellTemplate let-dataItem>
            <ng-container>
              {{ dataItem?.client?.raisonSociale }}
            </ng-container>
          </ng-template>
          <ng-template kendoGridHeaderTemplate let-column>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'alpha'"></app-grid-sort-header>
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column field="dateCreation" title="Date Création" [width]="130">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span *ngIf="dataItem?.dateCreation; else: emptyDate">{{ dataItem?.dateCreation | date:
              'dd/MM/yyyy' }}</span>
          </ng-template>
          <ng-template kendoGridHeaderTemplate let-column>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="valeurCmdBruteTtc" class="text-right" title="Montant Brut (Dh)" [width]="150">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.valeurCmdBruteTtc | number : '1.2-2' }}
          </ng-template>
          <ng-template kendoGridHeaderTemplate let-column>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column title="Statut" [width]="120">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-element-status
              [state]="dataItem?.natureCommande === 'I' ? dataItem?.statut : dataItem?.etatCommande"></app-element-status>
          </ng-template>
        </kendo-grid-column>

        <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
          <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
            [navigation]="navigation" style="width: 100%;" (pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
        </ng-template>

        <ng-template kendoGridNoRecordsTemplate>
          <span>Aucun résultat trouvé.</span>
        </ng-template>
      </kendo-grid>

      <ng-template #emptyDate>
        <span>--/--/----</span>
      </ng-template>
    </div>
  </div>
</div>