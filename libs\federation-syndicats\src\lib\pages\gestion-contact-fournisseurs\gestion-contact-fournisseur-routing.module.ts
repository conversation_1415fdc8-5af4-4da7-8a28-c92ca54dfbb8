import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ListeContactFournisseurComponent } from "./liste/liste-contact-fournisseur.component";
import { EditContactFournisseurComponent } from "./edit/edit-contact-fournisseur.component";

const routes: Routes = [
    {
        path: 'liste',
        component: ListeContactFournisseurComponent
    },
    {
        path: 'edit',
        component: EditContactFournisseurComponent
    },
    {
        path: 'edit/:id',
        component: EditContactFournisseurComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class GestionContactFournisseurRoutingModule {}