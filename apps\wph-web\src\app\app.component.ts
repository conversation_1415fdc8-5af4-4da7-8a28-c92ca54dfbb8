import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, Renderer2 } from "@angular/core";
import { Subscription } from "rxjs";
import { PlateformeService, Principal } from "@wph/shared";
import { AuthService } from "@wph/core/auth";
import { NavigationStart, Router } from "@angular/router";
import { Title } from "@angular/platform-browser";
import { KeyboardShortcutService } from "@wph/web/shared";


@Component({
  selector: "wph-root",
  templateUrl: "./app.component.html",
  styleUrls: ["./app.component.scss"]
})
export class AppComponent implements OnInit, OnDestroy {
  private principal: Principal;
  private subscriptions: Subscription = new Subscription();

  constructor(
    private router: Router,
    private titleService: Title,
    private renderer: Renderer2,
    private authService: AuthService,
    private plateformeService: PlateformeService,
    private keyboardShortcutService: KeyboardShortcutService
  ) {
    this.subscriptions.add(
      this.plateformeService.currentPlateforme$.subscribe(current => {
        this.setCurrentPlateformeTitle();
        this.setCurrentPlateformeFavicon();

        this.setCustomScrollbarStyling(current);
      })
    );
  }

  ngOnInit() {
    this.principal = this.authService.getPrincipal();

    this.subscriptions.add(this.authService.principal$.subscribe((principal) => {
      this.principal = principal;
    }));

    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        if (event.url === '/' && event.navigationTrigger === 'imperative') {
          const currentPlateforme = this.plateformeService.getCurrentPlateforme();

          switch (currentPlateforme) {
            case 'WIN_OFFRE':
              this.router.navigateByUrl('/accueil');
              break;
            case "COMMANDE_WEB":
              this.router.navigateByUrl('/commande-web/accueil');
              break;
            case 'WIN_GROUPE':
            case 'FEDERATION_SYNDICAT':
              this.router.navigateByUrl('/achats-groupes');
              break;
            case 'DEFAULT':
            default:
              this.router.navigateByUrl('/pharma-lien');
              break;
          }
        }
      }
    });

    if (this.authService.isAuthenticated() && this.plateformeService.isPlateForme('FEDERATION_SYNDICAT')) {
      this.keyboardShortcutService.addNavigationKeyboardShortcuts();
    }

    // Preload assets based on local storage key-value pair
    const preloadAssetsKey = localStorage.getItem('fromFlutterWebView');
    if (preloadAssetsKey === 'true') {
      this.preloadImages([
        'https://cdn.sophatel.com/pharmalien/images/pubwinplus.svg',
        'https://cdn.sophatel.com/pharmalien/images/pub-commande-web.svg',
        'https://cdn.sophatel.com/pharmalien/images/pub-matariel_v3.svg',
        'https://cdn.sophatel.com/pharmalien/images/quebg.svg'
      ]);
    }
  }

  private preloadImages(imageUrls: string[]): void {
    imageUrls.forEach(url => {
      const img = new Image();
      img.src = url;
    });
  }

  private setCustomScrollbarStyling(current: string) {
    switch (current) {
      case "COMMANDE_WEB":
        this.injectStyles('--cw-primary-700');
        break;
      case 'WIN_GROUPE':
        this.injectStyles('--fs-group-grid');
        break;  
      case 'FEDERATION_SYNDICAT':
        this.injectStyles('--fs-grid-primary');
        break;  
      case 'WIN_OFFRE':
        this.injectStyles('--wo-primary-400');
        break;
      case 'DEFAULT':
      default:
        this.injectStyles('--win-offre-primary');
        break;
    }
  }

  setCurrentPlateformeTitle() {
    if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT')) {
      this.titleService.setTitle('La Centrale Pharma');
    } else if (this.plateformeService.isPlateForme('WIN_GROUPE')) {
      this.titleService.setTitle('WinPlus Groupe')
    } else {
      this.titleService.setTitle('PharmaLien');
    }
  }

  setCurrentPlateformeFavicon() {
    const favicon: HTMLLinkElement | null = document.querySelector('#appIcon');

    if (favicon) {
      if (this.plateformeService.isPlateForme('WIN_GROUPE')) {
        favicon.href = './assets/images/winplusGroupe-white.svg';
      } else if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT')) {
        favicon.href = './assets/images/achats-groupes-logo.svg';
      } else {
        favicon.href = './assets/images/pharmalien_logo_only_dark.svg';
      }
    }

  }

  refreshTitle() {
    let titleSuffix = '';

    if (this.principal) {
      titleSuffix = this.principal.firstname + ' ' + this.principal.lastname;
    }

    this.authService.setTitle(titleSuffix);
  }

  injectStyles(colorVar: string): void {
    const styleElement = this.renderer.createElement('style');
    this.renderer.appendChild(
      styleElement,
      this.renderer.createText(`
        html::-webkit-scrollbar {
          width: 13.5px;
          height: 13.5px;
          border-radius: 5px;
          background-clip: padding-box;
          background-color: transparent !important;
          margin-right: 0 !important;
          padding-right: 0 !important;
        }
        ${(!this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') && !this.plateformeService.isPlateForme('WIN_GROUPE')) ? 
          `div.simplebar-content {
            height: calc(100vh - 100px) !important;
          }`  : ''
        }
        html::-webkit-scrollbar-thumb {
          width: 13.5px;
          height: 13.5px;
          background: var(${colorVar}) !important;
          border-radius: 5px;
        }

        html::-webkit-scrollbar-track {
          border-radius: 10px;
          width: 10px !important;
          padding-inline: 5px;
        }
      `)
    );

    this.renderer.appendChild(document.head, styleElement);
  }

  ngOnDestroy() {
    if (this.subscriptions) this.subscriptions.unsubscribe();

    this.keyboardShortcutService.clearKeyboardShortcutSubscriptions();
  }
}
