import { ProduitFournisseurDto } from "./ProduitFournisseurDto";

export enum TypeAlert {
    RENTRANT = 'R',  
    LENCEMENT = 'L', 
  }
  

  export class AlertProduitDto {
    codeSite: number;         
    dateCreation: string;     
    id: number;               
    pph: number;              
    ppv: number;              
    proCode: string;          
    proFamTarf: string;       
    proLibelle: string;       
    proPrixRay2: number;      
    produitFournisseur: ProduitFournisseurDto; 
    typeAlert: TypeAlert;     
  
    constructor(data: any) {
      this.codeSite = data.codeSite;
      this.dateCreation = data.dateCreation;
      this.id = data.id;
      this.pph = data.pph;
      this.ppv = data.ppv;
      this.proCode = data.proCode;
      this.proFamTarf = data.proFamTarf;
      this.proLibelle = data.proLibelle;
      this.proPrixRay2 = data.proPrixRay2;
      this.produitFournisseur = data.produitFournisseur;
      this.typeAlert = data.typeAlert;  
    }
  }
