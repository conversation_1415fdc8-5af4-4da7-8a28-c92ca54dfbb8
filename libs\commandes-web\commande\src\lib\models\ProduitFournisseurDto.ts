export class ProduitFournisseurDto {
    codeAbarre: string;             
    codeLabo: string;               
    codeProduit: string;            
    id: number;                    
    libelle: string;               
    libelleCategorie: string;       
    libelleFamilleTarifaire: string; 
    libelleForme: string;           
    libelleLabo: string;            
    pfht: number;                   
    pph: number;                     
    ppv: number;                     
    tva: number;                      
  
    constructor(data: any) {
      this.codeAbarre = data.codeAbarre;
      this.codeLabo = data.codeLabo;
      this.codeProduit = data.codeProduit;
      this.id = data.id;
      this.libelle = data.libelle;
      this.libelleCategorie = data.libelleCategorie;
      this.libelleFamilleTarifaire = data.libelleFamilleTarifaire;
      this.libelleForme = data.libelleForme;
      this.libelleLabo = data.libelleLabo;
      this.pfht = data.pfht;
      this.pph = data.pph;
      this.ppv = data.ppv;
      this.tva = data.tva;
    }
  }
  