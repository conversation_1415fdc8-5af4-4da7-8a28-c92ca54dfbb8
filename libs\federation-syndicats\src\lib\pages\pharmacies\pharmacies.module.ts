import { NgModule } from "@angular/core";
import { PharmaciesRoutingModule } from "./pharmacies-routing.module";
import { WebSharedModule } from "@wph/web/shared";
import { PharmaciesListComponent } from "./liste-pharmacies/pharmacies-list.component";
import { CommonModule } from "@angular/common";
import { MapComponent } from "./map/map.component";
import { EditPharmacieComponent } from "./edit-pharmacie/edit-pharmacie.component";
import { NgbNavModule } from "@ng-bootstrap/ng-bootstrap";
import { SharedModule } from "@wph/shared";

@NgModule({
    declarations: [PharmaciesListComponent, EditPharmacieComponent, MapComponent],
    imports: [PharmaciesRoutingModule, WebSharedModule, CommonModule, NgbNavModule,SharedModule]
})
export class PharmaciesModule {}
