<ion-header>
  <ion-toolbar color="">
    <ion-buttons slot="start">
      <ion-menu-button class="menu-icon"></ion-menu-button>
    </ion-buttons>
    <ion-title class="ion-text-center">{{principle?.societe?.raisonSociale}}</ion-title>
    <ion-buttons slot="end" (click)="openPage('account')">
      <ion-button class="action-btn" shape="round">
        <ion-icon name="person"></ion-icon>
        <ion-text>{{principle?.lastname}}</ion-text>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row>
      <ion-col size="12" class="logo-col ion-no-padding">
        <img class="logo"/>
      </ion-col>

      <ion-col size="11">
        <ion-button shape="round" class="item-btn-wrapper item-btn-acc" (click)="openPage('offres')">
          <ion-icon slot="start" class="icon-item-btn"></ion-icon>
         {{'OFFRES'}}
        </ion-button>
      </ion-col>

      <ion-col size="11">
        <ion-button shape="round" class="item-btn-wrapper item-btn-acc" (click)="openPage('commandes')">
          <ion-icon slot="start" class="icon-item-btn"></ion-icon>
         {{'MON HISTORIQUE'}}
        </ion-button>
      </ion-col>

      <ion-col size="11">
        <ion-button shape="round" class="item-btn-wrapper item-btn-acc" (click)="openPage('accueil/actualites')">
          <ion-icon slot="start" class="icon-item-btn"></ion-icon>
         {{'Actualités' | uppercase}}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
