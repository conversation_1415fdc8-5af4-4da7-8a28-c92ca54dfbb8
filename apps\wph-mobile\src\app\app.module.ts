import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NgModule } from "@angular/core";
import { BrowserModule } from "@angular/platform-browser";
import { RouteReuseStrategy, RouterModule } from "@angular/router";

import { IonicModule, IonicRouteStrategy } from "@ionic/angular";

import { AppComponent } from "./app.component";
import { AppRoutingModule } from "./app-routing.module";
import { AuthModule } from "@wph/core/auth";
import { ENV } from "../../../const";
import { environment } from "../environments/environment";
import { HttpClientModule } from "@angular/common/http";
import { MobileLayoutModule } from "@wph/mobile/layout";
import { MobileSharedModule } from "@wph/mobile/shared";

import localeFr from '@angular/common/locales/fr';
import localeFrExtra from '@angular/common/locales/extra/fr';

import { registerLocaleData } from "@angular/common";
import * as moment from "moment";
import { register } from 'swiper/element/bundle';


registerLocaleData(localeFr, 'fr-FR', localeFrExtra);
moment.fn.toJSON = function () { return this.format('YYYY-MM-DD HH:mm:ss'); };

@NgModule({
  declarations: [AppComponent],
  entryComponents: [],
  imports: [BrowserModule, IonicModule.forRoot({ mode: 'md' }), AppRoutingModule,
    HttpClientModule,
    RouterModule,
    AuthModule, MobileLayoutModule,
    MobileSharedModule
  ],

  providers: [
    {
      provide: RouteReuseStrategy,
      useClass: IonicRouteStrategy
    },
    {
      provide: ENV,
      useValue: environment
    },
    { provide: LOCALE_ID, useValue: 'fr-FR' },
  ],

  bootstrap: [AppComponent],
  exports: [
    AppComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppModule {
  constructor() { register(); }
}
