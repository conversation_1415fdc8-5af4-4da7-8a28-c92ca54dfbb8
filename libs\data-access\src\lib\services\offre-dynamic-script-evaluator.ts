import { BlocOffre } from "../models/bloc-offre.model";
import { DetailValeurPalier } from "../models/detail-valeur-palier.model";
import { Offre } from "../models/offre.model";

import Interpreter from 'js-interpreter';





export class OffreDynamicScriptEvaluator {
    blocOffre?: BlocOffre;

    blocPackParent?: BlocOffre;

    offre?: Offre;

    selectedPalier?: DetailValeurPalier;

    dynamicScriptCondition?: string;

    client?: any;



    constructor() {
    }


    
	public execute(): any {

        let initFunc = this.createInitFunctionOfInterpreter();

        let engine = new Interpreter( this.dynamicScriptCondition, initFunc );
		
        engine.run();

		return this.processResult(engine.value);
	}



    private createInitFunctionOfInterpreter() {
        let initFunc = function (interpreter, globalObject) {
            interpreter.setProperty(globalObject, "qte", this.blocOffre?.totalQteCmd ?? 0);
            interpreter.setProperty(globalObject, "mnt_brut_ttc", this.blocOffre?.totalValeurBruteCmdTtc ?? 0.00);
            interpreter.setProperty(globalObject, "mnt_net_ttc", this.blocOffre?.totalValeurNetteCmdTtc ?? 0.00);
            interpreter.setProperty(globalObject, "mnt_brut_ht", this.blocOffre?.totalValeurBruteCmdHt ?? 0.00);
            interpreter.setProperty(globalObject, "mnt_net_ht", this.blocOffre?.totalValeurNetteCmdHt ?? 0.00);

            interpreter.setProperty(globalObject, "palier", this.selectedPalier);

            interpreter.setProperty(globalObject, "pack_qte", this.blocPackParent?.totalQteCmd ?? 0);
            interpreter.setProperty(globalObject, "pack_mnt_brut_ttc", this.blocPackParent?.totalValeurBruteCmdTtc ?? 0.00);
            interpreter.setProperty(globalObject, "pack_mnt_net_ttc", this.blocPackParent?.totalValeurNetteCmdTtc ?? 0.00);
            interpreter.setProperty(globalObject, "pack_mnt_brut_ht", this.blocPackParent?.totalValeurBruteCmdHt ?? 0.00);
            interpreter.setProperty(globalObject, "pack_mnt_net_ht", this.blocPackParent?.totalValeurNetteCmdHt ?? 0.00);

            interpreter.setProperty(globalObject, "offre_qte", this.offre?.totalQteCmd ?? 0);
            interpreter.setProperty(globalObject, "offre_mnt_brut_ttc", this.offre?.totalValeurBruteCmdTtc ?? 0.00);
            interpreter.setProperty(globalObject, "offre_mnt_net_ttc", this.offre?.totalValeurNetteCmdTtc ?? 0.00);
            interpreter.setProperty(globalObject, "offre_mnt_brut_ht", this.offre?.totalValeurBruteCmdHt ?? 0.00);
            interpreter.setProperty(globalObject, "offre_mnt_net_ht", this.offre?.totalValeurNetteCmdHt ?? 0.00);
            interpreter.setProperty(globalObject, "offre_mnt_escompte", this.offre?.valeurEscompteCmd ?? 0.00);
            
            interpreter.setProperty(globalObject, "modalitepaie", this.offre?.delaiPaiement?.code ?? null);

            interpreter.setProperty(globalObject, "clientIce", this.client?.numIce);
        };

        return initFunc.bind(this);
    }

    private processResult(result: any): any {
		if(result==null)
			this.fireEmptyResultException();
		
		if( typeof result == "boolean")
			return result;  // boolean
		
		if( typeof result == "string")
			return this.processJsonStringResult(result as string);	// json

        if (typeof result == "object") {
			return result.properties;
		} 
		
		
		this.fireBadReturnValueTypeException(result);
		return null;  // unreachable,   only to silence compiler
	}

    private processJsonStringResult(result: string): any {
       
		let value = JSON.parse(result);

		if (typeof value == "boolean") {
			return value;
		}
		else if (typeof value == "object") {
			return value;
		} 
		

		this.fireBadReturnValueTypeException(value);
		return null;  // unreachable,   only to silence compiler
    }




    
    private fireEmptyResultException() {
        console.error("The dynamic script did not return a non-null value for the script:", this.dynamicScriptCondition);
        throw new Error("The dynamic script did not return a non-null value");
    }

    private fireBadReturnValueTypeException(result: any) {
        console.error("The dynamic script must return either a boolean or a json string object containing state and message for the value/script:", result, this.dynamicScriptCondition)
        throw new Error("The dynamic script must return either a boolean or a json string object containing state and message");
    }
}




export class OffreDynamicScriptEvaluatorBuilder {
    private blocOffre?: BlocOffre;
    private blocPackParent?: BlocOffre;
    private offre?: Offre;
    private selectedPalier?: DetailValeurPalier;
    private dynamicScriptCondition?: string;
    private client?: any;

    constructor() {}

    withBlocOffre(blocOffre: BlocOffre): OffreDynamicScriptEvaluatorBuilder {
        this.blocOffre = blocOffre;
        return this;
    }

    withBlocPackParent(blocPackParent: BlocOffre): OffreDynamicScriptEvaluatorBuilder {
        this.blocPackParent = blocPackParent;
        return this;
    }

    withOffre(offre: Offre): OffreDynamicScriptEvaluatorBuilder {
        this.offre = offre;
        return this;
    }

    withSelectedPalier(selectedPalier: DetailValeurPalier): OffreDynamicScriptEvaluatorBuilder {
        this.selectedPalier = selectedPalier;
        return this;
    }

    withDynamicScriptCondition(dynamicScriptCondition: string): OffreDynamicScriptEvaluatorBuilder {
        this.dynamicScriptCondition = dynamicScriptCondition;
        return this;
    }

    withClient(client: any): OffreDynamicScriptEvaluatorBuilder {
        this.client = client;
        return this;
    }

    build(): OffreDynamicScriptEvaluator {
        const evaluator = new OffreDynamicScriptEvaluator();
        evaluator.blocOffre = this.blocOffre;
        evaluator.blocPackParent = this.blocPackParent;
        evaluator.offre = this.offre;
        evaluator.selectedPalier = this.selectedPalier;
        evaluator.dynamicScriptCondition = this.dynamicScriptCondition;
        evaluator.client = this.client;
        return evaluator;
    }
}