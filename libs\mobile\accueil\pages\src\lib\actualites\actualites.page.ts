import { Component, OnD<PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { InfiniteScrollCustomEvent, NavController, Platform } from '@ionic/angular';
import { Pagination } from '@wph/data-access';
import { BlogPostCriteria, BlogPostDto, PosteService } from '@wph/web/gestion-annonces';
import { Subscription, map, tap } from 'rxjs';
import { Browser } from '@capacitor/browser';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import * as moment from 'moment';

@Component({
  selector: 'wph-actualites',
  templateUrl: './actualites.page.html',
  styleUrls: ['./actualites.page.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class ActualitesPage implements OnInit, OnDestroy {

  filterForm: FormGroup;
  postes: BlogPostDto[] = [];
  isFilterModalOpen: boolean = false;
  isFilterApplied: boolean = false;

  firstLoad: boolean = true;
  isLoading: boolean = false;
  hasError: boolean = false;

  totalPages = Infinity;
  page: number = 1;
  pagination: Pagination = { skip: 0, pageSize: 10 };

  youtubePlayerVars: YT.PlayerVars;

  defaultDate = new Date().toISOString();
  dateDebutChanged = false;
  dateFinChanged = false;

  selectedChip: string;

  backBtnSubscription: Subscription | null = null;

  constructor(
    private fb: FormBuilder,
    private platform: Platform,
    private sanitizer: DomSanitizer,
    private posteService: PosteService,
    private navController: NavController
  ) {
    this.youtubePlayerVars = this.posteService.youtubePlayerVars;
  }

  ngOnInit(): void {
    this.filterForm = this.fb.group({
      dateDebutVisibilite: [this.defaultDate],
      dateFinVisibilite: [this.defaultDate],
      scopes: [['G']],
      type: [null],
      typeActualite: [null]
    });

    this.getListePostes();

    this.backBtnSubscription = this.platform.backButton.subscribeWithPriority(10, (processNextHandler) => {
      this.navController.navigateBack('/accueil'), processNextHandler();
    });
  }

  get controls() {
    return this.filterForm?.controls;
  }

  getListePostes(ev?: Event): void {
    const { type, dateDebutVisibilite, dateFinVisibilite, ...values } = this.filterForm.getRawValue();

    const dateDebut: Date = new Date(dateDebutVisibilite);
    dateDebut && dateDebut?.setHours(0, 0, 0);

    const criteria: BlogPostCriteria = {
      ...values,
      visible: true,
      categorie: null,
      fournisseursId: null,
      type: type ?? null,
      dateDebutVisibilite: (this.isFilterApplied && this.dateDebutChanged) ? moment(dateDebut) : null,
      dateFinVisibilite: (this.isFilterApplied && this.dateFinChanged) ? moment(dateFinVisibilite) : null
    };

    this.posteService.searchPosts(
      criteria,
      {
        skip: this.pagination.skip,
        pageSize: this.pagination.pageSize,
        sortMethod: 'desc',
        sortField: 'dateDebutVisibilite'
      }
    )
      .pipe(
        tap(() => this.isLoading = true),
        map(res => {
          return {
            totalPages: res.totalPages,
            data: this.posteService.addReadMoreAndImageUrl(res)
          };
        }),
        tap({
          error: (_err) => {
            this.hasError = true;
            this.isLoading = false;
            this.firstLoad = false;

            ev && (ev as InfiniteScrollCustomEvent).target.complete();
          }
        })
      )
      .subscribe(res => {
        this.totalPages = res.totalPages;

        if (ev) {
          this.postes.push(...res.data);

          this.page++;
          this.pagination.skip++;
          (ev as InfiniteScrollCustomEvent).target.complete();
        } else {
          this.page = 1;
          this.postes = res.data;
          this.pagination.skip++;
        }

        this.hasError = false;
        this.firstLoad = false;
        this.isLoading = false;
      });

  }

  readMore(target: BlogPostDto) {
    target.readMore = !target.readMore;
  }

  refresh(event?: Event) {
    // Reset pagination & data
    this.page = 1;
    this.postes = [];
    this.pagination.skip = 0;
    this.totalPages = Infinity;

    this.getListePostes(event);
  }

  loadMore(infiniteScroll: Event) {
    this.getListePostes(infiniteScroll);
  }

  async openLink(url: SafeUrl): Promise<void> {
    const sanitizedUrl = this.sanitizer.sanitize(4, url);

    await Browser.open({ url: sanitizedUrl, presentationStyle: 'fullscreen' });
  }

  trackItems(_index: number, item: BlogPostDto) {
    return item?.id;
  }

  vider(): void {
    this.filterForm.reset({
      dateDebutVisibilite: this.defaultDate,
      dateFinVisibilite: this.defaultDate,
      scopes: ['G'],
      type: null,
      typeActualite: null
    });

    this.isLoading = true;
    this.selectedChip = '';
    this.isFilterApplied = false;
    this.dateDebutChanged = false;
    this.dateFinChanged = false;

    this.refresh();
  }

  applyFilter() {
    this.isLoading = this.isFilterApplied = true;

    if (!!this.selectedChip) {
      if (this.selectedChip === 'TEXTE') {
        this.controls['type'].setValue('M');
        this.controls['typeActualite'].setValue(null);
      } else {
        this.controls['type'].setValue('A');
        this.controls['typeActualite'].setValue(this.selectedChip);
      }
    }

    this.refresh();
  }

  selectChip(selected: string): void {
    if (this.selectedChip !== selected) {
      this.selectedChip = selected;
    } else {
      this.selectedChip = '';

      this.controls['type'].setValue(null);
      this.controls['typeActualite'].setValue(null);
    }
  }

  ngOnDestroy(): void {
    this.backBtnSubscription.unsubscribe();
  }

}
