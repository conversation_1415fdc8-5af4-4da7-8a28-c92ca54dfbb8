import { CommonModule } from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { NgbModal, NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { PosteService } from "../../services/poste.service";
import { BlogPostCriteria, BlogPostDto, PlateformeItem, PublicationScope } from "../../models/BlogPost.interface";
import { PlateformeService, Societe } from "@wph/shared";
import { Pagination } from "@wph/data-access";
import { DomSanitizer, SafeUrl } from "@angular/platform-browser";
import { FilterPostesModalComponent } from "../filter-postes-modal/filter-postes-modal.component";
import { YouTubePlayerModule } from '@angular/youtube-player';
import { AuthService } from "@wph/core/auth";
import { map } from "rxjs";
import { WebGestionAnnoncesModule } from "../../web-gestion-annonces.module";
import Swiper from "swiper";
import { SelectedPlateforme } from "@wph/web/layout";
import { NewsCardComponent } from "../news-card/news-card.component";
import { ComplexNewsCardComponent } from "../complex-news-card/complex-news-card.component";

type PostDisplayMode = 'swiper' | 'liste';

@Component({
    standalone: true,
    selector: 'wph-liste-postes',
    templateUrl: './liste-postes.component.html',
    styleUrls: ['./liste-postes.component.scss'],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    imports: [
        NgbModule,
        FormsModule,
        NewsCardComponent,
        ReactiveFormsModule,
        WebGestionAnnoncesModule,
        FilterPostesModalComponent,
        CommonModule,
        YouTubePlayerModule,
        ComplexNewsCardComponent
    ]
})
export class ListePostesComponent implements OnInit {
    @Input('listeFournisseur')
    set listeFournisseur(value: Societe[]) {
        this._listeFournisseur = value;
    }

    get listeFournisseur() {
        return this._listeFournisseur;
    }

    @Input('isPlateformeWinoffre')
    set isPlateformeWinoffre(value: boolean) {
        this._isPlateformeWinoffre = value;
    }

    get isPlateformeWinoffre() {
        return this._isPlateformeWinoffre;
    }

    @Input('targetPlateformeId') set targetPlateforme(value: number) {
        this.posteService.getPlateformeById(value).subscribe(res => {
            res && (this.targetPlateformeCriteria = res);
            !this.isPinnedPostList && this.getListePostes();
        });
    }

    @Input('mode') set postDisplayMode(value: PostDisplayMode) {
        this._postDisplayMode = value;
    }

    get postDisplayMode() {
        return this._postDisplayMode;
    }

    @Input('inheritedPosts') set inheritedPosts(value: BlogPostDto[]) {
        this._inheritedPosts = value;

        if (this.postDisplayMode === 'liste') {
            this.postes = [...this._inheritedPosts?.filter(post => post?.paramAffichage?.includes('message'))];
        } else {
            this.postes = [...this._inheritedPosts];
        }
    }

    get inheritedPosts() {
        return this._inheritedPosts;
    }

    @Input('isPinnedPostList') set isPinnedPostList(value: boolean) {
        this._isPinnedPostList = value;
    }
    get isPinnedPostList() {
        return this._isPinnedPostList;
    }

    @Input() overrideMaxHeight?: boolean;
    @Output() postsReady: EventEmitter<BlogPostDto[]> = new EventEmitter<BlogPostDto[]>();

    youtubePlayerVars: YT.PlayerVars;

    paginationParams: Pagination = { skip: 0, pageSize: 5 };
    showMore: boolean;
    postes: BlogPostDto[] = [];
    showSelectedFilters = false;
    selectedFournisseur: Societe;

    _isPinnedPostList: boolean = false;
    _inheritedPosts: BlogPostDto[] = [];
    _postDisplayMode: PostDisplayMode = 'liste';
    _isPlateformeWinoffre: boolean;
    targetPlateformeCriteria: PlateformeItem;

    pinnedPostsIsCollapsed: boolean = false;

    currentPlateforme: SelectedPlateforme;
    currentPlateforme$: any;
    currentPosteScope: PublicationScope[];
    showFournisseurFilter: boolean;

    _listeFournisseur: Societe[];
    filterCriteria = { dateDebutVisibilite: null, dateFinVisibilite: null, fournisseursId: 'T', type: 'T', scopes: null, typeActualite: null };

    @ViewChild('popFilterMsg') popFilterMsg: ElementRef;
    @ViewChild('swiperRef', { static: false }) swiperRef: ElementRef;

    constructor(
        private modalService: NgbModal,
        private sanitizer: DomSanitizer,
        private authService: AuthService,
        private posteService: PosteService,
        private plateformeService: PlateformeService,
    ) {
        this.youtubePlayerVars = this.posteService.youtubePlayerVars;
        this.currentPlateforme = this.plateformeService.getCurrentPlateforme();
        this.currentPlateforme$ = this.plateformeService.currentPlateforme$;
    }

    ngOnInit(): void {
        this.setCurrentPosteScope();
    }

    getListePostes(): void {
        const { fournisseursId, type } = this.filterCriteria;

        const criteria: BlogPostCriteria = {
            visible: true,
            categorie: null,
            ...this.filterCriteria,
            type: type !== 'T' ? type : null,
            scopes: this.currentPosteScope,
            plateformePostDtos: this.targetPlateformeCriteria ? [this.targetPlateformeCriteria] : null,
            fournisseursId: fournisseursId !== 'T' ? [+fournisseursId] : null
        };

        this.posteService.searchPosts(
            criteria,
            {
                skip: this.paginationParams.skip,
                pageSize: this.paginationParams.pageSize,
                sortMethod: 'desc',
                sortField: 'dateDebutVisibilite'
            }
        )
            .pipe(
                map(res => {
                    return {
                        totalPages: res.totalPages,
                        data: this.posteService.addReadMoreAndImageUrl(res)
                    };
                })
            )
            .subscribe(res => {
                this.postes.push(...res?.data);
                this.showMore = ((this.paginationParams.skip + 1) < res.totalPages);

                this.postsReady.emit(res?.data);
            });
    }

    setCurrentPosteScope(): void {
        this.selectedFournisseur = this.plateformeService.getCurrentGrossiste();

        if (
            !this.isPlateformeWinoffre && this.selectedFournisseur &&
            this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL', 'ROLE_ASSISTANT'])
        ) {
            this.currentPosteScope = ['G', 'F'];
            this.selectedFournisseur && (this.filterCriteria['fournisseursId'] = this.selectedFournisseur?.id as string);

        } else {
            //this.showFournisseurFilter = true;
            this.currentPosteScope = ['G', 'F'];
            this.filterCriteria['fournisseursId'] = 'T';
        }
    }

    openFilterModal(size: string) {
        this.modalService
            .open(this.popFilterMsg, {
                ariaLabelledBy: 'modal-basic-title',
                modalDialogClass: 'fs-radius-modal',
                size,
                backdrop: 'static',
                centered: true
            })
            .result.then(
                (result) => {
                    console.log(result);
                },
                (reason) => {
                    if (reason?.reset) {
                        this.setCurrentPosteScope();

                        this.filterCriteria = {
                            ...this.filterCriteria,
                            dateDebutVisibilite: null,
                            dateFinVisibilite: null,
                            type: 'T',
                            typeActualite: null
                        };
                        this.vider();
                    }

                    if (reason?.form) {
                        const { fournisseursId, ...values } = reason?.form;

                        this.currentPosteScope = (
                            fournisseursId !== 'T' &&
                            this.plateformeService.isPlateForme('DEFAULT')
                        ) ? ['F'] : this.currentPosteScope;

                        this.filterCriteria = { ...values, fournisseursId };

                        this.filterPostes();
                    }
                }
            );
    }

    readMore(target) {
        target.readMore = !target.readMore;
    }

    filterPostes() {
        this.paginationParams.skip = 0;
        this.postes = [];
        this.getListePostes();
    }

    vider() {
        this.paginationParams.skip = 0;
        this.postes = [];

        this.getListePostes();
    }

    afficherPlus() {
        this.paginationParams.skip += 1;
        this.getListePostes();
    }

    redirectToUrl(url: SafeUrl) {
        let sanitizedUrl: string | null = null;

        url && (sanitizedUrl = this.sanitizer.sanitize(4, url));
        sanitizedUrl && (window.open(sanitizedUrl, '_blank'));
    }

    toggleSwiperAutoplay() {
        const swiperInstance: Swiper = this.swiperRef?.nativeElement?.swiper;

        if (swiperInstance) {
            if (swiperInstance?.autoplay?.running) {
                swiperInstance?.autoplay?.stop();
            } else {
                swiperInstance?.autoplay?.start();
            }
        }
    }
}