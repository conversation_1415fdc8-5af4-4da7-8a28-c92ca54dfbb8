<ng-container *ngIf="postDisplayMode === 'liste'; else: postCardView">
  <ng-container *ngIf="postes?.length">
    <div class="row d-flex mx-0 p-0 w-100">
      <div class="col-12 p-0 m-0">
        <div class="card filter-card">
          <div class="card-body py-2 d-flex justify-content-center align-items-center">
            <span class="title-cstm">{{ 'Actualités' | uppercase }}</span>
          </div>
        </div>
      </div>
    </div>
  
    <div class="row w-100 my-2 mx-0 d-flex justify-content-center">
      <div class="col-12 isWinOffre px-0">
        <wph-complex-news-card *ngFor="let poste of postes" [post]="poste"></wph-complex-news-card>
        
        <div *ngIf="showMore" class="row justify-content-center mb-3">
          <button (click)="afficherPlus()" type="button" class="btn btn-md fw-semibold btn-info b-radius">
            <i class="mdi mdi-plus"></i> Afficher plus
          </button>
        </div>
      </div>
    </div>
  </ng-container>
</ng-container>


<!-- Poste filter modal start -->
<ng-template #popFilterMsg let-modal>
  <wph-filter-postes-modal [listeFournisseur]="listeFournisseur" [filterCriteria]="filterCriteria"
    [showFournisseurList]="showFournisseurFilter"></wph-filter-postes-modal>
</ng-template>
<!-- Post filter modal end -->

<ng-template #postCardView>
  <div class="card shadow-sm p-1 h-100 card-radius" 
  [ngClass]="{
    'info-card': (currentPlateforme$ | async) !== 'WIN_OFFRE', 
    'info-card-alt': (currentPlateforme$ | async) === 'WIN_OFFRE',
    'info-card-height-override': overrideMaxHeight
  }">
    <div class="row mx-0 p-1 w-100 d-flex align-items-center justify-content-between">
      <span *ngIf="(currentPlateforme$ | async) !== 'WIN_OFFRE' && (currentPlateforme$ | async) !== 'DEFAULT'" class="title text-uppercase col d-flex justify-content-center">Actualités et Messages</span>
      <span *ngIf="(currentPlateforme$ | async) === 'WIN_OFFRE'" class="py-1 px-0 border-left-cstm">
        <span class="text-dark h4  px-2">Actualités et Messages</span>
      </span>
      <span *ngIf="(currentPlateforme$ | async) === 'DEFAULT'" class="title text-uppercase col d-flex justify-content-center">
        <span *ngIf="isPinnedPostList">
          <i class="mdi mdi-pin mdi-24px mr-2"></i>
          Actualités épinglées
        </span>
        <span *ngIf="!isPinnedPostList">Actualités</span>
      </span>

      <span class="pointer-cus" (click)="openFilterModal('md')">
        <i class="mdi mdi-filter-variant mdi-24px icon-filter" title="filtrer"></i>
      </span>
    </div>
    <div id="pharmalien-swiper" class="row w-100 h-100">
      <swiper-container *ngIf="postes?.length; else: noPostesAvailable" class="h-100 p-0 sw-container" speed="2500"
        autoplay="true" loop="false" navigation="true" pagination="true" scrollbar="false" slidesPerView="1"
        centeredSlides="true" (pointerenter)="toggleSwiperAutoplay()" (pointerleave)="toggleSwiperAutoplay()"
        #swiperRef>

        <swiper-slide *ngFor="let post of postes">
          <wph-news-card [titre]="post?.titre" [overrideMaxHeight]="overrideMaxHeight" [imgActionLabel]="post?.libelleUrl" [videoUrl]="post?.videoUrl"
            [link]="post?.url ?? post?.imageUrl" [content]="post?.sujet" [createur]="post?.createur"
            [imgSrc]="post?.docImagePost?.idhash"></wph-news-card>
        </swiper-slide>

        <swiper-button-prev></swiper-button-prev>
        <swiper-button-next></swiper-button-next>
      </swiper-container>

      <ng-template #noPostesAvailable>
        <swiper-container class="h-100 p-0 sw-container" speed="1000" autoplay="true" loop="false" navigation="true"
          pagination="true" scrollbar="false" slidesPerView="1" centeredSlides="true" #swiperRef>
          <swiper-slide>
            <wph-news-card titre="Bienvenue à Pharmalien !" [showDefault]="true" [overrideMaxHeight]="overrideMaxHeight"
              content="Passez des commandes groupées entre pharmaciens et accédez à des remises avantageuses sur vos commandes en gros. Simplifiez vos approvisionnements et boostez vos économies dès aujourd'hui !"></wph-news-card>
          </swiper-slide>

          <swiper-button-prev></swiper-button-prev>
          <swiper-button-next></swiper-button-next>
        </swiper-container>
      </ng-template>

    </div>
  </div>
</ng-template>