import {
  HttpErrorResponse,
  HttpEvent,
  HttpEventType,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import {
  catchError,
  finalize,
  switchMap,
  take,
  tap
} from 'rxjs/operators';

import { LoaderService } from '../services/loader.service';
import { AuthService } from "../services/auth.service";
import { EventService, SelectedPlateforme } from '@wph/web/layout';
import { AlertService, PlateformeService } from '@wph/shared';

@Injectable()
export class AppHttpInterceptor implements HttpInterceptor {
  private isReauthModalShown: boolean = false;
  private replayableReqsCount: number = 0;
  private excludeUrls: string[] = ['user/auth', 'user/resetpassword', 'user/changepassword'];

  constructor(
    private authService: AuthService,
    private alertService: AlertService,
    private eventService: EventService,
    private loaderService: LoaderService,
    private plateformeService: PlateformeService,
  ) { }

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const url = request.urlWithParams;
    const targetPlateforme: SelectedPlateforme = this.plateformeService.getCurrentPlateforme();

    this.loaderService.show();

    request = this.setToken(request);

    if (targetPlateforme === 'COMMANDE_WEB' && request.method === 'POST' && !url.includes('search-produit')) {
      const code: number = this.plateformeService.getCurrentGrossiste()?.noeud?.codeSite;

      const codeSite = url.includes('checkdispo') ?
        { listeGrossistes: [code] } :
        { codeSite: code }

      request = request.clone({
        body: {
          ...request.body,
          ...codeSite
        }
      });
    }

    if (this.authService.isAuthTokenExpired(180) && !this.shouldExclude(url)) {
      this.loaderService.hide(), ++this.replayableReqsCount;

      if (!this.isReauthModalShown) {
        this.replayableReqsCount = 1;
        this.isReauthModalShown = true;

        this.alertService.expiredSession();
      }

      return this.alertService.reauthModalMsg$.pipe(
        take(1),
        switchMap(res => {
          if (res?.auth_success === true) {
            request = this.setToken(request);

            return this.handleNext(next, request).pipe(
              finalize(() => {
                --this.replayableReqsCount;

                if (this.replayableReqsCount === 0) {
                  this.isReauthModalShown = false;
                  this.eventService.broadcast('refreshSideBar', true);
                }
              })
            );
          } else {
            return throwError(() => new HttpErrorResponse({ status: 401 }));
          }
        }),
        catchError(error => throwError(() => error instanceof HttpErrorResponse ? error : new HttpErrorResponse(error)))
      );
    } else {
      return this.handleNext(next, request).pipe(
        finalize(() => this.loaderService.hide())
      );
    }
  }

  shouldExclude(url: string): boolean {
    return this.excludeUrls.some(excludeUrl => url?.includes(excludeUrl));
  }

  setToken(request: HttpRequest<any>) {
    const token = this.authService.getToken();
    if (token) {
      request = request.clone({
        setHeaders: {
          Authorization: 'Bearer ' + token
        }
      });
    }

    return request;
  }

  handleNext(next: HttpHandler, request: HttpRequest<any>) {
    return next.handle(request);
  }
}
