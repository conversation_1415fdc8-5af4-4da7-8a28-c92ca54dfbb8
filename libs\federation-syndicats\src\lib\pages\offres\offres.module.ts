import { NgModule } from '@angular/core';
import { OffresRoutingModule } from './offres-routing.module';
import { WebSharedModule } from '@wph/web/shared';
import { ListeOffresComponent } from './liste/liste-offres.component';
import { SharedModule } from '@wph/shared';
import { CommonModule } from '@angular/common';
import { NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { PagerModule } from '@progress/kendo-angular-pager';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { GroupeModule } from '../groupes/groupes.module';

@NgModule({
  declarations: [ListeOffresComponent],
  imports: [
    OffresRoutingModule,
    PagerModule,
    WebSharedModule,
    DropDownsModule,
    SharedModule,
    CommonModule,
    NgbNavModule,
    GroupeModule,
  ],
})
export class OffresModule {}
