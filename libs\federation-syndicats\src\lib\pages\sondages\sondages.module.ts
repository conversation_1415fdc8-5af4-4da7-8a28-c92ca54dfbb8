import { NgModule } from "@angular/core";
import { SondagesRoutingModule } from "./sondages-routing.module";
import { WebSharedModule } from "@wph/web/shared";
import { SharedModule } from "@wph/shared";
import { CommonModule } from "@angular/common";
import { SondagesGroupesListeComponent } from "./liste/sondages-liste-groupes.component";
import { MesSondagesListeComponent } from "./ma-liste/mes-sondages-liste.component";
import { SondagesMembresListeComponent } from "./Membres-liste/sondages-liste-membres.component";

@NgModule({
    declarations: [
        SondagesGroupesListeComponent,
        MesSondagesListeComponent,
        SondagesMembresListeComponent
      ],
    imports: [SondagesRoutingModule, WebSharedModule, SharedModule, CommonModule]

})
export class SondagesModule {}