<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-4">Mes Clients</h4>

    <div class="col-8 px-1">
      <div class="row justify-content-end align-items-center">
        <button (click)="openFilterModal(filterModal)" type="button" class="btn btn-sm btn-info m-1">
          <i class="mdi mdi-filter-variant"></i>
          Filtrer
        </button>
      </div>
    </div>
  </div>
</div>
<!-- END HEADER -->

<div class="card">
  <kendo-grid [data]="gridData" [pageable]="{
    buttonCount: 5,
    info: true,
    type: 'numeric',
    pageSizes: pageSizes,
    previousNext: true,
    position: 'bottom'
  }" [selectable]="false" (pageChange)="pageChange($event)" [pageSize]="navigation.pageSize" [skip]="navigation.skip"
    style="min-height: calc(100vh - 123px)" [resizable]="true" [sort]="gridSort" [sortable]="{mode: 'single'}"
    (sortChange)="gridSortChange($event)">

    <kendo-grid-column [width]="110" field="code" class="text-wrap">
      <ng-template kendoGridHeaderTemplate>
        <span class="text-wrap text-center">Code Pharmacie du Maroc</span>
      </ng-template>
      
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.clientGroupe?.code }}
      </ng-template>

    </kendo-grid-column>

    <kendo-grid-column [width]="180" field="raisonSociale" class="text-wrap">
      <ng-template kendoGridHeaderTemplate>
        <span class="text-wrap d-flex align-items-center">Raison Sociale</span>
      </ng-template>
      
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.clientGroupe?.raisonSociale }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column [width]="180" field="nomResponsable" class="text-wrap">
      <ng-template kendoGridHeaderTemplate>
        <span class="text-wrap d-flex align-items-center">Nom Responsable</span>
      </ng-template>
      
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.clientGroupe?.nomResponsable }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column [width]="180" field="ville" class="text-wrap">
      <ng-template kendoGridHeaderTemplate>
        <span class="text-wrap d-flex align-items-center">Ville</span>
      </ng-template>
      
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.clientGroupe?.ville }}
      </ng-template>
    </kendo-grid-column>
    
    <kendo-grid-column [width]="80" class="text-center no-ellipsis" [sortable]="false">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex align-items-center justify-content-center w-100">Actions</span>
        </ng-template>
        
        <ng-template kendoGridCellTemplate let-dataItem>
           <div class="d-flex justify-content-center k-gap-1">
                    <span (click)="openCredentialsModal(credentialsModal, dataItem)" class="circle circle-btn btn-warning pointer-cus" title="Consulter Identifiants">
                        <i class="mdi mdi-eye text-white"></i>
                    </span>

                    <span (click)="resetClientPassword(dataItem)" class="circle circle-btn btn-danger pointer-cus" title="Réinitialiser Mot de passe">
                        <i class="mdi mdi-lock-open text-white"></i>
                    </span>
                </div>
        </ng-template>
        
    </kendo-grid-column>

    <kendo-grid-messages pagerItems="lignes" pagerOf="de" pagerItemsPerPage="éléments par page"></kendo-grid-messages>

    <ng-template kendoGridNoRecordsTemplate>
      <span>Aucun résultat trouvé.</span>
    </ng-template>

  </kendo-grid>
</div>

<ng-template #credentialsModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">CONSULTER IDENTIFIANTS</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <div class="modal-body px-lg-0 px-2 py-2 m-0">
        <div class="row d-flex flex-wrap justify-content-start w-100 m-0">
            <div class="col-lg-6 col-12">
                <label for="raisonSociale" class="col-form-label text-dark">Raison Sociale</label>
                <div class="input-group">
                    <input id="raisonSociale" type="text" [readonly]="true" name="raisonSocialeGroupe"
                        [value]="selectedPharmacie?.clientGroupe?.raisonSociale ?? ''"
                        class="form-control form-control-md text-dark  b-radius">
                </div>
            </div>

            <div class="col-lg-6 col-12">
                <label for="nomResponsable" class="col-form-label text-dark">Nom Responsable</label>
                <div class="input-group">
                    <input id="nomResponsable" type="text" [readonly]="true" name="nomPharmacienGroupe"
                        [value]="selectedPharmacie?.clientGroupe?.nomResponsable ?? ''"
                        class="form-control form-control-md text-dark  b-radius">
                </div>
            </div>

            <div class="col-lg-6 col-12">
                <label for="codeClientGroupe" class="col-form-label text-dark">Identifiant Utilisateur</label>
                <div class="input-group">
                    <input id="codeClientGroupe" type="text" [readonly]="true" name="codeClientGroupe"
                        [value]="selectedPharmacie?.clientGroupe?.code ?? ''"
                        class="form-control form-control-md text-dark  b-radius">

                    <div class="input-group-append"
                        style="border-top-right-radius: 10px; border-bottom-right-radius: 10px;">
                        <button class="btn btn-md btn-dark text-white" type="button"
                            title="Copier l'identifiant utilisateur"
                            (click)="copyToClipboard(selectedPharmacie?.clientGroupe?.code, 'code')">
                            <i class="mdi mdi-content-copy"
                                [ngClass]="{'mdi-content-copy': !copyCode, 'mdi-check': copyCode}"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-12">
                <label for="mdp" class="col-form-label text-dark">Mot de passe par défaut</label>
                <div class="input-group">
                    <input id="mdp" type="text" [readonly]="true" name="nomPharmacienGroupe"
                        [value]="selectedPharmacie?.clientGroupe?.code + '2024'"
                        class="form-control form-control-md text-dark  b-radius">

                    <div class="input-group-append">
                        <button class="btn btn-md btn-dark text-white" type="button" title="Copier le mot de passe"
                            (click)="copyToClipboard(selectedPharmacie?.clientGroupe?.code + '2024', 'mdp')">
                            <i class="mdi mdi-content-copy"
                                [ngClass]="{'mdi-content-copy': !copyMdp, 'mdi-check': copyMdp}"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light">Fermer</button>
    </div>
</ng-template>

<ng-template #filterModal let-modal>
    <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Filtrer</h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss()">
        <i class="mdi mdi-close"></i>
    </button>
</div>

<form [formGroup]="filterForm" (ngSubmit)="appliquerFiltre(); modal.dismiss()" wphFocusTrap>
    <div class="p-2">
        <div class="form-group">
            <div class="row p-1">
                <div class="col-sm-6 col-12">
                    <label for="codeGroupe" class="col-sm-6 form-label p-0">Code Pharmacie du Maroc</label>
                    <input type="text" class="form-control form-control-md b-radius" id="codeGroupe" formControlName="code">
                </div>

                <div class="col-sm-6 col-12 mt-sm-0 mt-2">
                    <label for="raisonSociale" class="col-sm-6 form-label p-0">Raison Sociale</label>
                    <input type="text" class="form-control form-control-md b-radius" id="raisonSociale"
                        formControlName="raisonSociale">
                </div>
            </div>

            <div class="row p-1">
                <div class="col-sm-6 col-12">
                    <label for="nomResponsable" class="form-label">Nom Responsable</label>
                    <input type="text" class="form-control form-control-md b-radius" id="nomResponsable"
                        formControlName="nomResponsable">
                </div>

                <div class="col-sm-6 col-12 mt-sm-0 mt-2">
                    <label for="ville" class="col-sm-6 form-label p-0">Ville</label>
                    <input type="text" class="form-control form-control-md b-radius" id="ville" formControlName="ville">
                </div>
            </div>

        </div>
    </div>

    <div class="modal-footer">
        <button type="button" (click)="modal.dismiss()" class="btn btn-light" tabindex="-1">Fermer</button>
        <button type="button" (click)="viderFiltre(); modal.dismiss()" class="btn btn-secondary text-white" tabindex="-1">Vider</button>
        <button type="button" type="submit" class="btn btn-primary ml-1 text-white" tabindex="-1">Rechercher</button>
    </div>
</form>
</ng-template>