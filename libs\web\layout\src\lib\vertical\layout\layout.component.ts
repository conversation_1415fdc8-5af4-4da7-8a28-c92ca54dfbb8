import { Component, OnInit, AfterViewInit, Input, OnChanges, ChangeDetectorRef, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';

import {
  SIDEBAR_THEME_DARK, SIDEBAR_THEME_LIGHT, SIDEBAR_WIDTH_CONDENSED,
  SIDEBAR_WIDTH_SCROLLABLE
} from '../../shared/models/layout.model';
import { EventService } from "../../shared/services/event.service";
import { NavigationEnd, Router } from '@angular/router';
import { PlateformeService, Principal } from '@wph/shared';
import { Observable, Subject, filter, forkJoin, takeUntil } from 'rxjs';
import { FederationSyndicatService, FsOffreService } from '@wph/federation-syndicats';
import { AuthService } from '@wph/core/auth';
import { FeatureFlagService } from '@wph/web/shared';

@Component({
  selector: 'wph-vertical-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {

  @Input() isBoxed: boolean;
  @Input() isCondensed: boolean;
  @Input() isScrollable: boolean;
  @Input() sidebarTheme: string;
  @Input() sidebarType: string;

  @Input() demo: string;

  isInitCompleted: boolean;
  hideFooter: boolean = true;

  hideLeftSideBar: boolean;

  cstmStyle: HTMLStyleElement;
  currentPlateforme$: Observable<string>;

  isInactive: boolean;
  hasNoEmail: boolean;
  hasNoNumIce: boolean;

  shouldDisplayBanner: boolean;

  isResponsable: boolean;

  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  currentPlateforme: string;

  constructor(
    private router: Router,
    private cdr: ChangeDetectorRef,
    private authService: AuthService,
    private eventService: EventService,
    private fsOffreService: FsOffreService,
    private plateformeService: PlateformeService,
    private featureFlagService: FeatureFlagService,
    private federationSyndicatService: FederationSyndicatService
  ) {
    this.currentPlateforme$ = this.plateformeService.currentPlateforme$;

    if ((this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) &&
      !this.authService.hasAnyAuthority(['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'])
    ) {
      const userId = this.authService.getPrincipal()?.societe?.id;

      this.isResponsable = this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']);
      this.federationSyndicatService
        .getPharmacieById(userId)
        .subscribe((res) => {
          this.federationSyndicatService.inactiveAccount$.next(!res.statutMembreGroupe);
        });

      this.federationSyndicatService.inactiveAccount$
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe(accountState => {
          this.isInactive = accountState;
        });

      this.authService.principal$.pipe(takeUntil(this.unsubscribe$)).subscribe(principal => {
        if (principal) {
          this.checkForEmailAndNumIce(principal);
  
          if ((!this.hasNoEmail && !this.isInactive && !this.hasNoNumIce) || !this.shouldDisplayBanner) {
            this.removeInactiveBanner();
          } else if (this.hasNoEmail || this.isInactive || this.hasNoNumIce) {
            this.showInactiveBanner();
          }
        }
      });
    }

    if (this.plateformeService.isPlateForme('WIN_GROUPE') || this.plateformeService.isPlateForme('FEDERATION_SYNDICAT')) {
      // ? Get plateforme specific config parameters
        forkJoin([this.fsOffreService.getCommandeGroupeParameters()])
          .subscribe(res => {
            this.featureFlagService.loadFeatureFlags(res);
          });
    }
    
  }
  
  ngOnInit() {    
    this.isInitCompleted = false;

    this.updateFooterVisibility();
    this.checkForEmailAndNumIce();

    this.shouldDisplayBanner = !this.authService.hasAnyAuthority(['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR']);

    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.updateFooterVisibility();
      }
    });

    this.plateformeService.currentPlateforme$
      .pipe(
        takeUntil(this.unsubscribe$),
        filter(curr => curr !== this.currentPlateforme)
      )
      .subscribe(currentPlateforme => {
        this.currentPlateforme = currentPlateforme;
        this.cdr.detectChanges();
      });
    
    this.initializeSidebarClasses();
  }

  checkForEmailAndNumIce(principal?: Principal) {
    let testPrincipal: Principal;
    
    testPrincipal = principal ?? this.authService?.getPrincipal();

    this.hasNoEmail = !testPrincipal?.societe?.email;
    this.hasNoNumIce = !testPrincipal?.societe?.numIce;
  }

  navigateToUserAcc() {
    this.router.navigateByUrl('achats-groupes/account');
  }

  updateFooterVisibility(): void {
    if (this.router.url.includes('accueil')) {
      this.hideFooter = false;
    } else {
      this.hideFooter = true;
    }
  }

  initializeSidebarClasses(): void {
    // boxed vs fluid
    if (this.isBoxed) {
      document.body.setAttribute('data-layout-mode', 'boxed');
    } else {
      document.body.removeAttribute('data-layout-mode');
    }

    const mainLogo = document.getElementById('side-main-logo');
    // left sidebar theme
    switch (this.sidebarTheme) {
      case SIDEBAR_THEME_LIGHT:
        document.body.setAttribute('data-leftbar-theme', 'light');
        if (mainLogo) {
          mainLogo.setAttribute('src', 'assets/images/logo-dark.png');
        }
        break;
      case SIDEBAR_THEME_DARK:
        document.body.setAttribute('data-leftbar-theme', 'dark');
        if (mainLogo) {
          mainLogo.setAttribute('src', 'assets/images/logo.png');
        }
        break;
      default:
        document.body.setAttribute('data-leftbar-theme', 'default');
        if (mainLogo) {
          mainLogo.setAttribute('src', 'assets/images/logo.png');
        }
        break;
    }

    // left sidebar type
    switch (this.sidebarType) {
      case SIDEBAR_WIDTH_CONDENSED:
        document.body.setAttribute('data-leftbar-compact-mode', 'condensed');
        this.isCondensed = true;
        this.isScrollable = false;
        break;
      case SIDEBAR_WIDTH_SCROLLABLE:
        this.isScrollable = true;
        this.isCondensed = false;
        document.body.setAttribute('data-leftbar-compact-mode', 'scrollable');
        break;
      default:
        this.isCondensed = false;
        this.isScrollable = false;
        document.body.setAttribute('data-leftbar-compact-mode', 'fixed');
        break;
    }
  }

  ngOnChanges() {
    if (this.isInitCompleted) {
      this.initializeSidebarClasses();
    }
  }

  ngAfterViewInit() {
    this.isInitCompleted = true;
    document.body.removeAttribute('data-layout');

    this.showInactiveBanner();
  }

  showInactiveBanner(): void {
    setTimeout(() => {
      const hasInactiveBanner = document.getElementById('inactive-banner-row');

      if (hasInactiveBanner && !this.cstmStyle) {
        this.cstmStyle = document.createElement('style');

        this.cstmStyle.textContent = `
        .rowline { top: 115px !important; }
        .rowline+* {padding-top: 50px !important;}
        @media screen and (max-width: 991px) {
           .rowline { top: 60px !important; z-index: 5; }
           .page-title-box { padding-top: 0px !important;}
        }
        `;

        document.head.appendChild(this.cstmStyle);
      }
    }, 1000);
  }

  removeInactiveBanner(): void {
    if (this.cstmStyle) {
      document.head.removeChild(this.cstmStyle);
      this.cstmStyle = null;
    }

    this.shouldDisplayBanner = this.hasNoEmail = this.hasNoNumIce = this.isInactive = false;
  }

  /**
   * on settings button clicked from topbar
   */
  onSettingsButtonClicked() {
    this.eventService.broadcast('showRightSideBar');
  }

  /**
   * On mobile toggle button clicked
   */
  onToggleMobileMenu() {
    this.hideLeftSideBar = !this.hideLeftSideBar;
  }

  hideMenu() {
    if (window.innerWidth < 920) {
      this.eventService.displayMobileSidebar$.next(false);
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();

    if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
      this.removeInactiveBanner();
      this.federationSyndicatService.inactiveAccount$.next(null);
    }
  }
}
