import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { PageChangeEvent } from '@progress/kendo-angular-pager';
import { SortDescriptor } from '@progress/kendo-data-query';
import { Pagination } from '@wph/data-access';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { FederationSyndicatService } from '../../../services/federation-syndicats.service';
import { ActivatedRoute, Router } from '@angular/router';
import { GridDataResult, SelectionEvent } from '@progress/kendo-angular-grid';
import { AlertService, SocieteType } from '@wph/shared';
import { ExportPdf, ExportPdfService, UserInputService } from '@wph/web/shared';
import { SearchPharmacieEntreprise } from '../../../models/pharmacie-entreprise.model';
import { SuggestionFicheClient } from '../../../models/suggestion-fiche-client.model';
import { PharmacieEntrepriseCriteria } from '../../../models/pharmacie-entreprise-criteria.model';
import { HttpErrorResponse } from '@angular/common/http';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
@Component({
  selector: 'wph-pharmacies-list',
  templateUrl: './pharmacies-list.component.html',
  styleUrls: ['./pharmacies-list.component.scss'],
})


export class PharmaciesListComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  gridView: GridDataResult = { total: 0, data: [] };
  pharmacieSort: SortDescriptor[];
  editForm: FormGroup;
  isFormReady = false;
  filterList: FormControl = new FormControl();
  searchCriteria: PharmacieEntrepriseCriteria = {};

  navigation: Pagination = {
    skip: 0,
    pageSize: 15,
    sortField: 'createdAt',
    sortMethod: 'desc',
    totalElements: 0,
    last: false,
    unpaged: false,
  };
  displayFilter: boolean;
  filterForm: FormGroup;

  // stautsLabelsValues: any[] = [
  //   { label: 'Tout', value: null },
  //   { label: 'Nouvelle', value: 'T' },
  //   { label: 'Validés', value: 'V' },
  //   { label: 'Rejetés', value: 'R' }];

  exportPdfRef: ExportPdf;

  selectFilterData = [
    { label: 'Tous (non supprimés)', value: '' },
    { label: 'Supprimées', value: 'deleted' },
    { label: 'Membre du Groupe', value: 'inGroup' },
    { label: 'Non Membre du Groupe', value: 'notInGroup' },
    { label: 'Supprimées et Membre du Groupe', value: 'deletedInGroup' },
    { label: 'Supprimées et Non Membre du Groupe', value: 'deletedNotInGroup' },
  ];


  constructor(
    private router: Router,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private alertService: AlertService,
    private exportPdfServ: ExportPdfService,
    private userInputService: UserInputService,
    private fedSyndicatService: FederationSyndicatService,
  ) {
    this.initFilterGroup();
  }

  get isInactive$() {
    return this.fedSyndicatService.inactiveAccount$;
  }

  ngOnInit() {
    this.buildExport();
    this.listenToSearchFilterChanges();
    this.searchCriteria = new PharmacieEntrepriseCriteria({
      deletedOnly: false
    });
    this.searchPharmacie();
  }

  ngOnDestroy() {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  OnPageChange(event: number): void {
    this.searchPharmacie();

  }

  // This method will be triggered when the user toggles the deletedOnly switch
  deletedPhOnly(checked: boolean): void {
    this.searchCriteria.deletedOnly = checked;
    this.searchPharmacie();
  }


  searchPharmacie(): void {
    // Set typeEntreprise to search only for 'CLIENT'
    const filteredCriteria: PharmacieEntrepriseCriteria = {
      ...this.searchCriteria,
      typeEntreprise: [SocieteType.CLIENT],
    };

    this.gridView = { total: 0, data: [] };


    // Debugging: Check the final payload
    console.log('Final Request Payload:', filteredCriteria);


    const paginationParams = {
      page: this.getPageNumber(this.navigation.skip, this.navigation.pageSize),
      size: this.navigation.pageSize ?? 15,
      sort: this.navigation.sortField
        ? `${this.navigation.sortField},${this.navigation.sortMethod ?? 'desc'}`
        : 'createdAt,desc',
    };


    console.log('Filtered Criteria:', filteredCriteria);
    console.log('Pagination Params:', paginationParams);

    this.fedSyndicatService
      .searchPharmacie(filteredCriteria, paginationParams)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (res: SearchPharmacieEntreprise) => { 
          this.gridView = {
            total: res.totalElements ?? 0,
            data: res.content ?? [],
          };

          // Update the total elements in the navigation object
          this.navigation.totalElements = res.totalElements ?? 0;
          this.exportPdfRef.setData(this.gridView.data);
        },
        (error) => {
          console.error('Error fetching data', error);
        }
      );
  }

  buildExport(): void {
    this.exportPdfRef = this.exportPdfServ
      .ref<SuggestionFicheClient>()
      .setTitle('Liste des Pharmacies du Maroc')

      // Add the "Membre Groupe" column
      .addColumn('*', 'Membre Groupe', {
        width: 70,
        transform: (dataItem) => dataItem?.isMembreGroupe ? "Oui" : "Non"
      })

      .addColumn('nomResponsable', 'Résponsable', {
        width: 170
      })

      .addColumn('raisonSociale', 'Raison Sociale', {
        width: 170,
        transform: (value) => `PH. ${value}`
      })

      .addColumn('*', 'Ville / Localité', {
        width: 100,
        transform: (dataItem) => dataItem?.ville || dataItem?.localite || 'indisponible'
      })

      .addColumn('telephone', 'Téléphone', {
        width: 120,
        transform: (value) => value || 'indisponible'
      });

    if (!this.searchCriteria.deletedOnly) {
      this.exportPdfRef.addColumn('statutEntreprise', 'Statut', {
        width: 100,
        transform: (value) => value ? 'Actif' : 'Inactif'
      });
    }

    // Set the data for the export
    this.exportPdfRef.setData(this.gridView.data);
  }

  initFilterGroup(): void {
    this.filterForm = this.fb.group({
      nomResponsable: new FormControl(null),
      raisonSociale: new FormControl(null),
      ville: new FormControl(null),
      localite: new FormControl(null),
      filterOptions: new FormControl(null)
    });
  }

  listenToSearchFilterChanges(): void {
    this.filterList.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(200),
        distinctUntilChanged()
      )
      .subscribe((res: string) => {
        if (res?.length) {
          this.searchCriteria['raisonSociale'] = res;
          this.searchPharmacie();
        } else {
          this.searchCriteria.raisonSociale = res ? res : null;
          this.searchPharmacie();
        }
      });
  }

  appliquerFiltre(event: Event): void {
    event.preventDefault();
    if (this.filterForm.valid) {
      const formValue = this.filterForm.getRawValue();

      const cleanFormValue = Object.fromEntries(
        Object.entries(formValue).filter(([_, v]) => v != null)
      ) as { [key: string]: string };
      this.searchCriteria.isMembreGroupe = null;
      this.searchCriteria.deletedOnly = false;
      this.navigation.skip = 0;
      this.searchCriteria = new PharmacieEntrepriseCriteria({
        ...this.searchCriteria,
        nomResponsable: cleanFormValue['nomResponsable'] || null,
        raisonSociale: cleanFormValue['raisonSociale'] || null,
        localite: cleanFormValue['localite'] ? [cleanFormValue['localite']] : null,
        ville: cleanFormValue['ville'] || null,
      });

      switch (cleanFormValue['filterOptions']) {
        case 'inGroup':
          this.searchCriteria.isMembreGroupe = true;
          break;
        case 'notInGroup':
          this.searchCriteria.isMembreGroupe = false;
          break;
        case 'deleted':
          this.searchCriteria.deletedOnly = true;
          break;
        case 'deletedInGroup':
          this.searchCriteria.isMembreGroupe = true;
          this.searchCriteria.deletedOnly = true;
          break;
        case 'deletedNotInGroup':
          this.searchCriteria.isMembreGroupe = false;
          this.searchCriteria.deletedOnly = true;
          break;
        default:
          // Default case is already handled by the reset above
          break;
      }

      this.searchPharmacie();
    } else {
      console.error('Filter form is invalid');
    }
  }

  activerOuDesactiverPH(event: Event, dataItem: any): void {
    event.stopPropagation();

    const currentStatus = dataItem.statutEntreprise;
    const action = currentStatus ? 'désactiver' : 'activer';
    const confirmationMessage = `Êtes-vous sûr de vouloir ${action} la pharmacie "${dataItem.raisonSociale}" ?`;

    this.userInputService.confirmAlt('Confirmation', confirmationMessage).then(
      () => {
        if (currentStatus) {

          this.fedSyndicatService.desactiverPharmacieEntreprise(dataItem.id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(
              () => {
                dataItem.statutEntreprise = false;
                this.alertService.successAlt(`La pharmacie a été désactivée avec succès.`, 'Pharmacie désactivée', 'MODAL');
              },
              (error) => {
                console.error('Erreur lors de la désactivation de la pharmacie', error);
                const errorMessage = error?.error?.message || 'Erreur lors de la désactivation de la pharmacie';
                this.alertService.error(errorMessage, 'MODAL');
              }
            );
        } else {

          this.fedSyndicatService.activerPharmacieEntreprise(dataItem.id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(
              () => {
                dataItem.statutEntreprise = true;
                this.alertService.successAlt(`La pharmacie a été activée avec succès.`, 'Pharmacie activée', 'MODAL');
              },
              (error) => {
                console.error('Erreur lors de l\'activation de la pharmacie', error);
                const errorMessage = error?.error?.message || 'Erreur lors de l\'activation de la pharmacie';

                this.alertService.error(errorMessage, 'MODAL');
              }
            );
        }
      },
      () => {
        console.log('Action annulée par l\'utilisateur.');
      }
    );
  }

  toggleSupprimer(event: Event, dataItem: any): void {
    event.stopPropagation();

    // Determine if the pharmacy is currently deleted or not based on the search criteria
    const isDeleted = this.searchCriteria.deletedOnly;
    const action = isDeleted ? 'restaurer' : 'supprimer';
    const confirmationMessage = `Êtes-vous sûr de vouloir ${action} la pharmacie "${dataItem.raisonSociale}" ?`;
    if (dataItem.isMembreGroupe) {
      this.alertService.warning(
        'La pharmacie est membre dans un groupe et ne peut pas être supprimée.',
        'MODAL'
      );
      return;
    }

    if (!isDeleted && dataItem.statutEntreprise && !dataItem.isMembreGroupe) {
      this.alertService.warning(
        'La pharmacie est active et ne peut pas être supprimée. Veuillez d\'abord la désactiver.',
        'MODAL'
      );
      return;
    }

    this.userInputService.confirmAlt('Confirmation', confirmationMessage).then(
      () => {
        this.fedSyndicatService.togglePharmacieDeletionStatus(dataItem.id)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe(
            () => {
              const successMessage = isDeleted
                ? `La pharmacie "${dataItem.raisonSociale}" a été restaurée avec succès.`
                : `La pharmacie "${dataItem.raisonSociale}" a été supprimée avec succès.`;

              this.alertService.successAlt(successMessage, isDeleted ? 'Pharmacie restaurée' : 'Pharmacie supprimée', 'MODAL');
              this.searchPharmacie();
            },
            (error) => {
              console.error('Erreur lors de la mise à jour du statut de suppression/restauration', error);
              const errorMessage = error?.error?.message || `Erreur lors de la ${isDeleted ? 'restauration' : 'suppression'} de la pharmacie`;
              this.alertService.error(errorMessage, 'MODAL');
            }
          );
      },
      () => {
        console.log('Action annulée par l\'utilisateur.');
      }
    );
  }


  synchroniser(): void {
    this.userInputService.confirmAlt('Confirmation', 'Êtes-vous sûr de vouloir synchroniser la base des pharmacies du Maroc ?').then(
      () => {
        this.alertService.successAlt(`Synchronisation en cours...`, 'Synchronisation', 'MODAL');

        this.fedSyndicatService.synchroniserBasePharmacie().subscribe({
          next: (nbrPharmacies) => {
            const plural = nbrPharmacies > 1 ? 's' : '';
            this.modalService.dismissAll();

            this.vider();
            this.alertService.successAlt(`La base des pharmacies du Maroc a été synchronisée avec succès. <b>${nbrPharmacies}</b> nouvelle${plural} pharmacie${plural} synchronisée${plural}.`, 'Synchronisée', 'MODAL');
          }, 
          error: (err) => {
            this.modalService.dismissAll();
            throw new HttpErrorResponse(err);
          }
        });
      },
      () => null
    );
  }


  vider(): void {
    this.navigation.skip = 0;
    this.searchCriteria = new PharmacieEntrepriseCriteria({});

    this.filterForm.reset();

    this.searchPharmacie();
  }

  get filterField() {
    return this.filterForm.controls;
  }

  /* -------------------------------------------------------------------------- */

  /*                          calc page for pagination                          */
  /* -------------------------------------------------------------------------- */
  getPageNumber(skip: number, pageSize: number) {
    return Math.floor(skip / pageSize);
  }

  /* -------------------------------------------------------------------------- */
  /*                              pagination event                              */
  /* -------------------------------------------------------------------------- */
  pageChange(event: PageChangeEvent): void {
    if (
      event.skip !== this.navigation.skip ||
      event.take !== this.navigation.pageSize
    ) {
      this.navigation.skip = event.skip;
      this.navigation.pageSize = event.take;

      this.searchPharmacie();
    }
  }
  /* -------------------------------------------------------------------------- */
  /*                   check input ville valid or not on blure                  */
  /* -------------------------------------------------------------------------- */

  pharmacieGridSort(sort: SortDescriptor[]) {
    this.pharmacieSort = sort;
    if (
      this.pharmacieSort &&
      this.pharmacieSort.length > 0 &&
      this.pharmacieSort[0].dir
    ) {
      this.navigation.sortField = sort[0].field;
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
    }
    this.searchPharmacie();
  }

  suggererPharmacie(): void {
    this.router.navigateByUrl('/achats-groupes/pharmacie-maroc/suggerer');
  }


  consulterSuggestion(id: string, readOnly = true): void {
    this.router.navigate(['..', 'edit', id], {
      relativeTo: this.route,
      queryParams: { readOnly },
    });
  }

  onRowClick(event: SelectionEvent): void {
    const id = event.selectedRows[0].dataItem.id;
    this.consulterSuggestion(id, true);
  }
  formatter = (result: any) => {
    if (result.libelle) {
      return result.libelle.toUpperCase();
    }
  };

  onModifyClick(event: Event, dataItem: any): void {
    event.stopPropagation();
    this.router.navigate(['..', 'edit', dataItem.id], {
      relativeTo: this.route,
      queryParams: { readOnly: false }
    });
  }
  // onAnnulerClick(event: Event, dataItem: any): void {
  //   event.stopPropagation();
  //   if (!dataItem.statutEntreprise) {
  //     this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir annuler la suggestion pour: <b>${dataItem?.raisonSociale ?? ''}</b> ?`)
  //       .then(
  //         () => {
  //           this.fedSyndicatService.annulerSuggestion(dataItem.id).subscribe(
  //             () => {
  //               this.alertService.successAlt(`La suggestion pour "${dataItem?.raisonSociale}" a été annulée avec succès!`, undefined, 'MODAL');
  //               this.searchPharmacie();
  //             },
  //             (error) => {
  //               console.error('Error deleting suggestion', error);
  //             }
  //           );
  //         },
  //         () => null
  //       );
  //   }
  // }
}
