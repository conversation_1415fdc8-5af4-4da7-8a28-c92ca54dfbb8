@import 'swiper/scss';
@import '@ionic/angular/css/ionic-swiper';
@import 'swiper/scss/pagination';

.swiper{
  width:100%;
  height:calc(100vh - 106px);;
  margin-top: 50px;
}
.swiper .swiper-slide{
  justify-content: flex-start !important;
  align-items: flex-start !important;
}
.swiper-pagination-bullet {
  margin: -6px var(--swiper-pagination-bullet-horizontal-gap, 4px) !important;
  background: #F3F3F3;
  min-width: 36px !important;
  height: 36px !important;
  opacity: unset !important;
  line-height: 33px;
  font-weight:bold !important;
  color:#393C4B !important;
  &:nth-child(1){
    margin-left:18px !important;
  }
  &:last-child{
    margin-right:18px !important;
  }
}
.commande-preview-pagination{
  height: 37px;
  width: 100%;
  padding-bottom: 3px;
}
.swiper-button-prev, .swiper-button-next{
  position:absolute;
  font-size: 35px;
  top: 0px;
  z-index: 12;
}
.swiper-button-next{
  right:-10px;
}
.swiper-button-prev{
  left:-10px;
}
.swiper-pagination-bullet-active{
  background:#AADEF8 !important;
}
.disabled-nav{
  color: #A1A1A1 !important;
}
.pack-wrapper{
  background-color: #f3f3f3;
  width: 100% !important;
  display: flex;
  border: 1px solid #e7e7e7;
  border-radius: 5px;
  margin-bottom:10px !important;
  flex-direction: column !important;
  ion-label{
    border-bottom:1px solid #f3f3f3;
    padding:8px !important;
  }
}

.pack-wrapper-alt {
  background-color: #f1a52110 !important;
  border: 1px solid #e9c079 !important;

  ion-item {
    --inner-padding-end: 10px !important;
  }

  ion-label {
    border-bottom: 1px solid #e9c079 !important;
  }

  ion-list {
    background-color: #f1a52100 !important;
  
    ion-item {
      --background: #f1a52100 !important;
      --min-height: 30px !important; 
      padding-top: 6px
    }
  }
}

.pack-wrapper-primary {
  background-color: #219af110 !important;
  border: 1px solid #79c2e9 !important;

  ion-item {
    --inner-padding-end: 10px !important;
  }

  ion-label {
    border-bottom: 1px solid #79c2e9 !important;
  }

  ion-list {
    background-color: #219af100 !important;
  
    ion-item {
      --background: #219af100 !important;
      --min-height: 30px !important; 
      padding-top: 6px
    }
  }
}

.pack-wrapper-danger {
  background-color: #e979790c !important;
  border: 1px solid #e97979 !important;

  ion-item {
    --inner-padding-end: 10px !important;
  }

  ion-label {
    border-bottom: 1px solid #e97979 !important;
  }

  ion-list {
    background-color: #f1a52100 !important;
  
    ion-item {
      --background: #e979790c !important;
      --min-height: 30px !important; 
      padding-top: 6px
    }
  }
}

ion-item.item-palier {
  --background: transparent !important;
  border-top:.5px solid #ddd !important;
}
.normal-title{
  font-size: 15px !important;
  font-weight: bold !important;
}
.mt-list{
  margin-top: 0px;
  margin-bottom: 0px;
}
.palier-item{
  justify-content: space-between !important;
  align-items: center !important;
}
.title-wrapper{
  display:flex;
  justify-content: space-between;
  align-items: center;
  width:100%;
  margin-top: -7px;
  margin-bottom: -2px;
}
.details-wrapper{
  width: 100%;
  padding-top: 15px;
}
.fixed-extra-header{
  position: fixed;
  height: 50px;
  background: white;
  width: 100%;
  border-bottom:1px solid #dedede;
  box-shadow: 0px 2px 6px -6px #000;
  .swiper-pagination-bullets.swiper-pagination-horizontal {
    bottom: 0px !important;
    overflow: scroll;
    width: 100%;
    display: inline-flex;
    justify-content: start;
    align-items: center;
    height: 100%;
  }
}
.remise-badge{
  font-size: 12px !important;
  margin-left: 3px;
  background: white;
  color: #000;
  border: 0.5px solid #ddd;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  // ripple effect styling
  position: relative;
  overflow: hidden;
  .ripple {
    color: rgba(61,165,217, 0.4);
  }
  // //
  ion-icon {
    font-size: 16px;
    margin-right: 8px;
  }
}
.alert-head.sc-ion-alert-md{
  padding-top:10px;
  padding-bottom:10px;
}
.alert-head.sc-ion-alert-md {
  border-bottom: 1px solid #e7e7e7;
}
.alert-head.sc-ion-alert-md+.alert-message.sc-ion-alert-md {
  padding-top: 15px;
}
.alert-warning .alert-button-group {
  padding: 0px 0 0px 0px !important;
}
.alert-button.sc-ion-alert-ios {
  line-height: 39px;
}
.badge-input{
  border: 0.5px solid #ddd;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
  --min-height:12px !important;
  ion-input{
    --padding-bottom: 1px !important;
    --padding-top: 1px !important;
    padding-left:2px;
    padding-right:2px;
    margin-left:6px !important;
  }
  border-radius: 50px;
}
.w-input-total{
  width:90px !important;
  font-size: 13px !important;
}
.w-input-qt{
  width:60px !important;
  font-size: 13px !important;
}
.ex-m{
  margin-top:12px !important;
  margin-right:10px !important;
}


.product-wrapper{
  border: 0.3px solid #ddd;
  width: 100%;
  height: 70px;
  border-radius: 10px;
}
.product-title{
  h5{
    font-size:13px !important;
    text-align: start !important;
    margin: 6px 0px !important;
    font-weight: bold !important;
  }
}
.m-product-left{
  margin-left:8px !important;
}
.m-product-right{
  margin-right:8px !important;
}
.w-product-option{
  width:75px !important;
  font-size:13px !important;
  text-align: center;
  margin-right:2px !important;
}
.p-product{
  padding-left: unset;
  padding-right: unset;
  padding-inline-start: var(--ion-padding, 7px);
  padding-inline-end: var(--ion-padding, 7px);
  margin-bottom: 2px;
  margin-top: 2px;
}
.i-product-wrapper{
  --min-height: 83px !important;
  height: 68px;
  width: 50px;
  position: relative;
  border-radius: 10px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  ion-input{
    border: 0.3px solid #8e8e8e24;
    --padding-bottom: 1px !important;
    --padding-top: 0px !important;
    padding-left: 2px;
    padding-right: 2px;
    margin-left: -7px !important;
    min-height: 61px;
    margin-top: -1px;
    text-align: center !important;
    font-size:14px !important;
    --padding-start: 2px;
  }
}
.btn-product{
  position: absolute;
  z-index: 100;
  height: 64px;
  --border-radius: 13px !important;
  width: 20px;
  font-size: 0px;
  box-shadow: none !important;
  --box-shadow:none !important;
  --background: #acd9ee !important;
  color: #063b54;
  ion-icon{
    font-size:22px !important;
  }
}
.btn-left{
  left: -19px;
  top: -2px;
}
.btn-right{
  right: -14px;
  top: -2px;
}
.product-remise{
  margin-left: 6px;
  font-size: 14px;
  margin-top: 0px;
  margin-right: -17px;
}
.product-conditions{
  display:flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  span{
    margin-top: 15px;
    margin-right: 2px;
    font-size: 12px !important;
    font-weight: bold;
  }
}
.second-line-product{
  background:#F3F3F3 !important;
}
.error-input{
  background: rgba(255, 174, 182, 0.5) !important;
  color: inherit !important;
}
.valid-input {
  background: rgba(168, 252, 187, 0.5) !important;
}
.p-product-pack{
  padding-inline-start: var(--ion-padding, 4px) !important;
  padding-inline-end: var(--ion-padding, 4px) !important;
  margin-top: 20px !important;
  margin-bottom: 7px !important;
  position:relative;
}
.product-pack{
  border:1px solid #ddd;
  width: 100%;
  border-radius: 10px;
  padding-top: 7px;
  padding-bottom: 4px;
  h6{
    position:absolute;
    top: -27px;
    right: 16px;
    background: white;
    font-weight: bold;
  }
  .pack-condition{
    position:absolute;
    min-height: 21px;
    min-width: 42px;
    left: 25px;
    font-size: 13px;
    top: -10px;
    padding: 2px 8px;
    border-radius: 13px;
  }
}
.cmd-btns{
  --background: white;
  color: black;
}
.pack-error{
  background: #FFAEB6;
  color: #5D040D;
}
.pack-success{
  background-color: #A8FCBB;
  color: #A8FCBB;
}
.inner-cmd-wrapper{
  width: 100%;
  overflow-y: scroll;
  height:calc(100vh -  160px) !important;
}
//.cmd-footer{
//  // position: fixed;
//  // bottom:0px !important;
//  width: 100%;
//  height:91px;
//  z-index: 100;
//  background-color: #3DA5D9;
//}
.footer-wrapper{
  margin-top:1px;
  margin-bottom:0px;
}
.ion-p-action{
  padding-left: 0px;
  padding-right: 0px;
}

ion-modal#example-modal {
  --width: 90%;
  --min-width: 250px;
  --height: fit-content;
  --border-radius: 6px;
  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4);
  padding-top:150px;
  align-items: flex-start;
}

ion-modal#example-modal ion-icon {
  padding-bottom: 10px;
  padding-top: 10px;
  width: 90%;
  height: 48px;
  color: #aaaaaa;
}

ion-modal#example-modal .wrapper {
  padding-bottom: 10px;
  padding-top: 10px;
}
.mx-sm{
  margin-right:5px;
  font-size: 14px;
}
.scrollable-list{
  overflow-y: scroll;
  height: calc(100vh - 185px);
  padding-bottom: 100px;
  ion-item{
    --padding-start:10px !important;
  }
}
.padding-lg{
  padding-bottom: 187px !important;
}
.padding-md {
  padding-bottom: 60px !important;
}
.special-conditions{
  background: #CCE5FF;
  border: 1px solid #B8DAFF;
  border-radius: 8px;
  color:#004085 !important;
  ion-label{
    display: flex !important;
    justify-content: flex-start;
    align-items: center;
    margin-left: 8px;
    margin-top: 8px;
    margin-bottom:10px !important;
    ion-icon{
      margin-right: 4px !important;
    }
  }
  ion-list{
    padding-bottom:0 !important;
  }
  ion-item{
    --min-height:20px !important;
    --background: #CCE5FF !important;
    h5{
      font-size:12px !important;
      margin-top:0px !important;
      color:#004085 !important;
      font-weight: normal;
      &:last-child{
        margin-bottom:5px !important;
      }
    }
  }
}
.palier-item-height{
  --min-height: 35px !important;
}

.error-container {
  width: 100%;
  text-align: center;
  background: #f62222;
  color: var(--cs-background-light);
  font-size: 13px;
  padding: 3px;
}

.warning-container {
  width: 100%;
  text-align: center;
  background: #F1A621 !important;
  color: white;
  font-size: 13px;
  padding: 3px;
}

.text-danger {
  color: red;
}

// Wrapper styles

ion-item.cmd-details {
  width: 100%;
  margin-bottom: 10px;
  .wrapper-item, .inner-wrapper-item{
    display:flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100%;
    line-height: 1.1rem;
    overflow-wrap: break-word;
    text-wrap: wrap;
    overflow: visible;
  }
  .wrapper-item{
    flex-direction: column;
    padding: 10px;
    background: transparent !important;
    border-radius: 5px;
    margin-top: 4px;
    margin-bottom: 4px;
    border: 1px solid #ddd;
  }

  .badge-cmd{
    ion-badge{
      font-size:12px !important;
      font-weight: normal !important;
    }
    .badge-danger{
      --background:#e76565;
    }
    .badge-success{
      --background:#5db67f;
    }
    .badge-warning{
      --background:#e78e3a;
    }
  }

  .wrapper-child {
    margin: 6px 0 6px 0;
    font-size: 14px;
    text-align: start;
    line-height: 1.2rem;
  }

  .date-wrapper-child {
    margin: 6px 0 6px 0;
    font-size: 12px;
  }

  .date-wrapper-child:first-of-type {
    text-align: left !important;
  }

  .date-wrapper-child:last-of-type {
    text-align: right !important;
  }

  .wrapper-child-alt {
    line-height: 1.3rem !important;
    b {
      font-size: 16px;
    }
  }
}

.info-badge { 
  color: #000;
  font-size: 12px !important;
  font-weight: 600;
  background: #FBDE79 !important;
  border-color: #DCC600 !important;
}

.ajouter-client-btn {
  width: 100%;
  height: 35px;
  --ripple-color: transparent;
  --background: #F1A621 !important;
}

.client-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 35px;
  font-size: 12px;
  border-radius: 2px;
  color: #fff;
  font-weight: 600;
  background: #F1A621 !important;
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
 
  ion-icon {
    color: #fff;
    --ionicon-stroke-width: 50px !important;
    font-size: 16px !important;
    margin-right: 10px;
  }
}

.client-code-badge {
  font-size: 12px !important;
  background-color: #F1A621 !important;
}

.client-code-badge-primary {
  font-size: 12px !important;
  background-color: var(--cs-background-primary) !important;
}

.motif-refus-container {
  font-size: 14px;
  font-weight: 300;
  margin-left: 10px;
  margin-right: 16px;
  margin-bottom: 10px;
  border-radius: 5px;
  --background: #e979790c !important;
  border: 1px solid #e97979 !important;

  span {
    color: darken(#e97979, 20);
    font-weight: 500 !important;
  }
}