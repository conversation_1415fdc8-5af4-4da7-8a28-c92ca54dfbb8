import { Societe } from "@wph/shared";
import { Pageable, Sort } from "./listCommandesCriteria";
import { Catalogue } from "@wph/data-access";

export class ProduitDTO {
    id: number;
    codeProduit: string;
    codeBarre: string;
    codeAbarre: string;
    libelle: string;
    libelleCategorie: string;
    libelleFamilleTarifaire: string;
    libelleForme: string;
    codeLabo: string;
    libelleLabo: string;
    pfht: number;
    pph: number;
    ppv: number;
    tva: number;
    tauxTva?: number;
    fournisseur?: Societe;
    prixVenteHt?: number;
    libelleProduit?: string;
    prixVenteTtc?: number;
    catalogue?: Catalogue;
    codeProduitCatalogue?: string;


    disponibiliteCode: string;
    disponibiliteLibelle?: string;
}


export class ListProduitCriteria {
    content: ProduitDTO[];
    pageable: Pageable;
    last: boolean;
    totalElements: number;
    totalPages: number;
    size: number;
    number: number;
    sort: Sort;
    numberOfElements: number;
    first: boolean;
    empty: boolean;
}