<div id="win-offre-swiper" class="row d-flex mx-0 w-100" [style.height]="height" [style.maxHeight]="maxHeight">
    <swiper-container *ngIf="listeOffres?.length; else: placeholderSwiperContainer" class="p-0 sw-container"
        speed="2500" autoplay="true" loop="false" effect="fade" navigation="true" pagination="true" scrollbar="false"
        slidesPerView="1" centeredSlides="true" (pointerenter)="toggleSwiperAutoplay()"
        (pointerleave)="toggleSwiperAutoplay()" #swiperRef>

        <ng-container *ngFor="let offre of listeOffres" [ngTemplateOutlet]="offreSlideTemplate"
            [ngTemplateOutletContext]="{offre: offre}"></ng-container>

        <swiper-button-prev></swiper-button-prev>
        <swiper-button-next></swiper-button-next>
    </swiper-container>

    <!-- Placeholder swiper container -->
    <ng-template #placeholderSwiperContainer>
        <swiper-container class="h-100 p-0 sw-container" speed="2500" autoplay="true" loop="false" navigation="false"
            pagination="true" scrollbar="false" slidesPerView="1" centeredSlides="true"
            (pointerenter)="toggleSwiperAutoplay()" (pointerleave)="toggleSwiperAutoplay()" #swiperRef>
            <ng-container [ngTemplateOutlet]="offreSlideTemplate"></ng-container>
        </swiper-container>
    </ng-template>

</div>

<ng-template #offreSlideTemplate let-offre="offre">
    <swiper-slide *ngIf="offre; else: placeholderSlide">
        <div (click)="ctaBtn(offre)" class="img-container pointer-cus" [title]="offre?.canPlaceOrder ? 'Commander' : 'Consulter'" wphTrackInteraction
        [eventData]="{name: 'OFFRE: ' + offre?.titre, type: 'Offre', author: offre?.offreur?.raisonSociale}">
            <div class="offre-info-container mx-0 row h-100 w-100 d-flex justify-content-start">
                <div class="col-12 m-0 p-0 h-100 d-flex align-items-end">
                    <div class="row d-flex mx-0 w-100">

                        <div class="col-9 mx-1 my-2 p-0 d-flex align-items-center " style="margin-top: 50%;">
                            <button class="btn btn-warning text-white h4 btn-rounded">
                                <i *ngIf="offre?.canPlaceOrder" class="bi bi-cart-plus-fill mr-1"
                                    style="font-size: 1.2rem;"></i>
                                <span class="h4">{{offre?.canPlaceOrder ? 'Commander' : 'Consulter'}}</span>
                                <i *ngIf="!offre?.canPlaceOrder" class="bi bi-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <img [src]="getOffreImage(offre?.docImageOffre?.idhash)" [style.objectFit]="objectFit" [style.maxHeight]="maxHeight" alt="offre image">
        </div>
    </swiper-slide>

    <ng-template #placeholderSlide>
        <swiper-slide>
            <div class="img-container">
                <div class="offre-info-container mx-0 row h-100 w-100 d-flex justify-content-start">
                    <div class="col-12 m-0 p-0">
                        <div class="row d-flex mx-0 w-100">
                            <div class="col-10 m-0 p-0">
                                <h1 class="text-white" [ngClass]="{'truncate-two-lines': truncateTitle}">Découvrez des offres marché à remises exceptionnelles.</h1>
                            </div>
                            <div class="col-10 my-2 mx-1 px-2 py-0 border-left-cstm d-flex align-items-center">
                                <p class="m-0">
                                    <span class="text-white">Faites des économies en passant vos commandes sur nos
                                        offres et profitez de
                                        remises exclusives.</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <img src="assets/images/cat-img2.jpeg" [style.maxHeight]="maxHeight" alt="placeholder offre image">
            </div>
        </swiper-slide>
    </ng-template>

</ng-template>