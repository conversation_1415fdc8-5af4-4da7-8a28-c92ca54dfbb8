<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-9">Visualisation des données Google Analytics</h4>

        <div class="col-3 px-1">
            <div class="row justify-content-end align-items-center">
                <button (click)="openModal(analyticsModal)" type="button" class="btn btn-sm btn-info m-1"
                    title="Filtrer les données">
                    <i class="mdi mdi-filter-variant me-1"></i>Filtrer
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="container-fluid">
    <div class="d-flex row w-100 flex-wrap p-0 m-0">
        <div class="col-12 col-md-6 col-lg-3 p-1">
            <div class="card key-event-card h-100 b-radius shadow-sm">
                <div class="card-body d-flex flex-column justify-content-center align-items-center">
                    <h5 class="card-title text-dark">Nombre total de sessions engagées</h5>
                    <h1 class="card-text">{{ keyMetricsData?.engagedSessions ? (keyMetricsData?.engagedSessions |
                        number) : '---'}}</h1>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 p-1">
            <div class="card key-event-card h-100 b-radius shadow-sm">
                <div class="card-body d-flex flex-column justify-content-center align-items-center">
                    <h5 class="card-title text-dark">Utilisateurs actifs</h5>
                    <h1 class="card-text">{{ keyMetricsData?.activeUsers ? (keyMetricsData?.activeUsers | number) :
                        '---' }}</h1>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 p-1">
            <div class="card key-event-card h-100 b-radius shadow-sm">
                <div class="card-body d-flex flex-column justify-content-center align-items-center">
                    <h5 class="card-title text-dark">Nouveaux utilisateurs</h5>
                    <h1 class="card-text">{{ keyMetricsData?.newUsers ? (keyMetricsData?.newUsers | number) : '---' }}
                    </h1>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 p-1">
            <div class="card key-event-card h-100 b-radius shadow-sm">
                <div class="card-body d-flex flex-column justify-content-center align-items-center">
                    <h5 class="card-title text-dark">Durée moyenne des sessions engagées</h5>
                    <h1 class="card-text">{{ keyMetricsData?.averageSessionDuration ?
                        (keyMetricsData?.averageSessionDuration) : '---' }}</h1>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex row flex-wrap w-100 p-0 mx-0 my-4">
        <div class="col-12 col-lg-6 p-1" [style.minHeight.px]="400">
            <div class="card h-100 b-radius shadow-sm">
                <div class="card-body mb-2 mb-lg-0 d-flex w-100 justify-content-start">
                    <h3 class="card-title text-dark card-text">Les 5 pages/écrans les plus consultés</h3>
                </div>

                <div class="col-12 w-100 h-100 d-flex align-items-center justify-content-center">
                    <wph-web-google-chart-visualization [chartType]="googleChartType" [chartData]="chart1?.data"
                        [columns]="chart1?.columns" [chartOptions]="chart1?.chartOptions" class="w-100 h-100">
                    </wph-web-google-chart-visualization>
                </div>
            </div>
        </div>

        <div class="col-12 col-lg-6 p-1" [style.minHeight.px]="400">
            <div class="card h-100 b-radius shadow-sm">
                <div class="card-body mb-2 mb-lg-0 d-flex w-100 justify-content-start">
                    <h3 class="card-title text-dark card-text">Les 5 principales villes des utilisateurs actifs</h3>
                </div>

                <div class="col-12 w-100 h-100 d-flex align-items-center justify-content-center">
                    <wph-web-google-chart-visualization [chartType]="googleChartType" [chartData]="chart2?.data"
                        [columns]="chart2?.columns" [chartOptions]="chart2?.chartOptions" class="w-100 h-100">
                    </wph-web-google-chart-visualization>
                </div>
            </div>
        </div>

        <div class="col-12 col-lg-6 p-1" [style.minHeight.px]="400">
            <div class="card h-100 b-radius shadow-sm">
                <div class="card-body mb-2 mb-lg-0 d-flex w-100 justify-content-start">
                    <h3 class="card-title text-dark card-text">Données sur l'engagement du contenu: 5 meilleures</h3>
                </div>

                <div class="col-12 w-100 h-100 d-flex align-items-center justify-content-center">
                    <wph-web-google-chart-visualization [chartType]="googleChartType" [chartData]="chart3?.data"
                        [columns]="chart3?.columns" [chartOptions]="chart3?.chartOptions" class="w-100 h-100">
                    </wph-web-google-chart-visualization>
                </div>
            </div>
        </div>

        <div class="col-12 col-lg-6 p-1" [style.minHeight.px]="400">
            <div class="card h-100 b-radius shadow-sm">
                <div class="card-body mb-2 mb-lg-0 d-flex w-100 justify-content-start">
                    <h3 class="card-title text-dark card-text">Données sur l'engagement du contenu: Tabulaire</h3>
                </div>

                <div class="col-12 w-100 h-100 d-flex justify-content-center">
                    <div class="d-flex row w-100 py-0 px-1 mx-0 my-2 overflow-hidden">
                        <kendo-grid [data]="gridData" class="fs-grid fs-grid-white analytics-grid" [resizable]="true"
                            [pageSize]="gridData?.total || 20" [skip]="0" [pageable]="true"
                            style="height: fit-content;">
                            <kendo-grid-column field="name" title="Libellé" [width]="180" class="text-wrap">
                                <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                                    {{ dataItem?.name }}
                                </ng-template>
                            </kendo-grid-column>

                            <kendo-grid-column [width]="120" field="Vue" class="text-center">
                                <ng-template kendoGridHeaderTemplate>
                                    <span class="text-wrap w-100 d-flex justify-content-center">Nbr Vues</span>
                                </ng-template>

                                <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                                    {{ dataItem?.Vue || 0 }}
                                </ng-template>
                            </kendo-grid-column>

                            <kendo-grid-column [width]="120" field="Survol" class="text-center">
                                <ng-template kendoGridHeaderTemplate>
                                    <span class="text-wrap w-100 d-flex justify-content-center">Nbr Survols</span>
                                </ng-template>

                                <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                                    {{ dataItem?.Survol || 0 }}
                                </ng-template>
                            </kendo-grid-column>

                            <kendo-grid-column [width]="120" field="Clic" class="text-center">
                                <ng-template kendoGridHeaderTemplate>
                                    <span class="text-wrap w-100 d-flex justify-content-center">Nbr Clics</span>
                                </ng-template>

                                <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                                    {{ dataItem?.Clic || 0 }}
                                </ng-template>
                            </kendo-grid-column>
                            
                            <kendo-grid-column [width]="120" field="Toucher" class="text-center">
                                <ng-template kendoGridHeaderTemplate>
                                    <span class="text-wrap w-100 d-flex justify-content-center">Nbr Touches</span>
                                </ng-template>

                                <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                                    {{ dataItem?.Toucher || 0 }}
                                </ng-template>
                            </kendo-grid-column>

                            <kendo-grid-column [width]="120" class="text-center">
                                <ng-template kendoGridHeaderTemplate>
                                    <span class="text-wrap w-100 d-flex justify-content-center">Totaux</span>
                                </ng-template>

                                <ng-template kendoGridCellTemplate let-dataItem="dataItem">
                                    {{ (dataItem?.Vue || 0) + (dataItem?.Survol || 0) + (dataItem?.Clic || 0) + (dataItem?.Toucher || 0) | number :
                                    '1.0-0' }}
                                </ng-template>
                            </kendo-grid-column>

                            <kendo-grid-messages pagerItems="lignes" pagerOf="de"
                                pagerItemsPerPage="éléments par page"></kendo-grid-messages>

                            <ng-template kendoGridNoRecordsTemplate>
                                <span>Aucun résultat trouvé.</span>
                            </ng-template>
                        </kendo-grid>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<ng-template #analyticsModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title">Filtrer</h4>
        <span class="pointer-cus text-dark" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="bi bi-x-lg"></i>
        </span>
    </div>

    <form [formGroup]="filterOptions" class="p-0 m-0" (ngSubmit)="appliquerFiltre()" wphFocusTrap>
        <div class="modal-body">
            <div class="d-flex row p-0 m-0 w-100">
                <div class="form-group col-12">
                    <label for="dateDebut">Date Début</label>
                    <app-date-picker id="dateDebut" name="startDate" formControlName="startDate"></app-date-picker>
                </div>

                <div class="form-group col-12">
                    <label for="endDate">Date Fin</label>
                    <app-date-picker id="endDate" name="endDate" formControlName="endDate"></app-date-picker>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" tabindex="-1" class="btn btn-light"
                (click)="viderFiltre(); modal.dismiss('Cross click')">Vider</button>
            <button type="submit" tabindex="-1" class="btn btn-primary"
                (click)="modal.dismiss('Cross click')">Appliquer</button>
        </div>
    </form>
</ng-template>