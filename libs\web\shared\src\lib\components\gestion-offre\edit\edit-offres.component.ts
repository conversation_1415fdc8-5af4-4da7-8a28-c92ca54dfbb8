import { <PERSON><PERSON><PERSON>, <PERSON>ementRef, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from "@angular/core";
import { Offre, BlocOffre, OffresService, StaticDataOffreService, NatureOffreEnum, CheckTranscoResponse, Fournisseur, CheckPrixRequestDTO } from "@wph/data-access";
import { DeferredActionButtonsService, UserInputService, WorkerService } from "@wph/web/shared";
import { ActivatedRoute, Router, Params } from '@angular/router';
import * as moment from "moment";
import { AlertService, PlateformeService } from "@wph/shared";
import { cloneDeep, isEqual } from 'lodash';
import { AuthService } from "@wph/core/auth";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { catchError, map, Observable, of } from "rxjs";
import { Moment } from "moment";
import { Avis, AvisDTO, TypeAvis } from "libs/federation-syndicats/src/lib/models/avis.model";
import { EtatCommande } from "libs/federation-syndicats/src/lib/models/fs-commande.model";
import { EnteteCommandeConsolideeMarche } from "libs/federation-syndicats/src/lib/models/entete-commande.model";
import { FsOffreService } from "libs/federation-syndicats/src/lib/services/fs-offres.service";
import { FsCommandesService } from "libs/federation-syndicats/src/lib/services/fs-commandes.service";
import { FederationSyndicatService } from "libs/federation-syndicats/src/lib/services/federation-syndicats.service";
import { GroupeEntreprise } from "libs/federation-syndicats/src/lib/models/groupe-entreprise.model";
import { PharmacieEntreprise } from "libs/federation-syndicats/src/lib/models/pharmacie-entreprise.model";
import { GridDataResult } from "@progress/kendo-angular-grid";

@Component({
  selector: "wph-edit-offres",
  templateUrl: "./edit-offres.component.html",
  styleUrls: ["./edit-offres.component.scss"],
  encapsulation: ViewEncapsulation.Emulated
})
export class EditOffresComponent implements OnInit, OnDestroy {

  isOtherSelected: boolean = false;

  replaceComparatorObj: boolean = true;

  toggleOptionSelection(state: boolean) {
    this.isOtherSelected = state;
  }

  @ViewChild('refusalModal', { static: true }) refusalModal: TemplateRef<any>;

  activeConditionsIndex = 1;
  activeOptionsIndex = 1;

  conditionsIndexChanged(event: number): void {
    this.activeConditionsIndex = event;
  }

  optionsIndexChanged(event: number): void {
    this.activeOptionsIndex = event;
  }

  currentStep = 0;
  steps = [
    { label: 'Informations Générales', icon: 'bi-info-circle' },
    { label: 'Ajouter des packs', icon: 'bi-box' }
  ];

  nextStep() {
    if (this.currentStep < this.steps.length - 1) {
      this.currentStep++;
      this.updateComparatorObj();
    }
  }

  prevStep() {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  goToStepByIndex(index: number) {
    this.currentStep = index;
    this.updateComparatorObj();
  }

  offre: Offre;
  id: string;
  etatBar: string;
  readOnly = false;
  cloner = false;
  isLoading = false;
  pageMode: string;
  submitted: boolean;

  cmdUnitaireId: number;
  seuilTranscoPrix: number = 0.5;

  commandeUnitaireExists: boolean = true;
  cmdConsolideeExists: boolean = true;
  etatCommandeAchatGroupe: EtatCommande;

  initialSelectedOffreObj: Offre | null = null;

  activeHeaderIndex = 0;
  activeTabId = 0;
  selectedBlocElement: BlocOffre = null;
  validDateLivraison = false;
  activeItemId: any = null;

  modePaiementData: any[] = [];
  cmd: EnteteCommandeConsolideeMarche;
  avis: Avis;
  monGroupe: any;
  membreId: number;
  estResponsable: boolean;
  estNational: boolean;
  membreDetails: any;
  enteteCommandeId: number;
  feedbackSent = false;
  pharmacienOnly: boolean;
  natureOffreState: string;
  natureOffre: NatureOffreEnum;
  defaultCommentaireHeight: string;
  produitsSansTranscoData: GridDataResult = { data: [], total: 0 };
  prixTranscoData: GridDataResult = { data: [], total: 0 };
  from: string;

  @ViewChild('transcoPrixSeuil', { static: true }) transcoPrixSeuil: TemplateRef<any>;
  @ViewChild('uploadFileBtn', { static: true }) uploadFileBtn: any;
  @ViewChild('rechercheProduitRef') rechercheProduitRef: any;
  @ViewChild('commentaireOffre') commentaireEl: ElementRef<any>;
  @ViewChild('transcoPrixModal') transcoPrixModal: ElementRef<any>;
  @ViewChild('produitsSansTranscoModal') produitsSansTranscoModal: ElementRef<any>;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private authService: AuthService,
    private alertService: AlertService,
    private workerService: WorkerService,
    private offresService: OffresService,
    private fsOffreService: FsOffreService,
    private userInputService: UserInputService,
    private plateformeService: PlateformeService,
    private plateformeServivce: PlateformeService,
    private fsCommandeService: FsCommandesService,
    private fedSyndicatService: FederationSyndicatService,
    private staticDataOffreService: StaticDataOffreService,
    private deferredActionBtnService: DeferredActionButtonsService,
  ) { }

  get isInactive$() {
    return this.fedSyndicatService.inactiveAccount$;
  }

  get currentPlateforme$() {
    return this.plateformeServivce.currentPlateforme$;
  }


  ngOnInit() {
    this.membreId = this.authService.getPrincipal()?.societe?.id;
    this.pharmacienOnly = !this.authService.hasAnyAuthority(['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR']) &&
      (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE'));

    this.estResponsable = this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']);
    this.estNational = this.authService.hasAnyAuthority(['ROLE_NATIONAL']);

    if (this.plateformeServivce.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeServivce.isPlateForme('WIN_GROUPE')) {
      this.fedSyndicatService.getMyGroupe().then(myGroupe => {
        this.monGroupe = myGroupe;
        this.initializeAvis();
      });
    }

    this.membreDetails = this.authService.getPrincipal();

    this.route.queryParams.subscribe((params: Params) => {
      this.from = params['from'];
      this.pageMode = params['mode'];
      this.cloner = params["cloner"];
      this.natureOffre = params['natureOffre'];
      this.etatCommandeAchatGroupe = params['etatCommandeAchatGroupe'];

      this.readOnly = params['readOnly'] ? JSON.parse(params['readOnly']) : false;
      this.enteteCommandeId = params['enteteCommandeId'];

      if (!this.router.url.includes('saisie')) {
        this.readOnly = true;
        this.router.navigate([], { queryParams: { ...params, readOnly: this.readOnly } });
      } else {
        this.readOnly = false;
        this.router.navigate([], { queryParams: { ...params, readOnly: this.readOnly } });
      }

      if (!this.readOnly) {
        this.staticDataOffreService.init2();
      }
    });

    this.route.paramMap.subscribe(params => {
      this.id = params.get("id");
      const incrementViewCount = history.state?.['incr'] || false;
      
      if (this.id) {
        this.setStateOffre(incrementViewCount);
        this.checkIfCommandeConsolideeExists();
      } else {
        this.initOffre();
      }
    });

  }

  initializeAvis() {
    this.avis = {
      commentaire: null,
      estResponsable: this.estResponsable,
      id: null,
      typeAvis: TypeAvis.Negative,
      raison: '',
      enteteCommandeConsolideeMarche: { id: this.enteteCommandeId } as EnteteCommandeConsolideeMarche,
      groupeEntreprise: { id: this.monGroupe?.id } as GroupeEntreprise,
      sondeurEntreprise: { id: this.membreDetails?.societe?.id } as PharmacieEntreprise
    };

  }

  isIntersecting(intersecting: boolean) {
    if (intersecting) {
      if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL']) && !this.readOnly) {
        setTimeout(() => {
          this.defaultCommentaireHeight = `${(this.commentaireEl?.nativeElement as HTMLTextAreaElement)?.scrollHeight}px`;
        });
      }
    }
  }

  toggleReason(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.avis.raison = inputElement.value;
  }



  soumettreRefuserOffre(): void {

    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir réfuser cette offre ?`).then(
      () => {
        this.avis.id = null;
        this.fsCommandeService.sauvegarderAvis(this.avis).subscribe(
          () => {
            const offreTitle = this.offre?.titre || 'Offre';
            this.feedbackSent = true;
            this.fsOffreService.refuserFsOffre(this.offre?.enteteCommandeId).subscribe(res => {
              this.alertService.successAlt(`L'offre "${offreTitle}" a été réfusée avec succès.`, 'Offre Réfusé ', 'MODAL');

              this.back(true);
            });
          },
          (error) => {
            const errorMessage = error?.error?.message || 'Erreur lors de l\'enregistrement de l\'avis.';
            this.alertService.error(errorMessage, 'MODAL');
            console.error('Error saving avis:', error);
          }
        );
        this.modalService.dismissAll();
      },
      () => null
    );
  }


  initOffre() {
    this.offresService.currentEtatBar.subscribe(etat => this.etatBar = etat);

    this.offre = {
      ...new Offre(),
      listeBlocs: [],
      listePaliersRemisesAdditionnels: [],
      palierAdditionTestBrut: "O",
      palierTestValeurBrut: "O",
      etatProposant: 'BROUILLON',
      typeOffre: true,
      typePack: false
    }

    this.getModePaiementEnumeration();

    this.initialSelectedOffreObj = cloneDeep(this.offre);

    this.offresService.initialiserEtatOffre(this.offre);

    this.pushMobilePageOptions();
  }

  setStateOffre(incrementViewCount: boolean = false) {
    if (this.id) {
      if (this.authService.hasAnyAuthority(['ROLE_NATIONAL', 'ROLE_RESPONSABLE', 'ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR']) || this.plateformeService.isPlateForme('WIN_OFFRE')) {
        this.offresService.getOffreById(+this.id, incrementViewCount).subscribe(offre => {
          this.goSetStateOffre(offre);
        });
      } else {
        this.fedSyndicatService.getMyGroupe().then(myGroupe => {
          this.monGroupe = myGroupe;
          this.fsOffreService.getOpenedCmdConsolideeIdByOffre(this.monGroupe?.id, +this.id).subscribe(cmdConsolideeId => {
            if (cmdConsolideeId) {
              this.fsCommandeService.buildCommandeConsolidee(cmdConsolideeId).subscribe((offre: Offre) => {
                this.etatCommandeAchatGroupe = offre?.etatCommandeAchatGroupe;

                this.goSetStateOffre(offre);
                this.checkIfCommandeUnitaireExists(cmdConsolideeId);
              });
            } else {
              this.offresService.getOffreById(+this.id).subscribe(offre => {
                this.goSetStateOffre(offre);
              });
            }
          });
        });
      }
    }
  }

  goSetStateOffre(data: any) {
    if (this.cloner) {
      this.offre = {
        ...data,
        id: null,
        views: 0,
        etat: null,
        titre: null,
        dateFin: null,
        dateDebut: null,
        etatProposant: null,
        datePublication: null,
        compteurCommandes: 0,
        delaiLivraison: null,
        dateLivraisonPrevue: null,
      }
    } else {
      this.offre = {
        ...data,
        etatCommandeAchatGroupe: this.etatCommandeAchatGroupe,
        dateDebut: moment(data.dateDebut, "YYYY-MM-DD HH:mm:ss") ?? null,
        dateFin: moment(data.dateFin, "YYYY-MM-DD HH:mm:ss") ?? null,
        delaiLivraison: data.delaiLivraison ?? null
      }

      if (this.plateformeService.isPlateForme('WIN_OFFRE')) {
        this.offre['canPlaceOrder'] = this.canPlaceOrder();
      }

      setTimeout(() => {
        this.natureOffre = data?.natureOffre;
      });

      if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']) && !this.readOnly && this.offre?.etatProposant === 'PUBLIER') {
        this.offresService.modifiedBlocOffre$.next([]);
      }

      if (this.readOnly) {
        this.offresService.setCollapseStateOnOffre(this.offre, true);
        this.offresService.setConditionsAndPaliersRowDisplayState(this.offre, true)
      }

      if (this.readOnly &&
        !this.estCloture(this.offre?.dateFin) &&
        this.plateformeService.isPlateForme('WIN_GROUPE') &&
        this.authService.hasAnyAuthority(['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'])
      ) {
        this.steps = [this.steps[0], { label: 'Sélectionner Groupes', icon: 'bi-people' }];
      }
    }

    this.offresService.initialiserEtatOffre(this.offre);

    if (this.readOnly && this.offre?.natureOffre === 'G') {
      this.offresService.reinitialiserEtatCommande(this.offre);
    }

    this.getModePaiementEnumeration();

    if (this.pharmacienOnly && this.readOnly) {
      this.natureOffreState = this.offre?.natureOffre === 'I' ? 'OFFRE_INDIVIDUELLE' : 'OFFRE_GROUPE';
    }

    this.readOnly = this.readOnly || (this.offre?.etatProposant !== 'BROUILLON' && !this.cloner && !this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']));
    this.initializeAvis();

    if (this.offre && this.offre?.etatProposant === 'PUBLIER' && !this.estCloture(this.offre?.dateFin) && this.readOnly) {
      setTimeout(() => this.workerService.startCountdownWorker(this.offre));
    }

    if (!this.readOnly) {
      setTimeout(() => { this.initialSelectedOffreObj = cloneDeep(this.offre); }, 200);
    }

    this.pushMobilePageOptions();
  }

  activerOuDesactiverOffre(): void {
    const action = this.offre?.statutOffre ? 'désactiver' : 'activer';
    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir ${action} cette offre ? ${!this.offre?.statutOffre ? "Une fois activée, les membres de votre groupe auront la possiblité de saisir les commandes unitaires sur cette offre." : "Une fois désactivé, les membres de votre groupe n'auront plus la possibilité de saisir les commandes unitaires sur cette offre."}`).then(
      () => { },
      () => null
    );
  }

  checkIfCommandeUnitaireExists(cmdConsolideeId: number): void {
    this.fsCommandeService.getCommandeUnitaireByMembre(this.authService.getPrincipal()?.societe?.id, cmdConsolideeId).subscribe(res => {
      this.commandeUnitaireExists = !!res;

      this.commandeUnitaireExists && (this.cmdUnitaireId = res);
    });
  }

  checkIfCommandeConsolideeExists(): void {
    this.fedSyndicatService.getMyGroupe().then(myGroupe => {
      if (myGroupe) {
        this.fsOffreService.getOpenedCmdConsolideeIdByOffre(myGroupe?.id, +this.id).subscribe(res => {
          this.cmdConsolideeExists = !!res;
        });
      }
    });
  }

  consulterCommandeUnitaire(): void {
    this.router.navigate(
      [`/achats-groupes/commandes/edit/cmd-unitaire`, this.cmdUnitaireId],
      { queryParams: { readOnly: true, offreId: this.id } }
    );
  }

  reouvrirCommandeConsolidee(): void {
    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir réinitialiser l'état de cette offre ?`).then(
      () => {
        this.fsOffreService.reouvrirCommandeConsolidee(+this.id).subscribe(res => {
          const qParams: Params = this.route.snapshot.queryParams;

          this.router.navigate(['.'], { relativeTo: this.route, queryParams: { ...qParams, etatCommandeAchatGroupe: 'EN_ATTENTE' } });

          this.setStateOffre();
        });
      },
      () => null
    )
  }


  openRefusalModal(modalContent: TemplateRef<any>) {
    this.modalService.open(modalContent, { centered: true });
  }

  checkIfAvisExists(OffreId?: number): Observable<boolean> {
    const sondeurId = this.authService.getPrincipal()?.societe?.id;
    if (!sondeurId) {
      console.error('sondeurId is null');
      return of(false);
    }

    const cmdConsolideeId = null;
    const offreId = OffreId ?? null;

    return this.fsCommandeService
      .getBySondeurIdOffreIdCmdId(sondeurId, offreId, cmdConsolideeId)
      .pipe(
        map((avis: AvisDTO | AvisDTO[]) => {
          // Check if the response is an array
          if (Array.isArray(avis)) {
            return avis.length > 0;
          }
          // Check if the response is a single object
          return avis !== null;
        }),
        catchError((error) => {
          console.error('Error fetching avis:', error);
          return of(false);
        })
      );
  }

  refuserOffre() {
    if (this.offre?.etatCommandeAchatGroupe === 'REFUSEE') {
      this.alertService.error('Cette offre a déjà été refusée.', 'MODAL');
      return;
    }

    if (this.feedbackSent) {
      this.alertService.error('Un avis a déjà été envoyé pour cette offre.', 'MODAL');
      return;
    }

    this.checkIfAvisExists(this.offre?.id).subscribe(
      (avisExists) => {
        if (avisExists) {
          this.alertService.error('Un avis a déjà été envoyé pour cette offre.', 'MODAL');
        } else {
          this.openRefusalModal(this.refusalModal);
        }
      },
      (error) => {
        this.alertService.error('Erreur lors de la vérification de l\'avis.', 'MODAL');
        console.error('Erreur API:', error);
      },

    );

  }

  utiliserValuerHtValueChange(checked: boolean): void {
    this.offre.utiliserValeurHt = checked ? 'O' : 'N';
  }

  palierTestValeurBrutChange(checked: boolean): void {
    this.offre.palierTestValeurBrut = checked ? 'O' : 'N';
  }

  patchOffre(): void {
    if ((!this.offre?.distributeurs?.length && !this.offre?.distributeur) || !this.offre?.docImageOffre || !this.offre?.laboratoire || !this.offre?.offreur || !this.offre?.titre || !this.offre?.dateDebut || !this.offre?.dateFin) {
      this.alertService.error(`Impossible d'enregistrer l'offre car il y a des éléments obligatoires non-traités. Merci de traiter les éléments suivants: 
        <ul class="cstm-ul">
          ${!this.offre?.titre ? "<li>Spécifier le <b>titre de l'offre</b>.</li>" : ''}
          ${!this.offre?.offreur ? "<li>Spécifier <b>l'offreur</b>.</li>" : ''}
          ${!this.offre?.laboratoire ? "<li>Sélectionner un <b>laboratoire</b>.</li>" : ''}
          ${(!this.offre?.distributeurs?.length && !this.offre?.distributeur) ? '<li>Sélectionner au moins <b>un distributeur</b>.</li>' : ''}
          ${!this.offre?.dateDebut ? "<li>Spécifier la <b>date début</b> de l'offre.</li>" : ''}
          ${!this.offre?.dateFin ? "<li>Spécifier la <b>date fin</b> de l'offre.</li>" : ''}
          ${!this.offre?.docImageOffre ? "<li>Sélectionner <b>une image</b> de l'offre.</li>" : ''}
        <ul>
        `, 'MODAL');
    } else {
      this.userInputService.confirmAlt("Confirmation", "Êtes vous sûr de vouloir enregistrer les modifications sur cette offre ?").then(
        () => {
          if (typeof this.offre.natureOffre !== 'string') {
            this.offre.natureOffre = this.offre.natureOffre ? 'I' : 'G';
          }

          this.offresService.patchOffre(this.offre).subscribe(_res => {
            if (this.offresService.modifiedBlocOffre$.value?.length) {
              const modifiedBlocOffres: BlocOffre[] = this.offresService.modifiedBlocOffre$.value;

              this.offresService.changeBlocOrder(modifiedBlocOffres).subscribe(_res => {
                this.displaySuccessMessageAndNavigateBack();
              });
            } else {
              this.displaySuccessMessageAndNavigateBack();
            }
          });

          this.isLoading = false;
        },
        () => {
          this.isLoading = false;
        }
      )
    }
  }

  private displaySuccessMessageAndNavigateBack() {
    this.alertService.successAlt(`Les modifications sur l'offre ont été appliquées avec succès.`, 'Modifications Appliquées', 'MODAL');
    this.offresService.modifiedBlocOffre$.next(null), this.back(true);
  }

  save() {
    if (!this.offre?.listeBlocs?.length || (!this.offre?.distributeurs?.length && !this.offre?.distributeur) || !this.offre?.docImageOffre || !this.offre?.laboratoire || !this.offre?.offreur || !this.offre?.titre || !this.offre?.dateDebut || !this.offre?.dateFin) {
      this.alertService.error(`Impossible d'enregistrer l'offre car il y a des éléments obligatoires non-traités. Merci de traiter les éléments suivants: 
        <ul class="cstm-ul">
          ${!this.offre?.titre ? "<li>Spécifier le <b>titre de l'offre</b>.</li>" : ''}
          ${!this.offre?.offreur ? "<li>Spécifier <b>l'offreur</b>.</li>" : ''}
          ${!this.offre?.laboratoire ? "<li>Sélectionner un <b>laboratoire</b>.</li>" : ''}
          ${(!this.offre?.distributeurs?.length && !this.offre?.distributeur) ? '<li>Sélectionner au moins <b>un distributeur</b>.</li>' : ''}
          ${!this.offre?.dateDebut ? "<li>Spécifier la <b>date début</b> de l'offre.</li>" : ''}
          ${!this.offre?.dateFin ? "<li>Spécifier la <b>date fin</b> de l'offre.</li>" : ''}
          ${!this.offre?.docImageOffre ? "<li>Sélectionner <b>une image</b> de l'offre.</li>" : ''}
          ${!this.offre?.listeBlocs?.length ? '<li><span>Ajouter au moins <b>un pack</b>.</span></li>' : ''}
        <ul>
        `, 'MODAL');
    } else {
      const blocsWithoutProduits: string[] = this.offresService.checkIfPackHasProduits(this.offre);

      const presentationalData = blocsWithoutProduits.map(bloc => {
        return `${bloc?.split('|')[1]}`;
      });

      if (blocsWithoutProduits?.length) {
        this.alertService.error(`Impossible d'enregistrer l'offre car il y a des blocs sans produits. Merci d'ajouter des produits aux blocs suivants:
        <b>
         ${presentationalData.join(', ')}
        </b>`, 'MODAL'
        )
      } else {
        this.isLoading = true;
        this.userInputService.confirmAlt("Confirmation", "Êtes vous sûr de vouloir enregistrer cet offre ?").then(
          (_result) => {
            if (this.offre.venteDirecteLabo) {
              this.offre.distributeur = this.offre.laboratoire;
            }

            if (typeof this.offre.natureOffre !== 'string') {
              this.offre.natureOffre = this.offre.natureOffre ? 'I' : 'G';
            }

            if (typeof this.offre?.offreVisibility !== 'string') {
              this.offre.offreVisibility = this.offre?.offreVisibility ? 'PUBLIC' : 'PRIVATE';
            }

            this.createOffreJson(this.offre);

            this.isLoading = false;
          },
          (_cancel) => {
            this.isLoading = false;
          }
        );
      }
    }

  }

  createOffreJson(offre: Offre) {
    this.offresService.createOffreJson(offre).subscribe(_res => {
      this.back(true);
    });
  }

  getListeProduitsSansTranscodage(data: CheckTranscoResponse, distributeurs: Fournisseur[]) {
    if (data && distributeurs?.length) {
      const gridReadyResponseFormat = Object.values(data)?.flat();
      const distCodeSite = distributeurs?.map(item => item?.noeud?.codeSite);

      return gridReadyResponseFormat?.filter(item => distCodeSite?.includes(item?.codeSite));
    }

    return [];
  }

  getRaisonSocialeBySite(codeSite: number) {
    const target = this.offre?.distributeurs?.find(item => item?.noeud?.codeSite === codeSite);

    return target?.raisonSociale ?? codeSite;
  }

  offreDupliquer() {
    const routePrefix = this.getRedirectUrl(true);

    this.userInputService
      .confirmAlt(
        'Confirmation',
        `Êtes vous sûr de vouloir dupliquer cette offre ?`
      ).then(
        () => {
          this.router.navigate([`/${routePrefix}/offres/saisie/${this.offre.id}`], { queryParams: { cloner: true }, replaceUrl: true }).then(() => {
            this.readOnly = false;
            this.offre = {
              ...this.offre,
              id: null,
              views: 0,
              titre: null,
              dateFin: null,
              dateDebut: null,
              etatProposant: null,
              datePublication: null,
              compteurCommandes: 0,
              delaiLivraison: null,
              dateLivraisonPrevue: null,
            }
          });
        },
        () => null
      );

  }

  offrePublier() {
    if (this.plateformeServivce.isPlateForme('WIN_OFFRE')) {
      this.publierOffreWinOffre();
    } else {
      let selectedMemberIds = null;
      if (this.plateformeService.isPlateForme('WIN_GROUPE') && this.offre?.offreVisibility === 'PRIVATE') {
        selectedMemberIds = this.fsOffreService?.selectedGroupes$.value;
      }

      this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir ${this.offre?.etatProposant === 'PUBLIER' ? 'républier' : 'publier'} cette offre ?`).then(
        () => {
          this.fsOffreService.publierFsOffre(this.offre?.id, selectedMemberIds).subscribe(_data => {
            this.back(true);
            this.fsOffreService?.selectedGroupes$.next(null);
            this.alertService.successAlt(`L'offre a été publiée avec succès.`, 'Offre Publiée', 'MODAL');
          });
        }, () => null
      );
    }
  }

  // ? à utiliser dans le cas où on souhaite sélectionnner les groupes pour publier une offre
  publierFsOffreAvecGroupe() {
    const selectedMemberIds = this.fsOffreService?.selectedGroupes$.value;

    if (selectedMemberIds?.length) {
      this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir ${this.offre?.etatProposant === 'PUBLIER' ? 'républier' : 'publier'} cette offre ?`).then(
        () => {
          this.fsOffreService.publierFsOffre(this.offre?.id, selectedMemberIds).subscribe(_data => {
            this.back(true);
            this.fsOffreService?.selectedGroupes$.next(null);
            this.alertService.successAlt(`L'offre a été publiée avec succès.`, 'Offre Publiée', 'MODAL');
          });
        }, () => null
      );
    } else {
      this.alertService.error(`Veuillez sélectionner au moins un groupe pour publier cette offre.`, 'MODAL')
    }
  }

  publierOffreWinOffre() {
    this.userInputService.confirm('Confirmation', 'Êtes vous sûr de vouloir publier cet offre ?').then(
      () => {
        this.offresService.publierOffreById(this.offre.id).subscribe(_data => {
          this.back(true);
          this.alertService.success(`L'offre a été publiée avec succès.`, 'MODAL');
        });
      }, () => null);
  }

  checkTranscoOffre(offreId: number) {
    return this.offresService.checkTranscoOffre(offreId);
  }

  checkTranscoProduits() {
    this.offresService.checkTranscoProduits({ ...this.offre, natureOffre: null }).subscribe(res => {
      this.produitsSansTranscoData = { data: res, total: res?.length };

      this.modalService.open(
        this.produitsSansTranscoModal,
        { size: 'xl', centered: true, backdrop: 'static', modalDialogClass: 'fs-radius-modal', windowClass: 'custom-modal-width' }
      );
    });
  }

  checkTranscoPrix(seuilPrix: number) {
    const payload: CheckPrixRequestDTO = { offreDto: { ...this.offre, natureOffre: null }, seuil: seuilPrix };
    this.offresService.checkTranscoPrix(payload).subscribe(res => {
      this.prixTranscoData = { data: res, total: res?.length };

      this.modalService.open(
        this.transcoPrixModal,
        { size: 'xl', centered: true, backdrop: 'static', modalDialogClass: 'fs-radius-modal', windowClass: 'custom-modal-width' }
      );
    });
  }

  openModal(content: TemplateRef<any>, size = 'md') {
    this.modalService.open(content, { size, centered: true, backdrop: 'static', modalDialogClass: 'fs-radius-modal' });
  }

  estCloture(date: string | Moment) {
    return moment().isAfter(moment(this.offre?.dateFin));
  }

  cloturerOffre() {
    if (moment().isAfter(moment(this.offre?.dateFin))) {
      this.alertService.error("Cette offre est déjà terminée", 'MODAL');
    } else {

      this.userInputService.confirmAlt('Confirmation', 'Êtes vous sûr de vouloir cloturer cet offre ?').then(
        () => {
          this.offresService.clotureOffre(this.offre.id).subscribe(() => {
            this.back(true);
            this.alertService.successAlt(`L'offre a été cloturée avec succès.`, 'Offre Cloturée', 'MODAL');
          });
        }, () => null);
    }
  }

  offreAnnuler() {
    this.userInputService.confirmAlt('Confirmation', 'Êtes vous sûr de vouloir annuler cet offre ?').then(
      () => {
        this.offresService.annulerrOffreById(this.offre.id).subscribe(() => {
          this.back(true);
          this.alertService.successAlt(`L'offre a été annulée avec succès.`, 'Offre Annulée', 'MODAL');
        });
      }, () => null);
  }

  back(modified = false) {
    const redirectUrl = this.getRedirectUrl();

    if (modified) {
      this.submitted = true;
      this.router.navigateByUrl(redirectUrl, { state: { modified, mode: this.pageMode } });
    } else {
      this.pageMode ?
        this.router.navigate([redirectUrl], { queryParams: { mode: this.pageMode } }) :
        this.router.navigateByUrl(redirectUrl);
    }
  }

  canPlaceOrder(): boolean {
    const currentUser = this.authService.getPrincipal();

    if (currentUser?.societe?.typeEntreprise === 'FABRIQUANT') {
      const matched = this.offre?.distributeurs?.filter(dist => dist?.id === currentUser?.societe?.id);
      return !!matched?.length;
    }

    return true;
  }

  CommandeSurOffre() {
    if (moment().isBefore(moment(this.offre?.dateFin))) {
      this.router.navigate(['win-offre/commandes/edit/' + this.offre?.id], { queryParams: { offre: 'true' } });
    }
  }

  saisirCommande(): void {
    this.fedSyndicatService.getMyGroupe().then(myGroupe => {
      this.router.navigate(
        ['achats-groupes/commandes/edit/cmd-unitaire'],
        {
          queryParams:
          {
            readOnly: false,
            groupeId: myGroupe?.id,
            offreId: this.offre?.id,
            cmdConsolideeId: this.offre?.enteteCommandeId
          }
        });
    });
  }

  saisirCommandeIndividuelle(): void {
    this.router.navigate(
      ['achats-groupes/commandes/edit/cmd-individuelle'],
      {
        queryParams: { readOnly: false, offreId: this.offre?.id }
      }
    );
  }

  openPalierModal() {
    this.alertService.openPalierModal$.next(true);
  }

  getModePaiementEnumeration(): void {
    this.fsOffreService.getDomaineEnumeration('mode_paiement').subscribe(res => {
      this.offre['modePaiement'] = res;

      this.offre['modePaiementValuePair'] = res?.map(item => {
        return { label: item?.label, value: item };
      });

    });
  }

  getRedirectUrl(prefixOnly = false): string {
    if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
      if (prefixOnly) {
        return 'achats-groupes';
      } else {
        return this.from ? '/achats-groupes/commandes/groupe' : '/achats-groupes/offres/liste';
      }
    } else {
      return prefixOnly ? 'win-offre' : '/win-offre/offres/list';
    }
  }

  updateComparatorObj() {
    if (this.currentStep === 1 && this.id && !this.readOnly && this.replaceComparatorObj) {
      this.replaceComparatorObj = false;
      setTimeout(() => {
        this.initialSelectedOffreObj = cloneDeep(this.offre);
      }, 200);
    }
  }

  creerCommandeConsolidee(): void {
    this.fedSyndicatService.getMyGroupe().then(myGroupe => {
      if (myGroupe) {
        this.fsOffreService.getOpenedCmdConsolideeIdByOffre(myGroupe?.id, this.offre?.id).subscribe(res => {
          if (!!res) {
            this.alertService.error(`Vous avez déjà une commande sur cette offre, vous ne pouvez pas effectuer une autre commande.`, 'MODAL');
          } else {
            this.fsOffreService.creerCommandeConsolidee(this.offre?.id, this.monGroupe?.id).subscribe(res => {
              this.router.navigate(['/achats-groupes/commandes/edit/cmd-groupe', res?.enteteCommandeId],
                { queryParams: { readOnly: false, offreId: this.offre?.id } }
              );
            });
          }
        });
      }
    });
  }

  canCreateCommandeIndividuelle(): boolean {
    return this.natureOffre === 'I' || this.offre?.natureOffre === 'I';
  }

  adjustTextareaHeight(event: Event): void {
    const textarea = event.target as HTMLTextAreaElement;
    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight}px`;
  }

  exportOffreAsJson(): void {
    this.offresService.exportOffreAsJson(this.offre);
  }

  private pushMobilePageOptions() {
    this.deferredActionBtnService.pushPageOptions([
      {
        label: 'Importer Offre',
        shouldShow: !this.readOnly && !this.offre?.id,
        targetRoles: ['ROLE_SUPER_ADMIN'],
        iconClass: 'mdi mdi-cloud-upload',
        action: () => this.uploadFileBtn?.UploadFile()
      },
      {
        label: 'Télécharger Offre',
        shouldShow: this.readOnly,
        targetRoles: ['ROLE_SUPER_ADMIN'],
        iconClass: 'mdi mdi-content-save',
        action: () => this.exportOffreAsJson()
      },
      {
        label: 'Enregistrer les modifications',
        shouldShow: !this.readOnly && this.offre?.etatProposant === 'PUBLIER',
        targetRoles: ['ROLE_SUPER_ADMIN'],
        iconClass: 'mdi mdi-content-save',
        action: () => this.exportOffreAsJson()
      },
      {
        label: 'Transco Produit',
        shouldShow: this.plateformeService.isPlateForme('WIN_OFFRE') && this.readOnly || (!this.readOnly && this.offre?.etatProposant !== 'PUBLIER'),
        targetRoles: ['ROLE_SUPER_ADMIN'],
        iconClass: 'bi bi-check-circle-fill',
        action: () => this.checkTranscoProduits()
      },
      {
        label: 'Transco Prix',
        shouldShow: this.plateformeService.isPlateForme('WIN_OFFRE') && this.readOnly || (!this.readOnly && this.offre?.etatProposant !== 'PUBLIER'),
        targetRoles: ['ROLE_SUPER_ADMIN'],
        iconClass: 'bi bi-check-circle-fill',
        action: () => this.openModal(this.transcoPrixSeuil)
      },
      {
        label: 'Enregistrer',
        shouldShow: !this.readOnly && this.offre?.etatProposant !== 'PUBLIER',
        iconClass: 'mdi mdi-content-save',
        action: () => this.save()
      },
      {
        label: 'Publier',
        shouldShow: (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) && this.readOnly && (this.offre?.etat !== 'cloturee') && !this.estCloture(this.offre?.dateFin) && (this.offre?.etatProposant === 'B' || this.offre?.etatProposant === 'BROUILLON'),
        iconClass: 'bi bi-send-fill',
        targetRoles: ['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL'],
        action: () => this.offrePublier()
      },
      {
        label: 'Publier',
        shouldShow: (this.plateformeService.isPlateForme('WIN_OFFRE')) && this.readOnly && (this.offre?.etat !== 'cloturee') && !this.estCloture(this.offre?.dateFin) && (this.offre?.etatProposant === 'B' || this.offre?.etatProposant === 'BROUILLON'),
        iconClass: 'bi bi-send-fill',
        targetRoles: ['ROLE_SUPER_ADMIN'],
        action: () => this.offrePublier()
      },
      {
        label: 'Républier',
        shouldShow: (this.plateformeService.isPlateForme('WIN_GROUPE')) && this.readOnly && this.offre?.offreVisibility === 'PRIVATE' && (this.offre?.etat !== 'cloturee') && !this.estCloture(this.offre?.dateFin) && (this.offre?.etatProposant === 'B' || this.offre?.etatProposant === 'PUBLIER'),
        iconClass: 'bi bi-send-fill',
        targetRoles: ['ROLE_SUPER_ADMIN'],
        action: () => this.offrePublier()
      },
      {
        label: 'Saisir Commande',
        iconClass: 'bi bi-plus-circle-fill',
        shouldShow: (this.plateformeService.isPlateForme('WIN_OFFRE')) && this.readOnly && this.offre?.canPlaceOrder,
        targetRoles: ['ROLE_AGENT_FOURNISSEUR','ROLE_AGENT_POINT_VENTE', 'ROLE_AGENT_COMMERCIAL', 'ROLE_ASSISTANT'],
        action: () => this.CommandeSurOffre()
      },
      {
        label: 'Cloturer',
        iconClass: 'bi bi-trash',
        shouldShow: this.readOnly && ((this.offre?.etatProposant === 'P' || this.offre?.etatProposant === 'PUBLIER') && !this.estCloture(this.offre?.dateFin)),
        targetRoles: ['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL'],
        action: () => this.cloturerOffre()
      },
      {
        label: 'Annuler',
        iconClass: 'bi bi-trash',
        shouldShow: this.readOnly && (this.offre?.etat !== 'cloturee') && !this.estCloture(this.offre?.dateFin) && (this.offre?.etatProposant === 'B' || this.offre?.etatProposant === 'BROUILLON'),
        targetRoles: ['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL'],
        action: () => this.offreAnnuler()
      },
      {
        label: 'Dupliquer',
        iconClass: 'bi bi-files-alt',
        shouldShow: this.readOnly,
        targetRoles: ['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL'],
        action: () => this.offreDupliquer()
      },
      {
        label: 'Créer Commande Groupe',
        iconClass: 'bi bi-plus-circle-fill',
        shouldShow: (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) && this.readOnly && !this.estCloture(this.offre?.dateFin) && this.offre?.etat !== 'cloturee' && this.offre?.etatProposant !== 'CLOTURE' && !this.cmdConsolideeExists && this.natureOffre === 'G' && this.offre?.etatProposant === 'PUBLIER',
        targetRoles: ['ROLE_RESPONSABLE'],
        action: () => this.creerCommandeConsolidee()
      },
      {
        label: 'Saisir Commande',
        iconClass: 'bi bi-plus-circle-fill',
        shouldShow: (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) && this.readOnly && !this.estCloture(this.offre?.dateFin) && this.offre?.etat !== 'cloturee' && this.offre?.etatProposant !== 'CLOTURE' && this.offre?.etatProposant === 'PUBLIER' && (!this.commandeUnitaireExists && this.offre?.etatCommandeAchatGroupe === 'ACCEPTEE'),
        targetRoles: ['ROLE_AGENT_POINT_VENTE'],
        action: () => this.saisirCommande()
      },
      {
        label: 'Saisir Commande Individuelle',
        iconClass: 'bi bi-plus-circle-fill',
        shouldShow: (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) && this.readOnly && !this.estCloture(this.offre?.dateFin) && this.offre?.etat !== 'cloturee' && this.offre?.etatProposant !== 'CLOTURE' && this.offre?.etatProposant === 'PUBLIER' && this.natureOffre === 'I',
        targetRoles: ['ROLE_AGENT_POINT_VENTE'],
        action: () => this.saisirCommandeIndividuelle()
      },
      {
        label: 'Rechercher produit',
        shouldShow: true,
        iconClass: 'mdi mdi-magnify',
        action: () => this.rechercheProduitRef?.openSearchModal()
      },
      {
        label: 'Quitter',
        iconClass: 'mdi mdi-close',
        shouldShow: true,
        action: () => this.back()
      }
    ]);
  }

  getOffreFromJson(selectedFile: FormData): void {
    if (selectedFile) {
      const jsonOffreFile: any = selectedFile.get('file');

      if (jsonOffreFile && jsonOffreFile?.type === 'application/json') {
        const reader = new FileReader();

        reader.onload = (e) => {
          const jsonContent = e.target?.result as string;

          try {
            const offreObjFromJson = JSON.parse(jsonContent) as Offre;

            const initOffre = {
              ...offreObjFromJson,
              id: null,
              views: 0,
              dateFin: null,
              dateDebut: null,
              etatProposant: null,
              datePublication: null,
              compteurCommandes: 0,
              delaiLivraison: null,
              dateLivraisonPrevue: null,
            };

            if (initOffre?.offreExportSrc === this.plateformeService.getCurrentPlateforme()) {
              this.applyMappingUpdatesToOffre(initOffre);
              this.alertService.success("L'offre a été importée avec succès.", 'MODAL');
            } else {
              // ? Handle Fournisseur, Produit id mapping logic
              this.offresService.handleCrossPlateformeObjectIdMapping(initOffre).then(updatedOffre => {
                const [offre, produitsNull] = updatedOffre;

                if (produitsNull?.length) {
                  console.log("Les produits manquants: ", produitsNull?.join(', '));
                  this.alertService.error(`Les produits suivants n'ont pas été trouvés : <b>${produitsNull?.join(', ')}</b>`, 'MODAL');
                } else {
                  this.alertService.success("L'offre a été importée avec succès.", 'MODAL');
                }

                this.applyMappingUpdatesToOffre(offre);
              });
            }

          } catch (error) {
            console.error("Error parsing JSON data from the selected file : ", error);
            this.alertService.error("Une erreur est survenue lors de l'importation du fichier.");
          }

        };

        reader.onerror = (e) => {
          console.error('File could not be read:', e.target?.error);
          this.alertService.error(`Impossible de lier le fichier sélectionné. Veuillez réessayer.`);
        };

        reader.readAsText(jsonOffreFile);
      }
    }
  }

  private applyMappingUpdatesToOffre(initOffre: Offre) {
    this.offre = initOffre;
    this.offresService.initialiserEtatOffre(this.offre);
    this.offresService.reinitialiserEtatCommande(this.offre);
  }

  hasUnsavedData(): boolean {
    if (this.readOnly || this.offre?.etatProposant === 'PUBLIER' || this.submitted) {
      return false;
    }

    if (!this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL'])) {
      return false;
    }

    // Deep copy using JSON serialization
    const compareOffre = this.offre ? JSON.parse(JSON.stringify(this.offre)) : {};

    if (!this.id) {
      ['modePaiement', 'coffretEnabled', 'modePaiementValuePair', 'mapBlocsById'].forEach(prop => delete compareOffre[prop]);

      // Normalize listeBlocs if it contains a single empty item
      if (compareOffre?.listeBlocs?.length === 1 && !compareOffre.listeBlocs[0]?.listeFils?.length) {
        compareOffre.listeBlocs = [];
      }
    }

    return !isEqual(this.initialSelectedOffreObj, compareOffre);
  }

  ngOnDestroy(): void {
    this.workerService.stopWorker();
    this.fsOffreService.selectedGroupes$.next(null);
    this.deferredActionBtnService.pushPageOptions([]);
  }

}
