.form-control {
    color: black;
    font-size: 1rem;
    border-radius: var(--winoffre-base-border-radius);
    font-weight: 600;
}

.modal-footer {
    .btn {
        color: black;
        font-size: 1rem;
        border-radius: var(--winoffre-base-border-radius);
        font-weight: 600;
    }
}

.input-group {
    .btn {
        border-top-right-radius: var(--winoffre-base-border-radius);
        border-bottom-right-radius: var(--winoffre-base-border-radius);
    }
}

label {
    color: var(--winoffre-text-light-shade);
    font-weight: 600;
}

.picker-input {
    .form-control {
        border-radius: var(--winoffre-base-border-radius) !important;
    }
}