.label-title-acc{
  background: #EDECF1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 12px;
  border-radius: 50px;
  color: black;
  font-size: 18px;
  margin-top:-8px;
}
.googlemap{
  width:100% !important;
  height:160px !important;
  margin-bottom: 10px;
}
.add-icon{
  font-size: 25px;
  color: #88cadd;
  position: absolute;
  right:18px;
}
#mapId{
  width: 100%;
  height: 100%
}
.aucune {
  opacity: 50%;
}
.btn-logout{
  --border-radius: 55px;
  width: 70% !important;
    margin: 15px auto;
}

.acc-card {
  min-height: calc(100vh - 80px);
  padding-bottom: 50px;
}

.padding-start-list {
  display: flex;
  align-items: center;
}

.logout-row {
  position: absolute;
  bottom: calc(var(--ion-safe-area-bottom) + 10px) !important;
  width: 100%;
}

.ripple {
  color: rgba(247, 138, 138, 0.3);
}
