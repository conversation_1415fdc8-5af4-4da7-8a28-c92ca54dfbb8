<!-- Start Of Header -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <h5 class="page-title fw-4 ps-2 col-4">
            Détails Client
        </h5>

        <div class="col-8 px-1">
            <div class="row justify-content-end align-items-center">
                <button type="button" class="btn btn-sm btn-dark m-1" (click)="back()">
                    <i class="mdi mdi-close"></i>
                    Quitter
                </button>

                <button type="button"
                    [disabled]="accessClientForm?.invalid || (!estClient && accessClientForm?.invalid)"
                    class="btn btn-sm btn-primary m-1" (click)="activerAcces()">
                    Confirmer
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="container-fluid m-0 p-0">

    <div class="row mb-2">
        <div class="col-12">
            <div class="card px-2 py-1 d-flex justify-content-between" style="min-height: 40px;">
                <div class="row p-0">
                    <div class="col-xl-9 col-12" style="visibility: hidden; position: fixed; left: -400px">
                        <div id="client-picker-input" class="input-group picker-input">
                            <input id="search-client"
                                placeholder="Rechercher un client local par raison sociale ou code local"
                                [formControl]="$any(formControls['selectedSociete'])" type="search" [readOnly]="true"
                                class="form-control form-control-lg" style="padding-left: 30px !important;"
                                (ngModelChange)="societeSelectionChange($event)" [ngbTypeahead]="searchSociete"
                                [resultTemplate]="clientSearchTemplate" [inputFormatter]="societeFormatter"
                                [resultFormatter]="societeFormatter">

                            <ng-template #clientSearchTemplate let-result="result">
                                <div>
                                    <span class="badge badge-info mr-2">{{ result?.code}}</span>
                                    <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                                </div>
                                <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                            </ng-template>

                            <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
                        </div>
                    </div>

                    <div class="col-12 m-0 d-flex align-items-center">
                        <div class="d-flex w-100 align-items-center justify-content-center px-2">
                            <span [ngbTooltip]="estClientTooltip" container="body" tooltipClass="cstm-tooltip-container" class="mr-2 d-flex row align-items-center">
                                <input id="est-client-chck" class="pointer-cus" type="checkbox" [(ngModel)]="estClient"
                                    style="width: 18px; height: 18px;" #ckEstClient />
                                <label for="est-client-chck" class="h5 pointer-cus ml-1">EST CLIENT :</label>
                            </span>

                            <span *ngIf="!ckEstClient.checked"
                                class="badge badge-light rounded-pill py-1 px-2">NON</span>
                            <span *ngIf="ckEstClient.checked"
                                class="badge badge-success rounded-pill py-1 px-2">OUI</span>

                            <ng-template #estClientTooltip>
                                <div class="d-flex align-items-start">
                                    <i class="mdi mdi-alert-circle-outline mdi-18px text-warning mr-1"></i>
                                    <span class="text-white" style="font-size: .8rem;">
                                         {{ estClient ? 'Désactivez' : 'Activez' }} l'accès pour ce client en {{!estClient ? 'cochant' : 'décochant'}} la case, puis validez les changements en cliquant sur le bouton "Confirmer". 
                                    </span>
                                </div>
                            </ng-template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 col-12 mb-2 mb-lg-0">
            <div class="card h-100">
                <div class="card-header">
                    <span class="h4 text-center my-0 text-uppercase">
                        Informations pharmacie du Maroc
                    </span>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="code-client-g" class="form-label">Code Pharmacie du Maroc</label>
                                <input id="code-client-g" [value]="selectedPharmacie?.clientGroupe?.code" type="text"
                                    class="form-control form-control-sm" [readonly]="true">
                            </div>
                        </div>

                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="raison-sociale" class="form-label">Raison Sociale</label>
                                <input id="raison-sociale" [value]="selectedPharmacie?.clientGroupe?.raisonSociale"
                                    type="text" class="form-control form-control-sm" [readonly]="true">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="nom-responsable" class="form-label">Nom Responsable</label>
                                <input id="nom-responsable" [value]="selectedPharmacie?.clientGroupe?.nomResponsable"
                                    type="text" class="form-control form-control-sm" [readonly]="true">
                            </div>
                        </div>

                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="client-g-ville" class="form-label">Ville</label>
                                <input id="client-g-ville" [value]="selectedPharmacie?.clientGroupe?.ville" type="text"
                                    class="form-control form-control-sm" [readonly]="true">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-12">
            <div class="card">
                <div class="card-header">
                    <span class="h4 text-center my-0 text-uppercase">
                        Informations client local
                    </span>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="code-client-local" class="form-label">Code client chez le grossiste</label>
                                <input id="code-client-local" [(ngModel)]="selectedSociete.code" type="text"
                                    class="form-control form-control-sm" [readonly]="true">
                            </div>
                        </div>

                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="raison-sociale-locale" class="form-label">Raison Sociale</label>
                                <input id="raison-sociale-locale" [(ngModel)]="selectedSociete.raisonSociale"
                                    type="text" class="form-control form-control-sm" [readonly]="true">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="nom-responsable-locale" class="form-label">Nom Responsable</label>
                                <input id="nom-responsable-locale" [(ngModel)]="selectedSociete.nomPharmacien"
                                    type="text" class="form-control form-control-sm" [readonly]="true">
                            </div>
                        </div>

                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="ville-locale" class="form-label">Ville</label>
                                <input id="ville-locale" [(ngModel)]="selectedSociete.ville" type="text"
                                    class="form-control form-control-sm" [readonly]="true">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="tel-locale" class="form-label">Téléphone</label>
                                <input id="tel-locale" [(ngModel)]="selectedSociete.telephone" type="text"
                                    class="form-control form-control-sm" [readonly]="true">
                            </div>
                        </div>

                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="gsm-locale" class="form-label">GSM Responsable<span
                                        class="text-danger">*</span></label>
                                <input id="gsm-locale" [(ngModel)]="selectedSociete.gsm" type="text"
                                    class="form-control form-control-sm" [readonly]="false">
                            </div>
                        </div>

                        <div class="col-md-6 col-12">
                            <div class="mb-2">
                                <label for="email-locale" class="form-label">Email</label>
                                <input id="email-locale" [(ngModel)]="selectedSociete.email" type="text"
                                    class="form-control form-control-sm" [readonly]="false">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card my-2">
        <div class="card-header">
            <span class="h4 text-center my-0 text-uppercase">
                Services
            </span>
        </div>
        <kendo-grid [data]="serviceGridData" [pageable]="true"
            [pageSize]="serviceGridData ? serviceGridData?.data?.length : 0" [sortable]="false" [selectable]="false"
            style="height: 100%">
            <kendo-grid-column [width]="180" title="Libellé" field="serviceClient.libelle"
                class="text-left"></kendo-grid-column>

            <kendo-grid-column [width]="120" title="Statut" class="text-center no-ellipsis">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    <span *ngIf="serviceStatusAtIndex[rowIndex]"
                        class="badge badge-success rounded-pill py-1 px-2">Activé</span>
                    <span *ngIf="!serviceStatusAtIndex[rowIndex]"
                        class="badge badge-grey rounded-pill py-1 px-2">Désactivé</span>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="120" title="Action" class="text-center select-clmn">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    <div>
                        <input type="checkbox" [checked]="dataItem?.statut" [disabled]="!selectedSociete?.code"
                            (change)="serviceSelectionChange(serviceCheckbox.checked, dataItem, rowIndex); serviceCheckbox.checked && openModal(optionSelectionModal, dataItem?.serviceClient?.id)"
                            style="width: 18px; height: 18px;" #serviceCheckbox>
                    </div>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="120" title="Options" class="text-center select-clmn no-ellipsis">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    <span
                        (click)="serviceStatusAtIndex[rowIndex] && openModal(optionSelectionModal, dataItem?.serviceClient?.id);"
                        [ngClass]="{'badge-warning pointer-cus': serviceStatusAtIndex[rowIndex], 'badge-light': !serviceStatusAtIndex[rowIndex]}"
                        class="badge rounded-pill py-1 px-2"
                        [title]="serviceStatusAtIndex[rowIndex] ? 'Gérer des options du service': 'Veuillez activer le service'">Gérer</span>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>
        </kendo-grid>
    </div>
</div>

<!-- Start Option Selection Modal -->
<ng-template #optionSelectionModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">LISTE DES OPTIONS</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span>&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <kendo-grid [data]="optionsGridData" [hideHeader]="true">
            <kendo-grid-column [width]="200" title="Libellé" class="text-start">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    {{ dataItem.libelle }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="60" title="Action" class="text-center select-clmn">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    <div>
                        <input type="checkbox" [checked]="dataItem?.statut"
                            (change)="optionSelectionChange(optionCheckbox.checked, dataItem, rowIndex)"
                            style="width: 18px; height: 18px;" #optionCheckbox>
                    </div>
                </ng-template>
            </kendo-grid-column>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>
        </kendo-grid>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-light" (click)="modal.close('Close Btn click')">Fermer
        </button>

        <button type="button" class="btn btn-primary text-white"
            (click)="confirmSelectedOptions(); modal.close('Confirm Btn click')">Confirmer
        </button>
    </div>
</ng-template>
<!-- End Option Selection Modal   -->