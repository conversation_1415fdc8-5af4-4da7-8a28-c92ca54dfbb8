.blog-post-card {
  // Override Bootstrap card styles to match our design
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  margin-bottom: 24px;
  overflow: hidden;
  transition: box-shadow 0.2s ease-in-out;
  border: 1px solid #e1e5e9 !important;
}

.blog-post-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
}

/* Header Styles - Bootstrap integration */
.post-header {
  padding: 20px 24px 16px !important;
  border-bottom: 1px solid #e4e6ea;

  @media (max-width: 768px) {
    padding: 16px 20px 12px !important;
  }

  @media (max-width: 480px) {
    padding: 12px 16px !important;
  }
}

.author-info {
  gap: 12px;
}

.author-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  @media (max-width: 768px) {
    width: 40px;
    height: 40px;
  }
}

.avatar-placeholder {
  font-size: 20px;

  @media (max-width: 768px) {
    font-size: 18px;
  }
}

.author-name {
  font-size: 16px !important;
  color: #1d2129 !important;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 15px !important;
  }
}

.company-name {
  font-size: 14px !important;
  color: #65676b !important;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 13px !important;
  }
}

.post-date {
  font-size: 12px !important;
  color: #8a8d91 !important;
  line-height: 1.2;
}

/* Share Button - Bootstrap integration */
.share-button {
  padding: 8px 12px !important;
  background: transparent !important;
  border: 1px solid #d0d4d9 !important;
  border-radius: 8px !important;
  color: #65676b !important;
  font-size: 14px;
  transition: all 0.2s ease-in-out;

  &:hover:not(:disabled) {
    background: #f2f3f5 !important;
    border-color: #bcc0c4 !important;
    color: #65676b !important;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.sharing {
    background: #e3f2fd !important;
    border-color: #1976d2 !important;
    color: #1976d2 !important;
  }

  @media (max-width: 480px) {
    padding: 6px 10px !important;
    font-size: 13px;
  }
}

.share-icon {
  font-size: 16px;

  @media (max-width: 480px) {
    font-size: 14px;
  }
}

/* Post Title - Bootstrap integration */
.post-title {
  padding-bottom: 12px !important;

  .card-title {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #1d2129 !important;
    line-height: 1.3;
    margin-bottom: 0 !important;

    @media (max-width: 768px) {
      font-size: 18px !important;
    }
  }
}

/* Post Content - Bootstrap integration */
.post-content {
  .card-text {
    font-size: 15px !important;
    line-height: 1.5;
    color: #1d2129 !important;
    white-space: pre-line;

    @media (max-width: 768px) {
      font-size: 14px !important;
    }
  }
}

.text-content {
  transition: height 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.read-more-button {
  color: #1976d2 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  padding: 4px 0 !important;
  transition: color 0.2s ease-in-out;

  &:hover {
    color: #1565c0 !important;
    text-decoration: underline !important;
  }

  &:focus {
    box-shadow: none !important;
  }
}

/* Media Container - Swiper integration */
.media-container {
  margin-top: 0;
}

.swiper-news-card {
  --swiper-navigation-color: #1d2129;
  --swiper-pagination-color: #1976d2;
  --swiper-navigation-size: 20px;

  @media (max-width: 768px) {
    --swiper-navigation-size: 18px;
  }
}

/* Image and Video Content */
.post-image {
  height: auto;
  max-height: 500px;
  object-fit: cover;
  display: block;
}

.post-video {
  height: auto;
  max-height: 500px;
  display: block;
}

.youtube-player {
  max-height: 500px;
}

/* Media Caption */
.media-caption {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 800;
  color: #65676b;

  background: #fbfcff;
  border-top: 1px solid #e4e6ea;
}

/* Swiper Navigation Buttons */
.swiper-news-card {
  ::ng-deep {

    .swiper-button-prev,
    .swiper-button-next {
      background: rgba(255, 255, 255, 0.9) !important;
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      width: 40px !important;
      height: 40px !important;
      margin-top: -20px !important;
      backdrop-filter: blur(4px);
      transition: all 0.2s ease-in-out;

      &:after {
        font-size: 16px !important;
        font-weight: bold;
      }

      &:hover {
        background: rgba(255, 255, 255, 1) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      @media (max-width: 768px) {
        width: 36px !important;
        height: 36px !important;
        margin-top: -18px !important;

        &:after {
          font-size: 14px !important;
        }
      }
    }

    .swiper-button-prev {
      left: 16px !important;

      @media (max-width: 768px) {
        left: 12px !important;
      }
    }

    .swiper-button-next {
      right: 16px !important;

      @media (max-width: 768px) {
        right: 12px !important;
      }
    }

    .swiper-pagination-bullet {
      width: 8px !important;
      height: 8px !important;
      background: #bcc0c4 !important;
      opacity: 1 !important;
      transition: background-color 0.2s ease-in-out;

      &.swiper-pagination-bullet-active {
        background: #1976d2 !important;
      }
    }

    .swiper-pagination {
      bottom: 8px !important;

      @media (max-width: 768px) {
        bottom: 6px !important;
      }
    }
  }
}

/* Card Footer - Bootstrap integration */
.card-footer {
  .btn-primary {
    font-weight: 500;
    border-radius: 8px;
    padding: 12px 24px;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
    }

    @media (max-width: 768px) {
      padding: 10px 20px;
    }
  }
}

/* Loading and Error States */
.loading-placeholder {
  background: #f2f3f5;
  border-radius: 8px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #65676b;
  font-size: 14px;
}

.error-placeholder {
  background: #fff2f2;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c53030;
  font-size: 14px;
}

/* Accessibility */
.share-button:focus,
.read-more-button:focus {
  box-shadow: none !important;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .swiper-news-card ::ng-deep .swiper-wrapper {
    transition-duration: 0ms !important;
  }
}

/* Mobile-first responsive adjustments */
@media (max-width: 576px) {
  .blog-post-card {
    margin-bottom: 16px;
    border-radius: 8px !important;
  }

  .author-info {
    gap: 8px;
  }

  .post-title,
  .post-content {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .card-footer {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .blog-post-card {
    border: 2px solid #000 !important;
  }

  .share-button {
    border: 2px solid #000 !important;
    background: #fff !important;
    color: #000 !important;
  }

  .read-more-button {
    color: #000 !important;
    text-decoration: underline !important;
  }
}