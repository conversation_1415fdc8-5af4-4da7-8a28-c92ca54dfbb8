<!-- Start Of Header -->
<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-12">Commandes Unitaires</h4>
  </div>
</div>
<!-- <PERSON><PERSON> HEADER -->

<div class="row d-flex m-0 px-1">
  <div class="card m-0 w-100 p-0 bg-white" [style.height]="'calc(100vh - 60px)'">
    <div class="card-header py-1 pl-2 mx-0 bg-white">
      <div class="row p-0">

        <div class="col-12 p-0 d-flex justify-content-end">
          <div class="row flex-wrap d-flex px-1 py-0">
            <div class="col-auto p-0 m-1">
              <div class="input-group picker-input">
                <input type="search" [formControl]="searchFilter" placeholder="Code ou Titre de l'offre"
                  class="form-control form-control-md pl-4" id="groupeCritere" />

                <div class="picker-icons picker-icons-alt">
                  <i class="mdi mdi-magnify pointer"></i>
                </div>
              </div>
            </div>

            <button (click)="displayFilter = !displayFilter" type="button" class="btn btn-sm search-btn b-radius m-1">
              <span *ngIf="!displayFilter">
                <i class="bi bi-sliders"></i>
                Recherche Avancée
              </span>

              <span *ngIf="displayFilter">
                <i class="mdi mdi-close"></i>
                Fermer la recherche
              </span>

            </button>


            <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
          </div>
        </div>
      </div>

      <form [formGroup]="filterForm" (ngSubmit)="appliquerFiltre()" *ngIf="displayFilter" wphFocusTrap>
        <div class="row d-flex px-1 py-0 flex-wrap k-gap-2">

          <div class="col-lg-auto col-12 p-0 m-0">
            <label for="offreur" class="col-12 px-0 col-form-label text-left">Offreur</label>

            <div class="col-12 px-0 input-group picker-input">
              <input type="text" name="offreur" id="offreur" formControlName="offreur"
                class="form-control pl-4 form-control-md b-radius bg-white" [ngbTypeahead]="searchFournisseur"
                [resultFormatter]="laboFormatter" [inputFormatter]="laboFormatter">

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>

          <div class="col-lg-auto col-12 p-0 m-0">
            <label for="distributeur" class="col-12 px-0 col-form-label text-left">Distributeur</label>

            <div class="col-12 px-0 input-group picker-input">
              <input type="text" name="distributeur" id="distributeur" formControlName="distributeur"
                class="form-control pl-4 form-control-md b-radius bg-white" [ngbTypeahead]="searchFournisseur"
                [resultFormatter]="fournisseurFormatter" [inputFormatter]="fournisseurFormatter">

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>

          <div class="col-lg-auto col-12 p-0 m-0">
            <label for="dateDebut" class="col-12 px-0 col-form-label text-left">Date Création Début</label>

            <app-date-picker formControlName="dateDebut"></app-date-picker>
          </div>

          <div class="col-lg-auto col-12 p-0 m-0">
            <label for="dateFin" class="col-12 px-0 col-form-label text-left">Date Création Fin</label>

            <app-date-picker formControlName="dateFin"></app-date-picker>
          </div>

          <div class="col-lg-2 col-12 mt-1 py-1 pb-0 px-0">
            <label class="col-12 form-label p-0 m-0" for="selectstatut" style="margin-bottom: 5px !important;">Statut</label>

            <div class="col-12 px-0 input-group">
              <select2 id="selectstatut" (focus)="selectStatut.toggleOpenAndClose()" [data]="stautsLabelsValues" formControlName="statut" hideSelectedItems="false"
                class="form-control-sm p-0 w-100" multiple="false" #selectStatut></select2>
            </div>
          </div>

          <div class="col d-flex align-items-end p-0">
            <button (click)="viderFiltre()" type="button" title="Vider" class="btn btn-sm btn-outline-primary b-radius">
              <i class="bi bi-arrow-clockwise"></i>
            </button>

            <button type="submit" title="Appliquer filtre" class="btn btn-sm btn-primary b-radius mx-1">
              <i class="mdi mdi-filter"></i>
              <span class="mx-1">Appliquer</span>
            </button>

          </div>
        </div>
      </form>
    </div>

    <div class="card-body m-0 p-0 bg-white">
      <kendo-grid [data]="gridData" [pageable]="{
              buttonCount: 5,
              info: true,
              type: 'numeric',
              pageSizes: pageSizes,
              previousNext: true,
              position: 'bottom'
            }" [pageSize]="navigation.pageSize" class="fs-grid fs-listing-grid" (cellClick)="cellClickHandler($event)"
        (pageChange)="pageChange($event)" (pageChange)="pageChange($event)" (sortChange)="sortChange($event)"
        [sortable]="{ mode: 'single'}" [sort]="commandeSort" [resizable]="true" [skip]="navigation.skip"
        style="height: 100%">

        <kendo-grid-column field="codeCommande" [width]="120">
          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Code Cmd</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!"
              [active]="navigation.originalSortField === column.field" [title]="column.title"
              [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="titre" class="text-wrap" [width]="200">
          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Titre de l'offre</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!"
              [active]="navigation.originalSortField === column.field" [title]="column.title"
              [type]="'alpha'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="offreur.raisonSociale" class="text-wrap" [width]="160">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.offreur?.raisonSociale | uppercase }}
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Offreur</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!"
              [active]="navigation.originalSortField === column.field" [title]="column.title"
              [type]="'alpha'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="distributeur.raisonSociale" class="text-wrap" [width]="160">
          <ng-template kendoGridCellTemplate let-dataItem>
            <ng-container>
              {{ dataItem?.distributeur?.raisonSociale }}
            </ng-container>
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Distributeur</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!"
              [active]="navigation.originalSortField === column.field" [title]="column.title"
              [type]="'alpha'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="dateCreationCmd" [width]="120">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span *ngIf="dataItem?.dateCreationCmd; else: emptyDate">{{ dataItem?.dateCreationCmd | date:
              'dd/MM/yyyy' }}</span>
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Date Création</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!"
              [active]="navigation.originalSortField === column.field" [title]="column.title"
              [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="totalValeurTtcBruteCmd" class="text-right" [width]="150">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.totalValeurTtcBruteCmd | number : '1.2-2' }}
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Montant Brut (Dh)</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!"
              [active]="navigation.originalSortField === column.field" [title]="column.title"
              [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>


        <kendo-grid-column title="Statut" [width]="120">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-element-status [state]="dataItem?.etatCommandeAchatGroupe"></app-element-status>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE']" [hidden]="(isInactive$ | async) === true"
          title="Action" [width]="80">
          <ng-template kendoGridCellTemplate let-dataItem>
            <div class="d-flex justify-content-center k-gap-2">
              <span (click)="dataItem?.etatCommandeAchatGroupe === 'BROUILLON' && consulterCommande(dataItem, false)"
                [ngClass]="{'opacity-light': dataItem?.etatCommandeAchatGroupe !== 'BROUILLON'}"
                class="actions-icons btn-success pointer-cus" title="Modifier Commande">
                <i class="bi bi-pencil-square"></i>
              </span>

              <span (click)="dataItem?.etatCommandeAchatGroupe === 'BROUILLON' && annulerCommande(dataItem)"
                [ngClass]="{'opacity-light': dataItem?.etatCommandeAchatGroupe !== 'BROUILLON'}"
                class="actions-icons btn-danger pointer-cus" title="Annuler Commande">
                <i class="bi bi-trash"></i>
              </span>

            </div>
          </ng-template>

        </kendo-grid-column>

        <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
          <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
            [navigation]="navigation" style="width: 100%;" (pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
        </ng-template>

        <ng-template kendoGridNoRecordsTemplate>
          <span>Aucun résultat trouvé.</span>
        </ng-template>
      </kendo-grid>

      <ng-template #emptyDate>
        <span>--/--/----</span>
      </ng-template>
    </div>
  </div>
</div>

<ng-template #popFournisseurs let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">LISTE DES DISTRIBUTEURS</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span>&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <kendo-grid [data]="selectedFournisseurs" [hideHeader]="true">
      <kendo-grid-column [width]="100" field="etatProposant" title="">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.raisonSociale}}
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">Fermer
    </button>
  </div>
</ng-template>

<ng-template #satisfactionModal let-modal>
  <div class="card ">
    <div class="modal-header border-bottom-0 " style="gap: 20px;">
      <h5 class="modal-title d-flex align-items-center" style="color: #110C22; font-weight: 600; font-size: 20px;"
        class="mb-0">
        <img src="../assets/images/decision-making.png" style="height: 40px; width: 40px;" alt="Sondages">

        Sondage de Satisfaction
      </h5>
      <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"><i class="bi bi-x"
          style="font-size: 28px;"></i>
      </button>
    </div>
    <div style="background-color: var(--wf-primary-400);" class="text-white p-2  d-flex justify-content-between">
      <div class="d-flex align-items-center" style="gap: 20px; ">
        <i class="bi bi-bag-check " style="font-size: 30px;"></i>
        <span style="font-size: 16px; font-weight: 500;"> {{ titreOffre}}</span>
      </div>
      <div class="d-flex align-items-center" style="gap: 20px;">
        <i class="bi bi-shop " style="font-size: 30px;"></i>
        <span style="font-size: 20px; font-weight: 600;">
          {{ laboratoire }}
        </span>
      </div>
    </div>
    <div class="p-2">
      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-basket2-fill " style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Qualité et variété des produits proposés</label>
        </div>
        <ngb-rating [(rate)]="avis.qualite" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-tags-fill" style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Attrait et ampleur des réductions proposées</label>
        </div>
        <ngb-rating [(rate)]="avis.reduction" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-cash-stack" style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Souplesse des conditions de paiement</label>
        </div>
        <ngb-rating [(rate)]="avis.paiement" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-truck" style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Options et rapidité des délais de livraison</label>
        </div>
        <ngb-rating [(rate)]="avis.livraison" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-shop" style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Evaluation du laboratoire</label>
        </div>
        <ngb-rating [(rate)]="avis.laboratoire" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="">
        <label for="feedback" class="form-label">Avez-vous des réflexions que vous aimeriez partager ?</label>
        <textarea class="form-control satisfaction-textarea" id="feedback" rows="3"
          [(ngModel)]="avis.commentaire"></textarea>
      </div>
      <div class="d-flex justify-content-center" style="gap: 10px;">
        <button type="button" class="btn"
          style="color: #FFFFFF; border-width: 2px; border-color: #A9B2CA; color: #717B94; border-radius: 10px;"
          (click)="modal.dismiss()">Annuler</button>
        <button type="button" class="btn" style="background-color: var(--wf-primary-400); color: #FFFFFF; border-radius: 10px;"
          (click)="soumettreSatisfactionModal()">Soumettre</button>
      </div>
    </div>
  </div>
</ng-template>