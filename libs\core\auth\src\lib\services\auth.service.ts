import { Inject, Injectable, Injector, NgZone } from '@angular/core';
import {
  HttpClient,
  HttpEvent,
} from '@angular/common/http';
import { finalize, map } from 'rxjs/operators';
import { DomSanitizer, Title } from '@angular/platform-browser';

import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { ServiceFirebaseCloudMessagingService } from './service-firebase-cloud-messaging.service';
import { SelectedPlateforme } from '@wph/web/layout';
import { HasAccessService, IUserAccount, PlateformeService, Principal, Role, RolesEnum, ServiceOption, SocieteType, SSOResponseModel, UserRole } from '@wph/shared';
import { Router } from '@angular/router';
import { FeatureFlagService, KeyboardShortcutService } from '@wph/web/shared';
import { jwtDecode } from 'jwt-decode';
import { FEATURE_KEY_STORAGE } from '@wph/data-access';

@Injectable({
  providedIn: 'root',
})
export class AuthService {

  private principal: Principal = null;
  baseUrl: string;
  multiUser: boolean;
  private stackOfPrincipalsByLevel: any = { 1: [], 2: [] };

  private principals: Principal[] = [];
  private navigationSourceAndTargetMap: { [source: string]: 'WIN_OFFRE' | 'PHARMALIEN' } = {};

  private principalSubject = new Subject<Principal>();
  principalCleared$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  principal$ = this.principalSubject.asObservable();

  constructor(
    @Inject('ENVIROMENT') private envir: any,
    private ngZone: NgZone,
    private injector: Injector,
    private titleService: Title,
    private httpClient: HttpClient,
    private _sanitizer: DomSanitizer,
    private hasAccessService: HasAccessService,
    private plateformeService: PlateformeService,
    private featureFlagService: FeatureFlagService,
    private keyboardShortcutService: KeyboardShortcutService,
    private serviceFirebaseCloudMessagingService: ServiceFirebaseCloudMessagingService
  ) {
    this.baseUrl = envir['base_url'];
    this.multiUser = envir['multi_user'];
  }

  login(email: string, password: string) {
    return this.httpClient
      .post<Principal>(
        this.baseUrl + '/api/user/auth',
        {
          username: email,
          password: password,
        },
        { observe: 'response' }
      )
      .pipe(
        map(res => this.authenticateSuccess(res)),
        finalize(() => {
          if ((this.envir?.platform === 'MOBILE') && this.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])) {
            this.hasAccessService.setListeFournisseurAvecAcces();
          }
        })
      );
  }

  logout() {
    if (this.multiUser) {
      this.principals.splice(this.principals.indexOf(this.principal), 1);

      if (this.principal) {
        localStorage.removeItem(
          'principal_' + this.principal.indexInMultiUsers
        );
      }

      this.principal =
        this.principals.length > 0
          ? this.principals[this.principals.length - 1]
          : null;
    } else {
      this.principal = null;
      localStorage.removeItem('tokenExp');
      localStorage.removeItem('principal');
      localStorage.removeItem('exitedWinPlusSession');

      this.hasAccessService.clearAccess();
      this.plateformeService.clearPlateformeKeys();
    }

    this.principalSubject.next(this.principal);
    setTimeout(() => this.principalCleared$.next(true), 800);

    this.keyboardShortcutService.clearKeyboardShortcutSubscriptions();
  }

  /************************ refresh token methods *************************/
  refreshToken(): Observable<HttpEvent<any>> {
    return this.httpClient
      .post<Principal>(
        this.baseUrl + '/api/user/auth',
        {
          accessToken: this.getToken(),
        },
        { observe: 'response' }
      )
      .pipe(map(refreshTokenSuccess.bind(this)));

    function refreshTokenSuccess(resp: { body: { accessToken: any } }) {
      const bearerToken = resp.body.accessToken;
      if (bearerToken) {
        const indexInMultiUsersBackup = this.principal.indexInMultiUsers;
        this.principal = resp.body;

        console.log(
          '*****************************   token refresh success   **********************************'
        );

        this.principal.safeLogo = this._sanitizer.bypassSecurityTrustUrl(
          this.principal.logo
        );

        if (this.multiUser) {
          this.principal.indexInMultiUsers = indexInMultiUsersBackup;
          localStorage.setItem(
            'principal_' + indexInMultiUsersBackup,
            JSON.stringify(this.principal)
          );
        } else {
          localStorage.setItem('principal', JSON.stringify(this.principal));
        }

        this.principalSubject.next(this.principal);

        // this.startRefreshTokenTimer();

        return bearerToken;
      } else {
        return null;
      }
    }
  }

  getMyAccount() {
    return this.httpClient.get<IUserAccount>(
      this.baseUrl + '/api/user/myaccount',
      {
        observe: 'body',
        responseType: 'json',
      }
    );
  }

  activateUserEmail(token: string) {
    return this.httpClient.get<any>(
      this.baseUrl + '/api/user/activate' + '?tk=' + token,
      {
        observe: 'body',
        responseType: 'json',
      }
    );
  }

  saveAccount(account: IUserAccount) {
    return this.httpClient.post<IUserAccount>(
      this.baseUrl + '/api/user/edit',
      account,
      {
        observe: 'body',
        responseType: 'json',
      }
    );
  }

  getPrincipal() {
    if (this.principal == null) {
      if (this.multiUser) {
        // construct principals array
        const keys = Object.keys(localStorage);
        for (const key of keys) {
          if (key.includes('_')) {
            const principalString = localStorage.getItem(key);
            if (principalString) {
              const principal = JSON.parse(principalString);
              this.principals.push(principal);
            }
          }
        }

        if (this.principals.length) {
          const principalString = localStorage.getItem(
            'principal_' + this.principals[0].indexInMultiUsers
          );
          if (principalString) this.principal = JSON.parse(principalString);
        }
      } else {
        const principalString = localStorage.getItem('principal');
        if (principalString) this.principal = JSON.parse(principalString);
      }

      this.principalSubject.next(this.principal);
    }

    return this.principal;
  }

  currentUser() {
    return this.getPrincipal();
  }

  currentUsers() {
    return this.principals;
  }

  updatePrincipal(principal: Principal): void {
    localStorage.setItem('principal', JSON.stringify(this.principal));
    this.principalSubject.next(principal);
  }

  changeCurrentUser(user: Principal) {
    if (this.principals.includes(user)) {
      this.principal = user;

      this.principalSubject.next(this.principal);
    } else {
      console.warn('user not found');
    }
  }

  getToken() {
    if (this.principal === null) this.principal = this.getPrincipal();

    return this.principal ? this.principal.accessToken : null;
  }

  getTokenExp(): number {
    const token = this.getToken();
    const tokenExp = localStorage.getItem('tokenExp');

    if (!tokenExp && token) {
      const decodedToken = jwtDecode(token);
      localStorage.setItem('tokenExp', decodedToken?.exp?.toString());

      return decodedToken?.exp;
    }

    return +tokenExp;
  }

  setTokenExp(): void {
    const token = this.getToken();

    const decodedToken = jwtDecode(token);
    localStorage.setItem('tokenExp', decodedToken?.exp?.toString());
  }

  getTokenByLevel(level: number) {
    let principalObj = this.stackOfPrincipalsByLevel[level].pop();

    return principalObj ? principalObj.accessToken : null;
  }

  isAuthenticated() {
    return this.getToken() !== null;
  }

  hasAnyAuthority(authorities: UserRole[]): boolean {
    if (!this.isAuthenticated() || !authorities || !authorities?.length) {
      return false;
    }

    for (let i = 0; i < authorities.length; i++) {
      if (
        this.getPrincipal() &&
        this.getPrincipal()?.authorities.includes(authorities[i])
      ) {
        return true;
      }
    }

    return false;
  }

  hasAnyServiceOption(serviceOptions: ServiceOption[]): boolean {
    if (this.isAuthenticated() && this.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']) && !!this.plateformeService.getCurrentGrossiste()) {
      const fournisseurId = this.plateformeService.getCurrentGrossiste()?.id;
      const currentPlateformeCode = this.plateformeService.getCodeOfCurrentPlateforme();

      const availableServiceOptions = this.hasAccessService
        .getListeFournisseurAvecAcces()
        .find(fournisseur => fournisseur?.fournisseurId === fournisseurId)
        ?.servicesClientOptions
        ?.find(option => !!option[currentPlateformeCode]);

      if (!!availableServiceOptions) {
        for (const option of availableServiceOptions[currentPlateformeCode]) {
          if (serviceOptions.includes(option as ServiceOption)) return true;
        }
      }
    }

    return false;
  }

  resetPassword(email: string) {
    return this.httpClient.get<any>(this.baseUrl + '/api/user/resetpassword', {
      params: { email: email },
      observe: 'body',
      responseType: 'json',
    });
  }

  checkUsernameForReset(code: string) {
    return this.httpClient.get(this.baseUrl + '/api/user/check-username-for-reset', {
      params: { username: code },
      observe: 'body',
      responseType: 'text'
    });
  }

  resetPasswordBySms(code: string, gsm: string) {
    return this.httpClient.get(this.baseUrl + '/api/user/reset-pwd-bysms', {
      params: { username: code, gsm },
      observe: 'body',
      responseType: 'text',
    });
  }



  changePassword(token: string, newPass: string) {
    return this.httpClient.get<any>(this.baseUrl + '/api/user/changepassword', {
      params: { token: token, newpassword: newPass },
      observe: 'body',
      responseType: 'json',
    });
  }

  getUserRole(user: Principal) {
    const type: SocieteType = user?.societe?.typeEntreprise;

    switch (type) {
      case 'OWNER':
        return user?.authorities?.includes('ROLE_NATIONAL') ? RolesEnum.NATIONAL : RolesEnum.SUPERADMIN;
      case 'CLIENT':
        return this.getClientRole();
      case 'GROSSISTE':
      case 'FABRIQUANT':
        return this.getRoleFournisseur();
    }

    return user?.societe?.typeEntreprise === 'OWNER' ? RolesEnum.SUPERADMIN : user?.societe?.typeEntreprise === 'CLIENT' ? RolesEnum.POINT_DE_VENTE : RolesEnum.FOURNISSEUR;
  }

  getClientRole(): string {
    const principal = this.getPrincipal();

    if (
      principal?.authorities?.includes('ROLE_AGENT_POINT_VENTE') &&
      (
        !principal?.authorities?.includes('ROLE_NATIONAL') &&
        !principal?.authorities?.includes('ROLE_RESPONSABLE')
      )
    ) {
      return principal?.authorities?.includes('ROLE_AGENT_POINT_VENTE') ?
        RolesEnum.POINT_DE_VENTE : RolesEnum.ASSISTANT;

    } else if (
      principal?.authorities?.includes('ROLE_AGENT_POINT_VENTE') &&
      principal?.authorities?.includes('ROLE_NATIONAL')
    ) {
      return RolesEnum.NATIONAL;

    } else if (
      principal?.authorities?.includes('ROLE_AGENT_POINT_VENTE') &&
      principal?.authorities?.includes('ROLE_RESPONSABLE')
    ) {
      return RolesEnum.RESPONSABLE;

    } else if (principal?.authorities?.includes('ROLE_ASSISTANT')) {
      return RolesEnum.ASSISTANT;
    } else {
      return RolesEnum.POINT_DE_VENTE;
    }

  }

  getRoleFournisseur() {
    if (this.hasAnyAuthority(['ROLE_AGENT_COMMERCIAL'])) return RolesEnum.COMMERCIAL;
    else if (this.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) return RolesEnum.FOURNISSEUR;
    else if (this.hasAnyAuthority(['ROLE_AGENT_ACHAT'])) return RolesEnum.ACHAT;
    else return RolesEnum.FOURNISSEUR;
  }


  setToken(token: string): Observable<any> {
    return this.httpClient.post<any>(this.baseUrl + '/api/notif/push/settoken', null, { params: { token: token } });
  }

  unsetToken(): Observable<any> {
    return this.httpClient.post<any>(this.baseUrl + '/api/notif/push/unsettoken', null);
  }

  getTargetPlateforme(): SelectedPlateforme | undefined {
    return (sessionStorage.getItem('TARGET_PLATEFORME') as SelectedPlateforme) ?? undefined;
  }

  private convertToTitleCase(str: string): string {
    return str
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  setTitle(titleSuffix: string): void {
    const titlePrefix = this.convertToTitleCase(this.plateformeService.getCurrentPlateforme() || 'WIN_OFFRE');

    this.titleService.setTitle(`${titlePrefix}: ${titleSuffix}`);
  }

  requestDeactivation(): Observable<any> {
    const idHash = this.currentUser()?.idhash;
    return this.httpClient.get<any>(`${this.baseUrl}/api/user/requestdeactivation?userIdHash=${idHash}`);
  }

  redirect(): Observable<any> {
    return this.httpClient.get<any>(`${this.baseUrl}/api/v1/users/redirect`);
  }

  getMyPrincipal(authToken: string, targetPlateforme = 0): Observable<any> {
    return this.httpClient.get<any>(`${this.baseUrl}/api/user/myprincipal`, { headers: { Authorization: `Bearer ${authToken}` } }).pipe(
      map(res => { return { body: res } }),
      map(res => this.authenticateSuccess(res)),
      finalize(() => {
        const router = this.injector.get(Router);
        this.ngZone.run(() => {
          if (targetPlateforme === 1) this.plateformeService.setCurrentPlateforme('WIN_OFFRE');
          router.navigate([!targetPlateforme ? '/pharma-lien' : '/win-offre'], { replaceUrl: true });
        });
      }
      )
    );
  }

  getSsoToken(key: string): Observable<SSOResponseModel> {
    return this.httpClient.get<SSOResponseModel>(`${this.baseUrl}/api/sso/gettoken?key=${key}`, { responseType: 'json' });
  }

  getAllRoles(): Observable<Role[]> {
    return this.httpClient.get<Role[]>(`${this.baseUrl}/api/user/get-all-roles`, { responseType: 'json' });
  }

  authenticateSuccess(resp: any) {
    const bearerToken = resp?.body?.accessToken;
    if (bearerToken) {
      this.principal = resp?.body;

      this.principal.safeLogo = this._sanitizer.bypassSecurityTrustUrl(
        this.principal.logo
      );

      if (this.multiUser) {
        this.principals.push(this.principal);
        this.principal.indexInMultiUsers =
          this.principals.length > 1
            ? this.principals[this.principals.length - 2].indexInMultiUsers +
            1
            : 1;
        localStorage.setItem(
          'principal_' + this.principal.indexInMultiUsers,
          JSON.stringify(this.principal)
        );
      } else {

        localStorage.setItem('principal', JSON.stringify(this.principal));
      }

      this.principalSubject.next(this.principal);

      if (this.envir['base_url']) {
        // force set token for push notification
        this.serviceFirebaseCloudMessagingService.forceSetToken();
      }

      if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
        this.keyboardShortcutService.addNavigationKeyboardShortcuts();

        this.getMyAccount().subscribe(res => {
          let principal = this.getPrincipal();
          principal.societe.email = res?.email;

          this.updatePrincipal(principal);
          this.getTokenExp();
        });
      }

      return bearerToken;
    } else {
      const resetPasswordToken = resp?.body['resetPasswordToken'];

      if (resetPasswordToken) {
        return { token: resetPasswordToken };
      }

      return null;
    }
  }


  isAuthTokenExpired(lastMinutes?: number): boolean {
    const token = this.getToken();
    const tokenExp = this.getTokenExp();

    if (!token || !tokenExp) {
      return false; // No token or invalid expiration time, assume not expired
    }

    const nowInSeconds = Date.now() / 1000;

    if (lastMinutes !== undefined) {
      const timeSinceExpiration = nowInSeconds - tokenExp;
      // Token must be expired and within the lastMinutes threshold
      return (tokenExp < nowInSeconds) && (timeSinceExpiration <= lastMinutes * 60);
    }

    // Default behavior: check if the token is expired
    return tokenExp < nowInSeconds;
  }

  clearNavigationSourceAndTarget() {
    this.navigationSourceAndTargetMap = {};
    sessionStorage.removeItem('sourceNavigation');
  }

  hasTargetPlateforme(target: 'WIN_OFFRE' | 'PHARMALIEN'): boolean {
    return this.getNavigationSourceAndTarget() === target;
  }

  setNavigationSourceAndTarget(target: 'WIN_OFFRE' | 'PHARMALIEN', source = 'WIN_PLUS') {
    this.navigationSourceAndTargetMap[source] = target;
    sessionStorage.setItem('sourceNavigation', JSON.stringify(this.navigationSourceAndTargetMap));
  }

  getNavigationSourceAndTarget(source = 'WIN_PLUS'): "WIN_OFFRE" | "PHARMALIEN" | null {
    if (this.navigationSourceAndTargetMap && this.navigationSourceAndTargetMap[source]) {
      return this.navigationSourceAndTargetMap[source];
    } else {
      const sourceNavigation = sessionStorage.getItem('sourceNavigation');

      if (sourceNavigation) {
        const sourceNavigationMap = JSON.parse(sourceNavigation);

        return sourceNavigationMap[source];
      }
    }

    return null;
  }

  canAccessFeature(featureKey: string, userRoles: UserRole[], canAccessFeature: boolean, targetStorageKeys?: FEATURE_KEY_STORAGE[]): boolean {
    return (
      this.featureFlagService.isFeatureEnabled(featureKey, targetStorageKeys) &&
      (
        canAccessFeature || this.hasAnyAuthority(userRoles)
      )
    );
  }

}
