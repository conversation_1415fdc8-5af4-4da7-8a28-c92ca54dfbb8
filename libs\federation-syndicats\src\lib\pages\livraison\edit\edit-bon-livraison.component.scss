.btn-success{
    background-color: var(--fs-success) !important;
    border-color: var(--fs-success) !important;
}

.text-success{
  color: var(--fs-success) !important;
}


.btn-exapnd-details{
    background-color: var(--wf-primary-400) !important;
    border-color: var(--wf-primary-400) !important;
    height: 25px;
    width: 25px;
    padding: 0;
    font-size: 1rem;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 5px;
    position: absolute;
    cursor: pointer;
    right: 0;
    bottom: 0;
i{
    line-height: 1;
}
}

.b-radius {
    border-radius: var(--winoffre-base-border-radius) !important;
  }

  .picker-input {
    .form-control {
      border-radius: var(--winoffre-base-border-radius) !important;
    }
  }

.divider-y > *:not(:last-child) {
    border-right: 1px solid #d5d5d5 !important;
  }

.btn-danger{
    background-color: var(--fs-danger) !important;
    border-color: var(--fs-danger) !important;
}



.tabs-separate{
  background: #f0f2f5;
  width: 20px;
  /* height: 100%; */
  flex: none !important;
  @media screen and (max-width: 768px) {
    display: none;

  }
}



::ng-deep .bl-header-actions app-export-pdf button[title="Imprimer"]{
  margin: 0 !important;
  padding-inline: 8px !important;
  padding-block: 2px !important;
  i{
    font-size: 24px;
  }
}


::ng-deep ngb-typeahead-window {
  max-height: 332px;
  overflow-y: scroll;
  width: 100%;
}


.invalid-input{
  border: 2px solid #FF0000;
}


input[readonly] {
  color: black !important;
  font-weight: 600 !important;;
}


.synthese-container {
  background: #EBEBEB;
  border-radius: 10px;
}


.bl-input-search{
  width: 350px;
  border: none;
  @media screen and (max-width: 768px) {
    width: 100%;
  }

  &-wrapper{
    // width: 100%;
    @media screen and (max-width: 768px) {
      width: 100%;
    }
  }
}



.form-search-bl .form-group {
  @media screen and (max-width: 768px) {
    flex-basis: 100%;
  }
}



::ng-deep  .k-grid-content .line-invalid ,
::ng-deep .k-grid-content .line-invalid:hover{
  background-color: rgba(253, 47, 47, 0.301) !important;
  }




  ::ng-deep .k-grid-content .line-valid ,
  ::ng-deep .k-grid-content .line-valid:hover{
      background-color: rgba(0, 255, 0, 0.103) !important;
    }