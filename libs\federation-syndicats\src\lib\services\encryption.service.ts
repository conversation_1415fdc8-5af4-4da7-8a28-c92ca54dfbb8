import { Inject, Injectable } from '@angular/core';
const CryptoJS  = require('crypto-js');

@Injectable({
  providedIn: 'root'
})
export class EncryptionService {

  base64Key: any

  constructor(@Inject('ENVIROMENT') private env: any) { 
    this.base64Key = CryptoJS.enc.Base64.parse(this.env?.base64Key);
  }

  decryptCoords(data){
    let decryptedData =  CryptoJS.AES.decrypt(data, this.base64Key , {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
    }).toString(CryptoJS.enc.Utf8);

    return decryptedData;
  }

  encryptingData(data){
    let ecryptedData =  CryptoJS.AES.encrypt(data, this.base64Key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    }).toString(CryptoJS.enc.Utf8);

    return ecryptedData;
  }

}
