.gridbtn {
  .display {
    margin-right: 0;
  }
}

.display {
  display: inline-block;
  padding: 5px 0;
  min-width: 90px;
  border: 1px solid silver;
  border-radius: 5px;

  .grids {
    display: grid;
    grid-template-columns: auto;
    grid-template-rows: 1fr;
    gap: 0px 0px;
    grid-template-areas: "s1 s2 s3";
  }

  .s1 {
    grid-area: s1;
    text-align: center;

    i {
      transform: translateY(3px);
    }

    cursor: pointer;
  }

  .s2 {
    grid-area: s2;
    margin: auto;

    input {
      width: 100%;
      border: 1px solid silver;
      outline: 0px solid silver;
      border-radius: 5px;
    }
  }

  .s3 {
    grid-area: s3;
    text-align: center;

    i {
      transform: translateY(3px);
    }

    cursor: pointer;
  }

  // .fa-minus {
  //   color: #ff5722;
  // }
  // .fa-plus {
  //   color: #81d29a;
  // }
}
