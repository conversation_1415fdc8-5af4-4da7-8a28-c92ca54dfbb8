.image-container {
    background-image: var(--fs-actu-default-img);
    background-size: cover;
    background-position: center;
    border-radius: 12px;
    overflow: hidden;
}

// .sondage-item {
//     transition: all 0.1s ease;
// }

#wf-container {
  .sondage-item.expanded,
  .sondage-item:hover {
      border: 1px solid var(--wf-primary-500);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
      border-radius: 12px;
  }
  
  .sondage-item:hover .offre-title ,
  .sondage-item.selected .offre-title ,
  .sondage-item:hover .interet ,
  .sondage-item.selected .interet ,
  .sondage-item:hover i.transition-icon,
  .sondage-item.selected i.transition-icon
    {
      color: var(--wf-primary-500) !important;
  }
  
  .sondage-item:hover .image-container ,
  .sondage-item.selected .image-container {
      border: 2px solid var(--wf-primary-500);
      border-radius: 12px;
  }
}

#fs-container {
  .sondage-item.expanded,
  .sondage-item:hover {
      border: 1px solid var(--fs-grid-primary);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
      border-radius: 12px;
  }
  
  .sondage-item:hover .offre-title ,
  .sondage-item.selected .offre-title ,
  .sondage-item:hover .interet ,
  .sondage-item.selected .interet ,
  .sondage-item:hover i.transition-icon,
  .sondage-item.selected i.transition-icon
    {
      color: var(--fs-grid-primary) !important;
  }
  
  .sondage-item:hover .image-container ,
  .sondage-item.selected .image-container {
      border: 2px solid var(--fs-grid-primary);
      border-radius: 12px;
  }
}

// switch icons :

.status-switch-align.k-switch-on .k-switch-thumb {

    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: "\F26E";
      font-family: "Bootstrap-icons";
      color: var(--fs-success);
      font-size: 20px;
    }
  }

 .status-switch-align .k-switch-thumb {

    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: "\F26E";
      font-family: "Bootstrap-icons";
      color: #DCB544;
      font-size: 20px;
    }
  }


 .status-switch-align.fs-soundage-switch.k-switch-md {
    width: 110px;
  }

 .status-switch-align.fs-soundage-switch.k-switch-md .k-switch-track {
    width: 150px !important;
}

 .k-switch-off .k-switch-track {
    border-color: #DCB544;
    color: white;
    background-color: #DCB544;
}

.search-inpt-cstm {
    font-size: 1rem;
    font-weight: 600;
    border-radius: 10px !important;
}

.btn {
  font-size: 1rem;
  font-weight: 600;
}

.b-radius {
  border-radius: 10px !important;
}

.input-group {
  .form-control {
    color: black;
    font-weight: 700;
      border-top-left-radius: var(--winoffre-base-border-radius);
      border-bottom-left-radius: var(--winoffre-base-border-radius);

  }
  .btn {
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-right-radius: var(--winoffre-base-border-radius);
  }
}

label {
  color: black !important;
  font-weight: 700 !important;
}

.picker-input {
  .form-control{
      border-radius: var(--winoffre-base-border-radius) !important;
  }
}

.sondages-container {
  display: flex;
  flex-direction: column;
  max-height: 100vh; /* Ensure the container takes the full height of the viewport */
 /* This accounts for the header and any padding/margin */
  overflow: hidden; /* Prevent the parent container from causing overflow issues */
}

.sondages-list {
  flex: 1;
  overflow-y: auto; /* Allow vertical scrolling for the list */
  padding: 1rem;
  margin-bottom: 1rem;
  max-height: calc(100vh - 110px); /* Adjust this to fit the actual layout */
}

.sondage-item {
  margin-bottom: 1rem;
  padding: 1rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.table-container {
  display: none;
  overflow: hidden;
}

.sondage-item.expanded .table-container {
  display: block;
}

  kendo-grid {
    flex: 1;
    overflow: auto; // Allow the grid to scroll if needed
  }

