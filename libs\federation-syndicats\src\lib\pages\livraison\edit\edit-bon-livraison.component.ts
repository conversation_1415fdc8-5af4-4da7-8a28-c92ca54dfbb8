import { Produit } from './../../../../../../data-access/src/lib/models/produit.model';
import { RechercheProduitComponent } from './../../../../../../web/shared/src/lib/components/gestion-offre/recherche-produit/recherche-produit.component';
import { Offreur } from './../../../../../../data-access/src/lib/models/stats.model';
import { Component, ElementRef, OnDestroy, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModal, NgbTypeaheadSelectItemEvent } from '@ng-bootstrap/ng-bootstrap';
import { DataStateChangeEvent, GridDataResult, RowClassArgs } from "@progress/kendo-angular-grid";
import { filterBy } from "@progress/kendo-data-query";
import { AuthService } from '@wph/core/auth';
import { Fournisseur, Offre, OffresService, Pagination } from "@wph/data-access";
import { AlertService, PlateformeService } from "@wph/shared";
import { DeferredActionButtonsService, ExportPdf, ExportPdfService, UserInputService } from "@wph/web/shared";
import * as moment from 'moment';
import { Observable, OperatorFunction, Subject, debounceTime, distinctUntilChanged, of, switchMap, takeUntil } from "rxjs";
import { DetailBlAchatGroupeDTO, DetailBlIndividuelDTO, EnteteBlAchatGroupeDTO, EnteteBlConsolideeMarcheDTO, EnteteBlIndividuel, EtatBlAchatGroupeEnum } from "../../../models/bl.model";
import { DetailCommandeDto, EnteteCommandeConsolideeMarche } from "../../../models/entete-commande.model";
import { FederationSyndicatService } from "../../../services/federation-syndicats.service";
import { FsBLService } from "../../../services/fs-bl.service";
import { FsCommandesService } from '../../../services/fs-commandes.service';
import { BLCriteria } from "../../../models/bl-criteria";
import { FsCommandeCriteria } from "../../../models/fs-commande.model";

enum PageMode {
  SAISIE = 'S',
  P = 'P'
}

@Component({
  selector: 'wph-edit-bon-livraison',
  templateUrl: './edit-bon-livraison.component.html',
  styleUrls: ['./edit-bon-livraison.component.scss']
})
export class EditBonLivraisonComponent implements OnInit, OnDestroy {

  unsubscribe$: Subject<boolean> = new Subject<boolean>();

  @ViewChild('BLNameEDIT') BLnameInput: ElementRef<HTMLInputElement>;
  @ViewChild('addProductModal', { static: true }) addProductModal: TemplateRef<any>;
  @ViewChild(RechercheProduitComponent) rechercheProduitComponent: RechercheProduitComponent;




    idBL:string;
    BLName ='Bon de Livraison';
    idCommande:number;
    fournisseur: Fournisseur[] = [];
    searchResults = [];
    enableBLNameEdit = false;
    formSubmitted = false;
    isConsult= false;
    filter = '';
    isLoading = true;
    products = [];
    filterList: FormControl = new FormControl();
    displayFilter = false;
    BLFilterForm: FormGroup;
    advFilterForm:FormGroup;
    isUnitaire = false;
    isIndividuelle = false;
    blobUrl = null;
    synthese: { qLivree: number; mntBrut: number; mntNet: number; mntRemise: number; };
    isReadOnly: boolean;
    currentPlateforme: string;    

    searchProduitNavigation : Pagination = { pageSize: 20, skip: 0 };
    produitSearchResult: GridDataResult = { data: [], total: 0 };
    indexBlockOffre = 0;

    gridData: GridDataResult = {
      data:[ ] as DetailBlAchatGroupeDTO[],
      total:0
    }




    pageMode :PageMode = PageMode.SAISIE



    saisieBLForm:FormGroup;

    enteteBl:EnteteBlConsolideeMarcheDTO | EnteteBlIndividuel
    isCoffretEnabled: boolean;

    onRowSelect(event: any) {
      const selectedItem = event.dataItem;
      const currentBL = this.saisieBLForm.get('enteteCommandeAchatGroupe').value;

      if (currentBL) {
        this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir changer le bon de livraison ?`).then(async (confirmed) => {
          if (confirmed) {
            this.saisieBLForm.patchValue({
              enteteCommandeAchatGroupe: selectedItem
            });
        if(this.isIndividuelle){
          this.getCommandForSaieBLIndividuel(selectedItem.id);
        }else{
                   this.getCommandForSaieBL(selectedItem.id);
        }

            this.displayFilter = false;
            this.advFilterForm.reset();
            this.searchResults = [];
          }
        });
      } else {
        this.saisieBLForm.patchValue({
          enteteCommandeAchatGroupe: selectedItem
        });
        if(this.isIndividuelle){
          this.getCommandForSaieBLIndividuel(selectedItem.id);
        }else{
          this.getCommandForSaieBL(selectedItem.id);
        }
        this.displayFilter = false;
        this.advFilterForm.reset();
        this.searchResults = [];
      }

    }

    constructor(
      private router:Router,
      private alertService: AlertService,
      private userInputService: UserInputService,
      private fsBLService:FsBLService,
      private route: ActivatedRoute,
      private offreService: OffresService,
      private fb: FormBuilder,
      private commandesService: FsCommandesService,
      private authService: AuthService,
      private fsService: FederationSyndicatService,
      private deferredActionBtnService: DeferredActionButtonsService,
      private platformService:PlateformeService,
      private modalService:NgbModal
    ) {

      if(this.router.url.includes('unitaire')){
        this.isUnitaire = true;
      } else if(this.router.url.includes('individuelle')){
        this.isIndividuelle = true;
      }
      this.route.params.subscribe(params => {
        this.idBL = params['id'];
        this.idCommande = params['cmd'];
        if (this.idBL && !this.isUnitaire && !this.isIndividuelle) {
          this.pageMode = PageMode.P;
          this.getBLConsolideById();
        }
        this.initSaisieBLForm();
         if (this.idCommande && !this.idBL && !this.isIndividuelle) {
          this.getCommandForSaieBL(this.idCommande);
        }

        if (!this.idCommande && this.idBL && this.isUnitaire) {
          console.log("isUnitaire",this.isUnitaire)
          this.isConsult = true;
          this.saisieBLForm.disable();
          this.getBLUnitaireById();


        }
        if(!this.idCommande && !this.idBL){
          this.isLoading = false;
        }

        if(this.isIndividuelle && this.idCommande && !this.idBL){
          // get bl by commande
          this.getCommandForSaieBLIndividuel(this.idCommande);
          console.log("isIndividuelle && idCommande",this.idCommande,this.idBL)

        }else if(this.isIndividuelle && this.idBL && this.idCommande){
          this.getBLIndividuelById();
        }
        else if(this.isIndividuelle && this.idBL && !this.idCommande){
          this.getBLIndividuelById();
        }

      });

      this.route.queryParams.subscribe(params=>{
        if(params['readOnly'] === 'true'){
          this.saisieBLForm.disable();
          this.isConsult = true;
          this.isReadOnly = true;
        }

      })

      this.listenForRemiseChange()
    }




    get isInactive$() {
      return this.fsService.inactiveAccount$;
    }

    initFilterForm() {
      this.BLFilterForm = new FormGroup({
          numBl: new FormControl(null),
          numcommande: new FormControl(null),
          dateDebut: new FormControl(null),
          dateFin: new FormControl(null),
      });
      this.advFilterForm = new FormGroup({
         codeCommande: new FormControl(null),
          offreur: new FormControl(null),
          distributeur: new FormControl(null),
          dateCreationDebut: new FormControl(null),
      });

  }

  listenToSearchFilterChanges(): void {
    this.filterList.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(200),
        distinctUntilChanged()
      )
      .subscribe((res: string) => {
        console.log("res", res);

        if (res?.length) {
          this.BLFilterForm.patchValue({ numBl: res });
        } else {
          this.BLFilterForm.patchValue({ numBl: null });
        }
      });
  }

  filterCommandes() {
    this.applyFilters();
  }

  clearFilterCommandes() {
    this.BLFilterForm.reset();
    this.searchResults = [];

  }



  BuildFilterModel() {
    const CommandeSearchCreteria =  new BLCriteria({
      numeroBl: this.BLFilterForm.get('numBl').value,
      codeCommande: this.BLFilterForm.get('numcommande').value,
      dateReceptionDebut: this.BLFilterForm.get('dateDebut').value,
      dateReceptionFin: this.BLFilterForm.get('dateFin').value,
      supporteurCmdConsolideeId:this.authService.getPrincipal().societe?.id
    });
    return CommandeSearchCreteria;

  }


  getCommandForSaieBL(id: number) {
    this.commandesService.getCommandeConsolideeById(id).subscribe(res => {
      if (res?.etatCommande === 'LIVREE') {
        this.alertService.error(`Impossible de saisir un nouveau bon de livraison car la commande groupée est déjà totalement livrée.`, 'MODAL');
        this.back();
        return;
      }

      this.saisieBLForm.patchValue({
        enteteCommandeAchatGroupe: res,
        fournisseur: res.distributeur,
        tauxRf: res.tauxRf ?? 0,
        dateReceptionBl: moment(),
        transporteur: res.transporteur,
        raisonSocialeTransporteur: res.offre.raisonSocialeTransporteur,
      });

      if (res?.listeDetails?.length) {
        this.isCoffretEnabled = !!res?.listeDetails[0]?.blocOffre?.qteFixePrdInCoffret;
      }

      const lignes = res.listeDetails.map((ligne) => {
        return this.buildGridDataFromBL({
          id: null,
          produit: ligne,
          pph: ligne.prixVenteTtc,
          ppv: ligne.ppv,
          quantiteCommandee: ligne.qteCmd,
          quantiteLivree: 0,
          quantiteUg: 0,
          designation: ligne.libelleProduit,
          quantiteUgCmd: ligne.qteUg,
          blocOffreId: ligne.blocOffre.id,
          tauxRf: ligne.tauxRemise ?? 0,
          oldTauxRf: ligne.tauxRemise ?? 0,
          pphRemise: this.calcualtePPHRemise2(ligne.prixVenteTtc,ligne.tauxRemise),
          oldPphRemise: this.calcualtePPHRemise2(ligne.prixVenteTtc,ligne.tauxRemise),
          isCadeau: ligne.isCadeau,
          qteFixePrdInCoffret: ligne?.blocOffre?.qteFixePrdInCoffret,
          qteReliquatBl:ligne.qteReliquatBl,
          enteteBl:{etatBl:'BROUILLON'} as EnteteBlAchatGroupeDTO,
        });
      });
      this.gridData.data = lignes;
      this.products = lignes;
      this.saisieBLForm.get('enteteCommandeAchatGroupe').disable();
      this.saisieBLForm.get('fournisseur').disable();
      // this.calculatePPHRemise(this.gridData.data);
      this.generateSynthese(this.gridData.data);
      this.isLoading = false;

      this.pushMobileMenuOptions();
    });
  }
  getCommandForSaieBLIndividuel(id: number) {
    this.offreService.getCommandeDtoById(id).subscribe(res => {
      if (res?.etatCommande === 'LIVREE') {
        this.alertService.error(`Impossible de saisir un nouveau bon de livraison car la commande groupée est déjà totalement livrée.`, 'MODAL');
        this.back();
        return;
      }

      this.saisieBLForm.patchValue({
        enteteCommandeAchatGroupe: res,
        fournisseur: res.distributeur,
        tauxRf: res.tauxRf ?? 0,
        dateReceptionBl: moment(),
        transporteur: res.transporteur,
        raisonSocialeTransporteur: res.raisonSocialeTransporteur,
      });

      if (res?.listeDetails?.length) {
        this.isCoffretEnabled = !!res?.listeDetails[0]?.blocOffre?.qteFixePrdInCoffret;
      }

      const lignes = res.listeDetails.map((ligne) => {
        return this.buildGridDataFromBL({
          id: null,
          produit: ligne,
          pph: ligne.prixVenteTtc,
          ppv: ligne.ppv,
          quantiteCommandee: ligne.qteCmd,
          quantiteLivree: 0,
          quantiteUg: 0,
          designation: ligne.libelleProduit,
          quantiteUgCmd: ligne.qteUg,
          blocOffreId: ligne.blocOffre.id,
          tauxRf: ligne.tauxRemise ?? 0,
          pphRemise: ligne.prixVenteTtc,
          isCadeau: ligne.isCadeau,
          qteFixePrdInCoffret: ligne?.blocOffre?.qteFixePrdInCoffret,
          oldTauxRf: ligne.tauxRemise ?? 0,
          oldPphRemise: this.calcualtePPHRemise2(ligne.prixVenteTtc,ligne.tauxRemise),
          qteReliquatBl:ligne.qteReliquatBl,
          enteteBl:{etatBl:'BROUILLON'} as EnteteBlAchatGroupeDTO,
        });
      });

      this.gridData.data = lignes;
      this.products = lignes;
      this.saisieBLForm.get('enteteCommandeAchatGroupe').disable();
      this.saisieBLForm.get('fournisseur').disable();
      this.calculatePPHRemise(this.gridData.data);
      this.generateSynthese(this.gridData.data);
      this.isLoading = false;

      this.pushMobileMenuOptions();
    });
  }

    ngOnInit(): void {
      this.initFilterForm();

      this.platformService.currentPlateforme$.subscribe(res => {
        this.currentPlateforme = res;
        console.log("this.currentPlateforme", this.currentPlateforme);
      });
    }


    field(field: string) {
      return this.saisieBLForm.get(field);
    }

    hasError(field: string, error: string="required") {
      const control = this.field(field);
      return this.formSubmitted && control?.hasError(error);
    }


    openAddProductModal() {
      this.modalService.open(this.addProductModal, { ariaLabelledBy: 'modal-basic-title', size: 'xl' });
    }




    onTabChange(ev:any){
        this.enableBLNameEdit = false;
    }

    enableBLNameEditFn() {
      this.enableBLNameEdit = true;

      setTimeout(() => {
          this.BLnameInput?.nativeElement.select();
      }, 0);


    }



    dispatchBL() {
      const numeroBL = this.enteteBl?.id;

      this.router.navigateByUrl(`/achats-groupes/bons-livraison/edit/${numeroBL}/dispatch`);
    }



    openRechercheProduitModal() {
      this.rechercheProduitComponent.openModal();
    }





    onProduitSelected(produit: Produit[]) {

    const extraLines = this.gridData.data.filter(l => l.isExtraLine);
    const maxBlockOfferId = extraLines.length > 0 ? Math.max(...extraLines.map(l => l.blocOffreId)) : 0;
    this.indexBlockOffre = maxBlockOfferId + 1;
     const newLignes = produit.map((item) => {
       const currentBlockOfferId = this.indexBlockOffre++;
      return this.buildGridDataFromBL({
        id: null,
        produit:item,
        pph:item.prixVenteHt,
        ppv:item.ppv,
        quantiteCommandee:0,
        quantiteLivree: 0,
        quantiteUg: 0,
        designation:item.libelleProduit,
        quantiteUgCmd:0,
        blocOffreId:currentBlockOfferId,
        tauxRf:0,
        pphRemise:item.prixVenteHt,
        oldPphRemise:item.prixVenteHt,
        oldTauxRf:0,
        isCadeau:false,
        qteFixePrdInCoffret:0,
        qteReliquatBl:0,
        montant:0,
        enteteBl:{etatBl:'BROUILLON'} as EnteteBlAchatGroupeDTO,
        isExtraLine:true,
     })});
     this.gridData.data = [...this.gridData.data, ...newLignes];
     this.products = [...this.gridData.data];
     this.scrollToNewlyAddedItems();
    }


    CloturerBL() {
      this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir cloturer le bon de livraison ?`).then(async (confirmed) => {
        if (confirmed) {

          const groupe= await this.fsService.getMyGroupe()
          const groupeId = groupe.id;
          this.fsBLService.cloturerBLConsolide({blConsolideId: this.enteteBl.id,groupeId}).subscribe(res => {
            this.back();
            this.alertService.successAlt(`Le bon de livraison ${this.enteteBl.numeroBl} a été cloturé avec succès!`, 'Cloture du bon de livraison', 'MODAL');
          });
         }
      });
    }


    initSaisieBLForm() {


        this.saisieBLForm = this.fb.group({
          id:[null],
          fournisseur: [null, Validators.required],
          numeroBl:[null, Validators.required],
          cumulBl:[0, Validators.required],
          montantSaisi: [0, Validators.required],
          tauxRf:[0, Validators.required],
          transporteur: [null],
          raisonSocialeTransporteur: [null],
          dateReceptionBl:[null, Validators.required],
          codeCommande:[null],
          enteteCommandeAchatGroupe:[null, Validators.required],
         })
    }

    initBLFilterForm() {
      this.BLFilterForm = this.fb.group({
        enteteCommandeAchatGroupe: [''],
        offreur: [''],
        distributeur: ['']
      });
    }

    listenForRemiseChange(){
      if(this.isConsult){
        return;
      }
      this.saisieBLForm.get('tauxRf').valueChanges.subscribe(remise => {
        this.gridData.data.forEach((item: DetailBlAchatGroupeDTO & {isEditedFromGlobal:boolean}) => {
         if(item.tauxRf && !item.isEditedFromGlobal){
          return;
         }

         item.tauxRf = remise;
         item.pphRemise = parseFloat((Math.round(item.pph * (1 - (remise / 100)) * 100) / 100).toFixed(2));
         item.isEditedFromGlobal = true;

         this.generateSynthese(this.gridData.data);
        })
      })
    }

    clearCommande(commandeInput: HTMLInputElement=null,confirmed =true){
      if(!confirmed){
        this.processClearCommande(commandeInput);
        this.router.navigate(['/achats-groupes/bons-livraison/edit']);
        return;
      }
      this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir effacer la commande ?`).then(
        () => {
          this.processClearCommande(commandeInput);
        }
      ).catch(() => {});

    }

    private processClearCommande(commandeInput: HTMLInputElement=null,){
      this.saisieBLForm.get('enteteCommandeAchatGroupe').patchValue(null);
          this.saisieBLForm.get('enteteCommandeAchatGroupe').enable();
          this.saisieBLForm.get('fournisseur').enable();
          this.saisieBLForm.get('fournisseur').patchValue(null);
          this.saisieBLForm.get('montantSaisi').patchValue(0);
          this.saisieBLForm.get('tauxRf').patchValue(0);
          this.products = [];
          this.gridData.data = [];
          if(commandeInput){
            commandeInput?.focus();
            //  clear error message
            this.formSubmitted = false;
          }
    }


    selectInput(event): void {
      event.target?.select();
    }

    onLivreeChanged(field: string, event: any, dataItem: DetailBlAchatGroupeDTO & {isEditedFromGlobal:boolean}): void {
        const value = event.target.value;

        if (field === 'tauxRf' ) {
          let remise = dataItem.tauxRf;

          if (isNaN(remise)) {
            remise = 0;
            dataItem.tauxRf = 0;
          }

          if(dataItem?.isEditedFromGlobal){

            dataItem.isEditedFromGlobal = false;
          }

          dataItem.pphRemise = parseFloat((Math.round(dataItem.pph * (1 - (remise / 100)) * 100) / 100).toFixed(2));





        }


        if (field === 'pphRemise') {
          const remise = Math.round((1-(dataItem.pphRemise/dataItem.pph))*100);
          dataItem.tauxRf = remise;
          // event.target?.classList.add('is-invalid');


        }
        if(field === 'quantiteLivree' && dataItem.quantiteLivree  == 0){
          dataItem.quantiteLivree =0;
          event.target['value'] = 0;
        }
        if(field === 'quantiteUg' && dataItem.quantiteUg  == 0){
          dataItem.quantiteUg =0;
          event.target['value'] = 0;
        }

      this.generateSynthese(this.gridData.data);

    }

  private SelectTheInput(event: any) {
    setTimeout(() => {
      event.target?.select();
    }, 100);
  }

    back() {
        if(this.isIndividuelle){
          this.router.navigateByUrl(`/achats-groupes/bons-livraison/liste/individuelle`);
          return;
        }
        this.route.queryParams.subscribe(params => {
          if(params['from'] === 'dispatch' && params['blc']){
            this.router.navigateByUrl(`/achats-groupes/bons-livraison/edit/${params['blc']}/dispatch?action=bls-envoyes`);
          }
          else if(params['from'] === 'ls-sortie'){
            this.router.navigateByUrl(`/achats-groupes/bons-livraison/mes-bons`);
          }
          else{

            this.router.navigate(['/achats-groupes/bons-livraison/liste']);
          }
        })
    }

    addOriginalCommande(BL: EnteteBlConsolideeMarcheDTO,validate:boolean = false) {
      // if(validate){
      //   this.saveBl(BL,validate);
      //   return;
      // }
      const idCommande = this.idCommande ?? this.saisieBLForm.get('enteteCommandeAchatGroupe').value.id
      if(!idCommande){
        this.alertService.error("Merci de selectionner une commande", 'MODAL')
        return
      }
        BL.enteteCommandeAchatGroupe = this.isIndividuelle ?  undefined : this.saisieBLForm.get('enteteCommandeAchatGroupe').value;
        this.saveBl(BL,validate);
    }


    validateLinges(lignes: DetailBlAchatGroupeDTO[]): boolean {
      // verify all quantite livre not = 0 and not pass the commanded one
      // for (const ligne of lignes) {
      //   if (ligne.quantiteLivree > ligne.quantiteCommandee) {
      //     this.alertService.error(`La quantité livrée de ${ligne.designation} est supérieure à la quantité commande`, 'MODAL');
      //     return false;
      //   }
      //   if (ligne.quantiteLivree > ligne.qteReliquatBl) {
      //     this.alertService.error(`La quantité livrée de ${ligne.designation} est supérieure à la quantité qui reste non recupérable`, 'MODAL');
      //     return false;
      //   }

      //   //  all qteLivree is 0 must one by setted not all have 0
      // }
      const allHaveZero = lignes.every(ligne => ligne.quantiteLivree === 0);
      if (allHaveZero) {
          this.alertService.error(`Aucune quantité livrée n'est définie`, 'MODAL');
          return false;
      }
      return true;
    }


    generateSynthese(lignes:DetailBlAchatGroupeDTO[]){
      const field ={
        qLivree:0,
        mntBrut:0,
        mntNet:0,
        mntRemise:0,
      }
      field.qLivree = lignes.reduce((acc,curr)=>{
        return acc + +curr.quantiteLivree;
      },0);

      field.mntBrut = lignes.reduce((acc,curr)=>{
        return acc + (curr.pph * +curr.quantiteLivree);
      },0);


      field.mntNet = lignes.reduce((acc,curr)=>{
        return acc +  ((curr.pphRemise ?? curr.pph) * +curr.quantiteLivree);
      },0);


      field.mntRemise = lignes.reduce((acc,curr)=>{
        return acc +  (curr.pph * +curr.quantiteLivree) - ((curr.pphRemise ?? curr.pph) * +curr.quantiteLivree);
      },0);



        this.saisieBLForm.patchValue({cumulBl: parseFloat(field.mntNet.toFixed(2))});


      this.synthese = field;
    }


    calcualtePPHRemise2(pph:number,remise:number){
      return parseFloat((Math.round(pph * (1 - (remise / 100)) * 100) / 100).toFixed(2));
    }

    calculatePPHRemise(lignes:DetailBlAchatGroupeDTO[]){
      lignes.forEach(ligne => {
        let remise = ligne.tauxRf;
        if (isNaN(remise)) {
          remise = 0;
          ligne.tauxRf = 0;
        }

        ligne.pphRemise = parseFloat((Math.round(ligne.pph * (1 - (remise / 100)) * 100) / 100).toFixed(2));
      });
    }



    processSaveBl(validate:boolean = false) {


      if(this.saisieBLForm.invalid){
        this.formSubmitted = true;
        this.alertService.error("Merci de remplir touts les champs Obligatoire !", 'MODAL')
        return
      }
      if(this.enteteBl && this.enteteBl.etatBl != 'BROUILLON'){
        this.formSubmitted = true;
        this.alertService.error("Le BL est déjà validé", 'MODAL')
        return
      }
      const BLConsolideModel = this.isIndividuelle ? this.formToModelMapperIndividuel() : this.formToModelMapper() ;
      if(!this.validateLinges(BLConsolideModel.lignes as DetailBlAchatGroupeDTO[])){
        this.formSubmitted = true;
        return
      }


      if(isNaN(BLConsolideModel.montantSaisi) || BLConsolideModel.montantSaisi == 0){
        this.formSubmitted = true;
        this.alertService.error("Le montant de BL saisi est invalide, merci de le corriger !", 'MODAL')
        return
      }

      // if(BLConsolideModel.montantSaisi !== this.synthese.mntNet){
      //   this.formSubmitted = true;
      //   this.alertService.error("Attention, le montant saisi ne correspond pas au montant calculé, merci de le corriger !", 'MODAL')
      //   return
      // }
      this.addOriginalCommande(BLConsolideModel as EnteteBlConsolideeMarcheDTO,validate);
     }



    validateBlIndividuel() {
     const  BLConsolideModel = this.formToModelMapperIndividuel();

        if(this.saisieBLForm.invalid){
          this.formSubmitted = true;
          this.alertService.error("Merci de remplir touts les champs Obligatoire !", 'MODAL')
          return
        }
        if(this.enteteBl && this.enteteBl.etatBl != 'BROUILLON'){
          this.formSubmitted = true;
          this.alertService.error("Le BL est déjà validé", 'MODAL')
          return
        }
         if(!this.validateLinges(BLConsolideModel.lignes as DetailBlAchatGroupeDTO[])){
          this.formSubmitted = true;
          return
        }

        if(isNaN(BLConsolideModel.montantSaisi) || BLConsolideModel.montantSaisi == 0){
          this.formSubmitted = true;
          this.alertService.error("Le montant de BL saisi est invalide, merci de le corriger !", 'MODAL')
          return
        }

       this.userInputService.confirmAlt('Confirmation', `Voulez-vous vraiment Valider Le BL-${BLConsolideModel.numeroBl} `)
        .then(
          () => {
            this.fsBLService.saveBlIndividuel(BLConsolideModel as EnteteBlIndividuel).subscribe(res => {
            this.fsBLService.validateBLIndividuel({blIndividuelId: res.id}).subscribe(vres => {
              this.alertService.successAlt(`Le BL ${res.numeroBl} a été validé avec succès.`, 'Validation de BL', 'MODAL');
              this.back();
            });
          });
          },
          () => null
        )

    }


    deleteInsertedLine(line: DetailBlAchatGroupeDTO) {
      this.gridData.data = this.gridData.data.filter(l => l !== line);
      this.generateSynthese(this.gridData.data);
    }


    async saveBl(BLConsolideModel: EnteteBlConsolideeMarcheDTO | EnteteBlIndividuel,validate:boolean = false) {

      console.log("BLConsolideModel",BLConsolideModel);

      // return;
      if(this.isIndividuelle){
        this.fsBLService.saveBlIndividuel(BLConsolideModel as EnteteBlIndividuel).subscribe(res => {
          this.enteteBl = res;
          this.alertService.successAlt(`Le BL ${this.enteteBl.numeroBl} a été enregistré avec succès.`, 'Enregistrement de BL', 'MODAL');
          this.back()
        });
        return;
      }

    const groupe= await this.fsService.getMyGroupe()
    const groupeId = groupe.id;

    if(validate){
     try {
      const confirmValidation = await  this.userInputService.confirmAlt('Confirmation', `Voulez-vous vraiment Valider Le BL-${BLConsolideModel.numeroBl} `)

      if(confirmValidation){
       this.fsBLService.saveBlConsolide(BLConsolideModel as EnteteBlConsolideeMarcheDTO,{groupeId }).subscribe(res => {
         this.enteteBl = res;
         this.saisieBLForm.patchValue({
           id: res.id,
         });
         this.validateBl(false);
       });
      }
     } catch (error) {
      console.warn(error)

     }

    }else{
      this.fsBLService.saveBlConsolide(BLConsolideModel as EnteteBlConsolideeMarcheDTO,{groupeId }).subscribe(res => {
        this.enteteBl = res;
        this.saisieBLForm.patchValue({
          id: res.id,
        });
        this.alertService.successAlt(`Le BL ${this.enteteBl.numeroBl} a été enregistré avec succès!`, 'Enregistrement du BL', 'MODAL');
        this.back();
      });
    }

    }

    async validateBl(shouldConfirm:boolean = true) {


      const groupe= await this.fsService.getMyGroupe()
      const  groupeId = groupe.id;

      if(shouldConfirm){
          const validation = await this.userInputService.confirmAlt('Confirmation', `Voulez-vous vraiment Valider Le BL-${this.enteteBl.numeroBl} `);
          if(!validation){
            return;
          }

      }

      this.fsBLService.validateBLConsolide({blConsolideId :this.enteteBl.id,groupeId}).subscribe(res => {
        this.back();
        this.alertService.successAlt(`Le BL-${this.enteteBl.numeroBl} a été validé avec succès!`, 'Validation du BL', 'MODAL');
      });
     }

    async AnullerBl() {
      const groupe= await this.fsService.getMyGroupe()
      const groupeId = groupe.id;

      this.userInputService
      .confirmAlt('Confirmation', `Voulez-vous vraiment Supprimer Le BL ${this.enteteBl.numeroBl} `)
      .then((confirmed) => {
        if (confirmed) {
          this.fsBLService.anuulerBLConsolide({blConsolideId:`${this.enteteBl.id}`,groupeId}).subscribe(res=>{
            this.back();
            this.alertService.successAlt(`Le BL ${this.enteteBl.numeroBl}  a été supprimé avec succès!`, 'Suppression de  BL', 'MODAL');
          })
        }
      });
    }

    AnullerBlIndividuel() {

      this.userInputService
      .confirmAlt('Confirmation', `Voulez-vous vraiment Supprimer Le BL   ${this.enteteBl.numeroBl}  `)
      .then((confirmed) => {
        if (confirmed) {
          this.fsBLService.anuulerBLIndividuel({blIndividuelId:`${this.enteteBl.id}`}).subscribe(res=>{
            this.alertService.successAlt(`Le BL  ${this.enteteBl.numeroBl}  a été supprimé avec succès!`, 'Suppression de  BL', 'MODAL');
            this.back();
          })
         }
      });
    }

    consulterCommande() {

      if(this.isUnitaire){

        this.router.navigate(
          ['achats-groupes/commandes/edit/cmd-unitaire/',this.enteteBl?.enteteCommandeAchatGroupe?.id ],
           {queryParams: {readOnly: true,from: 'bl-unitaire',ref: this.enteteBl?.id}}
          );

      }else if(this.isIndividuelle){
        const id = (this.enteteBl as EnteteBlIndividuel)?.enteteCommandeIndividuelleDTO?.id ?? this.saisieBLForm.get('enteteCommandeAchatGroupe')?.value?.id
        this.router.navigate(
          ['/achats-groupes/commandes/edit/cmd-individuelle',id ],
           {queryParams: {readOnly: true,from: 'bl-individuel',ref: this.enteteBl?.id}}
          );
      }

      else{
        const id = this.enteteBl?.enteteCommandeAchatGroupe?.id ?? this.saisieBLForm.get('enteteCommandeAchatGroupe')?.value?.id
        const offreId = this.enteteBl?.enteteCommandeAchatGroupe?.offre?.id ?? this.saisieBLForm.get('enteteCommandeAchatGroupe')?.value?.offre?.id
        if(!this.enteteBl){
          window.open(this.router.createUrlTree(
            ['/achats-groupes/commandes/edit/cmd-groupe/', id],
            { queryParams: { readOnly: true, from: 'bl', ref: this.enteteBl?.id, offreId } }
          ).toString(), '_blank');
          return;
        }
        this.router.navigate(
          ['/achats-groupes/commandes/edit/cmd-groupe/',id],
           {queryParams: {readOnly: true,from: 'bl',ref: this.enteteBl?.id,offreId}}
        );
      }
    }

    commandeSelected(event:NgbTypeaheadSelectItemEvent<EnteteCommandeConsolideeMarche>) {

      const products : DetailBlAchatGroupeDTO[] = [];
      this.saisieBLForm.get('tauxRf').setValue(event.item.tauxRf ?? 0);
      event.item.listeDetails.forEach((ligne) => {
          products.push(
            new DetailBlAchatGroupeDTO({
              id:null,
              produit:ligne,
              pph:ligne.prixVenteHt,
              ppv:ligne.ppv,
              quantiteCommandee:ligne.qteCmd,
              quantiteLivree:0,
              quantiteUg:0,
              designation:ligne.libelleProduit,
              quantiteUgCmd:ligne.qteUg ?? 0,
              blocOffreId:ligne.blocOffre.id,
              tauxRf:ligne.tauxRemise ?? 0,
              isCadeau:ligne.isCadeau,
              qteReliquatBl:ligne.qteReliquatBl,
              enteteBl: {etatBl:'BROUILLON'} as EnteteBlAchatGroupeDTO,
            })
          )

      });
      this.gridData.data = products;
      this.products = products;

      this.saisieBLForm.patchValue({
        fournisseur: event.item.distributeur,
        dateReceptionBl: moment(),
        transporteur: event.item.transporteur,
        raisonSocialeTransporteur: event.item.raisonSocialeTransporteur,
      });
      this.saisieBLForm.get('enteteCommandeAchatGroupe').disable();
      this.saisieBLForm.get('fournisseur').disable();
      this.generateSynthese(this.gridData.data);
      this.calculatePPHRemise(this.gridData.data);


    }

    getBLUnitaireById() {
      const groupeId = this.authService.getPrincipal().groupe?.id;
      this.isLoading = true;
      this.fsBLService.getBLUnitaireById({blUnitaireId : this.idBL}).subscribe({
        next: (res) =>{
          this.enteteBl = res;
          this.isConsult = this.enteteBl.etatBl !== 'BROUILLON';
          this.isConsult && this.saisieBLForm.disable();

          this.saisieBLForm.patchValue({
            enteteCommandeAchatGroupe: res.enteteBlConsolideeMarche,
            montantSaisi: res.montantSaisi,
            fournisseur: res.fournisseur,
            tauxRf: res.tauxRf,
            transporteur: res.transporteur,
            codeCommande: res.codeCommande,
            numeroBl: res.numeroBl,
            cumulBl: res.montantCalcule ?? 0,


            dateReceptionBl:moment(res.dateReceptionBl).isValid() ? moment(res.dateReceptionBl) :null,
          });

          if (res?.lignes?.length) {
            this.isCoffretEnabled = !!res?.lignes[0]?.qteFixePrdInCoffret;
          }

          const lignes = res.lignes.map((ligne) => {
            return this.buildGridDataFromBL({
              id:ligne.id,
              produit:ligne.produit,
              pph:ligne.pph,
              ppv:ligne.ppv,
              quantiteCommandee:ligne.quantiteCommandee,
              quantiteLivree:ligne.quantiteLivree,
              quantiteUg:0,
              designation:ligne.designation,
              quantiteUgCmd:ligne.quantiteUgCmd ?? 0,
              blocOffreId:ligne.blocOffreId,
              pphRemise:ligne.pphRemise,
              tauxRf:ligne.tauxRf,
              montant:ligne.montant,
              isCadeau:ligne.isCadeau,
              qteFixePrdInCoffret: ligne?.qteFixePrdInCoffret,
              enteteBl:ligne.enteteBl,
            })
          })

          this.gridData.data =lignes;
          this.products =lignes
          this.generateSynthese(this.gridData.data);

          },
        complete: () => {
          this.isLoading = false;
          this.pushMobileMenuOptions();
        }
      });
    }

    getQuantiteReliquatByBlockOffreID(blockOffreId:number){

      if(this.isIndividuelle){
        for (const ligne   of (this.enteteBl as EnteteBlIndividuel).enteteCommandeIndividuelleDTO.listeDetails) {
           if(ligne['blocOffre']['id'] === blockOffreId){
             return ligne['qteReliquatBl'];
          }
        }
        return 0;
      }else{
        for (const ligne of this.enteteBl.enteteCommandeAchatGroupe.listeDetails) {
          if(ligne.blocOffre.id === blockOffreId){
            return ligne.qteReliquatBl;
          }
        }
      }
      return 0;

    }

    getBLConsolideById() {
      const groupeId = this.authService.getPrincipal().groupe?.id;
      this.isLoading = true;
      this.fsBLService.getBLConsolideById({
        blConsolideId: this.idBL,
        groupeId
      }).subscribe({
        next: (res) =>{

          this.enteteBl = res;

          this.saisieBLForm.patchValue({
            id: res.id,
            fournisseur: res.fournisseur,
            numeroBl: res.numeroBl,
            cumulBl: res.montantCalcule,
            montantSaisi: res.montantSaisi,
            tauxRf: res.tauxRf,
            transporteur: res.transporteur,
            raisonSocialeTransporteur: res.raisonSocialeTransporteur,
            codeCommande: res.codeCommande,
            enteteCommandeAchatGroupe: res.enteteCommandeAchatGroupe,
            dateReceptionBl:moment(res.dateReceptionBl).isValid() ? moment(res.dateReceptionBl) :null,
          });

          if (res?.lignes?.length) {
            this.isCoffretEnabled = !!res?.lignes[0]?.qteFixePrdInCoffret;
          }

          const lignes = res.lignes.map((ligne) => {
            return this.buildGridDataFromBL({
              id:ligne.id,
              produit:ligne.produit,
              pph:ligne.pph,
              ppv:ligne.ppv,
              quantiteCommandee:ligne.quantiteCommandee,
              quantiteLivree:ligne.quantiteLivree,
              quantiteUg:ligne.quantiteUg,
              designation:ligne.designation,
              quantiteUgCmd:ligne.quantiteUgCmd ?? 0,
              blocOffreId:ligne.blocOffreId,
              tauxRf:ligne.tauxRf,
              pphRemise:ligne.pphRemise,
              isCadeau:ligne.isCadeau,
              qteFixePrdInCoffret: ligne?.qteFixePrdInCoffret,
              qteReliquatBl:this.getQuantiteReliquatByBlockOffreID(ligne.blocOffreId),
              enteteBl:res,
              oldPphRemise:ligne.oldPphRemise,
              oldTauxRf:ligne.oldTauxRf,
              isExtraLine:ligne.isExtraLine || false,
            })
          })

          this.gridData.data =lignes;
          this.products =lignes
           this.generateSynthese(this.enteteBl?.lignes as DetailBlAchatGroupeDTO[]);

          this.isConsult = (this.enteteBl.etatBl === 'BROUILLON' && this.isReadOnly) ||  this.isReadOnly;

          //  another case for the command is valided or reparti should alwas be readonly
          if(this.enteteBl.etatBl === 'VALIDE' || this.enteteBl.etatBl === 'REPARTI'){
            this.saisieBLForm.disable();
            this.isConsult = true;
          }

          this.isConsult && this.saisieBLForm.disable();

          this.isLoading = false;


        },
        complete: () => {
          this.pushMobileMenuOptions();
        }
      });
    }
    getBLIndividuelById() {
       this.isLoading = true;
      this.fsBLService.getBLIndividuelById({
         blIndividuelId: this.idBL,
       }).subscribe({
        next: (res) =>{

          this.enteteBl = res;

          this.saisieBLForm.patchValue({
            id: res.id,
            fournisseur: res.fournisseur,
            numeroBl: res.numeroBl,
            cumulBl: res.montantCalcule,
            montantSaisi: res.montantSaisi,
            tauxRf: res.tauxRf,
            transporteur: res.transporteur,
            raisonSocialeTransporteur: res.raisonSocialeTransporteur,
            codeCommande: res.codeCommande,
            enteteCommandeAchatGroupe: res.enteteCommandeIndividuelleDTO,
            dateReceptionBl:moment(res.dateReceptionBl).isValid() ? moment(res.dateReceptionBl) :null,
          });

          if (res?.lignes?.length) {
            this.isCoffretEnabled = !!res?.lignes[0]?.qteFixePrdInCoffret;
          }

          const lignes = res.lignes.map((ligne) => {
            return this.buildGridDataFromBL({
              id:ligne.id,
              produit:ligne.produit,
              pph:ligne.pph,
              ppv:ligne.ppv,
              quantiteCommandee:ligne.quantiteCommandee,
              quantiteLivree:ligne.quantiteLivree,
              quantiteUg:ligne.quantiteUg,
              designation:ligne.designation,
              quantiteUgCmd:ligne.quantiteUgCmd ?? 0,
              blocOffreId:ligne.blocOffreId,
              tauxRf:ligne.tauxRf,
              pphRemise:ligne.pphRemise,
              isCadeau:ligne.isCadeau,
              qteFixePrdInCoffret: ligne?.qteFixePrdInCoffret,
              qteReliquatBl:this.getQuantiteReliquatByBlockOffreID(ligne.blocOffreId),
              enteteBl:res,
              oldPphRemise:ligne.oldPphRemise,
              oldTauxRf:ligne.oldTauxRf,
              isExtraLine:ligne.isExtraLine || false,
              montantOriginal:ligne.montantOriginal,
            })
          })

          this.gridData.data =lignes;
          this.products =lignes
           this.generateSynthese(this.enteteBl?.lignes as DetailBlAchatGroupeDTO[]);

          this.isConsult = (this.enteteBl.etatBl === 'BROUILLON' && this.isReadOnly) ||  this.isReadOnly;

          //  another case for the command is valided or reparti should alwas be readonly
          if(this.enteteBl.etatBl === 'VALIDE' || this.enteteBl.etatBl === 'REPARTI'){
            this.saisieBLForm.disable();
            this.isConsult = true;
          }

          this.isConsult && this.saisieBLForm.disable();

          this.isLoading = false;


        },
        complete: () => {
          this.pushMobileMenuOptions();
        }
      });
    }


    buildGridDataFromBL(BLDto:Partial<DetailBlAchatGroupeDTO>){

      return new DetailBlAchatGroupeDTO({
        id:BLDto.id,
        produit:BLDto.produit,
        pph:BLDto.pph,
        ppv:BLDto.ppv,
        quantiteCommandee:BLDto.quantiteCommandee,
        quantiteLivree:BLDto.quantiteLivree,
        quantiteUg:BLDto.quantiteUg,
        designation:BLDto.designation,
        quantiteUgCmd:BLDto.quantiteUgCmd ?? 0,
        blocOffreId:BLDto.blocOffreId,
        tauxRf:BLDto.tauxRf,
        pphRemise:BLDto.pphRemise,
        montant:BLDto.montant,
        isCadeau:BLDto.isCadeau,
        qteFixePrdInCoffret:BLDto.qteFixePrdInCoffret,
        qteReliquatBl:BLDto.qteReliquatBl,
        enteteBl:BLDto.enteteBl,
        oldPphRemise:BLDto.oldPphRemise,
        oldTauxRf:BLDto.oldTauxRf,
        isExtraLine:BLDto.isExtraLine || false,
        montantOriginal:BLDto.montantOriginal,
      })
    }


    buildGridDataFromBLIndividuel(BLDto:Partial<DetailBlIndividuelDTO>){

      return new DetailBlIndividuelDTO({
        id:BLDto.id,
        produit:BLDto.produit,
        pph:BLDto.pph,
        ppv:BLDto.ppv,
        quantiteCommandee:BLDto.quantiteCommandee,
        quantiteLivree:BLDto.quantiteLivree,
        quantiteUg:BLDto.quantiteUg,
        designation:BLDto.designation,
        quantiteUgCmd:BLDto.quantiteUgCmd ?? 0,
        blocOffreId:BLDto.blocOffreId,
        tauxRf:BLDto.tauxRf,
        pphRemise:BLDto.pphRemise,
        montant:BLDto.montant,
        isCadeau:BLDto.isCadeau,
        qteFixePrdInCoffret:BLDto.qteFixePrdInCoffret,
        qteReliquatBl:BLDto.qteReliquatBl,
        enteteBl:BLDto.enteteBl,
        oldPphRemise:BLDto.oldPphRemise,
        oldTauxRf:BLDto.oldTauxRf,
        isExtraLine:BLDto.isExtraLine || false,
        montantOriginal:BLDto.montantOriginal,
      })
    }


    Imprimer(){
      this.fsBLService.imprimerBLUnitaire({blUnitaireId : `${this.enteteBl.id}`}).subscribe(res =>{
        this.blobUrl = res;
      })

    }

    searchForCommande: OperatorFunction<string, readonly Offre[]> =  (
      text$: Observable<string>
    ) =>{
      return text$.pipe(
        debounceTime(200),
        distinctUntilChanged(),
        switchMap((term) => {
          return term?.length < 1
            ? of([])
            : this.commandesService
                .searchCommandesConsolideeProducts(
                  { skip: 0, pageSize: 10 },
                  { codeCommande: term ,
                    groupeEntreprise: this.isIndividuelle ? undefined : this.authService.getPrincipal().groupe,
                    supporterEntreprise:this.isIndividuelle ? undefined :  this.authService.getPrincipal().societe,
                    etatCommande: ["ENVOYEE","EN_LIVRAISON"]
                  }
                )
                .pipe(
                  switchMap((response) => {
                    if(response.content.length === 0){
                      this.alertService.error("Aucun résultat trouvé", "MODAL");
                    }
                    return of(response.content);
                  })
                );
        })
      );
    }

    searchForCommandeIndividuel: OperatorFunction<string, readonly Offre[]> = (
      text$: Observable<string>
    ) =>{
      return text$.pipe(
        debounceTime(200),
        distinctUntilChanged(),
        switchMap((term) => {
          return term?.length < 1
            ? of([])
            : this.offreService
                .searchCommandesDto(

                  { codeCommande: term ,
                    client: this.authService.getPrincipal()?.societe,
                  },
                  { skip: 0, pageSize: 10 },
                )
                .pipe(
                  switchMap((response) => {
                    if(response.content.length === 0){
                      this.alertService.error("Aucun résultat trouvé", "MODAL");
                    }
                    return of(response.content);
                  })
                );
        })
      );
    }
    searchForBl: OperatorFunction<string, readonly any[]> = (text$: Observable<string>) => {
      return text$.pipe(
        debounceTime(200),
        distinctUntilChanged(),
        switchMap((term) => {
          return term?.length < 1
            ? of([])
            : this.commandesService.searchCommandesConsolideeProducts(
                { skip: 0, pageSize: 10 },
                {
                  codeCommande: term,
                  groupeEntreprise: this.authService.getPrincipal().groupe,
                  supporterEntreprise: this.authService.getPrincipal().societe,
                  etatCommande: ["ENVOYEE", "EN_LIVRAISON"]
                }
              ).pipe(
                switchMap((response) => of(response.content))
              );
        })
      );
    };

    applyFilters() {
      const filterValues = this.advFilterForm.value;

      if(this.isIndividuelle){
        this.offreService
        .searchCommandesDto(

          { ...filterValues,
            client: this.authService.getPrincipal()?.societe,
            statut:["E","EL"]
          },
          { skip: 0, pageSize: 10 },
        ).subscribe({
          next: (res) => {
            this.searchResults = res?.content || [];
            if(this.searchResults.length === 0){
              this.alertService.error("Aucun résultat trouvé", "MODAL");
            }
           },
          error: () => {
            this.searchResults = [];
           }
        })
        return;
      }



      this.commandesService.searchCommandesConsolideeProducts(
        { skip: 0, pageSize: 10 },
        {
          codeCommande: filterValues.codeCommande,
          groupeEntreprise: this.authService.getPrincipal().groupe,
          supporterEntreprise: this.authService.getPrincipal().societe,
          etatCommande: ["ENVOYEE", "EN_LIVRAISON"],
          offreurId: this.advFilterForm.value.offreur?.id,
          distributeurId: this.BLFilterForm.value.distributeur?.id,
          dateCreationDebut: this.advFilterForm.value.dateCreationDebut,
          dateCreationFin: this.advFilterForm.value.dateCreationDebut,

        }
      ).subscribe({
        next: (res) => {
          this.searchResults = res?.content || [];
          if(this.searchResults.length === 0){
            this.alertService.error("Aucun résultat trouvé", "MODAL");
          }
         },
        error: () => {
          this.searchResults = [];
         }
      });
    }


    formatter = (result: Fournisseur) => result?.raisonSociale;
    formatter2 = (result: Fournisseur) => result?.raisonSociale;

    commandFormatter  = (result: EnteteCommandeConsolideeMarche) => `${result?.codeCommande} `;
    commandFormatterIndividuel  = (result: any) => `${result?.codeCommande}`;
    offreurFormatter = (result: any) => result?.offreur?.raisonSociale || result.raisonSociale || result;
    distributeurFormatter = (result: any) => result?.distributeur?.raisonSociale || result.raisonSociale || result;

    choseRowClass(row:RowClassArgs) {
      if(row.dataItem.isCadeau) {
        return 'cadeau-row';
      }
      if(row.dataItem.isExtraLine) {
        return 'extra-line-row';
      }

      // if (!this.isLoading &&  row.dataItem?.quantiteLivree > row.dataItem?.qteReliquatBl && (row.dataItem?.enteteBl?.etatBl === 'BROUILLON')){
      //   return  'line-invalid';
      // }else{
       return row.dataItem?.quantiteLivree <= row.dataItem?.qteReliquatBl && row.dataItem?.enteteBl?.etatBl === 'BROUILLON' ? 'line-valid':'' ;
      // }
    }
    hasUnsavedData(): boolean {
      return true;
    }


      searchFournisseur: OperatorFunction<string, readonly Offre[]> =  (
        text$: Observable<string>
      ) =>{
        return text$.pipe(
          debounceTime(200),
          distinctUntilChanged(),
          switchMap((term) => {
            return term?.length < 1
              ? of([])
              : this.offreService
                  .searchSociete(
                    { raisonSociale: term,typeEntreprises:["FABRIQUANT","GROSSISTE","TRANSPORTEUR"]},
                    { skip: 0, pageSize: 15 },
                  )
                  .pipe(
                    switchMap((response) => {
                      if(response?.content?.length === 0){
                        this.alertService.error("Aucun résultat trouvé", "MODAL");
                      }
                      return of(response.content);
                    })
                  );
          })
        );
      }

      searchForOffreur: OperatorFunction<string, readonly any[]> = (text$: Observable<string>) => {
        return text$.pipe(
          debounceTime(200),
          distinctUntilChanged(),
          switchMap((term) => {
            return term?.length < 1
              ? of([])
              : this.offreService.searchSociete(
                  {
                    raisonSociale: term,
                    typeEntreprises: ['FABRIQUANT', 'GROSSISTE'],
                  },
                  { skip: 0, pageSize: 10 },
                ).pipe(
                  switchMap((response) => {
                    if(response?.content?.length === 0){
                      this.alertService.error("Aucun résultat trouvé", "MODAL");
                    }

                    return of(response.content)
                  })
                );
          })
        );
      };

      searchForDistributeur: OperatorFunction<string, readonly any[]> = (text$: Observable<string>) => {
        return text$.pipe(
          debounceTime(200),
          distinctUntilChanged(),
          switchMap((term) => {
            return term?.length < 1
              ? of([])
              : this.commandesService.searchCommandesConsolideeProducts(
                  { skip: 0, pageSize: 10 },
                  {
                    offre: { distributeur: { raisonSociale: term } },
                    groupeEntreprise: this.authService.getPrincipal().groupe,
                    supporterEntreprise: this.authService.getPrincipal().societe,
                    etatCommande: ["ENVOYEE", "EN_LIVRAISON"]
                  } as FsCommandeCriteria
                ).pipe(
                  switchMap((response) => of(response.content))
                );
          })
        );
      };



      onFilterBLProduit(filter:string){
        const changeWord = filter.trim().toLowerCase();
        const filtredData = filterBy(  this.products ,{
          logic:"or",
          filters:[
              { field:"designation", operator:'contains',value:changeWord,ignoreCase: true},

              ]
        });

        console.log("🚀 ~ EditBonLivraisonComponent ~ onFilterBLProduit ~ filtredData:", filtredData)

       this.gridData.data = filtredData;
      }


       scrollToNewlyAddedItems() {
      try {
        // Use setTimeout to ensure DOM has updated with new items before scrolling
        setTimeout(() => {
          // Try multiple possible selectors for Kendo grid content
          const gridContainer = document.querySelector('.k-grid-content') || 
                                document.querySelector('.fs-grid .k-grid-content');
          
          if (gridContainer) {
            // Scroll to bottom of the grid
            gridContainer.scrollTop = gridContainer.scrollHeight;
            
            // As a fallback, try to scroll the last row into view
            const lastRow = document.querySelector('.k-grid tbody tr:last-child');
            if (lastRow && !gridContainer.scrollTop) {
              lastRow.scrollIntoView({ behavior: 'smooth', block: 'end' });
            }
          } else {
            console.warn('Grid container not found for scrolling');
          }
        }, 100); // Small delay to ensure DOM update is complete
      } catch (error) {
        console.warn('Could not scroll to newly added items:', error);
      }
    }


      formToModelMapper() {

        const qteTotaleLivree = this.gridData.data.reduce((a, b) => +a + (+b.quantiteLivree), 0);

        const quantiteUgLivree = this.gridData.data.reduce((a, b) => +a + (+b.quantiteUg), 0);


        const BLConsolideMarche = new EnteteBlConsolideeMarcheDTO({
          fournisseur: this.saisieBLForm.get('fournisseur').value,
          numeroBl: this.saisieBLForm.get('numeroBl').value,
          cumulBl: this.saisieBLForm.get('cumulBl').value,
          montantSaisi: this.saisieBLForm.get('montantSaisi').value,
          tauxRf: this.saisieBLForm.get('tauxRf').value,
          transporteur: this.saisieBLForm.get('transporteur').value,
          raisonSocialeTransporteur: this.saisieBLForm.get('raisonSocialeTransporteur').value,
          codeCommande: this.saisieBLForm.get('enteteCommandeAchatGroupe').value.codeCommande,
          enteteCommandeAchatGroupe: this.saisieBLForm.get('enteteCommandeAchatGroupe').value,
          id:this.saisieBLForm.get('id').value,
          dateReceptionBl: moment(this.saisieBLForm.get('dateReceptionBl').value).format('YYYY-MM-DD HH:mm:ss'),
          qteTotaleLivree: qteTotaleLivree,
          qteTotaleLivreeOriginal: qteTotaleLivree,
          // parse round to decimal value
          montantCalcule: parseFloat(this.synthese.mntNet.toFixed(2)),
          montantCalculeOriginal: parseFloat(this.synthese.mntNet.toFixed(2)),
          quantiteUg:quantiteUgLivree,
          etatBl:EtatBlAchatGroupeEnum.BROUILLON
        });
        BLConsolideMarche.lignes =  this.gridData.data.map((ligne:DetailBlAchatGroupeDTO) => {
          const currentMontant = ligne.pphRemise ?? ligne.pph;
          return new DetailBlAchatGroupeDTO({
            id:ligne.id,
            designation: ligne.designation,
            quantiteCommandee: ligne.quantiteCommandee,
            quantiteLivree: ligne.quantiteLivree ?? 0,
            pph: ligne.pph,
            ppv: ligne.ppv,
            quantiteUg: ligne.quantiteUg,
            produit:ligne.produit,
            quantiteUgCmd:ligne.quantiteUgCmd,
            blocOffreId:ligne.blocOffreId,
            tauxRf:ligne.tauxRf,
            pphRemise:ligne.pphRemise,
            montant: currentMontant * ligne.quantiteLivree,
            montantOriginal: currentMontant * ligne.quantiteLivree,
            isCadeau: ligne.isCadeau,
            qteFixePrdInCoffret: ligne.qteFixePrdInCoffret,
            qteReliquatBl: ligne.qteReliquatBl,
            oldPphRemise:ligne.oldPphRemise,
            oldTauxRf:ligne.oldTauxRf,
            isExtraLine:ligne.isExtraLine || false,
            quantiteLivreeOriginal: ligne.quantiteLivree ?? 0,
            
            // datePeremption: ligne.dateper,
          });
        });

        return BLConsolideMarche;

      }

      formToModelMapperIndividuel() {

        const qteTotaleLivree = this.gridData.data.reduce((a, b) => +a + (+b.quantiteLivree), 0);

        const quantiteUgLivree = this.gridData.data.reduce((a, b) => +a + (+b.quantiteUg), 0);


        const BLConsolideMarche = new EnteteBlIndividuel({
          fournisseur: this.saisieBLForm.get('fournisseur').value,
          numeroBl: this.saisieBLForm.get('numeroBl').value,
          cumulBl: this.saisieBLForm.get('cumulBl').value,
          montantSaisi: this.saisieBLForm.get('montantSaisi').value,
          tauxRf: this.saisieBLForm.get('tauxRf').value,
          transporteur: this.saisieBLForm.get('transporteur').value,
          raisonSocialeTransporteur: this.saisieBLForm.get('raisonSocialeTransporteur').value,
          codeCommande: this.saisieBLForm.get('enteteCommandeAchatGroupe').value?.codeCommande,
          enteteCommandeIndividuelleDTO : this.saisieBLForm.get('enteteCommandeAchatGroupe').value,
          id:this.saisieBLForm.get('id').value,
          dateReceptionBl: moment(this.saisieBLForm.get('dateReceptionBl').value).format('YYYY-MM-DD HH:mm:ss'),
          qteTotaleLivree: qteTotaleLivree,
          qteTotaleLivreeOriginal: qteTotaleLivree,
          // parse round to decimal value
          montantCalcule: parseFloat(this.synthese?.mntNet.toFixed(2)),
          montantCalculeOriginal: parseFloat(this.synthese?.mntNet.toFixed(2)),
          quantiteUg:quantiteUgLivree,
          etatBl:EtatBlAchatGroupeEnum.BROUILLON
        });
        BLConsolideMarche.lignes =  this.gridData.data.map((ligne:DetailBlAchatGroupeDTO) => {
          const currentMontant = ligne.pphRemise ?? ligne.pph;
          return new DetailBlAchatGroupeDTO({
            id:ligne.id,
            designation: ligne.designation,
            quantiteCommandee: ligne.quantiteCommandee,
            quantiteLivree: ligne.quantiteLivree ?? 0,
            pph: ligne.pph,
            ppv: ligne.ppv,
            quantiteUg: ligne.quantiteUg,
            // produit:ligne.produit,
            quantiteUgCmd:ligne.quantiteUgCmd,
            blocOffreId:ligne.blocOffreId,
            tauxRf:ligne.tauxRf,
            pphRemise:ligne.pphRemise,
            montant: currentMontant * ligne.quantiteLivree,
            isCadeau: ligne.isCadeau,
            qteFixePrdInCoffret: ligne.qteFixePrdInCoffret,
            qteReliquatBl: ligne.qteReliquatBl,
            isExtraLine:ligne.isExtraLine || false,
            oldPphRemise:ligne.oldPphRemise,
            oldTauxRf:ligne.oldTauxRf,
            quantiteLivreeOriginal: ligne.quantiteLivree ?? 0,
            montantOriginal: currentMontant * ligne.quantiteLivree,
            
            // datePeremption: ligne.dateper,
          });
        });

        return BLConsolideMarche;

      }

      pushMobileMenuOptions() {
        this.deferredActionBtnService.pushPageOptions([
          {
            iconClass: 'bi bi-bookmark-check-fill',
            label: 'Enregistrer Brouillon',
            shouldShow: !this.isConsult && !this.isUnitaire,
            action: () => this.processSaveBl()
          },
          {
            iconClass: 'bi bi-trash',
            label: 'Supprimer',
            shouldShow: this.enteteBl?.etatBl === 'BROUILLON' && !this.isUnitaire,
            action: () => this.isIndividuelle ? this.AnullerBlIndividuel() : this.AnullerBl()
          },
          {
            iconClass: 'bi bi-check',
            label: 'Valider',
            shouldShow: !this.isUnitaire && this.enteteBl?.etatBl === 'BROUILLON' || !this.enteteBl,
            action: () => this.isIndividuelle ? this.validateBlIndividuel() : this.processSaveBl(true)
          },
          {
            iconClass: this.enteteBl?.etatBl === 'REPARTI' ? 'bi bi-file-earmark-text' : 'bi bi-distribute-horizontal',
            label: this.enteteBl?.etatBl === 'REPARTI' ? 'Consulter Repartition' : 'Répartir',
            shouldShow: !this.isIndividuelle && this.isConsult && !this.isUnitaire && this.enteteBl?.etatBl === 'VALIDE' || !this.isUnitaire && this.enteteBl?.etatBl === 'REPARTI',
            action: () => this.dispatchBL()
          },
          {
            iconClass: 'bi bi-eye',
            label: 'Consulter Commande',
            shouldShow: this.enteteBl?.etatBl === 'REPARTI' ||  this.enteteBl?.etatBl === 'VALIDE' || this.saisieBLForm.get('enteteCommandeAchatGroupe')?.value,
            action: () => this.consulterCommande()
          },
          {
            iconClass: 'bi bi-printer-fill',
            label: 'Imprimer',
            shouldShow: this.isUnitaire,
            action: () => this.Imprimer()
          },
          {
            iconClass: 'mdi mdi-close',
            label: 'Quitter',
            shouldShow: true,
            action: () => this.back()
          }
        ]);
      }

      ngOnDestroy(): void {
        this.deferredActionBtnService.pushPageOptions([]);
      }
}

