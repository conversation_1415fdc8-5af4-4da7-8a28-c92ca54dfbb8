<div class="container-fluid bg-white" style="min-height: 100vh;">
  <div class="row" style="min-height: 100vh; overflow: hidden;">
      <!-- Sidebar -->
      <nav class="col-md-3 col-lg-3 d-md-block  sidebar h-100 border-left bg-light"  style="min-height: 100vh; position: sticky; top: 0;">
          <div class="sidebar-sticky">
              <h4 class="mt-3 mb-3 mx-4 text-center">La Centrale Pharma Guide</h4>
              <ul class="nav flex-column">
                <li *ngFor="let question of filteredQuestions" class="nav-item">
                  <a class="nav-link k-cursor-pointer" (click)="questionidChanged(question.id)" [ngClass]="{'active': activatedQuestion === question.id}">
                    {{ question.question }}
                  </a>
                </li>
              </ul>
          </div>
      </nav>

      <!-- Main content -->
      <main role="main" class="col-md-9 ml-sm-auto col-lg-9 px-2 py-1 content lacentrale-scroll"  style="height: 100vh;overflow-y: scroll;">
          <ng-container *ngIf="!activatedQuestion">
            <div class="d-flex flex-column align-items-center justify-content-start text-center k-gap-2 h-100 text-dark mt-4">
              <h1 class="mb-2 font-weight-bold">Bienvenue sur le guide de La Centrale Pharma</h1>
              <p class="mb-2">
                Cette page vous permet de consulter les questions fréquemment posées sur le site.
              </p>
              <div>

                <img src="/assets/images/faq.png" alt="guide-1" class="img-fluid rounded-circle" style="max-height: 350px;">
              </div>
              <button class="btn btn-faq-cm rounded-pill btn-lg mt-2" (click)="activatedQuestion = 1">
                Commencer
              </button>
            </div>
          </ng-container>

            <div class="faq-container text-dark" *ngIf="getCurrentQuestion()">
              <!-- <span class="font-18 font-weight-bold"  style="text-decoration: underline;" >{{ getCurrentQuestion()?.category }}</span> -->
              <div *ngFor="let answer of getCurrentQuestion().answers" class="faq-item mt-1">
                <h3 class="mb-4">{{ answer.question }} ?</h3>

                <!-- Text Content -->
                <p *ngIf="answer.textContent">{{ answer.textContent }}</p>

                <!-- Steps -->
                <div class="d-flex k-gap-4  flex-wrap">
                  <ng-container *ngIf="answer.steps && answer.steps.length > 0">
                    <div class="">


                    <h4>Étapes:</h4>
                    <ol>
                      <ng-container *ngFor="let step of answer.steps">
                        <li class="mb-1 font-16">
                          {{ step.text }}
                          <ul *ngIf="step.substeps && step.substeps.length > 0">
                            <li *ngFor="let substep of step.substeps">{{ substep.text }}</li>
                          </ul>
                        </li>
                      </ng-container>
                    </ol>
                   </div>
                  </ng-container>
                  <!-- Images -->
                  <div *ngIf="answer.images && answer.images.length > 0" class="image-container d-flex justify-content-center align-items-center w-100">
                    <ng-container *ngFor="let image of answer.images">
                      <video *ngIf="image.url" class="mx" style="max-height: 600px; width: 100%;" controls
                      [src]="cdnBaseUrl + image.url"
                      ></video>
                     </ng-container>
                  </div>
                  <!-- Video -->
                  <!-- <div *ngIf="answer.video" class="video-container flex-grow-1 ">
                    <iframe
                      width="560"
                      height="500"
                      [src]="getYouTubeEmbedUrl(answer.video.youtubeId)"
                      frameborder="0"
                      allow="autoplay; encrypted-media"
                      allowfullscreen>
                    </iframe>
                  </div> -->
                </div>
              </div>
            </div>

            <ng-container *ngIf="!getCurrentQuestion() && activatedQuestion">
              <h2 class="text-center mt-4">Aucune Content n'a été trouvée.</h2>
            </ng-container>
      </main>
  </div>
</div>


<!-- Floating action button -->
<button class="btn btn-lg info-fab d-flex align-items-center" (click)="openModal(shortcutModal)">
  <i class="bi bi-keyboard mr-1" style="font-size: 1.4rem"></i>
  <span style="font-size: 1.1rem;">Raccourcis</span>
</button>

<ng-template #shortcutModal let-modal>
  <div class="modal-header d-flex align-items-center">
      <h4 class="modal-title text-dark" id="modal-basic-title">Raccourcis Clavier</h4>

      <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span class="h3">&times;</span>
      </button>
  </div>
  
  <div class="modal-body m-0 p-0">
      <div *ngFor="let shortcut of keyboardShortcuts" class="row d-flex justify-content-between align-items-center my-3 mx-1 px-0">
          <div class="col d-flex justify-content-start m-0">
              <span class="text-dark" style="font-size: 1rem;">{{shortcut?.desc}}</span>
          </div>

          <div class="col d-flex justify-content-end m-0">
              <div class="row d-flex align-items-center">
                  <span *ngFor="let item of shortcut?.combo; let i=index">
                      <span class="combo-key mx-1">{{ item }}</span>
                      <span *ngIf="i < shortcut?.combo?.length - 1" class="h4">+</span>
                  </span>
              </div>
          </div>
      </div>
  </div>

  <div class="modal-footer">
      <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light">Fermer</button>
  </div>
</ng-template>

