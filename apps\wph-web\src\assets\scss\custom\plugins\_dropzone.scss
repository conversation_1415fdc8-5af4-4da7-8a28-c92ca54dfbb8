//
// dropzone.scss
//

ngx-dropzone, .dropzone {
    border: 2px dashed $input-border-color !important;
    background: $input-bg !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    min-height: 100px !important;
    padding: 20px !important;

    .dz-message {
        text-align: center;
        margin: 2rem 0;
    }
    &.dz-started {
        .dz-message {
            display: none;
        }
    }
}

