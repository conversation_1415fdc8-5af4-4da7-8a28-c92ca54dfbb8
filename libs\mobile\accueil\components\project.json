{"name": "mobile-accueil-components", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/mobile/accueil/components/src", "prefix": "wph", "targets": {"test": {"executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/mobile/accueil/components/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["libs/mobile/accueil/components/**/*.ts", "libs/mobile/accueil/components/**/*.html"]}}}, "tags": []}