import { Component, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { FormGroup, FormBuilder, FormControl } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { AlertService, PlateformeService, Role } from "@wph/shared";
import { AuthService } from "@wph/core/auth";
import { ExportPdf, ExportPdfService, UserInputService, getDynamicPageSize } from "@wph/web/shared";
import { Pagination } from "@wph/data-access";
import { PosteService } from "libs/web/gestion-annonces/src/lib/services/poste.service";
import { BlogPostCriteria, BlogPostDto, Categorie, Createur, PlateformeItem } from "libs/web/gestion-annonces/src/lib/models/BlogPost.interface";
import { DatePipe } from "@angular/common";
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from "rxjs";
import { LISTE_AVAILABLE_ROLES } from "./constants.const";

@Component({
    selector: 'wph-liste-annonces',
    templateUrl: './liste-annonces.component.html',
    styleUrls: ['./liste-annonces.component.scss']
})
export class ListeAnnoncesComponent implements OnInit, OnDestroy {
    gridView: GridDataResult;
    gridSort: SortDescriptor[];

    categories: Categorie[];
    plateformes: PlateformeItem[];
    postModalData: any | null = null;
    plateformeFilterData: any[];
    plateformeListGridData: GridDataResult;
    genericSearchControl = new FormControl();

    searchParams: BlogPostCriteria = {};
    navigation: Pagination = { skip: 0, pageSize: 15 };
    pageSizes: number[] = [5, 10, 15, 20];

    hasSuperAdminRole: boolean;
    currentUserSocieteId: number;
    isReadOnly: boolean = false;
    filterForm: FormGroup;

    displayFilter: boolean = false;
    modalTitle: string;

    selectRoles: { label: string, value: Role }[] = [];
    currentPlateforme: string;

    availableRoles: Role[] = [];

    unsubscribe$: Subject<boolean> = new Subject<boolean>();

    selectStatutPoste = [
        { label: 'Publié', value: 'P' },
        { label: 'Brouillon / Supprimé', value: 'B' }
    ];

    exportPdfRef: ExportPdf;

    constructor(
        private fb: FormBuilder,
        private datePipe: DatePipe,
        private srvAlert: AlertService,
        private modalService: NgbModal,
        private authService: AuthService,
        private posteService: PosteService,
        private exportPdfServ: ExportPdfService,
        private userInputService: UserInputService,
        private plateformeService: PlateformeService
    ) {
        this.filterForm = this.fb.group({
            statuts: [null],
            plateformePostDtos: [null],
            dateCreationDebut: [null],
            dateCreationFin: [null],
            listRoleCible: [null]
        });

        this.currentPlateforme = this.plateformeService.getCurrentPlateforme();

        if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
            this.filterForm.patchValue({
                plateformePostDtos: [5]
            });
        }
    }

    ngOnInit() {
        if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
            this.buildExport();
            this.listenToGenericCriteriaChanges();
        }

        this.setPageSize();

        this.currentUserSocieteId = this.authService.getPrincipal()?.societe?.id;
        this.hasSuperAdminRole = this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']);

        this.getListCategoris();
        this.getListePlateformes();

        this.getAvailableRoles();
    }

    buildExport(): void {
        this.exportPdfRef = this.exportPdfServ
            .ref<BlogPostDto>()
            .setTitle('Liste des Actualités & Annonces')
            .addColumn('auteur', 'Auteur', { width: 100 })
            .addColumn('titre', 'Titre', { width: 220 })
            .addColumn('categorie', 'Catégorie', {
                width: 100, transform: (value: Categorie) => {
                    return value?.libelle?.toUpperCase();
                }
            })
            .addColumn('createur', 'Saisie par', {
                width: 150, transform: (value: Createur) => {
                    return `${value?.firstname} ${value?.lastname}`;
                }
            })
            .addColumn('dateCreation', 'Date de saisie', {
                width: 80, transform: (value: Date) => {
                    return this.datePipe.transform(value, 'yyyy-MM-dd HH:mm');
                }
            })
            .addColumn('*', 'Statut', {
                width: 60, transform: (value: BlogPostDto) => {
                    if (value?.dateSuppression) {
                        return "Supprimé";
                    }

                    switch (value?.statut) {
                        case 'A':
                            return "Annulé";
                        case 'B':
                            return "Brouillon";
                        case 'P':
                            return "Publié";
                        default:
                            return value?.statut;
                    }
                }
            })
            .setData([]);
    }

    setPageSize(currentHeight?: number): void {
        const dynamicSize = getDynamicPageSize(currentHeight);

        if (dynamicSize !== this.navigation.pageSize) {
            this.navigation.pageSize = dynamicSize;

            this.pageSizes.push(dynamicSize);
            this.pageSizes = this.pageSizes.sort((a, b) => a - b);

            currentHeight && this.initSearch();
        }
    }

    getAvailableRoles(): void {
        if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
            this.authService.getAllRoles().subscribe((roles) => {
                this.availableRoles = roles.filter(role => {
                    return role.label !== 'ROLE_SUPER_ADMIN';
                });

                this.selectRoles = this.availableRoles.map(role => {
                    return { label: role.label, value: role };
                });
            });
        } else {
            this.availableRoles = LISTE_AVAILABLE_ROLES;
        }
    }

    listenToGenericCriteriaChanges() {
        this.genericSearchControl.valueChanges
            .pipe(
                takeUntil(this.unsubscribe$),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe(term => {
                this.searchParams['genericCriteria'] = term;
                this.initSearch();
            })
    }

    initSearch() {
        const filterValues = this.filterForm.getRawValue();

        if ((filterValues?.plateformePostDtos as number[])?.length) {
            this.searchParams = {
                ...this.searchParams,
                plateformePostDtos:
                    this.plateformes?.
                        filter(item => (filterValues?.plateformePostDtos as number[])?.includes(item.id))
            };
        }

        this.searchParams = {
            ...this.searchParams,
            statuts: filterValues['statuts'],
        };

        if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_OFFRE')) {
            this.searchParams['scopes'] = ['A'];
        } else {
            this.searchParams['scopes'] = this.hasSuperAdminRole ? ['G'] : ['F'];
            this.searchParams['fournisseursId'] = [this.authService.currentUser()?.societe?.id];
        }

        filterValues['dateCreationDebut'] && (this.searchParams['dateCreationDebut'] = filterValues['dateCreationDebut']);
        filterValues['dateCreationFin'] && (this.searchParams['dateCreationFin'] = filterValues['dateCreationFin']);
        filterValues['listRoleCible'] && (this.searchParams['listRoleCible'] = filterValues['listRoleCible']);

        this.posteService
            .searchPosts(
                { categorie: null, ...this.searchParams },
                { ...this.navigation, skip: this.getPageNumber(this.navigation.skip, this.navigation.pageSize) }
            )
            .subscribe((res) => {
                this.gridView = {
                    data: res.content,
                    total: res.totalElements,
                };

                if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
                    this.exportPdfRef.setData(this.gridView.data);
                }
            });
    }

    publierPosts(idPost: string) {
        if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
            this.userInputService.confirmAlt('Confirmer', 'Êtes-vous sûr de vouloir publier ce post ?').then(() => {
                this.posteService.publierPosts(idPost).subscribe((_res) => {
                    this.srvAlert.successAlt('Le post a été publié avec succès', 'Post Publié', 'MODAL');
                    this.initSearch();
                });
            }, () => null);
        } else {
            this.userInputService.confirm('Confirmer', 'Êtes-vous sûr de vouloir publier ce post ?').then(() => {
                this.posteService.publierPosts(idPost).subscribe((_res) => {
                    this.srvAlert.info('Le post a été publié avec succès');
                    this.initSearch();
                });
            }, () => null);
        }
    }

    /* -------------------------------------------------------------------------- */
    /*                                Grid Methods                                */
    /* -------------------------------------------------------------------------- */

    // ? recherche par filtration
    goSearchParams() {
        this.navigation.skip = 0;
        this.initSearch();
    }

    // ? calc the page for pagination
    getPageNumber(skip: number, pageSize: number) {
        return Math.floor(skip / pageSize);
    }

    // ? pagination event
    pageChange(event: any): void {
        if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
            this.initSearch();
        } else {
            if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
                this.navigation.skip = event.skip;
                this.navigation.pageSize = event.take;

                this.initSearch();
            }
        }
    }

    gridSortChange(sort: SortDescriptor[]): void {
        this.gridSort = sort;

        if (this.gridSort && this.gridSort.length > 0 && this.gridSort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.initSearch();
    }

    /* -------------------------------------------------------------------------- */
    /*                             Modal update or add                            */
    /* -------------------------------------------------------------------------- */

    openPostModal(modal: any, data = null, readOnly = false): void {
        this.postModalData = data;
        this.isReadOnly = readOnly;

        this.modalService.open(modal, { size: 'lg', centered: true, backdrop: 'static', modalDialogClass: 'fs-radius-modal' });
    }

    openPlateformeListModal(modal: any, data: PlateformeItem[], title = 'LISTE DES PLATEFORMES'): void {
        this.modalTitle = title;
        this.plateformeListGridData = {
            data: data,
            total: data?.length
        };

        this.modalService.open(modal, { size: 'md', centered: true }).result.then(
            () => { this.modalTitle = null },
            () => { this.modalTitle = null }
        );
    }

    getListCategoris() {
        this.posteService.listCategories().subscribe((res) => {
            this.categories = res;
        });
    }

    getListePlateformes(): void {
        if (!this.plateformes) {
            this.posteService.listePlateformes().subscribe(res => {
                this.plateformes = res;
                this.plateformeFilterData = this.transformPlateformeItemList(res);

                this.initSearch();
            })
        } else {
            this.initSearch();
        }
    }

    transformPlateformeItemList(items: PlateformeItem[]) {
        return items.map(plat => {
            return { label: plat?.libelle, value: plat?.id }
        });
    }

    open(content: any, size = 'md', centered = true) {
        this.modalService
            .open(content, { ariaLabelledBy: 'modal-basic-title', size, centered, modalDialogClass: 'fs-radius-modal' });
    }

    vider() {
        this.searchParams = {};
        this.filterForm.reset();
        this.navigation.skip = 0;
        this.searchParams['plateformePostDtos'] = null;

        this.initSearch();
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }
}
