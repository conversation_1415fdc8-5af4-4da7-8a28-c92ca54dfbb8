import { UserRole } from "@wph/shared";

export type FeatureFlagMapValue = { enabled: boolean };

export type FeatureFlagMap = Record<string, FeatureFlagMapValue>;

export type FeatureCategoryMap = Record<string, FeatureFlagModel[]>;

export enum FEATURE_KEY {
    SUPPORTEUR_CAN_EDIT_COMMANDE = 'supporterCanEditCommande',
    SUPPORTEUR_CAN_VALIDER_COMMANDE = 'supporterCanValiderCommande'
}

export enum FEATURE_KEY_STORAGE {
    ROOT = 'root_feature_flags',
    GROUP = 'group_feature_flags'
}

export interface BaseFeatureFlagMap {
    [key: string]: boolean;
}

export class FeatureFlagModel {
    key: FEATURE_KEY;
    active: boolean;

    constructor(key: FEATURE_KEY, active: boolean) {
        this.key = key;
        this.active = active;
    }
}

export interface CanAccessFeatureConfig {
    feature: FEATURE_KEY;
    roles: UserRole[];
    canAccess: boolean;
}