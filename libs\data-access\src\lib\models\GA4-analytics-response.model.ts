export interface DimensionHeader {
    name: string;
}

export interface MetricHeader {
    name: string;
    type: string;
}

export interface DimensionValue {
    value: string;
}

export interface MetricValue {
    value: string;
}

export interface Row {
    dimensionValues: DimensionValue[];
    metricValues: MetricValue[];
}

export interface AnalyticsResponse {
    dimensionHeaders: DimensionHeader[];
    metricHeaders: MetricHeader[];
    rows: Row[];
    totals?: any[];
    maximums?: any[];
    minimums?: any[];
    rowCount: number;
    metadata: {
        samplingMetadatas?: any[];
        dataLossFromOtherRow: boolean;
        currencyCode: string;
        timeZone: string;
    };
    propertyQuota?: any;
    kind: string;
}

type FilterMatchType = 'EXACT' | 'CONTAINS' | 'BEGINS_WITH' | 'ENDS_WITH' | 'FULL_REGEXP' | 'PARTIAL_REGEXP';

export interface GA4AnalyticsFilter {
    field?: string;
    value?: string;
    matchType?: FilterMatchType;
}


export class GA4AnalyticsSearchCriteria {
    startDate?: string;
    endDate?: string;
    dimensions?: string[];
    metrics?: string[];
    notFilters?: GA4AnalyticsFilter[];
    filters?: GA4AnalyticsFilter[]; 
    orderBys?: string[];
    limit?: number;
    offset?: number;

    constructor(partialCriteria?: Partial<GA4AnalyticsSearchCriteria>) {
        this.startDate = partialCriteria?.startDate || '28daysAgo';
        this.endDate = partialCriteria?.endDate || 'today';
        this.dimensions = partialCriteria?.dimensions || [];
        this.metrics = partialCriteria?.metrics || null;
        this.filters = partialCriteria?.filters || null;
        this.notFilters = partialCriteria?.notFilters || null;
        this.orderBys = partialCriteria?.orderBys || null;
        this.limit = partialCriteria?.limit || 0;
        this.offset = partialCriteria?.offset || 0;
    }
}
