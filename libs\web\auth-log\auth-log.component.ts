import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import { AuthLogService, Pagination } from '@wph/data-access';
import { AuthLog } from 'libs/data-access/src/lib/models/auth-log.model';
import { AuthLogCriteria } from 'libs/federation-syndicats/src/lib/models/auth-log.model';
@Component({
  selector: 'app-auth-log',
  templateUrl: './auth-log.component.html',
  styleUrls: ['./auth-log.component.css']
})
export class AuthLogComponent implements OnInit {

  authLogs: GridDataResult = {
    data: [] as AuthLog[],
    total: 0
  };

  navigation :Pagination= {
    pageSize: 21,
    skip: 0
  }

  authLogFilterForm : FormGroup;
  authLogCriteria :Partial<AuthLogCriteria> = {}
  modalRef: import("@ng-bootstrap/ng-bootstrap").NgbModalRef;

  constructor(
    private authLogService :AuthLogService,
    private fb:FormBuilder,
    private modalService :NgbModal
  ) { }

  ngOnInit() {
    this.searchAuthLogs();
    this.initFilterForm();
  }

  initFilterForm() {
    this.authLogFilterForm = this.fb.group({
      dateDebut: [null],
      dateFin: [null],
      nomPrenomOperateur: [null],
      operateur: [null],
      adresseIp: [null],
    });
  }

  cleanFilterQueryParams(filters:  Partial<AuthLogCriteria>) {
    // remove undifined,null values and empty strings
    Object.keys(filters).forEach(key => {
      if (filters[key] === undefined || filters[key] === '' || filters[key] === null || (typeof filters[key] === 'string' && filters[key].trim() === '')) {
        delete filters[key];
      }
    });
    return filters;
  }

  onPageChange(event: PageChangeEvent) {
    this.navigation.skip = event.skip;
    this.navigation.pageSize = event.take;
    this.searchAuthLogs();
  }


  searchAuthLogs() {
    this.authLogService.search(this.authLogCriteria,this.navigation).subscribe({
      next: (response) => {
        console.log('Auth logs:', response);
        this.authLogs.data = response.content;
        this.authLogs.total = response.totalElements;
      },

    })
  }

  openFilterModal(content: any) {
   this.modalRef =  this.modalService.open(content, { size: 'lg' });
  }

  filterAuthLog() {
    // collect the filter criteria from the form
    const criteria = new AuthLogCriteria({
      dateDebut: this.authLogFilterForm.get('dateDebut')?.value,
      dateFin: this.authLogFilterForm.get('dateFin')?.value,
      nomPrenomOperateur: this.authLogFilterForm.get('nomPrenomOperateur')?.value,
      operateur: this.authLogFilterForm.get('operateur')?.value,
      adresseIp: this.authLogFilterForm.get('adresseIp')?.value,
    });



    const cleanedCriteria = this.cleanFilterQueryParams(criteria);
    this.authLogCriteria = cleanedCriteria;
    this.searchAuthLogs();
    this.modalRef?.close();
  }

  resetFilter() {
    this.authLogFilterForm.reset();
    this.authLogCriteria = {};
    this.searchAuthLogs();
    this.modalRef?.close();
  }


}
