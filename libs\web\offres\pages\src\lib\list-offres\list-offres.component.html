<!-- Start Of Header -->
<div (click)="closeDropdowns()" class="rowline mb-0">
  <div class="page-title-box row pt-0 pb-lg-1">
    <h4 class="page-title fw-4 ps-2 col-lg-4 col-6">Liste des offres</h4>

    <div class="col-lg-8 col-6 px-1">
      <div class="row justify-content-end align-items-center">
        <button *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" type="button" (click)="offreNouvelle()"
          class="btn btn-sm btn-primary m-1 d-flex row text-white" title="nouvelle offre">
          <i class="bi bi-plus-circle-fill mr-sm-1"></i>
          <span class="d-sm-block d-none">Nouvelle Offre</span>
        </button>

      </div>
    </div>
  </div>
</div>
<!-- END HEADER -->

<div (click)="closeDropdowns()" class="card m-0 p-1 w-100 b-radius bg-white" [style.minHeight]="'calc(100vh - 123px)'" [style.maxHeight]="!isListView ? 'calc(100vh - 123px)' : 'none'">
  <div class="card-header py-1 pl-2 mx-0 border">
    <div class="d-flex row justify-content-between align-items-center w-100 mx-0 px-0">
      <div class="col-auto p-0">
        <button (click)="(isListView = false); (isSeleceted = false); updatePageParams()" title="Cartes"
          class="btn btn-sm py-1 px-2 {{!isListView ? 'btn-wo-selected text-white' : 'btn-light text-dark'}} border mx-1">
          <i class="mdi mdi-18px mdi-view-grid"></i>
        </button>

        <button (click)="(isListView = true); updatePageParams()" title="Liste"
          class="btn btn-sm {{isListView ? 'btn-wo-selected text-light' : 'btn-light text-dark'}} py-1 px-2  border mx-1">
          <i class="mdi mdi-18px mdi-format-list-bulleted"></i>
        </button>
      </div>

      <div *ngIf="!isListView" class="col-auto p-0 d-flex justify-content-center align-items-center" style="gap: 10px">
        <button (click)="updateSelectedTypeOffre('mono')" class="btn btn-md d-flex type-offre-btn py-1 px-2"
          [ngClass]="{'selected': selectedTypeOffre === 'mono'}">
          <i class="bi bi-box mr-md-1 mr-0"></i>
          <span class="d-none d-md-block">Mono-produit ({{ offresMonoProduit?.length }})</span>
        </button>

        <button (click)="updateSelectedTypeOffre('multi')" class="btn btn-md d-flex type-offre-btn py-1 px-2"
          [ngClass]="{'selected': selectedTypeOffre === 'multi'}">
          <i class="bi bi-boxes mr-md-1 mr-0"></i>
          <span class="d-none d-md-block">Multi-produit ({{ offresMultiProduit?.length }})</span>
        </button>
      </div>

      <div class="col-auto p-0 d-flex justify-content-end">
        <div class="row p-0">
          <div class="col p-0 m-1 d-lg-flex d-none">
            <div class="input-group picker-input">
              <input type="search" placeholder="Titre de l'offre" class="form-control b-radius form-control-md pl-4"
                id="titreOffre" style="border-radius: 8px;" [formControl]="searchControl" />

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>


          <button type="button" class="btn btn-sm search-btn m-1 b-radius" (click)="displayFilter = !displayFilter">
            <span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
              <i class="bi bi-sliders"></i>
              <span class="mx-1">Recherche Avancée</span>
            </span>

            <ng-template #closeFilter>
              <span class="d-flex align-items-center">
                <i class="mdi mdi-close"></i>
                <span class="mx-1">Fermer la recherche</span>
              </span>
            </ng-template>

          </button>
        </div>
      </div>

      <ng-container *ngIf="displayFilter">
        <wph-offre-filter [formGroup]="offreForm" [forceCloseAllDropdowns]="forceCloseDropdowns" [categorieProduits]="categorieProduits"
          (modalAction)="filterModalAction($event)"></wph-offre-filter>
      </ng-container>
    </div>
  </div>

  <div class="card-body offres-card mx-0 my-0 p-0" scrollListener style="overflow:hidden;"
    [style]="{'overflow': !isListView ? 'scroll' : 'hidden'}" #cardViewContainer>
    <kendo-grid [ngClass]="{'hidden-container': !isListView}" [data]="gridData" [pageable]="{
      buttonCount: 5,
      info: true,
      type: 'numeric',
      pageSizes: pageSizes,
      previousNext: true,
      position: 'bottom'
    }" [pageSize]="navigation.pageSize" [skip]="navigation.skip" [style.minHeight]="'calc(100vh - 205px)'"
      (pageChange)="pageoffresChange($event)" [sortable]="{ mode: 'single'}" [sort]="offreSort" [resizable]="true"
      (sortChange)="offreSortChange($event)" (cellClick)="cellClickHandler($event)">

      <kendo-grid-column [width]="120" field="numeroOffre" title="Numéro" class="text-left">
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="230" field="titre" title="Titre de l'offre" class="text-wrap">
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="160" field="offreur.raisonSociale" title="Offreur">
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>


        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <span class="text-wrap" [ngClass]="{
            'labo-label': dataItem.offreur?.typeEntreprise === 'FABRIQUANT',
            'grossiste-label': dataItem.offreur?.typeEntreprise === 'GROSSISTE'
          }">
            <b>{{dataItem.offreur?.raisonSociale | uppercase}}</b>
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column filtra [width]="140" [filterable]="false" [sortable]="false" field="distributeur.raisonSociale"
        title="Distributeur">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <div class="text-wrap" [ngClass]="{
              'labo-label': dataItem.distributeurs[0]?.typeEntreprise === 'FABRIQUANT',
              'grossiste-label': dataItem.distributeurs[0]?.typeEntreprise === 'GROSSISTE'
            }" *ngIf="dataItem.distributeurs?.length === 1">
            <b>{{dataItem.distributeurs[0]?.raisonSociale}}</b>
          </div>
          <div class="see-more" *ngIf="dataItem.distributeurs?.length > 1">Voir Plus ...</div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="120" field="dateDebut" title="Date de lancement" class="text-right">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap">Date de lancement</span>
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.dateDebut | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="120" field="dateFin" title="Date limite" class="text-right">
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.dateFin | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="120" field="delaiLivraison" title="Délai de livraison" class="text-center">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap">Délai de livraison</span>
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <span *ngIf="dataItem?.delaiLivraison">
            {{dataItem?.delaiLivraison | number }} Jour{{ dataItem?.delaiLivraison > 1 ? 's' : '' }}
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="140" field="etatProposant" title="Statut" class="text-center">
        <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
          let-filterService="filterService">
          <kendo-multiselect style="width: 215px" [data]="stautsLabelsValues" textField="label" valueField="value"
            [valuePrimitive]="true" [value]="filter | filterValues"
            (valueChange)="SelectChanges($event, filterService, 'etatProposant')">
          </kendo-multiselect>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <app-element-status
            [state]="(dataItem?.etat === 'cloturee') ? 'EXPIREE' : dataItem.etatProposant"></app-element-status>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" title="Actions" [width]="120">
        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="d-flex justify-content-center k-gap-2">
            <span (click)="offreEdit(dataItem)"
              *ngIf="(dataItem?.etatProposant === 'BROUILLON') && (dataItem?.etat ===  'en cours')"
              class="actions-icons btn-success pointer-cus" title="Modifier Offre">
              <i class="bi bi-pencil-square"></i>
            </span>

            <span (click)="offreEdit(dataItem)"
              *ngIf="(dataItem?.etatProposant === 'PUBLIER') && (dataItem?.etat !== 'cloturee' && dataItem?.etatProposant !== 'CLOTURE' && dataItem?.etatProposant !== 'ANNULER')"
              class="actions-icons btn-success pointer-cus" title="Modifier Offre">
              <i class="bi bi-pencil-square"></i>
            </span>

            <span (click)="offreDupliquer(dataItem)"
              *ngIf="(dataItem?.etatProposant !== 'BROUILLON')|| (dataItem?.etat === 'cloturee')"
              class="actions-icons btn-info pointer-cus" title="Dupliquer Offre">
              <i class="bi bi-files-alt"></i>
            </span>

            <span (click)="cloturerOffre(dataItem)"
              [ngClass]="{'opacity-light': dataItem?.etatProposant === 'CLOTURE' || dataItem?.etatProposant === 'ANNULER' || dataItem?.etat === 'cloturee'}"
              class="actions-icons  btn-danger pointer-cus"
              [title]="(dataItem?.etatProposant === 'BROUILLON') ? 'Annuler Offre' : 'Cloturer Offre'">
              <i class="bi bi-trash"></i>
            </span>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-messages pagerItems="lignes" pagerOf="de" pagerItemsPerPage="éléments par page"></kendo-grid-messages>

      <ng-template kendoGridNoRecordsTemplate>
        <span>Aucun résultat trouvé.</span>
      </ng-template>
    </kendo-grid>

    <!-- Offre tile view component -->
    <div class="row card-row d-flex flex-wrap mx-0 justify-content-md-start justify-content-center" [ngClass]="{'hidden-container': isListView}"
      style="gap:15px">
      <wph-widgets-offres [ngClass]="{'hidden-container': selectedTypeOffre !== 'mono'}"
        *ngFor="let offre of offresMonoProduit" [offre]="offre" mode="C"></wph-widgets-offres>

      <wph-widgets-offres [ngClass]="{'hidden-container': selectedTypeOffre !== 'multi'}"
        *ngFor="let offre of offresMultiProduit" [offre]="offre" mode="A"></wph-widgets-offres>
    </div>

  </div>
</div>

<ng-template #popFournisseurs let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">LISTE DES DISTRIBUTEURS</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span>&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <kendo-grid [data]="selectedFounrisseurs" [hideHeader]="true">
      <kendo-grid-column [width]="100" field="etatProposant" title="">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.raisonSociale}}
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">Fermer
    </button>
  </div>
</ng-template>

<ng-template #filterModal let-modal>
  <wph-offre-filter [formGroup]="offreForm" (modalAction)="filterModalAction($event)"></wph-offre-filter>
</ng-template>