
.card, .card-header, .card-body {
    border-radius: var(--winoffre-base-border-radius) !important;
}

.b-radius {
    border-radius: var(--winoffre-base-border-radius) !important;
}
.btn {
    font-size: 1rem;
    font-weight: 600;
}

.input-group {
    .form-control {
      color: black;
      font-weight: 700;
        border-top-left-radius: var(--winoffre-base-border-radius);
        border-bottom-left-radius: var(--winoffre-base-border-radius);

    }
    .btn {
      border-top-right-radius: var(--winoffre-base-border-radius);
      border-bottom-right-radius: var(--winoffre-base-border-radius);
    }
  }

label {
    color: black !important;
    font-weight: 700 !important;
}

.picker-input {
    .form-control{
        border-radius: var(--winoffre-base-border-radius) !important;
    }
}

::ng-deep #WIN_GROUPE-container .selected-switch {
    color: #f3f3f3;
    background: var(--wf-primary-600);
}

::ng-deep #FEDERATION_SYNDICAT-container .selected-switch {
  color: #f3f3f3;
  background: var(--fs-grid-primary);
}

.card-view {
    height: calc(100vh - 190px);
    overflow-x: hidden;
    overflow-y: auto;
}

.opacity-light {
  opacity: 0.6 !important;
}


.btn-success{
  background: var(--fs-success) !important;
  border-color: var(--fs-success) !important;
}

.btn-danger{
  background: var(--fs-danger) !important;
  border-color: var(--fs-danger) !important;
}

.form-check-input {
  height: 28px;
  width: 28px;
  border: 2px solid #A975CC;
  -webkit-appearance: none; /* Remove default styling on Webkit browsers */
  -moz-appearance: none; /* Remove default styling on Firefox */
  appearance: none;
  background-color: #fff;
  display: inline-block;
  position: relative;
  border-radius: 50%; /* Change to circular shape for radio buttons */
  margin-right: 10px;
}

.form-check-input:checked {
  background-color: #A975CC;
  border-color: #A975CC;
}

.form-check-input:checked::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  border-radius: 50%; /* Inner circle to show selected state */
  background-color: #fff;
  transform: translate(-50%, -50%);
}

.modal-body textarea {
  border: 2px solid #A975CC;
  -webkit-appearance: none; /* Remove default styling on Webkit browsers */
  -moz-appearance: none; /* Remove default styling on Firefox */
  appearance: none;
  background-color: #fff;
  display: inline-block;
  position: relative;
}