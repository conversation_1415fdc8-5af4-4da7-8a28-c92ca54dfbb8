import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { EditPharmacieComponent } from "./edit-pharmacie/edit-pharmacie.component";
import { PharmaciesListComponent } from "./liste-pharmacies/pharmacies-list.component";

const routes: Routes = [
    {
        path: 'edit/:id',
        component: EditPharmacieComponent
    },
    {
        path: 'suggerer',
        title: 'Ajouter Pharmacie',
        component: EditPharmacieComponent
    },
    {
        path: 'liste',
        title: 'Liste Pharmacies Maroc',
        component: PharmaciesListComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class PharmacieMarocRoutingModule {}