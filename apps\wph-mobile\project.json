{"name": "wph-mobile", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/wph-mobile/src", "prefix": "wph", "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/wph-mobile", "index": "apps/wph-mobile/src/index.html", "main": "apps/wph-mobile/src/main.ts", "polyfills": "apps/wph-mobile/src/polyfills.ts", "tsConfig": "apps/wph-mobile/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["apps/wph-mobile/src/assets", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["apps/wph-mobile/src/theme/variables.scss", {"input": "apps/wph-mobile/src/global.scss"}], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1.3mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "100kb"}], "fileReplacements": [{"replace": "apps/wph-mobile/src/environments/environment.ts", "with": "apps/wph-mobile/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "wph-mobile:build:production"}, "development": {"browserTarget": "wph-mobile:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "wph-mobile:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["apps/wph-mobile/**/*.ts", "apps/wph-mobile/**/*.html"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/wph-mobile"], "options": {"jestConfig": "apps/wph-mobile/jest.config.ts", "passWithNoTests": true}}, "cap": {"executor": "@nxext/capacitor:cap", "options": {"cmd": "--help"}, "configurations": {"android": {"cmd": "run android -l --external"}}}, "add": {"executor": "@nxext/capacitor:cap", "options": {"cmd": "add"}, "configurations": {"ios": {"cmd": "add ios"}, "android": {"cmd": "add android"}}}, "copy": {"executor": "@nxext/capacitor:cap", "options": {"cmd": "copy"}, "configurations": {"ios": {"cmd": "copy ios"}, "android": {"cmd": "copy android"}}}, "open": {"executor": "@nxext/capacitor:cap", "options": {"cmd": "open"}, "configurations": {"ios": {"cmd": "open ios"}, "android": {"cmd": "open android"}}}, "run": {"executor": "@nxext/capacitor:cap", "options": {"cmd": "run"}, "configurations": {"ios": {"cmd": "run ios"}, "android": {"cmd": "run android"}}}, "sync": {"executor": "@nxext/capacitor:cap", "options": {"cmd": "sync"}, "configurations": {"ios": {"cmd": "sync ios"}, "android": {"cmd": "sync android"}}}, "update": {"executor": "@nxext/capacitor:cap", "options": {"cmd": "update"}, "configurations": {"ios": {"cmd": "update ios"}, "android": {"cmd": "update android"}}}}, "tags": []}