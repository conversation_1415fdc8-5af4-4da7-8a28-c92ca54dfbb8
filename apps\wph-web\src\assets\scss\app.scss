/*
Template Name: Hyper - Angular Js Admin Dashboard
Version: 3.0.0
Author: CoderThemes
Email: <EMAIL>
File: Main Css File
*/
//Core files


@import "./node_modules/bootstrap/scss/functions";
@import "./node_modules/bootstrap/scss/variables";
@import "config/saas/variables";
@import "config/saas/custom-variables";
@import "./node_modules/bootstrap/scss/bootstrap";


// Structure
@import "custom/structure/general";
@import "custom/structure/left-menu";
@import "custom/structure/topbar";
@import "custom/structure/right-sidebar";
@import "custom/structure/page-head";
@import "custom/structure/footer";
@import "custom/structure/gener";
@import "custom/structure/querym";
// horizontal nav
@import "custom/structure/horizontal-nav";


//Fonts
@import "custom/fonts/nunito";

//Components
@import "custom/components/mixins";
@import "custom/components/accordions";
@import "custom/components/avatar";
@import "custom/components/breadcrumb";
@import "custom/components/buttons";
@import "custom/components/badge";
@import "custom/components/card";
@import "custom/components/dropdown";
@import "custom/components/docs";
@import "custom/components/forms";
@import "custom/components/modal";
@import "custom/components/nav";
@import "custom/components/pagination";
@import "custom/components/popover";
@import "custom/components/print";
@import "custom/components/progress";
@import "custom/components/reboot";
@import "custom/components/ribbons";
@import "custom/components/switch";
@import "custom/components/tables";
@import "custom/components/type";
@import "custom/components/utilities";
@import "custom/components/widgets";
@import "custom/components/social";
@import "custom/components/steps";
@import "custom/components/preloader";
@import "custom/components/hero";


//Pages
@import "custom/pages/authentication";
@import "custom/pages/components-demo";
@import "custom/pages/error";
@import "custom/pages/faq";
@import "custom/pages/maintenance";
@import "custom/pages/tasks";
@import "custom/pages/email";
@import "custom/pages/timeline";


// Vendors
@import "vendor/daterangepicker";
@import "vendor/bootstrap-datepicker.min";
@import "vendor/jquery.toast.min";
@import "vendor/select2.min";
@import "vendor/jquery.toast.min";
@import "vendor/bootstrap-timepicker.min";


// Plugins
@import "custom/plugins/apexcharts";
@import "custom/plugins/britechart";
@import "custom/plugins/simplebar";
//@import "custom/plugins/calendar";
@import "custom/plugins/chartjs";
@import "custom/plugins/datatable";
@import "custom/plugins/daterange";
@import "custom/plugins/datepicker";
@import "custom/plugins/dragula";
@import "custom/plugins/dropzone";
@import "custom/plugins/form-wizard";
@import "custom/plugins/maps";
@import "custom/plugins/metis-menu";
@import "custom/plugins/select2";
@import "custom/plugins/slimscroll";
@import "custom/plugins/toaster";
@import "custom/plugins/bootstrap-touchspin";
@import "custom/plugins/bootstrap-timepicker";
@import "custom/plugins/summernote";
@import "custom/plugins/simplemde";
@import "custom/plugins/typehead";
@import "custom/plugins/sparklines";
@import "custom/plugins/rateit";
@import "custom/plugins/rangeslider";
@import "custom/plugins/frappe-gantt";

@import "./custom-styles.scss";
@import "./kendo_grid.scss";
@import "~leaflet/dist/leaflet.css";
@import "bootstrap-icons";

:root {
  --win-offre-primary: #f0a10f;
  --win-offre-primary-light:  #CC9726;
  --win-offre-primary-shade: #c08911;
  --win-offre-primary-tint: #ffa500;

  --win-offre-bg-primary: #CD5C08;
  --win-offre-bg-light: #F7EDC8;
  --win-offre-bg-light-2: #faf7ec;
  --win-offre-bg-action: #6A9C89;

  --win-offre-bg-action-shade-1: #f0a10f;
  --win-offre-bg-action-shade-2: #898121;

  --win-offre-bg-action-tint-1: #E5C287;
  --win-offre-bg-action-tint-2: #FDA403;

  --winoffre-text-primary: #CD5C08;
  --winoffre-text-secondary: #6A9C89;
  --winoffre-text-light-shade: #475467;

  --cw-bg-tint: #87e5bc;
  --cw-primary: #018347;
  --cw-primary-light: #09bb68;
  --cw-primary-light-2: #009d4f;
  --cw-primary-light-3: #00f079;
  --cw-primary-tint: #d3ffeb;
  --cw-primary-grid-tint: #ecfaf3;
  --cw-primary-grid-hover-tint: #e9fef3;

  // ? COMMANDE WEB COLOR SCALE
  --cw-primary-50: #edfff6;
  --cw-primary-100: #d3ffeb;
  --cw-primary-200: #abffd8;
  --cw-primary-300: #6affbc;
  --cw-primary-400: #23ff98;
  --cw-primary-500: #00f079;
  --cw-primary-600: #00c960;
  --cw-primary-700: #009d4f;
  --cw-primary-800: #018347;
  --cw-primary-900: #018347;

  // ? WINOFFRE PALETTE
  --wo-primary-50: #f3f6f5;
  --wo-primary-100: #e1eae4;
  --wo-primary-200: #c5d5cb;
  --wo-primary-300: #9eb7a9;
  --wo-primary-400: #739483;
  --wo-primary-500: #537665;
  --wo-primary-600: #3e5d4e;
  --wo-primary-700: #324a3f;
  --wo-primary-800: #293c34;
  --wo-primary-900: #22322b;

  --wo-secondary: #F2971F;


  // ? LA CENTRALE PHARMA PALETTE
  --fs-primary-50: #f9f6fe;
  --fs-primary-100: #f2ebfc;
  --fs-primary-200: #e7dafa;
  --fs-primary-300: #d4bcf6;
  --fs-primary-400: #b991ef;
  --fs-primary-500: #9e67e5;
  --fs-primary-600: #8747d6;
  --fs-primary-700: #7335bb;
  --fs-primary-800: #623099;
  --fs-primary-900: #51287b;

  --fs-grid-primary: #6769B2;
  --fs-grid-light: #E3E4FA;

  // ? WEB_FIX PALETTE
  --wf-primary-50: #f4f8ed;
  --wf-primary-100: #e4efd8;
  --wf-primary-200: #cce0b6;
  --wf-primary-300: #accc8a;
  --wf-primary-400: #8eb665;
  --wf-primary-500: #709b47;
  --wf-primary-600: #567b35;
  --wf-primary-700: #435f2c;
  --wf-primary-800: #384c28;
  --wf-primary-900: #324225;

  --wf-secondary: #FCBB01;

  --ph-bg-tint: #87b3e5;

  --winoffre-base-border-radius: 10px;
  --winoffre-button-border-radius: 15px;


  --fs-group-bg: var(--wf-primary-600);
  --fs-group-grid: var(--wf-primary-500);
  --fs-member-bg: rgba(142, 182, 101, 0.21);
  --fs-chef-bg: rgba(235, 235, 235, 1);


  --fs-primary: #9e67e5;
  --fs-primary-light: #c3a2f1;
  --fs-primary-shade: #8648d5;
  --fs-primary-shade-2: #6459A9;
  --fs-primary-light-shade: #8f54dd;
  --fs-primary-tint: #d3bcf6;
  --fs-primary-tint-2: #e6dafa;
  --fs-primary-tint-3: #f2ebfc;

  --fs-secondary: #5256d9;
  --fs-secondary-light: #acbff5;
  --fs-secondary-shade: #4345bf;
  --fs-secondary-light-shade: #6e7be6;
  --fs-secondary-tint: #cbd9fa;
  --fs-secondary-tint-2: #e2eafd;
  --fs-secondary-tint-3: #eff4fe;
  --fs-secondary-text: #2D3748;
  --fs-success: #5E9E71;
  --fs-danger: #A93535;
  --fs-warning: #EE8245;
  --fs-actualite-default-img: url('../images/default-actualité.jpg');
  --wo-bokeh-blue-img: url('../images/bokeh_blue.jpeg');
  --fs-actu-default-img: url('../images/pharmaciens.jpeg');
  --fs-groupe-card-default-img: url('../images/groupe-card-default-2.jpg');
  --magic-wand-icon-green: url('../images/magic-wand-sparkle.svg');
  --magic-wand-icon-white: url('../images/magic-wand-sparkle-white.svg');

  --pharmalien-actu-default-img: url('../images/pharmalien-default-img.jpeg');

  --plateforme-selection-wp-bg: url('https://cdn.sophatel.com/pharmalien/images/winplus-pub.svg');
  --cmd-web-bg: url('https://cdn.sophatel.com/pharmalien/images/pub-commande-web.svg');
  --plateforme-selection-materiel-soph: url('https://cdn.sophatel.com/pharmalien/images/pub-matariel_v3.svg');
  --topbar-img: url('https://cdn.sophatel.com/pharmalien/images/quebg.svg');

}

body {
  background-color: #f0f2f5 !important;
}

.card {
  border: none !important;
  border-color: #dedede !important;
}

.sub-bloc {
  border: 1px solid #ebebeb !important;
}

.btn-warning-cus {
  background: $warning;
  color: #fff;
  border-color: darken($warning, 8) !important;

  &:hover {
    color: #fff;
    text-decoration: none;
  }

  &:focus {
    color: #fff;
    background-color: #d98200;
    border-color: none;
    box-shadow: none;

  }
}



.search-btn {
  background-color: #fff;
  border-color: var(--wf-primary-600);
  color: var(--wf-primary-500);
  border-radius: var(--winoffre-button-border-radius) !important;

  font-weight: 600 !important;
}

.search-btn:hover,
.search-btn:focus,
.search-btn:active {
  background-color: var(--wf-primary-500);
  border-color: var(--wf-primary-600);
  color: #fff;
}

#WIN_OFFRE-container {
  .search-btn {
    background-color: #fff;
    border-color: var(--wo-primary-500);
    color: var(--wo-primary-500);
    border-radius: 8px !important;
    font-size: 1rem;
    font-weight: 600 !important;
  }

  .search-btn:hover,
  .search-btn:focus,
  .search-btn:active {
    background-color: var(--wo-primary-500);
    border-color: var(--wo-primary-500);
    color: #fff;
  }

  .Imprimer-btn {
    background-color: #fff;
    border-color: var(--wo-primary-500);
    color: var(--wo-primary-500);
  }
  
  .Imprimer-btn:hover,
  .Imprimer-btn:focus,
  .Imprimer-btn:active {
    background-color: var(--wo-primary-500);
    border-color: var(--wo-primary-500);
    color: #fff;
  }
}

#FEDERATION_SYNDICAT-container {
  .search-btn {
    background-color: #fff;
    border-color: var(--fs-grid-primary);
    color: var(--fs-grid-primary);
    border-radius: 8px !important;
    font-size: 1rem;
    font-weight: 600 !important;
  }

  .search-btn:hover,
  .search-btn:focus,
  .search-btn:active {
    background-color: var(--fs-grid-primary);
    border-color: var(--fs-grid-primary);
    color: #fff;
  }

  .Imprimer-btn {
    background-color: #fff;
    border-color: var(--fs-grid-primary);
    color: var(--fs-grid-primary);
  }
  
  .Imprimer-btn:hover,
  .Imprimer-btn:focus,
  .Imprimer-btn:active {
    background-color: var(--fs-grid-primary);
    border-color: var(--fs-grid-primary);
    color: #fff;
  }
}

#WIN_GROUPE-container {
  .Imprimer-btn {
    background-color: #fff;
    border-color: var(--wf-primary-500);
    color: var(--wf-primary-500);
  }
  
  .Imprimer-btn:hover,
  .Imprimer-btn:focus,
  .Imprimer-btn:active {
    background-color: var(--wf-primary-500);
    border-color: var(--wf-primary-500);
    color: #fff;
  }
}

#COMMANDE_WEB-container {
  .Imprimer-btn {
    background-color: #fff;
    border-color: var(--cw-primary-600);
    color: var(--cw-primary-600);
  }

  .Imprimer-btn:hover,
  .Imprimer-btn:focus,
  .Imprimer-btn:active {
    background-color: var(--cw-primary-600);
    border-color: var(--cw-primary-600);
    color: #fff;
  }
}

.is-invalid {
  border-color: red !important;
}


.k-grid-header, .k-grid-header th {
  border-bottom: none !important;
}

.k-grid tr {
  background-color: transparent;
}

.k-grid td {
  background-color: transparent;
}

.k-grid tr:hover {
  background-color: #f5f5f5;
}

.k-pager-wrap,
.k-pager-numbers,
.k-pager-input {
  border: none !important;
}

.k-pager-numbers .k-link,
.k-pager-input .k-textbox {
  background-color: transparent;
  border: none;
}

kendo-grid tr th {
  font-size: 10px;
}

kendo-grid tfoot td {
  font-size: 12px;

}

.k-grid {
  border-radius: var(--winoffre-base-border-radius) !important;
  overflow: hidden;
}

.k-grid-header,
.k-grid-content,
.k-grid-pager {
  border-radius: 0;
}

.k-grid td {
  white-space: nowrap;
  color: #3D3D3D;
}

.btn-primary-offre {
  background-color: $yellow;
  color: #fff;
  border-color: $yellow;
}

.btn-primary-offre:hover {
  color: #fff;
  opacity: .9;
  box-shadow: none;
}

fieldset.scheduler-border {
  border: 1px groove #ddd !important;
  padding: 0 1.4em 0 1.4em !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000;
}

legend.scheduler-border {
  font-size: 1.2em !important;
  font-weight: bold !important;
  text-align: left !important;
  width: fit-content;
  /* Or auto */
  padding: 0 10px;
  /* To give a bit of padding on the left and right */
  border-bottom: none;
}

.btn-group-delete,
.btn-group-dupliquer,
.btn-reference-delete,
.btn-reference-dupliquer,
.btn-pack-delete,
.btn-pack-dupliquer {
  font-size: 16px;
  padding: 3px 8px;
  border-radius: 50px;
  box-shadow: none !important;
}

.btn-group-delete,
.btn-group-dupliquer {
  i {
    color: darken(#5fb5ce, 20) !important;
  }

  border: 1px solid darken(#5fb5ce, 20) !important;
  background: #b1e2f1;
}

.btn-reference-delete,
.btn-reference-dupliquer {
  i {
    color: #487421 !important;
  }

  border: 1px solid #487421 !important;
  background: #d9ffb6;
}

.btn-pack-delete,
.btn-pack-dupliquer {
  i {
    color: #6c757d !important;
  }

  border: 1px solid #6c757d !important;
  background: #f6f2f2;
}

.btn-group-delete:hover,
.btn-reference-delete:hover,
.btn-pack-delete:hover {
  i {
    color: white !important;
    transition: all .2s ease-in-out;
  }

  border: 1px solid darken(#fa5c7c, 20) !important;
  background: #fa5c7c;
  transition: all .2s ease-in-out;
}

.btn-group-dupliquer:hover,
.btn-reference-dupliquer:hover,
.btn-pack-dupliquer:hover {
  i {
    color: white !important;
    transition: all .2s ease-in-out;
  }

  border: 1px solid darken(#bf5de6, 20) !important;
  background: #bf5de6;
  transition: all .2s ease-in-out;
}


.control {
  display: block;
  position: relative;
  padding-left: 29px;
  margin-bottom: 8px;
  padding-top: 2px;
  cursor: pointer;
  font-size: 15px;
  font-weight: bold;
}

.control input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}

.control_indicator {
  position: absolute;
  top: 2px;
  left: 0;
  height: 20px;
  width: 20px;
  background: #fff;
  border: 1px solid #d7d7d7;
  border-radius: 4px;
}

.control:hover input~.control_indicator,
.control input:focus~.control_indicator {
  background: #f0f0f0;
}

.control input:checked~.control_indicator {
  background: #0075FF;
  border: 1px solid #0075FF;
  box-shadow: 0px 0px 2px #0075FF;
}

.control input:disabled~.control_indicator {
  background: #bcb8b8;
  opacity: 0.6;
  pointer-events: none;
}

.control_indicator:after {
  box-sizing: unset;
  content: '';
  position: absolute;
  display: none;
}

.control input:checked~.control_indicator:after {
  display: block;
}

.control-checkbox .control_indicator:after {
  left: 7px;
  top: 3px;
  width: 3px;
  height: 8px;
  border: solid #ffffff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.control-checkbox input:disabled~.control_indicator:after {
  border-color: #555050;
}

.add-pack {
  background: rgb(255 152 0 / 21%);
  border-color: #FF9800;
  color: #FF9800 !important;
  font-size: 16px;
  box-shadow: none !important;
}

.add-pack:hover {
  background: rgb(217 131 4 / 75%);
  border-color: rgb(217 131 4 / 75%);
  color: white !important;
}

.pointer-cus {
  cursor: pointer !important;
  pointer-events: auto !important;
}

.btn-info-offre {
  background-color: #d5f6ff !important;
  color: darken(#5fb5ce, 20);
  border-color: darken(#5fb5ce, 10) !important;

  .form-control {
    border: 1px solid #2f8198 !important;
  }
}

button.btn-info-offre {
  background-color: #5fb5ce !important;
  color: white;
  border-color: darken(#5fb5ce, 10) !important;
}

button.btn-secondary-offre {
  background-color: #86ae63 !important;
  color: white;
  // border-color: #82b952 !important;
}

button.btn-info-offre:hover {
  background-color: #5fb5ce !important;
  color: white;
  border-color: #5fb5ce !important;
}

.btn-secondary-offre {
  background-color: #e1f9cc !important;
  color: #537733;
  border-color: #82b952 !important;

  .form-control {
    border: 1px solid #82b952 !important;
  }
}

button.btn-secondary-offre:hover {
  background: #86ae63 !important;
  color: white !important;
  transition: all .2s ease-in-out;
}

.breadcrumfix {
  background-color: white;
  position: fixed;
  top: 70px;
  width: auto;
  z-index: 4;
}

button:disabled {
  cursor: default;
  pointer-events: all !important;
}

.Actionbreadcrumfix {
  background-color: white;
  position: fixed;
  top: 69px;
  z-index: 4;
}

.btn-success-offre {
  background-color: rgb(163, 167, 170);
  color: #fff;
  border-color: rgb(163, 167, 170);
}

button.btn-success-offre:focus,
button.btn-info-offre:focus,
button.btn-secondary-offre:focus {
  outline: none !important;
  box-shadow: none !important;
}

button.btn-success-offre:hover {
  background: rgb(163, 167, 170);
  color: white !important;
  transition: all .2s ease-in-out;
}

.btn-duplique {
  background-color: #bf5de6;
  color: #fdfdfd;
  border: none;
}

.btn-duplique:hover {
  background-color: #a842d1;
  color: #fcfcfc;
  border: none;
}

.bg-darkcus {
  background: $bg-leftbar-dark;
}

.half-top-palier,
.half-bottom-palier {
  font-weight: bold;
  border: 1px solid #fff;
}

.half-top-palier {
  color: var(--winoffre-text-light-shade);
  background-color: var(--win-offre-bg-light);
}

.half-bottom-palier {
  background: var(--win-offre-bg-action-tint-2);
  color: var(--winoffre-text-light-shade);
}

.palier-selected {
  // border: 1px solid #08b382 !important;
  background: #0acf97;
  font-weight: bold;
  color: white;
}

/*.palier-unselected {
  // border: 4px solid #fff;
}*/

.palier-invalid {
  border: 2px solid #fa5c7c !important;
}

.badQte .columnQte {
  background-color: rgba(255, 165, 0, 0.4);
  padding: 5px 10px 5px 10px;
  border-radius: var(--winoffre-base-border-radius);
}

.column-success {
  background: rgba(10, 207, 151, 0.2) !important;
  padding: 5px 10px 5px 10px;
  border-radius: var(--winoffre-base-border-radius);
}

.column-error {
  background: rgba(250, 92, 124, 0.2) !important;
  padding: 5px 10px 5px 10px;
  border-radius: var(--winoffre-base-border-radius);
}

.column-warning {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  background: rgba(255, 165, 0, 0.4) !important;
  padding: 5px 10px 5px 10px;
  border-radius: var(--winoffre-base-border-radius);
}


.increase-popover-width-warning {
  font-size: 1rem;
  font-weight: 600;
  max-width: 400px;
  width: 200px !important;
  background-color: rgb(250, 195, 92);
  border-color: rgb(182, 104, 15);
}

.editstyle {
  width: 100%;
  padding: 10px;
}

.btn-swap {

  background-color: #43b2cf;
  color: #fcfcfc;
  ;
  border-color: #64d8f5;
}

.btn-swap:hover {
  background-color: #1291b1;
  color: #fcfcfc;
  border-color: #64d8f5;
}

ngb-carousel {
  width: inherit;
  height: inherit;
}

.carousel-inner {
  overflow: visible;
  height: 100%;
  background-color: #ffffff;
  color: #ebecf1;
}

.carousel-control-prev {
  z-index: 20;
}


.carousel-control-next {
  z-index: 20;
}

.carousel-indicators {
  z-index: 20;
}

.carousel-item {
  display: block !important;
  z-index: 1;
  position: absolute;
  transition: opacity 1.5s ease-in-out, visibility 0.5s;
  opacity: 0;
  visibility: hidden;
}

.carousel-item.active {
  position: relative;
  transition: opacity 1.5s ease-in-out, visibility 0.5s;
  visibility: visible;
  opacity: 1;
}



.pointer-active {
  cursor: pointer;
}


.pointer-disabled {
  cursor: none;
  pointer-events: none;
}

.opacity-0 {
  opacity: 0;
}



.card-image-height {

  height: 200px;
  overflow: hidden;
  position: relative;

  min-height: 150px;


  .badge-danger,
  .badge-success,
  .badge-info {
    position: relative;

    border-radius: 0;
    z-index: 1;

  }

  .text-dangercus {
    color: $danger;
  }

  .card-img-logo {

    top: 10%;
    max-width: 130px;
    left: 7%;
    max-height: 35px;
  }
}


.card-cus-pack {

  &>.card {
    margin-bottom: .5rem;
    padding-bottom: .5rem;
    background: #fff;

    .card-header {
      padding-left: 14px;
      padding-right: 14px;
      border-bottom: none !important;
    }
  }

  .card .collapse .card-body {
    padding: 0;
  }

}

footer.footer {
  background: white;
  z-index: 5;
}

.custom-tooltip {
  left: 0px !important;

  .tooltip-inner {
    background-color: #276578;
    font-size: 125% !important;
    padding: 6px 15px !important;
  }
}

.cstm-tooltip-container {
  .tooltip-inner {
    min-width: 310px !important;
  }
}

::ng-deep .custom-tooltip .bs-tooltip-end .arrow::before {
  border-bottom-color: red !important;
  /* black */
  border-top-color: red !important;
  /* black */
  border-left-color: red !important;
  /* black */
  border-right-color: red !important;
}

.bgimg {
  border: 1px dashed red !important;

  img {
    height: 100px;
  }
}

.fixed-width-badge {
  width: 120px;
  text-align: center;
  display: inline-block;
}

.fixed-width-badge-reclamation {
  width: 100px;
  text-align: center;
  display: inline-block;
}

kendo-multiselect.k-input-md.k-rounded-md.k-input-solid.k-multiselect.k-input.ng-valid.ng-dirty.ng-touched {
  border: 1px solid #ddd !important;
  min-height: 32px !important;
  padding: 0px !important;
}

.pack-check-input {
  margin-left: 10px !important;
  margin-right: 4px !important;
}

.labo-label {
  color: #9d092e !important;
}

.grossiste-label {
  color: #ffa500 !important;
}

.img-picker-1 {
  content: url('../images/logo/imageicon.png');
}

.img-picker-2 {
  content: url('../images/logo/SATP_Aa_square-2x.png');
}

.left-homelandingcontup {
  display: grid;
  place-items: center;
}

.blurred-img {
  position: absolute;
  top: 0;
  left: 0;
  background: url("../images/bghomeleft.png") no-repeat;
  background-size: auto;
  z-index: 0;
  width: 100%;
  height: 100%;
  background-position: top right;
}

.loginformbg {
  background: url("../images/righthomebg.svg") no-repeat;
  background-size: contain;
  background-position: center;
}

.loginform {
  display: grid;
  place-content: center;
}

.formcard {
  .card {
    max-width: 400px;
    margin: 0 auto;
  }
}

.loginformbg {
  background: url("../images/righthomebg.svg") no-repeat;
  background-size: contain;
  background-position: center;
}

.card-body-cus {
  padding: .25rem .7rem .25rem .35rem;
  width: 100%;
}


.k-grid.fs-listing-grid td {
  white-space: unset;
  color: #3D3D3D;
}

::ng-deep #WIN_GROUPE-container .k-grid.fs-grid .k-grid-footer {
  padding: 0px 0px 0px 0px !important;
  background-color: var(--fs-group-grid) !important;
  color: #fff !important;
}

::ng-deep #WIN_GROUPE-container .k-grid.fs-grid .k-grid-footer tr:hover{
  background-color: var(--fs-group-grid) !important;
  color: #fff !important;
}

::ng-deep #FEDERATION_SYNDICAT-container .k-grid.fs-grid .k-grid-footer {
  padding: 0px 0px 0px 0px !important;
  background-color: var(--fs-grid-primary) !important;
  color: #fff !important;
}

::ng-deep #FEDERATION_SYNDICAT-container .k-grid.fs-grid .k-grid-footer tr:hover{
  background-color: var(--fs-grid-primary) !important;
  color: #fff !important;
}

.k-grid.fs-grid .k-grid-footer tr td {
  color: #fff !important;
}


iframe {
  width: -webkit-fill-available !important;
}

.select2.select2-container.select2-container--default.select2-container--focus.select2-container--below.select2-container--disabled {
  .select2-selection__rendered {
    //background-color: #E9ECEF !important;
    border-radius: var(--winoffre-base-border-radius) !important;
    font-size: 1rem;
    font-weight: 600;
    color: black !important;
    overflow: hidden !important;
  }
}


.select2-selection--multiple:before {
  content: "";
  position: absolute;
  right: 7px;
  top: 42%;
  border-top: 5px solid #888;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
}


.select2-selection.select2-selection--single {
  border-radius: var(--winoffre-base-border-radius) !important;
  font-size: 1rem;
  font-weight: 600;
  color: black !important;
}

.badge {
  font-size: 1rem;
}

.page-title-box,
.modal-footer {
  .btn {
    font-size: 1rem;
    border-radius: var(--winoffre-base-border-radius);
    font-weight: 600;
  }
}

#FEDERATION_SYNDICAT-container, #WIN_GROUPE-container {
  @media (max-width: 1000px) {
    .page-title-box {
      padding-right: 30px !important;
    }
  }
}


swiper-container::part(bullet-active) {
  background-color: #f3f3f3;
}

swiper-container::part(bullet) {
  background-color: #cdcdcd;
}

swiper-container::part(button-prev) {
  width: 25px;
  height: 20px;
  margin-left: 10px;
  padding: 6px 4px;
  border-radius: 10px;
  background: var(--fs-primary-shade);
  color: #fff !important;
}

swiper-container::part(button-next) {
  width: 25px;
  height: 20px;
  padding: 6px 4px;
  border-radius: 10px;
  background: var(--fs-primary-shade);
  color: #fff !important;
}

#pharmalien-swiper {
  swiper-container::part(button-prev) {
    width: 25px;
    height: 20px;
    margin-left: 10px;
    padding: 6px 4px;
    border-radius: 10px;
    background: var(--wo-secondary);
    color: #fff !important;
  }

  swiper-container::part(button-next) {
    width: 25px;
    height: 20px;
    padding: 6px 4px;
    border-radius: 10px;
    background: var(--wo-secondary);
    color: #fff !important;
  }

  swiper-container::part(bullet-active) {
    background-color: #f29d1e;
  }

  swiper-container::part(bullet) {
    background-color: #d98200;
  }

  swiper-container::part(pagination) {
    text-align: center !important;
  }
}

#win-offre-swiper {
  swiper-container::part(button-prev) {
    width: 25px;
    height: 20px;
    margin-left: 10px;
    padding: 6px 4px;
    border-radius: 10px;
    background: var(--wo-secondary);
    color: #fff !important;
  }

  swiper-container::part(button-next) {
    width: 25px;
    height: 20px;
    padding: 6px 4px;
    border-radius: 10px;
    background: var(--wo-secondary);
    color: #fff !important;
  }

  swiper-container::part(bullet-active) {
    background-color: #f29d1e;
  }

  swiper-container::part(bullet) {
    background-color: #d98200;
  }

  swiper-container::part(pagination) {
    text-align: center !important;
  }
}

#login-swiper {
  swiper-container::part(bullet-active) {
    background-color: #f3f3f3;
    width: 25px !important;
    border-radius: 15px !important;
  }

  swiper-container::part(bullet) {
    background-color: #cdcdcd;
  }

  swiper-container::part(pagination) {
    text-align: left !important;
  }
}





.actions-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 11px;
  height: 30px;
  width: 30px;
  flex-shrink: 0;

  i {
    font-size: 18px;
    line-height: 1;
    cursor: pointer;
  }
}

.actions-icons-ov {
  height: 25px !important;
  width: 25px !important;
  border-radius: 8px !important;
  
  i {
    font-size: .85rem !important;
  }
} 

.actions-icons-ov + span {
  font-size: .9rem !important;
  font-weight: 700 !important;
}



#WIN_GROUPE-container .action-back {
  background: var(--wf-primary-400) !important;
}

#FEDERATION_SYNDICAT-container .action-back {
  background: var(--fs-primary-500) !important;
}

// switch icons :

.status-switch-align.k-switch-on .k-switch-thumb {

  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: "\F26E";
    font-family: "Bootstrap-icons";
    color: var(--fs-success);
    font-size: 20px;
  }
}

.status-switch-align .k-switch-thumb {

  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: "\F62A";
    font-family: "Bootstrap-icons";
    color: var(--fs-secondary-text);
    font-size: 20px;
  }
}


.status-switch-align.k-switch-md {
  width: 55px;
}

.status-switch-align.k-switch-md .k-switch-track {
  width: 92px !important;
}






.actions-success {
  background: var(--fs-success) !important;
  border-color: var(--fs-success) !important;
}

.actions-danger {
  background: var(--fs-danger) !important;
  border-color: var(--fs-danger) !important;
}

.status-switch-align .k-switch-label-on {
  margin-top: 2px;
  margin-left: 3px;
}

.status-switch-align .k-switch-label-off {
  margin-top: 2px;
  margin-right: 3px;
}

// .k-grid .k-grid-header .k-header{
//   border: none;
//   background-color: #e6dafa;
//   padding-block: 10px;
//   color: #000;
// }
// .k-grid .k-grid-header .k-header a{
//   color: white;
// }
// .k-grid tr {
//   cursor: pointer;
// }

// .k-grid-header {
//   border: none !important;
// }
// .k-header th {
//   border: none !important;
// }


// .k-grid td,
// .k-grid th {
//   border-left: none !important;
// }

.k-grid.fs-grid .k-icon.k-i-sort-asc-small {
  display: none;
}

.k-grid.fs-grid .k-icon.k-i-sort-desc-small {
  display: none;
}

.k-grid.fs-grid .k-icon.k-i-sort-asc-small {
  color: #fff;
}

.k-grid.fs-grid .k-icon.k-i-sort-desc-small {
  color: #fff;
}

// ? WIN_GROUPE KENDO-GRID STYLING START
#WIN_GROUPE-container {
  .k-grid.fs-grid tr:not(.k-footer-template):hover {
   background-color: var(--fs-group-grid);
  }
  
  .k-grid.fs-grid .k-grid-header .k-header {
   border: none;
   background-color: var(--fs-group-grid);
   color: #fff;
   padding-block: 12px;
  
   &:hover {
     background-color: var(--wf-primary-600);
   }
  }
  .k-grid.fs-grid.fs-grid-white{
     border: 2px solid var(--fs-group-grid) !important;
  }
  .k-grid.fs-grid.fs-grid-white .k-grid-header .k-header {
   border-bottom: 3px solid var(--fs-group-grid) !important;
   background-color: #fff;
   color: #000;
   padding-block: 12px;
  
   &:hover {
     background-color: rgb(237, 237, 237);
   }
  }
  
  .k-grid.fs-grid.fs-grid-white .k-master-row td{
   border-bottom: 2px solid var(--fs-group-grid) !important;
  
  }
  
  .k-grid.fs-grid.fs-grid-white tr th{
   border-right: 2px solid var(--fs-group-grid) !important;
  
  }
  .k-grid.fs-grid.fs-grid-white tr th:first-child {
   border-right: 2px solid var(--fs-group-grid) !important;
   border-left: 2px solid var(--fs-group-grid);
  
  }
  
  .k-grid.fs-grid.fs-grid-white tr td:first-child {
   border-left: 2px solid var(--fs-group-grid);
  }
  // .k-grid.fs-grid.fs-grid-white tr td:nth-child(2){
  //   border-right: 2px solid var(--fs-group-grid) !important;
  
  // }
  .k-grid.fs-grid.fs-grid-white tr td {
   border-right: 2px solid var(--fs-group-grid) !important;
  
  }

  .k-grid.fs-grid.bl-grid .k-grid-header tr:first-child th {
    border: none;
    background-color: var(--fs-group-grid);
    color: #fff;
    padding-block: 12px;
  
    &:hover {
      background-color: transparent;
    }
  }

  .fs-grid .k-grid-toolbar{
    background-color: var(--fs-group-grid);
    border-bottom: none;
  }

  .k-grid.fs-grid *::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 0 15px var(--fs-group-grid,indigo);
  }

  .k-grid.fs-grid.fs-grid-white .end-phase {
    border-right: 6px solid var(--fs-group-grid) !important;
  }
  .k-grid.fs-grid.fs-grid-white tr th.end-phase {
    border-bottom :none !important;
    border-right: 6px solid var(--fs-group-grid) !important;
  }
  
  
  .k-grid.fs-grid.fs-grid-white .end-phase-member:not(:last-child) {
    border-right: 6px solid var(--fs-group-grid) !important;
  }

  .k-grid.fs-grid .k-grid-footer tr td:not(.odd-row) {
    color: #fff !important;
    background-color: var(--fs-group-grid) !important;
  }

  .card-view {
    padding-right:8px !important ;
    &::-webkit-scrollbar{
      width: 13.5px;
        height: 13.5px;
        border-radius: 5px;
        background-clip: padding-box;
        background-color: transparent !important;
        margin-right: 0 !important;
        padding-right: 0 !important;
    }
    &::-webkit-scrollbar-thumb {
      width: 13.5px;
      height: 13.5px;
      background: var(--fs-group-grid,indigo);
      border-radius: 20px;
     }
    &::-webkit-scrollbar-track {
      border-radius: 20px;
      width: 10px !important;
      padding-inline: 5px;
  
    }
  }
  
}
// ? WIN_GROUPE KENDO-GRID STYLING END

// ? FEDERATION_SYNDICAT KENDO-GRID STYLING START
#FEDERATION_SYNDICAT-container {
  .k-grid.fs-grid tr:not(.k-footer-template):hover {
   background-color: var(--fs-grid-primary);
  }
  
  .k-grid.fs-grid .k-grid-header .k-header {
   border: none;
   background-color: var(--fs-grid-primary);
   color: #fff;
   padding-block: 12px;
  
   &:hover {
     background-color: #6161a1;
   }
  }
  .k-grid.fs-grid.fs-grid-white{
     border: 2px solid var(--fs-grid-primary) !important;
  }
  .k-grid.fs-grid.fs-grid-white .k-grid-header .k-header {
   border-bottom: 3px solid var(--fs-grid-primary) !important;
   background-color: #fff;
   color: #000;
   padding-block: 12px;
  
   &:hover {
     background-color: rgb(237, 237, 237);
   }
  }
  
  .k-grid.fs-grid.fs-grid-white .k-master-row td{
   border-bottom: 2px solid var(--fs-grid-primary) !important;
  
  }
  
  .k-grid.fs-grid.fs-grid-white tr th{
   border-right: 2px solid var(--fs-grid-primary) !important;
  
  }
  .k-grid.fs-grid.fs-grid-white tr th:first-child {
   border-right: 2px solid var(--fs-grid-primary) !important;
   border-left: 2px solid var(--fs-grid-primary);
  
  }
  
  .k-grid.fs-grid.fs-grid-white tr td:first-child {
   border-left: 2px solid var(--fs-grid-primary);
  }
  // .k-grid.fs-grid.fs-grid-white tr td:nth-child(2){
  //   border-right: 2px solid var(--fs-group-grid) !important;
  
  // }
  .k-grid.fs-grid.fs-grid-white tr td {
   border-right: 2px solid var(--fs-grid-primary) !important;
  
  }

  .k-grid.fs-grid.bl-grid .k-grid-header tr:first-child th {
    border: none;
    background-color: var(--fs-grid-primary);
    color: #fff;
    padding-block: 12px;
  
    &:hover {
      background-color: transparent;
    }
  }

  .fs-grid .k-grid-toolbar{
    background-color: var(--fs-grid-primary);
    border-bottom: none;
  }

  .k-grid.fs-grid *::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 0 15px var(--fs-grid-primary, indigo);
  }

  .k-grid.fs-grid.fs-grid-white .end-phase {
    border-right: 6px solid var(--fs-grid-primary) !important;
  }
  .k-grid.fs-grid.fs-grid-white tr th.end-phase {
    border-bottom :none !important;
    border-right: 6px solid var(--fs-grid-primary) !important;
  }
  
  
  .k-grid.fs-grid.fs-grid-white .end-phase-member:not(:last-child) {
    border-right: 6px solid var(--fs-grid-primary) !important;
  }

  .k-grid.fs-grid .k-grid-footer tr td {
    color: #fff !important;
    background-color: var(--fs-grid-primary) !important;
  }
  

  .card-view {
    padding-right:8px !important ;
    &::-webkit-scrollbar{
      width: 13.5px;
        height: 13.5px;
        border-radius: 5px;
        background-clip: padding-box;
        background-color: transparent !important;
        margin-right: 0 !important;
        padding-right: 0 !important;
    }
    &::-webkit-scrollbar-thumb {
      width: 13.5px;
      height: 13.5px;
      background: var(--fs-grid-primary,indigo);
      border-radius: 20px;
     }
    &::-webkit-scrollbar-track {
      border-radius: 20px;
      width: 10px !important;
      padding-inline: 5px;
  
    }
  }
  
}
// ? FEDERATION_SYNDICAT KENDO-GRID STYLING END

.fs-listing-grid{
  tr td{
    border-left: 0.2px solid  #7080d754  !important;
    // border-right: 0.5px solid var(--fs-group-grid) !important;
    border-bottom: 0.2px solid  #7080d754  !important;
    // border-top: 0.5px solid var(--fs-group-grid) !important;
  }
}

.k-grid.fs-grid.bl-grid .k-cell-inner {
  justify-content: center;
}

.k-grid.fs-grid .k-grid-header .k-header a {
  color: white;
}

.k-grid.fs-grid.fs-grid-white td {
  padding-block: 3px ;
}

.k-grid.fs-grid tr {
  cursor: pointer;
}

.k-grid.fs-grid td {
  padding-block: 6px !important;
}

.k-grid.fs-grid .k-grid-header {
  border: none !important;
}

.k-grid.fs-grid .k-header th {
  border: none !important;
}



.k-grid.fs-grid th {
  vertical-align: middle !important;
}

// Modal


.fs-modal {

  border-radius: 10px !important;

  .success-text {
    font-size: 30px !important;
    font-weight: 800;
    color: var(--wf-primary-400);
  }

  .message-text {
    font-size: 18px !important;
    font-weight: 600;
    color: #696C75;
  }
}

.fs-modal-content .modal-content {
  background: transparent !important;
}

.cross-button {
  position: absolute;
  right: 4px;
  top: 5px;
  cursor: pointer;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #e3d9d9;

  i {
    font-size: 30px;
    color: #4d4949;
  }

  &:hover {
    color: var(--fs-danger);
    background: #e6dafa;
  }
}


.btn-fs-outline-success {
  color: var(--fs-success);
  border-color: var(--fs-success);
  box-shadow: none;

  &:hover {
    box-shadow: none;
    color: white;
    background: var(--fs-success);
  }

  & &:focus {
    box-shadow: none;
    color: white;
    background: var(--fs-success);
  }

  &:active {
    box-shadow: none !important;
    color: white !important;
    background: var(--fs-success) !important;
  }
}

.btn-fs-outline-dark {
  color: #7C7F86;
  border-color: #696C75;
  border-width: 2px;
  box-shadow: none;
  background: transparent;

  &:hover {
    box-shadow: none;
    color: white;
    background: #696C75;
  }


  &:active {
    box-shadow: none !important;
    color: white !important;
    background: #404246;
  }
}


.btn-fs-confirm {
  color: white;
  background: #EE8245;
  border-color: none;
  box-shadow: none;
  padding-inline: 20px;
  border-radius: 5px;

  &:hover {
    box-shadow: none;
    color: white;
    background: #EE8245;
  }


}

.fs-cstm-modal .modal-content {
  border-radius: 10px;
}

.btn-fs-cancel {
  color: #000;
  border-color: #696C75;
  box-shadow: none;
  padding-inline: 20px;
  border-radius: 5px;
  background: transparent;

  &:hover {
    box-shadow: none;
    color: white;
    background: #696C75;
  }
}

.step-item.dropdown {
  display: none;
  width: 45px;
  background: #e2e2e2;

  .nav-label.dropdown-toggle:after {
    display: none;
  }

  & li .nav-label {
    background: #fff;
  }

  & li .nav-label:after {
    display: none;
  }
}

#export-pdf-form {
  #tableColumns {
    .select2-container {
      .select2-selection__choice {
        background-color: var(--wf-primary-500) !important;
      }
    }

    .select2-container--default {
      .select2-results__option--highlighted[aria-selected] {
        background-color: var(--wf-primary-500) !important;
      }
    }
  }

  #commandeWebTableColumns {
    .select2-container {
      .select2-selection__choice {
        background-color: var(--cw-primary-700) !important;
      }
    }

    .select2-container--default {
      .select2-results__option--highlighted[aria-selected] {
        background-color: var(--cw-primary-700) !important;
      }
    }
  }

}

.k-grid.fs-grid .k-grid-content {
  scrollbar-width: auto !important;
  scrollbar-color: inherit !important;
}


.k-grid.fs-grid *::-webkit-scrollbar{
  width: 13.5px;
    height: 13.5px;
    border-radius: 5px;
    background-clip: padding-box;
    background-color: transparent !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
}
.k-grid.fs-grid *::-webkit-scrollbar-thumb {
  width: 13.5px;
  height: 13.5px;
 }
.k-grid.fs-grid *::-webkit-scrollbar-track {
  border-radius: 20px;
  width: 10px !important;

}

.btn-fs-size{
  padding-block: 6px !important;
}


.k-grid .k-grid-aria-root {
  overflow: hidden !important;
}


.has-scrollbar.k-grid-header, .has-scrollbar.k-grid-footer {
  padding : 0px 13px 0px 0px !important;
  background: transparent !important;
}
.no-footer-scoller.k-grid-footer {
  padding : 0px 0px 0px 0px !important;
  background: transparent !important;
}

.dispatch-grid.k-grid-footer {
  padding : 0px 0px 0px 0px !important;
  background: transparent !important;
}


.k-grid.fs-grid.fs-grid-white .even-row {
  background-color: #f8f9fa !important;
  color: #000 !important;
}

.k-grid.fs-grid.fs-grid-white .odd-row {
  background-color: #e0e4f1 !important;
  color: #000 !important;
}

.k-grid.fs-grid.fs-grid-white th.bg-even {
  background-color: #ffd483 !important;
}

.k-grid.fs-grid.fs-grid-white th.bg-odd {
  background-color: #a3c2a4 !important;
}

.k-grid.fs-grid.fs-grid-white .bg-even {
  background-color: #ffe8bd !important;
  color: #000 !important;
}

.k-grid.fs-grid.fs-grid-white .bg-odd {
  background-color: #d3f4d5 !important;
  color: #000 !important;
}

.k-grid.fs-grid.fs-grid-white .bg-even-dark {
  background-color: #efb54a !important;
  color: #000 !important;
}

.k-grid.fs-grid.fs-grid-white .bg-odd-dark {
  background-color: #315A32 !important;
  color: #000 !important;
}


.cadeau-row, .cadeau-row.k-alt {
  background-color: #d1d6eb !important;
  color: #000 !important;
  &:hover{
    background-color: #d1d6eb !important;
    color: #000 !important;
  }
}

.authentication-bg-fs{
  background: url(/assets/images/reset-bg-pattern.png) center no-repeat !important;
}

.k-grid.ph-grid .k-grid-header tr:nth-child(2) th {
  background-color: transparent !important;
  color: black !important;
}

.k-grid.ph-grid .k-grid-header tr th.ph-header  {
  .k-cell-inner > .k-link {
    justify-content: center !important;
  }

  .k-column-title {
    font-size: 1.2rem;
  }
}

.k-i-expand,
.k-i-collapse {
  cursor: pointer;
  border: none;
  background: none;
}

.k-i-collapse::before {
  content: '\e9bf'; /* Kendo UI collapse icon */
}

.k-i-expand::before {
  content: '\e9c0'; /* Kendo UI expand icon */
}

.hidden-row {
  display: none !important;
}


.custom-modal-width .modal-dialog {
  max-width: 95%;
}

.no-ellipsis {
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

.grid-top-radius {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}


#WIN_OFFRE-container {
  .k-grid.fs-grid.fs-grid-white tr th:first-child {
    border-left: none !important;

  }

  .k-grid.fs-grid.fs-grid-white tr td:first-child {
    border-left: none !important;
  }
}

.cmd-individuelle {
  background-color: #393c9b !important;
  margin-bottom: 5px !important;

  &:not(:has(.side-nav-second-level)) {
    &:hover,
    &:focus, &:active {
      background: #a2a6f1 !important;
    }
  }
  .side-nav-second-level {
    li {
      &:hover,&:focus, &:active {
        background-color: #a2a6f1 !important;
        border-radius: 10px;

      }

      a.side-nav-link-ref.activated {
        background: #a2a6f1 !important;
        border-radius: 10px;
      }

    }
  }


  &:hover i, &:focus i{
    color:rgb(255, 255, 255) !important;
  }
}

.guid-cmd-web {
  background-color: var(--win-offre-bg-action-shade-1) !important;
  margin-bottom: 5px !important;

  &:not(:has(.side-nav-second-level)) {
    &:hover,
    &:focus, &:active {
      background: var(--win-offre-primary-tint) !important;
    }
  }
  .side-nav-second-level {
    li {
      &:hover,&:focus, &:active {
        background-color: var(--win-offre-primary-tint) !important;
        border-radius: 10px;

      }

      a.side-nav-link-ref.activated {
        background: var(--win-offre-primary-tint) !important;
        border-radius: 10px;
      }

    }
  }


  &:hover i, &:focus i{
    color:rgb(255, 255, 255) !important;
  }
}

.dashboard-grid .k-grid-toolbar{
  background-color: #fff !important;
  overflow: visible !important;
}

.dashboard-modal-container {
  width: 80vw !important;
  max-width: inherit !important;
  @media screen and (max-width: 768px) {
    width: 95vw !important;
  }
}

.dashboard-modal .modal-content {
  border-radius: 10px;
}

.char-count {
  font-size: 0.75rem;
  color: #b4b4b4;
}

.char-count-pos {
  position: absolute;
  right: 10px;
  bottom: -5px;
}

.invalid-ipt {
  border: 1px solid #FF0000 !important;
}

.cstm-ul {
  li {
    text-align: start !important;
  }
}

// ? Serveur Indisponible modal styling
.fs-radius-modal .modal-content{
  border-radius: 20px !important;
}

.fs-radius-modal .maintenance-title{
  z-index: 1;
  background-image: linear-gradient(90deg, var(--wf-primary-500),var(--wf-primary-200));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.fs-radius-modal .maintenance-title-wo {
  z-index: 1;
  background-image: linear-gradient(90deg, #d57b0a, #f0a10f);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.fs-radius-modal .maintenance-title-fs {
  z-index: 1;
  background-image: linear-gradient(90deg, var(--fs-primary-500),var(--fs-primary-200));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.fs-radius-modal .illustration-maintenance{
margin-top: -80px;
}

//? Help modal styling
.help-modal-container {
  position: fixed;
  right: 0;
  top: 0;
  margin: 0 !important;
  width: 30vw;
  transform: translateX(220px);
  opacity: 0;
  animation: fadeInAnim 0.5s forwards;

  @media (max-width: 576px) {
    width: 95vw !important;
  }

  @media (min-width: 577px) {
    width: 65vw !important;
  }

  @media (min-width: 768px) {
    width: 55vw !important;
  }

  @media (min-width: 992px) {
    width: 45vw !important;
  }

  @media (min-width: 1200px) {
    width: 35vw !important;
  }

  @media (min-width: 1380px) {
    width: 30vw !important;
  }
}

.help-modal-window .modal-content {
  height: 100vh;
  border-radius: 10px;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.help-modal-window .modal-header {
  background: #393c9b !important;

}

.help-modal-window .modal-body {
  overflow-y: auto;
}

@keyframes fadeInAnim {
  0% {
    opacity: 0;
    transform: translateX(220px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.hidden-container {
  left: -100vw !important;
  visibility: hidden !important;
  position: absolute !important;
  z-index: -99 !important;
}

.external-link {
  color: #00a6af;
  &:hover {
      color: #00a6af !important;
      text-decoration: underline;
  }
}

#WIN_OFFRE-container {
  .highlight-row-wo, .highlight-row-wo:hover, .highlight-row-wo:active, .highlight-row-wo:focus{
    background-color: var(--cw-primary-100) !important;
    transition: all .5s ease-in-out;
  }
}

#FEDERATION_SYNDICAT-container {
  .highlight-row-wo, .highlight-row-wo:hover, .highlight-row-wo:active, .highlight-row-wo:focus{
    background-color: var(--fs-primary-100) !important;   
    transition: all .5s ease-in-out;
  }
}

#WIN_GROUPE-container {
  .highlight-row-wo, .highlight-row-wo:hover, .highlight-row-wo:active, .highlight-row-wo:focus{
    background-color: var(--wf-primary-100) !important;   
    transition: all .5s ease-in-out;
  }
}

.highlight-row-temp {
  background-color: var(--wf-primary-100) !important;   
}

.pharmalien-dashboard-footer {
  font-weight: 800 !important;
  font-size: 1rem !important;
  background: #FDA403 !important;
}

.fs-montant-total-membre-footer {
  color: #000 !important;
  background: transparent !important;
}

.fs-montant-total-membre-no-border {
  border: none !important;
}

.cstm-popover-remises {
  max-width: 380px !important;
  width: auto !important;
  // z-index: 9999 !important;
}


.cstm-tooltip-remises {
  opacity: 1 !important;
  z-index: 99 !important;
}

.cstm-tooltip-remises .tooltip-inner {
  background: var(--wo-primary-100);
  padding: .35rem !important;

  max-width: 380px !important;
  width: auto !important;
}

.cstm-tooltip-remises-fs .tooltip-inner {
  background: var(--wf-primary-100) !important;
}

.cstm-popover-close {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 6px !important;
  border-radius: 10px;
  color: #fff;
  font-size: .7rem;
  font-weight: 600;
  background: #000;
  position: absolute; 
  top: -5px; 
  right: -2px; 
  z-index: 9999;

  i {
    font-size: 1rem;
  }
}

.remises-row-close {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 6px;
  padding-right: 10px !important;
  border-radius: 10px;
  color: #fff;
  font-size: .7rem;
  font-weight: 600;
  background: #000;
  position: absolute; 
  top: 50px; 
  right: 5px; 
  z-index: 1;
  i {
    font-size: .85rem;
    margin-right: 5px;
  }
}

.bg-cstm-info-wf {
  margin: 5px 0;
  font-size: .7rem !important;
  font-weight: 800;
  background: #FCBB01;
  color: #000000 !important;
  border-width: 2px;
  border-radius: 8px;
  border-color: #FCBB01;
  border-style: solid;
  cursor: pointer;

  span > i {
    font-size: .6rem;
    margin-left: 5px;
  }

  &:hover {
    background: #fff;
    color: #000000 !important;
    border-width: 2px;
    border-color: #FCBB01;
    border-style: solid;
  }
}

.bg-cstm-info-fs {
  margin: 5px 0;
  font-size: .7rem !important;
  font-weight: 800;
  background: var(--fs-primary-600);
  color: #fff !important;
  border-width: 2px;
  border-radius: 8px;
  border-color: var(--fs-primary-600);
  border-style: solid;
  cursor: pointer;

  span > i {
    font-size: .6rem;
    margin-left: 5px;
  }

  &:hover {
    background: #fff;
    color: var(--fs-primary-600) !important;
    border-width: 2px;
    border-color: var(--fs-primary-600);
    border-style: solid;
  }
}

.bg-cstm-info-wo {
  margin: 5px 0;
  font-size: .7rem !important;
  font-weight: 800;
  background: var(--wo-secondary);
  color: #fff !important;
  border-width: 2px;
  border-radius: 8px;
  border-color: var(--wo-secondary);
  border-style: solid;
  cursor: pointer;

  span > i {
    font-size: .6rem;
    margin-left: 5px;
  }

  &:hover {
    background: #fff;
    color: var(--wo-secondary) !important;
    border-width: 2px;
    border-color: var(--wo-secondary);
    border-style: solid;
  }
}


.truncate-two-lines {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.truncate-one-line {
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.collapse-all {
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  border: none !important;
  background: var(--wf-secondary);

  label {
    cursor: pointer;
    font-weight: 700 !important;
    color: black !important;
  }
}

.modal-link-label, .modal-link-label:hover {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--wf-primary-600);
  text-decoration: underline;
}

#WIN_GROUPE-container {
  .collapse-expand-icon {
    background: var(--wf-secondary);
    border-radius: 10px;
    padding: 2px 6px;
  
    i {
      font-size: 1.1rem;
      color: #fff;
    }
  }
}


#WIN_OFFRE-container {
  .collapse-expand-icon {
    background: var(--wo-secondary);
    border-radius: 10px;
    padding: 2px 6px;
  
    i {
      font-size: 1.1rem;
      color: #fff;
    }
  }
}

#FEDERATION_SYNDICAT-container {
  .collapse-expand-icon {
    background: var(--fs-primary-500);
    border-radius: 10px;
    padding: 2px 6px;
  
    i {
      font-size: 1.1rem;
      color: #fff;
    }
  }
}

.invalid-input{
  border: 2px solid #FF0000 !important;
}

.custom-tooltip-style .tooltip-inner {
  background-color: var(--cw-primary-600); 
  color: #ffffff;     
  font-size: .76rem !important; 
  font-weight: 800;   
  border: 1px solid var(--cw-primary-600); 
  border-radius: .25rem;
  padding: .5rem .75rem;
}

.custom-tooltip-style .arrow::before {
  border-bottom-color: var(--cw-primary-500);
}

.disponibilite-produit-modal .modal-content {
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  max-height: calc(100vh - (env(safe-area-inset-top) + env(safe-area-inset-bottom)));
  width: auto;
  min-width: 35vw;
  max-width: 450px;

  .modal-body {
    max-height: calc(100vh - 130px);
    overflow-y: auto;
  }

  .modal-footer {
    background: #fff;
    position: absolute;
    bottom: 0;
    width: 100%;
  }
} 

.demande-acces-modal .modal-content {
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  max-height: calc(100vh - (env(safe-area-inset-top) + env(safe-area-inset-bottom)));
  width: 80vw;

  .modal-body {
    max-height: calc(100vh - 130px);
    overflow-y: auto;
  }

  .modal-footer {
    background: #fff;
    position: absolute;
    bottom: 0;
    width: 100%;
  }
}

@media (max-width: 991px) {
  .demande-acces-modal .modal-content, .disponibilite-produit-modal .modal-content {
    width: 100vw;
  }
}

@media (min-width: 992px) {
  .demande-acces-modal .modal-content, .disponibilite-produit-modal .modal-content {

    .modal-body {
      min-height: calc(100vh - 130px);
      max-height: none !important;
    }
  }
}

.cmd-web-card {
  background-image: var(--cmd-web-bg);
  background-color: #EFEFF1;
  border-radius: 10px;
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: bottom;
  padding-top: 30px !important;
  position: relative;
  width: 100%;
  margin-bottom: 10px !important;
}

.card-win-plus {
  background-image: var(--plateforme-selection-wp-bg);
  border-radius: 10px;
  background-size: 100% auto;
  background-color: #f0f2f5;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
}

.gradient_card {
  background-image: var(--plateforme-selection-materiel-soph);
  border-radius: 10px;
  background-size: 100% auto;
  background-color: #f0f2f5;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
}

.card-win-plus, .gradient_card, .cmd-web-card {
  height: calc((100vh - 205px) * 1/3);
  aspect-ratio: 445 / 190;
  width: 100%;
  overflow: hidden;

  button {
    z-index: 2;
    position: relative;
    font-size: 1.2rem !important;
  }

  // gradient overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background: linear-gradient(to bottom, rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.08));
    border-radius: 10px;
    z-index: 1;
  }
}

@media (max-width: 1366px) {
  .card-win-plus, .gradient_card {
    height: auto !important;
    min-height: 150px !important;
    background-size: contain !important;
  }
  
  .cmd-web-card {
    height: auto !important;
    min-height: 200px !important;
  }
}

.safe-height {
  min-height: calc(100vh - (123px + env(safe-area-inset-top) + env(safe-area-inset-bottom)));
}

.k-grid-content .extra-line-row ,
.k-grid-content .extra-line-row:hover{
      background-color: rgba(248, 172, 100, 0.555) !important;
}
.row-responsable {
  background: var(--wf-primary-100) !important;
}
