import { Component, OnInit, Template<PERSON>ef, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { Ga4DataService } from "../../services/ga4-data.service";
import { AnalyticsResponse, GA4_DIMENSION, GA4AnalyticsSearchCriteria, INTERACTION } from "@wph/data-access";
import { buildSeriesByDimension, prepareGoogleChartData } from "@wph/web/shared";
import { ApexOptions } from "ng-apexcharts";
import { FormBuilder, FormGroup } from "@angular/forms";
import { Moment } from "moment";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import * as moment from "moment";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { Subject } from "rxjs";
import { ChartType } from "angular-google-charts";

@Component({
    selector: "wph-web-ga4-visualization",
    templateUrl: "./ga4-visualization.component.html",
    styleUrls: ["./ga4-visualization.component.scss"],
})
export class Ga4VisualizationComponent implements OnInit, OnDestroy {
    gridData: GridDataResult = { data: [], total: 0 };
    filterOptions: FormGroup;
    chartOptions: ApexOptions;
    searchCriteria: GA4AnalyticsSearchCriteria;
    keyMetricsData: any = {};

    visualizationType: 'apex' | 'google' = 'google';
    googleChartData: any[][] = [];
    googleChartOptions: any = {};
    googleChartColumns: string[] | null = null;
    googleChartType: ChartType = ChartType.ColumnChart;
    INTERACTION = INTERACTION;

    chart1: { data: any[][], columns: string[], chartOptions: any } | null = null;
    chart2: { data: any[][], columns: string[], chartOptions: any } | null = null;
    chart3: { data: any[][], columns: string[], chartOptions: any } | null = null;

    selectInteractionType: any[] = [
        { label: 'Tout', value: null },
        { label: 'Vue', value: INTERACTION.VIEW },
        { label: 'Clic', value: INTERACTION.CLICK },
        { label: 'Survol', value: INTERACTION.HOVER },
    ];

    private destroy$ = new Subject<void>();
    private readonly baseSearchCriteria: GA4AnalyticsSearchCriteria = {
        startDate: "30daysAgo",
        endDate: "today",
        metrics: ["eventCount"],
        dimensions: [GA4_DIMENSION.CONTENT_INTERACTION_TYPE, GA4_DIMENSION.CONTENT_NAME],
        limit: 0,
        offset: 0,
    };

    constructor(private fb: FormBuilder, private modalService: NgbModal, private ga4DataService: Ga4DataService) {
        this.filterOptions = this.fb.group({
            contentName: [null],
            endDate: [moment()],
            interactionType: [null],
            startDate: [moment().subtract(30, 'days')],
        });

        this.searchCriteria = { ...this.baseSearchCriteria };

        this.googleChartOptions = {
            title: null,
            width: '100%',
            height: '100%',
            colors: ['#1abc9c', '#3498db', '#e74c3c', '#f1c40f', '#9b59b6', '#2ecc71'],
            legend: { position: 'none' },
            bar: { groupWidth: '75%' },
            vAxis: { format: 'short', gridlines: { count: 4 } },
        };
    }

    ngOnInit(): void {
        this.fetchBatchAnalyticsData();
    }

    get f() {
        return this.filterOptions.controls;
    }

    setVisualization(type: 'apex' | 'google'): void {
        this.visualizationType = type;
    }

    private fetchPageViewsDataGeneric(criteria: GA4AnalyticsSearchCriteria, startDate?: string, endDate?: string): Promise<AnalyticsResponse | null> {
        criteria.startDate = startDate || "28daysAgo";
        criteria.endDate = endDate || "today";

        return new Promise<AnalyticsResponse | null>((resolve, reject) => {
            this.ga4DataService.fetchGa4Data(criteria).subscribe((response) => {
                if (response && response.rowCount) {
                    resolve(response);
                } else {
                    reject(null);
                }
            });
        });
    }

    private processKeyMetricsResponse(response: AnalyticsResponse): void {
        const keyMetricValues = response?.rows[0]?.metricValues?.map(mv => mv.value) || [];
        moment.locale('fr');
        this.keyMetricsData = {
            activeUsers: keyMetricValues[0],
            newUsers: keyMetricValues[1],
            engagedSessions: keyMetricValues[2],
            averageSessionDuration: (moment.duration(keyMetricValues[3], "seconds").locale('fr')).humanize(),
            eventCount: keyMetricValues[4],
        };
    }

    private preparePageViewChartData(
        response: AnalyticsResponse,
        targetDimension: string = 'pageTitle',
        metricName: string = 'screenPageViews',
        seriesNameOverride: string | null = null,
        targetSplitDimension: string = 'eventName',
        useAlternateCategories: boolean = true
    ) {
        let { series, categories } = buildSeriesByDimension(
            response,
            targetDimension,
            targetSplitDimension,
            metricName,
        );

        if (seriesNameOverride) {
            series = series.map(s => ({ ...s, name: seriesNameOverride }));
        }

        if (!useAlternateCategories) {
            const translatedSeries = series.map((serie) => {
                switch (serie.name) {
                    case INTERACTION.VIEW:
                        serie.name = 'Vue';
                        break;
                    case INTERACTION.CLICK:
                        serie.name = 'Clic';
                        break;
                    case INTERACTION.HOVER:
                        serie.name = 'Survol';
                        break;
                    case INTERACTION.TOUCH:
                        serie.name = 'Touche';
                }
                return serie;
            });

            const gridReadyData = categories.map((item, index) => {
                const row = { name: item };
                translatedSeries.forEach((s) => {
                    row[s.name] = s.data[index] || 0;
                });
                return row;
            });

            this.gridData = { data: gridReadyData, total: gridReadyData.length };
        }

        // Prepare data for Google Charts
        let googleChartsData = prepareGoogleChartData(categories, series, 'Page Title', useAlternateCategories);

        googleChartsData = googleChartsData?.slice(0, 6);
        return { data: googleChartsData.slice(1), columns: googleChartsData[0], chartOptions: { ...this.googleChartOptions } };
    }

    openModal(content: TemplateRef<any>, size = 'md'): void {
        this.modalService.open(content, { size, centered: true, modalDialogClass: 'fs-radius-modal' });
    }

    appliquerFiltre(): void {
        const { startDate, endDate } = this.filterOptions.value;
        this.searchCriteria.filters = [];

        startDate && (this.searchCriteria.startDate = (startDate as Moment).format("YYYY-MM-DD"));
        endDate && (this.searchCriteria.endDate = (endDate as Moment).format("YYYY-MM-DD"));

        this.fetchBatchAnalyticsData(this.searchCriteria.startDate, this.searchCriteria.endDate);
    }

    viderFiltre(): void {
        this.filterOptions.reset({ startDate: moment().subtract(30, 'days'), endDate: moment(), interactionType: null });
        this.searchCriteria = { ...this.baseSearchCriteria };
        this.searchCriteria.filters = []; // Ensure filters are explicitly cleared

        this.fetchBatchAnalyticsData();
    }

    fetchBatchAnalyticsData(startDate?: string, endDate?: string): void {
        this.fetchPageViewsDataGeneric(new GA4AnalyticsSearchCriteria({
            metrics: ['activeUsers', 'newUsers', 'engagedSessions', 'averageSessionDuration', 'eventCount']
        }), startDate, endDate).then((response) => {
            this.processKeyMetricsResponse(response);
        });

        this.fetchPageViewsDataGeneric(new GA4AnalyticsSearchCriteria({
            metrics: ['screenPageViews'],
            dimensions: ['pageTitle', 'eventName'],
        }), startDate, endDate).then((response) => {
            this.chart1 = this.preparePageViewChartData(response);
        });

        this.fetchPageViewsDataGeneric(new GA4AnalyticsSearchCriteria({
            metrics: ['activeUsers'],
            dimensions: ['city', 'eventName'],
            filters: [{ field: 'eventName', value: 'page_view', matchType: 'EXACT' }],
        }), startDate, endDate).then((response) => {
            this.chart2 = this.preparePageViewChartData(response, 'city', 'activeUsers', 'Utilisateurs actifs');
        });

        this.fetchPageViewsDataGeneric(new GA4AnalyticsSearchCriteria({
            ...this.baseSearchCriteria,
        }), startDate, endDate).then((response) => {
            this.chart3 = this.preparePageViewChartData(response, GA4_DIMENSION.CONTENT_NAME, 'eventCount', null, GA4_DIMENSION.CONTENT_INTERACTION_TYPE, false);
            this.chart3.chartOptions['legend'] = { position: 'top', maxLines: 3 };
        });
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }
}