<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="600" height="600" fill="white" style=""><rect id="backgroundrect" width="100%" height="100%" x="0" y="0" fill="none" stroke="none"/>

  <title>Abstract user icon</title>

  <defs>
    <clipPath id="circular-border">
      <circle cx="300" cy="300" r="250" id="svg_1"/>
    </clipPath>
  </defs>
  
  
  
  
<g class="currentLayer" style=""><title>Layer 1</title><circle cx="300" cy="300" r="280" fill="#313a46" id="svg_2" class="" fill-opacity="1"/><circle cx="300" cy="230" r="100" id="svg_3" class=""/><circle cx="300" cy="550" r="190" clip-path="url(#circular-border)" id="svg_4"/></g></svg>