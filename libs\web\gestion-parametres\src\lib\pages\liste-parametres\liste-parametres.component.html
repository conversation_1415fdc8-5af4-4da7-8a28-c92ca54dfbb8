<!-- Start Of Header -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-4">Config Param<PERSON>tres</h4>

        <div class="col-8 px-1">
            <div class="row justify-content-end align-items-center">
                <button class="btn btn-sm btn-warning text-white rounded-pharma m-1"
                    (click)="resetForm(); (editMode = false); openModal(parameterModal, 'md')">
                    <i class="mdi mdi-plus"></i>
                    Ajouter Paramètre
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="container-fluid m-0 p-0">
    <kendo-grid [pageable]="true" [sortable]="false" [resizable]="true" [data]="grid" [pageSize]="grid?.total"
        style="height: calc(100vh - 123px)">

        <kendo-grid-column [width]="80" field="id" title="ID" class="text-left"></kendo-grid-column>

        <kendo-grid-column [width]="80" field="codeSite" title="Code Site" class="text-left">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Code Site</span>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="100" field="maxQteCmd" class="text-left">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Max Qté Commande</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.maxQteCmd | number }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="100" field="enableCheckQuota" title="Activer Vérification Quota"
            class="text-center">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Activer Vérification Quota</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                <span *ngIf="dataItem?.enableCheckQuota" class="badge badge-success rounded-pill py-1 px-2 pointer-cus">
                    Oui
                </span>

                <span *ngIf="!dataItem?.enableCheckQuota"
                    class="badge badge-warning rounded-pill py-1 px-2 pointer-cus">Non</span>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="100" field="maxQteCheckdispo" title="Vérification de disponibilité (Qté max)">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Vérification de disponibilité (Qté max)</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.maxQteCheckdispo | number }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="100" field="accepterCmdIndispo" title="Accepter Commande Indisponible"
            class="text-center">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Accepter Commande Indisponible</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                <span *ngIf="dataItem?.accepterCmdIndispo"
                    class="badge badge-success rounded-pill py-1 px-2 pointer-cus">
                    Oui
                </span>

                <span *ngIf="!dataItem?.accepterCmdIndispo"
                    class="badge badge-warning rounded-pill py-1 px-2 pointer-cus">Non</span>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [width]="80" title="Action" class="text-center no-ellipsis">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-center w-100">Action</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                <div class="d-flex row justify-content-center align-items-center k-gap-2">
                    <button class="circle btn btn-primary text-white" title="Modifier"
                        (click)="patchFormValues(dataItem); (editMode = true); openModal(parameterModal, 'md');">
                        <i class="mdi mdi-pencil"></i>
                    </button>

                    <button class="circle btn btn-danger text-white" title="Désactiver Batch"
                        (click)="desactiverBatchDuSite(dataItem?.codeSite)">
                        <i class="bi bi-trash-fill"></i>
                    </button>
                </div>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

        <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun résultat trouvé.</span>
        </ng-template>
    </kendo-grid>
</div>

<!--- Create Parameter Modal -->
<ng-template #parameterModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">{{ editMode ? 'Modifier' : 'Ajouter' }}</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <form [formGroup]="paramForm" class="py-2 px-0" (ngSubmit)="createOrUpdateParameter(); modal.dismiss()"
        wphFocusTrap>
        <div class="row mb-3 px-2">
            <div class="col-12">
                <label for="codeSiteParam" class="form-label">Code Site</label>
                <div *ngIf="!editMode; else: editModeInput" id="client-picker-input" class="input-group picker-input">
                    <input type="text" class="form-control form-control-sm pl-4" id="codeSiteParam"
                        formControlName="codeSite" [ngbTypeahead]="searchSociete"
                        [inputFormatter]="societeInputFormatter" [resultFormatter]="societeFormatter">

                    <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
                </div>

                <ng-template #editModeInput>
                    <input type="number" id="codeSiteParam" class="form-control form-control-sm"
                        formControlName="codeSite">
                </ng-template>
            </div>
        </div>

        <div class="row my-3 px-2">
            <div class="col-12">
                <label for="maxQteCmd" class="form-label">Max Qté Commande</label>
                <input type="number" id="maxQteCmd" class="form-control form-control-sm" formControlName="maxQteCmd">
            </div>

            <div class="col-12 mt-3">
                <label for="maxQteCheckdispo" class="form-label">Vérification de disponibilité (Qté max)</label>
                <input type="number" id="maxQteCheckdispo" class="form-control form-control-sm"
                    formControlName="maxQteCheckdispo">
            </div>
        </div>

        <div class="row my-3 px-2">
            <div class="col-12">
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="accepterCmdIndispo"
                        formControlName="accepterCmdIndispo">
                    <label for="accepterCmdIndispo" class="custom-control-label">Accepter Commande Indisponible</label>
                </div>
            </div>

            <div class="col-12 mt-2">
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="enableCheckQuota"
                        formControlName="enableCheckQuota">
                    <label for="enableCheckQuota" class="custom-control-label">Activer la Vérification de Quota</label>
                </div>
            </div>
        </div>

        <div class="modal-footer pt-1 mx-0 my-0">
            <button type="button" (click)="modal.dismiss()" class="btn btn-light" tabindex="-1">Fermer</button>
            <button type="button" type="submit" class="btn btn-primary ml-1" tabindex="-1">Enregistrer</button>
        </div>
    </form>
</ng-template>