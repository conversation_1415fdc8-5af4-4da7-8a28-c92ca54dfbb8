import { Component } from '@angular/core';
import { Observable } from 'rxjs';
import { LoaderService } from '../../services/loader.service';

@Component({
  selector: 'wph-loader',
  templateUrl: './loader.component.html',
  styleUrls: ['./loader.component.css']
})
export class LoaderComponent {
  loaderState$: Observable<boolean> = this.loaderService.loaderState;

  constructor(private loaderService: LoaderService) {}
}
