import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UpDownInputComponent } from './components/up-down-input/up-down-input.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InputReaderComponent } from './components/input-reader/input-reader.component';
import { ConfirmComponent } from './components/confirm/confirm.component';
import { GridModule } from '@progress/kendo-angular-grid';
import {
  NgbAlertModule,
  NgbDropdownModule,
  NgbModalModule,
  NgbModule,
  NgbPaginationModule,
  NgbToastModule,
  NgbTypeaheadModule
} from '@ng-bootstrap/ng-bootstrap';
import { RegexFilterComponent } from './components/regex-filter/regex-filter.component';

@NgModule({
  declarations: [
    UpDownInputComponent,
    InputReaderComponent,
    ConfirmComponent,
    RegexFilterComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    GridModule,
    NgbModule,
    NgbAlertModule,
    NgbPaginationModule,
    NgbTypeaheadModule,
    NgbToastModule,
    NgbModalModule,
    NgbDropdownModule,
  ],
  exports: [
    UpDownInputComponent,
    GridModule,
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    RegexFilterComponent,
    NgbAlertModule,
    NgbPaginationModule,
    NgbTypeaheadModule,
    NgbToastModule,
    NgbModalModule,
    NgbDropdownModule,
  ],
})
export class SharedModule {}
