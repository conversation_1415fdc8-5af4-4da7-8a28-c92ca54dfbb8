import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ListeCommandesComponent } from "./liste/liste-commandes.component";
import { EditCommandeComponent } from "./edit/edit-commande.component";
import { ListeCommandeGroupe } from "./commande-groupe/liste-commande-groupe.component";
import { AuthoritiesGuard, CanDeactivateGuard } from "@wph/web/shared";
import { ListeCommandesComponentIndividuelle } from "./liste-individuelle/liste-commandes-individuelle.component";
import { CommandesLaboComponent } from "./commandes-labo/commandes-labo.component";
import { CmdIndividuelleAdminComponent } from "./commande-individuelle-admin/commande-individuelle-admin.component";
import { CommandeGroupeAdminComponent } from "./commande-groupe-admin/commande-groupe-admin.component";
import { ToutesCommandesAdminComponent } from "./toutes-commandes-admin/toutes-commandes-admin.component";

const routes: Routes = [
    {
        path: '',
        pathMatch: 'full',
        redirectTo: 'liste'
    },
    {
        path: 'liste',
        title: 'Liste Commandes Unitaires',
        component: ListeCommandesComponent
    },
    {
        path: 'liste/individuelle',
        title: 'Liste Commandes Individuelles',
        component: ListeCommandesComponentIndividuelle
    },
    {
        path: 'liste/individuelle/admin',
        title: 'Liste Commandes Individuelles',
        component: CmdIndividuelleAdminComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'liste/groupe/admin',
        title: 'Liste Commandes Groupes',
        component: CommandeGroupeAdminComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'liste/commandes/admin',
        title: 'Liste Commandes',
        component: ToutesCommandesAdminComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL'] }
    },
    {
        path: 'liste/laboratoire',
        title: 'Liste Commandes Laboratoire',
        component: CommandesLaboComponent
    },
    {
        path: 'groupe',
        title: 'Commandes Groupe',
        component: ListeCommandeGroupe
    },
    {
        path: 'edit/:type', // ? Type Commande -> cmd-groupe / cmd-unitaire / cmd-individuelle
        canDeactivate: [CanDeactivateGuard],
        component: EditCommandeComponent
    },
    {
        path: 'edit/:type/:id', // ? Type Commande -> cmd-groupe / cmd-unitaire / cmd-individuelle | Commande ID
        canDeactivate: [CanDeactivateGuard],
        component: EditCommandeComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class CommandesRoutingModule {}