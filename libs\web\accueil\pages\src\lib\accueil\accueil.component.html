<!-- Start Of Header -->
<div *jhiHasAnyPlateforme="['COMMANDE_WEB']" class="rowline mb-2">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 col-12">TABLEAU DE BORD : {{ currentUserRole }}</h4>
  </div>
</div>
<!-- END HEADER -->

<div (click)="closeOpenSelect2Dropdowns()" class="page-content px-2 mt-2" #resizeTarget>
  <ng-container *jhiHasAnyPlateforme="['WIN_OFFRE']">
    <div class="row d-flex justify-content-center">
      <div class="col-xl-5">
        <div class="row my-2 flex-wrap mx-auto gap-2 d-flex justify-content-between w-100 align-items-center">
          <div class="col-12 col-lg-6 m-0 p-0 mb-lg-0 mb-2 d-flex justify-content-center">
            <div (click)="navigateToOffresWithSelectedCategory(null)"
              class="card wo-card-shadow p-0 pointer-cus force-card-width">
              <div class="card-body h-100 px-2 py-1">
                <div class="row d-flex justify-content-between align-items-center w-100 mx-0">
                  <div class="col-auto m-0 p-0">
                    <span class="icon-container-wo">
                      <i class="bi bi-file-arrow-up-fill"></i>
                    </span>
                  </div>
                  <div class="col-auto m-0 px-1 py-0">
                    <span class="text-color-wo count-container-text">{{'offres publiées' | uppercase}}</span>
                  </div>
                  <div class="col-auto m-0 p-0 d-flex justify-content-end">
                    <span class="text-color-wo count-container-value">{{ nombreMesOffresPubliees | number }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-12 col-lg-6 m-0 p-0  mb-lg-0 mb-2 d-flex justify-content-center">
            <div (click)="navigateToListeCommandes()" class="card wo-card-shadow p-0 pointer-cus force-card-width">
              <div class="card-body px-2 py-1">
                <div class="row d-flex justify-content-between align-items-center w-100 mx-0">
                  <div class="col-auto m-0 p-0">
                    <span class="icon-container-wo">
                      <i class="bi bi-bag-check-fill"></i>
                    </span>
                  </div>
                  <div class="col-auto m-0 py-0 px-1">
                    <span class="text-color-wo count-container-text">{{'commandes' | uppercase}}</span>
                  </div>
                  <div class="col-auto m-0 p-0 d-flex justify-content-end">
                    <span class="text-color-wo count-container-value">{{ nombreMesCommandes | number }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-xl-6 col-12 my-2 my-xl-0 mx-lg-auto mx-xl-0">
        <div class="row my-2 flex-wrap mx-auto gap-2 d-flex justify-content-between w-100 align-items-center">
          <div class="col-12 px-2 py-1 border-left-cstm">
            <span class="text-dark h4">Recherchez parmi les offres disponibles</span>
          </div>

          <div class="col-12 px-2 py-1">
            <form [formGroup]="searchOffreForm" class="row mx-0 w-100 d-flex align-items-center" style="gap: 10px;">
              <div (click)="$event.stopPropagation(); closeOpenSelect2Dropdowns(typeCriteriaSelect)" class="col-md-4 col-12 m-0 p-0">
                <select2 class="form-control-md w-100" [data]="listeOffreCriteria" placeholder="Critère offre"
                  multiple="false" hideSelectedItems="false" formControlName="typeCriteria" #typeCriteriaSelect></select2>
              </div>

              <div *ngIf="(f['typeCriteria']?.value !== 'C'); else: catSelection" class="col-md-5 col-12 m-0 p-0">
                <div class="input-group picker-input">
                  <input type="text" class="form-control form-control-md"
                    [placeholder]="f['typeCriteria']?.value === 'P' ? 'Rechercher par désignation' : (f['typeCriteria']?.value === 'L' ? 'Rechercher par raison sociale' : '')"
                    formControlName="libelleProduit" [ngbTypeahead]="searchOffresByProduit"
                    [resultFormatter]="f['typeCriteria']?.value === 'P' ? offreFormatteur : laboFormatteur"
                    [inputFormatter]="f['typeCriteria']?.value === 'P' ? offreFormatteur : laboFormatteur"
                    [readOnly]="f['typeCriteria']?.pristine" aria-label="Rechercher une offre"
                    aria-describedby="button-addon2" style="border-radius: 10px;">

                  <div class="picker-icons">
                    <i class="bi bi-search" style="font-size: .8em;"></i>
                  </div>
                </div>
              </div>

              <ng-template #catSelection>
                <div (click)="$event.stopPropagation(); closeOpenSelect2Dropdowns(categorieSelect)" class="col-md-5 col-12 m-0 p-0">
                  <select2 class="form-control-md w-100" [data]="listeCategorie" placeholder="Sélectionner Catégorie"
                    multiple="false" hideSelectedItems="false" formControlName="categoriePrdId" #categorieSelect></select2>
                </div>
              </ng-template>

              <div class="col-auto m-0 p-0 d-flex justify-content-end align-items-center">
                <button (click)="resetOffreForm()" type="button" class="btn btn-sm filter-btn"
                  style="background: #d5d5d5;">
                  <i class="bi bi-arrow-counterclockwise"></i>
                </button>

                <button (click)="submitSearchOffreForm()" type="button"
                  class="btn btn-sm btn-warning mx-1 text-white filter-btn">
                  <i class="bi bi-search"></i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <div
    class="{{ isPlateformeWinOffre ? 'd-flex justify-content-center' : 'd-flex justify-content-xl-start justify-content-center poste-container'}} row">
    <ng-container *jhiHasAnyPlateforme="['WIN_OFFRE']">
      <div class="col-xl-5">
        <wph-offre-slider [listeOffres]="listeOffres" [objectFit]="dynamicObjectFit"></wph-offre-slider>

        <div class="row mb-2 mt-1 mx-0 gap-2 d-flex justify-content-between w-100 align-items-center">
          <div class="col-6 my-1 mx-0 p-0">
            <div (click)="navigateToOffresWithSelectedCategory(3)"
              class="card p-0 m-0 wo-card-shadow category-card d-flex justify-content-center align-items-center">
              <img src="assets/images/cat-img1.jpeg" class="h-100 w-100" alt="Compléments Alimentaires Image">
              <div class="card-overlay d-flex align-items-center justify-content-center">
                <span class="text-center text-white px-1">Compléments Alimentaires</span>
              </div>
            </div>
          </div>

          <div class="col-6 my-1 mx-0 p-0 d-flex justify-content-end">
            <div (click)="navigateToOffresWithSelectedCategory(2)"
              class="card p-0 m-0 wo-card-shadow category-card d-flex justify-content-center align-items-center">
              <img src="assets/images/cat-img2.jpeg" class="h-100 w-100" alt="Compléments Alimentaires Image">
              <div class="card-overlay d-flex align-items-center justify-content-center">
                <span class="text-center text-white px-1">Médicaments</span>
              </div>
            </div>
          </div>

          <div class="col-6 my-1 mx-0 p-0">
            <div (click)="navigateToOffresWithSelectedCategory(9)"
              class="card p-0 m-0 wo-card-shadow category-card d-flex justify-content-center align-items-center">
              <img src="assets/images/cat-img3.jpeg" class="h-100 w-100" alt="Compléments Alimentaires Image">
              <div class="card-overlay d-flex align-items-center justify-content-center">
                <span class="text-center text-white px-1">Parapharmacie et cosmétiques</span>
              </div>
            </div>
          </div>

          <div class="col-6 my-1 mx-0 p-0 d-flex justify-content-end">
            <div (click)="navigateToOffresWithSelectedCategory(12)"
              class="card p-0 m-0 wo-card-shadow category-card d-flex justify-content-center align-items-center">
              <img src="assets/images/cat-img4.jpeg" class="h-100 w-100" alt="Compléments Alimentaires Image">
              <div class="card-overlay d-flex align-items-center justify-content-center">
                <span class="text-center text-white px-1">{{ 'Matériel Informatique' | titlecase }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>

    <ng-container *jhiHasAnyPlateforme="['COMMANDE_WEB']">
      <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT', 'ROLE_AGENT_ACHAT']">
        <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']">
          <ng-container *jhiHasAnyServiceOption="['PASSER_COMMANDE']; else: noShortcut">
            <div class="col-lg-10 col-xl-3 col-12">
              <div class="card mb-2 racc-card">
                <div class="card-header flex align-items-center mx-0 px-0">
                  <span class="h5 text-left my-0 ml-2 text-uppercase">
                    <i class="mdi mdi-layers-plus"></i>
                    Raccourcis
                  </span>
                </div>

                <div class="p-2 my-1">
                  <div class="d-flex justify-content-center align-items-center flex-column">
                    <div class="racourcis-wrapper w-100" (click)="goTo('/commande-web/add-commande/new')">
                      <i class="mdi mdi-cart-plus mr-1"></i>
                      <p class="racourcis-text">Nouvelle commande</p>
                    </div>
                  </div>
                </div>

                <div class="p-2 my-1">
                  <div class="d-flex justify-content-center align-items-center flex-column">
                    <div class="racourcis-wrapper-alt w-100" (click)="goTo('/commande-web/Alerte-produit')">
                      <i class="mdi mdi-bell-outline mr-1"></i>
                      <p class="racourcis-text">Alerte Produit</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
        </ng-container>

        <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_ACHAT']">
          <div class="col-lg-10 col-xl-3 col-12" *ngIf="isPlateformeCommandeWeb">
            <div class="card mb-2 racc-card">
              <div class="card-header flex align-items-center mx-0 px-0">
                <span class="h5 text-left my-0 ml-2 text-uppercase">
                  <i class="mdi mdi-layers-plus"></i>
                  Raccourcis
                </span>
              </div>

              <div class="p-2 my-1">
                <div class="d-flex justify-content-center align-items-center flex-column">
                  <div class="racourcis-wrapper-alt w-100" (click)="goTo('/commande-web/Alerte-produit')">
                    <i class="mdi mdi-bell-outline mr-1"></i>
                    <p class="racourcis-text">Alerte Produit</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>


        <ng-template #noShortcut>
          <div class="col-lg-10 col-xl-3 col-12" *ngIf="isPlateformeCommandeWeb"></div>
        </ng-template>

      </ng-container>

      <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
        <div class="col-xl-3 col-0"></div>
      </ng-container>
    </ng-container>

    <div
      class="{{ isPlateformeWinOffre ? 'col-xl-6 col-12' : 'col-lg-10 col-xl-5 col-12'}} my-2 my-xl-0 pb-2 mx-lg-auto mx-xl-0">
      <ng-container *ngIf="isPlateformeWinOffre; else: listePostes">
        <div class="row w-100 mx-0">
          <div class="col-12 p-0 m-0">
            <ng-container [ngTemplateOutlet]="listePostes"></ng-container>
          </div>
        </div>

        <div class="row my-2 mx-0 d-flex justify-content-end w-100 align-items-center">
          <div class="col-12 my-2 my-md-0 col-md-4 m-0 p-0 d-flex justify-content-end align-items-center">
            <button (click)="prevLogoSlide()" class="btn btn-sm mx-1 bg-white circle">
              <i class="bi bi-chevron-left"></i>
            </button>

            <button (click)="nextLogoSlide()" class="btn btn-sm bg-white circle">
              <i class="bi bi-chevron-right"></i>
            </button>
          </div>
        </div>

        <div class="row my-2 mx-0 d-flex justify-content-end w-100 align-items-center">
          <div class="col-12 p-0 m-0">
            <ng-container [ngTemplateOutlet]="swiperLaboLogoTemplate"></ng-container>
            <div class="shadow-overlay-left" *ngIf="!hideLogoShadowLeft"></div>
            <div class="shadow-overlay" *ngIf="!hideLogoShadowRight"></div>
          </div>
        </div>

      </ng-container>

      <!-- Liste postes start -->
      <ng-template #listePostes>
        <wph-liste-postes [listeFournisseur]="listeFournisseur" [overrideMaxHeight]="!isPlateformeWinOffre"
          mode="swiper" [isPlateformeWinoffre]="isPlateformeWinOffre"
          [targetPlateformeId]="targetPlateformeId"></wph-liste-postes>
      </ng-template>
      <!-- Liste postes end -->

    </div>

    <ng-container *jhiHasAnyPlateforme="['COMMANDE_WEB']">
      <div class="col-12 col-lg-10 col-xl-4">
        <div class="card p-2 cmd-web-card my-xl-0 my-2 shadow">

          <div class="d-flex row w-100 m-0 p-0 justify-content-center pos-card">
            <span class="cmd-web-title truncate-one-line">{{ currentGrossiste?.raisonSociale }}</span>
          </div>
        </div>

        <a href="https://www.sophatel.com/demo_contact.html" target="_blank" class="pointer-cus" title="Demander démo">
          <div class="card p-2 card-win-plus my-xl-0 my-2 shadow" wphTrackInteraction
            [eventData]="{name: 'Publicité: WinPlus Pharma', type: 'Publicité', author: currentGrossiste?.raisonSociale}">
          </div>
        </a>
      </div>
    </ng-container>

  </div>

  <ng-container *jhiHasAnyPlateforme="['WIN_OFFRE']">
    <div class="row d-flex justify-content-center w-100 mx-auto mt-4 mb-2">
      <div class="col-12 col-xl-11 px-2 py-1 border-left-cstm">
        <span class="text-dark h4">Offres disponibles par période</span>
      </div>

      <div class="col-12 col-xl-11 px-2 py-1 my-1">
        <div class="row d-flex justify-content-between w-100 mx-0">
          <div class="col-12 col-md-8 d-flex align-items-center m-0 p-0">
            <span (click)="applyDateInterval('day')" class="mr-2 txt-periode"
              [ngClass]="{'selected-txt': selectedInterval === 'day'}">Aujourd'hui</span>
            <span (click)="applyDateInterval('week')" class="mx-2 txt-periode"
              [ngClass]="{'selected-txt': selectedInterval === 'week'}">Semaine Dernière</span>
            <span (click)="applyDateInterval('month')" class="mx-2 txt-periode"
              [ngClass]="{'selected-txt': selectedInterval === 'month'}">Mois Dernier</span>
          </div>

          <div class="col-12 my-2 my-md-0 col-md-4 m-0 p-0 d-flex justify-content-end align-items-center">
            <button (click)="prevSlide()" class="btn btn-sm mx-1 bg-white circle">
              <i class="bi bi-chevron-left"></i>
            </button>

            <button (click)="nextSlide()" class="btn btn-sm bg-white circle">
              <i class="bi bi-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="row d-flex justify-content-center w-100 mx-auto my-0">
      <div class="col-12 col-xl-11 p-0 m-0">
        <ng-container [ngTemplateOutlet]="swiperHorizontalTemplate"></ng-container>
        <div class="shadow-overlay-left" *ngIf="!hideShadowLeft"></div>
        <div class="shadow-overlay" *ngIf="!hideShadowRight"></div>
      </div>
    </div>

  </ng-container>

</div>
<!-- end row -->

<ng-template #swiperHorizontalTemplate>
  <swiper-container class="h-100 p-0 sw-container" speed="800" autoplay="false" loop="false" navigation="false"
    pagination="false" scrollbar="false" [slidesPerView]="slidesPerView" [spaceBetween]="spaceBetween"
    centeredSlides="true" #horizontalSwiperRef>

    <swiper-slide *ngFor="let offre of listeOffresByDateInterval">
      <div (click)="Consulter(offre?.id)"
        class="card mx-2  mb-1 p-2 wo-card-shadow text-nowrap overflow-hidden pointer-cus"
        style="width: 310px; border-radius: 10px;" wphIntersectionObserver
        [options]="{ rootMargin: '0px', threshold: 0.2 }" (isIntersecting)="isCardVisible($event, offre)">

        <wph-countdown-timer [offre]="offre" [separator]="false" [ignoreWillExpire]="true" mode="D"
          class="col-12 mx-0 mt-0 mb-1 p-0 d-flex justify-content-between align-items-center"></wph-countdown-timer>

        <img [src]="getOffreImage(offre?.docImageOffre?.idhash)" alt="offre image"
          style="border-radius: 10px; height: 180px;">
        <div class="col-12 my-1 p-1 text-truncate">
          <span class="text-dark h4">{{ offre?.titre }}</span>
        </div>

        <div class="col-12 m-0 py-0 px-1 d-flex justify-content-between align-items-center">
          <span class="text-warning h5">Jusqu'à -{{ (offre?.maxRemiseFinancier | abs) | number : '1.0-0' }}%</span>
          <span class="text-dark d-flex align-items-center h5"><i class="bi bi-basket mr-1"></i> {{
            offre?.nombreProduitsProposes | number }} produits</span>
        </div>
      </div>
    </swiper-slide>

    <ng-container *ngIf="offresByDateIntervalHasMore" [ngTemplateOutlet]="showMoreSlideTemplate"></ng-container>

    <ng-container *ngIf="(!isLoadingOffresByDateInterval && !listeOffresByDateInterval?.length)"
      [ngTemplateOutlet]="noResultsSlideTemplate"></ng-container>
  </swiper-container>

</ng-template>

<ng-template #swiperLaboLogoTemplate>
  <swiper-container class="h-100 p-0 sw-container" speed="800" autoplay="false" loop="false" navigation="false"
    pagination="false" scrollbar="false" [slidesPerView]="logoSlidesPerView" [spaceBetween]="logoSpaceBetween"
    centeredSlides="true" #laboLogoSwiperRef>

    <swiper-slide *ngFor="let logoItem of listeLaboLogoSrc">
      <div [title]="logoItem?.libelle"
        (click)="!prodOnly ? navigateToOffresWithSelectedLabo(logoItem?.libelle) : navigateToOffresWithSelectedLaboId(logoItem?.id)"
        class="card m-1 p-1 wo-card-shadow category-card-alt d-flex justify-content-center align-items-center">
        <img [src]="logoItem?.src" class="h-100 w-100" loading="eager" [alt]="logoItem?.libelle">
      </div>
    </swiper-slide>
  </swiper-container>
</ng-template>

<ng-template #noResultsSlideTemplate>
  <swiper-slide>
    <div class="card mx-2  mb-1 p-2 wo-card-shadow pointer-cus" style="width: 310px; border-radius: 10px;">
      <div class="placeholder-container d-flex row w-100 m-0 p-2" [style.height]="'220px'">
        <div class="col-12 m-0 p-0 d-flex justify-content-center align-items-center">
          <i class="bi bi-box-seam-fill text-white h1"></i>
        </div>
        <div class="col-12 m-0 p-0 d-flex justify-content-center align-items-center">
          <span class="text-center text-white h3">Aucune offre trouvée pour la période sélectionnée.</span>
        </div>

      </div>
    </div>
  </swiper-slide>
</ng-template>

<ng-template #showMoreSlideTemplate>
  <swiper-slide class="d-flex align-items-center">
    <div (click)="navigateToOffresByDateInterval()" class="card mx-2 mb-1 p-2 wo-card-shadow pointer-cus"
      style="width: 310px; border-radius: 10px;">
      <div class="placeholder-container d-flex row w-100 m-0 p-2" [style.height]="'100%'">
        <div class="col-12 m-0 p-0 d-flex justify-content-center align-items-center">
          <span class="text-center text-white h3">Afficher toutes les offres</span>
        </div>

        <div class="col-12 m-0 p-0 d-flex justify-content-center align-items-center">
          <i class="bi bi-chevron-right text-white h1"></i>
        </div>
      </div>
    </div>
  </swiper-slide>
</ng-template>