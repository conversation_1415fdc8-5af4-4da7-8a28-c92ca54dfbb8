.card,
.card-header,
.b-radius {
  border-radius: var(--winoffre-base-border-radius);
}

.card,
.card-header,
.card-body {
  border-radius: var(--winoffre-base-border-radius) !important;

  .btn {
    border-radius: var(--winoffre-base-border-radius) !important;
  }
}

.opacity-light {
  opacity: 0.6 !important;
}

::ng-deep #FEDERATION_SYNDICAT-container .bg-card-header {
  top: 0;
  left: 0;
  width: auto;
  position: relative;
  background: var(--fs-grid-primary);
  padding: 5px;

  border-radius: 10px 0px 10px 0px;

  input::placeholder {
    color: rgb(255, 255, 255, 0.75);
    font-weight: 700;
  }

  input::selection {
    background: var(--fs-primary-tint);
  }

  input,
  input:focus,
  input:active {
    padding: 0;
    color: #fff !important;
    font-weight: 700;
    font-size: clamp(1.3rem, 2vw, 1.8rem);
    border: transparent !important;
    padding-left: 10px;
    outline: none;
    width: 100%;
    background: transparent;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

::ng-deep #WIN_GROUPE-container .bg-card-header {
  top: 0;
  left: 0;
  width: auto;
  position: relative;
  background: var(--fs-group-grid);
  padding: 5px;

  border-radius: 10px 0px 10px 0px;

  input::placeholder {
    color: rgb(255, 255, 255, 0.75);
    font-weight: 700;
  }

  input::selection {
    background: var(--fs-primary-tint);
  }

  input,
  input:focus,
  input:active {
    padding: 0;
    color: #fff !important;
    font-weight: 700;
    font-size: clamp(1.3rem, 2vw, 1.8rem);
    border: transparent !important;
    padding-left: 10px;
    outline: none;
    width: 100%;
    background: transparent;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.table-container {
  max-height: 300px;
  overflow-y: auto;

  scrollbar-width: thin !important;
  scrollbar-color: var(--fs-secondary-light) white !important;
  border-radius: var(--winoffre-base-border-radius)
}

label,
.form-control {
  font-size: 1rem;
  font-weight: 700;
  color: black;
}

.input-group {
  .form-control {
    border-radius: var(--winoffre-base-border-radius);
  }

  button {
    border: 1px solid #f3f3f3;
  }
}

.b-text {
  color: black;
  font-size: 1rem;
  font-weight: 700;
}

.table-row {
  display: flex;
  margin-bottom: 5px;
  cursor: pointer;
}

.table-cell {
  flex: 1;
  padding: 5px 10px;

  color: black;
  font-weight: 600;
  width: 170px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-cell:last-child {
  border-bottom: none;
}


.actions-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-inline: 10px;
  padding-block: 8px;
  border-radius: 11px;


  i {
    font-size: 18px;
    line-height: 1;
    cursor: pointer;
  }
}

::ng-deep .fs-cstm-modal .modal-content {
  border-radius: 10px;
}

.action-danger {
  background-color: var(--fs-danger);
  border-color: var(--fs-danger);

  &:hover {

    opacity: 0.9;
  }

  &:active {
    opacity: 0.7;
  }

  i {
    color: white;
  }
}

.action-success {
  background-color: var(--fs-success);
  border-color: var(--fs-success);

  &:hover {

    opacity: 0.9;
  }

  &:active {
    opacity: 0.7;
  }

  i {
    color: white;
  }
}

.table-row {
  padding: 0.375rem 1.5rem;
  padding-left: 10px !important;
  padding-right: 10px !important;
}

::ng-deep #client-picker-input .dropdown-item {
  padding: 0 !important;
  width: 100%;
}


.action-back {
  background: var(--wf-primary-400);
}


.status-badge {
  display: inline-block;
  padding: 0.25em 0.5em;
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  background-color: var(--fs-success);
}

.status-badge-inactif {
  display: inline-block;
  padding: 0.25em 0.5em;
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  background-color: #AAAFB5;
}

.cstm-btn {
  font-size: 1rem;
  font-weight: 600;
}

#gestion-membre-container {
  ::ng-deep .fs-grid .k-grid-toolbar {
    background-color: transparent;
    border: none !important;
  }

  ::ng-deep .nav-tabs {
    width: 100% !important;
    border-bottom: none !important;
    gap: 0px !important;
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: 2px solid var(--wf-primary-500) !important;
  }

  ::ng-deep .nav-tabs .nav-link,
  .nav-tabs .nav-item.show .nav-link {
    margin: 0 !important;
    font-weight: 800 !important;
    padding: 6px 8px !important;
    color: var(--wf-primary-600) !important;
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
    position: relative !important;
    border-bottom: 0px solid var(--fs-group-grid);
  }

  ::ng-deep .nav-tabs .nav-link.active,
  .nav-tabs .nav-item.show .nav-link {
    margin: 0 !important;
    background-color: var(--fs-group-grid) !important;
    color: #fff !important;
    padding: 6px 8px !important;
    border-color: transparent !important;
    font-weight: 800 !important;
    margin: 0 !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
  }
}

.fs-grid .k-grid-toolbar {
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
  border: 1px solid var(--fs-group-grid) !important;
  border-bottom: 2px solid var(--fs-group-grid) !important;
}

.editable-input {
  border-radius: 10px !important; 
  width: 120px !important; 
  border: 1px solid #ccc !important;
}