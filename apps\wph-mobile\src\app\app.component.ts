import { Compo<PERSON>, OnInit, NgZone, OnD<PERSON>roy } from "@angular/core";
import { SplashScreen } from '@capacitor/splash-screen';
import { App, URLOpenListenerEvent } from '@capacitor/app';
import { Router } from "@angular/router";
import { FcmService } from "./services/fcm.service";
import { HasAccessService } from "@wph/shared";
import { AuthService } from "@wph/core/auth";
import { NavController } from "@ionic/angular";
@Component({
  selector: 'wph-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {

  constructor(
    private zone: NgZone,
    private router: Router,
    private fcmService: FcmService,
    private authService: AuthService,
    private navController: NavController,
    private hasAccessService: HasAccessService,
  ) { }

  ngOnInit(): void {
    this.splashScreen();

    App.addListener('appStateChange', (state) => {
      if (
        state.isActive &&
        this.authService.isAuthenticated() &&
        this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])
      ) {
        // Set list "fournisseur avec accès "on each application resume event
        this.hasAccessService.setListeFournisseurAvecAcces();
      } else if (state.isActive && !this.authService.isAuthenticated()) {
        this.authService.logout();
        this.navController.navigateRoot('/auth/login');
      }
    })
  }

  private splashScreen(): void {
    setTimeout(() => {
      SplashScreen.hide();
    }, 500);
    this.fcmService.initPush();
  }


  private initializeApp() {
    App.addListener('appUrlOpen', (event: URLOpenListenerEvent) => {
      this.zone.run(() => {
        const domain = "app.sophatel.com";
        const pathArray = event.url.split(domain);


        const appPath = pathArray.pop();
        if (appPath) {
          this.router.navigateByUrl(appPath);
        }
      })
    })
  }

  ngOnDestroy(): void {
    App.removeAllListeners();
  }
}
