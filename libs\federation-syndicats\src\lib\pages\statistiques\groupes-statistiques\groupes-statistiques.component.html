<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 col-4">Statistiques des groupes</h4>
        <div class="row d-flex flex-row  justify-content-end   col-8 px-1" style="gap: 25px;">
          <div class="row  d-flex align-items-center">
            <p class="mb-0 mr-2 fw-bold">Du:</p>
            <button (click)="backwardOneMonth('start')" class="btn btn-sm btn-left px-1">
                <i class="mdi mdi-chevron-left mdi-18px"></i>
            </button>
            <ng-container>
                <input [formControl]="datePicker" style="visibility: hidden; position: absolute; right: 10px;">
                <span class="mx-0 my-0 d-flex align-items-center date-container">
                    <i class="mdi mdi-calendar mdi-18px"></i>
                    <span class="mx-1">{{ displayedStartDate$ | async | date: 'MMMM yyyy' | titlecase }}</span>
                </span>
            </ng-container>
            <button (click)="forwardOneMonth('start')" class="btn btn-sm btn-right px-1">
                <i class="mdi mdi-chevron-right mdi-18px"></i>
            </button>
        </div>

          <div class="row  d-flex  align-items-center ">
                <p class="mb-0 mr-2 fw-bold">Au:</p>
                <button (click)="backwardOneMonth('end')" class="btn btn-sm btn-left px-1">
                    <i class="mdi mdi-chevron-left mdi-18px"></i>
                </button>

                <ng-container>
                    <input [formControl]="datePicker2" style="visibility: hidden; position: absolute; right: 10px;">

                    <span class="mx-0 my-0 d-flex align-items-center date-container">
                        <i class="mdi mdi-calendar mdi-18px"></i>
                        <span class="mx-1">{{ (displayedEndDate$ | async | date: 'MMMM yyyy') | titlecase}}</span>
                    </span>
                </ng-container>

                <button (click)="forwardOneMonth('end')"
                    [disabled]="((displayedEndDate$ | async)?.getMonth() === now?.getMonth()) && ((displayedEndDate$ | async)?.getFullYear() === now?.getFullYear())"
                    class="btn btn-sm btn-right px-1">
                    <i class="mdi mdi-chevron-right mdi-18px"></i>
                </button>
            </div>



            <div class="d-flex align-items-center k-gap-2">
                <button (click)="getMyStatistique()" class="btn btn-sm btn-info px-1">
                    Filtrer
                </button>
                <button (click)="exportStatsExcel()" class="btn btn-sm btn-dark px-1"
                *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']"
                >
                  Exporter en Excel
              </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->
<div class=" ">
  <div class="row d-flex flex-row mx-lg-3 mt-1" >
   <div class="col-12">
    <div class="card" style="width: 100%;">
      <div class="card-header">
        <h4 class="my-0 text-uppercase text-dark">Statistique CMD par Groupe</h4>
      </div>
      <div class="card-body p-1">
        <apx-chart *ngIf="chartOptions"
        [noData]="chartsNoDaTA"
            style="width: 500px;"
            [series]="chartOptions?.series"
            [chart]="chartOptions?.chart"
            [dataLabels]="chartOptions?.dataLabels"
            [plotOptions]="chartOptions?.plotOptions"
            [yaxis]="chartOptions?.yaxis"
            [legend]="chartOptions?.legend"
            [fill]="chartOptions?.fill"
            [stroke]="chartOptions?.stroke"
            [tooltip]="chartOptions?.tooltip"
            [xaxis]="chartOptions?.xaxis"></apx-chart>
      </div>
    </div>
   </div>
   <div class="col-12 my-3">
    <kendo-grid [data]="gridDataGroupe" class="fs-grid fs-listing-grid" [sortable]="false" [pageable]="false" style="height: 100%">
      <kendo-grid-column field="groupe.raisonSociale" title="Groupe" [width]="150"></kendo-grid-column>
      <kendo-grid-column field="groupe.ville" title="Ville" [width]="100"></kendo-grid-column>
      <kendo-grid-column field="periode" title="Période" [width]="150"></kendo-grid-column>
      <kendo-grid-column field="montantSupportee" class="text-right" title="Dépense" [width]="150"></kendo-grid-column>
      <!-- <kendo-grid-column field="montantConsomme" class="text-right" title="Montant Consomme" [width]="150"></kendo-grid-column> -->
      <!-- <kendo-grid-column field="balance" class="text-right" title="Balance" [width]="150"></kendo-grid-column> -->
    </kendo-grid>
   </div>
   <div class="col-12">
    <div class="card" style="width: 100%;" #StatsLaboChart>
      <div class="card-header d-flex justify-content-between align-items-center">
        <h4 class="my-0 text-uppercase text-dark">Statistique Cmd par Labo</h4>
        <div class="d-flex btn-group">
          <button class="btn" [ngClass]="{'btn-info':laboCriteria === 'TOUS','btn-outline-info':laboCriteria !== 'TOUS'}" (click)="changeLaboCriteria('TOUS')">Tous </button>
          <button class="btn" [ngClass]="{'btn-info':laboCriteria === 'GROUPE','btn-outline-info':laboCriteria !== 'GROUPE'}" (click)="changeLaboCriteria('GROUPE')">Cmds Groupe </button>
          <button class="btn" [ngClass]="{'btn-info':laboCriteria === 'INDIVIDUEL','btn-outline-info':laboCriteria !== 'INDIVIDUEL'}" (click)="changeLaboCriteria('INDIVIDUEL')">Cmds Indiv</button>
        </div>
      </div>
      <div class="card-body p-1">
        <apx-chart *ngIf="laboChartOptions"
            style="width: 500px;"
            [noData]="chartsNoDaTA"
            [series]="laboChartOptions?.series"
            [chart]="laboChartOptions?.chart"
            [dataLabels]="laboChartOptions?.dataLabels"
            [plotOptions]="laboChartOptions?.plotOptions"
            [yaxis]="laboChartOptions?.yaxis"
            [legend]="laboChartOptions?.legend"
            [fill]="laboChartOptions?.fill"
            [stroke]="laboChartOptions?.stroke"
            [tooltip]="laboChartOptions?.tooltip"
            [xaxis]="laboChartOptions?.xaxis"></apx-chart>
      </div>
    </div>
   </div>
   <div class="col-12 my-3">
    <kendo-grid [data]="gridDataLabo" (cellClick)="consultCommandeByType($event)" class="fs-grid fs-listing-grid" [sortable]="false" [pageable]="false" style="height: 100%">
      <kendo-grid-column field="offreur.raisonSociale" title="Laboratoire" [width]="150">
        <ng-template kendoGridFooterTemplate >

          <div class="text-left" >Total</div>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="periode" title="Période" [width]="150">
         <ng-template kendoGridFooterTemplate>
          <div>{{laboTotalStats.periode}}</div>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="countCmdPasse" class="text-right" title="Nbr cmd" [width]="80">
         <ng-template kendoGridFooterTemplate >
          <div class="text-right">{{laboTotalStats.totaleCommandes}}</div>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="montantSupportee" class="text-right" title="Total Brut" [width]="150">
        <ng-template kendoGridCellTemplate  let-dataItem>
          {{dataItem.montantSupportee | number:'1.2-2'}}
        </ng-template>
         <ng-template kendoGridFooterTemplate >
          <div class="text-right">{{laboTotalStats.totalMontantSupporte | number:'1.2-2'}}</div>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="montantConsomme" class="text-right" title="Total Net" [width]="150">
        <ng-template kendoGridCellTemplate  let-dataItem>
          {{dataItem.montantConsomme | number:'1.2-2'}}
        </ng-template>
         <ng-template kendoGridFooterTemplate >
          <div class="text-right">{{laboTotalStats.totalMontantConsomme | number:'1.2-2'}}</div>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column  class="text-right" title="Total Remise" [width]="150">
        <ng-template kendoGridCellTemplate let-dataItem>
          <span class="text-right">{{dataItem.montantSupportee - dataItem.montantConsomme | number:'1.2-2'}}</span>
        </ng-template>
        <ng-template kendoGridFooterTemplate >
          <div class="text-right">{{laboTotalStats.totalRemise | number:'1.2-2'}}</div>
        </ng-template>
      </kendo-grid-column>
      <!-- <kendo-grid-column field="montantSupportee" class="text-right" title="Chiffre d'affaire" [width]="150"></kendo-grid-column> -->

      <!-- <kendo-grid-column field="montantConsomme" class="text-right" title="Montant Consomme" [width]="150"></kendo-grid-column> -->
      <!-- <kendo-grid-column field="balance" class="text-right" title="Balance" [width]="150"></kendo-grid-column> -->
    </kendo-grid>
   </div>
  </div>
