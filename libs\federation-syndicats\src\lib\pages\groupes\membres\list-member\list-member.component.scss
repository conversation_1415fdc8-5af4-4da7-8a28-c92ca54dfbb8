
.card, .card-header, .card-body {
    border-radius: var(--winoffre-base-border-radius) !important;

    .btn {
        border-radius: var(--winoffre-base-border-radius) !important;
    }
}

.b-radius {
  border-radius: var(--winoffre-base-border-radius) !important;
}

.b-text {
  color: black;
  font-size: 1rem;
  font-weight: 700;
}

.btn {
    font-size: 1rem;
    font-weight: 600;
}

.opacity-light {
  opacity: 0.6 !important;
}

.input-group {
    .form-control {
        color: black;
        font-weight: 700;
        border-top-left-radius: var(--winoffre-base-border-radius);
        border-bottom-left-radius: var(--winoffre-base-border-radius);

    }
    .btn {
      border-top-right-radius: var(--winoffre-base-border-radius);
      border-bottom-right-radius: var(--winoffre-base-border-radius);
    }
  }

label {
    color: var(--winoffre-text-light-shade);
    font-weight: 600;
}

.picker-input {
    .form-control{
        border-radius: var(--winoffre-base-border-radius) !important;
    }
}

.selected-switch {
    color: #f3f3f3;
    background: var(--fs-secondary);
}

.card-view {
    height: calc(100vh - 190px);
    overflow-x: hidden;
    overflow-y: auto;
}



.btn-success{
  background: var(--fs-success) !important;
  border-color: var(--fs-success) !important;
}

.btn-danger{
  background: var(--fs-danger) !important;
  border-color: var(--fs-danger) !important;
}





.text-warning {
  color: var(--fs-warning) !important;
}




.actions-email {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-inline: 5px;
  padding-block: 4px;
  border-radius: 11px;
  width: fit-content;


  img{
    width: 30px;
    height: 30px;
  }
}


.is-group-owner{
  background: var(--fs-warning);
  color: white;
}


.form-select-role{
  width: 100%;
  border-radius: var(--winoffre-base-border-radius);
  background: var(--primary);
  padding: 2px !important;
  height: 32px !important;
  width: fit-content;
  color: #fff;
}




// Modal


.fs-modal {

border-radius: 10px !important;

  .success-text{
    font-size: 30px !important;
    font-weight: 800;
    color: var(--wf-primary-400);
    ;
  }
  .message-text{
    font-size: 18px !important;
    font-weight: 600;
    color: #696C75;
  }
}

::ng-deep .fs-modal-content .modal-content{
  background: transparent !important;
}

.cross-button{
  position: absolute;
  right: 4px;
  top: 5px;
  cursor: pointer;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #e3d9d9;

  i{
    font-size: 30px;
    color: #4d4949;
  }
  &:hover {
    color: var(--fs-danger);
    background: #e6dafa;
  }
}


.btn-fs-outline-success{
  color: var(--fs-success);
  border-color: var(--fs-success);
  box-shadow: none;

  &:hover{
    box-shadow: none;
    color: white;
    background: var(--fs-success);
  }

  &


  &:focus{
    box-shadow: none;
    color: white;
    background: var(--fs-success);
  }
  &:active{
    box-shadow: none !important;
    color: white !important;
    background: var(--fs-success) !important;
  }
}
.btn-fs-outline-dark{
  color:#7C7F86;
  border-color: #696C75;
  border-width: 2px;
  box-shadow: none;
  background: transparent;

  &:hover{
    box-shadow: none;
    color: white;
    background: #696C75;
   }


  &:active{
    box-shadow: none !important;
    color: white !important;
    background: #404246;
  }
}


.btn-fs-confirm{
  color: white;
  background: #EE8245;
  border-color:none;
  box-shadow: none;
  padding-inline:20px;
  border-radius: 5px;

  &:hover{
    box-shadow: none;
    color: white;
    background: #EE8245;
  }


}

.btn-fs-cancel{
  color: #000;
  border-color:#696C75;
  box-shadow: none;
  padding-inline:20px;
  border-radius: 5px;
  background: transparent;

  &:hover{
    box-shadow: none;
    color: white;
    background: #696C75;
  }
}