import { Inject, Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Subject, Observable, BehaviorSubject, of, tap, forkJoin, firstValueFrom, catchError } from "rxjs";
import { <PERSON>Offre, toJSON } from '../models/bloc-offre.model';
import { DetailValeurPalier } from '../models/detail-valeur-palier.model';
import { Offre, OffreCriteria } from '../models/offre.model';
//import { offres, produits } from '../edit-commande/testdata';
import { ProduitCriteria } from '../models/produitCriteria.model';
import { CheckPrixRequestDTO, CheckTranscoPrdResponseDTO, CheckTranscoPrixResponse, CheckTranscoResponse, Produit, SearchProduit } from '../models/produit.model';
import { Fournisseur } from '../models/fournisseur.model';
import { FormeProduit } from '../models/forme-produit.model';
import { GammeProduit } from '../models/gamme-produit.model';
import { ArticleAcceuil, MessageAcceuil, MessageCriteria } from '../models/acceuil.model';
import { CheckProduitInOffreResponseDto, Commande, CommandeCriteria } from '../models/commande.model';
import { StatsSearch } from "../models/statsSearch.model";
import { StatsData } from "../models/stats.model";
import { Pagination } from "../models/PaginationDTO.ts";
import { AuthService } from "@wph/core/auth";
import { PalierSelector } from "../models/palier-selector.model";
import { OffreDynamicScriptEvaluator, OffreDynamicScriptEvaluatorBuilder } from "./offre-dynamic-script-evaluator";
import { PalierValeurCadeau } from "../models/palier-valeur-cadeau.model";
import { SelectedTypeRemiseEnum } from "../models/selected-type-remise.enum";
import { TypeSelectionRfUgEnum } from "../models/type-selection-rf-ug.enum";
import { ConditionBlocOffreCommandeConsolidee } from "@wph/federation-syndicats";
import { ClientSiteDto, EntrepriseDTO, SearchEntrepriseDto } from "libs/federation-syndicats/src/lib/models/entreprise.model";
import { MODE_PAIEMENT } from "@wph/data-access";
import { BaseEtrangereToBaseWinplusGrouping, DocMetaDataDto, DomainEnumeration, IndicateurConsommation, IndicateurStock, InputTAP, OutputTAP, PlateformeService, ProduitListDto, SearchSelectedProduitsAssociations, SelectedProduitsAssociations, TAPResponse, UploadFileServiceService } from "@wph/shared";
import { cloneDeep, isEqual } from 'lodash';
import { DecimalPipe } from "@angular/common";
import * as moment from "moment";
import { ToastrService } from "ngx-toastr";


type ScriptEvaluationResponse = {
  error: boolean;
  message: string;
};




@Injectable({
  providedIn: 'root'
})
export class OffresService {

  isMultiple = false;
  qteUgCumle = 0;
  baseUrl: string;
  device: any;

  subject = new Subject<any>();
  subjectSelectedBlocElementInEditOffre = new Subject<BlocOffre>();
  private dynamicActiveTabIndex = new Subject<number>();
  activeTabIndex$ = this.dynamicActiveTabIndex.asObservable();

  private produitCategories$: BehaviorSubject<DomainEnumeration[]> = new BehaviorSubject<DomainEnumeration[]>(null);
  private EtatLeftBar: BehaviorSubject<string>;
  public currentEtatBar: Observable<string>;

  modifiedBlocOffre$: BehaviorSubject<BlocOffre[]> = new BehaviorSubject(null);

  produitsAvecStockInsuffisant$: BehaviorSubject<Array<{ blocId: number; code: string }>> = new BehaviorSubject<Array<{ blocId: number; code: string }>>(null);



  constructor(
    private toastr: ToastrService,
    private httpClient: HttpClient,
    private authService: AuthService,
    private decimalPipe: DecimalPipe,
    private plateformeService: PlateformeService,
    private uploadService: UploadFileServiceService,
    @Inject('ENVIROMENT') private environment: any
  ) {
    this.baseUrl = this.environment.base_url;
    this.device = this.environment.platform;

    if (this.authService.hasAnyAuthority(['ROLE_AGENT_COMMERCIAL', 'ROLE_ASSISTANT']) || this.plateformeService.isPlateForme('DEFAULT')) {
      this.EtatLeftBar = new BehaviorSubject<string>('condensed');
    } else {
      this.EtatLeftBar = new BehaviorSubject<string>('fixed');
    }

    this.currentEtatBar = this.EtatLeftBar.asObservable();
  }



  initialiserEtatOffre(offre: Offre) {  // target commande consolidée globale
    if (offre && offre.listeBlocs) {

      offre.mapBlocsById = {};
      offre.natureOffre = offre?.natureOffre ?? (!this.plateformeService.isPlateForme('WIN_OFFRE') ? 'G' : 'I');

      for (const bloc of offre.listeBlocs) {
        bloc.offre = offre;   // remplir offre au niveau des premiers fils seulement
        bloc.hash = (Math.floor(Math.random() * (999999 - 100000)) + 100000).toString();
        bloc.toJSON = toJSON;
        this.setDefaultFieldsOfBlocCreation(bloc);

        this.initSubscribers(bloc);

        this.initConditionUnitOnBlocOffre(bloc);
        this.initialiserEtatCommandeBlocOffre(bloc);

        this.declareIdBlocOffre(offre, bloc);
      }

      offre.coffretEnabled = offre.listeBlocs.findIndex(blocPack => blocPack.coffretEnabled) != -1;

      if (offre?.docImageOffre) {
        offre.offreImageUrl = this.uploadService.fetchUploadedDocument(offre?.docImageOffre?.idhash);
      }
    }
  }



  private setDefaultFieldsOfBlocCreation(blocOffre: BlocOffre) {

    if (blocOffre) {
      blocOffre.previousSelectedPalier = null;
      blocOffre.currentSelectedPalier = null;
    }
    else {
      return;
    }

    if (blocOffre.listeFils) {
      for (const fils of blocOffre.listeFils) {
        fils.parent = blocOffre; // remplir le champs parent de toutes l'arborescence
        fils.hash = (Math.floor(Math.random() * (999999 - 100000)) + 100000).toString();
        fils.toJSON = toJSON;
        this.setDefaultFieldsOfBlocCreation(fils);
      }
    }
  }



  private initialiserEtatCommandeBlocOffre(blocOffre: BlocOffre): void {
    if (!blocOffre) {
      return;
    }


    if (this.isBlocTypeProduit(blocOffre)) {
      this.refreshEtatBlocOffre(blocOffre);
    } else {
      for (const blocFils of blocOffre.listeFils) {
        this.initialiserEtatCommandeBlocOffre(blocFils);
      }
    }
  }

  reinitialiserEtatCommandeBlocOffre(blocOffre: BlocOffre): void {
    if (!blocOffre) {
      return;
    }

    for (const blocFils of blocOffre.listeFils) {
      if (blocFils?.typeBloc === 'F') {
        blocFils.qteCmd = 0;
        blocFils.totalQteCmd = 0;

        blocFils.qteUgSaisie = 0;
        blocFils.totalQteUg = 0;
        this.refreshEtatBlocOffre(blocFils);
      } else {
        this.reinitialiserEtatCommandeBlocOffre(blocFils);
      }
    }
  }

  reinitialiserEtatCommande(offre: Offre): void {
    if (offre?.listeBlocs?.length) {
      for (const bloc of offre?.listeBlocs) {
        this.reinitialiserEtatCommandeBlocOffre(bloc);
      }
    }
  }


  reinitialiserEtatCommandeBlocOffreSurCmdUnit(blocOffre: BlocOffre): void {
    if (!blocOffre) {
      return;
    }

    for (const blocFils of blocOffre.listeFils) {
      if (blocFils?.typeBloc === 'F') {
        blocFils.qteCmd = 0;
        blocFils.totalQteCmd = 0;

        blocFils.qteUgSaisie = 0;
        blocFils.totalQteUg = 0;
        this.refreshEtatBlocOffreSurCmdUnit(blocFils);
      } else {
        this.reinitialiserEtatCommandeBlocOffreSurCmdUnit(blocFils);
      }
    }
  }

  reinitialiserEtatCommandeUnitaire(offre: Offre): void {
    if (offre?.listeBlocs?.length) {
      for (const bloc of offre?.listeBlocs) {
        this.reinitialiserEtatCommandeBlocOffreSurCmdUnit(bloc);
      }
    }
  }


  private searchForProduitInBlocOffre(blocOffre: BlocOffre, searchQuery: string, accumulator: BlocOffre[], resultLimit?: number) {
    if (!blocOffre) return;

    for (const blocFils of blocOffre?.listeFils) {
      if (
        this.isBlocTypeProduit(blocFils) &&
        (resultLimit && (accumulator?.length < resultLimit)) &&
        blocFils?.libelleProduit?.toLowerCase()?.includes(searchQuery?.toLowerCase())
      ) {
        accumulator.push(blocFils);
      } else {
        this.searchForProduitInBlocOffre(blocFils, searchQuery, accumulator, resultLimit);
      }
    }
  }


  searchForProduitInOffre(offre: Offre, searchQuery: string, resultLimit = 0): Map<string, BlocOffre[]> {
    const accumulator: Map<string, BlocOffre[]> = new Map<string, BlocOffre[]>();

    for (const blocOffre of offre?.listeBlocs) {
      accumulator.set(`${blocOffre?.id}|${blocOffre?.titre}`, []);
      this.searchForProduitInBlocOffre(blocOffre, searchQuery, accumulator.get(`${blocOffre?.id}|${blocOffre?.titre}`), resultLimit);
    }

    this.clearEmptyMapValues(accumulator);

    return accumulator;
  }

  private clearEmptyMapValues(blocOffreMap: Map<string, BlocOffre[]>): void {
    for (const key of blocOffreMap.keys()) {
      if (!blocOffreMap?.get(key)?.length) blocOffreMap.delete(key);
    }
  }

  propagateActiveIndex(index: number) {
    this.dynamicActiveTabIndex.next(index);
  }

  getIndexOfPackParent(offre: Offre, blocFilsId: number): number {
    if (!offre?.listeBlocs) return -1; // Handle invalid input

    for (const [index, blocOffre] of offre.listeBlocs.entries()) {
      if (this.findBlocOffreWithId(blocOffre, blocFilsId)) {
        // ? Clear local search value, force display all bloc-fils items to enable scroll to selected bloc-fils logic
        blocOffre.designationProduit = '';

        return index + 1;
      }
    }

    return -1; // No match found
  }

  private findBlocOffreWithId(blocOffre: BlocOffre, blocOffreId: number, visited = new Set<BlocOffre>()): boolean {
    if (!blocOffre || visited.has(blocOffre)) return false;

    visited.add(blocOffre);

    for (const blocFils of blocOffre?.listeFils) {
      if (this.isBlocTypeProduit(blocFils) && blocFils?.id === blocOffreId) {
        blocFils.displayDetailsSousBloc = true;
        return true;
      } else if (this.findBlocOffreWithId(blocFils, blocOffreId, visited)) {
        blocFils.displayDetailsSousBloc = true;
        return true; // Exit early
      }
    }

    return false;
  }


  // declarer id du bloc et ses fils dans Offre
  private declareIdBlocOffre(offre: Offre, blocOffre: BlocOffre): void {
    if (!blocOffre) {
      return;
    }

    if (!blocOffre.id) {
      console.warn('Attention un bloc n\'a pas d\'id');
    }

    const exisitingElement = offre.mapBlocsById[blocOffre.id];

    if (exisitingElement) {
      console.warn('Attention un id a été déjà déclaré : ' + blocOffre.id);
    } else {
      offre.mapBlocsById[blocOffre.id] = blocOffre;
    }

    if (blocOffre.listeFils) {
      for (const blocFils of blocOffre.listeFils) {
        this.declareIdBlocOffre(offre, blocFils);
      }
    }
  }


  public declareMemberIdBlocOffre(cmdGrpAsoffre: Offre, blocOffreCmdUnit: BlocOffre, memberId: number): void {
    if (!blocOffreCmdUnit) {
      return;
    }

    const blocOffreCmdGrp = cmdGrpAsoffre.mapBlocsById[blocOffreCmdUnit.id] as BlocOffre;

    if (!blocOffreCmdGrp.mapBlocsByMemberId) {   // init field if undefined or null
      blocOffreCmdGrp.mapBlocsByMemberId = {};
    }

    blocOffreCmdGrp.mapBlocsByMemberId[memberId] = blocOffreCmdUnit;

    if (blocOffreCmdUnit?.listeFils) {
      for (const blocFils of blocOffreCmdUnit.listeFils) {
        this.declareMemberIdBlocOffre(cmdGrpAsoffre, blocFils, memberId);
      }
    }
  }




  private initSubscribers(blocOffre: BlocOffre) {
    if (!blocOffre) {
      return;
    }

    if (this.needToSubscribeToPackChanges(blocOffre)) {
      let packParent = this.getPackParent(blocOffre);

      if (packParent !== blocOffre) {
        if (!packParent.subscribers) {
          packParent.subscribers = []
        }

        packParent.subscribers.push(blocOffre);
      }
    }


    for (const blocFils of blocOffre.listeFils) {
      this.initSubscribers(blocFils);
    }
  }

  private needToSubscribeToPackChanges(blocOffre: BlocOffre) {

    return (blocOffre.listePaliers?.length && blocOffre.listePaliers[0].valueRelatifPack === true) ||
      (blocOffre.typeBloc !== 'P' && blocOffre.blocObligatoire === 'O')
      ;
  }



  private hasThisBlocToBeIgnored(blocOffre: BlocOffre): boolean {
    return this.isBlocTypeProduit(blocOffre) && (blocOffre.qteCmd == 0 && blocOffre.qteUgSaisie == 0 && blocOffre.blocObligatoire != "O");
  }




  // calcul qlq totaux  et  retourne   etat      'N' pour nonsaisi,        'V' pour valide,        'I' pour invalide
  refreshEtatBlocOffre(blocOffre: BlocOffre, emitEventToBlocsSubscribers: boolean = true): string {
    if (!blocOffre) {
      return null;
    }

    let ignoreRafraisirNoeudsPére = false;
    const offre: Offre = this.getOffreRacine(blocOffre);    // get offre

    const forceRecalculFils = (this.isBlocTypeProduit(blocOffre));

    this.resetEtatBlocOffre(blocOffre);

    this.calculerTotauxOfBloc(blocOffre, forceRecalculFils);

    const qteCmdBloc = blocOffre.totalQteCmd;

    // rafraichir champs calculés des parents
    this.refreshChampsCalculesParents(blocOffre);


    let selectedPalier: DetailValeurPalier = this.findAndApplyPalierToBloc(blocOffre);

    blocOffre.selectedPalier = selectedPalier;

    // rafraichir une deuxième fois champs calculés des parents après selection palier
    this.refreshChampsCalculesParents(blocOffre);

    this.setEtatBasedOnLocalConditions(blocOffre);

    this.setEtatBaseOnDynamicCondition(blocOffre, selectedPalier);

    this.setEtatBasedOnOffreConditions(offre);

    // blocObligatoire avec qteCmd===0
    if (qteCmdBloc === 0 && blocOffre.blocObligatoire === 'O') {
      let packParent = this.getPackParent(blocOffre);
      if (packParent === blocOffre || packParent.totalQteCmd !== 0) {
        blocOffre.etat = 'I';  // forcer etat invalide
        blocOffre.messageEtat = 'Element obligatoire non saisi';
      }
    }

    if (this.isBlocTypeProduit(blocOffre) === false) {
      const selectedParentPalier: DetailValeurPalier = blocOffre?.currentSelectedPalier;
      const isRatioUg = !!selectedPalier?.ratioUg;

      if (
        qteCmdBloc && (!offre.accepterPalierInvalide || offre.accepterPalierInvalide === 'N') &&
        ((blocOffre.listePaliers) && !selectedParentPalier)) {

        if (blocOffre && blocOffre.listePaliers && blocOffre.listePaliers.length) {
          blocOffre.etat = 'I';
          blocOffre.messageEtat = this.extractMessageEtatFromFirstPalier(blocOffre.listePaliers[0]);

        }
      }


      if (blocOffre.totalQteUg === -1) {
        blocOffre.etat = 'I';
        blocOffre.messageEtat = 'Quantite cmd est invalide';
      }
      else if (isRatioUg && (blocOffre.qteUgSaisie !== blocOffre.totalQteUg)) {
        blocOffre.etat = 'I';
        blocOffre.messageEtat = `Veuillez saisir les unités gratuites offertes. Unités offertes: ${blocOffre?.totalQteUg}`;
      }

    }
    else { // bloc type produit
      if (qteCmdBloc &&
        (!offre.accepterPalierInvalide || offre.accepterPalierInvalide === 'N') &&
        !selectedPalier) {  // alors ne pas accepter palier non selectionné

        let currentBloc = blocOffre;
        while (currentBloc) {
          if (currentBloc && currentBloc.listePaliers && currentBloc.listePaliers.length) {
            currentBloc.etat = 'I';
            currentBloc.messageEtat = this.extractMessageEtatFromFirstPalier(currentBloc.listePaliers[0]);
            ignoreRafraisirNoeudsPére = true;
            this.invaliderNoeudsParents(currentBloc, currentBloc.messageEtat);
            break;
          }

          currentBloc = currentBloc.parent;   // parent
        }
      }

      if (this.isBlocTypeProduit(blocOffre) && selectedPalier?.ratioUg) {
        blocOffre.qteUgSaisie = blocOffre?.totalQteUg;
      }

      if (blocOffre.totalQteUg === -1) {
        blocOffre.etat = 'I';
        blocOffre.messageEtat = 'Quantite cmd est invalide';
      }

      this.checkConditionMultiple(blocOffre);
    }

    // si bloc produit invalide alors invalider aussi tous les noeuds père
    if (blocOffre.etat === 'I') {
      this.invaliderNoeudsParents(blocOffre, blocOffre.messageEtat);
    } else {
      if (!qteCmdBloc) {
        blocOffre.etat = 'N'; // non saisi
        blocOffre.messageEtat = '';
      } else {
        blocOffre.etat = 'V';   // valide
        blocOffre.messageEtat = '';
        ignoreRafraisirNoeudsPére = false;
      }
    }

    // rafraichir etat des noeuds pères
    if (!ignoreRafraisirNoeudsPére) {
      this.refreshEtatBlocsParent(blocOffre, emitEventToBlocsSubscribers);
    }


    if (this.hasThisBlocToBeIgnored(blocOffre)) {
      return blocOffre.etat;
    }

    if (emitEventToBlocsSubscribers === true) {
      this.refreshEtatBlocsSubscribers(blocOffre);
    }


    // revérifier état des fils
    this.checkEtatDesFils(blocOffre);

    // si bloc saisi alors vérifier dépendances (prerequis & exclusions)
    if (qteCmdBloc) {
      this.checkPrerequis(blocOffre, offre);
      this.checkExclusions(blocOffre, offre);
    }

    return blocOffre.etat;
  }



  private refreshEtatBlocsSubscribers(blocOffre: BlocOffre) {
    if (blocOffre.subscribers?.length) {
      for (const blocSubscriber of blocOffre.subscribers) {
        this.refreshEtatBlocOffre(blocSubscriber, false);
      }
    }
  }



  private refreshEtatBlocsParent(blocOffre: BlocOffre, emitEventToBlocsSubscribers: boolean) {
    let currentBloc = blocOffre;
    while (currentBloc) {
      currentBloc = currentBloc.parent;

      if (currentBloc) {
        this.refreshEtatBlocOffre(currentBloc, emitEventToBlocsSubscribers);
      }
    }
  }

  private checkConditionMultiple(blocOffre: BlocOffre) {
    if (blocOffre.totalQteCmd && blocOffre.multipleQtePrdCmd && (blocOffre.totalQteCmd % blocOffre.multipleQtePrdCmd) !== 0) { // qte multiple non respectée
      blocOffre.etat = 'I';
      blocOffre.messageEtat = 'Condition non respectée sur quantité commandée (doit être un multiple de ' + blocOffre.multipleQtePrdCmd + ') ';
    }
  }

  private checkEtatDesFils(blocOffre: BlocOffre) {
    for (const fils of blocOffre.listeFils) {
      if (fils.etat === 'I') {
        blocOffre.etat = 'I'; // forcer invalide
        blocOffre.messageEtat = fils.messageEtat;
        this.invaliderNoeudsParents(blocOffre, fils.messageEtat);
        break;
      }
    }
  }

  private checkExclusions(blocOffre: BlocOffre, offre: Offre) {
    if (blocOffre.listOfBlocOffresExclus && blocOffre.listOfBlocOffresExclus.length) {
      for (const idBlocExclu of blocOffre.listOfBlocOffresExclus) {
        const blocExclu = offre.mapBlocsById[idBlocExclu];

        if (blocExclu) {
          if (blocExclu.totalQteCmd) { // blocExclu saisi
            blocOffre.etat = 'I'; // forcer invalide
            blocOffre.messageEtat = 'Le bloc exclu ne doit pas être saisi (' + (blocExclu.titre || blocExclu.libelleProduit) + ').';
            this.invaliderNoeudsParents(blocOffre, blocOffre.messageEtat);
          }
        } else {
          console.warn('Attention le bloc avec id: ' + idBlocExclu + 'n\'est pas déclaré');
        }
      }
    }
  }

  private checkPrerequis(blocOffre: BlocOffre, offre: Offre) {
    if (blocOffre.listOfBlocOffresPrerequis && blocOffre.listOfBlocOffresPrerequis.length) {
      for (const idBlocPrerequis of blocOffre.listOfBlocOffresPrerequis) {
        const blocPrerequis = offre.mapBlocsById[idBlocPrerequis];

        if (blocPrerequis) {
          if (!blocPrerequis.totalQteCmd) { // blocPrerequis non saisi
            blocOffre.etat = 'I'; // forcer invalide
            blocOffre.messageEtat = 'Le bloc prerequis doit être saisi (' + (blocPrerequis.titre || blocPrerequis.libelleProduit) + ').';
            this.invaliderNoeudsParents(blocOffre, blocOffre.messageEtat);
          }
        } else {
          console.warn('Attention le bloc avec id: ' + idBlocPrerequis + 'n\'est pas déclaré');
        }
      }
    }
  }

  public getOffreRacine(blocOffreSource: BlocOffre): Offre {
    if (!blocOffreSource) {
      return null;
    }

    if (blocOffreSource.offre) {
      return blocOffreSource.offre;   // si déjà calculer
    }

    let currentBlocOffre = blocOffreSource;
    while (currentBlocOffre.parent) {
      currentBlocOffre = currentBlocOffre.parent;   // parent
    }

    blocOffreSource.offre = currentBlocOffre.offre; // set offre pour ne pas recalculer

    return currentBlocOffre.offre;
  }

  public getPackParent(blocOffreSource: BlocOffre): BlocOffre {   // last parent with no parent
    if (!blocOffreSource) {
      return null;
    }

    let currentBlocOffre = blocOffreSource;
    while (currentBlocOffre.parent) {
      currentBlocOffre = currentBlocOffre.parent;   // parent
    }

    return currentBlocOffre;
  }



  private refreshChampsCalculesParents(blocOffre: BlocOffre) {
    let currentBloc = blocOffre;
    while (currentBloc) {

      if (!currentBloc.parent) {  // bloc racine (maj offre)
        this.calculerTotauxOfOffre(currentBloc.offre);
      }

      currentBloc = currentBloc.parent;   // parent

      if (!currentBloc) {
        return;
      }

      this.calculerTotauxOfBloc(currentBloc, false);

      if (this.isBlocTypeProduit(currentBloc) == false &&
        currentBloc.listePaliers &&
        currentBloc.listePaliers.length > 0 &&
        currentBloc.listePaliers[0].ratioUg) {

        let palierSelectedBlocCourant: DetailValeurPalier = null;

        palierSelectedBlocCourant = currentBloc.currentSelectedPalier;

        if (palierSelectedBlocCourant) {
          this.setQteUgByRatioPalierConfig(currentBloc, palierSelectedBlocCourant);
        } else {
          currentBloc.totalQteUg = 0;
        }
      } else {
        currentBloc.totalQteUg = this.calculerTotalQuantiteUg(currentBloc, false);
      }

    }
  }



  // determiner les paliers sélectionnés
  private findAndApplyPalierToBloc(blocOffre: BlocOffre): DetailValeurPalier {
    let firstSelectedPalier = null;

    let currentBloc = blocOffre;
    while (currentBloc) {
      if (currentBloc.listePaliers && currentBloc.listePaliers.length) {

        let palierSelector = this.buildBlocPalierSelector(currentBloc);

        let lastPalierValide: DetailValeurPalier = this.findValeurPalierInBloc(currentBloc, palierSelector);

        // declarer selection palier
        for (const palier of currentBloc.listePaliers) {
          palier.selected = false;
        }

        // selectionner palier valide
        if (lastPalierValide) {
          lastPalierValide.selected = true;
        }

        currentBloc.previousSelectedPalier = currentBloc.currentSelectedPalier;
        currentBloc.currentSelectedPalier = lastPalierValide;

        if (
          (currentBloc.previousSelectedPalier != currentBloc.currentSelectedPalier) || 
          currentBloc.currentSelectedPalier?.typeSelectionRfUg === TypeSelectionRfUgEnum.OR
        ) {
          this.processApplicationPalierToBloc(currentBloc);
        }

        if (currentBloc.listePaliers && currentBloc.listePaliers.length) {   // seulement la 1ère liste non vide de paliers
          firstSelectedPalier = lastPalierValide;
        }

        break;  // sortir
      }

      currentBloc = currentBloc.parent;   // iterer les parents
    }

    return firstSelectedPalier;
  }



  private findValeurPalierInBloc(currentBloc: BlocOffre, palierSelector: PalierSelector) {
    let palierSelectionne: DetailValeurPalier = null;
    const offre: Offre = this.getOffreRacine(currentBloc); // pour tester sur flag valeur brute/nette?

    for (const palier of currentBloc.listePaliers) {
      const testSurValeurbrut: boolean = palier.isValeurNet == null || palier.isValeurNet === false;

      if (this.isBadPalier(palier, palierSelector, testSurValeurbrut, currentBloc)) {
        palierSelectionne = null;
      } else {
        palierSelectionne = palier;
        break;
      }
    }
    return palierSelectionne;
  }


  private isBadPalier(palier: DetailValeurPalier, palierSelector: PalierSelector, testSurValeurbrut: boolean, currentBloc: BlocOffre) {
    return (palier.qteMin && palierSelector.value < palier.qteMin) ||
      (palier.qteMax && palierSelector.value > palier.qteMax) ||
      (palier.valeurMin && palierSelector.value * (testSurValeurbrut ? 1 : (1 - (palier.tauxRf ?? 0) / 100.)) < palier.valeurMin) || // palierSelector.value   is always brut
      (palier.valeurMax && palierSelector.value * (testSurValeurbrut ? 1 : (1 - (palier.tauxRf ?? 0) / 100.)) > palier.valeurMax) || // palierSelector.value   is always brut
      (palier.nbrObjFilsMin && palierSelector.value < palier.nbrObjFilsMin) ||
      (palier.nbrObjFilsMax && palierSelector.value > palier.nbrObjFilsMax) ||
      (palier.nombreProduitsMin && palierSelector.value < palier.nombreProduitsMin) ||
      (palier.nombreProduitsMax && palierSelector.value > palier.nombreProduitsMax) ||
      (palier.delaiPaiement && palierSelector.delaiPaiement?.id !== palier.delaiPaiement?.id && !this.isDelaiPaiementDeTypeComptant(palier, palierSelector)) ||
      (palier.ratioUg && palier.qteMin 
                      && (palier.typeSelectionRfUg ==null || (palier.typeSelectionRfUg === TypeSelectionRfUgEnum.OR && currentBloc?.selectedTypeRemiseEnum === 'UG'))
                      && this.calculerTotalQteUgByRationPalierConfig(palier, currentBloc) == -1);
  }



  private isDelaiPaiementDeTypeComptant(palier: DetailValeurPalier, palierSelector: PalierSelector): boolean {
    if (!palierSelector.delaiPaiement) {
      return false;
    }

    if (
      palierSelector?.delaiPaiement?.code === MODE_PAIEMENT.CHEQUES ||
      palierSelector?.delaiPaiement?.code === MODE_PAIEMENT.ESPECES ||
      palierSelector?.delaiPaiement?.code === MODE_PAIEMENT.VIREMENT
    ) {
      return palier?.delaiPaiement?.label?.toLowerCase() === 'comptant';
    }

    return false;
  }

  mergePaliersDeModePaiementComptant(listePaliers: DetailValeurPalier[]) {

    const codesModePaiementComptant = [MODE_PAIEMENT.CHEQUES, MODE_PAIEMENT.ESPECES, MODE_PAIEMENT.VIREMENT];

    const allModePaiementComptantExist = codesModePaiementComptant.every(mode => listePaliers.map(palier => palier?.delaiPaiement?.code)?.includes(mode));

    if (allModePaiementComptantExist) {
      const palierModePaiementComptant = listePaliers.filter(palier => codesModePaiementComptant?.includes(palier?.delaiPaiement?.code as MODE_PAIEMENT));

      const modePaiementComptantTestValue = this.getResultOfPalier(palierModePaiementComptant[0]);
      const allModePaiementComptantHaveSameValue = palierModePaiementComptant.every(mode => isEqual(this.getResultOfPalier(mode), modePaiementComptantTestValue));

      if (allModePaiementComptantHaveSameValue) {
        const filteredListePaliers = listePaliers.filter(palier => !codesModePaiementComptant?.includes(palier?.delaiPaiement?.code as MODE_PAIEMENT));
        const selectedPalier = palierModePaiementComptant.find(palier => palier.selected) || palierModePaiementComptant[0];

        selectedPalier.delaiPaiement.label = 'comptant';
        filteredListePaliers.push(selectedPalier);

        return filteredListePaliers;
      }
    }

    return listePaliers;
  }

  private getResultOfPalier(palier: DetailValeurPalier) {
    return (palier?.tauxRf ? ['tauxRf', palier?.tauxRf] : null) ||
      (palier?.tauxEscompte ? ['tauxEscompte', palier?.tauxEscompte] : null) ||
      (palier?.tauxUg ? ['tauxUg', palier?.tauxUg] : null) ||
      (palier?.ratioUg ? ['ratioUg', palier?.ratioUg] : null) ||
      (palier?.listCadeaux?.length ? ['listCadeaux', palier?.listCadeaux] : null);
  }

  private buildBlocPalierSelector(currentBloc: BlocOffre): PalierSelector {
    let cmdValue = null;

    const firstPalier = currentBloc.listePaliers[0];

    let forceRecalculFils: boolean = false;

    let targetBlocForCalculValue: BlocOffre = currentBloc;  // by default calcul relatif to current bloc,   unless valueRelatifPack

    if (firstPalier.valueRelatifPack) {
      targetBlocForCalculValue = this.getPackParent(currentBloc);
      forceRecalculFils = true;
    }


    if (firstPalier.qteMin) {
      cmdValue = this.calculerTotalQuantiteCommande(targetBlocForCalculValue, forceRecalculFils);
    } else if (firstPalier.valeurMin) {
      cmdValue = this.calculerTotalValeurBruteCommande(targetBlocForCalculValue, forceRecalculFils);    // on prend le brut as value    (but the test is using brut + boolean isValeurNet)
    } else if (firstPalier.nbrObjFilsMin) {
      cmdValue = this.calculerNombreObjetsFils(targetBlocForCalculValue);
    } else if (firstPalier.nombreProduitsMin) {
      cmdValue = this.calculerNombreProduitsDescendants(targetBlocForCalculValue, forceRecalculFils);
    }

    let offre = this.getOffreRacine(currentBloc);

    return new PalierSelector(cmdValue, offre.delaiPaiement);
  }


  // appliquer palier aux fils
  private processApplicationPalierToBloc(blocOffre: BlocOffre, palier: DetailValeurPalier = null): void {
    if (!blocOffre) {
      return;
    }

    if (blocOffre.listePaliers && blocOffre.listePaliers.length) {
      let palierSelectedBlocCourant: DetailValeurPalier = blocOffre.currentSelectedPalier;

      if (palierSelectedBlocCourant) {
        palier = palierSelectedBlocCourant;   // ecraser palier avec le palier sélectionné du bloc courant
      } else {
        palier = null;
      }
    }

    if (this.isBlocTypeProduit(blocOffre)) {
      // bloc produit
      if (palier) {
        this.assignTauxRemiseAndTauxUgFromPalier(blocOffre, palier);
        this.applyAssignedPalierUg(blocOffre, palier);

        blocOffre.totalValeurNetteCmd = blocOffre.totalValeurBruteCmd * (1 - blocOffre.tauxRemise / 100);
      } else {
        blocOffre.tauxRemise = null;
        blocOffre.tauxUg = null;
        blocOffre.ratioUg = null;
        blocOffre.totalQteUg = null;
        blocOffre.totalValeurNetteCmd = blocOffre.totalValeurBruteCmd;
      }

      this.refreshChampsCalculesParents(blocOffre);

    } else {   // <> produit
      for (const blocFils of blocOffre.listeFils) {
        this.processApplicationPalierToBloc(blocFils, palier);
      }
    }
  }



  private applyAssignedPalierUg(blocOffre: BlocOffre, palier: DetailValeurPalier) {
    if (Math.abs(blocOffre.tauxUg) > 1e-2) {
      blocOffre.ratioUg = null;
      blocOffre.totalQteUg = Math.floor(blocOffre.tauxUg / 100 * blocOffre.totalQteCmd);
    } else if (palier.ratioUg &&
      (palier.typeSelectionRfUg == null ||
        palier.typeSelectionRfUg === TypeSelectionRfUgEnum.AND ||
        (palier.typeSelectionRfUg === TypeSelectionRfUgEnum.OR && this.isSelectedTypeRemiseUg(blocOffre) == true))) {
      if (blocOffre.listePaliers && blocOffre.listePaliers.length) {
        this.setQteUgByRatioPalierConfig(blocOffre, palier);
      }
      else {
        blocOffre.totalQteUg = blocOffre?.qteUgSaisie;
      }
    } else {
      blocOffre.tauxUg = null;
      blocOffre.ratioUg = null;
      blocOffre.totalQteUg = 0;
    }
  }

  private assignTauxRemiseAndTauxUgFromPalier(blocOffre: BlocOffre, palier: DetailValeurPalier) {
    blocOffre.tauxRemise = (blocOffre.plafondRemiseSpeciale == null ? (palier.tauxRf || 0) : Math.min(blocOffre.plafondRemiseSpeciale, (palier.tauxRf || 0)));
    blocOffre.tauxUg = (blocOffre.plafondUgSpeciale == null ? (palier.tauxUg || 0) : Math.min(blocOffre.plafondUgSpeciale, (palier.tauxUg || 0)));

    if (palier.typeSelectionRfUg === TypeSelectionRfUgEnum.OR) {

      if (blocOffre.selectedTypeRemiseEnum == null) {
        blocOffre.selectedTypeRemiseEnum = SelectedTypeRemiseEnum.RF;   // set it to Default RF if   null
      }

      if (this.isSelectedTypeRemiseRf(blocOffre) == true)
        blocOffre.tauxUg = 0.0;

      if (this.isSelectedTypeRemiseUg(blocOffre) == true)
        blocOffre.tauxRemise = 0.0;
    }
  }

  private isSelectedTypeRemiseUg(produitOffre: BlocOffre): boolean {
    return SelectedTypeRemiseEnum.UG === produitOffre.selectedTypeRemiseEnum;
  }

  private isSelectedTypeRemiseRf(produitOffre: BlocOffre): boolean {
    return produitOffre.selectedTypeRemiseEnum == null || SelectedTypeRemiseEnum.RF === produitOffre.selectedTypeRemiseEnum;
  }


  private setQteUgByRatioPalierConfig(blocOffre: BlocOffre, palier: DetailValeurPalier) {

    blocOffre.tauxUg = null;
    blocOffre.ratioUg = palier.ratioUg;

    blocOffre.totalQteUg = this.calculerTotalQteUgByRationPalierConfig(palier, blocOffre);
  }



  private calculerTotalQteUgByRationPalierConfig(palier: DetailValeurPalier, blocOffre: BlocOffre) {
    let ratioParts = palier.ratioUg.split(":");
    let chaqueQteCmd = +ratioParts[0];
    let qteUgDonnee = +ratioParts[1];

    let admettreRestes: boolean = this.isPalierRatioAdmettreRestes(palier);

    let standardQteUgExpression = qteUgDonnee * (blocOffre.totalQteCmd / chaqueQteCmd);

    let calculatedTotalQteUg = null;

    if (this.isPalierRatioAppliquerListeDefinie(palier)) { // liste definie (pas de test sur admettreRestes)
      if (blocOffre.totalQteCmd == chaqueQteCmd)
        calculatedTotalQteUg = standardQteUgExpression;
      else if (blocOffre.totalQteCmd != 0)
        calculatedTotalQteUg = -1;
    }
    else if (this.isPalierRatioAppliquerMultipleUnique(palier)) {
      if (admettreRestes) // cas Tous + multiple
        calculatedTotalQteUg = standardQteUgExpression;
      else { // cas uniquement + multiple
        if (blocOffre.totalQteCmd % chaqueQteCmd == 0)
          calculatedTotalQteUg = standardQteUgExpression;

        else
          calculatedTotalQteUg = -1;
      }
    }
    else if (this.isPalierRatioAppliquerToutMultipleAndOthers(palier)) {
      calculatedTotalQteUg = this.calculQteUgByCumulWithoutReste(palier, blocOffre.listePaliers, blocOffre.totalQteCmd, admettreRestes);
    }

    return calculatedTotalQteUg;
  }

  private calculQteUgByCumulWithoutReste(palier: DetailValeurPalier, listeAllPaliers: DetailValeurPalier[], totalQteCmd: number, accepterReste: boolean): number {

    let ratioParts = palier.ratioUg.split(":");
    let chaqueQteCmd = +ratioParts[0];
    let qteUgDonnee = +ratioParts[1];

    let reste = totalQteCmd % chaqueQteCmd;
    let totalUg = qteUgDonnee * (totalQteCmd / chaqueQteCmd);   // first init ug

    for (let index = listeAllPaliers.length - 1; index >= 0; index--) { //  to see other paliers from end to start
      let element: DetailValeurPalier = listeAllPaliers[index];
      ratioParts = element.ratioUg.split(":");
      chaqueQteCmd = +ratioParts[0];
      qteUgDonnee = +ratioParts[1];

      if (reste >= chaqueQteCmd) {
        let fac = reste / chaqueQteCmd;
        reste = reste % chaqueQteCmd;
        totalUg = totalUg + (qteUgDonnee * fac);
      }
    }

    if (reste == 0 || accepterReste == false)
      return totalUg;
    else
      return -1;
  }








  // determiner les paliers sélectionnés
  private findPalierToApplyToOffre(offre: Offre) {
    if (!offre.listePaliersRemisesAdditionnels || !offre.listePaliersRemisesAdditionnels.length) {
      return null;
    }

    let lastPalierValide: DetailValeurPalier = null;

    for (const palier of offre.listePaliersRemisesAdditionnels) {
      let palierSelector = this.buildOffrePalierSelector(offre, palier);

      if (this.isCmdValueNotRespectingConditions(palierSelector, palier)) {
        lastPalierValide = null;
      } else {
        lastPalierValide = palier;
        break;
      }
    }

    // declarer selection palier
    for (const palier of offre.listePaliersRemisesAdditionnels) {
      palier.selected = false;
    }

    // selectionner palier valide
    if (lastPalierValide) {
      lastPalierValide.selected = true;
    }

    return lastPalierValide;
  }

  private isCmdValueNotRespectingConditions(palierSelector: PalierSelector, palier: DetailValeurPalier) {
    return (palier.qteMin && palierSelector.value < palier.qteMin) ||
      (palier.qteMax && palierSelector.value > palier.qteMax) ||
      (palier.valeurMin && palierSelector.value < palier.valeurMin) ||
      (palier.valeurMax && palierSelector.value > palier.valeurMax) ||
      (palier.delaiPaiement && palierSelector.delaiPaiement?.id !== palier.delaiPaiement?.id && !this.isDelaiPaiementDeTypeComptant(palier, palierSelector));
  }

  private buildOffrePalierSelector(offre: Offre, palier: DetailValeurPalier): PalierSelector {
    let cmdValue: number | null = null;
    if (offre.listePaliersRemisesAdditionnels[0].qteMin) {
      cmdValue = offre.totalQteCmd;
    } else if (offre.listePaliersRemisesAdditionnels[0].valeurMin) {
      const testSurValeurbrut: boolean = palier.isValeurNet == null || palier.isValeurNet === false;
      if (testSurValeurbrut) {
        cmdValue = offre.totalValeurBruteCmd;
      } else {
        cmdValue = offre.totalValeurNetteCmd;
      }
    }
    return new PalierSelector(cmdValue, offre.delaiPaiement);
  }



  // calcul total qte cmd  (utiliser forceRecalculFils = false   si  fils ont déjà le champs totalQteCmd rempli)
  private calculerTotalQuantiteCommande(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit
      return blocOffre.qteCmd || 0;
    } else {
      let sumQteCmd = 0;

      if (blocOffre.listeFils) {
        sumQteCmd += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerTotalQuantiteCommande(b, forceRecalculFils) : (b.totalQteCmd || 0))
          , 0
        );
      }

      return sumQteCmd;
    }
  }



  private calculerQtePrdFixe(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit
      return blocOffre.qteFixePrdInCoffret || 0;
    } else {
      let sum = 0;

      if (blocOffre.listeFils) {
        sum += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerQtePrdFixe(b, forceRecalculFils) : (b.qteFixePrdInCoffret || 0))
          , 0
        );
      }

      return sum;
    }
  }



  // calcul total qte ug  (utiliser forceRecalculFils = false   si  fils ont déjà le champs totalQteUg rempli)
  private calculerTotalQuantiteUg(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit
      return blocOffre.totalQteUg || 0;
    } else {
      let sumQteUg = 0;

      if (blocOffre.listeFils) {
        sumQteUg += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerTotalQuantiteUg(b, forceRecalculFils) : (b.totalQteUg || 0))
          , 0
        );
      }

      return sumQteUg;
    }
  }
  // calcul total qte ug saisie (utiliser forceRecalculFils = false   si  fils ont déjà le champs totalQteUg rempli)
  private calculerTotalQuantiteUgSaisie(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit
      return blocOffre.qteUgSaisie || 0;
    } else {
      let sumQteUg = 0;

      if (blocOffre.listeFils) {
        sumQteUg += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerTotalQuantiteUgSaisie(b, forceRecalculFils) : (b.qteUgSaisie || 0))
          , 0
        );
      }

      return sumQteUg;
    }
  }





  // calcul total valeur cmd  (utiliser forceRecalculFils = false   si  fils ont déjà le champs totalValeurBruteCmd rempli)
  private calculerTotalValeurBruteCommande(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit

      const offre: Offre = this.getOffreRacine(blocOffre);
      if (!offre?.utiliserValeurHt || offre?.utiliserValeurHt === 'N') {
        return (blocOffre.qteCmd || 0) * (blocOffre.prixVenteTtc || 0);
      } else {
        return (blocOffre.qteCmd || 0) * (blocOffre.prixVenteHt || 0);
      }
    } else {
      let sumValeurCmd = 0;

      if (blocOffre.listeFils) {
        sumValeurCmd += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerTotalValeurBruteCommande(b, forceRecalculFils) : (b.totalValeurBruteCmd || 0))
          , 0
        );
      }

      return sumValeurCmd;
    }
  }
  // calcul total valeur brute cmd  (utiliser forceRecalculFils = false   si  fils ont déjà le champs totalValeurNetteCmd rempli)
  private calculerTotalValeurNetteCommande(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit

      const offre: Offre = this.getOffreRacine(blocOffre);
      if (!offre?.utiliserValeurHt || offre?.utiliserValeurHt === 'N') {
        return (blocOffre.qteCmd || 0) *
          (blocOffre.prixVenteTtc || 0) *
          (1 - (blocOffre.tauxRemise || 0) / 100);
      } else {
        return (blocOffre.qteCmd || 0) *
          (blocOffre.prixVenteHt || 0) *
          (1 - (blocOffre.tauxRemise || 0) / 100);
      }
    } else {
      let sumValeurCmd = 0;

      if (blocOffre.listeFils) {
        sumValeurCmd += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerTotalValeurNetteCommande(b, forceRecalculFils) : (b.totalValeurNetteCmd || 0))
          , 0
        );
      }

      return sumValeurCmd;
    }
  }













  private calculerTotalValeurBruteCommandeTtc(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit
      return (blocOffre.qteCmd || 0) * (blocOffre.prixVenteTtc || 0);
    } else {
      let sumValeurCmd = 0;

      if (blocOffre.listeFils) {
        sumValeurCmd += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerTotalValeurBruteCommandeTtc(b, forceRecalculFils) : (b.totalValeurBruteCmd || 0))
          , 0
        );
      }

      return sumValeurCmd;
    }
  }

  private calculerTotalValeurNetteCommandeTtc(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit
      return (blocOffre.qteCmd || 0) *
        (blocOffre.prixVenteTtc || 0) *
        (1 - (blocOffre.tauxRemise || 0) / 100);
    } else {
      let sumValeurCmd = 0;

      if (blocOffre.listeFils) {
        sumValeurCmd += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerTotalValeurNetteCommandeTtc(b, forceRecalculFils) : (b.totalValeurNetteCmd || 0))
          , 0
        );
      }

      return sumValeurCmd;
    }
  }






  // calcul total valeur cmd  (utiliser forceRecalculFils = false   si  fils ont déjà le champs totalValeurBruteCmd rempli)
  private calculerTotalValeurBruteCommandeHt(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit
      return (blocOffre.qteCmd || 0) * (blocOffre.prixVenteHt || 0);
    } else {
      let sumValeurCmd = 0;

      if (blocOffre.listeFils) {
        sumValeurCmd += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerTotalValeurBruteCommandeHt(b, forceRecalculFils) : (b.totalValeurBruteCmd || 0))
          , 0
        );
      }

      return sumValeurCmd;
    }
  }
  // calcul total valeur brute cmd  (utiliser forceRecalculFils = false   si  fils ont déjà le champs totalValeurNetteCmd rempli)
  private calculerTotalValeurNetteCommandeHt(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit
      return (blocOffre.qteCmd || 0) *
        (blocOffre.prixVenteHt || 0) *
        (1 - (blocOffre.tauxRemise || 0) / 100);
    } else {
      let sumValeurCmd = 0;

      if (blocOffre.listeFils) {
        sumValeurCmd += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerTotalValeurNetteCommandeHt(b, forceRecalculFils) : (b.totalValeurNetteCmd || 0))
          , 0
        );
      }

      return sumValeurCmd;
    }
  }










  private calculerNombreObjetsFils(blocOffre: BlocOffre): number {
    if (blocOffre.listeFils) {
      return blocOffre.listeFils.filter(fils => fils.totalQteCmd).length;   // nombre de fils commandés
    } else {
      return 0;
    }
  }
  private calculerNombreProduitsDescendants(blocOffre: BlocOffre, forceRecalculFils: boolean): number {
    if (this.isBlocTypeProduit(blocOffre)) {  // cas produit commandé => compter 1
      return blocOffre.qteCmd ? 1 : 0;
    } else {
      let countProduits = 0;

      if (blocOffre.listeFils) {
        countProduits += blocOffre.listeFils.reduce(
          (a, b) => +a + (forceRecalculFils ? this.calculerNombreProduitsDescendants(b, forceRecalculFils) : (b.totalNombreProduits || 0))
          , 0
        );
      }

      return countProduits;
    }
  }



  private invaliderNoeudsParents(blocOffre: BlocOffre, messageEtat: string) {
    let currentBloc = blocOffre;
    while (currentBloc) {
      currentBloc = currentBloc.parent;   // parent

      if (currentBloc) {
        currentBloc.etat = 'I';
        currentBloc.messageEtat = messageEtat;
      }
    }
  }



  private setEtatBasedOnLocalConditions(blocOffre: BlocOffre) {

    /**************** condition qte ******************/
    if (blocOffre.qteMin && blocOffre.totalQteCmd !== 0 && (blocOffre.totalQteCmd < blocOffre.qteMin)) {
      blocOffre.etat = 'I';
      blocOffre.messageEtat = `Conditions non respectées sur Quantité. Quantité Minimale requise est: ${blocOffre?.qteMin}.`;
    }
    if (blocOffre.qteMax && (blocOffre.totalQteCmd > blocOffre.qteMax)) {
      blocOffre.etat = 'I';
      blocOffre.messageEtat = `Conditions non respectées sur Quantité. Quantité Maximale est: ${blocOffre?.qteMax}.`;
    }

    /***************** condition valeur  ******************/
    if (blocOffre.valeurMin || blocOffre.valeurMax) {
      let valeurBloc = 0;
      let offre = this.getOffreRacine(blocOffre);
      if (offre.palierTestValeurBrut === 'O') {
        valeurBloc = this.calculerTotalValeurBruteCommande(blocOffre, false);
      }
      else {
        valeurBloc = this.calculerTotalValeurNetteCommande(blocOffre, false);
      }

      if ((blocOffre.valeurMin && (blocOffre.totalQteCmd !== 0 && valeurBloc < blocOffre.valeurMin))) {
        blocOffre.etat = 'I';
        blocOffre.messageEtat = `Conditions non respectées sur Valeur. Valeur Minimale requise est: ${this.decimalPipe.transform(blocOffre?.valeurMin, '1.2-2', 'fr-FR')}`;
      }
      if ((blocOffre.valeurMax && (valeurBloc > blocOffre.valeurMax))) {
        blocOffre.etat = 'I';
        blocOffre.messageEtat = `Conditions non respectées sur Valeur. Valeur Maximale est: ${this.decimalPipe.transform(blocOffre?.valeurMax, '1.2-2', 'fr-FR')}`;
      }
    }

    /**************** condition nbrObFils ******************/
    if ((blocOffre.nbrObjFilsMin && (blocOffre.totalQteCmd !== 0 && blocOffre.totalNbrObjFils < blocOffre.nbrObjFilsMin))) {
      blocOffre.etat = 'I';
      blocOffre.messageEtat = `Conditions non respectées sur Nombre de sous-blocs commandés. Nombre minimal de sous-blocs requis est: ${blocOffre?.nbrObjFilsMin}`;
    }
    if ((blocOffre.nbrObjFilsMax && (blocOffre.totalNbrObjFils > blocOffre.nbrObjFilsMax))) {
      blocOffre.etat = 'I';
      blocOffre.messageEtat = `Conditions non respectées sur Nombre de sous-blocs commandés. Nombre maximal de sous-blocs est: ${blocOffre?.nbrObjFilsMax}`;
    }

    /**************** condition nbrProduit en dessous de l'arborescence ******************/
    if ((blocOffre.nombreProduitsMin && (blocOffre.totalQteCmd !== 0 && blocOffre.totalNombreProduits < blocOffre.nombreProduitsMin))) {
      blocOffre.etat = 'I';
      blocOffre.messageEtat = `Conditions non respectées sur Nombre de produits commandés. Nombre de produits minimal requis est: ${blocOffre?.nombreProduitsMin}`;
    }
    if ((blocOffre.nombreProduitsMax && (blocOffre.totalNombreProduits > blocOffre.nombreProduitsMax))) {
      blocOffre.etat = 'I';
      blocOffre.messageEtat = `Conditions non respectées sur Nombre de produits commandés. Nombre de produits maximal est: ${blocOffre?.nombreProduitsMax}`;
    }
  }

  private setEtatBasedOnOffreConditions(offre: Offre) {

    /**************** condition qte ******************/
    if ((offre.qteMin && (offre.totalQteCmd < offre.qteMin))) {
      offre.etatCmdOffre = 'I';
      offre.messageEtatCmdOffre = `Conditions non respectées sur Quantité. Quantité Minimale requise est: ${offre?.qteMin}.`;
    }
    if ((offre.qteMax && (offre.totalQteCmd > offre.qteMax))) {
      offre.etatCmdOffre = 'I';
      offre.messageEtatCmdOffre = `Conditions non respectées sur Quantité. Quantité Maximale est: ${offre?.qteMax}.`;
    }

    /**************** condition valeur ******************/
    if (offre.valeurMin || offre.valeurMax) {
      let valeurOffre = 0;
      if (offre.palierTestValeurBrut === 'O') {
        valeurOffre = offre.totalValeurBruteCmd;
      }
      else {
        valeurOffre = offre.totalValeurNetteCmd;
      }

      if ((offre.valeurMin && (valeurOffre < offre.valeurMin))) {
        offre.etatCmdOffre = 'I';
        offre.messageEtatCmdOffre = `Conditions non respectées sur Valeur. Valeur Minimale requise est: ${this.decimalPipe.transform(offre?.valeurMin, '1.2-2', 'fr-FR')}`;
      }
      if ((offre.valeurMax && (valeurOffre > offre.valeurMax))) {
        offre.etatCmdOffre = 'I';
        offre.messageEtatCmdOffre = `Conditions non respectées sur Valeur. Valeur Maximale est: ${this.decimalPipe.transform(offre?.valeurMax, '1.2-2', 'fr-FR')}`;
      }
    }
  }


  private setEtatBaseOnDynamicCondition(blocOffre: BlocOffre, selectedPalier: DetailValeurPalier): void {
    let dynamicScriptCondition = blocOffre.dynamicScriptCondition;
    if (!dynamicScriptCondition) {
      return;
    }

    dynamicScriptCondition = dynamicScriptCondition.trim();
    if (dynamicScriptCondition.length == 0) {
      return;
    }


    let scriptEvaluator: OffreDynamicScriptEvaluator = new OffreDynamicScriptEvaluatorBuilder()
      .withBlocOffre(blocOffre)
      .withOffre(this.getOffreRacine(blocOffre))
      .withBlocPackParent(this.getPackParent(blocOffre))
      .withSelectedPalier(selectedPalier)
      .withDynamicScriptCondition(dynamicScriptCondition)
      .withClient(this.authService.getPrincipal()?.societe)
      .build();

    let evaluationResponse: ScriptEvaluationResponse = this.evaluateDyanmicCondition(scriptEvaluator);
    if (evaluationResponse && evaluationResponse.error === true) {
      blocOffre.etat = 'I';
      blocOffre.messageEtat = evaluationResponse.message;
    }

  }



  private evaluateDyanmicCondition(scriptEvaluator: OffreDynamicScriptEvaluator): ScriptEvaluationResponse {
    let evaluationResult: any = scriptEvaluator.execute();
    if (evaluationResult === false) {
      return { error: true, message: "Condition Dynamique non respectée" };
    }
    else if (typeof evaluationResult == 'object') {

      if (("error" in evaluationResult) == false) {
        console.warn("{}   does not contain  field 'error'", evaluationResult);
        return null;
      }

      if (evaluationResult["error"] === false) // valide   et pas d'erreur
        return null;

      if (evaluationResult["message"] == undefined) {
        console.warn("{}   does not contain  field 'message'", evaluationResult);
        return null;
      }

      // ici non valide erreur ==> set etat of bloc  and  take the message

      return { error: true, message: evaluationResult["message"] };
    }
    else {
      return null;
    }
  }


  private calculerTotauxOfOffre(offre: Offre) {
    if (offre) {
      offre.totalQteCmd = 0;
      offre.totalQteUg = 0;
      offre.TotalUgSaisie = 0;
      offre.totalValeurBruteCmd = 0;
      offre.totalValeurNetteCmd = 0;
      offre.totalValeurBruteCmdTtc = 0;
      offre.totalValeurNetteCmdTtc = 0;
      offre.totalValeurBruteCmdHt = 0;
      offre.totalValeurNetteCmdHt = 0;

      for (const bloc of offre.listeBlocs) {
        offre.totalQteCmd += this.calculerTotalQuantiteCommande(bloc, false);
        offre.totalQteUg += this.calculerTotalQuantiteUg(bloc, false);
        offre.TotalUgSaisie += this.calculerTotalQuantiteUgSaisie(bloc, false);
        offre.totalValeurBruteCmd += this.calculerTotalValeurBruteCommande(bloc, false);
        offre.totalValeurNetteCmd += this.calculerTotalValeurNetteCommande(bloc, false);
        offre.totalValeurBruteCmdTtc += this.calculerTotalValeurBruteCommandeTtc(bloc, false);
        offre.totalValeurNetteCmdTtc += this.calculerTotalValeurNetteCommandeTtc(bloc, false);
        offre.totalValeurBruteCmdHt += this.calculerTotalValeurBruteCommandeHt(bloc, false);
        offre.totalValeurNetteCmdHt += this.calculerTotalValeurNetteCommandeHt(bloc, false);
      }
    } else {
      console.warn('offre is null');
    }
  }



  private calculerTotauxOfBloc(blocOffre: BlocOffre, forceRecalculFils: boolean) {
    blocOffre.totalQteCmd = this.calculerTotalQuantiteCommande(blocOffre, forceRecalculFils);
    blocOffre.totalValeurBruteCmd = this.calculerTotalValeurBruteCommande(blocOffre, forceRecalculFils);
    blocOffre.totalValeurNetteCmd = this.calculerTotalValeurNetteCommande(blocOffre, forceRecalculFils);
    blocOffre.totalValeurBruteCmdTtc = this.calculerTotalValeurBruteCommandeTtc(blocOffre, forceRecalculFils);
    blocOffre.totalValeurNetteCmdTtc = this.calculerTotalValeurNetteCommandeTtc(blocOffre, forceRecalculFils);
    blocOffre.totalValeurBruteCmdHt = this.calculerTotalValeurBruteCommandeHt(blocOffre, forceRecalculFils);
    blocOffre.totalValeurNetteCmdHt = this.calculerTotalValeurNetteCommandeHt(blocOffre, forceRecalculFils);
    blocOffre.totalNbrObjFils = this.calculerNombreObjetsFils(blocOffre);
    blocOffre.totalNombreProduits = this.calculerNombreProduitsDescendants(blocOffre, forceRecalculFils);
    blocOffre.totalQtePrdFixe = this.calculerQtePrdFixe(blocOffre, forceRecalculFils);

    blocOffre.totalNbrPackCmd = blocOffre.totalQtePrdFixe ? blocOffre.totalQteCmd / blocOffre.totalQtePrdFixe : null;

    blocOffre.qteUgSaisie = this.calculerTotalQuantiteUgSaisie(blocOffre, forceRecalculFils);
  }



  private resetEtatBlocOffre(blocOffre: BlocOffre) {
    blocOffre.etat = '';
    blocOffre.messageEtat = '';
  }

  private isBlocTypeProduit(blocOffre: BlocOffre): boolean {
    return blocOffre.typeBloc === 'F';
  }

  private isPalierRatioAdmettreRestes(palier: DetailValeurPalier): boolean {
    return "T" === palier.reste;
  }

  private isPalierRatioAppliquerListeDefinie(palier: DetailValeurPalier): boolean {
    return "N" === palier.multiple;
  }

  private isPalierRatioAppliquerToutMultipleAndOthers(palier: DetailValeurPalier): boolean {
    return "C" === palier.multiple;
  }

  private isPalierRatioAppliquerMultipleUnique(palier: DetailValeurPalier): boolean {
    return "O" === palier.multiple;
  }


  /**************************************************************** fin refreshEtatBlocOffre ****************************************************************/







  /**************************************** Debut refresh Etat bloc offre sur commande unitaire ****************************************/





  initialiserEtatOffreUnitaire(offre: Offre) {
    if (offre && offre.listeBlocs) {

      offre.mapBlocsById = {};

      for (const bloc of offre.listeBlocs) {
        bloc.offre = offre;   // remplir offre au niveau des premiers fils seulement
        bloc.hash = (Math.floor(Math.random() * (999999 - 100000)) + 100000).toString();
        bloc.toJSON = toJSON;
        this.setDefaultFieldsOfBlocCreation(bloc);

        this.initConditionUnitOnBlocOffre(bloc);
        this.initialiserEtatCommandeUnitaireBlocOffre(bloc);

        this.declareIdBlocOffre(offre, bloc);
      }

      offre.coffretEnabled = offre.listeBlocs.findIndex(blocPack => blocPack.coffretEnabled) != -1;

      if (offre?.docImageOffre) {
        offre.offreImageUrl = this.uploadService.fetchUploadedDocument(offre?.docImageOffre?.idhash);
      }
    }
  }



  private initialiserEtatCommandeUnitaireBlocOffre(blocOffre: BlocOffre): void {
    if (!blocOffre) {
      return;
    }


    if (this.isBlocTypeProduit(blocOffre)) {
      this.refreshEtatBlocOffreSurCmdUnit(blocOffre);
    } else {
      for (const blocFils of blocOffre.listeFils) {
        this.initialiserEtatCommandeUnitaireBlocOffre(blocFils);
      }
    }
  }


  refreshEtatBlocOffreSurCmdUnit(blocOffre: BlocOffre, verifierEtatFreres: boolean = true): string {
    if (!blocOffre) {
      return null;
    }

    let ignoreRafraisirNoeudsPére = false;
    const offre: Offre = this.getOffreRacine(blocOffre);    // get offre

    const forceRecalculFils = (this.isBlocTypeProduit(blocOffre));

    this.resetEtatBlocOffre(blocOffre);

    this.calculerTotauxOfBloc(blocOffre, forceRecalculFils);

    const qteCmdBloc = blocOffre.totalQteCmd;

    // rafraichir champs calculés des parents
    if (verifierEtatFreres) {  // ignorer cas verification frere
      this.refreshChampsCalculesParents(blocOffre);
    }


    this.setEtatBasedOnConditionsSpecificToGroup(blocOffre);



    // si bloc produit invalide alors invalider aussi tous les noeuds père
    if (blocOffre.etat === 'I') {
      this.invaliderNoeudsParents(blocOffre, blocOffre.messageEtat);
    } else {
      if (!qteCmdBloc) {
        blocOffre.etat = 'N'; // non saisi
        blocOffre.messageEtat = '';
      } else {
        blocOffre.etat = 'V';   // valide
        blocOffre.messageEtat = '';
        ignoreRafraisirNoeudsPére = false;
      }
    }

    // rafraichir etat des noeuds pères
    if (!ignoreRafraisirNoeudsPére) {
      this.refreshEtatBlocsParentModeUntaire(blocOffre);
    }


    // revérifier état des fils
    this.checkEtatDesFils(blocOffre);

    // si bloc saisi alors vérifier dépendances (prerequis & exclusions)
    if (qteCmdBloc) {
      this.checkPrerequis(blocOffre, offre);
      this.checkExclusions(blocOffre, offre);
    }

    return blocOffre.etat;
  }



  private setEtatBasedOnConditionsSpecificToGroup(blocOffre: BlocOffre) {
    const conditionsSpecificToGroup: ConditionBlocOffreCommandeConsolidee = blocOffre.conditionCmdUnitaireSpecGroup;

    if (conditionsSpecificToGroup) {
      /**************** condition qte ******************/
      if (blocOffre.totalQteCmd > 0) {
        if ((conditionsSpecificToGroup.qteMin && (blocOffre.totalQteCmd < conditionsSpecificToGroup.qteMin))) {
          blocOffre.etat = 'I';
          blocOffre.messageEtat = `Conditions non respectées sur Quantité. Quantité Minimale requise est: ${conditionsSpecificToGroup?.qteMin}.`;
        }
        if ((conditionsSpecificToGroup.qteMax && (blocOffre.totalQteCmd > conditionsSpecificToGroup.qteMax))) {
          blocOffre.etat = 'I';
          blocOffre.messageEtat = `Conditions non respectées sur Quantité. Quantité Maximale est: ${conditionsSpecificToGroup?.qteMax}.`;
        }

        /***************** condition valeur  ******************/
        if ((conditionsSpecificToGroup.valeurMin && (blocOffre.totalValeurBruteCmd < conditionsSpecificToGroup.valeurMin))) {
          blocOffre.etat = 'I';
          blocOffre.messageEtat = `Conditions non respectées sur Valeur. Valeur Minimale requise est: ${this.decimalPipe.transform(conditionsSpecificToGroup?.valeurMin, '1.2-2', 'fr-FR')}`;
        }
        if ((conditionsSpecificToGroup.valeurMax && (blocOffre.totalValeurBruteCmd > conditionsSpecificToGroup.valeurMax))) {
          blocOffre.etat = 'I';
          blocOffre.messageEtat = `Conditions non respectées sur Valeur. Valeur Maximale est: ${this.decimalPipe.transform(conditionsSpecificToGroup?.valeurMax, '1.2-2', 'fr-FR')}`;
        }
      }
      else if (blocOffre.totalQteCmd == 0) {
        if (conditionsSpecificToGroup.blocObligatoire === 'O') {

          let packParent = this.getPackParent(blocOffre);
          if (packParent === blocOffre || packParent.totalQteCmd !== 0) {
            blocOffre.etat = 'I';
            blocOffre.messageEtat = 'Element obligatoire non saisi';
          }
        }
      }
    }
  }



  private initConditionUnitOnBlocOffre(blocOffre: BlocOffre): void {
    if (!blocOffre) {
      return;
    }

    blocOffre.conditionCmdUnitaireSpecGroup = this.fetchInitialConditionUnitByBlocOffre(blocOffre);

    for (const blocFils of blocOffre.listeFils) {
      this.initConditionUnitOnBlocOffre(blocFils);
    }
  }


  private fetchInitialConditionUnitByBlocOffre(bloc: BlocOffre): ConditionBlocOffreCommandeConsolidee {
    let conditionUnitaire: ConditionBlocOffreCommandeConsolidee = null;

    let offre = this.getOffreRacine(bloc);
    if (offre.listeConditionsBlocOffreCmd?.length) {
      conditionUnitaire = offre.listeConditionsBlocOffreCmd.find(cond => cond.blocOffreId === bloc.id);
    }

    if (!conditionUnitaire) {
      conditionUnitaire = { blocOffreId: bloc.id };
    }

    return conditionUnitaire;
  }

  public fetchAllConditionUnitInAllInnerBlocOffres(offre: Offre): ConditionBlocOffreCommandeConsolidee[] {
    let result: ConditionBlocOffreCommandeConsolidee[] = [];

    for (const blocOffre of offre.listeBlocs) {
      this.fetchAllConditionUnitInAllInnerBlocOffresBis(blocOffre, result);
    }

    return result;
  }

  private fetchAllConditionUnitInAllInnerBlocOffresBis(blocOffre: BlocOffre, accumulatedResult: ConditionBlocOffreCommandeConsolidee[]) {
    if (blocOffre.conditionCmdUnitaireSpecGroup) {
      accumulatedResult.push(blocOffre.conditionCmdUnitaireSpecGroup);
    }

    for (const blocFils of blocOffre.listeFils) {
      this.fetchAllConditionUnitInAllInnerBlocOffresBis(blocFils, accumulatedResult);
    }
  }




  private refreshEtatBlocsParentModeUntaire(blocOffre: BlocOffre) {
    let currentBloc = blocOffre;
    while (currentBloc) {
      currentBloc = currentBloc.parent;

      if (currentBloc) {
        this.refreshEtatBlocOffreSurCmdUnit(currentBloc);
      }
    }
  }











































  palierOffreTraitement(offre: Offre) {
    offre.NetteAvantRemiseAdd = null;
    if (!offre.listePaliersRemisesAdditionnels) {
      offre.listePaliersRemisesAdditionnels = [];
    }

    const valeurpalier = this.findPalierToApplyToOffre(offre);

    offre.etatCmdOffre = '';
    offre.messageEtatCmdOffre = '';

    this.setEtatBasedOnOffreConditions(offre);
    this.setOffreGlobalEtatBaseOnDynamicCondition(offre, valeurpalier);


    if (!valeurpalier) {
      return;
    }

    offre.NetteAvantRemiseAdd = offre.totalValeurNetteCmd;


    const testSurValeurbrut: boolean = valeurpalier.isValeurNet == null || valeurpalier.isValeurNet === false;
    if (testSurValeurbrut) {
      offre.totalValeurNetteCmd = offre.totalValeurBruteCmd - (offre.totalValeurBruteCmd * valeurpalier.tauxRf / 100.) - (offre.totalValeurBruteCmd - offre.totalValeurNetteCmd);
    } else {
      offre.totalValeurNetteCmd = offre.totalValeurBruteCmd - (offre.totalValeurNetteCmd * valeurpalier.tauxRf / 100.) - (offre.totalValeurBruteCmd - offre.totalValeurNetteCmd);
    }

    let tauxEscompte = valeurpalier.tauxEscompte ?? 0;
    let valeurEscompte = (offre.totalValeurNetteCmd * tauxEscompte / 100.);  // ht or ttc
    let valeurEscompteTtc = (offre.totalValeurNetteCmdTtc * tauxEscompte / 100.);
    let valeurEscompteHt = (offre.totalValeurNetteCmdHt * tauxEscompte / 100.);
    offre.valeurEscompteCmd = valeurEscompteTtc;

    offre.totalValeurNetteCmd = offre.totalValeurNetteCmd - valeurEscompte;
    offre.totalValeurNetteCmdTtc = offre.totalValeurNetteCmdTtc - valeurEscompteTtc;
    offre.totalValeurNetteCmdHt = offre.totalValeurNetteCmdHt - valeurEscompteHt

  }




  private setOffreGlobalEtatBaseOnDynamicCondition(offre: Offre, selectedPalier: DetailValeurPalier): void {

    let dynamicScriptCondition = offre.dynamicScriptCondition;
    if (!dynamicScriptCondition) {
      return;
    }

    dynamicScriptCondition = dynamicScriptCondition.trim();
    if (dynamicScriptCondition.length == 0) {
      return;
    }


    let scriptEvaluator: OffreDynamicScriptEvaluator = new OffreDynamicScriptEvaluatorBuilder()
      .withOffre(offre)
      .withSelectedPalier(selectedPalier)
      .withDynamicScriptCondition(dynamicScriptCondition)
      .withClient(this.authService.getPrincipal()?.societe)
      .build();

    let evaluationResponse: ScriptEvaluationResponse = this.evaluateDyanmicCondition(scriptEvaluator);
    if (evaluationResponse && evaluationResponse.error === true) {
      offre.etatCmdOffre = 'I';
      offre.messageEtatCmdOffre = evaluationResponse.message;
    }
  }




















  changeEtatBar(etat: string) {
    this.EtatLeftBar.next(etat);
  }




  getValidCommandeObservable(): Observable<any> {
    return this.subject.asObservable();
  }





  // return 'Q' si type quantite, 'V' si type valeur, 'S' si type Sous-element Fils ,   'P'  si type nbr Prd CMD
  private getSignatureTypePalier(palier: DetailValeurPalier): string {
    let signature = '';
    if (palier.qteMin || palier.qteMax) {
      signature = signature + 'Q';
    }
    if (palier.valeurMin || palier.valeurMax) {
      signature = signature + 'V';
    }
    if (palier.nbrObjFilsMin || palier.nbrObjFilsMax) {
      signature = signature + 'S';
    }
    if (palier.nombreProduitsMin || palier.nombreProduitsMax) {
      signature = signature + 'P';
    }
    if (palier.delaiPaiement) {
      signature = signature + 'D';
    }

    return signature;
  }

  private checkRatioUgenfant(bloc: BlocOffre): any {
    const currentbloc = bloc;
    if (currentbloc.listeFils) {
      for (const iterator of currentbloc.listeFils) {
        if (this.isBlocTypeProduit(iterator) && iterator.listePaliers && iterator.listePaliers.length && iterator.listePaliers[0].ratioUg) {
          return 'RatioUg existe deja dans un sous-bloc';
        } else if (iterator.typeBloc !== 'F') {
          return this.checkRatioUgenfant(iterator);
        }
      }
    }
    return null;
  }

  private checkRatioUgparent(bloc: BlocOffre) {
    let currentbloc = bloc.parent;
    while (currentbloc) {
      if (currentbloc.listePaliers && currentbloc.listePaliers.length && currentbloc.listePaliers[0].ratioUg) {
        return 'RatioUg existe deja dans un bloc parent';
      }
      currentbloc = currentbloc.parent;
    }
    return null;
  }



  private checkPalierChamps(palier: DetailValeurPalier): string {
    if (!palier.qteMin && !palier.valeurMin && !palier.nbrObjFilsMin && !palier.nombreProduitsMin && !palier.delaiPaiement) {
      return 'Veuillez SVP saisir une Qte ou une valeur ou un delai de paiement';
    }

    if (palier.tauxRf === null && !palier.tauxUg && !palier.listCadeaux?.length && !palier.ratioUg && palier.tauxEscompte == null) {
      return 'Veuillez SVP saisir un taux RF ou un taux UG ou  un ratio UG ou des cadeaux ou un taux d\'escompte';
    }

    const CODE_PATTERN = /^[0-9]+:[0-9]+$/;
    if (palier.ratioUg !== null && !CODE_PATTERN.test(palier.ratioUg)) {
      return 'Veuillez SVP saisir un RatioUG sous forme    nombre:nombre    (exemple  1:6 )';
    }

    return null;
  }



  controlerEtAjouterPalier(bloc: BlocOffre, palier: DetailValeurPalier, paliers: DetailValeurPalier[]) {
    let messageErrorPalier;

    if (bloc && palier.ratioUg) {
      if (this.isBlocTypeProduit(bloc) == false) {
        messageErrorPalier = this.checkRatioUgenfant(bloc);
      } else {
        messageErrorPalier = this.checkRatioUgparent(bloc);
      }

      if (messageErrorPalier) {
        return messageErrorPalier;
      }
    }

    messageErrorPalier = this.checkPalierChamps(palier);
    if (messageErrorPalier) {
      return messageErrorPalier;
    }


    if (!paliers.length) {
      paliers.push(palier);   // first
      return;
    }


    const firstPalier = paliers[0];

    const typeFirstPalier = this.getSignatureTypePalier(firstPalier);
    const typeNewPalier = this.getSignatureTypePalier(palier);

    if (typeFirstPalier !== typeNewPalier) {
      return 'Veuillez ajouter un palier du même type que les autres';
    }


    // redondance
    this.patchRendundantPalierFields(palier, firstPalier);


    paliers.push(palier);

    this.sortPaliers(paliers, typeFirstPalier);


    // re-setting max values
    this.refreshMaxPaliers(paliers);

    return null;
  }



  private patchRendundantPalierFields(targetPalier: DetailValeurPalier, sourcePalier: DetailValeurPalier) {
    targetPalier.valueRelatifPack = sourcePalier.valueRelatifPack;
    targetPalier.reste = sourcePalier.reste;
    targetPalier.multiple = sourcePalier.multiple;
    targetPalier.isValeurNet = sourcePalier.isValeurNet;
  }



  controlerModificationPalier(palier: DetailValeurPalier, paliers: DetailValeurPalier[], qteMin: number, valMin: number): any {


    let messageErrorPalier = this.checkPalierChamps(palier);
    if (messageErrorPalier) {
      return messageErrorPalier;
    }

    const firstPalier = paliers[0];

    const typeFirstPalier = this.getSignatureTypePalier(firstPalier);
    const typeNewPalier = this.getSignatureTypePalier(palier);

    if (typeFirstPalier !== typeNewPalier) {
      return 'Veuillez ajouter un palier du même type que les autres';
    }


    if (palier === firstPalier) {
      for (let i = 1; i < paliers.length; i++) {
        this.patchRendundantPalierFields(paliers[i], firstPalier);
      }
    }


    this.sortPaliers(paliers, typeFirstPalier);

    // re-setting max values
    this.refreshMaxPaliers(paliers);

    return null;
  }

  private sortPaliers(paliers: DetailValeurPalier[], typeFirstPalier: string) {
    paliers.sort((pal1, pal2) => {
      if (typeFirstPalier.includes('Q')) {
        return pal1.qteMin - pal2.qteMin;
      }
      if (typeFirstPalier.includes('V')) {
        return pal1.valeurMin - pal2.valeurMin;
      }
      if (typeFirstPalier.includes('S')) {
        return pal1.nbrObjFilsMin - pal2.nbrObjFilsMin;
      }
      if (typeFirstPalier.includes('P')) {
        return pal1.nombreProduitsMin - pal2.nombreProduitsMin;
      }
      return 0;
    });
  }

  refreshMaxPaliers(paliers: DetailValeurPalier[]) {

    if (!paliers.length) {
      return;
    }

    const firstPalier = paliers[0];

    const typeFirstPalier = this.getSignatureTypePalier(firstPalier);

    // re-setting max values
    for (let indexPalier = 0; indexPalier < paliers.length - 1; indexPalier++) {
      const elementCourant = paliers[indexPalier];
      const elementSuivantDifferent = this.findNextPalierDifferent(paliers, typeFirstPalier, indexPalier);

      elementCourant.qteMax = null;
      elementCourant.valeurMax = null;
      elementCourant.nbrObjFilsMax = null;
      elementCourant.nombreProduitsMax = null;

      if (!elementSuivantDifferent) {
        continue;
      }

      if (typeFirstPalier.includes('Q')) {
        elementCourant.qteMax = elementSuivantDifferent.qteMin - 1;
      }
      else if (typeFirstPalier.includes('V')) {
        elementCourant.valeurMax = elementSuivantDifferent.valeurMin - 0.05;
      }
      else if (typeFirstPalier.includes('S')) {
        elementCourant.nbrObjFilsMax = elementSuivantDifferent.nbrObjFilsMin - 1;
      }
      else if (typeFirstPalier.includes('P')) {
        elementCourant.nombreProduitsMax = elementSuivantDifferent.nombreProduitsMin - 1;
      }
    }

    paliers[paliers.length - 1].qteMax = null;
    paliers[paliers.length - 1].valeurMax = null;
    paliers[paliers.length - 1].nbrObjFilsMax = null;
    paliers[paliers.length - 1].nombreProduitsMax = null;
  }

  private findNextPalierDifferent(paliers: DetailValeurPalier[], typeFirstPalier: string, currentIndexPalier: number): DetailValeurPalier | null {
    const elementRelatif = paliers[currentIndexPalier];

    for (let indexPalier = currentIndexPalier + 1; indexPalier < paliers.length; indexPalier++) {
      const element = paliers[indexPalier];

      if (
        (typeFirstPalier.includes('Q') && element.qteMin !== elementRelatif.qteMin) ||
        (typeFirstPalier.includes('V') && Math.abs(element.valeurMin - elementRelatif.valeurMin) > 1e-2) ||
        (typeFirstPalier.includes('S') && element.nbrObjFilsMin !== elementRelatif.nbrObjFilsMin) ||
        (typeFirstPalier.includes('P') && element.nombreProduitsMin !== elementRelatif.nombreProduitsMin)
      ) {
        return element;
      }
    }

    return null;
  }











  supprimerBlocOffre(blocOffre: BlocOffre, indexInParent: number = null) {

    if (!blocOffre) {
      return;
    }

    const offre: Offre = this.getOffreRacine(blocOffre);

    if (blocOffre.parent) {  // avec parent

      if (indexInParent === null) {
        indexInParent = blocOffre.parent.listeFils.findIndex(bloc => bloc.hash === blocOffre.hash);
      }

      blocOffre.parent.listeFils.splice(indexInParent, 1);
    } else {  // parent = offre
      if (indexInParent === null) {
        indexInParent = offre.listeBlocs.findIndex(bloc => bloc.hash === blocOffre.hash);
      }

      offre.listeBlocs.splice(indexInParent, 1);
    }

    // supprimer de la map
    delete offre.mapBlocsById[blocOffre.id];
  }




  dupliquerBlocOffre(bloc: BlocOffre, inParentBloc: BlocOffre = null, firstCall: boolean = true, count: number = 0, hash: string = null): BlocOffre {
    console.log('count:', count);


    if (count >= 1000) {
      console.error('count exceeded 1000');
      return null;
    }


    const newBloc = {
      titre: bloc.titre + '  (Copie)',
      description: bloc.description,
      typeBloc: bloc.typeBloc,
      hash: hash,
      blocOrder: bloc.blocOrder,
      blocObligatoire: bloc.blocObligatoire,
      valeurMin: bloc.valeurMin,
      valeurMax: bloc.valeurMax,
      qteMin: bloc.qteMin,
      qteMax: bloc.qteMax,
      colisage: bloc?.colisage,
      nbrObjFilsMin: bloc.nbrObjFilsMin,
      nbrObjFilsMax: bloc.nbrObjFilsMax,
      nombreProduitsMin: bloc.nombreProduitsMin,
      nombreProduitsMax: bloc.nombreProduitsMax,
      dynamicScriptCondition: bloc.dynamicScriptCondition,
      ratioUg: bloc.ratioUg,
      typeRemise: bloc.typeRemise,
      coffretEnabled: bloc.coffretEnabled,
      qteFixePrdInCoffret: bloc.qteFixePrdInCoffret,

      catalogue: bloc.catalogue,
      codeProduitCatalogue: bloc.codeProduitCatalogue,
      libelleProduit: bloc.libelleProduit,
      prixVenteHt: bloc.prixVenteHt,
      prixVenteTtc: bloc.prixVenteTtc,
      ppv: bloc.ppv,
      plafondRemiseSpeciale: bloc.plafondRemiseSpeciale,
      plafondUgSpeciale: bloc.plafondUgSpeciale,
      multipleQtePrdCmd: bloc.multipleQtePrdCmd,

      offre: bloc.offre,
      parent: inParentBloc ? inParentBloc : bloc.parent,

      listeFils: [] as any,
      listePaliers: [] as any,

      toJSON
    } as BlocOffre;


    if (bloc.listePaliers) {
      for (const palier of bloc.listePaliers) {
        newBloc.listePaliers.push(this.dupliquerPalier(palier));
      }
    }



    if (bloc.listeFils) {
      for (const blocFils of bloc.listeFils) {
        newBloc.listeFils.push(this.dupliquerBlocOffre(blocFils, newBloc, false, count + 1));
      }
    }



    if (firstCall) {
      if (newBloc.parent) {
        newBloc.parent.listeFils.push(newBloc);
      } else {
        newBloc.offre.listeBlocs.push(newBloc);
      }
    }

    return newBloc;
  }




  private dupliquerPalier(palier: DetailValeurPalier): DetailValeurPalier {
    const newPalier: DetailValeurPalier = {
      valeurMin: palier.valeurMin,
      valeurMax: palier.valeurMax,
      qteMin: palier.qteMin,
      qteMax: palier.qteMax,
      nbrObjFilsMin: palier.nbrObjFilsMin,
      nbrObjFilsMax: palier.nbrObjFilsMax,
      nombreProduitsMin: palier.nombreProduitsMin,
      nombreProduitsMax: palier.nombreProduitsMax,
      tauxRf: palier.tauxRf,
      tauxUg: palier.tauxUg,
      ratioUg: palier.ratioUg,
      reste: palier.reste,
      multiple: palier.multiple,

      delaiPaiement: palier.delaiPaiement,
      tauxEscompte: palier.tauxEscompte,

      listCadeaux: []
    };

    for (const cadeau of palier.listCadeaux) {
      newPalier.listCadeaux.push(this.dupliquerCadeau(cadeau))
    }

    return newPalier;
  }



  private dupliquerCadeau(cadeau: PalierValeurCadeau): PalierValeurCadeau {
    const newCadeau: PalierValeurCadeau = {
      qteCadeau: cadeau.qteCadeau,
      produit: cadeau.produit,
      libelleCadeau: cadeau.libelleCadeau,
      valeurUnitCadeau: cadeau.valeurUnitCadeau,
      ppvProduit: cadeau.ppvProduit,
    }

    return newCadeau;
  }

  checkIfPackHasProduits(offre: Offre) {
    let blocHasProduitMap = {};
    if (offre) {
      for (const bloc of offre.listeBlocs) {
        this.checkIfBlocHasProduits(blocHasProduitMap, bloc);
      }
    }

    const mapKeys = Object.keys(blocHasProduitMap);
    let blocHasNoProduitAccum = [];

    if (mapKeys?.length) {
      for (const key of mapKeys) {
        if (blocHasProduitMap[key] === false) {
          blocHasNoProduitAccum.push(key);
        }
      }
    }

    return blocHasNoProduitAccum;
  }

  checkIfBlocHasProduits(accum: any, blocOffre: BlocOffre) {
    if (!blocOffre) {
      return;
    }

    if (!blocOffre?.listeFils?.length) {
      accum[`${blocOffre?.hash}|${blocOffre?.titre}`] = false;
      return;
    }

    for (const blocFils of blocOffre?.listeFils) {
      if (blocFils?.typeBloc === 'F') {
        const packParent = blocFils.parent;
        accum[`${packParent?.hash}|${packParent?.titre}`] = true;
      } else {
        if (!blocFils?.listeFils?.length) {
          accum[`${blocFils?.hash}|${blocFils?.titre}`] = false;
        }
        this.checkIfBlocHasProduits(accum, blocFils);
      }

    }
  }

  setConditionsAndPaliersRowDisplayState(offre: Offre, state: boolean) {
    if (offre && offre?.listeBlocs?.length) {
      for (const bloc of offre?.listeBlocs) {
        bloc['displayConditionsEtRemisesRow'] = state;
        this.setConditionsAndPaliersRowDisplayStateOnSousBlocs(bloc, state);
      }
    }
  }

  private setConditionsAndPaliersRowDisplayStateOnSousBlocs(blocOffre: BlocOffre, state: boolean) {
    if (!blocOffre) {
      return;
    }

    for (const blocFils of blocOffre?.listeFils) {
      if (blocFils?.typeBloc === 'G' || blocFils?.typeBloc === 'R') {
        blocFils['displayConditionsEtRemisesRow'] = state;
        this.setConditionsAndPaliersRowDisplayStateOnSousBlocs(blocFils, state);
      }
    }
  }


  accelerateurDeSaisie(qteCmd: number, blocParent: BlocOffre, type?: string): void {
    if (!blocParent?.listeFils?.length) {
      return;
    }

    for (const blocFils of blocParent?.listeFils) {
      blocFils.qteCmd = qteCmd;

      type === 'groupe' ?
        this.refreshEtatBlocOffre(blocFils) :
        this.refreshEtatBlocOffreSurCmdUnit(blocFils);
    }

    type === 'groupe' && (this.palierOffreTraitement(this.getOffreRacine(blocParent)));

  }




  saveOffre(offre: Offre) {
    // offres.push(offre);
  }


  getListeFournisseur() {
    return this.httpClient.get<Fournisseur[]>(this.baseUrl + '/api/v1/entreprise/societes-grossite', { observe: 'body' });
  }

  getListeFournisseurLabo() {
    return this.httpClient.get<Fournisseur[]>(this.baseUrl + '/api/v1/entreprise/societes-fabriquant', { observe: 'body' });
  }

  getListeOffreurs() {
    return this.httpClient.get<Fournisseur[]>(this.baseUrl + '/api/v1/entreprise/fournisseurs', { observe: 'body' });
  }

  getListeFournisseurGrossiste() {
    return this.httpClient.get<Fournisseur[]>(this.baseUrl + '/api/v1/entreprise/societes-grossite', { observe: 'body' });
  }
  getListeFormesProduit() {
    return this.httpClient.get<FormeProduit[]>(this.baseUrl + '/api/offres/produits/formes/list', { observe: 'body' });
  }
  getListeGammesProduit() {
    return this.httpClient.get<GammeProduit[]>(this.baseUrl + '/api/offres/produits/gammes/list', { observe: 'body' });
  }
  getListeOffres() {
    return this.httpClient.get<Offre[]>(this.baseUrl + '/api/offres/list', { observe: 'body' });
  }
  getOffreById(id: number, incr = false) {
    return this.httpClient.get<Offre>(this.baseUrl + '/api/offres/' + id, { observe: 'body', params: { incr } });
  }
  publierOffreById(id: number) {
    return this.httpClient.get<Offre>(this.baseUrl + '/api/offres/publier/' + id, { observe: 'body' });
  }
  annulerrOffreById(id: number) {
    return this.httpClient.delete<Offre>(this.baseUrl + '/api/offres/annuler/' + id, { observe: 'body' });
  }

  createOffre(newOffre: any) {
    return this.httpClient.post<Offre>(this.baseUrl + '/api/offres/edit', newOffre, { observe: 'body' });
  }

  createOffreJson(payload: Offre): Observable<Offre> {
    return this.httpClient.post<Offre>(`${this.baseUrl}/api/offres/editjson`, payload, { observe: 'body' });
  }

  patchOffre(payload: Offre): Observable<Offre> {
    return this.httpClient.post<Offre>(`${this.baseUrl}/api/offres/patch-offre`, payload, { observe: 'body' });
  }


  createCommande(newOffre: Offre) {
    const newCommande = JSON.parse(JSON.stringify(newOffre));
    newCommande.priseCommande = this.device === 'MOBILE' ? 'MO' : 'WO';
    return this.httpClient.post<Commande>(this.baseUrl + '/api/commandes/edit', newCommande, { observe: 'body' });
  }

  envoyerCommandeIndividuelle(commandeId: number, emailFournisseur: string): Observable<any> {
    return this.httpClient.get<any>(`${this.baseUrl}/api/commandes/envoyer/${commandeId}`, { observe: 'body', params: { emailFournisseur } })
  }

  imprimerCommandeIndividuelle(cmdId: number): Observable<any> {
    return this.httpClient.get<any>(`${this.baseUrl}/api/commandes/print-cmd-indiv`, { params: { cmdId }, observe: 'body', responseType: 'blob' as 'json' });
  }

  imprimerCommandeUnitaire(cmdId: number): Observable<any> {
    return this.httpClient.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/print-cmd-unitaire`, { params: { cmdUnitaireId: cmdId }, observe: 'body', responseType: 'blob' as 'json' });
  }

  clotureOffre(offreId: number) {
    return this.httpClient.get<Offre>(this.baseUrl + `/api/offres/cloture/${offreId}`, { observe: 'body' });
  }

  searchProduits(criteria: ProduitCriteria) {
    return this.httpClient.post<Produit[]>(this.baseUrl + '/api/v1/management-produit/produits/search', criteria, { observe: 'body' });
  }

  searchProduitsPageable(criteria: ProduitCriteria, pagination: Pagination) {
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize),
    }

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return this.httpClient.post<SearchProduit>(this.baseUrl + '/api/v1/management-produit/search-produit', criteria, { params, observe: 'body' });
  }

  searchMessages(criteria: MessageCriteria) {
    return this.httpClient.post<MessageAcceuil[]>(this.baseUrl + '/api/messages/search', criteria, { observe: 'body' });
  }
  searchOffres(pagination: Pagination, criteria: OffreCriteria) {
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
    }

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }
    return this.httpClient.post<any>(this.baseUrl + '/api/offres/search', criteria, { observe: 'body', params });
  }

  searchGrossistes(criteria: OffreCriteria) {
    // TODO: Add paginated request and Post
    return this.httpClient.get<any>(this.baseUrl + '/api/offres/fournisseurs/list', { observe: 'body' })
  }
  searchOffres2(startIndex: number, pageSize: number, criteria: OffreCriteria) {
    return this.httpClient.post<Offre[]>(this.baseUrl + '/api/offres/search2?page=' + startIndex + '&size=' + pageSize, criteria, { observe: 'body' });
  }
  getListeArticle() {
    return this.httpClient.get<ArticleAcceuil[]>(this.baseUrl + '/api/messages/annonces/list', { observe: 'body' });
  }
  getListeMessage() {
    return this.httpClient.get<MessageAcceuil[]>(this.baseUrl + '/api/messages/list', { observe: 'body' });
  }
  searchCommandes(criteria: CommandeCriteria, pagination: Pagination = null) {
    const params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
      sort: pagination.sortField ? pagination.sortField + ',' + pagination.sortMethod : null,
    }

    if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']) && !criteria?.statut) {
      criteria['statut'] = ['A', 'R', 'N', 'S', 'B', 'AC', 'E'];
    }

    return this.httpClient.post<any>(this.baseUrl + `/api/commandes/search`, criteria, { observe: 'body', params });
  }

  // ? Search Commande Dto (with details)
  searchCommandesDto(criteria: CommandeCriteria, pagination: Pagination = null) {
    const params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
      sort: pagination.sortField ? pagination.sortField + ',' + pagination.sortMethod : null,
    }

    if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']) && !criteria?.statut) {
      criteria['statut'] = ['A', 'R', 'N', 'S', 'B'];
    }

    return this.httpClient.post<any>(this.baseUrl + `/api/commandes/search-dto`, criteria, { observe: 'body', params });
  }

  getCommandeDtoById(id: number) {
    return this.httpClient.get<any>(this.baseUrl + `/api/commandes/${id}/dto`, { observe: 'body', responseType: 'json' });
  }

  getListeCommandes(pagination: Pagination) {
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
    }

    return this.httpClient.get<any>(this.baseUrl + '/api/commandes/mycmd', {
      observe: "body",
      params: params
    });
  }

  getPageNumber(skip: number, pageSize: number) {
    return (skip && pageSize) ? Math.floor(skip / pageSize) : 0;
  }

  getCommandesById(id: number) {
    return this.httpClient.get<Offre>(this.baseUrl + '/api/commandes/' + id, { observe: 'body' });
  }

  valdierCommandeById(id: number) {
    return this.httpClient.get<Offre>(this.baseUrl + '/api/commandes/nouvelle/' + id, { observe: 'body' });
  }

  accepterCommande(id: number, codeClientLocalCmd = null, forceAccept = false): Observable<any[]> {
    let params = {};
    params['forceAccept'] = forceAccept;
    codeClientLocalCmd && (params['codeClientLocalCmd'] = codeClientLocalCmd);

    return this.httpClient.get<any[]>(this.baseUrl + `/api/commandes/accepter/${id}`, { observe: 'body', params });
  }

  annulerCommandeById(id: number) {
    return this.httpClient.delete<Offre>(this.baseUrl + '/api/commandes/annuler/' + id, { observe: 'body' });
  }

  supprimerCommandeById(id: number) {
    return this.httpClient.delete<Offre>(this.baseUrl + '/api/commandes/supprimer/' + id, { observe: 'body' });
  }

  refuserCommande(id: number, motifRefus: string) {
    const params = { motifRefus: motifRefus.trim() };

    return this.httpClient.get<Offre>(this.baseUrl + `/api/commandes/refuse/${id}`, { observe: 'body', params });
  }

  getDashboard() {
    return this.httpClient.get<any>(this.baseUrl + '/api/offres/dashboard', { observe: 'body' });
  }

  printCommandeById(id: number) {
    return this.httpClient.get<any>(this.baseUrl + '/api/commandes/' + id + '/print', { observe: 'body', responseType: 'blob' as 'json' });
  }

  printCommandes(ListIdsCommandes: number[]) {
    return this.httpClient.post<any>(this.baseUrl + '/api/commandes/print', ListIdsCommandes, { observe: 'body', responseType: 'blob' as 'json' });
  }

  getStats(searchObject: StatsSearch): Observable<StatsData> {
    return this.httpClient.post<StatsData>(this.baseUrl + '/api/v1/statistique', searchObject);
  }

  searchSociete(criteria: any, pagination?: Pagination): Observable<SearchEntrepriseDto> {
    const params = {
      page: pagination ? String(this.getPageNumber(pagination?.skip, pagination?.pageSize)) : 0,
      size: pagination ? String(pagination?.pageSize) : 20
    };

    if (pagination?.sortField) {
      params['sort'] = pagination?.sortField + ',' + pagination?.sortMethod;
    }

    return this.httpClient.post<SearchEntrepriseDto>(this.baseUrl + '/api/v1/entreprise/search', criteria, { observe: 'body', params });
  }

  searchListeEntrepriseByCodeEntreprise(listeCodeEntreprise: string[]): Observable<EntrepriseDTO[]> {
    return this.httpClient.post<EntrepriseDTO[]>(`${this.baseUrl}/api/v1/entreprise/search-listentreprises`, listeCodeEntreprise, { observe: 'body' })
      .pipe(catchError(error => {
        console.error('HTTP request failed: ', error);
        return of([]);
      }));
  }

  getSocieteById(id: number): Observable<EntrepriseDTO> {
    return this.httpClient.get<EntrepriseDTO>(`${this.baseUrl}/api/v1/entreprise/${id}`, { observe: 'body' });
  }

  saveSociete(payload: EntrepriseDTO): Observable<EntrepriseDTO> {
    return this.httpClient.post<EntrepriseDTO>(`${this.baseUrl}/api/v1/entreprise/save-fournisseur`, payload, { observe: 'body' });
  }

  getClientGroupeByClientsSiteDto(payload: ClientSiteDto[]): Observable<ClientSiteDto[]> {
    return this.httpClient.post<ClientSiteDto[]>(`${this.baseUrl}/api/v1/entreprise/get-clients-groupe`, payload);
  }

  createAccesClientByClientSite(payload: ClientSiteDto[]): Observable<ClientSiteDto[]> {
    return this.httpClient.post<ClientSiteDto[]>(`${this.baseUrl}/api/v1/entreprise/create-acces-clients`, payload);
  }

  getListeCategories(): Observable<DomainEnumeration[]> {
    if (this.produitCategories$?.value) {
      return of(this.produitCategories$.value);
    }

    return this.httpClient.get<DomainEnumeration[]>(this.baseUrl + '/api/v1/management-produit/categories', { observe: 'body' })
      .pipe(tap(res => {
        this.produitCategories$.next(res);
      }));
  }

  getDomaineEnumeration(domaine: string): Observable<DomainEnumeration[]> {
    return this.httpClient.get<DomainEnumeration[]>(`${this.baseUrl}/api/domainenumeration/list`, { params: { domaine }, observe: 'body' });
  }

  getListeProduitByCodeProduitCatalogue(listeCodeProduitCatalogue: string[]): Observable<Produit[]> {
    return this.httpClient.post<Produit[]>(`${this.baseUrl}/api/v1/management-produit/search-listproduit`, listeCodeProduitCatalogue, { observe: 'body' });
  }

  exportOffreAsJson(targetOffre: Offre): void {
    const offre = cloneDeep(targetOffre);
    this.clearOffreIdAndBlocImageValues(offre);

    if (offre) {
      const blob = new Blob([JSON.stringify({ ...offre, offreExportSrc: this.plateformeService.getCurrentPlateforme() })], { type: 'application/json' });

      // Create a link element & trigger download
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `${this.plateformeService.getPlateformePrefix().toUpperCase()}-EXPORT-${moment().unix()}.json`);
      link.style.visibility = 'hidden';

      document.body.appendChild(link);

      link.click();

      // Cleanup
      document.body.removeChild(link);
    } else {
      console.error('Invalid object parameter.');
    }
  }

  async downloadDocMetaDataItem(item: DocMetaDataDto) {
    if (item) {
      const response = await fetch(this.uploadService.fetchUploadedDocument(item.idhash));
      const blob = await response.blob();

      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', item.name);
      link.style.visibility = 'hidden';

      document.body.appendChild(link);

      link.click();

      // Cleanup
      document.body.removeChild(link);
    }
  }

  async printDocMetaDataItem(item: DocMetaDataDto) {
    if (item) {
      console.log(`Starting print for: ${item.name} (ID: ${item.idhash})`);

      try {
        const response = await fetch(this.uploadService.fetchUploadedDocument(item.idhash));
        console.log(`Response status: ${response.status}`);

        if (!response.ok) {
          return;
        }

        const blob = await response.blob();
        console.log(`Blob size: ${blob.size} bytes`);

        const url = URL.createObjectURL(blob);
        console.log(`Generated object URL: ${url}`);

        // Open the document in a new tab and trigger print
        const printWindow = window.open(url, '_blank');
        if (printWindow) {
          printWindow.onload = () => {
            printWindow.print();
            console.log(`Print dialog opened for: ${item.name}`);
          };
        } else {
          console.error("Failed to open print window.");
        }
      } catch (error) {
        console.error(`Error printing document: ${error}`);
      }
    } else {
      console.warn("No item provided for printing.");
    }
  }


  private getListeBlocProduitFromOffre(offre: Offre) {
    const accumulator: BlocOffre[] = [];

    for (const blocOffre of offre?.listeBlocs) {
      this.getListeProduitsFromBlocOffre(blocOffre, accumulator);
    }

    return accumulator;
  }

  private getListeProduitsFromBlocOffre(blocOffre: BlocOffre, accumulator: BlocOffre[]) {
    if (!blocOffre) return;

    for (const blocFils of blocOffre?.listeFils) {
      if (this.isBlocTypeProduit(blocFils)) {
        accumulator.push(blocFils);
      } else {
        this.getListeProduitsFromBlocOffre(blocFils, accumulator);
      }
    }
  }

  private removeBlocProduitWithIdFromOffre(offre: Offre, targetBlocProduitId: number) {
    for (const blocOffre of offre?.listeBlocs) {
      this.removeBlocProduitWithId(blocOffre, targetBlocProduitId);
    }
  }

  private removeBlocProduitWithId(blocOffre: BlocOffre, targetBlocProduitId: number) {
    if (!blocOffre) return;

    blocOffre?.listeFils?.forEach((blocFils, i, array) => {
      if (this.isBlocTypeProduit(blocFils) && blocFils?.id === targetBlocProduitId) {
        array?.splice(i, 1);
      } else {
        this.removeBlocProduitWithId(blocFils, targetBlocProduitId);
      }
    });
  }


  private getUniqueEntrepriseDto(offre: Offre) {
    const entrepriseMap = new Map<number, Fournisseur>();

    [offre?.offreur, offre?.laboratoire, ...offre?.distributeurs]?.map(fourn => {
      entrepriseMap.set(fourn?.id, fourn);
    });

    return Array.from(entrepriseMap.values());
  }

  async handleCrossPlateformeObjectIdMapping(offre: Offre): Promise<[Offre, number[]]> {
    // ? Clear out of reach offre-image, bloc-image asset links
    this.clearOffreIdAndBlocImageValues(offre);

    // ? Handle object id mapping for delai-paiements
    if (offre?.listDelaiPaiements?.length) {
      await this.mapModePaiement(offre);
    }
    // ? Handle object id mapping for entreprise-dto objects
    await this.mapEntrepriseDto(offre);
    // ? Handle product id mapping for bloc-produit objects
    const produitsNull = await this.mapBlocProduit(offre);

    return [offre, produitsNull];
  }

  private async mapModePaiement(offre: Offre): Promise<void> {
    const res = await firstValueFrom(this.getDomaineEnumeration('mode_paiement'));
    res?.forEach(domain => {
      const targetDomain = offre.listDelaiPaiements?.find(item => item?.code === domain?.code);
      if (targetDomain) {
        Object.assign(targetDomain, domain);
      }
    });
  }

  private async mapEntrepriseDto(offre: Offre): Promise<void> {
    const uniqueEntrepriseDtos = this.getUniqueEntrepriseDto(offre);
    const listeCodeEntreprise = uniqueEntrepriseDtos?.map(entr => entr?.code);
    const entrepriseIndicesToRemove = [];

    if (listeCodeEntreprise?.length) {
      const resArr = await firstValueFrom(this.searchListeEntrepriseByCodeEntreprise(listeCodeEntreprise));


      [offre?.offreur, offre?.laboratoire, ...(offre?.distributeurs || [])].forEach((fourn, i, array) => {
        const targetFourn = resArr?.find(resFourn => resFourn?.code === fourn?.code);

        if (targetFourn) {
          Object.assign(fourn, targetFourn);
        } else {
          entrepriseIndicesToRemove.push(i);
        }
      });

      for (const index of entrepriseIndicesToRemove.sort((a, b) => b - a)) {
        if (index > 1) {
          offre?.distributeurs?.splice(index - 2, 1);
        } else if (index === 0) {
          offre.offreur = null;
        } else if (index === 1) {
          offre.laboratoire = null;
        }
      }

    }
  }

  private async mapBlocProduit(offre: Offre): Promise<number[]> {
    const listeBlocProduits = this.getListeBlocProduitFromOffre(offre);
    const listeCodeProduitCatalogue = listeBlocProduits?.map(produit => produit?.codeProduitCatalogue);

    const produitsNull: number[] = [];

    if (listeCodeProduitCatalogue?.length) {
      const resArr = await firstValueFrom(this.getListeProduitByCodeProduitCatalogue(listeCodeProduitCatalogue));

      listeBlocProduits.forEach(blocProduit => {
        const targetBlocProduit = resArr?.find(produit => produit?.codeProduitCatalogue === blocProduit?.codeProduitCatalogue);

        if (targetBlocProduit) {
          Object.assign(blocProduit, targetBlocProduit)
        } else {
          produitsNull.push(blocProduit?.id), this.removeBlocProduitWithIdFromOffre(offre, blocProduit?.id);
        }
      });
    }

    return produitsNull;
  }

  private clearOffreIdAndBlocImageValues(offre: Offre) {
    offre.id = offre.docImageOffre = null;

    for (const blocOffre of offre?.listeBlocs) {
      blocOffre.docImageBlocOffre = null;
      this.clearBlocImageValues(blocOffre);
    }
  }

  private clearBlocImageValues(blocOffre: BlocOffre) {
    if (!blocOffre) return;

    for (const blocFils of blocOffre?.listeFils) {
      blocFils.docImageBlocOffre = null;
      this.clearBlocImageValues(blocFils);
    }
  }

  getAssociationDataFromResponse(associationsResponse: OutputTAP[]): BaseEtrangereToBaseWinplusGrouping[] {
    // Use a dictionary (object) to group items by their `id_base_etrangere`
    const groupedObj = associationsResponse.reduce((acc, item) => {
      const groupId = item.id_base_etrangere;

      // If we haven't seen this id_base_etrangere yet, initialize it
      if (!acc[groupId]) {
        acc[groupId] = {
          id: this.generateHashFromProduitDesignation(item.nom_base_etrangere),
          id_base_etrangere: item.id_base_etrangere,
          codePrdExtern: item.codePrdExtern,
          nom_base_etrangere: item.nom_base_etrangere,
          prix_vente_base_etrangere: item.prix_vente_base_etrangere?.toString(),
          categorie_base_etrangere: item.categorie_base_etrangere,
          associations: [],
          currentAssociation: item
        };
      }

      // Push the "WinPlus" association details into the `associations` array
      acc[groupId].associations.push({
        code_prd_winplus: item.code_prd_winplus,
        nom_base_winplus: item.nom_base_winplus,
        code_groupe: item.code_groupe,
        prix_vente_base_winplus: item.prix_vente_base_winplus,
        categorie_base_winplus: item.categorie_base_winplus,
        code_barre_win: item.code_barre_win,
        type_association: item.type_association
      });

      return acc;
    }, {} as Record<string, BaseEtrangereToBaseWinplusGrouping>);

    return Object.values(groupedObj);
  }

  generateHashFromProduitDesignation(input: string): string {
    let hash = 0;

    for (let i = 0; i < input.length; i++) {
      hash = Math.imul(31, hash) + input.charCodeAt(i);
      hash |= 0; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36);
  }

  // ? STATISTIQUES DE CONSOMMATION WINPLUS + STOCK WINPLUS LOGIC START
  getListeCodeProduitForOffre(offre: Offre) {
    const accumulator = new Set<string>();

    for (const blocOffre of offre?.listeBlocs) {
      this.getListeCodeProduitForCurrentBloc(blocOffre, accumulator);
    }

    return Array.from(accumulator);
  }

  private getListeCodeProduitForCurrentBloc(currentBloc: BlocOffre, accumulator: Set<string>) {
    if (!currentBloc) return;

    for (const blocFils of currentBloc?.listeFils) {
      if (this.isBlocTypeProduit(blocFils)) {
        accumulator.add(blocFils?.codeProduitCatalogue);
      } else {
        this.getListeCodeProduitForCurrentBloc(blocFils, accumulator)
      }
    }
  }

  assignStockProduitWinplusToProduitOffre(offre: Offre, listeIndicateurStockWinplus: IndicateurStock[]) {
    const codeProduitToStockMap = this.generateCodeProduitToStockMap(listeIndicateurStockWinplus);

    if (Object.keys(codeProduitToStockMap)?.length) {
      for (const blocOffre of offre?.listeBlocs) {
        this.assignProduitStockToTarget(blocOffre, codeProduitToStockMap);
      }
    }

  }

  private assignProduitStockToTarget(currentBloc: BlocOffre, codeProduitToStockMap: { [codeProduit: string]: number }) {
    if (!currentBloc) return;

    for (const blocFils of currentBloc?.listeFils) {
      if (this.isBlocTypeProduit(blocFils)) {
        blocFils.stockProduitWinplus = codeProduitToStockMap[blocFils?.codeProduitCatalogue] ?? 0;
      } else {
        this.assignProduitStockToTarget(blocFils, codeProduitToStockMap);
      }
    }
  }

  private generateCodeProduitToStockMap(listeIndicateurStock: IndicateurStock[]) {
    if (!listeIndicateurStock?.length) return {};

    const codeProduitStockMap = {};

    for (const indicateur of listeIndicateurStock) {
      codeProduitStockMap[indicateur?.codePrdWinplus] = indicateur?.stock ?? 0;
    }

    return codeProduitStockMap;
  }
  // ? STATISTIQUES DE CONSOMMATION WINPLUS + STOCK WINPLUS LOGIC END

  getListeDesignationProduitsFromImages(images: FormData): Observable<{ list_designation: string[] }> {
    return this.httpClient.post<{ list_designation: string[] }>(`https://winlens.sophatel.com/ocr_offre`, images, { responseType: 'json' });
  }

  getTAPAssociationPayload(ocrResult: string[]): InputTAP[] {
    return ocrResult.map(designation => ({
      designation,
      ID: this.generateHashFromProduitDesignation(designation)
    }));
  }

  associerTAP(tapPayload: InputTAP[]): Observable<TAPResponse> {
    return this.httpClient.post<TAPResponse>(`${this.baseUrl}/api/v1/management-produit/associer`, tapPayload, { observe: 'body' });
  }

  saveListeProduitsFromSelectedAssociations(associations: ProduitListDto): Observable<ProduitListDto> {
    return this.httpClient.post<ProduitListDto>(`${this.baseUrl}/api/v1/management-produit/save-produitlist`, associations, { observe: 'body' });
  }

  getListeProduitsByNomListe(nom: string): Observable<ProduitListDto[]> {
    return this.httpClient.post<ProduitListDto[]>(`${this.baseUrl}/api/v1/management-produit/get-produitlist`, {}, { params: { nomProduitList: nom }, observe: 'body' });
  }

  getAllListeProduits(pagination: Pagination): Observable<SearchSelectedProduitsAssociations> {
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize),
    }

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return this.httpClient.post<SearchSelectedProduitsAssociations>(`${this.baseUrl}/api/v1/management-produit/get-all-produitlist`, {}, { params, observe: 'body' });
  }

  fetchIndicateurStockWinPlus(listCodeProduitCatalogue: string[], codeSite?: number): Observable<IndicateurStock[]> {
    codeSite = codeSite ?? 170;
    return this.httpClient.post<IndicateurStock[]>(`${this.baseUrl}/api/v1/winstat/${codeSite}/fetch-indicateur-stock-app`, listCodeProduitCatalogue, { observe: 'body' });
  }

  checkTranscoOffre(offreId: number): Observable<CheckTranscoResponse> {
    return this.httpClient.get<CheckTranscoResponse>(`${this.baseUrl}/api/offres/check-transco/${offreId}`);
  }

  checkTranscoProduits(offre: Offre): Observable<CheckTranscoPrdResponseDTO[]> {
    return this.httpClient.post<CheckTranscoPrdResponseDTO[]>(`${this.baseUrl}/api/offres/check-transco-prd`, offre);
  }

  checkTranscoPrix(payload: CheckPrixRequestDTO): Observable<Array<CheckTranscoPrixResponse>> {
    return this.httpClient.post<Array<CheckTranscoPrixResponse>>(`${this.baseUrl}/api/offres/check-prix`, payload, { responseType: 'json' });
  }

  changeBlocOrder(payload: BlocOffre[]): Observable<Offre> {
    return this.httpClient.post<Offre>(`${this.baseUrl}/api/offres/change-bloc-order`, payload);
  }

  checkProductInOffre(codeSite: number, listCodeProduit: string[]): Observable<CheckProduitInOffreResponseDto[]> {
    return this.httpClient.post<CheckProduitInOffreResponseDto[]>(`${this.baseUrl}/api/offres/check-prd-frn-in-offre`, { codeSite, listCodeProduit });
  }

  // ? ALERTE DE QUANTITE NECESSAIRE POUR AVOIR UNE REMISE : START
  public showQteCmdAlertForNextValidRemise(blocOffre: BlocOffre) {
    const nextValidRemise = this.findNextPalierAndCompare(blocOffre);

    if (!nextValidRemise) return;

    const [palier, bloc] = [nextValidRemise.palier, nextValidRemise.bloc];

    let message: string;

    const typeRemiseFormat = this.formatTypeRemise(palier);

    if (palier?.qteMin) {
      message = this.extractAlertMessageForPalierSurQte(palier, bloc, message, typeRemiseFormat);
    } else if (palier?.valeurMin) {
      message = this.extractAlertMessageForPalierSurValeur(palier, bloc, message, typeRemiseFormat);
    }

    this.toastr.info(message, '', { enableHtml: true });
  }

  public findMessageEtatOfNearestInvalidBlocOffre(blocOffre: BlocOffre): [string, BlocOffre | null] {
    let currentBloc = blocOffre;

    if (!currentBloc || currentBloc?.etat === 'I') {
      return [currentBloc?.messageEtat || '', currentBloc];
    }

    while (currentBloc) {
      if (currentBloc?.etat === 'I') {
        return [currentBloc?.messageEtat || '', currentBloc];
      }
      currentBloc = currentBloc.parent;
    }

    return ['', null];
  }

  private getNextValidPalier(currentBloc: BlocOffre, seuil: number): DetailValeurPalier {
    return currentBloc.listePaliers.find(palier => {

      if (palier.qteMin) {
        return currentBloc.totalQteCmd < palier.qteMin && !palier.selected &&
          (currentBloc.totalQteCmd + Math.ceil(palier.qteMin * (1 - seuil))) >= palier.qteMin;
      }

      if (palier.valeurMin) {
        const valeurDeTest = palier.isValeurNet ? currentBloc.totalValeurNetteCmd : currentBloc.totalValeurBruteCmd;

        return valeurDeTest < palier.valeurMin && !palier.selected &&
          (valeurDeTest + Math.ceil(palier.valeurMin * (1 - seuil))) >= palier.valeurMin;
      }

      return false;
    });
  }

  private findNextPalierAndCompare(blocOffre: BlocOffre, seuil = 0.7): { palier: DetailValeurPalier, bloc: BlocOffre } {
    let currentBloc = blocOffre;

    while (currentBloc) {
      if (currentBloc?.listePaliers && currentBloc?.listePaliers?.length) {
        const validPalier = this.getNextValidPalier(currentBloc, seuil);

        if (validPalier) {
          return { palier: validPalier, bloc: currentBloc };
        }
      }
      currentBloc = currentBloc?.parent;
    }

    return null;
  }

  private formatTypeRemise(palier: DetailValeurPalier): string {
    if (palier?.tauxRf || palier?.tauxUg) {
      return palier?.tauxRf ?
        `RF${palier?.tauxRf}%` :
        `UG${palier?.tauxUg}%`;
    } else if (palier?.ratioUg) {
      const ugResult = palier?.ratioUg.split(':')[1];
      return `${this.decimalPipe.transform(ugResult, '1.0-0')} unités gratuits`;
    }
    return '';
  }

  private extractAlertMessageForPalierSurValeur(palier: DetailValeurPalier, bloc: BlocOffre, message: string, typeRemiseFormat: string) {
    let valeurMinDiff: number;

    if (!palier?.isValeurNet) {
      valeurMinDiff = palier?.valeurMin - bloc?.totalValeurBruteCmd;
    } else {
      valeurMinDiff = palier?.valeurMin - bloc?.totalValeurNetteCmd;
    }

    const montantRestantFormat = `${this.decimalPipe.transform(valeurMinDiff, '1.2-2')} DH (${palier?.isValeurNet ? 'Net' : 'Brut'})`;

    message = `
      Il vous reste un montant de <b class="h5">${montantRestantFormat}</b>
      pour atteindre une remise de
      <span class="h5 ml-1">
        <i class="bi bi-tags-fill"></i> ${typeRemiseFormat}
      </span>.
    `;
    return message;
  }

  private extractAlertMessageForPalierSurQte(palier: DetailValeurPalier, bloc: BlocOffre, message: string, typeRemiseFormat: string) {
    const qteCmdDiff = palier?.qteMin - bloc?.totalQteCmd;

    message = `
      Il vous reste <b class="h5">${qteCmdDiff} unités</b> 
      à commander pour atteindre une remise de 
      <span class="h5 ml-1">
        <i class="bi bi-tags-fill"></i> ${typeRemiseFormat}
      </span>.
    `;
    return message;
  }
  // ? ALERTE DE QUANTITE NECESSAIRE POUR AVOIR UNE REMISE : END

  // ? INDICATEUR CONSOMMATION WINPLUS START
  fetchIndicateurConsommationWinPlus(listCodeProduitCatalogue: string[], codeSite?: number) {
    codeSite = codeSite ?? 170;
    return this.httpClient.post<IndicateurConsommation[]>(`${this.baseUrl}/api/v1/winstat/${codeSite}/fetch-indicateur-consommation-app`, listCodeProduitCatalogue, { observe: 'body' });
  }

  getFormattedIndicateurConsommationData(indicateurConsommationWinplus: IndicateurConsommation) {
    if (indicateurConsommationWinplus) {
      return Object.keys(indicateurConsommationWinplus)
        .filter(item => item !== 'codePrdWinplus')
        .sort((a, b) => {
          const numA = parseInt(a?.replace('consommationMois', ''));
          const numB = parseInt(b?.replace('consommationMois', ''));
          return numA - numB;
        })
        .map(key => {
          const monthValueFromKey = +key?.replace('consommationMois', '');

          return {
            consommation: indicateurConsommationWinplus[key],
            libellePeriode: this.getMonthYearPair(monthValueFromKey),
          };
        })
        .slice(0, 12)
        .sort((a, b) => a?.libellePeriode?.localeCompare(b?.libellePeriode));
    }

    return null;
  }

  private getMonthYearPair(offset: number): string {
    const currentDate = moment();

    const targetDate = currentDate.subtract(offset, 'months');
    const result = targetDate.format('MM/YYYY');

    return result;
  }
  // ? INDICATEUR CONSOMMATION WINPLUS END


  // ? COLLAPSE SOUS-BLOC LOGIC START
  setCollapseStateOnOffre(offre: Offre, collapseState: boolean) {
    if (offre && offre?.listeBlocs?.length) {
      for (const blocOffre of offre?.listeBlocs) {
        blocOffre.displayDetailsSousBloc = collapseState;
        this.forceCollapseOrExpandAllOnBlocOffre(blocOffre, collapseState);
      }
    }
  }

  forceCollapseOrExpandAllOnBlocOffre(blocOffre: BlocOffre, collapse: boolean) {
    if (!blocOffre) return;

    for (const blocFils of blocOffre?.listeFils) {
      if (!this.isBlocTypeProduit(blocFils)) {
        blocFils.displayDetailsSousBloc = collapse;
      }

      this.forceCollapseOrExpandAllOnBlocOffre(blocFils, collapse);
    }
  }
  // ? COLLAPSE SOUS-BLOC LOGIC END

  private extractMessageEtatFromFirstPalier(palier: DetailValeurPalier): string {
    if (palier && !palier?.ratioUg && (palier?.qteMin || palier?.qteMax)) {
      if (palier?.qteMin) return `Aucune remise applicable. Essayez avec "${palier?.qteMin} unités" ou plus.`;
      if (palier?.qteMax) return `Aucune remise applicable. Essayez avec "${palier?.qteMax} unités" ou moins.`;
    } else if (palier && !palier?.ratioUg && (palier?.valeurMin || palier?.valeurMax)) {
      if (palier?.valeurMin) return `Remise non applicable. Montant minimum requis : ${this.decimalPipe.transform(palier?.valeurMin, '1.2-2', 'fr-FR')} DH.`;
      if (palier?.valeurMax) return `Remise non applicable. Montant maximum autorisé : ${this.decimalPipe.transform(palier?.valeurMax, '1.2-2', 'fr-FR')} DH.`;
    } else if (palier && palier?.delaiPaiement) {
      return "Remise non applicable. Veuillez sélectionner un mode de paiement pour continuer.";
    }

    return 'Aucune remise applicable. Ajustez les quantités selon les remises disponibles.';
  }

}
