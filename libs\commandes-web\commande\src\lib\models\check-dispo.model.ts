export class CheckDispoItem {
    codeProduit?: string;
    quantiteMin?: number;
    typeCode?: number;

    constructor(partialCheckDispoItem: Partial<CheckDispoItem>) {
        this.codeProduit = partialCheckDispoItem?.codeProduit || null;
        this.quantiteMin = partialCheckDispoItem?.quantiteMin || null;
        this.typeCode = partialCheckDispoItem?.typeCode || 2;
    }
}

export class CheckDispoResponseItem extends CheckDispoItem {
    dispo: string;
    dispoLibelle: string;
}

export class CheckDispoResponse {
    codeRetour:number;
    grossite: number;
    listeReponses: CheckDispoResponseItem[];
}