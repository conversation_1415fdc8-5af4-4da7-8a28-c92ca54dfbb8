import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ExecutionBatchComponent } from './execution-batch/execution-batch.component';
import { ConfigAdminComponent } from './config-admin/config-admin.component';

const routes: Routes = [

  {
    path: 'execution-batch',
    component: ExecutionBatchComponent,
  },

  {
    path: 'config-admin',
    component: ConfigAdminComponent,
  },


];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminBatchRoutingModule { }
