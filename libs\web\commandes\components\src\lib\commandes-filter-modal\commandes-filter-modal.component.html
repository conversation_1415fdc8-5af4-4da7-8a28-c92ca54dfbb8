<form [formGroup]="filterForm" (click)="closeOpenSelect2Dropdowns()" class="filter-container" (ngSubmit)="applyFilter()" wphFocusTrap>
    <div class="row py-0 flex-wrap px-1 mx-0 my-2 k-gap-2 w-100">

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="codeCommande" class="col-12 px-0 col-form-label text-left">Code Commande</label>
            <div class="col-12 px-0 input-group">
                <input type="text" class="form-control form-control-md" id="codeCommande"
                    formControlName="codeCommande">
            </div>
        </div>

        <div id="client-picker-input" class="col-12 col-lg-auto p-0 m-0"
            *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
            <label for="client" class="col-12 px-0 col-form-label text-left">Client</label>

            <div class="col-12 px-0 input-group picker-input">
                <input type="text" class="form-control form-control-md pl-4" id="client" formControlName="client"
                    [ngbTypeahead]="searchClient" [resultTemplate]="searchClientTemplate"
                    [resultFormatter]="clientFormatter" [inputFormatter]="clientFormatter" [editable]="false">

                <ng-template #searchClientTemplate let-result="result">
                    <div>
                        <span class="badge badge-info mr-2">{{result?.code}}</span>

                        <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                    </div>

                    <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                </ng-template>

                <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
            </div>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="fournisseur" class="col-12 px-0 col-form-label text-left">Distributeur</label>

            <div class="col-12 px-0 input-group picker-input">
                <input type="text" class="form-control form-control-md pl-4" id="fournisseur"
                    formControlName="fournisseur" [ngbTypeahead]="searchFournisseur"
                    [resultFormatter]="fournisseurFormatter" [inputFormatter]="fournisseurFormatter" [editable]="false">

                <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
            </div>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="laboratoire" class="col-12 px-0 col-form-label text-left">Offreur</label>

            <div class="col-12 px-0 input-group picker-input">
                <input type="text" class="form-control form-control-md pl-4" id="laboratoire"
                    [ngbTypeahead]="searchlaboratoire" [resultFormatter]="fournisseurFormatter"
                    [inputFormatter]="fournisseurFormatter" [editable]="false" formControlName="laboratoire">

                <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
            </div>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="dateDebut" class="col-12 px-0 col-form-label text-left">Date Création Début</label>

            <app-date-picker formControlName="dateDebutCommande"></app-date-picker>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="dateFin" class="col-12 px-0 col-form-label text-left">Date Création Fin</label>

            <app-date-picker formControlName="dateFinCommande"></app-date-picker>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="dateLivraisonDebut" class="col-12 px-0 col-form-label text-left">Date Acceptation Début</label>
            
            <app-date-picker formControlName="dateValidationDebut"></app-date-picker>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="dateLivraisonFin" class="col-12 px-0 col-form-label text-left">Date Acceptation Fin</label>

            <app-date-picker formControlName="dateValidationFin"></app-date-picker>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0 mt-2">
            <label class="col-12 form-label px-0 pb-1 pt-0" for="nonExpiree" style="margin-bottom: 0px;">Non
                Expirées</label>

            <div (click)="$event.stopPropagation(); closeOpenSelect2Dropdowns(nonExpireeSelect)" class="col-12 mx-0 px-0 input-group position-relative">
                <select2 id="nonExpiree" hideSelectedItems="false" formControlName="nonExpireesUniquement"
                    class="form-control-sm p-0 w-100" style="min-width: 205px; width: auto" [data]="[{label: 'Oui', value: true}, {label: 'Non', value: false}]"
                    multiple="false" #nonExpireeSelect></select2>
            </div>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0 mt-2" *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']">
            <label class="col-12 form-label px-0 pb-1 pt-0" for="selectstatut" style="margin-bottom: 0px;">Statut</label>

            <div (click)="$event.stopPropagation(); closeOpenSelect2Dropdowns(statutSelect)" class="col-12 px-0 mx-0 input-group position-relative">
                <select2 id="selectstatut" formControlName="statut" hideSelectedItems="true" (update)="statutSelect.isOpen = false" (removeOption)="statutSelect.isOpen = false"
                    class="form-control-sm p-0 w-100" multiple="true" style="min-width: 205px; width: auto" [data]="listStatuts" #statutSelect></select2>
            </div>
        </div>

        <div *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']"
            class="col-12 col-lg-auto p-0 m-0 mt-2">
            <label class="col-12 form-label px-0 pb-1 pt-0" for="selectstatut" style="margin-bottom: 0px;">Statut
            </label>

            <div (click)="$event.stopPropagation(); closeOpenSelect2Dropdowns(statutSelect)" class="col-12 px-0 mx-0 input-group position-relative">
                <select2 id="selectstatut" formControlName="statut" hideSelectedItems="true" (update)="statutSelect.isOpen = false" (removeOption)="statutSelect.isOpen = false"
                    class="form-control-sm p-0 w-100" multiple="true" style="min-width: 205px; width: auto" [data]="listStatuts" #statutSelect></select2>
            </div>
        </div>

        <div class="col-4 col-md-auto d-flex align-items-end py-0 my-lg-0 my-2">
            <button (click)="clearFilters()" type="button" class="btn btn-sm btn-outline-primary b-radius">
                <i class="bi bi-arrow-clockwise mr-1"></i> <span>Vider</span>
            </button>

            <button type="submit" class="btn btn-sm btn-primary b-radius mx-1">
                <i class="mdi mdi-filter mr-1"></i> <span>Rechercher</span>
            </button>
        </div>
    </div>
</form>