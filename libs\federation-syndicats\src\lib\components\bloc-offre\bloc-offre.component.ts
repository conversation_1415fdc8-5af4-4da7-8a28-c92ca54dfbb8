import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';

import { CellClickEvent, CellCloseEvent, GridComponent, GridDataResult, RowClassArgs } from '@progress/kendo-angular-grid';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';

import { NgbModal, NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { Observable, Subject, iif, of, takeUntil } from 'rxjs';
import {
  BlocOffre,
  CommandeType,
  DetailValeurPalier,
  FEATURE_KEY,
  FEATURE_KEY_STORAGE,
  FournisseurDropdownItem,
  Offre,
  OffresService,
  TYPE_CMD,
} from '@wph/data-access';
import { AlertService, ClientFournisseur, DocMetaDataDto, DomainEnumeration, PlateformeService, SocieteType, UploadFileServiceService } from '@wph/shared';
import { AuthService } from '@wph/core/auth';
import { ActivatedRoute, Router } from '@angular/router';
import {
  ConditionBlocOffreCommandeConsolidee,
  EnteteCommandeUnitaireMarche,
  FederationSyndicatService,
  FsCommandesService,
  FsOffreService,
  PharmacieEntreprise,
} from '@wph/federation-syndicats';
import { PharmacieEntrepriseCriteria } from '../../models/pharmacie-entreprise-criteria.model';
import { GroupeEntreprise } from '../../models/groupe-entreprise.model';
import * as _ from 'lodash';
import { SelectedTypeRemiseEnum } from 'libs/data-access/src/lib/models/selected-type-remise.enum';
import { blocHasRatioUg, blocHasTauxUg, buildDataItemConditions, buildSupporteursList, canSelectTypeRfUg, hasAnyProduitsConditions, hasConditions, hasGroupeConditions, hasManyConditions, hasOffreConditions, hasPaliers, hasRatioUg, hasTauxUg, hasTypeSelectionRfUg, UserInputService, WorkerService } from '@wph/web/shared';
import { SelectedPlateforme } from '@wph/web/layout';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'wph-bloc-offre',
  templateUrl: './bloc-offre.component.html',
  styleUrls: ['./bloc-offre.component.scss'],
  animations: [
    trigger('fade', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 }))
      ]),
      transition(':leave', [
        animate('200ms ease-out', style({ opacity: 0 }))
      ])
    ])
  ]
})
export class BlocOffreComponent implements OnInit, OnChanges, OnDestroy, AfterViewInit {
  /******* Fin Partie Liste Porduit **********/
  blobUrl: any;
  pdfViewerTitle: string;
  targetItem: DocMetaDataDto;
  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  activeIds: string[] = [];
  activesSet = false;
  startsWith: RegExp = new RegExp('^[0-9]*$');
  forceSelectDistributeur: boolean = false;
  showCodeLocalWarning: boolean = false;
  popoverContent: string;
  extraConditionsTargetBloc: BlocOffre | null = null;

  TYPE_CMD = TYPE_CMD;
  buildDataItemConditions = buildDataItemConditions;

  largeImageTargetUrl: string;

  selectedBlocElement: BlocOffre;
  resizeObserver: ResizeObserver | null = null;
  isResponsable: boolean = false;
  selectedGroupeConditionsBloc: BlocOffre;

  modePaiementValuePair: any[] = [{ label: 'Aucune', value: null }];
  transporteurValuePair: any[] = [{ label: 'Aucun', value: null }];

  sideBarState: string;
  firstCardHeight: string;
  fixedPackSyntheseHeight: number;
  dynamicImageMaxHeight: string;

  criteria: PharmacieEntrepriseCriteria;

  cmdType: CommandeType;

  activeIndex: number = 1;

  afficherSynthese: boolean = false;

  filterResult: PharmacieEntreprise[] = [];

  isSelectingDistributeur: boolean = false;
  searchDistributeur: FormControl = new FormControl();
  distributorResult: PharmacieEntreprise[] = [];
  selectedSupporter: PharmacieEntreprise = null;

  qteCmdPackCoffret: number = 0;

  coffretEnabled?: boolean;
  isPlateformeWinGroupe: boolean = false;
  rechercheProduitCmdModel: string;
  rechercheProduitMembreModel: string;

  _managedGroupe: GroupeEntreprise;
  @Input('managedGroupe') set managedGroupe(value: GroupeEntreprise) {
    this._managedGroupe = value;
  }
  get managedGroupe() {
    return this._managedGroupe;
  }

  _selectedCommande: Offre | EnteteCommandeUnitaireMarche;
  @Input('selectedCommande') set selectedCommande(
    value: Offre | EnteteCommandeUnitaireMarche
  ) {
    this._selectedCommande = value;
  }

  get selectedCommande() {
    return this._selectedCommande;
  }

  _cmdsUnitaires: Offre[];
  @Input('cmdsUnitaires') set cmdsUnitaires(value: Offre[]) {
    this._cmdsUnitaires = value;
    this.modifiedCmdsUnitaires = [];

    if (value?.length) {
      this.membresSupporteurs = this.cmdsUnitaires
        ?.map((cmd) => cmd?.client as PharmacieEntreprise)
        ?.filter((membre) => !!membre);

      if (this.cmdsUnitaires?.length) {
        for (const cmd of this.cmdsUnitaires) {
          this.offresService.initialiserEtatOffre(cmd);
        }
      }
    } else {
      this.membresSupporteurs = [];
    }
  }

  get cmdsUnitaires() {
    return this._cmdsUnitaires;
  }

  _reinitNbrCoffret: boolean;
  @Input('reinitNbrCoffret') set reinitNbrCoffret(value: boolean) {
    this._reinitNbrCoffret = value;
    if (value) {
      this.qteCmdPackCoffret = 0;
    }
  }

  get reinitNbrCoffret() {
    return this._reinitNbrCoffret;
  }

  @Input('offre')
  set _offre(offre: any) {
    this.canModifyGroupConditions = this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']);

    if (!this.activesSet && offre?.listeBlocs) {
      this.activesSet = true;
      offre?.listeBlocs?.forEach((bloc, index) => {
        this.activeIds.push('custom-panel-' + index);
      });
    }

    this.offre = offre;
    this.coffretEnabled = offre?.coffretEnabled;

    if (this.offre) {
      this.resultExisteFilsBlocsProduits = {};
      this.filsProduits = {};
      this.filsNonProduits = {};
      this.isFirstSet = true;

      this.modePaiementValuePair = [{ label: 'Aucune', value: null }];

      if (this.offre.listDelaiPaiements && this.offre.listDelaiPaiements.length) {
        this.offre.listDelaiPaiements?.map(item => {
          if (this.offre.delaiPaiement && (item.id === this.offre?.delaiPaiement?.id)) {
            this.modePaiementValuePair.push({ label: item.label, value: this.offre.delaiPaiement });
          } else {
            this.modePaiementValuePair.push({ label: item.label, value: item });
          }
        });
      }

      this.transporteurValuePair = [{ label: 'Aucun', value: null }];

      if (this.offre.raisonSocialeTransporteur) {
        this.offre?.raisonSocialeTransporteur?.split(',').map(transporteur => {
          const transporteurCmd = transporteur.trim();
          this.transporteurValuePair.push({ label: transporteurCmd, value: transporteurCmd });
        });
      }

      if (this.offre.distributeurs && this.offre.distributeurs.length) {
        this.distributeursValuePair = this.offre.distributeurs.map(dist => {
          if (this.offre.distributeur && (dist.id === this.offre.distributeur?.id)) {
            return { label: dist.raisonSociale, value: this.offre.distributeur };
          }
          return { label: dist?.raisonSociale, value: dist };
        });

        if (this.offre?.distributeurs?.length === 1 && !this.offre.distributeur) {
          this.offre.distributeur = this.offre?.distributeurs[0];
        }
      }

      if (this.offre?.etatCommandeAchatGroupe !== 'ACCEPTEE' && this.offre?.etatCommandeAchatGroupe !== 'EN_ATTENTE') {
        this.canModifyGroupConditions = false;
      }

      if (this.offre.coffretEnabled) {
        this.canModifyGroupConditions = false;
      }

      if (this.cmdType === TYPE_CMD.GROUPE) {
        this.fetchMembreOfCurrentGroupe();
      } else if (this.cmdType === TYPE_CMD.INDIVIDUELLE && this.offre.coffretEnabled) {
        this.qteCmdPackCoffret = this.offre?.listeBlocs[0]?.totalQteCmd / this.offre?.listeBlocs[0]?.totalQtePrdFixe;

        this.refreshCalculMontantUnitaireBrutCoffret();
      }

      this.workerService.stopCountDownOnOffre(this.offre?.id);
      this.workerService.startCountdownWorker(this.offre);
    }

    this.setOffreImageMaxHeight();
  }

  @Input('readOnly')
  set readOnly(readOnly: boolean) {
    this._readOnly = readOnly;
  }

  get readOnly() {
    return this._readOnly;
  }

  @Input('listeBlocsOffreProduits')
  set _listeBlocsOffreProduits(blocsProduits: BlocOffre[]) {
    this.listeBlocsOffreProduits = blocsProduits;

    if (
      this.listeBlocsOffreProduits &&
      this.listeBlocsOffreProduits.length > 0
    ) {
      this.parentOfProduits = this.listeBlocsOffreProduits[0].parent;
    }

    this.loadItems();

    let currentbloc = this.parentOfProduits;
    while (currentbloc) {
      if (currentbloc.listePaliers && currentbloc.listePaliers.length > 0) {
        for (const iterator of currentbloc.listePaliers) {
          if (iterator.ratioUg) {
            this.EnableSaisiUG = 'p';
          }
          break;
        }
      }
      currentbloc = currentbloc.parent;
    }

    if (blocsProduits?.length) {
      this.coffretEnabled = this.offresService.getPackParent(blocsProduits[0]).coffretEnabled;

      if (this.coffretEnabled) {
        this.qteCmdPackCoffret = this.parentOfProduits?.totalQteCmd / this.parentOfProduits?.totalQtePrdFixe;

        this.refreshCalculMontantUnitaireBrutCoffret();
      }
    }
  }

  @Input()
  blocOffre: BlocOffre;

  membersPerPage: number = 3;
  currentPage: number = 0;
  currentPagePacks: number = 0;
  currentPagePackMembre: number = 0;

  get currentMembers() {
    if (this.membresSupporteurs) {
      const start = this.currentPage * this.membersPerPage;
      const end = start + this.membersPerPage;
      return this.membresSupporteurs?.slice(start, end);
    }

    return [];
  }

  activePackIndex: number = 0;
  rechercheProduitUnitModel: string = '';

  modePaiementData: any[] = [];

  membresSupporteurs: PharmacieEntreprise[] = [];

  modifiedCmdsUnitaires: BlocOffre[] = [];
  membresParPackAggregate: any = {};

  memberIndex: number;
  distributeursValuePair: any[];
  supporteursValuePair: any[];
  canModifyGroupConditions: boolean = true;

  selectedOption: string = 'F';
  cmdGroupeSelectOptions = [
    { label: 'Produits', value: 'F' },
    { label: 'Membres', value: 'M' },
    { label: 'Pack', value: 'P' },
  ];

  isAdminUser: boolean;

  currentPlateforme$: Observable<SelectedPlateforme>;

  isFirstSet: boolean = true;
  parentElementRef: Element | null = null;

  offre: Offre;
  montantUnitaireBrutCoffret: number;
  selectedSociete: FournisseurDropdownItem | null = null;
  selectedClientLocal: ClientFournisseur | string;
  isClientLocalReadOnly: boolean = false;

  productIds: string[];
  blocIds: number[];

  /********** Partie Liste Porduit **********/
  listeBlocsOffreProduits: BlocOffre[];
  EnableSaisiUG = 'f';
  parentOfProduits: BlocOffre;
  _readOnly: boolean;
  gridData: GridDataResult;
  dataNbrPacksParMembre: GridDataResult;

  public formGroup: FormGroup;
  validCommande = true;
  private resultExisteFilsBlocsProduits = {};

  fixedSyntheseLeft: string;
  fixedSyntheseWidth: string;

  private filsProduits = {};

  private filsNonProduits = {};

  hasOffreConditions: Function = hasOffreConditions;

  hasGroupeConditions: Function = hasGroupeConditions;

  hasPaliers: Function = hasPaliers;

  hasManyConditions: Function = hasManyConditions;

  hasRatioUg: Function = hasRatioUg;

  hasTauxUg: Function = hasTauxUg;

  hasTypeSelectionRfUg: Function = hasTypeSelectionRfUg;

  blocHasTauxUg: Function = blocHasTauxUg;

  blocHasRatioUg: Function = blocHasRatioUg;

  canSelectTypeRfUg: Function = canSelectTypeRfUg;

  @Output() modePaiementChange: EventEmitter<DomainEnumeration> = new EventEmitter<DomainEnumeration>();

  @ViewChild('fixedPackSynthese') fixedPackSynthese: ElementRef;
  @ViewChild('firstCard') firstCard: ElementRef;

  constructor(
    private router: Router,
    private ngZone: NgZone,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private alertService: AlertService,
    private workerService: WorkerService,
    private offresService: OffresService,
    private fsOffreService: FsOffreService,
    private userInputService: UserInputService,
    private viewContainerRef: ViewContainerRef,
    private plateformeService: PlateformeService,
    private fsCommandeService: FsCommandesService,
    private uploadService: UploadFileServiceService,
    private fedSyndicatService: FederationSyndicatService
  ) {
    this.checkAdminUser();
    this.currentPlateforme$ = this.plateformeService.currentPlateforme$;
    this.isPlateformeWinGroupe = this.plateformeService.isPlateForme('WIN_GROUPE');

    if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) {
      this.cmdGroupeSelectOptions = [this.cmdGroupeSelectOptions[0], this.cmdGroupeSelectOptions[2]]
    }
    this.parentElementRef = this.viewContainerRef?.element?.nativeElement?.parentElement;
  }

  get isInactive$() {
    return this.fedSyndicatService.inactiveAccount$;
  }

  get FEATURE_KEY() {
    return FEATURE_KEY;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.offre && this.cmdsUnitaires) {
      if (this.cmdsUnitaires?.length) {
        for (const cmdUnitaire of this.cmdsUnitaires) {
          for (const bloc of cmdUnitaire?.listeBlocs) {
            this.offresService.declareMemberIdBlocOffre(this.offre, bloc, cmdUnitaire?.client?.id);
          }
        }

        this.dataNbrPacksParMembre = {
          data: this.offre.listeBlocs,
          total: this.offre.listeBlocs.length
        }

        this.membresSupporteurs.map((item, index) => {
          this.membresParPackAggregate[item?.id] = {
            totalQteCmdForMember: this.cmdsUnitaires[index]?.totalQteCmd,
            totalValeurBruteCmdMember: this.cmdsUnitaires[index]?.totalValeurBruteCmd,
            totalValeurNetteCmdMember: this.cmdsUnitaires[index]?.totalValeurNetteCmd
          };
        })
      }
    }
  }

  checkAdminUser() {
    const hasSuperAdmin = this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']);
    const hasNational = this.authService.hasAnyAuthority(['ROLE_NATIONAL']);
    const hasResponsable = this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']);

    this.isAdminUser = hasSuperAdmin || (hasNational && !hasResponsable);
  }

  private listenToActivePackIndexChanges(): void {
    this.offresService.activeTabIndex$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(activeIndex => {
        this.activePackIndex = this.cmdType === TYPE_CMD.UNITAIRE ? activeIndex : activeIndex - 1;
      });
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.setFixedSyntheseWidth();

      const firstCardHeight: number =
        this.firstCard?.nativeElement?.clientHeight;

      if (firstCardHeight) {
        this.firstCardHeight = firstCardHeight + 'px';
      }
    }, 800);
  }

  ngOnInit(): void {
    this.isResponsable = this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']);
    this.cmdType = this.route.snapshot.params['type'];

    if (this.cmdType === TYPE_CMD.GROUPE) {
      this.modifiedCmdsUnitaires = [];
    }

    if (this.cmdType === TYPE_CMD.UNITAIRE) {
      this.activePackIndex = 1;
    }

    this.listenToSidebarStateChanges();
    this.listenToPageContainerResizeEvents();
    this.listenToActivePackIndexChanges();
  }

  private fetchMembreOfCurrentGroupe(): void {
    this.fedSyndicatService.getMyGroupe().then(myGroupe => {
      if (myGroupe) {
        this.managedGroupe = myGroupe;

        this.fedSyndicatService
          .searchPharmacieEntreprise({ skip: 0, pageSize: 50 }, { groupeEntrepriseDTO: myGroupe, typeEntreprises: [SocieteType.CLIENT] })
          .subscribe((res) => {
            this.supporteursValuePair = buildSupporteursList(res?.content, this.offre);
          });
      }
    });
  }

  selectedOptionChange(value: string) {
    this.selectedOption = value;
  }

  cmdGroupeTabChange(_event: Event, blocOffre: BlocOffre) {
    this.rechercheProduitCmdModel = null;
    this.rechercheProduitMembreModel = null;
    this.selectedBlocElement = blocOffre;
  }

  mergeListePaliers(listePaliers: DetailValeurPalier[]) {
    return this.offresService.mergePaliersDeModePaiementComptant(listePaliers);
  }

  setOffreImageMaxHeight(): void {
    let rowMultiplier: number = 0;

    this.offre?.commandStatut !== 'NOUVELLE' &&
      this.offre?.listePaliersRemisesAdditionnels?.length &&
      rowMultiplier++;

    if (!this.readOnly || this.offre?.commandStatut === 'BROUILLON') {
      rowMultiplier += this.authService.hasAnyAuthority([
        'ROLE_AGENT_FOURNISSEUR',
        'ROLE_AGENT_COMMERCIAL',
      ])
        ? 4
        : 3;
    } else {
      rowMultiplier += this.authService.hasAnyAuthority([
        'ROLE_AGENT_FOURNISSEUR',
        'ROLE_AGENT_COMMERCIAL',
      ])
        ? 5
        : 4;
    }

    this.dynamicImageMaxHeight = rowMultiplier * 65 + 'px';
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  nextPage() {
    if (
      (this.currentPage + 1) * this.membersPerPage <
      this.membresSupporteurs?.length
    ) {
      this.currentPage++;
    }
  }

  previousPage() {
    if (this.currentPage > 0) {
      this.currentPage--;
    }
  }

  get canGoToPreviousPage() {
    return this.currentPage > 0;
  }

  get canGoToNextPage() {
    return (
      (this.currentPage + 1) * this.membersPerPage <
      this.membresSupporteurs?.length
    );
  }

  get currentMembersPacks() {
    const start = this.currentPagePacks * this.membersPerPage;
    const end = start + this.membersPerPage;
    return this.membresSupporteurs?.slice(start, end);
  }

  get currentMembersPacksParMembre() {
    const start = this.currentPagePackMembre * this.membersPerPage;
    const end = start + this.membersPerPage;
    return this.membresSupporteurs?.slice(start, end);
  }

  nextPagePacks() {
    if (
      (this.currentPagePacks + 1) * this.membersPerPage <
      this.membresSupporteurs?.length
    ) {
      this.currentPagePacks++;
    }
  }

  previousPagePacks() {
    if (this.currentPagePacks > 0) {
      this.currentPagePacks--;
    }
  }

  nextPagePacksParMembre() {
    if (
      (this.currentPagePackMembre + 1) * this.membersPerPage <
      this.membresSupporteurs?.length
    ) {
      this.currentPagePackMembre++;
    }
  }

  previousPagePacksParMembre() {
    if (this.currentPagePackMembre > 0) {
      this.currentPagePackMembre--;
    }
  }

  get canGoToPreviousPagePacks() {
    return this.currentPagePacks > 0;
  }

  get canGoToNextPagePacks() {
    return (
      (this.currentPagePacks + 1) * this.membersPerPage <
      this.membresSupporteurs?.length
    );
  }

  get canGoToPreviousPagePacksParMembre() {
    return this.currentPagePackMembre > 0;
  }

  get canGoToNextPagePacksParMembre() {
    return (
      (this.currentPagePackMembre + 1) * this.membersPerPage <
      this.membresSupporteurs?.length
    );
  }

  existeFilsBlocsProduits(currentBloc: BlocOffre = null) {
    if (!currentBloc) {
      currentBloc = this.blocOffre;
    }

    if (this.resultExisteFilsBlocsProduits[currentBloc.id] != null) {
      return this.resultExisteFilsBlocsProduits[currentBloc.id];
    }

    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc === 'F') {
          this.resultExisteFilsBlocsProduits[currentBloc.id] = true;

          return true;
        }
      }
      this.resultExisteFilsBlocsProduits[currentBloc.id] = false;
      return false;
    } else {
      this.resultExisteFilsBlocsProduits[currentBloc.id] = false;
      return false;
    }
  }

  getListeFilsBlocsProduits(currentBloc: BlocOffre = null) {
    if (!currentBloc) {
      currentBloc = this.blocOffre;
    }

    if (this.filsProduits[currentBloc.id]) {
      return this.filsProduits[currentBloc.id];
    }

    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      this.filsProduits[currentBloc.id] = [];
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc === 'F') {
          bloc.conditionsString = this.buildDataItemConditions(bloc, this.cmdType);
          this.filsProduits[currentBloc.id].push(bloc);
        }
      }
    } else {
      this.filsProduits[currentBloc.id] = [];
    }

    return this.filsProduits[currentBloc.id];
  }

  getListeFilsBlocsNonProduits(
    currentBloc: BlocOffre = null,
    targetBloc?: string
  ) {
    if (!currentBloc) {
      currentBloc = this.blocOffre;
    }

    if (this.filsNonProduits[currentBloc.id]) {
      return this.filsNonProduits[currentBloc.id];
    }

    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      this.filsNonProduits[currentBloc.id] = [];
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc !== 'F' || bloc.typeBloc === targetBloc) {
          this.filsNonProduits[currentBloc.id].push(bloc);
        }
      }
    } else {
      this.filsNonProduits[currentBloc.id] = [];
    }

    return this.filsNonProduits[currentBloc.id];
  }

  private loadItems(): void {
    this.gridData = {
      data: this.listeBlocsOffreProduits,
      total: this.listeBlocsOffreProduits.length,
    };

    this.refreshCalculMontantUnitaireBrutCoffret();
  }

  updateValueObligatoire(blocOffre: BlocOffre, booleanValue: boolean) {
    const payload = new ConditionBlocOffreCommandeConsolidee({ blocOffreId: blocOffre.id, blocObligatoire: booleanValue ? 'O' : 'N' });

    if (!blocOffre.conditionCmdUnitaireSpecGroup) {
      blocOffre.conditionCmdUnitaireSpecGroup = payload;
    } else {
      blocOffre.conditionCmdUnitaireSpecGroup.blocObligatoire = booleanValue ? 'O' : 'N';
    }
  }

  openConditionsModal(content: TemplateRef<any>, bloc: BlocOffre) {
    if (!bloc.conditionCmdUnitaireSpecGroup) {
      bloc.conditionCmdUnitaireSpecGroup = new ConditionBlocOffreCommandeConsolidee({ blocOffreId: bloc.id });
    }

    this.selectedGroupeConditionsBloc = bloc;
    this.modalService.open(content, { size: 'md', centered: true });
  }

  hasGroupConditions(blocOffre: BlocOffre) {
    return (
      (blocOffre?.conditionCmdUnitaireSpecGroup?.qteMin ?? false) ||
      (blocOffre?.conditionCmdUnitaireSpecGroup?.qteMax ?? false) ||
      (blocOffre?.conditionCmdUnitaireSpecGroup?.valeurMin ?? false) ||
      (blocOffre?.conditionCmdUnitaireSpecGroup?.valeurMax ?? false)
    );
  }

  enregistrerLesModifications() {
    this.userInputService.confirmAlt('Confirmation', 'Êtes vous sûr de vouloir enregistrer les modifications ?').then(
      () => {
        this.goAttacherSupporteur().subscribe(res => {
          this.goAttacherDistributeur().subscribe(res => {
            iif(
              () => this.offre?.etatCommandeAchatGroupe !== 'EN_ATTENTE',
              of(null),
              this.goSaveConditions()
            ).subscribe(res => {
              this.refreshPage();
              this.alertService.successAlt(`Vos modifications ont été enregistrées avec succès.`, 'Modifications Enregistrées', 'MODAL');
            });

          });
        })
      },
      () => null
    )
  }

  goAttacherSupporteur() {
    if (this.offre?.supporterEntreprise) {
      return this.fsCommandeService.attacherSupporteur(this.offre?.enteteCommandeId, this.offre?.supporterEntreprise?.id);
    }
    return of(null);
  }

  goAttacherDistributeur() {
    if (this.offre?.distributeur) {
      return this.fsOffreService.attacherDistributeurCommandeConsolidee(this.offre?.enteteCommandeId, this.offre?.distributeur?.id);
    }
    return of(null);
  }

  goSaveConditions() {
    const conditionsBlocOffreCmd = this.offresService.fetchAllConditionUnitInAllInnerBlocOffres(this.offre);

    if (conditionsBlocOffreCmd?.length) {
      const selectedConditions = conditionsBlocOffreCmd;

      return this.fsOffreService.saveConditionsCommandeConsolidee(this.offre?.enteteCommandeId, selectedConditions);
    }

    return of(null);
  }


  handleCellCommit(event: KeyboardEvent, grid: GridComponent | null): void {
    if (grid && (event.key === 'Enter' || event.key === 'Tab' || event.key === 'Escape')) {
      if (grid.isEditing()) {
        grid.closeCell();
      }
    }
  }

  saveConditions() {
    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir enregistrer les modifications ?`).then(
      () => {
        let conditionsBlocOffreCmd = this.offresService.fetchAllConditionUnitInAllInnerBlocOffres(this.offre);

        if (conditionsBlocOffreCmd?.length) {
          const selectedConditions = conditionsBlocOffreCmd;

          this.fsOffreService.saveConditionsCommandeConsolidee(this.offre?.enteteCommandeId, selectedConditions)
            .subscribe(res => {
              this.refreshPage();
              this.alertService.successAlt('Conditions du groupe enregistrées avec succès.', 'Conditions Enregistrées', 'MODAL');
            });
        }
      },
      () => null
    )
  }

  validerLesChangements(): void {
    if (this.offre.cmdsUnitairesModifie?.length) {
      this.fsCommandeService
        .saveDetailCommandeUnitaire(
          this.offre.cmdsUnitairesModifie,
          this.managedGroupe?.id
        )
        .subscribe((res) => {
          this.refreshPage();

          this.offre.cmdsUnitairesModifie = [];
          this.alertService.successAlt(`Vos changements ont été validés avec succès.`, 'Validation des Changements', 'MODAL');
        });
    } else {
      this.alertService.error("Vous n'avez aucun changement à valider.", 'MODAL');
    }
  }

  refreshPage(): void {
    const qParams = this.route.snapshot.queryParams;
    this.router.navigate([], { relativeTo: this.route, skipLocationChange: true, queryParamsHandling: 'merge', queryParams: { ...qParams, modified: true, _timestamp: new Date().getTime() } });
  }

  open(content: TemplateRef<any>, size?: string) {
    this.modalService.open(content, { modalDialogClass: 'fs-radius-modal', size, centered: true, animation: true });
  }

  fireEvent(e: Event) {
    e.stopPropagation();
    e.preventDefault();
    return false;
  }

  public cellClickHandler(args: CellClickEvent) {
    const { sender, rowIndex, columnIndex, column, dataItem, isEdited } = args;

    if ((column.field !== 'qteCmd' && column.field !== 'qteUgSaisie') || (this.cmdType === TYPE_CMD.UNITAIRE && this.readOnly)) {
      return;
    }

    if (this.isInactive$.value === true) {
      return;
    }

    if (!isEdited) {
      sender.editCell(rowIndex, columnIndex, this.createFormGroup(dataItem));
    }
  }

  public cellCloseHandler(args: any) {

    const { formGroup, dataItem } = args;

    if (!formGroup.valid) {
      // prevent closing the edited cell if there are invalid values.
      args.preventDefault();

    } else if (formGroup.dirty) {
      dataItem.qteCmd = formGroup.value.qteCmd ? +formGroup.value.qteCmd : null;

      if (this.cmdType === TYPE_CMD.GROUPE || this.cmdType === TYPE_CMD.INDIVIDUELLE) {
        dataItem.qteUgSaisie = formGroup.value.qteUgSaisie ? +formGroup.value.qteUgSaisie : null;
        dataItem.totalQteUg = dataItem.qteUgSaisie;

        this.fireRefreshEtatCommandeConsolidee(dataItem);
      } else {
        this.offresService.refreshEtatBlocOffreSurCmdUnit(dataItem);
      }

      this.checkifvalid(dataItem);
    }
  }

  fireRefreshEtatCommandeConsolidee(dataItem: BlocOffre) {
    this.offresService.refreshEtatBlocOffre(dataItem);
    this.offresService.palierOffreTraitement(this.offresService.getOffreRacine(dataItem));
  }

  listenToSidebarStateChanges(): void {
    this.offresService.currentEtatBar
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((etatBar) => {
        this.sideBarState = etatBar;

        this.setFixedSyntheseWidth();
      });
  }

  private setFixedSyntheseWidth(): void {
    if (window.innerWidth > 991) {
      this.fixedSyntheseLeft =
        this.sideBarState === 'condensed' ? '70px' : '260px';

      this.fixedSyntheseWidth =
        this.sideBarState === 'condensed'
          ? 'calc(100% - 70px)'
          : 'calc(100% - 260px)';
    } else {
      (this.fixedSyntheseLeft = '0'), (this.fixedSyntheseWidth = '100%');
    }

    setTimeout(() => {
      const syntheseHeight: number =
        this.fixedPackSynthese?.nativeElement?.clientHeight;

      if (syntheseHeight) {
        this.fixedPackSyntheseHeight = syntheseHeight + 15;
      }
    }, 200);

  }

  listenToPageContainerResizeEvents(): void {
    this.resizeObserver = new ResizeObserver(entries => {
      this.ngZone.run(() => {
        for (let entry of entries) {
          const { width, height } = entry.contentRect;

          if (window.innerWidth > 919) {
            this.fixedSyntheseLeft =
              (this.sideBarState === 'condensed') ? '70px' : '260px';

            this.fixedSyntheseWidth =
              (this.sideBarState === 'condensed') ? 'calc(100% - 70px)' : 'calc(100% - 260px)';
          } else {
            (this.fixedSyntheseLeft = '0'), (this.fixedSyntheseWidth = '100%');
          }
        }

        this.afficherSynthese = (window.innerWidth > 991) ? true : false;

        setTimeout(() => {
          const syntheseHeight: number =
            this.fixedPackSynthese?.nativeElement?.clientHeight;

          if (syntheseHeight) {
            this.fixedPackSyntheseHeight = syntheseHeight + 15;
          }
        }, 200);
      });
    });

    if (this.parentElementRef) {
      this.resizeObserver.observe(this.parentElementRef);
    }
  }

  public createFormGroup(dataItem: BlocOffre): FormGroup {
    return this.formBuilder.group({
      qteCmd: dataItem.qteCmd,
      qteUgSaisie: dataItem.qteUgSaisie,
    });
  }

  public createCmdGroupeFormGroup(dataItem: BlocOffre, memberIndex: number): FormGroup {
    const memberData = dataItem.mapBlocsByMemberId[memberIndex];
    const formGroup = this.formBuilder.group({});

    Object.keys(memberData).forEach(key => {
      if (Array.isArray(memberData[key])) {
        formGroup.addControl(key, this.formBuilder.array(memberData[key].map(value => new FormControl(value))));
      } else {
        formGroup.addControl(key, new FormControl(memberData[key]));
      }
    });

    return formGroup;
  }

  public createNbrByMemberFormGroup(dataItem: BlocOffre, memberIndex: number): FormGroup {
    return this.formBuilder.group(dataItem.mapNbrByMemberId[memberIndex]);
  }

  public checkifvalid(currentbloc: BlocOffre) {
    const currentstat = currentbloc.parent;

    if ((currentstat && currentstat.etat === 'I')) {
      this.validCommande = false;
    } else {
      this.validCommande = true;
    }

    this.offresService.subject.next(this.validCommande)
  }

  public rowClassCallback(context: RowClassArgs) {
    if (context.dataItem.etat === 'I') {
      return { badQte: true };
    } else {
      return {};
    }
  }

  hasConditions(blocOffre: BlocOffre) {
    return hasConditions(blocOffre, this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']));
  }

  hasAnyProduitsConditions(listeFils: BlocOffre[]) {
    return hasAnyProduitsConditions(listeFils, this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']));
  }

  modePaiementValueChange(selected: DomainEnumeration): void {
    if (this.offre.delaiPaiement?.id !== selected?.id) {
      this.modePaiementChange.emit(selected);
    }
  }

  fireModePaiementValueChange(): void {
    for (let bloc of this.offre?.listeBlocs) {
      this.offresService.refreshEtatBlocOffre(bloc);
      this.offresService.palierOffreTraitement(this.offresService.getOffreRacine(bloc));

      this.checkifvalid(bloc);
    }
  }

  openExtraConditionsModal(
    content: TemplateRef<any>,
    targetBloc: BlocOffre,
    size = 'md'
  ): void {
    this.extraConditionsTargetBloc = targetBloc;
    this.modalService.open(content, { size });
  }

  rowClassProduitUnitaire = (context: RowClassArgs) => {
    if (this.rechercheProduitUnitModel?.length) {
      return !context?.dataItem?.libelleProduit?.toLowerCase()?.includes(this.rechercheProduitUnitModel?.toLowerCase()) ? 'hidden-row' : '';
    }
    return '';
  };

  rowClassGeneric = (context: RowClassArgs) => {
    if (context?.dataItem?.parent?.designationProduit?.length) {
      return !context?.dataItem?.libelleProduit?.toLowerCase()?.includes(context?.dataItem?.parent?.designationProduit?.toLowerCase()) ? 'hidden-row' : '';
    }
    return '';
  }

  precedent(): void {
    this.isSelectingDistributeur = false;
    this.selectedSupporter = null;
  }

  checkIsSupporteur(supporteur: PharmacieEntreprise): boolean {
    if (supporteur) {
      return this.authService.getPrincipal().societe?.id === supporteur?.id;
    }
    return false;
  }

  cmdGroupeCellClick(args: CellClickEvent) {
    const { sender, rowIndex, columnIndex, column, dataItem, isEdited } = args;

    if (this.authService.canAccessFeature(
      FEATURE_KEY.SUPPORTEUR_CAN_EDIT_COMMANDE,
      ['ROLE_RESPONSABLE'],
      this.checkIsSupporteur(this.offre?.supporterEntreprise),
      [FEATURE_KEY_STORAGE.ROOT, FEATURE_KEY_STORAGE.GROUP]
    )) {      
      if (((this.canModifyGroupConditions && columnIndex < 3) || (!this.canModifyGroupConditions && !column?.title?.includes('Dr.'))) || this.offre?.coffretEnabled) {
        return;
      }

      if (this.isInactive$.value === true) {
        return;
      }

      // Sanitize client.id of selected cell from column.field | client id are prefixed with '_' to prevent kendo-grid valid column field warnings
      const sanitizedClientId: string = column?.field?.substring(1);

      this.memberIndex = +sanitizedClientId;

      if (!isEdited && this.cmdsUnitaires?.length) {
        sender.editCell(rowIndex, columnIndex, this.createCmdGroupeFormGroup(dataItem, this.memberIndex));
      }
    }
  }

  nbrPackGroupeCellClick(args: CellClickEvent) {
    const { sender, rowIndex, columnIndex, column, dataItem, isEdited } = args;

    if (
      this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']) ||
      (this.plateformeService.isPlateForme('WIN_GROUPE') && this.checkIsSupporteur(this.offre?.supporterEntreprise))
    ) {
      if (!column?.title?.includes('Dr.')) {
        return;
      }

      if (this.isInactive$.value === true) {
        return;
      }

      // Sanitize client.id of selected cell from column.field | client id are prefixed with '_' to prevent kendo-grid valid column field warnings
      const sanitizedClientId: string = column?.field?.substring(1);

      this.memberIndex = +sanitizedClientId;

      if (!isEdited && this.cmdsUnitaires?.length) {
        sender.editCell(rowIndex, columnIndex, this.createCmdGroupeFormGroup(dataItem, this.memberIndex));
      }
    }
  }

  getBlocImage(idHash: string) {
    if (idHash)
      return this.uploadService.fetchUploadedDocument(idHash);

    return '';
  }

  nbrPackGroupeCellClose(args: CellCloseEvent) {
    const { formGroup, dataItem } = args;

    if (!formGroup.dirty) {
      return;
    }

    const nbrPackUnitaireApres = formGroup.get('totalNbrPackCmd').value;
    const nbrPackUnitaireAvant = dataItem.mapBlocsByMemberId[this.memberIndex].totalNbrPackCmd;


    const blocPackGroup: BlocOffre = dataItem;

    for (const blocFilsGroup of blocPackGroup.listeFils) {
      const modifiedUnitItem: BlocOffre = blocFilsGroup.mapBlocsByMemberId[this.memberIndex];

      const qteUnitaireApres = modifiedUnitItem.qteFixePrdInCoffret * nbrPackUnitaireApres;
      const qteUnitaireAvant = modifiedUnitItem.qteCmd;

      this.fireChangeQteUnit(blocFilsGroup, qteUnitaireApres, qteUnitaireAvant, modifiedUnitItem);
    }

    dataItem.totalNbrPackCmd = dataItem.totalNbrPackCmd + (nbrPackUnitaireApres - nbrPackUnitaireAvant);

    dataItem.mapBlocsByMemberId[this.memberIndex].totalNbrPackCmd = formGroup.get('totalNbrPackCmd').value;

  }

  cmdGroupeCellClose(args: CellCloseEvent) {
    const { formGroup, dataItem } = args;

    if (!formGroup.dirty) {
      return;
    }


    const modifiedItem: BlocOffre = formGroup.value;

    const qteUnitaireApres = formGroup.get('qteCmd').value;
    const qteUnitaireAvant = dataItem.mapBlocsByMemberId[this.memberIndex].qteCmd;

    this.fireChangeQteUnit(dataItem, qteUnitaireApres, qteUnitaireAvant, modifiedItem);
  }

  private fireChangeQteUnit(blocOffreGroupe: BlocOffre,
    qteUnitaireApres: number,
    qteUnitaireAvant: number,
    blocOffreUnitaireModifie: BlocOffre) {

    blocOffreGroupe.qteCmd = blocOffreGroupe.qteCmd + (qteUnitaireApres - qteUnitaireAvant);

    blocOffreGroupe.mapBlocsByMemberId[this.memberIndex].qteCmd = qteUnitaireApres;

    const enteteCommandeId = this.cmdsUnitaires.find(cmd => cmd?.client?.id === this.memberIndex).enteteCommandeId;

    const selectedCmdUnitIds = this.modifiedCmdsUnitaires.map(fils => {
      return `${fils?.detailCommandeId || fils?.enteteCommandeId}|${fils.id}`;
    });

    let currentBlocModifieKeyId = `${blocOffreUnitaireModifie?.detailCommandeId || enteteCommandeId}|${blocOffreUnitaireModifie.id}`

    if (!blocOffreUnitaireModifie?.detailCommandeId) {
      blocOffreUnitaireModifie['enteteCommandeId'] = enteteCommandeId;
    }

    if (!selectedCmdUnitIds?.includes(currentBlocModifieKeyId)
    ) {
      this.modifiedCmdsUnitaires.push(blocOffreUnitaireModifie);
    } else {
      let targetIndex: number;

      if (blocOffreUnitaireModifie.detailCommandeId) {
        targetIndex = this.modifiedCmdsUnitaires.findIndex(cmd => (cmd.detailCommandeId === blocOffreUnitaireModifie.detailCommandeId));
      } else {
        targetIndex = this.modifiedCmdsUnitaires.findIndex(cmd =>
          (cmd.enteteCommandeId === blocOffreUnitaireModifie.enteteCommandeId) &&
          (cmd.id === blocOffreUnitaireModifie.id)
        );
      }

      if (targetIndex > -1) {
        this.modifiedCmdsUnitaires[targetIndex] = blocOffreUnitaireModifie;
      }
    }

    // Actualiser l'état du commande groupe
    this.offresService.refreshEtatBlocOffre(blocOffreGroupe);
    this.offresService.palierOffreTraitement(this.offresService.getOffreRacine(blocOffreGroupe));

    this.checkifvalid(blocOffreGroupe);

    // Recalculer les totaux de la commande unitaire modifiée
    this.offresService.refreshEtatBlocOffreSurCmdUnit(blocOffreGroupe.mapBlocsByMemberId[this.memberIndex]);

    this.offre.cmdsUnitairesModifie = this.modifiedCmdsUnitaires;
  }

  selectedRemiseChange(newSelecteTypeRemise: SelectedTypeRemiseEnum, dataItem: BlocOffre) {
    dataItem.selectedTypeRemiseEnum = newSelecteTypeRemise;

    // Actualiser l'état du commande groupe
    this.fireRefreshEtatCommandeConsolidee(dataItem);

    // ? Set qteUgSaisie after palier selection
    dataItem.qteUgSaisie = dataItem?.totalQteUg;

    this.checkifvalid(dataItem);
  }

  qteCmdPackCoffretValueChange(value: number, increment = false, sub = false): void {
    value && (this.qteCmdPackCoffret = value);

    increment && (++this.qteCmdPackCoffret);

    if (sub && (this.qteCmdPackCoffret - 1) > -1) {
      --this.qteCmdPackCoffret;
    }

    this.refreshCalculMontantUnitaireBrutCoffret();
  }

  appliquerLaSaisieAccelere(qteCmd: string, blocParent: BlocOffre, type: string) {
    this.offresService.accelerateurDeSaisie(+qteCmd, blocParent, type);
    this.checkifvalid(blocParent);
  }

  private refreshCalculMontantUnitaireBrutCoffret() {
    this.montantUnitaireBrutCoffret = 0;

    const filsProduitCoffret = this.cmdType === TYPE_CMD.UNITAIRE ? this.gridData.data : this.offre?.listeBlocs[0]?.listeFils;

    if (filsProduitCoffret?.length) {
      for (const blocPrd of filsProduitCoffret) {
        this.montantUnitaireBrutCoffret += (blocPrd.qteFixePrdInCoffret ?? 0) * (blocPrd.prixVenteTtc ?? 0);

        blocPrd['qteCmd'] = blocPrd.qteFixePrdInCoffret * this.qteCmdPackCoffret;

        if (this.cmdType === TYPE_CMD.UNITAIRE) {
          this.offresService.refreshEtatBlocOffreSurCmdUnit(blocPrd);
        } else { // ? Cas d'une commande individuelle
          this.offresService.refreshEtatBlocOffre(blocPrd);
        }
      }

      if (this.qteCmdPackCoffret) {
        this.montantUnitaireBrutCoffret *= this.qteCmdPackCoffret;
      }
    }

  }

  private getUploadedFileUrl(idHash: string) {
    return this.uploadService.fetchUploadedDocument(idHash);
  }

  async openAttachement(item: DocMetaDataDto, modalContent?: TemplateRef<any>) {
    if (item.fileType?.includes('image')) {
      this.openLargImageModal(modalContent, this.getUploadedFileUrl(item.idhash), item);
    } else {
      this.pdfViewerTitle = item.name.toUpperCase();
      this.blobUrl = await this.getUploadedFileUrlBlob(item.idhash);
    }
  }

  private async getUploadedFileUrlBlob(idhash: string) {
    const response = await fetch(this.getUploadedFileUrl(idhash));
    const blob = await response.blob();

    return blob;
  }

  downloadDisplayedLargeImage(item: DocMetaDataDto) {
    this.offresService.printDocMetaDataItem(item);
  }

  openModal(content: TemplateRef<any>, size = 'lg') {
    this.modalService.open(content, { size, modalDialogClass: 'fs-radius-modal', centered: true });
  }

  isIntersecting(isVisible: boolean, tip: NgbTooltip) {
    !isVisible && tip.close();
  }

  openLargImageModal(content: TemplateRef<any>, target: string, item: DocMetaDataDto = null) {
    this.largeImageTargetUrl = target;

    setTimeout(() => this.targetItem = item, 400);
    this.modalService.open(content, { centered: true, size: 'xl' }).result.then(
      () => this.targetItem = null,
      () => this.targetItem = null
    );
  }

  trackFn(_index: number, item: BlocOffre) {
    return item?.hash;
  }

  trackMembres(_index: number, item: PharmacieEntreprise) {
    return item?.id;
  }

  forceExpandOrCollapseAllSousBlocs(bloc: BlocOffre, collapse: boolean) {
    this.offresService.forceCollapseOrExpandAllOnBlocOffre(bloc, collapse);
  }
}
