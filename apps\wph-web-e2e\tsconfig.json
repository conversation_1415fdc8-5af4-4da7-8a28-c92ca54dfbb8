{"extends": "../../tsconfig.base.json", "compilerOptions": {"sourceMap": false, "outDir": "../../dist/out-tsc", "allowJs": true, "types": ["cypress", "node"], "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*.ts", "src/**/*.js", "cypress.config.ts"], "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}