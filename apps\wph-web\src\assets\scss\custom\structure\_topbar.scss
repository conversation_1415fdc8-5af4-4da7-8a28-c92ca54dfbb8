//
// topbar.scss
//

.navbar-custom {
    padding: 0 $grid-gutter-width;
    background-color: $bg-topbar;
    box-shadow: $shadow;
    min-height: $topbar-height;
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 99;
    max-height: 70px;
    background: #00a6d5;

    .topbar-left {
        background-color: $bg-topbar;
        height: $topbar-height;
        position: fixed;
        z-index: 1;
        width: 0;
        text-align: center;
        top: 0;
        left: 0;
        .logo {
            line-height: $topbar-height;
            i {
                display: none;
            }
        }
    }
    .topbar-right-menu {
        li {
            float: left;
            max-height: $topbar-height;

            &.show {
                .nav-link {
                    color: $gray-700;
                }
            }
        }
        .nav-link {
            padding: 0;
            color: $gray-600;
            min-width: 32px;
            display: block;
            text-align: center;
            margin: 0 10px;
            @include media-breakpoint-down(md) {
                min-width: auto;
            }
        }
    }

    .app-search {
        padding: calc(#{$topbar-height - 38px} / 2) 0;
        overflow: hidden;
        display: none;
        form {
            max-width: 320px;
        }
    }
}

.topbar-dropdown {
    .nav-link {
        line-height: $topbar-height;
    }
}

/* Search */

.app-search {
    .form-control {
        border: none;
        height: 38px;
        padding-left: 40px;
        padding-right: 20px;
        background-color: $bg-topbar-search;
        box-shadow: none;
    }
    span {
        position: absolute;
        z-index: 9;
        font-size: 20px;
        line-height: 38px;
        left: 10px;
        top: 0;
    }
    .input-group-append {
        margin-left: 0;
        z-index: 4;
    }
}

#FS_notification-drop-down {
    .dropdown-menu.dropdown-menu-right {
        transform: none !important;
        top: 85% !important;
        right: 0 !important;
        left: auto !important;
        width: 150px;
    }
}

/* Notification */
.notification-list {
    margin-left: 0;

    .dropdown-menu.dropdown-menu-right {
        transform: none !important;
        top: 100% !important;
        right: 0 !important;
        left: auto !important;
    }

    .noti-title {
        background-color: transparent;
        padding: 15px 20px;
    }

    .noti-icon {
        color: #fff;
        font-size: 22px;
        vertical-align: middle;
        line-height: $topbar-height;
    }

    .noti-icon-badge {
        display: inline-block;
        position: absolute;
        top: 20px;
        left: 28px;
        border-radius: 50%;
        height: 17px;
        width: 17px;
        font-size: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 600;
        background-color: $danger;
    }

    .notify-item {
        padding: 10px 20px;

        .notify-icon {
            float: left;
            height: 36px;
            width: 36px;
            line-height: 36px;
            text-align: center;
            margin-right: 10px;
            border-radius: 50%;
            color: $white;
        }

        .notify-details {
            margin-bottom: 0;
            overflow: hidden;
            margin-left: 45px;
            text-overflow: ellipsis;
            white-space: nowrap;
            b {
                font-weight: 500;
            }
            small {
                display: block;
            }
            span {
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 13px;
            }
        }

        .user-msg {
            margin-left: 45px;
            white-space: normal;
            line-height: 16px;
        }
    }
    .topbar-dropdown-menu {
        .notify-item {
            padding: 7px 20px;
        }
    }
}

.profile-dropdown {
    width: 170px;
    i,span {
        vertical-align: middle;
    }
}

.notification-dropdown {
    position: relative;
    top: -15px;
    z-index: 99;
}

.extra-padding-top {
    padding-top: 8px !important;
}

.win-cstm {
    color: var(--wo-primary-900) !important;
}

.nav-user {
    padding: 20px 0 0 60px !important;
    text-align: left !important;
    position: relative;
    background-color: transparent !important;
    border: 1px solid $gray-100;
    border-width: 0 1px;
    min-height: 55px;

    .account-user-avatar {
        position: absolute;
        top: calc(#{$topbar-height - 38px} / 2);
        left: 15px;

        img {
            height: 36px;
            width: 36px;
            object-fit: contain;
        }
    }
    .account-position {
        display: block;
        font-size: 14px;
        margin-top: -3px;
        display: block;
        font-weight: $font-weight-bold;
        color: #fff;
    }
    .account-user-name {
        display: block;
        font-weight: 600;
        color: #fff;
        font-size: 12px;
    }
  
}

.button-menu-mobile {
    border: none;
    color: $light;
    height: $topbar-height;
    line-height: $topbar-height;
    width: 60px;
    background-color: transparent;
    font-size: 24px;
    cursor: pointer;
    float: left;

    i {
        vertical-align: middle;
    }

    &.disable-btn {
        display: none;
    }
}

[data-keep-enlarged="true"] {
    .navbar-custom {
        padding-left: 0;
    }

    .button-menu-mobile {
        &.disable-btn {
            display: inline-block;
        }
    }
}

@media (max-width: 1024px) {
    .navbar-custom {
        left: 0;
        right: 0;

        .app-search {
            display: none;
        }
    }
}


@include media-breakpoint-down(md) {
    .wrapper .navbar-custom, body[data-leftbar-compact-mode=condensed] .wrapper .footer{
        left: 0 !important;
    }

    .button-menu-mobile {
        &.disable-btn {
            display: inline-block;
        }
    }
}

@include media-breakpoint-down(sm) {

    .navbar-custom {
        left: 0;
        padding: 0 10px;
        margin: -5px -10px 0;
    }
    .Actionbreadcrumfix,.breadcrumfix {
       border-radius: 0;
        top: 65px !important;
    }
    .nav-user {
        padding: 0px 5px 17px 57px !important;

        .account-position,
        .account-user-name {
            display: none;
        }
    }
}

// Scrollable Layout

@include media-breakpoint-up(xl) {
    body[data-leftbar-compact-mode="scrollable"] {
        .navbar-custom {
            position: absolute;
        }
    }
}

// Topbar Dark
.topnav-navbar-dark {
    background-color: $bg-topbar-dark;

    .nav-user {
        background-color: lighten($bg-topbar-dark,5%);
        border: 1px solid lighten($bg-topbar-dark,7.5%);
    }

    .topbar-right-menu {
        li {
            &.show {
                .nav-link {
                    color: $white;
                }
            }
        }
    }

    .app-search {
        .form-control {
            background-color: $bg-topbar-dark-search;
            color: $white;
        }
        span {
            color: $gray-600;
        }
    }

    .navbar-toggle {
        span {
            background-color: rgba($white, 0.8) !important;
        }
    }
}

body[data-layout-mode="boxed"] {
    .navbar-custom {
        position: relative;
        left: 0 !important;
        margin: -$topbar-height (-$grid-gutter-width / 2) 0;
    }

    &[data-layout="topnav"] {
        .navbar-custom {
            margin: 0;
        }
    }
}
