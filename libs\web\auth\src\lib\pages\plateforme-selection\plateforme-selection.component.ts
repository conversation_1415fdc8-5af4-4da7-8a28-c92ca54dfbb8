import { Component, Element<PERSON><PERSON>, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { AuthService } from "@wph/core/auth";
import { AccesClient, AccesClientService, HasAccessService, PlateformeService, Principal, Societe } from "@wph/shared";
import { EventService, SelectedPlateforme } from "@wph/web/layout";
import { map, tap } from "rxjs";
import { CommandeService } from "@wph/commandes-web/commande";
import { DynamicColorPaletteService, BaseColorFournisseur, BaseColorFournisseurRGB, CodeSiteFournisseur } from "@wph/web/shared";
import { Offre, OffreCriteria, OffresService } from "@wph/data-access";
import { SIDEBAR_WIDTH_CONDENSED, SIDEBAR_WIDTH_FIXED } from "libs/web/layout/src/lib/shared/models/layout.model";
import { BlogPostDto } from "@wph/web/gestion-annonces";

@Component({
    selector: 'wph-plateforme-selection',
    templateUrl: './plateforme-selection.component.html',
    styleUrls: ['./plateforme-selection.component.scss']
})
export class PlateformeSelectionComponent implements OnInit, OnDestroy {
    winOffreOnly: boolean;
    userRole: string;
    user: Principal | null = null;
    listeAccesClient: AccesClient[] | null = null;
    selectedGrossiste: AccesClient | null = null;

    canAccessWinOffre: boolean;
    inheritedPosts: BlogPostDto[] = [];

    currentUser: Principal | null = null;
    listeOffres: Offre[] | null = null;

    get baseRGB() {
        return BaseColorFournisseurRGB;
    }

    // Blog-post related variables
    listeFournisseur: Societe[];

    @ViewChild('popFilterMsg') popFilterMsg: ElementRef;

    constructor(
        @Inject('ENVIROMENT') private env: any,
        private router: Router,
        private authService: AuthService,
        private eventService: EventService,
        private commandeService: CommandeService,
        private hasAccesService: HasAccessService,
        private offresService: OffresService,
        private plateformeService: PlateformeService,
        private accesClientService: AccesClientService,
        private colorPaletteService: DynamicColorPaletteService
    ) {
        this.canAccessWinOffre = !this.env?.production;
        this.currentUser = this.authService.getPrincipal();
    }

    ngOnInit(): void {
        this.plateformeService.clearPlateformeKeys(true);
        this.eventService.broadcast('refreshSideBar', true);

        this.user = this.authService.getPrincipal();
        this.userRole = this.authService.getUserRole(this.user);

        if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
            this.listeFournisseur = [this.authService.currentUser()?.societe];
        } else if (this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT', 'ROLE_AGENT_ACHAT'])) {
            this.getListeAccesClient();
        }

        if (
            this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']) ||
            this.authService.currentUser()?.societe?.typeEntreprise === 'FABRIQUANT'
        ) {
            this.winOffreOnly = true;
        }

        this.searchOffres();

        setTimeout(() => {
            this.eventService.broadcast('changeLeftSidebarType', SIDEBAR_WIDTH_CONDENSED);
        }, 200);
    }

    getListeAccesClient() {
        this.accesClientService.getListeAccesClient()
            .pipe(
                tap(listeAccesClient => {
                    const filteredListe = listeAccesClient?.filter(accesClient => !accesClient?.dateDesactivation);

                    this.listeFournisseur = filteredListe.map(accesClient => accesClient?.fournisseur);
                    // Liste des fournisseurs avec accès "WINOFFRE"
                    this.hasAccesService.setListeFournisseurAvecAcces(listeAccesClient);
                }),
                map((listeAccessClient) => listeAccessClient?.filter(accesClient => this.plateformeService.hasServiceWithCode('CW', accesClient)))
            )
            .subscribe(res => {
                this.listeAccesClient = res;
            });
    }

    setSelectedGrossiste(selected: AccesClient) {
        this.selectedGrossiste = selected;
        if (BaseColorFournisseur[this.selectedGrossiste?.fournisseur?.noeud?.codeSite]) {
            this.colorPaletteService.setBaseColor(BaseColorFournisseur[this.selectedGrossiste?.fournisseur?.noeud?.codeSite])
        } else {
            this.colorPaletteService.setBaseColor(BaseColorFournisseur[CodeSiteFournisseur.DEFAULT]);
        }
    }

    searchOffres() {
        this.offresService.searchOffres({ pageSize: 10, skip: 0 }, new OffreCriteria({ nonExpireesUniquement: 'O', statut: ['P'] })).subscribe(res => {
            if (res?.content?.length) {
                res.content.forEach((offre: Offre) => {
                    offre['canPlaceOrder'] = this.canPlaceOrder(offre);
                });
            }
            this.listeOffres = res?.content;
        });
    }

    canPlaceOrder(offre: Offre): boolean {
        let canPlaceOrder: boolean;

        if (this.currentUser?.societe?.typeEntreprise === 'FABRIQUANT') {
            const matched = offre?.distributeurs?.filter(
                (dist) => dist?.id === this.currentUser?.societe?.id
            );

            canPlaceOrder = !!matched?.length;
        } else if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
            canPlaceOrder = false;
        }
        else {
            canPlaceOrder = true;
        }

        return canPlaceOrder;
    }


    navigateToAccueil(targetPlateforme: SelectedPlateforme) {
        if (targetPlateforme === 'COMMANDE_WEB') {
            if (this.selectedGrossiste?.dateDesactivation) return;
            
            this.plateformeService.setCurrentPlateforme(targetPlateforme);
            
            const targetGrossiste = this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']) ?
            this.selectedGrossiste.fournisseur : this.selectedGrossiste;
            
            this.plateformeService.setCurrentGrossiste(targetGrossiste);
            
            this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT', 'ROLE_AGENT_ACHAT']) && this.getPanier();
            
            this.router.navigateByUrl('/commande-web/accueil');
        } else {
            this.plateformeService.setCurrentPlateforme(targetPlateforme);
            this.router.navigateByUrl('win-offre/accueil');
        }

        setTimeout(() => {
            this.offresService.changeEtatBar(SIDEBAR_WIDTH_FIXED);
            this.eventService.broadcast(
                'changeLeftSidebarType', SIDEBAR_WIDTH_FIXED);
            this.eventService.broadcast('refreshSideBar', true);
        });
    }

    getPanier(): void {
        this.commandeService.getPanier().subscribe(panier => {
            this.commandeService.panierChanged(panier);
        });
    }

    logout() {
        this.authService.logout();
        this.router.navigateByUrl('/auth/login');
    }

    ngOnDestroy(): void {
        this.eventService.broadcast('refreshSideBar', true);
    }
}
