import { <PERSON>rror<PERSON><PERSON><PERSON>, Inject, Injectable, Injector, NgZone } from "@angular/core";
import { HttpErrorResponse } from '@angular/common/http';

import { Router } from '@angular/router';
import { AlertService } from '@wph/shared';
import { AuthService } from "../services/auth.service";
import { ModalController, NavController } from "@ionic/angular";
import { StateModalComponent } from "@wph/mobile/shared";
import { Subject, throttleTime } from "rxjs";

@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
  private errors$: Subject<[string, number]> = new Subject<[string, number]>();
  // Error handling is important and needs to be loaded first.
  // Because of this we should manually inject the services with Injector.
  constructor(
    @Inject('ENVIROMENT') private envir: any,
    private injector: Injector,
    private navController: NavController
  ) {
    this.listenToErrorMessageStream();
  }

  async handleError(error: Error | HttpErrorResponse) {
    if (error instanceof HttpErrorResponse) {
      console.error('handleError HttpErrorResponse: ' + JSON.stringify(error));

      // ? Server Error stream propagation
      this.errors$.next([this.getServerMessage(error), error?.status]);
    } else {
      console.error('handleError ClientError: ' + error);
      console.log('Error StackTrace: ' + this.getClientStack(error));

      // ? Client Error stream propagation
      this.errors$.next([error?.message ? this.getClientMessage(error) : 'Une erreur est survenue', 0]);
    }
  }

  getClientMessage(error: Error): string {
    if (!navigator.onLine) {
      return 'No Internet Connection';
    }
    return error?.message ? error?.message : error?.toString();
  }

  getClientStack(error: Error): string {
    return error?.stack;
  }

  getServerMessage(error: HttpErrorResponse): string {
    const authService = this.injector.get(AuthService);

    if (error?.status === 401) {
      if (authService.getToken()) {
        return 'Votre session a expirée, merci de réauthentifier';
      } else {
        return error?.error?.message || '';
      }
    } else if (error?.status === 400 && error?.error?.message?.includes('auth')) {
      return error?.error?.message;
    }
    else if (error?.status === 0 || error?.status === 405 || error?.status >= 500) {
      return 'Serveur Indisponible';
    }
    else {
      return error?.error?.message || error?.error?.error;
    }
  }

  logoutAndRedirect() {
    const router = this.injector.get(Router);
    const ngZone = this.injector.get(NgZone);
    const authService = this.injector.get(AuthService);

    authService.logout();

    ngZone.run(() => {
      if (this.envir.platform === 'MOBILE') {
        this.navController.navigateRoot("/auth/login");
      } else {
        router.navigateByUrl("/auth/login");
      }
    });
  }

  async presentMessageModal(message: string, type: string) {
    const modalController = this.injector.get(ModalController);

    const alert = await modalController.create({
      cssClass: 'small-modal',
      component: StateModalComponent,
      componentProps: { message, type }
    });

    await alert.present();
  }

  listenToErrorMessageStream(): void {
    const alertService: AlertService = this.injector.get(AlertService);

    this.errors$.pipe(throttleTime(500)).subscribe(([message, status]) => {
      // ? Standard HTTP_ERROR_RESPONSE alert handling
      if (this.envir?.platform === 'WEB') {
        if (message?.includes('Unauthorized')) {
          this.logoutAndRedirect();
        }
        // ? Expired Session alert handling
        else if (message && (message?.includes('session') || message?.includes('réauthentifier'))) {
          this.logoutAndRedirect();
        }
        // ? Expired/Cached Chunk, ChunkLoadError alert handling
        else if (message?.includes('ChunkLoadError') && status !== 503) {
          try {
            window.location.reload();
          } catch (e) {
            window.location.assign(window.location.href);
          }
        }
        // ? Serveur Indisponible alert handling
        else if (
          message?.includes('Unknown') ||
          message?.includes('Serveur Indisponible') ||
          (message?.includes('ChunkLoadError') && status === 503)
        ) {
          alertService.serverUnavailable();
        } else {
          alertService.error(message || 'Une erreur est survenue', 'MODAL');
        }
      } else { // ? PLATFORM === MOBILE
        this.presentMessageModal(message || 'Une erreur est survenue', 'error');
      }
    });
  }
}
