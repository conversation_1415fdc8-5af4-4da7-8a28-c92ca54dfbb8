// libs\commandes-web\commande\src\lib\pages\reclamation\reclamations.component.scss

#list-nouveaux-produits {
  padding: 10px;

  .search {
    margin: 10px 0;
  }

  .header_search {
    display: grid;
    gap: 0.5rem;
    grid-template-columns: auto;
    grid-template-areas: "right";
    // height: 45px;
    justify-content: right;
  }

  // .left {
  //   grid-area: left;
  // }
  .right {
    grid-area: right;

    /*.form-inline {
      // transform: translateY(10px);
    }*/
  }

  //   kendo-grid {
  //     z-index: -1;
  //   }
}

.form-control  {
  color: black;
  font-size: 1rem;
  border-radius: var(--winoffre-base-border-radius);
  font-weight: 600;
}

.modal-footer {
  .btn  {
    color: black;
    font-size: 1rem;
    border-radius: var(--winoffre-base-border-radius);
    font-weight: 600;
  }
}

.input-group {
  .btn {
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-right-radius: var(--winoffre-base-border-radius);
  }
}

label {
  color: var(--winoffre-text-light-shade);
  font-weight: 600;
}

.picker-input {
  .form-control {
    border-radius: var(--winoffre-base-border-radius) !important;
  }
}


.circle-alt {
  width: 25px !important;
  line-height: 20px !important;
  height: 25px;
}
.circle {
  border-radius: 50%;
  display: inline-block;
  padding: 3px 3px;
  text-align: center !important;
  margin: auto 2px;
  line-height: 20px;
  font-size: 14px;
}

// Make grid rows clickable
.clickable-grid {
  tr {
      cursor: pointer;
      &:hover {
          background-color: rgba(0,0,0,.02);
      }
  }
}

// Style view modal
.reclamation-details {
  .info-group {

      label {
          font-weight: 600;
          color: #495057;
          margin-bottom: 0.5rem;
          display: block;
      }

      p {
          margin-bottom: 0;
          color: #212529;
      }
  }

  .message-content {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 1rem;
      white-space: pre-wrap;
  }

  .content-section {
      border-bottom: 1px solid #dee2e6;
      padding-bottom: 1rem;

      &:last-child {
          border-bottom: none;
      }
  }

  .badge {
      font-size: 0.875rem;
      padding: 0.4em 0.8em;
  }
}

::ng-deep .k-grid tbody tr {
  cursor: pointer;
  
  &:hover {
      background-color: rgba(0, 0, 0, 0.04);
  }
}

// ::ng-deep .k-grid {
//   tbody {
//       tr {
//           cursor: pointer;
//           transition: background-color 0.2s ease;
          
//           &:hover {
//               background-color: rgba(0, 0, 0, 0.04);
//           }

//           // Prevent hover effect on action buttons
//           .action-btns {
//               cursor: default;
//           }
//       }
//   }
// }


dl {
  margin: 0;
  padding: 0.5rem;
  position: relative; // For proper positioning of action buttons

  dt {
    margin-bottom: 0.5rem;

    &.limited-width {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap; // Allow wrapping
      width: calc(100% - 40px); // Adjusted width to fit within the container
      word-wrap: break-word; // Ensure text doesn't overflow
      
      span {
        color: #6c757d;
        margin-left: 0.5rem;
      }
    }

    &.action-btns {
      // position: absolute;
      // top: 0;
      // right: 0;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;

      button {
        margin: 0 5px;
      }
    }
  }
}


// Ensure grid items adapt for mobile
@media (max-width: 768px) {
  dl {
    dt {
      font-size: 14px;

      // &.action-btns {
      //   margin-top: -15vh; // Adjust for mobile screen proportions
      // }

      &.action-btns div{
        display: flex;
        // justify-content: space-evenly !important;
        align-items: center;
        
      }
      &.action-btns button{
        margin-bottom: 10px;
      }
      
    }
  }

  .circle {
    border-radius: 50%;
    display: inline-block;
    padding: 5px 7px;
    text-align: center !important;
    margin: auto 2px;
    line-height: 20px;
    font-size: 14px;
    width: 35px; // Ensure consistent size
    height: 35px;
  }

  .circle-alt {
    width: 35px !important;
    line-height: 25px !important;
    height: 35px;
  }

}
