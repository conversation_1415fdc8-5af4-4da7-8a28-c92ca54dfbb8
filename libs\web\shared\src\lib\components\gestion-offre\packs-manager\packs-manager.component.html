<ng-container *ngIf="!readOnly">
  <div class="row mt-3 mx-2 bg-transparent">
    <div [id]="(currentPlateforme$ | async) === 'WIN_OFFRE' ? 'pack-tab-nav-wo-pack' : 
    (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT' ? 'pack-tab-nav-pack-fs' : 'pack-tab-nav-pack'"
      class="col-12 card offrecmd bg-transparent">
      <ul ngbNav #nav="ngbNav" (activeIdChange)="onPackTabChange($event)" [(activeId)]="activeTabId"
        class="nav-tabs mb-0 bg-white" [ngClass]="{
          'card-border-cstm': (currentPlateforme$ | async) === 'WIN_GROUPE',
          'card-border-cstm-wo': (currentPlateforme$ | async) === 'WIN_OFFRE',
          'card-border-cstm-fs': (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT'
        }">

        <li [ngbNavItem]="ib + 1" *ngFor="let bloc of offre.listeBlocs; let ib=index; trackBy: trackItems">
          <a ngbNavLink (click)="checkSelectionBloc(true, bloc);" class="row d-flex h-100 m-0 align-items-center"
            style="gap: 8px; min-width: 250px">
            <span class="col m-0 p-0 d-flex align-items-center">
              <input (click)="checkSelectionBloc(true, bloc)" (blur)="(titreInput.readOnly = true);" type="text"
                placeholder="Saisir le titre" [ngClass]="{'pointer-cus': titreInput.readOnly}" [title]="bloc?.titre"
                class="bg-transparent modifiable-input" maxlength="100" [(ngModel)]="bloc.titre" readonly
                [readOnly]="titreInput.readOnly" #titreInput>
            </span>

            <span class="d-flex align-items-center" style="width: fit-content !important;">

              <ng-container *ngIf="offre?.etatProposant !== 'PUBLIER'">
                <i *ngIf="bloc?.titre?.length" class="char-count text-white mr-2">{{ bloc?.titre?.length || 0 }}/100</i>
                <i (click)="(titreInput.readOnly = false); titreInput.focus(); titreInput.select()"
                  class="bi bi-pencil-square pointer-cus"></i>
                <ng-container *ngIf="!offre.typeOffre">
                  <i (click)="dupliquerBloc({bloc, ib})" class="bi bi-copy mx-2 text-white pointer-cus"></i>
                  <i (click)="removeBloc(bloc, ib)" class="bi bi-trash text-white pointer-cus"></i>
                </ng-container>
              </ng-container>
            </span>
          </a>

          <ng-template ngbNavContent>
            <ngb-accordion #b="ngbAccordion" class="card-cus-pack mb-6 bg-white" activeIds="custom-panel-{{ib}}"
              id="custom-accordion-{{ib}}">
              <ngb-panel id="custom-panel-{{ib}}" class="mb-4 pb-4 bg-white">
                <ng-template ngbPanelHeader let-opened="opened" let-id="ib">
                  <wph-pack [bloc]="bloc" [selectedBlocElement]="selectedBlocElement" [readOnly]="readOnly"
                    [activeItemId]="activeItemId" [ib]="ib" (updateActiveItemId)="handleActiveItemIdChange($event)"
                    (pushNewBloc)="addBlock($event)" (blocRemoved)="handleBlocRemoval($event)"
                    (blocDuplicated)="dupliquerBloc($event)" (openProductModal)="openProduitModal()">
                  </wph-pack>
                </ng-template>

                <ng-template ngbPanelContent>
                  <ng-container *ngIf="existeFilsBlocsProduits(bloc)">
                    <wph-bloc-produit style="overflow-x: auto;" (produitSortChanged)="handleProduitSortChange($event)"
                      [data]="{ data: getListeFilsBlocsProduits(bloc), total: 0}"
                      [readOnly]="readOnly || offre?.etatProposant === 'PUBLIER'"
                      (blocRemoved)="handleBlocRemoval($event)"></wph-bloc-produit>
                  </ng-container>

                  <div class="row mx-1 mt-1">
                    <ng-container
                      *ngFor="let bloc of getListeFilsBlocsNonProduits(bloc); let blocGroupeIndex=index; trackBy: trackItems"
                      [ngTemplateOutlet]="blocGroupeTemplate"
                      [ngTemplateOutletContext]="{bloc: bloc, parentId: ib, blocGroupeIndex: blocGroupeIndex}">
                    </ng-container>
                  </div>
                </ng-template>
              </ngb-panel>
            </ngb-accordion>
          </ng-template>
        </li>

        <li *ngIf="!offre?.typeOffre && offre?.etatProposant !== 'PUBLIER'" [ngbNavItem]="999">
          <a ngbNavLink class="d-flex align-items-center"
            (click)="$event.preventDefault(); (selectedBlocElement = null); addBlock('P')">
            <strong class="d-block text-dark">
              <i class="mdi mdi-plus mdi-18px"></i>
              Ajouter
            </strong>
          </a>
        </li>
      </ul>
      <div class="w-auto"></div>
      <div [ngbNavOutlet]="nav" class="bg-white p-1 mx-0" [ngClass]="{
        'card-border-cstm': (currentPlateforme$ | async) === 'WIN_GROUPE',
        'card-border-cstm-wo': (currentPlateforme$ | async) === 'WIN_OFFRE',
        'card-border-cstm-fs': (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT'
      }"></div>
    </div>
  </div>

  <ng-template #blocGroupeTemplate let-bloc="bloc" let-parentId="parentId" let-blocGroupeIndex="blocGroupeIndex">
    <ng-container *ngIf="bloc">
      <div [id]="getSousBlocId(bloc?.typeBloc)" [ngClass]="{
          'card-border-cstm': (currentPlateforme$ | async) === 'WIN_GROUPE',
          'card-border-cstm-wo': (currentPlateforme$ | async) === 'WIN_OFFRE',
          'card-border-cstm-fs': (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT' 
        }" class="card bg-transparent card-radius w-100 groupe-tab-nav mb-2">
        <ul ngbNav #blocGroupeNav="ngbNav" class="nav-tabs bg-transparent">

          <li [ngbNavItem]="parentId + '-' + (blocGroupeIndex + 2)" [id]="bloc?.hash">
            <a ngbNavLink (click)="checkSelectionBloc(true, bloc)" class="row d-flex h-100 m-0 align-items-center"
              style="gap: 8px; min-width: 250px">
              <span class="col m-0 p-0 d-flex align-items-center">
                <input (click)="checkSelectionBloc(true, bloc)"
                  (blur)="!titreInput.readOnly && (titreInput.readOnly = true);"
                  [ngClass]="{'pointer-cus': titreInput.readOnly}" type="text" placeholder="Saisir le titre"
                  class="bg-transparent modifiable-input-block" [(ngModel)]="bloc.titre" readonly maxlength="100"
                  [title]="bloc?.titre" [readOnly]="titreInput.readOnly" #titreInput>
              </span>

              <span *ngIf="offre?.etatProposant !== 'PUBLIER'" class="d-flex align-items-center block">
                <i *ngIf="bloc?.titre?.length" class="char-count text-white mr-2">{{ bloc?.titre?.length || 0 }}/100</i>

                <i (click)="(titreInput.readOnly = false); titreInput.focus(); titreInput.select()"
                  class="bi bi-pencil-square pointer-cus"></i>
                <i (click)="dupliquerBloc({bloc, blocGroupeIndex})" class="bi bi-copy mx-2 pointer-cus"></i>
                <i (click)="removeBloc(bloc, parentId)" class="bi bi-trash pointer-cus"></i>
              </span>
            </a>

            <ng-template ngbNavContent>
              <ngb-accordion #b="ngbAccordion" class="card-cus-pack mb-6 bg-white"
                activeIds="custom-panel-{{parentId + '-' + blocGroupeIndex}}"
                id="custom-accordion-{{parentId + '-' + blocGroupeIndex}}">
                <ngb-panel id="custom-panel-{{parentId + '-' + blocGroupeIndex}}" class="mb-4 pb-4 bg-white">
                  <ng-template ngbPanelHeader let-opened="opened" let-id="blocGroupeIndex">
                    <ng-container *ngIf="!readOnly">
                      <wph-pack [bloc]="bloc" [selectedBlocElement]="bloc" [readOnly]="readOnly"
                        [activeItemId]="activeItemId" [ib]="blocGroupeIndex"
                        (updateActiveItemId)="handleActiveItemIdChange($event)" (pushNewBloc)="addBlock($event)"
                        (blocRemoved)="handleBlocRemoval($event)" (blocDuplicated)="dupliquerBloc($event)"
                        (openProductModal)="openProduitModal()">
                      </wph-pack>
                    </ng-container>
                  </ng-template>

                  <ng-template ngbPanelContent>
                    <ng-container *ngIf="existeFilsBlocsProduits(bloc)">
                      <wph-bloc-produit style="overflow-x: auto;"
                        [data]="{ data: getListeFilsBlocsProduits(bloc), total: 0}"
                        [readOnly]="readOnly || offre?.etatProposant === 'PUBLIER'"
                        (produitSortChanged)="handleProduitSortChange($event)"
                        (blocRemoved)="handleBlocRemoval($event)"></wph-bloc-produit>
                    </ng-container>

                    <ng-container
                      *ngFor="let bloc of getListeFilsBlocsNonProduits(bloc); let subBlockIndex=index; trackBy: trackItems"
                      [ngTemplateOutlet]="blocGroupeTemplate"
                      [ngTemplateOutletContext]="{bloc: bloc, parentId: parentId + '-' + blocGroupeIndex, blocGroupeIndex: subBlockIndex}">
                    </ng-container>
                  </ng-template>
                </ngb-panel>
              </ngb-accordion>
            </ng-template>
          </li>
        </ul>
        <div [ngbNavOutlet]="blocGroupeNav" class="p-1"></div>
      </div>
    </ng-container>
  </ng-template>
</ng-container>


<wph-recherche-produit [enableSousListeSearch]="true" [offre]="offre"
  (produitSelectionChange)="ajouterProduitsSelectionnes($event)"></wph-recherche-produit>