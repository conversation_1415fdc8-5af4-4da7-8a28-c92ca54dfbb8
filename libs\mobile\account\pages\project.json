{"name": "mobile-account-pages", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/mobile/account/pages/src", "prefix": "wph", "targets": {"test": {"executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/mobile/account/pages/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["libs/mobile/account/pages/**/*.ts", "libs/mobile/account/pages/**/*.html"]}}}, "tags": []}