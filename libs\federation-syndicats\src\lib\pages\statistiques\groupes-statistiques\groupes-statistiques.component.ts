import { Component, ElementRef, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';

import { BehaviorSubject, forkJoin, Subject, takeUntil } from 'rxjs';
import { ChartType } from 'chart.js';
import { FormControl } from '@angular/forms';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { AlertService } from '@wph/shared';
import { AuthService } from '@wph/core/auth';
import { GroupByField, StatisticsCriteriaDTO } from '../../../models/statistique.model';
import * as moment from 'moment';
import { FsStatistiqueService } from '../../../services/fs-statistique.service';
import { ChartOptions } from '@wph/web/shared';
import { CellClickEvent, GridDataResult } from '@progress/kendo-angular-grid';
import { Router } from '@angular/router';

@Component({
  selector: 'wph-groupes-statistiques',
  templateUrl: './groupes-statistiques.component.html',
  styleUrls: ['./groupes-statistiques.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class GroupesStatistiquesComponent implements OnInit, OnDestroy {
  private unsubscribe$ = new Subject<boolean>();
  @ViewChild('StatsLaboChart') StatsLaboChart: ElementRef<HTMLDivElement>;

  lineChartData: any[] = [];
  lineLaboChartData: any[] = [];
  pieLaboChartData: any = { labels: [], datasets: [] };
  public pieLaboChartType: ChartType = 'pie';
  statistiqueCriteria:StatisticsCriteriaDTO
  gridDataGroupe:GridDataResult = {total:0,data:[]}
  gridDataLabo:GridDataResult = {total:0,data:[]}
  laboTotalStats:any = {}

  pieChartData: any = {};
  displayedStartDate$ = new BehaviorSubject<Date>(new Date());
  displayedEndDate$ = new BehaviorSubject<Date>(new Date());
  startDate = new FormControl();
  endDate = new FormControl();
  now = new Date();
  datePicker = new FormControl();
  datePicker2 = new FormControl();
  minEndDate: NgbDateStruct | null = null;
  laboCriteria: 'GROUPE'|'INDIVIDUEL'|'TOUS' ="TOUS";

  chartsNoDaTA =  {
    text: 'Aucune donnée à afficher',
     offsetX: 0,
    offsetY: 0,
    style: {
      color: '#666',
      fontSize: '18px',
      fontFamily: 'Arial, sans-serif',
      background: '#f3f3f3',
      border: '1px solid #ccc',
      padding: '10px',
      borderRadius: '4px'
    }
  }


  chartOptions:ChartOptions;
  laboChartOptions:ChartOptions;

  public consommationColor = 'rgba(105,159,177,1)';
  public supporteColor = 'rgba(255,206,86,1)';

  public monthlyData: any = {
    '2023': {
      consommé: [50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160],
      supporté: [30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140],
    },
    '2024': {
      consommé: [65, 59, 80, 81, 56, 55, 40, 70, 90, 100, 110, 120],
      supporté: [28, 48, 40, 19, 86, 27, 90, 60, 70, 80, 90, 100],
    },
  };

  public laboData: Record<string, number> = {
    'Bottu': 220,
    'Laprophan': 150,
    'Medirep': 50,
    'Sophaca_Mar': 170,
    'Tadlapharm':  100
  };

  public laboLabels: string[] = ['Bottu', 'Laprophan', 'Medirep', 'Sophaca_Mar', 'Tadlapharm'];

  public laboColors: any = {
    'Bottu': 'rgba(93, 57, 181, 1)',
    'Laprophan': 'rgba(72, 219, 251, 1)',
    'Medirep': 'rgba(255, 99, 132, 1)',
    'Sophaca_Mar': 'rgba(255, 159, 64, 1)',
    'Tadlapharm': 'rgba(75, 192, 192, 1)'
  };

  public lineChartGroupes: string[] = [
    'Groupe 1',
    'Groupe 2',
    'Groupe 3',
    'Groupe 4',
    'Groupe 5',
  ];
  public lineChartOptions = {
    responsive: true,
    scales: {
      x: {
        title: {
          display: true,
          text: 'Groupes',
        },
      },
      y: {
        title: {
          display: true,
          text: 'Valeurs en Dh',
        },
      },
    },
  };

  public lineLaboChartOptions = {
    responsive: true,
    scales: {
      x: {
        title: {
          display: true,
          text: 'Labos',
        },
      },
      y: {
        title: {
          display: true,
          text: 'Montant en Dh',
        },
      },
    },
  };
  public lineChartColors: Array<any> = [
    {
      backgroundColor: 'rgba(93, 57, 181, 0.2)',
      borderColor: 'rgba(93, 57, 181, 1)',
      pointBackgroundColor: 'rgba(93, 57, 181, 1)',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#fff',
      pointHoverBorderColor: 'rgba(93, 57, 181, 0.8)',
    },
    {
      backgroundColor: 'rgba(72, 219, 251, 0.2)',
      borderColor: 'rgba(72, 219, 251, 1)',
      pointBackgroundColor: 'rgba(72, 219, 251, 1)',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#fff',
      pointHoverBorderColor: 'rgba(72, 219, 251, 0.8)',
    },
  ];
  public lineChartLegend = true;
  public lineChartType: ChartType = 'line';

  public pieChartLabels: string[] = ['Consommé', 'Supporté'];
  public pieChartType: ChartType = 'pie';
  public pieChartOptions: any = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: (tooltipItem: any) => {
            const value = tooltipItem.raw;
            const total = this.pieChartData.datasets[0].data.reduce(
              (a, b) => a + b,
              0
            );
            const percentage = ((value / total) * 100).toFixed(2) + '%';
            return `${
              this.pieChartLabels[tooltipItem.dataIndex]
            }: ${percentage}`;
          },
        },
      },
    },
  };
  public pieChartColors: Array<any> = [
    {
      backgroundColor: ['rgba(93, 57, 181, 0.8)', 'rgba(72, 219, 251, 0.8)'],
      borderColor: ['rgba(93, 57, 181, 1)', 'rgba(72, 219, 251, 1)'],
      hoverBackgroundColor: ['rgba(93, 57, 181, 1)', 'rgba(72, 219, 251, 1)'],
      hoverBorderColor: ['rgba(93, 57, 181, 1)', 'rgba(72, 219, 251, 1)'],
    },
  ];

  public pieLaboChartOptions: any = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: (tooltipItem: any) => {
            const value = tooltipItem.raw;
            const total = this.pieLaboChartData.datasets[0].data.reduce(
              (a: number, b: number) => a + b,
              0
            );
            const percentage = ((value / total) * 100).toFixed(2) + '%';
            return `${
              this.pieLaboChartData.labels[tooltipItem.dataIndex]
            }: ${percentage}`;
          },
        },
      },
    },
  };
  public pieLaboChartColors: Array<any> = [];
  constructor(private alertService: AlertService,private router:Router,private fsStatistiqueService :FsStatistiqueService) {
  }

  natureCommandeMapping(naturecommande:typeof this.laboCriteria) {
    switch (naturecommande) {
      case "GROUPE":
        return "AG";
      case "INDIVIDUEL":
        return "I";
      default:
        return null;
    }
  }

  consultCommandeByType(event :CellClickEvent){
    const {dataItem} = event;
  const payload = {
    idLabo:  dataItem.offreur.id,
    natureCommande:this.natureCommandeMapping(this.laboCriteria),
    dateDebut:moment(this.displayedStartDate$.value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    dateFin: moment(this.displayedEndDate$.value).endOf('month').format('YYYY-MM-DD HH:mm:ss')
  }
  console.log(payload);
  this.router.navigate(['/achats-groupes/commandes/liste/commandes/admin'], {queryParams: payload});
  }

  exportStatsExcel() {
    this.statistiqueCriteria = new StatisticsCriteriaDTO({
      dateDebut: moment(this.displayedStartDate$.value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      dateFin: moment(this.displayedEndDate$.value).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      listGroupByfields:[GroupByField.ADMIN]
    });

    this.fsStatistiqueService.getStatictiquesExecl(this.statistiqueCriteria).subscribe(
      (response) => {
        // The response is already a Blob, so we can use it directly
        const url = window.URL.createObjectURL(response.body);
        const link = document.createElement('a');
        link.href = url;

        // Get the filename from the Content-Disposition header if possible
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = 'rapport_achat_groupe.xlsx';
        if (contentDisposition) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches != null && matches[1]) {
            filename = matches[1].replace(/['"]/g, '');
          }
        }
        link.download = filename;

        // Append to body, click, and clean up
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Free up resources
        window.URL.revokeObjectURL(url);
      },
      (error) => {
        console.error('Error downloading the file:', error);
        // Handle the error (e.g., show a message to the user)
      }
    );
  }


  // getMyStatistique(): void {

  //   this.statistiqueCriteria = new StatisticsCriteriaDTO({
      // listGroupByfields: [GroupByField.GROUP],
      // dateDebut: moment(this.displayedStartDate$.value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      // dateFin: moment(this.displayedEndDate$.value).endOf('month').format('YYYY-MM-DD HH:mm:ss'),

  //   });
  //   this.fsStatistiqueService.getStatictiques(this.statistiqueCriteria).subscribe(res => {
  //     this.lineChartGroupes = [
  //       ...res.map(stat=>stat.groupe.raisonSociale)
  //     ]

  //      const montantSupporter = res.map(item => item.montantSupportee ?? 0);


  //     this.lineChartData = [
  //        {data:montantSupporter,label:"Montant Depensé"},
  //      ]

  //      this.pieChartData =  {
  //       labels: [
  //         ...res.map(stat=>stat.groupe.raisonSociale)
  //       ],
  //       datasets: [
  //         {
  //           data: [
  //             ...montantSupporter
  //           ]
  //         }
  //       ]
  //     };


  //   });






  //   this.getMyStatistiqueLabo();

  // }


  getMyStatistique(): void {
   this.statistiqueCriteria = new StatisticsCriteriaDTO({
    listGroupByfields: [GroupByField.GROUP],
    dateDebut: moment(this.displayedStartDate$.value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    dateFin: moment(this.displayedEndDate$.value).endOf('month').format('YYYY-MM-DD HH:mm:ss'),

   });
   this.fsStatistiqueService.getStatictiques(this.statistiqueCriteria).subscribe(res => {
     res.map(ligne=>{
       const startDate = moment(this.displayedStartDate$.value).startOf('month');
       const endDate = moment(this.displayedEndDate$.value).endOf('month');
       ligne.periode = startDate.isSame(endDate, 'month') ? startDate.format('MM/YYYY') : `${startDate.format('MM/YYYY')} - ${endDate.format('MM/YYYY')}`;
       ligne.balance = ligne.balance ?? 0;
       ligne.montantConsomme = ligne.montantConsomme ?? 0;
       ligne.montantSupportee = ligne.montantSupportee ?? 0;
     })
     this.gridDataGroupe.data = res;
     this.gridDataGroupe.total = res.length;
     this.chartOptions = {

       series: [
        //  {
        //    name: "Consome",
        //    data: res.map(stat=>stat.montantConsomme ?? 0)
        //  },
         {
           name: "Mnt Livré",
           data: res.map(stat=>stat.montantSupportee ?? 0)
         },
        //  {
        //    name: "Balance",
        //    data: res.map(stat=>stat.balance ?? 0)
        //  }
       ],
       chart: {
         type: "bar",
         height: 350,
         foreColor: "#000000",
         zoom:{
           enabled: true
         },
         toolbar:{
           show:true,
           autoSelected:"zoom",
           tools:{
             zoomin:true
           }

         }

       },
       plotOptions: {
         bar: {
           horizontal: false,
           columnWidth: "10%",
           borderRadius: 30,

           // endingShape: "rounded"
         }
       },
       dataLabels: {
         enabled: false,
         background:{
           foreColor: "#000000"
         }
       },
       stroke: {
         show: true,
         width: 2,
         colors: ["transparent"]
       },
       xaxis: {
         categories: res.map(item => item.groupe.raisonSociale),
         tickPlacement: "between",
         tickAmount: 20,
         range: 3,

       },
       yaxis: [
{
        tickAmount:8,
           title: {
             text: "Montant en DH",
             style: {
               color: "#000000"
             }
           }}

       ],
       fill: {
         opacity: 1
       },
       tooltip: {
         y: {
           formatter: function(val) {
             return  val + " DH";
           }
         }
       },
       grid:{},
       legend:{},
       annotations: {}
     };
   });
   this.getMyStatistiqueLabo();

 }

 getMyStatistiqueLabo(): void {
  this.statistiqueCriteria = new StatisticsCriteriaDTO({
    listGroupByfields: [GroupByField.OFFREUR,GroupByField.PHARMACIEN,GroupByField.ADMIN],
    dateDebut: moment(this.displayedStartDate$.value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    dateFin: moment(this.displayedEndDate$.value).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
  });


  if (this.laboCriteria === 'INDIVIDUEL') {
    this.fetchAndProcessData(true);
  } else if (this.laboCriteria === 'GROUPE') {
    this.fetchAndProcessData(false);
  } else {
    // TOUS: fetch both and combine
    forkJoin({
      individual: this.fsStatistiqueService.getStatictiques(this.statistiqueCriteria, true),
      group: this.fsStatistiqueService.getStatictiques(this.statistiqueCriteria)
    }).subscribe(({ individual, group }) => {
      const combinedData = this.combineAndSumData(individual, group);
      this.processData(combinedData);
    });
  }
}

private fetchAndProcessData(isIndividual: boolean): void {
  this.fsStatistiqueService.getStatictiques(this.statistiqueCriteria, isIndividual).subscribe(res => {
    const combinedData = this.combineAndSumData(res, []);
    this.processData(combinedData);
  });
}

private processData(res: any[]): void {
  res.map(ligne => {
    const startDate = moment(this.displayedStartDate$.value).startOf('month');
    const endDate = moment(this.displayedEndDate$.value).endOf('month');
    ligne.periode = startDate.isSame(endDate, 'month') ? startDate.format('MM/YYYY') : `${startDate.format('MM/YYYY')} - ${endDate.format('MM/YYYY')}`;
    ligne.balance = ligne.balance ?? 0;
    ligne.montantConsomme = ligne.montantConsomme ?? 0;
    ligne.montantSupportee = ligne.montantSupportee ?? 0;
  });

  this.gridDataLabo.data = res;
  this.gridDataLabo.total = res.length;
  const montantSupporte = res.map(item => item.montantSupportee ?? 0);
  const montantConsomme = res.map(item => item.montantConsomme ?? 0);
  const totalMontantSupporte = montantSupporte.reduce((a, b) => a + b, 0);
  const totalMontantConsomme = montantConsomme.reduce((a, b) => a + b, 0);
  const totaleCommandes = res.reduce((a, b) => a + b.countCmdPasse, 0);
  const totalRemise =  totalMontantSupporte - totalMontantConsomme;
  const periode = res[0]?.periode;
  this.laboTotalStats = {
    totalMontantSupporte,
    totalMontantConsomme,
    totaleCommandes,
    totalRemise,
    periode
  }
  this.updateChartOptions(res, montantSupporte,montantConsomme);
}

private combineAndSumData(individual: any[], group: any[]): any[] {
  const combinedMap = new Map();
  const processData = (data: any[]) => {
    data.forEach(item => {
      const key = `${item.offreur.raisonSociale}-${item.periode}`;
      if (!combinedMap.has(key)) {
        combinedMap.set(key, { ...item });
      } else {
        const existing = combinedMap.get(key);
        existing.balance += item.balance ?? 0;
        existing.montantConsomme += item.montantConsomme ?? 0;
        existing.montantSupportee += item.montantSupportee ?? 0;
        existing.countCommandeConsommee += item.countCommandeConsommee ?? 0;
        existing.countCommandeSupportee += item.countCommandeSupportee ?? 0;
        existing.countCmdPasse += item.countCmdPasse ?? 0;
        existing.countClient += item.countClient ?? 0;
      }
    });
  };

  processData(individual);
  processData(group);

  return Array.from(combinedMap.values());
}

private updateChartOptions(res: any[], montantSupporte: number[], montantConsomme: number[] = []): void {
  this.laboChartOptions = {
    chart: {
      type: "bar",
      height: "300px",
      foreColor: "#000000",
      zoom: { enabled: true },
      toolbar: {
        show: true,
        autoSelected: "zoom",
        tools: { zoomin: true }

      }
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "30%",
        borderRadius: 10,

      }
    },
    dataLabels: { enabled: false },
    stroke: {
      show: true,
      width: 2,
      colors: ["transparent"]
    },
    series: [
      {
        name: "Mnt Net",
        data: montantConsomme
      }
    ],
    xaxis: {
      tickPlacement: "between",
      categories: res.map(item => {
        const label = `${item.offreur.raisonSociale} (${item.countCmdPasse} cmd)`;
        return label.length > 20 ? label.substring(0, 17) + "..." : label; // Truncate long labels
      }),
      labels: {
        style: {
          fontSize: "12px"
        },
        rotate: -45, // Rotate labels for better fit
        trim: true
      }
    },
    yaxis: [
      {

        tickAmount: 10,
        title: {
          text: "Montant en DH",
          style: { color: "#000000" }
        }

      }
    ],
    fill: { opacity: 1 },
    tooltip: {
      y: {
        formatter: (val) => `${val} DH`
      }
    },
    grid: {},
    legend: {},
    annotations: {},
    responsive: [
      {
        breakpoint: 600, // For small screens
        options: {
          chart: {
            height: 300
          },
          xaxis: {
            labels: {
              rotate: -45, // Rotate more aggressively
              style: {
                fontSize: "10px"
              }
            }
          },
          plotOptions: {
            bar: {
              columnWidth: "30%" // Make bars thinner for small screens
            }
          }
        }
      }
    ]
  };
}


  ngOnInit(): void {
    this.initializeDefaultDates();
    this.setFormControlValues();
    this.updateChartData(this.displayedStartDate$.value, this.displayedEndDate$.value);
    this.listenToDatePickerChanges();
    this.calculateLaboData();
    this.getMyStatistique();
  }


  changeLaboCriteria(labo:'GROUPE'|'INDIVIDUEL'|'TOUS'){
    if(labo === this.laboCriteria){
      return;
    }
    this.laboCriteria = labo;
    this.getMyStatistiqueLabo()
    setTimeout(() => {
      this.StatsLaboChart.nativeElement?.scrollIntoView();
      }, 200);

   }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  initializeDefaultDates(): void {
    const defaultStartDate = new Date();
    defaultStartDate.setMonth(defaultStartDate.getMonth() - 6); // 6 months ago
    const defaultEndDate = new Date();

    this.displayedStartDate$.next(defaultStartDate);
    this.displayedEndDate$.next(defaultEndDate);
  }

  setFormControlValues(): void {
    const startDateValue = this.displayedStartDate$.value;
    const endDateValue = this.displayedEndDate$.value;

    this.startDate.setValue({
      year: startDateValue.getFullYear(),
      month: startDateValue.getMonth() + 1,
      day: startDateValue.getDate()
    });

    this.endDate.setValue({
      year: endDateValue.getFullYear(),
      month: endDateValue.getMonth() + 1,
      day: endDateValue.getDate()
    });
  }

  forwardOneMonth(picker: 'start' | 'end'): void {
    if (picker === 'start') {
      const newDate = new Date(this.displayedStartDate$.value);
      newDate.setMonth(newDate.getMonth() + 1);
      this.displayedStartDate$.next(newDate);
      this.setFormControlValues();
    } else if (picker === 'end') {
      const newDate = new Date(this.displayedEndDate$.value);
      newDate.setMonth(newDate.getMonth() + 1);
      this.displayedEndDate$.next(newDate);
      this.setFormControlValues();
    }
    this.getMyStatistique()
  }

  backwardOneMonth(picker: 'start' | 'end'): void {
    if (picker === 'start') {
      const newDate = new Date(this.displayedStartDate$.value);
      newDate.setMonth(newDate.getMonth() - 1);
      this.displayedStartDate$.next(newDate);
      this.setFormControlValues();
    } else if (picker === 'end') {
      const newDate = new Date(this.displayedEndDate$.value);
      newDate.setMonth(newDate.getMonth() - 1);
      this.displayedEndDate$.next(newDate);
      this.setFormControlValues();
    }
    this.getMyStatistique()
  }

  validateDatePeriod(): void {
    const startDate = this.startDate.value
      ? new Date(
          this.startDate.value.year,
          this.startDate.value.month - 1,
          this.startDate.value.day
        )
      : null;
    const endDate = this.endDate.value
      ? new Date(
          this.endDate.value.year,
          this.endDate.value.month - 1,
          this.endDate.value.day
        )
      : null;

    if (startDate && endDate && startDate > endDate) {
      this.alertService.error(
        'La date de début ne peut pas être postérieure à la date de fin',
        'MODAL'
      );
      return;
    }

    this.displayedStartDate$.next(startDate);
    this.displayedEndDate$.next(endDate);

    this.updateChartData(startDate, endDate);
  }

  updateChartData(startDate: Date, endDate: Date): void {
    if (!startDate || !endDate) {
      this.lineChartData = [];
      this.pieChartData = { labels: [], datasets: [] };
      this.lineLaboChartData = [];
      this.pieLaboChartData = { labels: [], datasets: [] };
      return;
    }

    // Calculate the period length based on the date range
    const periodLength = this.getMonthsBetween(startDate, endDate).length;

    // Generate fake data for the given date range
    const consomméData = this.generateFakeData(this.lineChartGroupes.length);
    const supportéData = this.generateFakeData(this.lineChartGroupes.length);

    // Update the line chart data for groups
    this.lineChartData = [
      {
        data: consomméData,
        label: 'Consommé',
        borderColor: 'rgba(105,159,177,1)',
        backgroundColor: 'rgba(105,159,177,0.2)',
        pointBackgroundColor: 'rgba(105,159,177,1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(105,159,177,0.8)'
      },
      {
        data: supportéData,
        label: 'Supporté',
        borderColor: 'rgba(255,206,86,1)',
        backgroundColor: 'rgba(255,206,86,0.2)',
        pointBackgroundColor: 'rgba(255,206,86,1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(255,206,86,0.8)'
      }
    ];

    // Calculate totals for the pie chart
    const totalConsommé = consomméData.reduce((a: number, b: number) => a + b, 0);
    const totalSupporté = supportéData.reduce((a: number, b: number) => a + b, 0);

    // Update the pie chart data
    this.pieChartData = {
      labels: ['Consommé', 'Supporté'],
      datasets: [{
        data: [totalConsommé, totalSupporté],
        backgroundColor: ['rgba(105,159,177,0.8)', 'rgba(255,206,86,0.8)'],
        hoverBackgroundColor: ['rgba(105,159,177,1)', 'rgba(255,206,86,1)'],
        borderColor: ['rgba(105,159,177,1)', 'rgba(255,206,86,1)'],
        hoverBorderColor: ['rgba(105,159,177,1)', 'rgba(255,206,86,1)']
      }]
    };

    // Update the labo data
    this.calculateLaboData();
  }


  getMonthsBetween(startDate: Date, endDate: Date): string[] {
    const months = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    start.setDate(1);
    end.setDate(1);

    while (start <= end) {
      months.push(start.toLocaleString('default', { month: 'long', year: 'numeric' }));
      start.setMonth(start.getMonth() + 1);
    }

    return months;
  }

  generateFakeData(length: number): number[] {
    return Array.from({ length }, () => Math.floor(Math.random() * 100));
  }

  listenToDatePickerChanges(): void {
    this.datePicker.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(res => {
        const selectedDate = new Date(res);

        if (
          (selectedDate?.getMonth() <= this.now?.getMonth()) ||
          (selectedDate?.getFullYear() !== this.now?.getFullYear())
        ) {
          this.displayedStartDate$.next(selectedDate);
          this.updateChartData(this.displayedStartDate$.value, this.displayedEndDate$.value);
        }
      });

    this.datePicker2.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(res => {
        const selectedDate = new Date(res);

        if (
          (selectedDate?.getMonth() <= this.now?.getMonth()) ||
          (selectedDate?.getFullYear() !== this.now?.getFullYear())
        ) {
          this.displayedEndDate$.next(selectedDate);
          this.updateChartData(this.displayedStartDate$.value, this.displayedEndDate$.value);
        }
      });
  }

  calculateLaboData(): void {
    // Get the maximum length of data points for x-axis
    const dataLength = this.laboLabels.length;

    const datasets = this.laboLabels.map((labo, index) => {
      const data = Array(dataLength).fill(null);
      data[index] = this.laboData[labo];

      return {
        label: labo,
        data: data, // Each labo should have its data placed at the correct x-axis index
        fill: false,
        borderColor: this.laboColors[labo],
        backgroundColor: this.laboColors[labo],
        pointBackgroundColor: this.laboColors[labo],
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: this.laboColors[labo],
        Width: 20,
        tension: 0.1
      };
    });

    this.lineLaboChartData = datasets;

    // Calculate total consumption and percentages for the pie chart
    const totalConsumption = Object.values(this.laboData).reduce((sum, value) => sum + (value as number), 0);
    const percentages = Object.values(this.laboData).map(value => ((value as number) / totalConsumption) * 100);

    // Prepare the pie chart data
    this.pieLaboChartData = {
      labels: this.laboLabels,
      datasets: [{
        data: percentages,
        backgroundColor: this.laboLabels.map(labo => this.laboColors[labo]),
        hoverBackgroundColor: this.laboLabels.map(labo => this.laboColors[labo])
      }]
    };

    // Prepare pie chart colors
    this.pieLaboChartColors = [{
      backgroundColor: this.laboLabels.map(labo => this.laboColors[labo]),
      borderColor: this.laboLabels.map(labo => this.laboColors[labo]),
      hoverBackgroundColor: this.laboLabels.map(labo => this.laboColors[labo]),
      hoverBorderColor: this.laboLabels.map(labo => this.laboColors[labo])
    }];
  }
}
