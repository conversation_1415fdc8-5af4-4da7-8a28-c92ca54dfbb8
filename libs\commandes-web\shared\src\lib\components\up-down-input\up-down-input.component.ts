import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
    selector: 'up-down-input',
    templateUrl: './up-down-input.component.html',
    styleUrls: ['./up-down-input.component.scss'],
})
export class UpDownInputComponent {
    @Input('width') width: string = 37 + 'px';
    @Output('value') value = new EventEmitter<{ id: string; number: number }>();
    @Output('sub') isSub = new EventEmitter<boolean>();

    data: { id: string; number: number } = { id: '', number: 1 };

    _targetID: string;
    _stockNumber: number;

    @Input('start') set stockNumber(value: number) {
        this._stockNumber = value;
        this.data.number = value;
    }
    get stockNumber(): number {
        return this._stockNumber;
    }

    @Input('targetID') set targetID(value: string) {
        this._targetID = value;
        this.data.id = value;
    }

    get targetID(): string {
        return this._targetID;
    }

    constructor() {}

    up() {
        this.stockNumber >= 9999 ? (this.stockNumber = 9999) : this.stockNumber++;
        this.data.number = this.stockNumber;
        this.value.emit(this.data);
    }

    down() {
        this.stockNumber > 1 ? this.stockNumber-- : (this.stockNumber = 1);
        this.data.number = this.stockNumber;
        this.value.emit(this.data);
    }

    checkKeys(e: any): boolean {
        const target = e.keyCode | e.which | e.charCode;
        const check = [101, 69, 43, 44, 45, 46].includes(target) ? false : true;
        if (check && target == 13) {
            this.sub();
        }
        return check;
    }

    onSearchChange(val): void {
        const regex = new RegExp('^[0-9]+$');

        if (regex.test(String(val))) {

            const qte = Number(val);
            if (qte <= 0) {
                this.data.number = 1;
                this.stockNumber = 1;
            } else if (qte >= 9999) {
                this.data.number = 9999;
                this.stockNumber = 9999;
            } else {
                this.data.number = qte;
            }
        } else {
            this.data.number = 1;
            this.stockNumber = 1;
        }

        this.value.emit(this.data);
    }

    sub() {
        this.isSub.emit(true);
    }
}
