import { CommonModule } from "@angular/common";
import { CUSTOM_ELEMENTS_SCHEMA, Component, ElementRef, Input, ViewChild } from "@angular/core";
import { YouTubePlayerModule } from "@angular/youtube-player";
import { WebSharedModule } from "@wph/web/shared";
import { BlogPostDto, ContentItem } from "../../models/BlogPost.interface";
import Swiper from "swiper";
import { PosteService } from "../../services/poste.service";

@Component({
    selector: "wph-complex-news-card",
    templateUrl: "./complex-news-card.component.html",
    styleUrls: ["./complex-news-card.component.scss"],
    standalone: true,
    imports: [WebSharedModule, CommonModule, YouTubePlayerModule],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ComplexNewsCardComponent {
    @Input() set post(value: BlogPostDto) {
        this._post = value;
    }

    get post(): BlogPostDto | null {
        return this._post;
    }
    @Input() maxTextLength: number = 200;

    isTextExpanded: boolean = false;
    isSharing: boolean = false;

    @ViewChild('swiperRef', { static: false }) swiperRef: ElementRef;

    _post: BlogPostDto | null = null;
    youtubePlayerVars: YT.PlayerVars;

    constructor(private posteService: PosteService) {
        this.youtubePlayerVars = this.posteService.youtubePlayerVars;
     }

    get hasMultipleContent(): boolean {
        return (this.post?.contentItems?.length || 0) > 1;
    }

    get truncatedText(): string {
        if (!this.post?.sujet) return '';

        if (this.isTextExpanded || (this.post.sujet?.length || 0) <= this.maxTextLength) {
            return this.post.sujet;
        }

        return this.post.sujet.substring(0, this.maxTextLength) + '...';
    }

    get shouldShowReadMore(): boolean {
        return (this.post?.sujet?.length || 0) > this.maxTextLength && !this.isTextExpanded;
    }

    get shouldShowReadLess(): boolean {
        return (this.post?.sujet?.length || 0) > this.maxTextLength && this.isTextExpanded;
    }

    get totalSlides(): number {
        return this.post?.contentItems?.length || (this.hasAnyContent ? 1 : 0);
    }

    get hasAnyContent(): boolean {
        return !!(this.post?.imageUrl || this.post?.videoUrl || this.post?.contentItems?.length);
    }

    ngOnInit() {
        if (!this.post?.contentItems?.length && (this.post?.imageUrl || this.post?.videoUrl)) {
            this.post.contentItems = [];

            if (this.post.videoUrl) {
                this.post.contentItems.push({
                    type: 'VIDEO',
                    url: this.post.videoUrl
                });
            }

            if (this.post.imageUrl) {
                this.post.contentItems.push({
                    type: 'IMAGE',
                    url: this.post.imageUrl
                });
            }
        }
    }

    toggleTextExpansion(): void {
        this.isTextExpanded = !this.isTextExpanded;
    }

    async sharePost(): Promise<void> {
        if (!this.post?.url && !this.post?.imageUrl && !this.post?.videoUrl) return;

        this.isSharing = true;

        let videoUrl = this.post?.videoUrl ? this.post.videoUrl : null;

        if (videoUrl) {
            videoUrl = `https://www.youtube.com/watch?v=${videoUrl}`;
        }

        try {
            if (navigator.share) {
                await navigator.share({
                    title: this.post.titre || 'Découvrez cette publication',
                    text: this.post.sujet ? this.truncatedText.substring(0, 100) + '...' : '',
                    url: (this.post.url || this.post?.imageUrl || videoUrl) as string
                });
            } else {
                // Fallback to clipboard
                await navigator.clipboard.writeText((this.post.url || this.post?.imageUrl || videoUrl) as string);
                console.log('Lien copié dans le presse-papier !');
            }
        } catch (error) {
            console.error('Erreur lors du partage:', error);
        } finally {
            setTimeout(() => {
                this.isSharing = false;
            }, 1000);
        }
    }

    getCompanyName(): string {
        return this.post?.createur?.entrepriseDTO?.raisonSociale || '';
    }

    isVideoContent(content: ContentItem | null): boolean {
        return content?.type === 'VIDEO';
    }

    isImageContent(content: ContentItem | null): boolean {
        return content?.type === 'IMAGE';
    }

    onImageError(event: Event): void {
        const img = event.target as HTMLImageElement;
        img.style.display = 'none';
    }

    onVideoError(event: Event): void {
        const video = event.target as HTMLVideoElement;
        video.style.display = 'none';
    }

    toggleSwiperAutoplay() {
        const swiperInstance: Swiper = this.swiperRef?.nativeElement?.swiper;

        if (swiperInstance) {
            if (swiperInstance?.autoplay?.running) {
                swiperInstance?.autoplay?.stop();
            } else {
                swiperInstance?.autoplay?.start();
            }
        }
    }

    redirectToUrl() {
        if (this.post?.url || this.post?.imageUrl) {
            window.open((this.post?.url || this.post?.imageUrl) as string, '_blank');
        }
    }
}