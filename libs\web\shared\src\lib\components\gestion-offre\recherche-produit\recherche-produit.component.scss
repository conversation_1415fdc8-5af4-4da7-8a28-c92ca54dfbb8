#wo-recherche-produit {
  ::ng-deep .k-grid.fs-grid tr:hover {
      background-color: var(--wo-primary-50) !important;
  }

  ::ng-deep .k-grid.fs-grid .k-grid-header .k-header {
      border: none;
      background-color: var(--wo-primary-400) !important;
      color: #fff;
      padding-block: 12px;

      &:hover {
          background-color: var(--wo-primary-50);
      }
  }

  ::ng-deep .k-grid-content {
    scrollbar-width: thin !important;
    //FOR MOZILLA BROWSER
    scrollbar-color: var(--wo-primary-400) white !important;
    overflow-y: auto !important;
    border-radius: var(--winoffre-base-border-radius);
  }

  ::ng-deep .k-grid tr.k-alt {
    background-color: var(--wo-primary-50) !important;
  }

  ::ng-deep .fs-listing-grid{
    tr td{
      border-left: 0.2px solid  var(--wo-primary-100) !important;
      border-bottom: 0.2px solid  var(--wo-primary-10) !important;
    }
  }

  ::ng-deep .k-checkbox:checked {
    background-color: var(--wo-primary-300) !important;
    color: white !important;
    border-color: var(--wo-primary-200) !important;
  }
}

#fs-recherche-produit {
  ::ng-deep .k-grid.fs-grid tr:hover {
      background-color: var(--fs-primary-50) !important;
  }

  ::ng-deep .k-grid.fs-grid .k-grid-header .k-header {
      border: none;
      background-color: var(--fs-grid-primary) !important;
      color: #fff;
      padding-block: 12px;

      &:hover {
          background-color: var(--fs-primary-50);
      }
  }

  ::ng-deep .k-grid-content {
    scrollbar-width: thin !important;
    //FOR MOZILLA BROWSER
    scrollbar-color: var(--fs-grid-primary) white !important;
    overflow-y: auto !important;
    border-radius: var(--winoffre-base-border-radius);
  }

  ::ng-deep .k-grid tr.k-alt {
    background-color: #f3f6fa !important;
  }

  ::ng-deep .fs-listing-grid{
    tr td{
      border-left: 0.2px solid  #e9edf6 !important;
      border-bottom: 0.2px solid  #e9edf6 !important;
    }
  }

  ::ng-deep .k-checkbox:checked {
    background-color: #bec7e3 !important;
    color: white !important;
    border-color: #bec7e3 !important;
  }
}

.file-img {
  width: 65px;
  height: 65px;

  object-fit: contain;
  object-position: center;
}

.img-cnt-dashed {
  width: 80%;
  height: 60%;
  border: 5px dashed #C4C4C4;
  border-radius: 10px;
  cursor: pointer;
  margin: auto 0;
  
  img {
    width: 145px;
    height: 145px;
  }
}

.card-border-cstm {
  border: 2px solid var(--wf-primary-500) !important;
}

.card-border-cstm-fs {
  border: 2px solid var(--fs-grid-primary) !important;
}

.card-border-cstm-wo {
  border: 2px solid var(--wo-primary-400) !important;
}

.card-radius {
  border-radius: 10px !important;
}

#produitForm {
  .form-control {
    color: black !important;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 10px !important;
  }
}