import { Component, OnInit, ViewEncapsulation } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { AuthService } from "../../../../../../core/auth/src";
import { first } from "rxjs/operators";
import {LoadingController, NavController} from "@ionic/angular";

@Component({
  selector: 'wph-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class LoginComponent implements OnInit {

  data = {
    'headerTitle': 'Login Platform des offres',
    "forgotPassword": "", // TODO: Mot de passe oublier?
    "subtitle": "Welcome",
    "labelUsername": "Username",
    "labelPassword": "Mot de passe",
    "title": "Login to your account",
    "username": "Entrer le nom d'utilisateur",
    "password": "Entrer le mot de passe",
    "register": "", // TODO: Register
    "login": "Connexion",
    "subscribe": "Inscrire",
    "skip": "Skip",
    "facebook": "Facebook",
    "twitter": "Twitter",
    "google": "Google",
    "pinterest": "Pinterest",
    "description": "", // TODO: Don't have account?
    "logo": "assets/images/logos/logo.png",
  };
  loading = false;
  returnUrl = '/';

  loginForm!: FormGroup<{email: FormControl<string>, password: FormControl<string>}>;
  formSubmitted = false;
  error: any;

  showPassword = false;

  constructor(
    private route: ActivatedRoute,
    private navController: NavController,
    private authenticationService: AuthService,
    private fb: FormBuilder,
    private loadingCtrl: LoadingController
  ) {}

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      email: [localStorage.getItem('USER_NAME') || '', [Validators.required]],
      password: ['', Validators.required],
    });


    // reset login status
    this.authenticationService.logout();

    // get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';
  }

  /**
   * convenience getter for easy access to form fields
   */
  get formValues() {
    return this.loginForm.controls;
  }

  /**
   * On submit form
   */
  async onSubmit() {
    const loading = await this.loadingCtrl.create({
      message: 'Chargement...',
      duration: 3000
    });

    loading.present();
    this.formSubmitted = true;
    if (this.loginForm.valid) {
      this.loading = true;
      this.authenticationService
        .login(
          this.formValues['email']?.value,
          this.formValues['password']?.value
        )
        .pipe(first())
        .subscribe(
          (data: any) => {

            this.authenticationService.setToken(localStorage.getItem('fcmToken')).subscribe((data: any) => {
              localStorage.setItem('USER_NAME', this.formValues['email']?.value)
              this.loginForm.reset();
              this.navController.navigateRoot('/');
              loading.dismiss();
            });
          },
          (error: any) => {
            this.error = error;
            this.loading = false;
            loading.dismiss();
          }
        );
    }
  }

  onForgotPasswordFunc($event: MouseEvent) {

  }

  onRegisterFunc() {

  }

  get errMessage() {
    switch (this.error.error.status) {
      case 'UNAUTHORIZED':
        return 'Email ou mot de passe incorrect';
      default:
        return 'Une erreur est survenue';
    }
  }

  handlePasswordChange() {
    this.navController.navigateForward('/auth/forgot-password');
  }
}
