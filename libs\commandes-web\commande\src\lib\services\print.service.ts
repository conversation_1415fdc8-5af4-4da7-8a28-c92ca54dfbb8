import { Injectable } from "@angular/core";

@Injectable({ 
    providedIn: 'root'
})
export class PrintService {
    constructor() {}

    print(nodeToPrintId: string, headerId: string): void {
        const nodeToPrint = document.getElementById(nodeToPrintId)?.cloneNode(true);
        const sideBarMenu = document.getElementById('left-sidebar-menu');
        const printSection = document.createElement('div');
        const pageHeader = document.getElementById(headerId);

        printSection.appendChild(nodeToPrint);

        document.body.appendChild(printSection);

        // Apply styles for printing if necessary
        printSection.style.visibility = 'hidden';
        printSection.style.position = 'absolute';
        printSection.style.left = '0';
        printSection.style.top = '0';

        pageHeader.style.visibility = 'hidden';
        sideBarMenu.style.visibility = 'hidden';

        window.print();

        // Clean up the DOM after printing
        document.body.removeChild(printSection);
        pageHeader.style.visibility = 'visible';
        sideBarMenu.style.visibility = 'visible';
    }
}