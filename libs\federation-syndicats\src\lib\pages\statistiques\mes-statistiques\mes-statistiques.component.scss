

.chart-row {
  max-height: 400px !important; 
}
.sw-container {
  width: calc(100% + 10px);
}

.chart-canvas {
  height: 100% !important; 
  max-height: 100% !important; 
}
.card-radius {
  border-radius: var(--winoffre-base-border-radius);
}


.info-card {
  min-height: 220px;
  overflow: hidden;
}

.btn-right {
  border-top-left-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
  border-top-right-radius: var(--winoffre-base-border-radius) !important;
  border-bottom-right-radius: var(--winoffre-base-border-radius)!important;

  color: #fff;
  background: var(--wf-primary-400);
  outline: none;
  border: none;
}

.date-container {
  padding: 1.8px 10px;
  border: 1px solid var(--wf-primary-400);

  font-size: 1rem;
  color: black;
  font-weight: 600;
  cursor: pointer;
}

.btn-left {
  border-top-left-radius: var(--winoffre-base-border-radius) !important;
  border-bottom-left-radius: var(--winoffre-base-border-radius) !important;
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;

  color: #fff;
  background: var(--wf-primary-400);
  outline: none;
  border: none;
}

#FEDERATION_SYNDICAT-container {
  .btn-left, .btn-right {
    background: var(--fs-primary-500) !important;
  }
}

#WIN_GROUPE-container {
  .btn-left, .btn-right {
    background: var(--wf-primary-400) !important;
  }
}