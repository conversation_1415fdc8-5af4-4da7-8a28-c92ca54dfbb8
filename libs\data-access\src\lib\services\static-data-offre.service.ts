import { Inject, Injectable } from "@angular/core";
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Subject, Observable, BehaviorSubject, map, forkJoin } from "rxjs";
import { BlocOffre, toJSON } from '../models/bloc-offre.model';
import { DetailValeurPalier } from '../models/detail-valeur-palier.model';
import { Offre, OffreCriteria } from '../models/offre.model';
//import { offres, produits } from '../edit-commande/testdata';
import { ProduitCriteria } from '../models/produitCriteria.model';
import { Produit } from '../models/produit.model';
import { Fournisseur } from '../models/fournisseur.model';
import { FormeProduit } from '../models/forme-produit.model';
import { GammeProduit } from '../models/gamme-produit.model';
import { ArticleAcceuil, MessageAcceuil, MessageCriteria } from '../models/acceuil.model';
import { Commande, CommandeCriteria } from '../models/commande.model';
import { StatsSearch } from "../models/statsSearch.model";
import { OffreDto, StatsData } from "../models/stats.model";
import { Pagination } from "../models/PaginationDTO.ts";
import { OffresService } from "@wph/data-access";


@Injectable({
  providedIn: 'root'
})
export class StaticDataOffreService {

  private staticDataSubj: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  staticDataObs$ = this.staticDataSubj.asObservable();

  private httpCallAlreadyMade: boolean = false;


  constructor(private offresService: OffresService) {
  }

  

  init()  {
    if(this.httpCallAlreadyMade===true) {
      return ;
    }

    this.httpCallAlreadyMade = true;

    forkJoin( [
      this.offresService.getListeGammesProduit(),
      this.offresService.getListeFormesProduit(),
      //this.offresService.getListeFournisseurGrossiste(),
      //this.offresService.getListeFournisseurLabo(),
      //this.offresService.getListeOffreurs()
    ] ).subscribe(resp => {
      this.staticDataSubj.next( resp );

      setTimeout(() => {
        this.httpCallAlreadyMade = false;
      }, 1500);
    });
  }

  init2() {
    if(this.httpCallAlreadyMade===true) {
      return ;
    }

    this.httpCallAlreadyMade = true;

    forkJoin( [
      this.offresService.getListeGammesProduit(),
      this.offresService.getListeFormesProduit(),
    ] ).subscribe(resp => {
      this.staticDataSubj.next( resp );

      setTimeout(() => {
        this.httpCallAlreadyMade = false;
      }, 1500);
    });
  }

}
