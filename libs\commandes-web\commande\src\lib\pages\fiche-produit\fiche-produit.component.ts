import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { CommandeService } from '../../services/commande.service';
import { ProduitDTO } from '../../models/produitDTO';
import { iif } from 'rxjs';
import { AlertService } from '@wph/shared';

@Component({
    selector: 'wph-fiche-produit',
    templateUrl: './fiche-produit.component.html',
    styleUrls: ['./fiche-produit.component.scss'],
})
export class FicheProduitComponent implements OnInit {
    idParam: any;
    page: any;
    dataProduit: ProduitDTO
    qteCMD = 1;
    status: string;
    isProduitFournisseur: boolean;
    codeColor: any;
    produitType: string;
    disponibiliteCode: string;
    disponibiliteLibelle: string;

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private srv: CommandeService,
        private alertService: AlertService
    ) { }

    ngOnInit() {
        // Subscribe to both route params and query params
        this.route.params.subscribe((params) => {
            this.idParam = params['id'];
            this.produitType = params['type'];

            this.isProduitFournisseur = (this.produitType === 'produit-fournisseur');
        });
    
        this.route.queryParams.subscribe((queryParams) => {
            this.page = queryParams['page'];
        });
    
        this.initProduit();
    }

    initProduit() {
        iif(
            () => this.isProduitFournisseur,
            this.srv.getCatalogueProduitByCode(this.idParam),
            this.srv.getProduitGeneralById(this.idParam)
        )
            .subscribe(res => {
                this.dataProduit = res;
            });
    }

    searchAddQteCmd(e: any) {
        this.qteCMD = e.number;
    }

    commanderProduit() {
        const body = {
            quantite: Number(this.qteCMD),
            codeProduit: this.dataProduit.codeProduit,
        };

        this.srv.dansPanier(body).subscribe((res) => {
            this.alertService.info('Le produit a été ajouté au panier avec succès.');
            // update commande number in panier
            this.srv.panierChanged(res);
        });
    }

    dispoCheck() {
        this.srv.checkDispo({
            codeProduit: this.dataProduit.codeProduit,
            qteCmd: this.qteCMD
        }).subscribe((res) => {
            if (res[0].codeRetour) {
                this.disponibiliteCode = 'D';
                this.disponibiliteLibelle = 'Appelez votre grossiste';
            } else {
                this.disponibiliteCode = res[0].listeReponses[0].dispo;
                this.disponibiliteLibelle = res[0].listeReponses[0].dispoLibelle;
            }
        });
    }

    goBack() {
        if(this.page === 'alert-produit'){
            this.router.navigateByUrl('/commande-web/Alerte-produit');
        }else if(this.produitType === 'produit-fournisseur'){
            this.router.navigateByUrl('/commande-web/catalogue-produit');
        }else {
            this.router.navigateByUrl('/commande-web/list-nouveaux-produits');
        }
    }
}
