<div *ngIf="(currentPlateforme$ | async) === 'DEFAULT'" class="row pb-3 pb-md-0">
  <div class="col-lg-6 left-homelanding px-0">
    <div class="
        left-homelandingcont
        h-100
        d-flex
        flex-column
        align-content-between
      ">

      <div class="
          left-homelandingcontup
          bg-white
          position-relative
          px-3 px-md-4
          py-4
        " style="min-height: 100%; display: grid; place-items: center">
        <div class="blurred-img d-none d-lg-block"></div>
        <h3 class="display-4 position-relative" style="z-index: 1; max-width: 520px">
          <PERSON><PERSON>n, atteignez le succès sans efforts inutiles.
        </h3>
      </div>
    </div>
  </div>

  <div class="col-lg-6 d-flex flex-column justify-content-center" style="background-color: #f3f3f3">
    <div class="loginform d-block overflow-hidden">
      <div class="formcard position-relative my-4 my-lg-0 mx-2">
        <div class="loginformbg d-none d-md-block hidden"></div>
        <div class="card position-relative my-4 my-lg-0" style="z-index: 1">
          <!-- Logo -->
          <div class="card-header py-2 text-center" [ngClass]="{
          'wf-bg': (currentPlateforme$ | async) === 'WIN_GROUPE',
          'fs-bg': (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT'
          }" style="background: var(--win-offre-primary);">
            <span>
              <img src="assets/images/pharmalien_logo_light.svg" alt="" height="50" />
            </span>
          </div>

          <div class="card-body-cus py-4 px-3 px-md-4">
            <div class="text-center w-75 m-auto">
              <h4 class="text-dark-50 text-center mt-0 font-weight-bold">
                S'identifier
              </h4>
              <p class="text-muted mb-4">
                Entrez votre identifiant d'utilisateur et votre mot de passe pour accéder
                à l'application
              </p>
            </div>

            <form name="login-form" [formGroup]="loginForm" (ngSubmit)="onSubmit()" wphFocusTrap>
              <!-- <app-ui-preloader [display]="loading"></app-ui-preloader> -->
              <ngb-alert type="danger" *ngIf="error" [dismissible]="false">{{ error }}</ngb-alert>

              <div class="mb-3">
                <label for="emailaddress" class="form-label">Identifiant d'utilisateur</label>

                <input type="email" formControlName="email" class="form-control" autocomplete="current-username"
                  [ngClass]="{ 'is-invalid': submitted && f['email']?.errors }" id="emailaddress"
                  placeholder="Identifiant" />

                <div *ngIf="submitted && f['email']?.errors" class="invalid-feedback">
                  <div *ngIf="f['email']?.errors?.['required']">Identifiant d'utilisateur est obligatoire</div>
                  <div *ngIf="f['email']?.errors?.['email']">Adresse email incorrecte</div>
                </div>
              </div>

              <div class="form-group mb-3">
                <a routerLink="/auth/reset-password" class="text-muted float-right" tabindex="-1"><small>Mot de passe
                    oublié?</small></a>

                <label for="password">Mot de passe</label>

                <div class="input-group input-group-merge">
                  <input [type]="showPassword ? 'text' : 'password'" formControlName="password" class="form-control"
                    [ngClass]="{ 'is-invalid': submitted && f['password']?.errors }" id="password"
                    autocomplete="current-password" placeholder="Mot de passe" />

                  <div class="input-group-text" (click)="showPassword = !showPassword">
                    <span class="mdi mdi-eye" *ngIf="showPassword"></span>
                    <span class="mdi mdi-eye-off" *ngIf="!showPassword"></span>
                  </div>

                  <div *ngIf="submitted && f['password']?.errors" class="invalid-feedback">
                    <div *ngIf="f['password']?.errors?.['required']">Le mot de passe est obligatoire</div>
                  </div>
                </div>
              </div>

              <div class="mb-3 mb-0 text-center">
                <button class="btn btn-primary" type="submit" tabindex="-1" style="background: var(--win-offre-primary);">
                  Se connecter
                </button>
              </div>
            </form>

          </div>
          <!-- end card-body-cus -->
        </div>
      </div>
      <!-- end card -->

      <div class="row mt-3">
        <div class="col-12 text-center">
          <!-- <p >Vous n'avez pas de compte? <a routerLink="/account/signup" class="text-muted ml-1 primary-color"><b>S'inscrire</b></a></p> -->
        </div> <!-- end col -->
      </div>
      <!-- end row -->
    </div>
  </div>

  <footer class="footer footer-alt">
    <p class="p-0 m-0">Copyright <a href="https://sophatel.com" target="_blank" class="external-link">Sophatel Ingénierie</a> &copy; {{today | date : 'yyyy'}}. Tous droits réservés</p>
  </footer>
</div>

<ng-container *ngIf="(currentPlateforme$ | async) === 'WIN_GROUPE'">
  <div style="height: calc(100vh - 45px);" class="d-flex row align-items-center justify-content-center">
    <div class="col-xl-7 col-11 mx-2" style="max-width: 950px;">
      <div class="card">
        <div class="row">
          <div class="col-md-6 col-12">
            <div class="card-body-cus py-4 px-3 px-md-4">
              <div class="row d-flex justify-content-center mx-0 mb-3">
                <img src="assets/images/winplusGroupe-green.svg"  style="height: 80px; width: auto" alt="logo">
              </div>
              <div class="text-center w-75 m-auto">
                <!-- <span class="login-title text-center mt-0 mb-2 font-weight-bold" style="color: var(--wf-primary-500);">
                  {{ 'Bienvenue à' | uppercase }}
                </span> -->


                <p class="text-muted mb-4">
                  <b>Entrez votre code et votre mot de passe pour accéder à l'application</b>
                </p>
              </div>

              <form name="login-form" [formGroup]="loginForm" (ngSubmit)="onSubmit() " wphFocusTrap>
                <ngb-alert type="danger" *ngIf="error" [dismissible]="false">{{ error }}</ngb-alert>

                <div class="mb-3">
                  <label for="emailaddress" class="form-label">Code</label>

                  <input type="email" formControlName="email" class="form-control" autocomplete="current-username"
                    [ngClass]="{ 'is-invalid': submitted && f['email']?.errors }" id="emailaddress"
                    placeholder="Code" />

                  <div *ngIf="submitted && f['email']?.errors" class="invalid-feedback">
                    <div *ngIf="f['email']?.errors?.['required']">Identifiant d'utilisateur est obligatoire</div>
                    <div *ngIf="f['email']?.errors?.['email']">Adresse email incorrecte</div>
                  </div>
                </div>

                <div class="form-group mb-3">
                  <a routerLink="/auth/reset-password" class="text-muted float-right" tabindex="-1"><small>Mot de passe
                      oublié?</small></a>

                  <label for="password">Mot de passe</label>

                  <div class="input-group input-group-merge">
                    <input [type]="showPassword ? 'text' : 'password'" formControlName="password" class="form-control"
                      [ngClass]="{ 'is-invalid': submitted && f['password']?.errors }" id="password"
                      autocomplete="current-password" placeholder="Mot de passe" />

                    <div class="input-group-text" (click)="showPassword = !showPassword">
                      <span class="mdi mdi-eye" *ngIf="showPassword"></span>
                      <span class="mdi mdi-eye-off" *ngIf="!showPassword"></span>
                    </div>

                    <div *ngIf="submitted && f['password']?.errors" class="invalid-feedback">
                      <div *ngIf="f['password']?.errors?.['required']">Le mot de passe est obligatoire</div>
                    </div>
                  </div>
                </div>

                <div class="mb-3 mb-0 text-center">
                  <button class="btn btn-primary w-100"  style="background-color: var(--wf-primary-500);" type="submit" tabindex="-1">
                    Se connecter
                  </button>
                </div>
              </form>

            </div>
          </div>

          <div class="col-6 m-0 d-none d-md-flex align-items-center">
            <div class="card bg-gradient-wf p-4">
              <div id="login-swiper" class="card inner-card w-100 h-100 p-2">

                <swiper-container class="h-100 p-0 sw-container" speed="1000" autoplay="true" loop="false"
                  navigation="false" pagination="true" scrollbar="false" slidesPerView="1" centeredSlides="true"
                  #swiperRef>

                  <swiper-slide>
                    <span class="content">Rejoignez votre groupement et bénéficiez des services et remises
                      exclusives.</span>
                  </swiper-slide>

                  <swiper-slide>
                    <span class="content">Rejoignez votre groupement et bénéficiez des services et remises
                      exclusives.</span>
                  </swiper-slide>

                </swiper-container>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <footer class="footer footer-alt">
    <p class="p-0 m-0">Copyright <a href="https://sophatel.com" target="_blank" class="external-link">Sophatel Ingénierie</a> &copy; {{today | date : 'yyyy'}}. Tous droits réservés</p>
  
    <div class="badge badge-success ml-3" style="font-size: 12px;">
      v{{version}}
    </div>
  
  </footer>
</ng-container>

<ng-container *ngIf="(currentPlateforme$ | async) === 'FEDERATION_SYNDICAT'">
  <div style="height: calc(100vh - 45px);" class="d-flex row align-items-center justify-content-center">
    <div class="col-xl-7 col-11 mx-2" style="max-width: 950px;">
      <div class="card">
        <div class="row">
          <div class="col-md-6 col-12">
            <div class="card-body-cus py-4 px-3 px-md-4">
              <div class="text-center w-75 m-auto">
                <div class="row d-flex justify-content-center mb-2">
                  <img src="assets/images/logo-fs-colored.png"  height="80" width="80" alt="logo">
                </div>

                <span class="login-title text-center mt-0 mb-2 font-weight-bold" style="color:#6D3D81;">
                  {{'Bienvenue à la centrale pharma' | titlecase}}
                </span>

                <p class="text-muted mb-4">
                  <b>Veuillez entrez vos identifiants.</b>
                </p>
              </div>

              <form name="login-form" [formGroup]="loginForm" (ngSubmit)="onSubmit() " wphFocusTrap>
                <ngb-alert type="danger" *ngIf="error" [dismissible]="false">{{ error }}</ngb-alert>

                <div class="mb-3">
                  <label for="emailaddress" class="form-label">Code</label>

                  <input type="email" formControlName="email" class="form-control" autocomplete="current-username"
                    [ngClass]="{ 'is-invalid': submitted && f['email']?.errors }" id="emailaddress"
                    placeholder="Code" />

                  <div *ngIf="submitted && f['email']?.errors" class="invalid-feedback">
                    <div *ngIf="f['email']?.errors?.['required']">Identifiant d'utilisateur est obligatoire</div>
                    <div *ngIf="f['email']?.errors?.['email']">Adresse email incorrecte</div>
                  </div>
                </div>

                <div class="form-group mb-3">
                  <a routerLink="/auth/reset-password" class="text-muted float-right" tabindex="-1"><small>Mot de passe
                      oublié?</small></a>

                  <label for="password">Mot de passe</label>

                  <div class="input-group input-group-merge">
                    <input [type]="showPassword ? 'text' : 'password'" formControlName="password" class="form-control"
                      [ngClass]="{ 'is-invalid': submitted && f['password']?.errors }" id="password"
                      autocomplete="current-password" placeholder="Mot de passe" />

                    <div class="input-group-text" (click)="showPassword = !showPassword">
                      <span class="mdi mdi-eye" *ngIf="showPassword"></span>
                      <span class="mdi mdi-eye-off" *ngIf="!showPassword"></span>
                    </div>

                    <div *ngIf="submitted && f['password']?.errors" class="invalid-feedback">
                      <div *ngIf="f['password']?.errors?.['required']">Le mot de passe est obligatoire</div>
                    </div>
                  </div>
                </div>

                <div class="mb-3 mb-0 text-center">
                  <button class="btn btn-primary w-100"  style="background-color: #6D3D81;" type="submit" tabindex="-1">
                    Se connecter
                  </button>
                </div>
              </form>

            </div>
          </div>

          <div class="col-6 m-0 d-none d-md-flex align-items-center">
            <div class="card bg-gradient-fs p-4">
              <div id="login-swiper" class="card inner-card w-100 h-100 p-2">

                <swiper-container class="h-100 p-0 sw-container" speed="1000" autoplay="true" loop="false"
                  navigation="false" pagination="true" scrollbar="false" slidesPerView="1" centeredSlides="true"
                  #swiperRef>

                  <swiper-slide>
                    <span class="content">Rejoignez votre groupement et bénéficiez des services et remises
                      exclusives.</span>
                  </swiper-slide>

                  <swiper-slide>
                    <span class="content">Rejoignez votre groupement et bénéficiez des services et remises
                      exclusives.</span>
                  </swiper-slide>

                </swiper-container>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <footer class="footer footer-alt">
    <p class="p-0 m-0">Copyright <a href="https://sophatel.com" target="_blank" class="external-link">Sophatel Ingénierie</a> &copy; {{today | date : 'yyyy'}}. Tous droits réservés</p>
  
    <div class="badge badge-success ml-3" style="font-size: 12px;">
      v{{version}}
    </div>
  
  </footer>
</ng-container>

