import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { GroupesStatistiquesComponent } from "./groupes-statistiques/groupes-statistiques.component";
import { MesStatistiquesComponent } from "./mes-statistiques/mes-statistiques.component";
import { MembresStatistiquesComponent } from "./members-statistique/members-statistiques.component";
import { AuthoritiesGuard } from "@wph/web/shared";
import { LaboStatistiquesComponent } from "./labo-statistiques/labo-statistiques.component";

const routes: Routes = [
    {
        path: 'groupes',
        title: 'Statistiques Groupes',
        component: GroupesStatistiquesComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'membres',
        title: 'Statistiques Membres',
        component: MembresStatistiquesComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_RESPONSABLE'] }
    },
    {
        path: 'mes-statistiques',
        title: 'Mes Statistiques',
        component: MesStatistiquesComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_AGENT_POINT_VENTE','ROLE_AGENT_FOURNISSEUR', 'ROLE_NATIONAL'] }
    },
    {
      path: 'labo-statistiques',
      title: 'Mes Statistiques',
      component: LaboStatistiquesComponent,
      canActivate: [AuthoritiesGuard],
      data: { authorities: ['ROLE_AGENT_FOURNISSEUR'] }
  },
    {
        path: 'mes-statistiques/individuelle',
        title: 'Mes Statistiques Individuelles',
        component: MesStatistiquesComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_NATIONAL'] }
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class StatistiquesRoutingModule {}
