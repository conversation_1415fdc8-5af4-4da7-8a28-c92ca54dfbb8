import { Inject, Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree } from "@angular/router";
import { PlateformeService } from "@wph/shared";
import { SelectedPlateforme } from "@wph/web/layout";

@Injectable({
    providedIn: 'root'
})
export class AppHostnameGuard implements CanActivate {
    pharmaHubPlateformeKeys: SelectedPlateforme[] = [
        'DEFAULT',
        'WIN_OFFRE',
        'COMMANDE_WEB'
    ];

    constructor(
        @Inject('ENVIROMENT') private env: any,
        private router: Router,
        private plateformeService: PlateformeService
    ) { }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean | UrlTree {
        const hostname = location.hostname;
        const url: string = route['_routerState'].url;

        const urlParts: string[] = url?.split('/');
        urlParts.shift();

        if ((this.env?.ACHATS_GROUPES_HOST as string[])?.includes(hostname)) {
            // Force current plateforme value to --> "FEDERATION_SYNDICAT"
            if (
                !this.plateformeService.getCurrentPlateforme() ||
                !this.plateformeService.isPlateForme('FEDERATION_SYNDICAT')
            ) {
                this.plateformeService.setCurrentPlateforme('FEDERATION_SYNDICAT');
            }

            // Force redirect if route path does not match allowed "ACHATS_GROUPES" routes
            if (!urlParts[0]?.includes('achats-groupes') && !urlParts[0]?.includes('auth')) {
                return this.router.createUrlTree(['achats-groupes', 'accueil']);
            }

            return true;

        } else if ((this.env?.WIN_GROUPE_HOST as string[])?.includes(hostname)) {
            // Force current plateforme value to --> "WIN_GROUPE"
            if (
                !this.plateformeService.getCurrentPlateforme() ||
                !this.plateformeService.isPlateForme('WIN_GROUPE')
            ) {
                this.plateformeService.setCurrentPlateforme('WIN_GROUPE');
            }

            // Force redirect if route path does not match allowed "ACHATS_GROUPES" routes
            if (!urlParts[0]?.includes('achats-groupes') && !urlParts[0]?.includes('auth')) {
                return this.router.createUrlTree(['achats-groupes', 'accueil']);
            }

            return true;

        } else if ((this.env?.PHARMA_HUB_HOST as string[])?.includes(hostname)) {
            // Force current plateforme value to "DEFAULT", if it doesn't match any of the "PHARMA_HUB" plateforme keys
            if (
                !this.plateformeService.getCurrentPlateforme() ||
                !this.pharmaHubPlateformeKeys.includes(this.plateformeService.getCurrentPlateforme())
            ) {
                this.plateformeService.setCurrentPlateforme('DEFAULT');
            }

            return true;
        }

        return false;
    }
}