<div class="rowline mb-0">
  <div class="page-title-box row">
    <div class="col-auto d-flex align-items-center">
      <button *jhiHasAnyPlateforme="['FEDERATION_SYNDICAT', 'WIN_GROUPE']" class="actions-icons action-back btn text-white"
        (click)="onCancel()">
        <i class="bi bi-chevron-left" style="font-size: 20px;"></i>
      </button>
      <h4 class="page-title col-auto">Mon Compte</h4>
    </div>

    <div class="col-auto px-1">
      <div class="row justify-content-end align-items-center">
        <button *ngIf="activeIndex === 1" type="submit" class="btn btn-sm btn-primary m-1 text-white d-flex"
          (click)="onSubmit()">
          <i class="mdi mdi-content-save"></i> <span class="d-none d-md-block">Enregistrer</span> </button>

        <button type="button" class="btn btn-sm btn-dark m-1 text-white" (click)="onCancel()">
          <i class="mdi mdi-close"></i> Quitter
        </button>
      </div>
    </div>
  </div>
</div>

<ng-container *jhiHasAnyPlateforme="['DEFAULT', 'WIN_OFFRE', 'COMMANDE_WEB']">
  <div *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE']" class="container-fluid px-1">
    <div class="card shadow-none bg-transparent mb-1 mb-4">
      <ul ngbNav #compteNav="ngbNav" (activeIdChange)="activeIndexChange($event)" class="nav-tabs">
        <li [ngbNavItem]="1">
          <a ngbNavLink>
            <span class="d-block">
              <i class="mdi mdi-account mdi-18px"></i>
              <b class="mx-1">Gestion de compte</b>
            </span>
          </a>
          <ng-template ngbNavContent>
            <form [formGroup]="editForm" *ngIf="isFormReady" class="my-1 row d-flex bg-white mx-0" wphFocusTrap
              style="border-radius: var(--winoffre-base-border-radius);">

              <div class="col-md-6 col-12">
                <h4 class="pl-2">{{'Informations générales' | uppercase}}</h4>
                <hr>

                <div class="row px-2">
                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="firstname">Prénom</label>
                      <input type="text" id="firstname" formControlName="firstname" class="form-control form-control-md"
                        autocomplete="off">
                    </div>
                    <div *ngIf="firstname.invalid &&  submitted" class="alert alert-danger">
                      <div *ngIf="$any(firstname.errors)?.maxlength">
                        longueur maximum 40 characters.
                      </div>
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="lastname">Nom</label>
                      <input type="text" id="lastname" formControlName="lastname" class="form-control form-control-md"
                        autocomplete="off">
                    </div>
                    <div *ngIf="lastname.invalid &&  submitted" class="alert alert-danger">
                      <div *ngIf="$any(lastname.errors)?.maxlength">
                        longueur maximum 40 characters.
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row px-2 pt-2">
                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="email">Adresse Email</label>
                      <input type="text" id="email" formControlName="email" class="form-control form-control-md"
                        autocomplete="off">
                    </div>

                    <div *ngIf="email.invalid && submitted" class="alert alert-danger">
                      <div *ngIf="$any(email.errors)?.required">
                        L'adresse email est obliatoire.
                      </div>
                      <div *ngIf="$any(email.errors)?.maxlength">
                        longueur maximum 60 characters.
                      </div>
                      <div *ngIf="$any(email.errors)?.email">
                        Adresse email invalide.
                      </div>
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="username">Code</label>
                      <input type="text" id="username" formControlName="username" class="form-control form-control-md"
                        [readOnly]="true" autocomplete="off">
                    </div>
                    <div *ngIf="username.invalid &&  submitted" class="alert alert-danger">
                      <div *ngIf="$any(username.errors)?.required">
                        Le code est obliatoire.
                      </div>
                      <div *ngIf="$any(username.errors)?.maxlength">
                        longueur maximum 60 characters.
                      </div>
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group mb-1">
                      <label for="gsm">GSM</label>
                      <input type="text" placeholder="Ex : 06XXXXXXXX"
                        [ngClass]="{'is-invalid': gsm?.invalid && (gsm?.dirty || gsm?.touched || submitted)}"
                        maxlength="10" id="gsm" formControlName="gsm" class="form-control form-control-md"
                        autocomplete="off">
                    </div>

                    <div *ngIf="gsm?.invalid && (gsm?.dirty || gsm?.touched || submitted)" class="text-danger mb-1">
                      Numéro de gsm invalide.
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group mb-1">
                      <label for="numIce">Numéro ICE</label>
                      <input type="text" id="numIce" formControlName="numIce" class="form-control form-control-md"
                        autocomplete="off">
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group mb-1">
                      <label for="fixe">Téléphone</label>
                      <input placeholder="Ex : 05XXXXXXXX" type="text"
                        [ngClass]="{'is-invalid': tel?.invalid && (tel?.dirty || tel?.touched || submitted)}"
                        maxlength="10" id="fixe" formControlName="tel" class="form-control form-control-md"
                        autocomplete="off">
                    </div>

                    <div *ngIf="tel?.invalid && (tel?.dirty || tel?.touched || submitted)" class="text-danger mb-1">
                      Numéro de téléphone invalide.
                    </div>
                  </div>
                </div>

                <div class="row px-2 pt-2">
                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="country">Pays</label>
                      <input type="text" id="country" class="form-control form-control-md" formControlName="country"
                        autocomplete="off" [ngbTypeahead]="searchCountry" [resultFormatter]="countryFormatter"
                        [inputFormatter]="countryFormatter" (selectItem)="chooseCountry($event)" [editable]="false">
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="city">Ville</label>
                      <input type="text" id="city" formControlName="city" class="form-control form-control-md"
                        [ngbTypeahead]="searchCity" [resultFormatter]="cityFormatter" [inputFormatter]="cityFormatter"
                        [editable]="false" autocomplete="off">
                    </div>
                    <div *ngIf="city.invalid &&  submitted" class="alert alert-danger">
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-6 col-12">
                <h4 class="pl-2">{{'Réinitialiser votre mot de passe' | uppercase}}</h4>
                <hr>

                <div class="row px-2">
                  <div class="col-md">
                    <div formGroupName="passwordGroupData" *ngIf="!isSignup">

                      <div class="row">
                        <div class="col-md-6 col-12">
                          <div class="form-group">
                            <label for="oldPassword">Ancien mot de passe</label>
                            <div class="input-group">
                              <input [type]="showAncienMdp ? 'text' : 'password'" id="oldPassword"
                                formControlName="oldPassword" class="form-control form-control-md" autocomplete="off">

                              <div class="input-group-append">
                                <button (click)="showAncienMdp = !showAncienMdp" tabindex="-1"
                                  class="btn btn-sm btn-light text-dark btn-outline-light calendar" type="button">
                                  <i *ngIf="!showAncienMdp" class="mdi mdi-eye"></i>
                                  <i *ngIf="showAncienMdp" class="mdi mdi-eye-off"></i>
                                </button>
                              </div>
                            </div>
                          </div>
                          <div *ngIf="$any(passwordGroupData.errors)?.noOldPass &&  submitted"
                            class="alert alert-danger">
                            L'ancien mot de passe est obligatoire.
                          </div>
                        </div>
                      </div>

                      <div class="form-row pt-2">
                        <div class="col-md-6 col-12">
                          <div class="form-group">
                            <label for="newPassword">{{isSignup ? '' : 'Nouveau '}} mot de passe</label>
                            <div class="input-group">
                              <input [type]="showNewMdp ? 'text' : 'password'" id="newPassword" formControlName="newPassword"
                                class="form-control" autocomplete="off">
      
                              <div class="input-group-append">
                                <button (click)="showNewMdp = !showNewMdp" tabindex="-1"
                                  class="btn btn-sm btn-light text-dark btn-outline-light calendar" type="button">
                                  <i *ngIf="!showNewMdp" class="mdi mdi-eye"></i>
                                  <i *ngIf="showNewMdp" class="mdi mdi-eye-off"></i>
                                </button>
                              </div>
                            </div>
                          </div>
                          <div *ngIf="$any(passwordGroupData.errors)?.noNewPass &&  submitted"
                            class="alert alert-danger">
                            Le mot de passe est obligatoire.
                          </div>
                          <div *ngIf="$any(passwordGroupData.errors)?.notSame &&  submitted" class="alert alert-danger">
                            Les mots de passe ne coinçident pas.
                          </div>
                        </div>

                        <div class="col-md-6 col-12">
                          <div class="form-group">
                            <label for="confirmPass">Confirmation</label>
                            <div class="input-group">
                              <input [type]="showConfirmMdp ? 'text' : 'password'" id="confirmPass"
                                formControlName="confirmPass" class="form-control" autocomplete="off">
      
                              <div class="input-group-append">
                                <button (click)="showConfirmMdp = !showConfirmMdp" tabindex="-1"
                                  class="btn btn-sm btn-light text-dark btn-outline-light calendar" type="button">
                                  <i *ngIf="!showConfirmMdp" class="mdi mdi-eye"></i>
                                  <i *ngIf="showConfirmMdp" class="mdi mdi-eye-off"></i>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </form>
          </ng-template>
        </li>

        <li [ngbNavItem]="2">
          <a ngbNavLink>
            <span class="d-block">
              <i class="mdi mdi-pharmacy mdi-18px"></i>
              <b class="mx-1">Gestion de pharmacie</b>
            </span>
          </a>

          <ng-template ngbNavContent>
            <form [formGroup]="$any(editForm.get('societe'))" class="d-flex row my-1 bg-white mx-0"
              style="border-radius: var(--winoffre-base-border-radius);">
              <div class="col-12">
                <h4 class="pl-2">{{'Informations de ma pharmacie' | uppercase}}</h4>
                <hr>

                <div class="row d-flex px-2">
                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="raisonSociale">Raison Sociale</label>
                      <input type="text" id="raisonSociale" formControlName="raisonSociale"
                        class="form-control form-control-md" readonly autocomplete="off">
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="nomResponsable">Nom Responsable</label>
                      <input type="text" id="nomResponsable" readonly formControlName="nomResponsable"
                        class="form-control form-control-md" autocomplete="off">
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="ville">Ville</label>
                      <input type="text" id="ville" formControlName="ville" readonly
                        class="form-control form-control-md" autocomplete="off">
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="localite">Localité</label>
                      <input type="text" id="localite" formControlName="localite" readonly
                        class="form-control form-control-md" autocomplete="off">
                    </div>
                  </div>

                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label for="adresse">Adresse</label>
                      <input type="text" id="adresse" formControlName="adresse" readonly
                        class="form-control form-control-md" autocomplete="off">
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </ng-template>
        </li>
      </ul>

      <div [ngbNavOutlet]="compteNav"></div>
    </div>
  </div>

  <ng-container
    *jhiHasAnyAuthority="['ROLE_AGENT_COMMERCIAL', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_ASSISTANT', 'ROLE_SUPER_ADMIN', 'ROLE_AGENT_ACHAT']">
    <ng-container *ngTemplateOutlet="simpleAccountView"></ng-container>
  </ng-container>
</ng-container>


<ng-container *jhiHasAnyPlateforme="['FEDERATION_SYNDICAT', 'WIN_GROUPE']">
  <ng-container *ngTemplateOutlet="simpleAccountView"></ng-container>
</ng-container>

<ng-template #simpleAccountView>
  <div class="container-fluid m-0 px-1">
    <div class="card shadow-none  mb-1 mb-4" style="border-radius: var(--winoffre-base-border-radius);">
      <form [formGroup]="editForm" *ngIf="isFormReady" class="my-2 row d-flex" wphFocusTrap>

        <div class="col-md-6 col-12">
          <h4 class="pl-2">{{'Informations générales' | uppercase}}</h4>
          <hr>

          <div class="row px-2 pt-2">
            <div class="col-12 col-md-6">
              <div class="form-group">
                <label for="firstname">Prénom</label>
                <input type="text" id="firstname" formControlName="firstname" class="form-control form-control-md"
                  autocomplete="off">
              </div>
              <div *ngIf="firstname.invalid &&  submitted" class="alert alert-danger">
                <div *ngIf="$any(firstname.errors)?.maxlength">
                  longueur maximum 40 characters.
                </div>
              </div>
            </div>

            <div class="col-12 col-md-6">
              <div class="form-group">
                <label for="lastname">Nom</label>
                <input type="text" id="lastname" formControlName="lastname" class="form-control form-control-md"
                  autocomplete="off">
              </div>
              <div *ngIf="lastname.invalid &&  submitted" class="alert alert-danger">
                <div *ngIf="$any(lastname.errors)?.maxlength">
                  longueur maximum 40 characters.
                </div>
              </div>
            </div>

            <div class="col-12 col-md-6">
              <div class="form-group">
                <label for="email">Adresse Email</label>
                <input type="text" id="email" formControlName="email"
                  [ngClass]="{'is-invalid': email.invalid && email.touched}" class="form-control form-control-md"
                  autocomplete="off">
              </div>

              <div *ngIf="email.invalid && email.touched" class="alert alert-danger">
                <div *ngIf="$any(email.errors)?.required">
                  L'adresse email est obliatoire.
                </div>
                <div *ngIf="$any(email.errors)?.maxlength">
                  longueur maximum 60 characters.
                </div>
                <div *ngIf="$any(email.errors)?.email">
                  Adresse email invalide.
                </div>
              </div>
            </div>

            <div class="col-12 col-md-6">
              <div class="form-group">
                <label for="username">Nom d'utilisateur</label>
                <input type="text" id="username" formControlName="username" class="form-control form-control-md"
                  [readOnly]="true" autocomplete="off">
              </div>
              <div *ngIf="username.invalid &&  submitted" class="alert alert-danger">
                <div *ngIf="$any(username.errors)?.required">
                  Le nom d'utilisateur est obliatoire.
                </div>
                <div *ngIf="$any(username.errors)?.maxlength">
                  longueur maximum 60 characters.
                </div>
              </div>
            </div>

            <div class="col-12 col-md-6">
              <div class="form-group mb-1">
                <label for="gsm">GSM</label>
                <input type="text" placeholder="Ex : 06XXXXXXXX"
                  [ngClass]="{'is-invalid': gsm?.invalid && (gsm?.dirty || gsm?.touched || submitted)}" maxlength="10"
                  id="gsm" formControlName="gsm" class="form-control form-control-md" autocomplete="off">
              </div>

              <div *ngIf="gsm?.invalid && (gsm?.dirty || gsm?.touched || submitted)" class="text-danger mb-1">
                Numéro de gsm invalide.
              </div>
            </div>

            <div class="col-12 col-md-6">
              <div class="form-group mb-1">
                <label for="fixe">Téléphone</label>
                <input placeholder="Ex : 05XXXXXXXX" type="text"
                  [ngClass]="{'is-invalid': tel?.invalid && (tel?.dirty || tel?.touched || submitted)}" maxlength="10"
                  id="fixe" formControlName="tel" class="form-control form-control-md" autocomplete="off">
              </div>

              <div *ngIf="tel?.invalid && (tel?.dirty || tel?.touched || submitted)" class="text-danger mb-1">
                Numéro de téléphone invalide.
              </div>
            </div>

            <div class="col-12 col-md-6">
              <div class="form-group mb-1">
                <label for="numIce">Numéro ICE</label>
                <input type="text" id="numIce" formControlName="numIce" class="form-control form-control-md"
                  autocomplete="off">
              </div>
            </div>

            <div class="col-12 col-md-6">
              <div class="form-group">
                <label for="country">Pays</label>
                <input type="text" id="country" class="form-control form-control-md" formControlName="country"
                  autocomplete="off" [ngbTypeahead]="searchCountry" [resultFormatter]="countryFormatter"
                  [inputFormatter]="countryFormatter" (selectItem)="chooseCountry($event)" [editable]="false">
              </div>
            </div>

            <div class="col-12 col-md-6">
              <div class="form-group">
                <label for="city">Ville</label>
                <input type="text" id="city" formControlName="city" class="form-control form-control-md"
                  [ngbTypeahead]="searchCity" [resultFormatter]="cityFormatter" [inputFormatter]="cityFormatter"
                  [editable]="false" autocomplete="off">
              </div>
              <div *ngIf="city.invalid &&  submitted" class="alert alert-danger">
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6 col-12">
          <h4 class="pl-2">{{'Réinitialiser votre mot de passe' | uppercase}}</h4>
          <hr>

          <div class="row px-2 pt-2">
            <div class="col-md">
              <div formGroupName="passwordGroupData" *ngIf="!isSignup">

                <div class="row">
                  <div class="col-md-6 col-12">
                    <div class="form-group">
                      <label for="oldPassword">Ancien mot de passe</label>
                      <div class="input-group">
                        <input [type]="showAncienMdp ? 'text' : 'password'" id="oldPassword"
                          formControlName="oldPassword" class="form-control form-control-md" autocomplete="off">

                        <div class="input-group-append">
                          <button (click)="showAncienMdp = !showAncienMdp" tabindex="-1"
                            class="btn btn-sm btn-light text-dark btn-outline-light calendar" type="button">
                            <i *ngIf="!showAncienMdp" class="mdi mdi-eye"></i>
                            <i *ngIf="showAncienMdp" class="mdi mdi-eye-off"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div *ngIf="$any(passwordGroupData.errors)?.noOldPass &&  submitted" class="alert alert-danger">
                      L'ancien mot de passe est obligatoire.
                    </div>
                  </div>
                </div>

                <div class="form-row pt-2">
                  <div class="col-md-6 col-12">
                    <div class="form-group">
                      <label for="newPassword">{{isSignup ? '' : 'Nouveau '}} mot de passe</label>
                      <div class="input-group">
                        <input [type]="showNewMdp ? 'text' : 'password'" id="newPassword" formControlName="newPassword"
                          class="form-control" autocomplete="off">

                        <div class="input-group-append">
                          <button (click)="showNewMdp = !showNewMdp" tabindex="-1"
                            class="btn btn-sm btn-light text-dark btn-outline-light calendar" type="button">
                            <i *ngIf="!showNewMdp" class="mdi mdi-eye"></i>
                            <i *ngIf="showNewMdp" class="mdi mdi-eye-off"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div *ngIf="$any(passwordGroupData.errors)?.noNewPass &&  submitted" class="alert alert-danger">
                      Le mot de passe est obligatoire.
                    </div>
                    <div *ngIf="$any(passwordGroupData.errors)?.notSame &&  submitted" class="alert alert-danger">
                      Les mots de passe ne coinçident pas.
                    </div>
                  </div>

                  <div class="col-md-6 col-12">
                    <div class="form-group">
                      <label for="confirmPass">Confirmation</label>
                      <div class="input-group">
                        <input [type]="showConfirmMdp ? 'text' : 'password'" id="confirmPass"
                          formControlName="confirmPass" class="form-control" autocomplete="off">

                        <div class="input-group-append">
                          <button (click)="showConfirmMdp = !showConfirmMdp" tabindex="-1"
                            class="btn btn-sm btn-light text-dark btn-outline-light calendar" type="button">
                            <i *ngIf="!showConfirmMdp" class="mdi mdi-eye"></i>
                            <i *ngIf="showConfirmMdp" class="mdi mdi-eye-off"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </form>
    </div>
  </div>
</ng-template>