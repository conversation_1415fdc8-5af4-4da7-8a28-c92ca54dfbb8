import { Component, ElementRef, EventEmitter, Input, OnDestroy, Output, ViewChild } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthService } from "@wph/core/auth";
import { AlertService, DocMetaDataDto, PlateformeService, Role, UploadFileServiceService } from "@wph/shared";
import * as moment from "moment";
import { Fournisseur, SelectLabelValuePair } from "@wph/data-access";
import { PosteService } from "libs/web/gestion-annonces/src/lib/services/poste.service";
import { BlogPostDto, Categorie, PlateformeItem } from "libs/web/gestion-annonces/src/lib/models/BlogPost.interface";
import { HtmlToImageService, UserInputService } from "@wph/web/shared";
import { HttpEventType } from "@angular/common/http";

@Component({
    selector: 'wph-add-edit-post-modal',
    templateUrl: './add-edit-post-modal.component.html',
    styleUrls: ['./add-edit-post-modal.component.scss']
})
export class AddEditPostModalComponent implements OnDestroy {
    @Input() set categories(value: Categorie[]) {
        this._categories = value;
        if (this._categories) {
            this.selectCategorie = this._categories.map(cat => {
                return { label: cat?.libelle, value: cat?.id };
            });
        }
    }
    get categories() {
        return this._categories;
    }

    @Input() set plateformes(value: any[]) {
        this.orginalPlateformesListe = value;
        this._plateformes = value.map(plat => {
            return { label: plat?.libelle, value: plat?.id }
        });
    }
    get plateformes() {
        return this._plateformes;
    }

    @Input()
    set isReadOnly(value: boolean) {
        this._isReadOnly = value;
    }

    get isReadOnly() {
        return this._isReadOnly;
    }

    @Input('data')
    set prefilledData(value: any) {
        this._prefilledData = value;
        !!this._prefilledData && this.patchValues();
    }

    get prefilledData() {
        return this._prefilledData;
    }

    @Input() set availableRoles(value: Role[]) {
        if (value && value.length) {
            this._availableRoles = value;
            this.initUserRoles(value);
        } 
    }

    get availableRoles() {
        return this._availableRoles;
    }

    @Output() changesApplied: EventEmitter<boolean> = new EventEmitter<boolean>();

    selectRoles: { label: string, value: number }[] = []

    _availableRoles: Role[] = [];
    _plateformes: any[];
    postForm: FormGroup;
    submitted = false;
    _prefilledData: any;
    _categories: Categorie[];
    _isReadOnly: boolean;
    modalTitle: string = 'Ajouter un post';
    orginalPlateformesListe: PlateformeItem[];

    selectedColor: string;
    imageSelected: string;
    selectedImageMetadata: DocMetaDataDto;
    selectedLogoMetadata: DocMetaDataDto;
    uploadedDocUrl: string | null = null;
    uploadedLogoUrl: string | null = null;
    imageHasAction: boolean;
    largeImageModalTarget: string;

    urlRegex = new RegExp('[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)?');

    selectTypeDePoste = [
        { label: 'Texte', value: 'M' },
        { label: 'Image', value: 'I' },
        { label: 'Vidéo', value: 'V' }
    ];

    selectVisibility = [
        { label: 'Tout le monde', value: 'G' },
        { label: 'Mes Clients Seulement', value: 'F' },
        { label: 'Tout le monde', value: 'A' },
    ];

    postBackgroundColors = ['#3A96D2', '#65A946', '#FDA403', '#1070B3', '#018347', '#539843', '#953292'];

    tempType: string;
    imgPreview: string;

    currentSociete: Fournisseur;

    selectCategorie: SelectLabelValuePair[];

    @ViewChild('selectedImage') selectedImage: ElementRef;

    constructor(
        private fb: FormBuilder,
        private modalService: NgbModal,
        private srvAlert: AlertService,
        private authService: AuthService,
        private posteService: PosteService,
        private userInputService: UserInputService,
        private plateformeService: PlateformeService,
        private html2imageService: HtmlToImageService,
        private uploadService: UploadFileServiceService,
    ) {
        this.postForm = this.fb.group({
            id: [null],
            titre: ['', [Validators.required]],
            sujet: [
                '',
                [
                    Validators.required,
                    Validators.maxLength(512),
                    Validators.minLength(20),
                ],
            ],
            listRolesCible: [null],
            plateformes: [null, Validators.required],
            categorie: [null, [Validators.required]],
            paramAffichage: [''],
            statut: [null],
            libelleUrl: [null, Validators.required],
            url: [null, Validators.pattern(this.urlRegex)],
            imageAvecAction: [false],
            videoUrl: [null, [Validators.required, Validators.pattern(this.urlRegex)]],
            type: [null, [Validators.required]],
            scopePublication: [null, [Validators.required]],
            auteur: [`${this.authService.getPrincipal()?.firstname + ' ' + this.authService.getPrincipal()?.lastname}`, [Validators.required]],
            dateDebutVisibilite: [null, [Validators.required]],
            dateFinVisibilite: [null, [Validators.required]]
        });

        if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
            this.postForm.patchValue({
                plateformes: [5],
                scopePublication: 'A'
            });
        } else {
            if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) {
                this.postForm.patchValue({
                    plateformes: [4]
                });
            }

            this.postForm.patchValue({
                scopePublication: this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']) ? 'G' : 'F'
            });
        }

        this.currentSociete = this.authService.getPrincipal()?.societe;
    }
    ngOnDestroy(): void {
        this.prefilledData = null;
    }

    get f() {
        return this.postForm.controls;
    }

    dismiss(): void {
        this.imageSelected = '';
        this.modalService.dismissAll();
    }

    open(content: any, size = 'xl'): void {
        console.log(this.selectedImage);

        this.modalService.open(content, { size, centered: true });
    }

    initUserRoles(roles: Role[]) {
        if (roles) {
            this.selectRoles = roles.map((role) => {
                return { label: role.label, value: role?.id };
            });
        }
    }

    testHtmlToCanvas(bgColor: string) {
        this.selectedColor = bgColor;
        const container = document.getElementById('text-image-container');
        this.html2imageService.captureElementToImage(container, { backgroundColor: bgColor }).then(res => {
            this.imgPreview = res;
            const fileFromBase64Image = this.html2imageService.convertBase64ToFile(res);

            if (fileFromBase64Image && this.f['titre']?.value && this.f['sujet']?.value) {
                this.uploadService.uploadFile(this.html2imageService.getUploadFormData(fileFromBase64Image)).subscribe({
                    next: (event) => {
                        if (event.type === HttpEventType.UploadProgress) {
                            console.log(event.loaded);
                        }
                        if (event.type === HttpEventType.Response) {
                            this.selectedImageMetadata = event?.body;
                        }
                    }
                });
            }
        });
    }

    patchValues(): void {
        this.postForm.reset();
        this.submitted = false;
        this.modalTitle = this.isReadOnly ? 'Consulter' : 'Modifier un poste';

        // Set uploaded post image
        this.selectedImageMetadata = this.prefilledData?.docImagePost;
        this.prefilledData?.docImagePost && (this.uploadedDocUrl = this.uploadService.fetchUploadedDocument(this.prefilledData?.docImagePost?.idhash));

        // Set uploaded post logo
        this.selectedLogoMetadata = this.prefilledData?.docLogoPost;
        this.prefilledData?.docLogoPost && (this.uploadedLogoUrl = this.uploadService.fetchUploadedDocument(this.prefilledData?.docLogoPost?.idhash));

        this.imageHasAction = (!!this.prefilledData?.url && !!this.prefilledData?.libelleUrl);

        if (this.prefilledData?.type === 'A' || !this.prefilledData?.type) {
            this.prefilledData.type = this.prefilledData?.videoUrl && 'V';
            this.prefilledData.type = (this.prefilledData?.docImagePost || (this.prefilledData?.url && this.prefilledData?.libelleUrl)) && 'I'
        }

        let typePost = this.prefilledData?.type;

        if(this.prefilledData?.videoUrl) typePost = 'V';

        this.postForm.patchValue({
            id: this.prefilledData?.id,
            titre: this.prefilledData?.titre,
            sujet: this.prefilledData?.sujet,
            categorie: this.prefilledData?.categorie?.id,
            statut: this.prefilledData?.statut,
            auteur: this.prefilledData?.auteur,
            type: typePost,
            imageAvecAction: this.prefilledData?.url && this.prefilledData?.libelleUrl,
            url: this.prefilledData?.url,
            libelleUrl: this.prefilledData?.libelleUrl,
            videoUrl: this.prefilledData?.videoUrl,
            plateformes: this.prefilledData?.plateformes?.map(item => item.id),
            listRolesCible: this.prefilledData?.listRolesCible?.map(item => item.id),
            scopePublication: this.prefilledData?.scopePublication,
            paramAffichage: this.prefilledData?.paramAffichage,
            dateFinVisibilite: moment(this.prefilledData?.dateFinVisibilite),
            dateDebutVisibilite: moment(this.prefilledData?.dateDebutVisibilite)
        });

        this.isReadOnly ? this.postForm.disable() : this.postForm.enable();
        (this.prefilledData?.statut === 'P') && this.f['type'].disable();
    }

    getUploadedDocuments(selectedImage: DocMetaDataDto[], type = 'image') {
        if (selectedImage?.length) {

            if (type === 'image') {
                this.selectedImageMetadata = selectedImage[0];
                this.uploadedDocUrl = this.uploadService.fetchUploadedDocument(this.selectedImageMetadata?.idhash);
            } else {
                this.selectedLogoMetadata = selectedImage[0];
                this.uploadedLogoUrl = this.uploadService.fetchUploadedDocument(this.selectedLogoMetadata?.idhash);
            }

        } else {
            type === 'image' ?
                (this.selectedImageMetadata = this.uploadedDocUrl = null) :
                (this.selectedLogoMetadata = this.uploadedLogoUrl = null);
        }
    }

    onSubmit() {
        this.submitted = true;
        this.postForm.markAllAsTouched();

        const { type, imageAvecAction, plateformes, ...values } = this.postForm.getRawValue();

        let body: BlogPostDto;
        let rolesPayload: Role[] = [];

        const plateformesPayload = this.orginalPlateformesListe.filter(item => (plateformes as number[])?.includes(item.id));
        
        if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL'])) {
            rolesPayload = this.availableRoles.filter(item => values['listRolesCible']?.includes(item.id));
        } else {
            rolesPayload = this.availableRoles;
        }

        if (type === 'M' && !this.selectedImageMetadata) {
            if (this.requiredFieldsValid() && this.f['sujet'].valid) {
                body = {
                    type,
                    docImagePost: null,
                }
            }
        } else if (type === 'A' || type === 'I' || type === 'V' || (type === 'M' && this.selectedImageMetadata)) {
            if (
                this.requiredFieldsValid() &&
                ((type === 'I' && (!!this.selectedImageMetadata || this.imageAvecActionValid())) ||
                    (type === 'V' && this.f['videoUrl'].valid))
            ) {
                body = {
                    type: 'A',
                    docImagePost: this.selectedImageMetadata,
                };
            }

            if (type === 'M' && this.selectedImageMetadata) {
                values['sujet'] = '';

                body = {
                    type: 'A',
                    paramAffichage: 'message',
                    docImagePost: this.selectedImageMetadata,
                };
            }
        }

        if (body) {
            const selectedCategorie = this.categories.find(cat => cat.id === +values['categorie'])

            !!this.selectedLogoMetadata && (
                body = {
                    ...body,
                    plateformes: plateformesPayload,
                    listRolesCible: rolesPayload,
                    docLogoPost: this.selectedLogoMetadata
                }
            );

            body = {
                ...body, ...values, categorie: selectedCategorie,
                statut: values['statut'] || 'B',
                plateformes: plateformesPayload,
                listRolesCible: rolesPayload,
                fournisseur: this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN', 'ROLE_NATIONAL']) ? null : this.authService?.getPrincipal()?.societe
            };

            this.posteService.addOrUpdatePost(body).subscribe(_res => {
                this.modalService.dismissAll();
                if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
                    this.srvAlert.successAlt('Le post a été enregistré avec succès.', 'Post Enregistré', 'MODAL');
                } else {
                    this.srvAlert.info('Le post a été enregistré avec succès.');
                }

                this.changesApplied.emit(true);
            });
        }
    }

    requiredFieldsValid(): boolean {
        return (
            this.f['titre'].valid &&
            this.f['auteur'].valid &&
            this.f['categorie'].valid &&
            this.f['scopePublication'].valid &&
            this.f['dateFinVisibilite'].valid &&
            this.f['dateDebutVisibilite'].valid
        );
    }

    imageAvecActionValid(): boolean {
        return (
            this.f['url'].valid &&
            this.f['libelleUrl'].valid &&
            !!this.selectedImageMetadata
        );
    }

    clearOptionalFields(_ev: Event): void {
        this.f['sujet'].reset();
        this.f['url'].reset();
        this.f['videoUrl'].reset();
        this.f['libelleUrl'].reset();
        this.f['imageAvecAction'].reset(false);

        this.clearDocImageData();
    }

    clearDocImageData(): void {
        // Clear post image data
        this.uploadedDocUrl = null;
        this.selectedImageMetadata = null;

        // Clear post logo data
        this.uploadedLogoUrl = null;
        this.selectedLogoMetadata = null;
    }

    annulerPosts(idPost: string, statut: string) {
        if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
            this.userInputService.confirmAlt('Confirmer', `Êtes-vous sûr de vouloir ${(statut === 'B') ? 'supprimer' : 'annuler'} ce poste ?`).then(
                () => {
                    this.posteService.annulerPosts(idPost).subscribe((_res) => {
                        this.srvAlert.successAlt(`Le post a éte ${(statut === 'B') ? 'supprimé' : 'annulé'} avec succès.`, `Post ${(statut === 'B') ? 'supprimé' : 'annulé'}`, 'MODAL');
                        this.changesApplied.emit(true), this.dismiss();
                    });
                }, () => null);
        } else {
            this.userInputService.confirm('Confirmer', `Êtes-vous sûr de vouloir ${(statut === 'B') ? 'supprimer' : 'annuler'} ce poste ?`).then(
                () => {
                    this.posteService.annulerPosts(idPost).subscribe((_res) => {
                        this.srvAlert.info(`Le post a éte ${(statut === 'B') ? 'supprimé' : 'annulé'} avec succès.`);
                        this.changesApplied.emit(true), this.dismiss();
                    });
                }, () => null);
        }
    }
}