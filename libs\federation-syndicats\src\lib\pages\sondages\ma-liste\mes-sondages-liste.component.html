<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-6 col-sm-4">Liste des Sondages</h4>
    <div class="col-6 col-sm-8 px-1">
      <div class="row justify-content-end align-items-center">
        <!-- Add button if needed -->
      </div>
    </div>
  </div>
</div>

<div class="row d-flex m-0 px-1 sondages-container">
  <div class="card m-0 w-100 p-0 bg-white" [style.height]="'calc(100vh - 70px)'">
    <div class="card-header py-1 pl-2 mx-0 bg-white">
      <div class="row p-0 d-flex justify-content-end">
        <div class="col-8 p-0">
          <div class="col-12 col-md-8 col-lg-6 p-0 m-1 ml-auto">
            <div class="input-group picker-input">
              <input type="text" [formControl]="searchControl" placeholder="Titre de l'offre"
                class="form-control form-control-md pl-4 rounded-lg" id="groupeCritere"
                style="border-radius: 10px !important;" />
              <div class="input-group-append picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-container mt-2 w-100 p-2 sondages-list lacentrale-scroll">
      <div class="d-flex justify-content-start align-items-center">
        <h2 class="table-header" style="font-size: 20px; margin-right: 10px;">Liste des avis</h2>
        <kendo-switch [(ngModel)]="isInterested" [readonly]="false" size="medium" onLabel="Intéressé"
          offLabel="Non Intéressé" class="status-switch-align fs-soundage-switch"
          (ngModelChange)="onSwitchChange()"></kendo-switch>
      </div>

      <div class="m-0 px-1 pt-1 pb-0">
        <kendo-grid [data]="filteredData" [pageable]="{
            buttonCount: 5,
            info: true,
            type: 'numeric',
            pageSizes: pageSizes,
            previousNext: true,
            position: 'bottom'
          }" [pageSize]="navigation.pageSize" class="fs-grid" (pageChange)="pageChange($event)" [style.height]="'100%'"
          (sortChange)="sortChange($event)" [sortable]="{ mode: 'single'}" [sort]="groupeSort" [resizable]="true"
          [skip]="navigation.skip" [selectable]="true">

          <kendo-grid-column field="titre" title="Titre de l'Offre" [width]="150">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Titre de l'Offre</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
              {{dataItem?.enteteCommandeConsolideeMarche?.offre?.titre}}
            </ng-template>
          </kendo-grid-column>

          <ng-container *ngIf="isInterested; else notInterestedTemplate">
            <kendo-grid-column field="qualite" title="Qualité" [width]="80">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.qualite}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="reduction" title="Réduc." [width]="80">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.reduction}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="paiement" title="Paiement" [width]="80">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.paiement}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="livraison" title="Livraison" [width]="80">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.livraison}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="laboratoire" title="Lab." [width]="80">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.laboratoire}} <i class="bi bi-star-fill" style="color:#EAB308 !important;"></i>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="commentaire" title="Commentaire" [width]="150"></kendo-grid-column>
          </ng-container>

          <ng-template #notInterestedTemplate>
            <kendo-grid-column field="raison" class="text-wrap" title="Raison du Refus" [width]="180">
              <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Raison du Refus</span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="commentaire" class="text-wrap" title="Commentaires"
              [width]="150"></kendo-grid-column>
          </ng-template>

          <kendo-grid-column field="dateCreation" title="Date Soumission" [width]="150">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Date Soumission</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
              {{dataItem.dateCreation | date: 'dd/MM/yyyy - HH:MM'}}
            </ng-template>
          </kendo-grid-column>

          <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
            <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
              [navigation]="navigation" style="width: 100%;" (pageChange)="OnPageChange($event)">
            </wph-grid-custom-pager>
          </ng-template>

          <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun résultat trouvé.</span>
          </ng-template>
        </kendo-grid>
      </div>
    </div>
  </div>
</div>