import { Pipe, PipeTransform } from '@angular/core';
import * as moment from 'moment-timezone';
import 'moment/locale/fr';

moment.locale('fr');

@Pipe({
    name: 'timeAgo',
    pure: true
})
export class TimeAgoPipe implements PipeTransform {
    
    transform(value: Date | string | number, timeZone: string, useShortForm = true, withoutSuffix = false): string {
        let formattedVal = value ?  moment.tz(moment.utc(value), timeZone).fromNow(withoutSuffix) : '';

        if (useShortForm) {
            formattedVal = formattedVal?.replace(/\b(un|une)\b/, '1');
            formattedVal = formattedVal?.replace(/\b(jour|jours)\b/, 'J');
            formattedVal = formattedVal?.replace(/\b(heure|heures)\b/, 'h');
            formattedVal = formattedVal?.replace(/\b(minute|minutes)\b/, 'm');
            formattedVal = formattedVal?.replace('il y a', "y'a");
            formattedVal = formattedVal?.replace('quelques secondes', 'qlq s');
        }

        return formattedVal;
    }
}
