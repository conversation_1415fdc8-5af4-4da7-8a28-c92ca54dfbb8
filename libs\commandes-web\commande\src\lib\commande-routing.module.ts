import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CatalogueProduitComponent } from './pages/catalogue-produit/catalogue-produit.component';
import { ListCommandeComponent } from './pages/list-commande/list-commande.component';
import { ListNouveauxProduitsComponent } from './pages/list-nouveaux-produits/list-nouveaux-produits.component';
import { EditCommandeComponent } from './pages/edit-commande/edit-commande.component';
import { FicheProduitComponent } from './pages/fiche-produit/fiche-produit.component';
import { BonCommandeComponent } from './pages/bon-commande/bon-commande.component';
import { BlCommandeComponent } from './pages/bl-commande/bl-commande.component';
import { ListCommandesNormalesComponent } from './pages/list-commandes-normales/list-commandes-normales.component';
import { AuthoritiesGuard, CanDeactivateGuard, ServiceOptionsGuard } from '@wph/web/shared';
import { AlerteProduitsComponent } from './pages/alerte-produit/alerte-produits.component';
import { ReclamationsComponent } from './pages/reclamation/reclamations.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'accueil'
  },
  {
    path: 'accueil',
    loadChildren: () => import('@wph/web/accueil/pages')
      .then(m => m.WebAccueilPagesModule)
  },

  {
    path: 'account',
    loadChildren: () => import('@wph/web/account/pages')
      .then(m => m.WebAccountPagesModule)
  },

  {
    path: 'catalogue-produit',
    title: 'CommandeWeb: Catalogue Produit',
    component: CatalogueProduitComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard],
    data: { authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'] }
  },
  {
    path: 'Alerte-produit',
    title: 'CommandeWeb: Alerte Produit',
    component: AlerteProduitsComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard],
    data: { authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT', 'ROLE_AGENT_ACHAT'] }
  },

  {
    path: 'reclamation',
    title: 'CommandeWeb: Réclamations',
    component: ReclamationsComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard], // , ServiceOptionsGuard
    data: { 
      // serviceOptions: ['RECLAMATION'],
      authorities: ['ROLE_AGENT_POINT_VENTE'] } // , 'ROLE_ASSISTANT'
  },

  {
    path: 'reclamation/fournisseur',
    title: 'CommandeWeb: Réclamations',
    component: ReclamationsComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard,],
    data: { 
      authorities: ['ROLE_AGENT_FOURNISSEUR'] }
  },

  {
    path: 'list-commandes',
    title: 'CommandeWeb: Liste Commandes',
    component: ListCommandeComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard],
    data: { authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'] }
  },

  {
    path: 'list-nouveaux-produits',
    title: 'CommandeWeb: Nouveaux Produits',
    component: ListNouveauxProduitsComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard],
    data: { authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'] }
  },

  {
    path: 'add-commande/:id',
    title: 'CommandeWeb: Ajouter Commande',
    component: EditCommandeComponent,
    pathMatch: 'full',
    canDeactivate: [CanDeactivateGuard],
    canActivate: [AuthoritiesGuard, ServiceOptionsGuard],
    data: {
      serviceOptions: ['PASSER_COMMANDE'],
      authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']
    }
  },

  {
    path: 'edit-commande/:id',
    title: 'CommandeWeb: Modifier Commande',
    component: EditCommandeComponent,
    pathMatch: 'full',
    canDeactivate: [CanDeactivateGuard],
    canActivate: [AuthoritiesGuard, ServiceOptionsGuard],
    data: {
      serviceOptions: ['PASSER_COMMANDE'],
      authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']
    }
  },

  {
    path: 'edit/:id',
    title: 'CommandeWeb: Modifier Commande',
    component: EditCommandeComponent,
    pathMatch: 'full',
    canDeactivate: [CanDeactivateGuard],
    canActivate: [AuthoritiesGuard, ServiceOptionsGuard],
    data: {
      serviceOptions: ['PASSER_COMMANDE'],
      authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']
    }
  },

  {
    path: 'fiche-produit/:id/:type',
    title: 'CommandeWeb: Fiche Produit',
    component: FicheProduitComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard],
    data: { authorities: ['ROLE_AGENT_ACHAT', 'ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'] }
  },

  {
    path: 'bon-commande/:id',
    title: 'CommandeWeb: Bon de Commande',
    component: BonCommandeComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard],
    data: {
      authorities: [
        'ROLE_ASSISTANT',
        'ROLE_AGENT_POINT_VENTE',
        'ROLE_AGENT_FOURNISSEUR',
        'ROLE_AGENT_COMMERCIAL'
      ]
    }
  },

  {
    path: 'bl-commande/:id',
    title: 'CommandeWeb: Bl de Commande',
    component: BlCommandeComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard],
    data: {
      authorities: [
        'ROLE_ASSISTANT',
        'ROLE_AGENT_POINT_VENTE',
        'ROLE_AGENT_FOURNISSEUR',
        'ROLE_AGENT_COMMERCIAL'
      ]
    }
  },

  {
    path: 'list/normales',
    title: 'CommandeWeb: Commandes Normales',
    component: ListCommandesNormalesComponent,
    pathMatch: 'full',
    canActivate: [AuthoritiesGuard],
    data: { authorities: ['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL'] }
  },

  {
    path: 'autres',
    title: 'CommandeWeb: Factures et Réclamations',
    loadChildren: () => import('@wph/commandes-web/factures-et-reclamations')
      .then(m => m.CommandesWebFacturesEtReclamationsModule)
  },
  // ? WILD CARD
  {
    path: '**',
    redirectTo: 'accueil'
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CommandeRoutingModule { }
