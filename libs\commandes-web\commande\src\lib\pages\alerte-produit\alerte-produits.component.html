<div id="list-nouveaux-produits" class="p-0">
  <!-- Start Of Header -->
  <div class="rowline mb-0">
    <div class="page-title-box row align-items-center">
      <h4 class="page-title fw-4 ps-2 col d-md-block d-none">Alerte Produits</h4>

      <div class="col px-0 py-2 mt-md-0 mt-1 py-md-0">
        <div class="row justify-content-end align-items-center">
          <button *jhiHasAnyAuthority="['ROLE_AGENT_ACHAT']" (click)="openAjouterLigneModal(AjoutLigneModal, 'md')"
            class="btn btn-sm btn-warning text-white rounded-pharma mx-1">
            <i class="mdi mdi-plus"></i> Saisir un nouveau
          </button>
          <button (click)="openModal(filterProduitModal, 'md')" class="btn btn-sm btn-info rounded-pharma mx-1">
            <i class="mdi mdi-filter-variant"></i> Filtrer
          </button>

        </div>
      </div>
    </div>
  </div>
  <!-- END HEADER -->
  
  <div class="card" style="height: calc(100vh - 123px);">
    <ul ngbNav #parentNav="ngbNav" [(activeId)]="parentTabId" (navChange)=" onParentTabChange($event.nextId)"
      class="nav-tabs bg-nav-pills mb-2 bg-white">
      <li [ngbNavItem]="1" >
        <a ngbNavLink>
          <i class="mdi mdi-package-down d-md-none d-block fs-4"></i>
          <span class="d-none d-md-block px-4">Produits rentrants</span>
        </a>
        <ng-template ngbNavContent>
          <ng-container *ngTemplateOutlet="cards"></ng-container>
        </ng-template>
      </li>
      <li [ngbNavItem]="2" >
        <a ngbNavLink>
          <i class="mdi mdi-package-up d-md-none d-block fs-4"></i>
          <span class="d-none d-md-block px-4">Lancement Produits</span>
        </a>
        <ng-template ngbNavContent>
          <ng-container *ngTemplateOutlet="cards"></ng-container>
        </ng-template>
      </li>
    </ul>
    <div class="px-1 col-12 mb-2">
      <div [ngbNavOutlet]="parentNav"></div>
    </div>
  </div>
  <!-- ---------------------------- Tabs Section ----------------------------- -->

  <ng-template #cards>
    <div class="card">
      <ul ngbNav  #childNav="ngbNav" [(activeId)]="activeTabId" (navChange)=" onTabChange($event.nextId)"
        class="nav-tabs bg-nav-pills mb-2 bg-white">
        <li [ngbNavItem]="6"  >
          <a ngbNavLink (click)="initSearch()" >
            <i class="mdi mdi-calendar-month d-md-none d-block fs-4"></i>
            <span class="d-none d-md-block px-4">Ce mois</span>
          </a>
          <ng-template ngbNavContent>
            <ng-container *ngTemplateOutlet="TabData"></ng-container>
          </ng-template>
        </li>
        <li [ngbNavItem]="5" >
          <a ngbNavLink >
            <i class="mdi md  ²²²i-calendar-week d-md-none d-block fs-4"></i>
            <span class="d-none d-md-block px-4">Cette semaine</span>
          </a>
          <ng-template ngbNavContent>
            <ng-container *ngTemplateOutlet="TabData"></ng-container>
          </ng-template>
        </li>
        <li [ngbNavItem]="4" >
          <a ngbNavLink >
            <i class="mdi mdi-calendar-today d-md-none d-block fs-4"></i>
            <span class="d-none d-md-block px-4">Aujourd’hui</span>
          </a>
          <ng-template ngbNavContent>
            <ng-container *ngTemplateOutlet="TabData"></ng-container>
          </ng-template>
        </li>
  
      
        <li [ngbNavItem]="3" >
          <a ngbNavLink>
            <i class="mdi mdi-tune d-md-none d-block fs-4"></i>
            <span class="d-none d-md-block px-4">Personnaliser</span>
          </a>
          <ng-template ngbNavContent>
            <ng-container *ngTemplateOutlet="TabData"></ng-container>
          </ng-template>
        </li>
      </ul>
      <div class="px-0 col-12 mb-2">
        <div [ngbNavOutlet]="childNav"></div>
      </div>
    </div>
  </ng-template>
  <ng-template #TabData>
    <div class="card">

      <div class="left">
        <div class="search input-group picker-input">
          <input type="text" id="designation" placeholder="Recherche ..." class="form-control pl-4"
            [formControl]="localSearchData" (keydown.enter)="goSearch()" />

          <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
        </div>
      </div>
      <kendo-grid [data]="filteredData" (pageChange)="pageChange($event)" [pageSize]="navigation.pageSize"
        [skip]="navigation.skip" [pageable]="{
                buttonCount: 5,
                info: true,
                type: 'numeric',
                pageSizes: pageSizes,
                previousNext: true,
                position: 'bottom'
              }" [sortable]="{mode: 'single'}" [sort]="gridSort" (sortChange)="gridSortChange($event)"
        [groupable]="false" [reorderable]="true" [resizable]="true" style="height: calc(100vh - 295px)">

        <kendo-grid-column media="(max-width: 768px)" title="Liste Nouveaux Produits">
          <ng-template kendoGridCellTemplate let-dataItem>
            <!---  Mobile Column Template  --->
            <dl class="pt-1">
              <dt class="my-2 limited-width">Date Création: <span>{{ dataItem.dateCreation | momentTimezone: "yyyy-MM-DD
                  HH:mm" : "Africa/Casablanca" }}</span></dt>

              <dt class="my-2 limited-width"><span class="text-wrap m-0 p-0"><b>Libellé:</b> {{ dataItem?.proLibelle }}</span></dt>

              <dt class="my-2 limited-width">PPH: <span>{{ dataItem?.pph | number: "1.2-2":"fr-FR" }}</span>
              </dt>

              <dt class="my-2 limited-width">PPV: <span>{{ dataItem?.ppv | number: "1.2-2":"fr-FR" }}</span>
              </dt>

              <dt class="my-2 limited-width">Fabriquant: <span>{{ dataItem?.produitFournisseur?.libelleLabo }}</span>
              </dt>

              <dt class="action-btns">
                <div class="d-flex row mt-2 mx-0 justify-content-start">
                  <button *jhiHasAnyAuthority="['ROLE_AGENT_ACHAT']" class="circle circle-alt btn btn-danger text-white my-2"
                    title="Supprimer" (click)="deleteLine(dataItem.id); $event.stopPropagation()">
                    <i class="mdi mdi-delete-forever"></i>
                  </button>

                  <button (click)="navige(dataItem?.produitFournisseur?.id)" title="Détails"
                    class="circle circle-alt btn btn-warning text-white">
                    <i class="mdi mdi-eye"></i>
                  </button>
                </div>
              </dt>
            </dl>
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="dateCreation" [width]="80">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap">Date Création</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ dataItem?.dateCreation | momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="proLibelle" title="Libellé" class="text-wrap"
          [width]="120"></kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="pph" title="PPH" [width]="60" [resizable]="false">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="text-right">
              {{ dataItem?.pph | number: "1.2-2":"fr-FR" }}
            </div>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="ppv" title="PPV" [width]="60" [resizable]="false"
          filter="boolean">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="text-right">
              {{ dataItem?.ppv | number: "1.2-2":"fr-FR" }}
            </div>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="libelleLabo" class="text-wrap" title="Fabriquant" [width]="80"
          [resizable]="false" filter="numeric">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.produitFournisseur?.libelleLabo }}
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" title="Action" [width]="80" [sortable]="false" [resizable]="false"
          class="text-center">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <button *jhiHasAnyAuthority="['ROLE_AGENT_ACHAT']" class="circle btn btn-danger text-white"
              title="Supprimer" (click)="deleteLine(dataItem.id); $event.stopPropagation()">
              <i class="mdi mdi-delete-forever pointer"></i>
            </button>
            <button (click)="navige(dataItem?.produitFournisseur?.id)" title="Détails"
              class="circle btn btn-warning text-white mx-1">
              <i class="mdi mdi-eye"></i>
            </button>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
          pagerItemsPerPage="éléments par page"></kendo-grid-messages>

        <ng-template kendoGridNoRecordsTemplate>
          <span>Aucun résultat trouvé.</span>
        </ng-template>
      </kendo-grid>
    </div>
  </ng-template>

</div>

<!-- Start filter modal-->
<ng-template #filterProduitModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Filtrer</h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <i class="mdi mdi-close"></i>
    </button>
  </div>

  <form [formGroup]="filterProduitForm" (ngSubmit)="recherche()">
    <div class="p-2">
      <div class="form-group">
        <div class="row p-1">


          <div class="col-md-6 mb-3">
            <label for="dateDu" class="col-12 px-0 col-form-label text-left">Date Création Début</label>
            <div class="input-group">
              <input type="text" formControlName="dateDu" class="form-control form-control-md"
                class="form-control pr-4 form-control-md b-radius bg-white" id="dateDu" ngbDatepicker
                #drange1="ngbDatepicker" (click)="drange1.toggle()" placeholder="jj/mm/aaaa">

              <div class="input-group-append">
                <button class="btn btn-md btn-light text-dark btn-outline-light calendar" (click)="drange1.toggle()"
                  type="button">
                  <i class="mdi mdi-calendar"></i>
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-3">
            <label for="dateAu" class="col-12 px-0 col-form-label text-left">Date Création Fin</label>

            <div class="input-group">
              <input [readOnly]="true" (click)="drange2.toggle()" type="text" name="dateAu" id="dateAu"
                formControlName="dateAu" ngbDatepicker #drange2="ngbDatepicker"
                class="form-control pr-4 form-control-md b-radius bg-white" placeholder="jj/mm/aaaa">

              <div class="input-group-append">
                <button class="btn btn-md btn-light text-dark btn-outline-light calendar" (click)="drange2.toggle()"
                  type="button">
                  <i class="mdi mdi-calendar"></i>
                </button>
              </div>
            </div>
          </div>


          <div class="col-12 mb-3">
            <label for="typeAlert" class="col-sm-6 form-label p-0">Type Alerte</label>

            <div class="input-group picker-input">
              <select2 formControlName="typeAlert" hideSelectedItems="true" style="width: 100%;"
                class="form-control-sm w-100" multiple="false" [data]="listTypeAlert"></select2>
            </div>
          </div>



        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light">Fermer</button>
      <button type="button" class="btn btn-secondary text-white" (click)="vider()">Vider</button>
      <button type="button" type="submit" class="btn btn-primary ml-1 text-white">Rechercher</button>
    </div>
  </form>

</ng-template>



<!-- Start AjoutLigne modal-->
<ng-template #AjoutLigneModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Nouveau entrant / lancement </h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <i class="mdi mdi-close"></i>
    </button>
  </div>

  <form [formGroup]="AjouterLigneForm" (ngSubmit)="AjouterLigne()">
    <div class="p-2">
      <div class="form-group">
        <div class="row p-1">

          <div class="col-12 mb-3">
            <label for="designation" class="col-sm-6 form-label p-0">Chercher un produit</label>
            <div id="client-picker-input" class=" animated mx-0 px-0 my-1 col-auto input-group picker-input">
              <input type="text" style="padding-left: 30px !important;" id="designation" placeholder="Nouvelle ligne"
                class=" form-control form-control-md" formControlName="designationProduit" [ngbTypeahead]="$any(search)"
                (selectItem)="selectedProdact($event)" [resultFormatter]="formatter" [inputFormatter]="formatter"
                [editable]="false" appTypeaheadScrollFix />
              <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>

            </div>

          </div>
          <div class="col-12 mb-3">
            <label for="typeAlert" class="col-sm-6 form-label p-0">Type Alerte</label>

            <div class="input-group picker-input">
              <select2 formControlName="typeAlert" hideSelectedItems="true" style="width: 100%;"
                class="form-control-sm w-100" multiple="false" [data]="listTypeAlert"></select2>
            </div>
          </div>



        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light">Fermer</button>
      <button type="button" class="btn btn-secondary text-white" (click)="viderAjoutLigne()">Vider</button>
      <button type="button" type="submit" class="btn btn-primary ml-1 text-white">Ajouter</button>
    </div>
  </form>

</ng-template>