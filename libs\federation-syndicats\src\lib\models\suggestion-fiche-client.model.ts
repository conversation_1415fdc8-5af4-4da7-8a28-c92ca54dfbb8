import { Pagination } from "@wph/data-access";
import { User } from "@wph/shared";

export class SuggestionFicheClient {
    id: number;
    raisonSociale: string;
    civilite: string;
    codePostal: string;
    email: string;
    nomResponsable: string;
    adresse: string;
    adresse2: string;
    localite: string;
    ville: string;
    adresseAr: string;
    adresseAr2: string;
    villeRc: string;
    telephone: string;
    gsm1: string;
    gsm2: string;
    numCin: string;
    numIce: string;
    numIf: string;
    numRc: string;
    numPatente: string;
    numRib: string;
    numInpe: string;
    numCnss: string;
    numCompte: string;
    fax: string;
    siteWeb: string;
    longitude: number;
    latitude: number;
    createdAt: Date;
    createdById: User;
    updatedAt: Date;
    updatedById: User;
    deletedAt: Date;
    deletedById: User;

    dateTraitement: Date;
    userSuggereurId: number;
    userTraitantId: number;
    clientCibleId: number;
    etatSuggestion: string;
    motifRejet: string;
}

export interface SearchSuggestionFicheClient extends Pagination {
    content?: SuggestionFicheClient[];
}