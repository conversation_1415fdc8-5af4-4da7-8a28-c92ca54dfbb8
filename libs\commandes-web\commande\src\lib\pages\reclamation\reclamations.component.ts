// src/app/components/reclamations/reclamations.component.ts

import { <PERSON>mponent, OnInit, OnDestroy, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CellClickEvent, GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import {
  CompositeFilterDescriptor,
  SortDescriptor,
} from '@progress/kendo-data-query';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import * as moment from 'moment';
import { Reclamation } from '../../models/reclamation';
import { ReclamationsService } from '../../services/reclamations.service';
import { ReclamationsServiceTest } from '../../services/reclamations.service.test';
import { FsOffreService } from '@wph/federation-syndicats';
import { AuthService } from '@wph/core/auth';
import { AlertService, Principal } from '@wph/shared';
import { ReclamationCriteria } from '../../models/reclamation';
import { Pagination } from '../../models/reclamation';
import { ReclamationList } from '../../models/reclamation';
import { getDynamicPageSize, UserInputService } from '@wph/web/shared';
import { ReclamationBadgeService } from '../../services/reclamation-badge.service';

@Component({
  selector: 'app-reclamations',
  templateUrl: './reclamations.component.html',
  styleUrls: ['./reclamations.component.scss'],
})
export class ReclamationsComponent implements OnInit, OnDestroy {
  gridView: GridDataResult;
  gridSort: SortDescriptor[] = [];

  searchParams: ReclamationCriteria;
  navigation: Pagination = {
    skip: 0,
    pageSize: 15,
  };
  pageSizes: number[] = [5, 10, 15, 20];

  filterForm: FormGroup;
  addReclamationForm: FormGroup;
  editReclamationForm: FormGroup;
  replyReclamationForm: FormGroup;

  principal: Principal;
  subscriptions: Subscription = new Subscription();

  codeSite: number;

  types = [];
  statuses = [
    { label: 'Nouvelle', value: 'N', id: 'N' }, // Added id for select2
    { label: 'En Cours', value: 'R', id: 'R' },
    { label: 'Traité', value: 'T', id: 'T' },
    { label: 'Sans suite', value: 'S', id: 'S' },
  ];

  managerStatuses = [];
  modalRef: any;
  currentReclamation: Reclamation;
  isFournisseur: boolean;

  constructor(
    private reclamationsService: ReclamationsService,
    // private reclamationsService: ReclamationsServiceTest,
    private modalService: NgbModal,
    private fsOffreService: FsOffreService,
    public authService: AuthService,
    private fb: FormBuilder,
    private userInputService: UserInputService,
    private alertService: AlertService,
    private cdr: ChangeDetectorRef,
    private reclamationBadgeService: ReclamationBadgeService
  ) {
    this.isFournisseur = this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']);
  }

  ngOnInit() {
    // Get the principal object
    this.principal = this.authService.getPrincipal();
    this.subscriptions.add(
      this.authService.principal$.subscribe((principal) => {
        this.principal = principal;
      })
    );

    // Get codeSite from localStorage
    const targetGrossiste = JSON.parse(
      sessionStorage.getItem('TARGET_GROSSISTE')
    );
    this.codeSite = targetGrossiste?.noeud?.codeSite;

    this.setPageSize();
    this.initializeForms();
    this.getTypeReclamation();
    this.initSearch();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  setPageSize(currentHeight?: number): void {
    const dynamicSize = getDynamicPageSize(currentHeight);

    if (dynamicSize !== this.navigation.pageSize) {
      this.navigation.pageSize = dynamicSize;

      this.pageSizes.push(dynamicSize);
      this.pageSizes = this.pageSizes.sort((a, b) => a - b);

      currentHeight && this.initSearch();
    }
  }

  initializeForms() {
    const momentDateDu = moment();
    momentDateDu.subtract(7, 'days');
    momentDateDu.set('hours', 0), momentDateDu.set('minutes', 0), momentDateDu.set('seconds', 0);

    this.filterForm = this.fb.group({
      type: [[]],  // Will hold array of selected values
      statut: [['N','R']], // Will hold array of selected values
      // dateDu: [moment(today)],
      dateDu: [], // momentDateDu
      dateAu: [], //moment()
    });

    this.addReclamationForm = this.fb.group({
      typeReclamationId: [null, Validators.required],
      message: [null, Validators.required],
    });

    this.editReclamationForm = this.fb.group({
      id: [null],
      typeReclamationLabel: [{ value: null }],  // , disabled: true
      typeReclamationId: [{ value: null, disabled: this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']) }],
      message: [null, Validators.required],
    });

    this.replyReclamationForm = this.fb.group({
      id: [null],
      reponse: [null],
      statut: [null],
      typeReclamationId: [{ value: null, disabled: true }],
      typeReclamationLabel: [{ value: '', disabled: true }],
      message: [{ value: '', disabled: true }]
    });
  }

  getTypeReclamation() {
    this.fsOffreService
      .getDomaineEnumeration('type_reclamation')
      .subscribe((res) => {
        this.types = res?.map((item) => ({
          id: item?.code,     // Required for select2
          text: item?.label,  // Required for select2
          label: item?.label,
          value: item?.code,
          fullObject: item,
        }));
      });
  }

  initSearch() {
    const formValues = this.filterForm.getRawValue();
    
    // Only include non-null values in searchParams
    const searchParams: ReclamationCriteria = {
        codeSite: this.codeSite
    };

    if (formValues.type?.length) {
        searchParams.type = formValues.type
            .map(typeId => {
                const selectedType = this.types.find(t => t.value === typeId);
                return selectedType?.fullObject;
            })
            .filter(t => t);
    }

    if (formValues.statut?.length) {
        searchParams.statut = formValues.statut;
    }

    if (formValues.dateDu) {
        searchParams.dateDu = moment(formValues.dateDu).format('YYYY-MM-DD HH:mm:ss');
    }

    if (formValues.dateAu) {
        searchParams.dateAu = moment(formValues.dateAu).format('YYYY-MM-DD HH:mm:ss');
    }

    this.searchParams = new ReclamationCriteria(searchParams);

    this.gridView = {
        data: [],
        total: 0,
    };

    this.reclamationsService
        .getAllReclamation(this.searchParams, {
            ...this.navigation,
            skip: this.getPageNumber(
                this.navigation.skip,
                this.navigation.pageSize
            ),
            pageSize: this.navigation.pageSize,
        })
        .subscribe((res: ReclamationList) => {
            this.gridView = {
                data: res.content,
                total: res.totalElements,
            };
        });
  }
  getPageNumber(skip, pageSize) {
    return Math.floor(skip / pageSize);
  }

  pageChange(event: PageChangeEvent): void {
    if (
      event.skip !== this.navigation.skip ||
      event.take !== this.navigation.pageSize
    ) {
      this.navigation.skip = event.skip;
      this.navigation.pageSize = event.take;

      this.initSearch();
    }
  }

  gridSortChange(sort: SortDescriptor[]): void {
    this.gridSort = sort;

    if (this.gridSort && this.gridSort.length > 0 && this.gridSort[0].dir) {
      this.navigation.sortField = sort[0].field;
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
    }

    this.initSearch();
  }

  openAddReclamationModal(content) {
    this.addReclamationForm.reset();
    this.modalService.open(content, {
      size: 'lg',
      backdrop: 'static',
      centered: true,
    });
  }

  openEditReclamationModal(content, reclamation: Reclamation) {
    // Find the type object
    const selectedType = this.types.find(
      t => t.value === reclamation.typeReclamationId || 
           t.value === reclamation.typeReclamation?.code
    );
    
    this.editReclamationForm.patchValue({
        id: reclamation.id,
        // typeReclamationId: reclamation.typeReclamationId,
        typeReclamationId: selectedType?.value,
        typeReclamationLabel: reclamation.typeReclamation?.label || this.getTypeReclamationLabel(reclamation.typeReclamationId),
        message: reclamation.message || '',
        reponse: reclamation.reponse || '',
    });

    // If ROLE_AGENT_FOURNISSEUR, disable the type selection
    if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) {
      this.editReclamationForm.get('typeReclamationId').disable();
    } else {
        this.editReclamationForm.get('typeReclamationId').enable();
    }

    this.modalService.open(content, {
        size: 'lg',
        backdrop: 'static',
        centered: true,
    });
  }

  openReplyReclamationModal(content, reclamation: Reclamation) {
    const selectedType = this.types.find(
      t => t.value === reclamation.typeReclamationId
    );
  
    this.replyReclamationForm.patchValue({
      id: reclamation.id,
      typeReclamationId: reclamation.typeReclamationId,
      typeReclamationLabel: selectedType?.label || reclamation.typeReclamation?.label,
      message: reclamation.message,
      statut: reclamation.statut === 'R' ? null : reclamation.statut,
      reponse: reclamation.reponse,
    });
    
    this.managerStatuses = this.statuses.filter(s => s.value === 'S' || s.value === 'T');
    
    this.modalService.open(content, { size: 'lg', backdrop: 'static', centered: true });
  }

  openViewReclamationModal(event:CellClickEvent,content) {
    if(event.column?.title === "Actions"){
      return;
    }
    
    const reclamation = event.dataItem as Reclamation;
    this.modalRef?.close();
    this.currentReclamation = reclamation;
    this.cdr.detectChanges(); // Trigger change detection
    this.modalRef = this.modalService.open(content, {
      size: 'lg',
      backdrop: true,
      centered: true,
      keyboard: true,
    });
  }


  openFilterModal(content: TemplateRef<any>) {
    this.modalService.open(content, { size: 'md', centered: true });
  }

  onAddReclamation() {
    if (this.addReclamationForm.valid) {
      const selectedType = this.types.find(
        (t) =>
          t.value === this.addReclamationForm.get('typeReclamationId').value
      );

      const reclamation: Reclamation = {
        ...this.addReclamationForm.value,
        dateCreation: moment().format('YYYY-MM-DD HH:mm:ss'),
        statut: 'N',
        typeReclamation: selectedType?.fullObject, // Send full object
        clientId: this.principal?.societe?.id,
        codeClient: this.principal?.societe?.id,
        ville: this.principal?.societe?.ville,
        raisonSociale: this.principal?.societe?.raisonSociale,
        nomPharmacien: `${this.principal?.firstname} ${this.principal?.lastname}`,
        codeSite: this.codeSite,
        managerId: null, // Set manager_id if applicable
      };

      this.reclamationsService.createOrEdit(reclamation).subscribe(() => {
        this.initSearch();
        this.modalService.dismissAll();
        // Force update the badge count
        this.reclamationBadgeService.forceUpdate(this.codeSite);
      });
    }
  }

  onEditReclamation() {
    if (this.editReclamationForm.valid) {
      const selectedType = this.types.find(
        (t) => t.value === this.editReclamationForm.get('typeReclamationId').value
      );
  
      const updatedReclamation: Reclamation = {
        id: this.editReclamationForm.get('id').value,
        message: this.editReclamationForm.get('message').value,
        dateModification: moment().format('YYYY-MM-DD HH:mm:ss'),
        statut: this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']) ? 'N' : 'R',
        typeReclamation: selectedType?.fullObject,
        typeReclamationId: selectedType?.value
    };
  
      this.reclamationsService.createOrEdit(updatedReclamation).subscribe(() => {
        this.initSearch();
        this.modalService.dismissAll();
        // Force update the badge count
        this.reclamationBadgeService.forceUpdate(this.codeSite);
      });
    }
  }

  onReplyReclamation() {
    if (this.replyReclamationForm.valid) {
      const formValue = this.replyReclamationForm.getRawValue();
      const selectedType = this.types.find(
        (t) => t.value === formValue.typeReclamationId
      );

      if (formValue.statut == 'T' || formValue.statut == 'S' ){
        if(!formValue.reponse || !formValue.reponse.trim()){
          this.alertService.error('Veuillez saisir une réponse', 'MODAL');
          return;
        }
      }
  
      const replyData: Reclamation = {
        id: formValue.id,
        reponse: formValue.reponse,
        message: formValue.message,
        typeReclamation: selectedType?.fullObject,
        // statut: ( formValue.statut && formValue.statut != 'N' ) || 'R', // If no status selected, default to 'R' (En cours)
        statut: (formValue.statut && formValue.statut !== 'N') ? formValue.statut : 'R', // If no status selected, default to 'R' (En cours)
        dateModification: moment().format('YYYY-MM-DD HH:mm:ss'),
      };
  
      this.reclamationsService.createOrEdit(replyData).subscribe(() => {
        this.initSearch();
        this.modalService.dismissAll();
        // Force update the badge count
        this.reclamationBadgeService.forceUpdate(this.codeSite);
      });
    }
  }

  onFilterReclamations() {
    this.navigation.skip = 0;
    this.initSearch();
    this.modalService.dismissAll();
  }

  canEdit(reclamation: Reclamation): boolean {
    return (
      this.authService.hasAnyAuthority([
        'ROLE_AGENT_POINT_VENTE',
        'ROLE_ASSISTANT',
      ]) && reclamation.statut === 'N'
    );
  }

  canDelete(reclamation: Reclamation): boolean {
    return this.canEdit(reclamation);
  }

  canReply(reclamation: Reclamation): boolean {
    return this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']);
  }

  // deleteReclamation(id: number) {
  //   this.userInputService
  //     .confirm(null, 'Êtes-vous sûr de vouloir annuler cette réclamation ?')
  //     .then(
  //       () => {
  //         this.reclamationsService.cancelOrCheck(id).subscribe(() => {
  //           this.initSearch();
  //         });
  //       }, () => null
  //     );
  // }

  // changeStatutToSansSuiteOrEnCours(id: number) {
  //   this.userInputService
  //     .confirm(null, 'Êtes-vous sûr de vouloir changer le statut?')
  //     .then(
  //       () => {
  //         this.reclamationsService.cancelOrCheck(id).subscribe(() => {
  //           this.initSearch();
  //         });
  //       }, () => null
  //     );
  // }

  cancelReclamation(id: number) {
    this.userInputService
      .confirm(null, 'Êtes-vous sûr de vouloir annuler cette réclamation ?')
      .then(
        () => {
          this.reclamationsService.cancelReclamation(id).subscribe(() => {
            this.initSearch();
            // Force update the badge count
            this.reclamationBadgeService.forceUpdate(this.codeSite);
          });
        },
        () => null
      );
  }

  checkReclamation(id: number) {
    this.userInputService
      .confirm(
        null,
        'Êtes-vous sûr de vouloir mettre en cours cette réclamation ?'
      )
      .then(
        () => {
          this.reclamationsService.checkReclamation(id).subscribe(() => {
            this.initSearch();
          });
        },
        () => null
      );
  }

  getTypeReclamationLabel(value: string): string {
    const type = this.types.find((t) => t.value === value);
    return type ? type.label : value;
  }

  getStatutLabel(value: string): string {
    const status = this.statuses.find((s) => s.value === value);
    return status ? status.label : value;
  }

  reload() {
    this.initSearch();
  }

  vider() {
    const today = new Date().setHours(12, 0, 0);
    
    this.filterForm.patchValue({
      type: [],
      statut: [],
      // dateDu: moment(today),
      dateDu: null,
      dateAu: null
    });
    
    this.navigation.skip = 0;
    this.initSearch();
  }

  truncateText(text: string, limit: number = 25): string {
    if (!text) return '';
    return text.length > limit ? `${text.substring(0, limit)}...` : text;
  }
}