// 
// dropdown.scss
//

.dropdown-menu {
    box-shadow: $shadow;
}

// Dropdown Animated (Custom)
.dropdown-menu-animated {
    display: block;
    visibility: hidden;
    opacity: 0;
    transition: all 300ms ease;
    margin-top: 20px !important;
}

.show {
    > .dropdown-menu {
        visibility: visible;
        opacity: 1;
        margin-top: 0 !important;
    }
}


// Dropdown Large (Custom)
.dropdown-lg {
    width: $dropdown-lg-width;
}


@include media-breakpoint-down(sm) {
    .dropdown-lg {
        width: 200px !important;
    }
}


// Dropdown arrow hide
.arrow-none {
    &:after {
        display: none;
    }
}

#client-picker-input {
    .dropdown-menu {
        width: fit-content !important;
        z-index: 99;

        max-height: 300px;
        overflow-y: auto;
      
        scrollbar-width: thin !important;
        scrollbar-color: var(--fs-secondary-light) white !important;
        border-radius: var(--winoffre-base-border-radius)
    }

    .dropdown-item {
        display: flex !important;
        justify-content: space-between !important;
        padding-left: 10px !important;
        padding-right: 10px !important;
    }
}