import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable()
export class LoaderService {
  private loaderSubject = new Subject<boolean>();
  private requestCount: number = 0;

  loaderState = this.loaderSubject.asObservable();

  constructor() {}

  show() {
    this.requestCount++; 
    (this.requestCount === 1) && this.loaderSubject.next(true);
  }

  hide() {
    this.requestCount--;
    (this.requestCount === 0) && this.loaderSubject.next(false);
  }
}
