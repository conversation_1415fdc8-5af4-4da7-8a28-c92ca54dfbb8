import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'wph-detail-actualite',
  templateUrl: './detail-actualite.page.html',
  styleUrls: ['./detail-actualite.page.scss'],
})
export class DetailActualitePage implements OnInit {

  postItem:any = null;

  constructor(private router: Router) {
    if (router.getCurrentNavigation().extras.state) {
       this.postItem = this.router.getCurrentNavigation().extras.state;
    }
    console.log(this.postItem);
  }

  ngOnInit() {
  }


}
