/* Custom Styles */
.custom-container {
  padding: 2rem; /* Adds padding around the container */
  background-color: #f8f9fa; /* Optional: Light background color */
  border-radius: 8px; /* Rounded corners for the container */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Soft shadow around the container */
}

.custom-card {
  padding: 1rem; /* Adds padding inside each card */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Soft shadow around the cards */
  border-radius: 8px; /* Optional: Rounded corners for the cards */
}

/* Optional: You can add hover effect to cards */
.custom-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Styling for vertical line in header */
.vertical-line {
  border-left: 2px solid #000;
  height: 30px; /* Adjust the height of the line */
}

/* Add padding for card headers, body, and footer */
.card-header {
  padding: 1rem;
  background-color: #fff;
  border-bottom: 1px solid #ddd;
}

.card-body {
  padding: 1rem;
}

.card-footer {
  padding: 1rem;
  background-color: #f8f9fa;
  border-top: 3px solid #ddd;
}

.historique-btn {
  background-color: #d9d9d9;
  color: #6e7780;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.executer-btn:hover {
  background-color: #357a99;
}

.historique-btn:hover {
  background-color: #c4c4c4;
}

body {
  margin-top: 20px;
}
.timeline {
  border-left: 3px solid #e0e0e0;
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
  margin-left: 200px;
  max-width: 70%;
  letter-spacing: 0.2px;
  position: relative;
  line-height: 1.4em;
  font-size: 1.03em;
  padding: 50px;
  list-style: none;
  text-align: left;
}

@media (max-width: 767px) {
  .timeline {
    max-width: 98%;
    margin-left: 50px;
    padding: 25px;
  }
}

.timeline h1 {
  font-weight: 300;
  font-size: 1.4em;
}

.timeline h2,
.timeline h3 {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 10px;
}

.timeline .event {
  padding-bottom: 25px;
  margin-bottom: 25px;
  position: relative;
}

@media (max-width: 767px) {
  .timeline .event {
    padding-top: 30px;
  }
}

.timeline .event:last-of-type {
  padding-bottom: 0;
  margin-bottom: 0;
  border: none;
}

.timeline .event:before,
.timeline .event:after {
  position: absolute;
  display: block;
  top: 0;
}

.timeline .event:before {
  left: -207px;
  content: attr(data-date);
  text-align: right;
  font-weight: 100;
  font-size: 0.9em;
  min-width: 120px;
}

@media (max-width: 767px) {
  .timeline .event:before {
    left: 0px;
    text-align: left;
  }
}

.timeline .event:after {
  -webkit-box-shadow: 0 0 0 3px #3f94c1;
  box-shadow: 0 0 0 3px #3f94c1;
  left: -55.8px;
  background: #3f94c1;
  border-radius: 50%;
  height: 9px;
  width: 9px;
  content: "";
  top: 5px;
}

@media (max-width: 767px) {
  .timeline .event:after {
    left: -31.8px;
  }
}

.timeline {
  &:before {
    background-color: white !important;
  }
}
/* Status Pills */
.status-pill {
  display: inline-block;
  text-align: center;
  white-space: nowrap;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  min-width: 80px;
}

.status-en-cours {
  background-color: #9bc6f2;
  color: #094eaf;
}

.status-succes {
  background-color: #bee1cf;
  color: #28563c;
}

.status-erreur {
  background-color: #f0b0ab;
  color: #a93535;
}

.status-alerte {
  background-color: #fdf0af;
  color: #b54201;
}

/* Custom subtle shadow */
.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* Smooth transitions for interactivity */
.card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

//

/* Custom Button Styles */
.btn {
  transition: all 0.25s ease;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.btn-historique {
  background: rgba(108, 117, 125, 0.1);
  border: 1px solid rgba(108, 117, 125, 0.2);
  color: #6c757d;
}

.btn-historique:hover:not(:disabled) {
  background: rgba(108, 117, 125, 0.2);
  transform: translateY(-1px);
}

.btn-historique:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn-execute {
  background: linear-gradient(135deg, #0d6efd, #0b5ed7);
  color: white;
  border: none;
  box-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);
}

.btn-execute:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
  background: linear-gradient(135deg, #0b5ed7, #094db4);
}

/* Hover effect for both buttons */
.btn:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.btn:hover:after {
  opacity: 1;
}