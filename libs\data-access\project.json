{"name": "data-access", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/data-access/src", "prefix": "wph", "targets": {"test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/data-access"], "options": {"jestConfig": "libs/data-access/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": []}}}, "tags": []}