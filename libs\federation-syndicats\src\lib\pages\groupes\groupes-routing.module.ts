import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { SaisieGroupeComponent } from "./saisie/saisie-groupe.component";
import { ListMemberComponent } from "./membres/list-member/list-member.component";
import { SaisieMemberComponent } from "./membres/saisie-member/saisie-member.component";
import { ListeGroupeComponent } from "libs/web/shared/src/lib/components/liste/liste-groupe.component";
import { AuthoritiesGuard } from "@wph/web/shared";

const routes: Routes = [
    {
        path: 'liste',
        title: 'Groupes',
        component: ListeGroupeComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'membres',
        title: 'Membres',
        component: ListMemberComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_NATIONAL'] }
    },
    {
        path: 'membres-saisie',
        title: 'Membres <PERSON>',
        component: SaisieMemberComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_RESPONSABLE'] }
    },
    {
        path: ':id/membres-saisie',
        title: 'Membres Saisie',
        component: SaisieMemberComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_RESPONSABLE'] }
    },
    {
        path: 'saisie',
        title: 'Ajouter Groupe',
        component: SaisieGroupeComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'] }
    },
    {
        path: ':id/parametrer',
        title: 'Paramétrer',
        component: SaisieGroupeComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'] }
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class GroupesRoutingModule {}
