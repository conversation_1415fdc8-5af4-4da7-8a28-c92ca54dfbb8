import { DatePipe } from "@angular/common";
import { Component, TemplateRef, ViewChild } from "@angular/core";
import { FormGroup, FormControl, FormBuilder, Validators, ValidatorFn, AbstractControl, ValidationErrors } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CellClickEvent, GridDataResult, PageChangeEvent, SelectionEvent } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { AuthService } from "@wph/core/auth";
import { Pagination, OffresService, Offre, Fournisseur } from "@wph/data-access";
import { EnteteCommandeConsolideeMarche, EtatCommande, FederationSyndicatService, FsCommandeCriteria, FsCommandesService, GroupeEntreprise, PharmacieEntreprise, PharmacieEntrepriseCriteria } from "@wph/federation-syndicats";
import { AlertService, PlateformeService, SocieteType } from "@wph/shared";
import { ExportPdf, ExportPdfService, UserInputService, getDynamicPageSize } from "@wph/web/shared";
import { Subject, takeUntil, debounceTime, distinctUntilChanged, Observable, switchMap, of, map, catchError } from "rxjs";
import { Avis, AvisDTO, TypeAvis } from "../../../models/avis.model";
import { GroupeEntrepriseCriteria } from "../../../models/groupe-entreprise-criteria.model";

interface StatutLabelValuePair {
    label: string;
    value: EtatCommande;
}

@Component({
    selector: 'wph-ag-liste-commande-groupe',
    templateUrl: './liste-commande-groupe.component.html',
    styleUrls: ['./liste-commande-groupe.component.scss']
})
export class ListeCommandeGroupe {
    unsubscribe$: Subject<boolean> = new Subject<boolean>();

    @ViewChild('LaboEmailModal') LaboEmailModal: any;

    gridData: GridDataResult;
    displayFilter: boolean;

    searchCriteria: FsCommandeCriteria = new FsCommandeCriteria();

    filterForm: FormGroup;
    laboEmailForm: FormGroup;
    searchFilter: FormControl = new FormControl();
    startsWith: RegExp = new RegExp('^CD-\\d*$', 'i');

    exportPdfRef: ExportPdf;
    commandeSort: SortDescriptor[];
    pageSizes: number[] = [5, 10, 15, 20];
    navigation: Pagination & { originalSortField: string } = { pageSize: 15, skip: 0, originalSortField: '' };

    monGroupe: GroupeEntreprise;

    selectedFournisseurs: Fournisseur[];

    stautsLabelsValues: StatutLabelValuePair[] = [
        { "label": 'Tout', "value": null },
        { "label": "Ouverte", "value": "ACCEPTEE" },
        { "label": "Annulée", "value": "ANNULEE" },
        { "label": "Cloturée", "value": "CLOTUREE" },
        { "label": "Commandé", "value": "VALIDEE" },
        { "label": "Envoyée", "value": "ENVOYEE" },
        { "label": "En Attente", "value": "EN_ATTENTE" },
        { "label": "En Livraison", "value": "EN_LIVRAISON" },
        { "label": "Fin de saisie", "value": "FIN_SAISIE" },
        { "label": "Livrée", "value": "LIVREE" },
        { "label": "Réfusée", "value": "REFUSEE" },
    ];

    selectedKeys: any[] = [];
    temp: any;
    avis: Avis;
    feedbackSent: boolean = false;
    @ViewChild('satisfactionModal', { static: true })
    satisfactionModal: TemplateRef<any>;
    titreOffre: string;
    laboratoire: any;
    allowedEtatCommande: EtatCommande[] = [];

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private datePipe: DatePipe,
        private route: ActivatedRoute,
        private modalService: NgbModal,
        private authService: AuthService,
        private alertService: AlertService,
        private offresService: OffresService,
        private exportPdfServ: ExportPdfService,
        private userInputServ: UserInputService,
        private commandeService: FsCommandesService,
        private plateformeService: PlateformeService,
        private fedSyndicatService: FederationSyndicatService
    ) {
        this.initFilterForm();


        this.laboEmailForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
            confirmEmail: ['', [Validators.required, Validators.email, this.EmailEqualtyValidator()]]
        });

        if (!this.authService.hasAnyAuthority(['ROLE_RESPONSABLE', 'ROLE_SUPER_ADMIN'])) {
            this.allowedEtatCommande = ['ACCEPTEE', 'ENVOYEE', 'EN_LIVRAISON', 'LIVREE'];
            this.stautsLabelsValues = this.stautsLabelsValues.filter(item => this.allowedEtatCommande.includes(item.value))
        } else {
            this.allowedEtatCommande = null;
        }

        this.setForcedCriteria();
    }

    get isInactive$() {
        return this.fedSyndicatService.inactiveAccount$;
    }

    setForcedCriteria(forceEtat = true): void {
        if (this.authService.hasAnyAuthority(['ROLE_NATIONAL']) && !this.monGroupe) {
            this.searchCriteria['supporterEntreprise'] = this.authService.getPrincipal()?.societe;
        }

        if (forceEtat) {
            this.searchCriteria['etatCommande'] = this.allowedEtatCommande;
        }

        if (this.monGroupe) {
            this.searchCriteria['groupeEntreprise'] = this.monGroupe;
        }
    }

    ngOnInit(): void {
        this.setPageSize();

        const isSupporteur = this.route.snapshot.queryParams['supporterEntreprise'];
        const statut = this.route.snapshot.queryParams['statut'];

        if (statut) {
            this.filterForm.get('etatCommande').setValue(statut);
        }

        this.fedSyndicatService.getMyGroupe().then(myGroupe => {
            this.monGroupe = myGroupe;
            this.searchCriteria = new FsCommandeCriteria({
                ...this.searchCriteria,
                groupeEntreprise: myGroupe,
                etatCommande: statut ? [statut] : null,
                supporterEntreprise: isSupporteur ? this.authService.getPrincipal()?.societe : null
            });

            this.searchCommandesGroupe();
        });

        this.buildExport();
        this.listenToSearchFilterChanges();
    }

    checkIfAvisExists(cmdConsolideeId?: number): Observable<boolean> {
        const sondeurId = this.authService.getPrincipal()?.societe?.id;
        if (!sondeurId) {
            console.error('sondeurId is null');
            return of(false);
        }

        return this.commandeService
            .getBySondeurIdOffreIdCmdId(sondeurId, cmdConsolideeId)
            .pipe(
                map((avis: AvisDTO | AvisDTO[]) => {
                    // Check if the response is an array
                    if (Array.isArray(avis)) {
                        return avis.length > 0;
                    }
                    // Check if the response is a single object
                    return avis !== null;
                }),
                catchError((error) => {
                    console.error('Error fetching avis:', error);
                    return of(false);
                })
            );
    }
    initializeAvis(item: Offre) {
        this.avis = {
            commentaire: null,
            estResponsable: this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']),
            id: null,
            typeAvis: TypeAvis.Positive,
            entCmdUnitaireMarcheId: null,
            enteteCommandeConsolideeMarche: { id: item.enteteCommandeId } as EnteteCommandeConsolideeMarche,
            groupeEntreprise: this.authService.getPrincipal()?.groupe ?? null,
            sondeurEntreprise: { id: this.authService.getPrincipal()?.societe?.id } as PharmacieEntreprise,
            raison: null,
            laboratoire: 0,
            livraison: 0,
            paiement: 0,
            qualite: 0,
            reduction: 0
        };
        this.titreOffre = item.titre; // Set the title of the offer
        this.laboratoire = item.laboratoire.raisonSociale;
    }


    openSatisfactionModal(item: Offre) {
        if (this.feedbackSent) {
            this.alertService.error(`Un avis a déjà été envoyé pour cette commande.`, 'MODAL');
            return;
        }
        this.checkIfAvisExists(item?.enteteCommandeId).subscribe((avisExists) => {
            if (avisExists) {
                this.alertService.error(`Un avis a déjà été envoyé pour cette commande.`, 'MODAL');
            } else {
                this.initializeAvis(item);
                this.modalService.open(this.satisfactionModal, { centered: true });
            }
        });
    }

    soumettreSatisfactionModal(): void {
        if (this.feedbackSent) {
            this.alertService.error(`Un avis a déjà été envoyé pour cette commande.`, 'MODAL');
            return;
        }

        this.userInputServ.confirmAlt('Confirmation', `Êtes vous sûr de vouloir envoyer cet avis ?`).then(
            () => {
                this.commandeService.sauvegarderAvis(this.avis).subscribe(
                    () => {
                        this.feedbackSent = true; // Set feedbackSent flag to true
                        this.alertService.successAlt(`Votre avis a été envoyé avec succès.`, 'Avis envoyé', 'MODAL');
                    },
                    (error) => {
                        const errorMessage = error?.error?.message || 'Erreur lors de l\'enregistrement de l\'avis.';
                        this.alertService.error(errorMessage, 'MODAL');
                        console.error('Error saving avis:', error);
                    }
                );
                this.modalService.dismissAll();
            },
            () => null
        );
    }

    envoyerCommande(item: Offre, modal: any): void {
        if (this.laboEmailForm.invalid) {
            this.alertService.error("Veuillez saisir une adresse email valide", "MODAL")
            return;
        }
        if (item?.etatCommandeAchatGroupe === 'VALIDEE') {
            this.commandeService.envoyerCommandeConsolidee(item?.enteteCommandeId, this.monGroupe?.id, this.laboEmailForm.get('email').value).subscribe(res => {
                this.navigation.skip = 0;
                this.searchCommandesGroupe();
                modal?.dismiss();
                this.temp = null;

                this.alertService.successAlt(`La commande sur l'offre: <b>${item?.titre}</b> a été envoyée avec succès.`, 'Commande Envoyé ', 'MODAL');
                setTimeout(() => {
                    if (this.modalService.hasOpenModals()) {
                        this.modalService.dismissAll();
                    }
                    this.openSatisfactionModal(item);
                }, 3500);

            });
        }
    }

    OnPageChange(event: number): void {
        this.searchCommandesGroupe()

    }

    setPageSize(currentHeight?: number): void {
        const dynamicSize = getDynamicPageSize(currentHeight, 44);

        if (dynamicSize !== this.navigation.pageSize) {
            this.navigation.pageSize = dynamicSize;

            this.pageSizes.push(dynamicSize);
            this.pageSizes = this.pageSizes.sort((a, b) => a - b);

            currentHeight && this.searchCommandesGroupe();
        }
    }

    searchCommandesGroupe(): void {
        this.commandeService.searchCommandesConsolidee(this.navigation, this.searchCriteria).subscribe(res => {
            this.gridData = {
                data: res?.content,
                total: res?.totalElements
            };
            this.exportPdfRef.setData(res.content);

        });
    }

    initFilterForm(): void {
        this.filterForm = this.fb.group({
            offreur: [null],
            distributeur: [null],
            supporterEntreprise: [null],
            etatCommande: [null],
            dateDebut: [null],
            dateFin: [null],
            groupeEntreprise: [null],
        });
    }

    listenToSearchFilterChanges(): void {
        this.searchFilter.valueChanges
            .pipe(
                takeUntil(this.unsubscribe$),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe((term: string) => {
                this.navigation.skip = 0;
                const criteriaKey = this.startsWith.test(term) ? 'codeCommande' : 'titreOffre';

                this.searchCriteria = new FsCommandeCriteria({ groupeEntreprise: this.monGroupe, [criteriaKey]: term });

                this.setForcedCriteria();

                this.searchCommandesGroupe();
            });
    }

    paste(event: any) {
        event.preventDefault();
    }

    sortChange(sort: SortDescriptor[]): void {
        this.commandeSort = sort;

        if (this.commandeSort && this.commandeSort.length > 0 && this.commandeSort[0].dir) {
            let sortableField = '';
            if (['offreur.raisonSociale', 'titre'].includes(sort[0].field)) {
                sortableField = 'offre.' + sort[0].field
            }
            else if (sort[0].field?.endsWith('Cmd') && sort[0].field !== "totalValeurTtcNetteCmd") {
                sortableField = sort[0].field.replace('Cmd', '')
            }
            else if (sort[0].field === 'totalValeurTtcNetteCmd') {
                sortableField = 'valeurCmdNetTtc'
            }
            else {
                sortableField = sort[0].field
            }
            this.navigation.originalSortField = sort[0].field;
            this.navigation.sortField = sortableField;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
            this.navigation.originalSortField = '';
        }

        this.searchCommandesGroupe();
    }


    pageChange(event: PageChangeEvent): void {
        if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.navigation.pageSize = event.take;

            this.searchCommandesGroupe();
        }
    }

    cellClickHandler(event: CellClickEvent): void {
        if (event?.column.title !== 'Action') {
            const offre: Offre = event?.dataItem;

            if (
                (this.authService.hasAnyAuthority(['ROLE_RESPONSABLE', 'ROLE_SUPER_ADMIN'])) ||
                ( 
                    this.plateformeService.isPlateForme('WIN_GROUPE') &&
                    this.checkIsSupporteur(offre?.supporterEntreprise)
                ) ||
                (
                    this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') && 
                    (this.checkIsSupporteur(offre?.supporterEntreprise)) &&
                    this.allowedEtatCommande?.includes(offre?.etatCommandeAchatGroupe)
                )
            ) {
                this.consulterCommande(offre, true);
            } else {
                this.consulterOffre(offre, true);
            }
        }
    }

    checkIsSupporteur(supporteur: PharmacieEntreprise): boolean {
        if (supporteur) {
            return this.authService.getPrincipal().societe?.id === supporteur?.id;
        }
        return false;
    }

    envoyerMultiple(): void {
        this.userInputServ.confirmAlt('Confirmation', `Êtes vous sûr de vouloir envoyer les commandes sélectionnées ?.`).then(
            () => { console.log(this.selectedKeys); },
            () => null
        );
    }

    saisirCommande(item: Offre): void {
        if (item?.etatCommandeAchatGroupe === 'ACCEPTEE') {
            this.fedSyndicatService.getMyGroupe().then(myGroupe => {
                this.router.navigate(
                    ['achats-groupes/commandes/edit/cmd-unitaire'],
                    {
                        queryParams:
                        {
                            readOnly: false,
                            groupeId: myGroupe?.id,
                            offreId: item?.id
                        }
                    });
            });
        }
    }


    annulerCommande(item: Offre): void {
        if (item?.etatCommandeAchatGroupe === 'BROUILLON') {
            this.userInputServ.confirmAlt('Confirmation', `Êtes vous sûr de vouloir annuler la commande sur l'offre: <b>${item?.titre}</b> ?`).then(
                () => {
                    this.commandeService.annulerCommandeConsolidee(item?.enteteCommandeId).subscribe(res => {
                        this.navigation.skip = 0;
                        this.searchCommandesGroupe();

                        this.alertService.successAlt(`La commmande sur l'offre: <b>${item?.titre}</b> a été annulée avec succès.`, 'Commande Annulée', 'MODAL');
                    });
                },
                () => null
            );
        }
    }

    saisirBl(id_cmd: string | number): void {
        this.router.navigate(['/achats-groupes/bons-livraison', id_cmd, 'edit', '']);
    }

    selectionChange(event: SelectionEvent): void {
        const selectedRows = event?.selectedRows?.filter(row => row?.dataItem?.etatCommande !== 'ENVOYEE');
        const deselectedRows = event?.deselectedRows?.filter(row => row?.dataItem?.etatCommande !== 'ENVOYEE');

        selectedRows?.forEach(row => {
            if (!this.selectedKeys.includes(row?.dataItem?.id)) {
                this.selectedKeys.push(row?.dataItem?.id);
            }
        })

        deselectedRows?.forEach(row => {
            const index = this.selectedKeys.indexOf(row.dataItem.id);
            if (index >= 0) {
                this.selectedKeys.splice(index, 1);
            }
        })
    }

    consulterCommande(item: Offre, readOnly = false): void {
        this.router.navigate(['/achats-groupes/commandes/edit/cmd-groupe', item?.enteteCommandeId], { queryParams: { readOnly, offreId: item?.id } });
    }

    consulterOffre(item: Offre, readOnly = true): void {
        this.router.navigate(['/achats-groupes/offres/edit', item?.id], {
            queryParams: {
                readOnly,
                natureOffre: 'G',
                etatCommandeAchatGroupe: item?.etatCommandeAchatGroupe,
                enteteCommandeId: item?.enteteCommandeId,
                from: 'commandes-groupe',
            }
        });
    }

    buildExport(): void {
        this.exportPdfRef = this.exportPdfServ
            .ref<any>()
            .setTitle('Liste des Commandes Groupe')
            .addColumn('codeCommande', 'Code Commande')
            .addColumn('titre', "Titre de l'offre", { width: 180 })
            .addColumn('*', 'Offreur', {
                transform: (value: Offre) => {
                    return value?.offreur?.raisonSociale;
                }
            })
            .addColumn('*', 'Distributeur', {
                transform: (value: Offre) => {
                    return value?.distributeur?.raisonSociale;
                }
            });

        if (this.authService.hasAnyAuthority(['ROLE_RESPONSABLE'])) {
            this.exportPdfRef.addColumn('*', 'Supporteur', {
                transform: (value: Offre) => {
                    return value?.supporterEntreprise ? `Dr. ${value?.supporterEntreprise?.nomResponsable}` : '';
                }
            });
        }

        this.exportPdfRef
            .addColumn('dateCreationCmd', 'Date Création', {
                transform: (value) => {
                    return this.datePipe.transform(value, 'dd/MM/yyyy') || '--/--/----';
                }
            })
            .addColumn('etatCommandeAchatGroupe', 'Statut')
            .setData([]);

    }

    filterList(searchQuery: string) {
        return this.offresService.searchSociete({
            raisonSociale: searchQuery,
            typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE]
        });
    }

    filterListeGroupe(searchQuery: string) {
        const navigation: Pagination = { pageSize: 5, skip: 0 };
        const criteria = new GroupeEntrepriseCriteria({ raisonSociale: searchQuery, typeEntreprises: [SocieteType?.GROUPE_CLIENT] });

        return this.fedSyndicatService.searchGroupeEntreprise(navigation, criteria);
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map(res => res?.content.slice(0, 5))
        );

    searchGroupeClient = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterListeGroupe(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map(res => res?.content.slice(0, 5))
        );

    fournisseurFormatter = (result: { raisonSociale: any; }) => result ? result.raisonSociale : null;

    laboFormatter = (result: { raisonSociale: any; }) => result ? (result.raisonSociale === 'DIVERS' ? null : result.raisonSociale) : null;

    filterListPharmacien(searchQuery: string) {
        const criteria = new PharmacieEntrepriseCriteria({
            nomResponsable: searchQuery,
            groupeEntrepriseDTO: this.monGroupe,
            typeEntreprises: [SocieteType.CLIENT]
        });

        return this.fedSyndicatService.searchPharmacieEntreprise({ pageSize: 10, skip: 0 }, criteria);
    }

    searchPharmacien = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterListPharmacien(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map(res => res?.content.slice(0, 5))
        );

    pharmacienFormatter = (result: Fournisseur) => result ? `Dr. ${result?.nomResponsable}` : null;

    appliquerFiltre(): void {
        const payload = this.filterForm?.getRawValue();

        if (payload?.etatCommande) this.searchCriteria['etatCommande'] = [payload?.etatCommande];

        this.searchCriteria = new FsCommandeCriteria({
            ...this.searchCriteria,
            distributeurId: payload?.distributeur?.id,
            offreurId: payload?.offreur?.id,
            dateCreationDebut: payload?.dateDebut,
            dateCreationFin: payload?.dateFin,
            groupeEntreprise: payload?.groupeEntreprise,
            supporterEntreprise: typeof payload?.supporterEntreprise === 'object' ? payload?.supporterEntreprise : null
        });

        this.setForcedCriteria(!payload?.etatCommande);

        this.navigation.skip = 0;
        this.searchCommandesGroupe();
    }

    viderFiltre(): void {
        this.filterForm.reset();

        this.navigation.skip = 0;
        this.searchCriteria = new FsCommandeCriteria();

        this.setForcedCriteria();

        this.searchCriteria = new FsCommandeCriteria({ ...this.searchCriteria, groupeEntreprise: this.monGroupe });

        this.searchCommandesGroupe();
    }

    openDistributeurs(fournisseurs: Fournisseur[], content) {
        this.selectedFournisseurs = fournisseurs;
        this.modalService.open(content, { ariaLabelledBy: "modal-basic-title", windowClass: 'fs-cstm-modal' }).result.then((result) => {
            console.log(`Closed with: ${result}`);
        }, (reason) => {
            console.log(`Dismissed ${reason}`);
        });
    }

    askLaboEmail(dataItem: any) {
        this.modalService.open(this.LaboEmailModal, { ariaLabelledBy: 'modal-basic-title', centered: true, windowClass: 'fs-modal-content' })
        this.temp = dataItem;
    }


    fieldError(field: string, error: string) {
        return this.laboEmailForm.get(field).touched && this.laboEmailForm.get(field).hasError(error)
    }

    isFieldInvalid(field: string) {
        return this.laboEmailForm.get(field).invalid && this.laboEmailForm.get(field).touched;
    }



    EmailEqualtyValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const mainEmail = this.laboEmailForm?.get('email').value;
            if (mainEmail === control.value) {
                return null;
            }
            return { diff: true, email: false };
        };
    }

    reload() {
        this.searchCommandesGroupe();
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }
}
