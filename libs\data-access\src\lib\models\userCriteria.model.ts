import { SocieteType } from "@wph/shared";

export class UserCriteria {
  codeEntreprise?:string;
  email?:string;
  id?:string;
  nomResponsable?:string;
  nomUser?: string;
  prenomUser?: string;
  raisonSociale?: string;
  typeEntreprise?: SocieteType;
  statut?: string[];
  entrepriseId?: string;

  constructor(criteria?: UserCriteria) {
    this.codeEntreprise = criteria?.codeEntreprise  || null;
    this.email = criteria?.email || null;
    this.id = criteria?.id || null;
    this.nomResponsable = criteria?.nomResponsable || null;
    this.nomUser = criteria?.nomUser || null;
    this.prenomUser = criteria?.prenomUser || null;
    this.raisonSociale = criteria?.raisonSociale || null;
    this.statut = criteria?.statut || null;
    this.typeEntreprise = criteria?.typeEntreprise || null;
    this.entrepriseId = criteria?.entrepriseId || null;
  }
}
