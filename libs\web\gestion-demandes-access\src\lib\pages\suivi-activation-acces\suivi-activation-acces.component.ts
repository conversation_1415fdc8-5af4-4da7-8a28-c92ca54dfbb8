import { Component, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { PageChangeEvent } from "@progress/kendo-angular-pager";
import { SortDescriptor } from "@progress/kendo-data-query";
import { DemandeAccesClient, DemandeAccesClientCriteria, Fournisseur, OffresService, Pagination } from "@wph/data-access";
import { DemandeAccesClientService } from "../../services/demande-acces-client.service";
import { AuthService } from "@wph/core/auth";
import { BootstrapColorClasses, UserInputService } from "@wph/web/shared";
import { AccesClientService, AlertService, ClientFournisseur, ClientFournisseurCriteria, SocieteType } from "@wph/shared";
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map } from "rxjs";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ActivatedRoute } from "@angular/router";
import { phoneValidator } from 'libs/federation-syndicats/src/lib/validators/phone-validator';

@Component({
    selector: 'wph-suivi-activation-acces',
    templateUrl: './suivi-activation-acces.component.html',
    styleUrls: ['./suivi-activation-acces.component.scss']
})
export class SuiviActivationAccesComponent implements OnInit {
    motifDeRefus: string | null = null;
    filterForm: FormGroup | null = null;
    sort: SortDescriptor[];
    startsWith: RegExp = new RegExp('^[0-9]*$');
    navigation: Pagination = { pageSize: 20, skip: 0 };
    pageSizes: number[] = [5, 10, 15, 20];
    isEditing: boolean;
    copyCode: boolean;
    copyMdp: boolean;
    assocClientFounisseurData: GridDataResult = { data: [], total: 0 };
    selectedClientGroupeToModify: Fournisseur | null = null;
    selectedClientLocalToModify: Fournisseur | null = null;
    selectedClientGroupeToModifyCode: Fournisseur | null = null;
    demandesAccesData: GridDataResult = { data: [], total: 0 };
    selectedDemande: DemandeAccesClient | null = null;
    searchCriteria: DemandeAccesClientCriteria = new DemandeAccesClientCriteria();
    selectStatutData = [
        { label: 'Acceptée', value: 'A' },
        { label: 'Refusée', value: 'R' },
        { label: 'En Attente', value: 'E' },
    ];

    currentFournId: number | null = null;
    currentFournCodeSite: number | null = null;

    @ViewChild('associationClientFournModal', { static: true }) associationClientFournModalRef: TemplateRef<any>;
    
    constructor(
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private modalService: NgbModal,
        private authService: AuthService,
        private alertService: AlertService,
        private offresService: OffresService,
        private userInputService: UserInputService,
        private accesClientService: AccesClientService,
        private demandeAccesService: DemandeAccesClientService
    ) {
        this.currentFournId = this.authService.getPrincipal()?.societe?.id;
        this.currentFournCodeSite = this.authService.getPrincipal()?.societe?.noeud?.codeSite;

        this.filterForm = this.fb.group(new DemandeAccesClientCriteria());

        this.searchCriteria = new DemandeAccesClientCriteria({
            codeSite: this.currentFournCodeSite,
            fournisseurId: this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']) ? this.currentFournId : null
        });

        if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
            this.searchCriteria['etatsDemande'] = ['E'];
            this.filterForm.get('etatsDemande').setValue(['E']);
        }
    }

    ngOnInit(): void {
        this.route.queryParams.subscribe(params => {
            if (params['fournisseurId'] || params['codeSite']) {
                this.searchCriteria.etatsDemande = null;
                this.searchCriteria.codeSite = +params['codeSite'];
                this.searchCriteria.fournisseurId = +params['fournisseurId'];

                this.filterForm.patchValue({
                    etatsDemande: null,
                    codeSite: this.searchCriteria.codeSite,
                    fournisseurId: this.searchCriteria.fournisseurId,
                });
                this.searchDemandesAccesClient();
            } else {
                this.searchDemandesAccesClient();
            }
        });
    }

    searchDemandesAccesClient() {
        this.demandeAccesService.searchDemandesAccesClient(this.searchCriteria, this.navigation).subscribe(res => {
            this.demandesAccesData = { data: res?.content, total: res?.totalElements };
        });
    }

    sortChange(sort: SortDescriptor[]) {
        this.sort = sort;
        if (this.sort && this.sort.length > 0 && this.sort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.searchDemandesAccesClient();
    }

    pageChange(event: PageChangeEvent) {
        if ((event.skip !== this.navigation.skip) || (event?.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.searchDemandesAccesClient();
        }
    }

    accepterDemandeAcces(item: DemandeAccesClient): void {
        this.userInputService.confirm('Confirmation', `Voulez-vous vraiment activer l'accès pour le client : <b>${item?.raisonSocialeLocal}</b> ?`).then(
            () => this.finaliserAccepterDemande(item),
            () => null
        );
    }

    finaliserAccepterDemande(item: DemandeAccesClient): void {
        this.demandeAccesService.accepterDemandeAcces(item?.id).subscribe(_res => {
            this.searchDemandesAccesClient(), this.modalService.dismissAll();

            setTimeout(() => {
                this.alertService.success(`L'accès du client <b>${item?.raisonSocialeLocal}</b> a été accepté avec succès.`, 'MODAL');
            }, 100);
        });
    }

    openFilterModal(content: TemplateRef<any>, size = 'lg'): void {
        this.modalService.open(content, { size, modalDialogClass: 'fs-radius-modal' }).result.then(
            () => null,
            () => null
        );
    }

    appliquerFiltre(): void {
        const payload = this.filterForm.getRawValue();

        this.navigation.skip = 0;

        if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
            this.searchCriteria = {
                ...payload,
                codeSite: (typeof payload?.fournisseurId === 'object') ? payload?.fournisseurId?.noeud?.codeSite : this.currentFournCodeSite,
                fournisseurId: this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']) ? this.currentFournId : (typeof payload?.fournisseurId === 'object') ? payload?.fournisseurId?.id : null
            }
        } else {
            this.searchCriteria = { ...payload, codeSite: this.currentFournCodeSite, fournisseurId: this.currentFournId };
        }

        this.searchDemandesAccesClient();
    }

    copyToClipboard(text: string, type: 'code' | 'mdp'): void {
        navigator?.clipboard?.writeText(text).then(
            () => {
                type === 'code' ? this.copyCode = true : this.copyMdp = true;
                setTimeout(() => {
                    type === 'code' ? this.copyCode = false : this.copyMdp = false;
                }, 2000);
            },
            () => null
        );
    }

    vider(): void {
        this.navigation.skip = 0;
        this.filterForm.reset();

        this.searchCriteria = {
            ...this.filterForm.getRawValue(),
            codeSite: this.currentFournCodeSite,
            fournisseurId: this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']) ? this.currentFournId : null
        };

        this.searchDemandesAccesClient();
    }

    filterFournList(searchQuery: string, typeEntreprises = [SocieteType.GROSSISTE, SocieteType.SOCIETE], useCode = false) {
        const criteriaKey = useCode ? 'code' : 'raisonSociale';
        const criteria = { [criteriaKey]: searchQuery, typeEntreprises };

        return this.offresService.searchSociete(criteria, { pageSize: 5, skip: 0 });
    }

    openRefuserDemandeAccesModal(content: TemplateRef<any>, item: DemandeAccesClient, size = 'md'): void {
        this.selectedDemande = item, this.motifDeRefus = null;

        this.modalService.open(content, { modalDialogClass: 'fs-radius-modal', size, centered: true }).result.then(
            () => null,
            () => null
        );
    }

    confirmerRefusDemandeAcces(modal: any): void {
        if (this.motifDeRefus && this.selectedDemande) {
            this.demandeAccesService.refuserDemandeAcces(this.selectedDemande.id, this.motifDeRefus).subscribe(_res => {
                const raisonSocialeDemandeur = this.selectedDemande?.raisonSocialeGroupe;

                this.searchDemandesAccesClient(), this.modalService.dismissAll();

                setTimeout(() => {
                    this.alertService.success(`La demande d'accès du client: <b>${raisonSocialeDemandeur}</b> a été réfusée avec succès.`, 'MODAL');
                }, 100);
            });
        }
    }

    consulterDemandeAcces(content: TemplateRef<any>, item: DemandeAccesClient, size = 'lg', identifiants = false): void {
        this.selectedDemande = item;

        if (item?.etatDemandeAcces === 'R' && this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) {
            this.accesClientService.searchClientFournisseur({
                codeSite: this.currentFournCodeSite,
                raisonOrCodeCSite: item?.codeClientLocal
            }).subscribe(res => {
                const targetClientLocal = res?.find(client => client?.code === item?.codeClientLocal);

                if (targetClientLocal) {
                    this.selectedDemande = {
                        ...this.selectedDemande,
                        villeLocal: targetClientLocal?.ville,
                        codeClientLocal: targetClientLocal?.code,
                        adresseLocal: targetClientLocal?.adresse,
                        localiteLocal: targetClientLocal?.localite,
                        nomPharmacienLocal: targetClientLocal?.nomPharmacien,
                        raisonSocialeLocal: targetClientLocal?.raisonSociale
                    };
                }
            });
        }

        this.modalService.open(content, identifiants ?
            { modalDialogClass: 'fs-radius-modal', centered: true, size } :
            { modalDialogClass: 'demande-acces-modal', animation: false, windowClass: 'custom-modal-width' }
        ).result.then(
            () => {
                setTimeout(() => {
                    this.selectedDemande = null, this.isEditing = false;
                }, 300)
            },
            () => {
                setTimeout(() => {
                    this.selectedDemande = null, this.isEditing = false;
                }, 300)
            }
        );
    }

    appliquerModificationClientGroupe() {
        this.selectedClientGroupeToModify = this.selectedClientGroupeToModify || this.selectedClientGroupeToModifyCode;

        if (!this.selectedClientGroupeToModify) {
            this.alertService.error('Veuillez sélectionner un client à modifier.', 'MODAL');
            return;
        }

        this.userInputService.confirm(
            'Confirmation',
            `Êtes-vous sûr de vouloir modifier la pharmacie du Maroc: <b>${this.selectedDemande?.raisonSocialeGroupe}</b> ?`,
            'Appliquer'
        ).then(
            () => {
                const payload = new DemandeAccesClient({
                    ...this.selectedDemande,
                    clientGroupe: this.selectedClientGroupeToModify,
                    villeGroupe: this.selectedClientGroupeToModify?.ville,
                    codeClientGroupe: this.selectedClientGroupeToModify?.code,
                    adresseGroupe: this.selectedClientGroupeToModify?.adresse,
                    localiteGroupe: this.selectedClientGroupeToModify?.localite,
                    nomPharmacienGroupe: this.selectedClientGroupeToModify?.nomResponsable,
                    raisonSocialeGroupe: this.selectedClientGroupeToModify?.raisonSociale
                });

                this.demandeAccesService.saveDemandeAccesClient(payload).subscribe(res => {
                    this.selectedDemande = res, this.isEditing = false;
                    this.alertService.success('La pharmacie du Maroc a été modifié avec succès.', 'MODAL');

                    setTimeout(() => { this.selectedClientGroupeToModify = null }, 300);
                });
            },
            () => null
        );
    }

    appliquerModificationClientGroupeAdmin() {
        this.selectedClientGroupeToModify = this.selectedClientGroupeToModify || this.selectedClientGroupeToModifyCode;

        if (!this.selectedClientGroupeToModify) {
            this.alertService.error('Veuillez sélectionner une pharmacie du Maroc.', 'MODAL');
            return;
        }

        this.selectedDemande = new DemandeAccesClient({
            ...this.selectedDemande,
            clientGroupe: this.selectedClientGroupeToModify,
            villeGroupe: this.selectedClientGroupeToModify?.ville,
            codeClientGroupe: this.selectedClientGroupeToModify?.code,
            adresseGroupe: this.selectedClientGroupeToModify?.adresse,
            localiteGroupe: this.selectedClientGroupeToModify?.localite,
            nomPharmacienGroupe: this.selectedClientGroupeToModify?.nomResponsable,
            raisonSocialeGroupe: this.selectedClientGroupeToModify?.raisonSociale
        });
    }

    appliquerModificationClientLocalAdmin() {
        if (!this.selectedClientLocalToModify && !this.selectedClientLocalToModify && !this.selectedDemande?.fournisseur) {
            this.alertService.error('Veuillez sélectionner un client local.', 'MODAL');
            return;
        }

        if (this.selectedDemande?.fournisseur && !this.selectedClientLocalToModify) {
            const searchClientLocalIpt = document.getElementById('clientLocalMod') as HTMLInputElement;
            
            if (searchClientLocalIpt) setTimeout(() => searchClientLocalIpt.focus(), 100);
            return;
        }

        this.selectedDemande = new DemandeAccesClient({
            ...this.selectedDemande,
            villeLocal: this.selectedClientLocalToModify?.ville,
            codeClientLocal: this.selectedClientLocalToModify?.code,
            adresseLocal: this.selectedClientLocalToModify?.adresse,
            localiteLocal: this.selectedClientLocalToModify?.localite,
            nomPharmacienLocal: (this.selectedClientLocalToModify as any)?.nomPharmacien,
            raisonSocialeLocal: this.selectedClientLocalToModify?.raisonSociale
        });
    }

    annulerModificationClientGroupe() {
        this.isEditing = false, this.selectedClientGroupeToModifyCode = this.selectedClientGroupeToModify = null;
    }

    annulerModificationClientLocal() {
        this.selectedDemande.fournisseur = this.selectedClientLocalToModify = null;
    }

    renvoyerDemandeAccess(modal: any): void {
        this.userInputService.confirm('Confirmation', `Êtes-vous sûr de vouloir renvoyer la demande d'accès pour le client: <b>${this.selectedDemande?.raisonSocialeLocal}</b>`, 'Renvoyer', 'Abandonner', BootstrapColorClasses.primary).then(
            () => {
                const payload: DemandeAccesClient = new DemandeAccesClient({
                    ...this.selectedDemande,
                    id: null,
                    etatDemandeAcces: 'E',
                    motifRefus: null,
                    dateTraitement: null,
                    dateCreation: null
                });

                this.demandeAccesService.associerTac(payload).subscribe(res => {
                    if (!res?.match || res?.score < 80) {
                        this.userInputService.confirm(
                            'Confirmation',
                            `Il semble que les informations du client local ne correspondent pas. Vous pouvez vérifier ou continuer si cela est intentionnel.`,
                            'Envoyer quand même', 'Annuler'
                        ).then(
                            () => {
                                this.continueEnvoyerCmd(payload, modal);
                            },
                            () => null
                        );
                    } else {
                        this.continueEnvoyerCmd(payload, modal);
                    }
                });
            },
            () => null
        );
    }

    private continueEnvoyerCmd(payload: DemandeAccesClient, modal: any) {
        this.demandeAccesService.saveDemandeAccesClient(payload).subscribe(_res => {
            this.searchDemandesAccesClient(), modal.dismiss();
            this.alertService.success("La demande d'accès client a été envoyée avec succès", 'MODAL');
        });
    }

    enoyerDemandeAccesClientAdmin(modal: any): void {
        if (
            !this.selectedDemande?.fournisseur || !this.selectedDemande?.gsm ||
            !this.selectedDemande?.codeClientGroupe || !this.selectedDemande?.codeClientLocal
        ) {
            this.alertService.error(`Veuillez remplir tous les champs obligatoires. Merci de compléter les informations suivantes:
                <ul>
                    ${!this.selectedDemande?.codeClientGroupe ? '<li>Pharmacie du Maroc</li>' : ''}
                    ${!this.selectedDemande?.fournisseur ? '<li>Fournisseur</li>' : ''}
                    ${!this.selectedDemande?.codeClientLocal ? '<li>Client Local</li>' : ''}
                    ${!this.selectedDemande?.gsm ? '<li>GSM</li>' : ''}
                </ul> 
            `, 'MODAL');
            return;
        }

        const gsmValid = new FormControl(this.selectedDemande.gsm, [phoneValidator()]).valid;

        if (!gsmValid) {
            this.alertService.error('Le numéro de GSM est invalide. Veuillez vérifier le format.', 'MODAL');
            return;
        }

        const payload = new DemandeAccesClient({
            ...this.selectedDemande, 
            codeSite: this.selectedDemande?.fournisseur?.noeud?.codeSite 
        });

        this.demandeAccesService.adminCreationDemandeAccesClient(payload).subscribe(res => {
            this.searchDemandesAccesClient(), modal.dismiss();

            setTimeout(() => {
                this.alertService.success(`La demande d'accès pour le client <b>${res?.raisonSocialeLocal}</b> a été créée avec succès.`, 'MODAL');
            }, 100);
        });
    }

    consulterClientFournisseur(item: DemandeAccesClient) {
        const payload: ClientFournisseurCriteria = new ClientFournisseurCriteria({ codeClientGroupe: item?.codeClientGroupe });

        this.accesClientService.fetchClientFournisser(payload).subscribe(res => {
            this.assocClientFounisseurData = { data: res, total: res?.length };

            this.openAssocClientFournisseurModal(this.associationClientFournModalRef);
        });
    }

    openAssocClientFournisseurModal(content: TemplateRef<any>, size = 'xl'): void {
        this.modalService.open(content, { size, centered: true, modalDialogClass: 'fs-radius-modal', windowClass: 'custom-modal-width' });
    }

    openCreationDemandeAccesClientModal(content: TemplateRef<any>, size = 'lg'): void {
        this.selectedDemande = new DemandeAccesClient();
        this.selectedClientGroupeToModifyCode = this.selectedClientGroupeToModify = this.selectedClientLocalToModify = null;

        this.modalService.open(content, {
            modalDialogClass: 'demande-acces-modal',
            animation: false, windowClass: 'custom-modal-width'
        }).result.then(() => this.selectedDemande = null, () => this.selectedDemande = null);
    }

    filterClientLocal(raisonSocialeOuCode: string): Observable<ClientFournisseur[]> {
        return this.accesClientService.searchClientFournisseur({ 
            codeSite: this.selectedDemande?.fournisseur?.noeud?.codeSite, 
            raisonOrCodeCSite: raisonSocialeOuCode 
        });
    }

    searchClient = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterFournList(term.toLowerCase(), [SocieteType.CLIENT, SocieteType.SOCIETE]);
                }
                return of({ content: [] });
            }),
            map(res => res.content)
        );

    searchClientByCode = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterFournList(term, [SocieteType.CLIENT, SocieteType.SOCIETE], true);
                }
                return of({ content: [] });
            }),
            map(res => res.content)
        );

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterFournList(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map(res => res.content)
        );

    searchClientLocal = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterClientLocal(term);
                }
                return of([]);
            }),
            map(res => res)
        );

    fournisseurFormatter = (result: { raisonSociale: any; }) => result ? result.raisonSociale : null;
    clientFormatter = (result: Fournisseur) => result ? `${result?.raisonSociale} | ${result?.nomResponsable} | ${result?.ville}` : null;
    clientLocalFormatter = (result: ClientFournisseur) => result ? `${result?.raisonSociale?.trim()} | ${result?.nomPharmacien?.trim()} | ${result?.ville?.trim()}` : null;

}