.logo {
  width: auto;
  display: flex;
  justify-self: center;
  position: relative;
  height: 50px;
  padding-top: 8px;
  padding-bottom: 8px;
}

.logo-wrapper{
  width: auto;
  box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.5);
}

.grossiste-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #bbbaba;

  .btn-badge {
    border: 1px solid #bbbaba;
  }
}

.clickable-img {
  cursor: pointer;
}

.plateforme-container {
  position: relative;
  width: auto;
}

.fournisseur-text {
  text-overflow: ellipsis;
}

.card-col-cstm {
  min-height: 150px;
  border-right: 1px solid #dcdbdb;
}

@media screen and (max-width: 768px) {
  .card-col-cstm {
    border-right: none;
    border-bottom: 1px solid #dcdbdb;
    margin: 0px;
  }
}

@media screen and (min-width: 1367px) {
  .card-col-cstm {
    border-right: none;
    border-bottom: 1px solid #dcdbdb;
    margin: 0px;
  }

  .plateforme-container {
    position: fixed;
    max-width: 350px;
    transform: translateX(1.2vw);
  }
}

.choose-platform{
  position:fixed;
  overflow: hidden;
}
.btn-cmd-grossiste{
  box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.5);
  background: linear-gradient(173deg, rgb(0, 157, 79) 0%, rgb(1, 131, 71) 100%);
  color:white;
  font-size:20px;
}
.fournisseur-img{
  img{
    height: 90%;
    width: 90%;
    min-width: 30px;
    min-height: 30px;
  }
  width: 40px;
  height: 40px;
  background: #0c724361;
  margin-right: 8px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px;
}

.plat-selection-card {
  max-height: none;
  margin: 0 auto;
  border-radius: 10px;
  background: #fff;
}

.platform-selection-sep {
  height: 5px; 
  border-color: #c089115c;
}

.btn-section {
  font-size: .97rem;
  font-weight: 900;
  color: #6C757D;
}

@media (min-width: 1367px) {
  .plat-selection-card, .actualites-col {
    max-height: calc(100vh - 80px);
    overflow-x: hidden;
    overflow-y: auto;

    scrollbar-width: thin !important;
    scrollbar-color: var(--win-offre-bg-action-tint-1) white !important;
  }

  .grossite-selection-container {
    max-height: calc(100vh - 80px);
  }

  .page-content {
    min-height: calc(100vh - 76px) !important;
    overflow-y: hidden !important;
  }
}

.bg-btn-container {
  background: rgba(255, 241, 186, .7);
}

.btn-radius {
  border-radius: 10px !important;
}

.page-content {
  padding-bottom: 45px !important;
}

.grossiste-img-container {
  height: 100%;
  display: flex;
  justify-content: end;
  img {
    object-fit: contain;
    object-position: center;
    width: 100%;
    height: auto;
  }
}

.winplus-offre-container {
  width: 40px !important;
  background-color: rgba(115, 148, 131, .65); 
  border-radius: 10px; 
}

.win_plus_pharma_logo {
  height: 55px;
  width: auto;
  max-width: 50%;
  position: absolute;
  top: 15px;
  left: 10px
}

.winplus_pharma_presentation {
  background-size: contain;
  background-position: center;
  max-width: 99.8% !important;
  height: 100%;
  width: auto;

}

.materiel_info {
  background-size: contain;
  background-position: bottom;
  max-width: 99.8% !important;
  height: 100%;
  width: auto;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.win-offre-btn {
  position: relative;
  background: linear-gradient(173deg, #c5d5cb 18%, #9eb7a8 100%) !important;
  padding: 2px;

  &.animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -150%;
    width: 200%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(15, 188, 177, 0) 0%,
      rgba(255, 255, 255, 0.35) 50%,
      rgba(15, 188, 177, 0) 100%
    );
    animation: shimmer 1.8s ease-in-out alternate infinite;
    // pointer-events: none;
    z-index: 1;
  }
}

.btn-acces-client-deactivated {
  color: #dcdbdb;
  opacity: 0.7 !important;
  cursor: not-allowed !important;
}

.page-content-inner {
  max-width: none;
  position: relative;
}

@media (min-width: 768px) and (max-width: 1366px) {
  .page-content-inner {
    max-width: 600px !important;
  }
}

@media (max-width: 1366px) {
  .page-content {
    overflow-y: auto !important;
    margin-top: 15px !important;
    min-height: calc(100vh - 68px) !important;
    max-height: none !important;
  }

}
