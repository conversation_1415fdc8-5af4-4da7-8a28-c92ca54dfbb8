<!-- Start Of Header -->
<div class="rowline mb-2">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 col-4">Accueil</h4>

        <div *jhiHasAnyAuthority="['ROLE_NATIONAL', 'ROLE_RESPONSABLE', 'ROLE_AGENT_POINT_VENTE']" class="col-8 px-1">
            <div class="row d-flex justify-content-end align-items-center">
                <button (click)="backwardOneMonth()" class="btn btn-sm btn-left px-1">
                    <i class="mdi mdi-chevron-left mdi-18px"></i>
                </button>

                <ng-container>
                    <input [formControl]="datePicker" style="visibility: hidden; position: absolute; right: 10px;"
                        ngbDatepicker #dPicker="ngbDatepicker">

                    <span (click)="dPicker.toggle()" class="mx-0 my-0 d-flex align-items-center date-container">
                        <i class="mdi mdi-calendar mdi-18px"></i>
                        <span class="mx-1">{{ (displayedDate | date: 'MMMM yyyy') | titlecase}}</span>
                    </span>
                </ng-container>

                <button (click)="forwardOneMonth()"
                    [disabled]="(displayedDate?.getMonth() === now?.getMonth()) && (displayedDate?.getFullYear() === now?.getFullYear())"
                    class="btn btn-sm btn-right px-1">
                    <i class="mdi mdi-chevron-right mdi-18px"></i>
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="page-content px-2">
    <div class="row flex-wrap mx-auto gap-2 d-flex justify-content-center w-100 align-items-center">
        <ng-container>
            <div class="col-12 col-md-6 col-xl-3"
                *jhiHasAnyAuthority="['ROLE_NATIONAL','ROLE_RESPONSABLE', 'ROLE_PHARMACIEN', 'ROLE_AGENT_POINT_VENTE']">
                <wph-simple-stat-card label="Supporté" iconClass="mdi-wallet" [isCurrency]="true"
                    [value]="enteteStatistique?.montantSupporte ?? 0"></wph-simple-stat-card>
            </div>

            <div class="col-12 col-md-6 col-xl-3"
                *jhiHasAnyAuthority="['ROLE_NATIONAL','ROLE_RESPONSABLE', 'ROLE_PHARMACIEN', 'ROLE_AGENT_POINT_VENTE']">
                <wph-simple-stat-card label="Consommé" iconClass="mdi-cart" [isCurrency]="true"
                    [value]="enteteStatistique?.montantConsome ?? 0"></wph-simple-stat-card>
            </div>

            <div class="col-12 col-md-6 col-xl-3"
                *jhiHasAnyAuthority="['ROLE_NATIONAL','ROLE_RESPONSABLE', 'ROLE_PHARMACIEN', 'ROLE_AGENT_POINT_VENTE']">
                <wph-simple-stat-card label="Balance" iconClass="mdi-calculator" [isCurrency]="true"
                    [value]="enteteStatistique?.montantBalance ?? 0"></wph-simple-stat-card>
            </div>

            <div class="col-12 col-md-6 col-xl-3" *jhiHasAnyAuthority="['ROLE_NATIONAL']">
                <wph-simple-stat-card label="Totales des groupes" [value]="totalActiveGroups"
                    label2="Totals des membres" [value2]="totalActiveMembers" iconClass="mdi-account-multiple"
                    [stacked]="true"></wph-simple-stat-card>
            </div>
            <div class="col-12 col-md-6 col-xl-3" *ngIf="roleResponsableOnly">
                <wph-simple-stat-card label="Total des membres" [value]="totalActiveMembersByGroupe"
                    iconClass="mdi-account-multiple" [stacked]="true"></wph-simple-stat-card>
            </div>


        </ng-container>


    </div>

    <div class="row flex-wrap mx-auto gap-2 d-flex  w-100 align-items-center" *ngIf="roleLAbortoireOnly">
        <!-- LABORATIORE -->
        <div class="col-12 col-md-6 col-xl-3">
            <wph-simple-stat-card label="Chiffre d'affaire Commande TTC"
                [value]="laboratioreDashboardData?.chiffreDaffaireLaboCmdTtc" iconClass="mdi-account-multiple"
                [isCurrency]="true"></wph-simple-stat-card>

        </div>
        <div class="col-12 col-md-6 col-xl-3">
            <wph-simple-stat-card label="Chiffre d'affaire Commande HT"
                [value]="laboratioreDashboardData?.chiffreDaffaireLaboCmdHt" iconClass="mdi-account-multiple"
                [isCurrency]="true"></wph-simple-stat-card>

        </div>
    </div>

    <div class="row flex-wrap gap-2 d-flex justify-content-between mx-auto my-4">
        <div *ngIf="shouldDisplayStats" class="col-lg-6 col-12 mb-lg-0 mb-2">
            <div class="row d-flex mb-2">
                <div class="col-6">
                    <wph-link-card link="/achats-groupes/pharmacies/liste" label="Suggestions En Attente" [value]="12"
                        *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']"></wph-link-card>
                    <wph-link-card link="/achats-groupes/offres/liste" label="Offres" [value]="totalOffresActives"
                        [badgeValue]="totalNouvellesOffres"
                        *jhiHasAnyAuthority="['ROLE_RESPONSABLE', 'ROLE_NATIONAL', 'ROLE_AGENT_POINT_VENTE']"></wph-link-card>
                </div>
                <div class="col-6">
                    <wph-link-card link="/achats-groupes/commandes/liste?statut=BROUILLON" label="Commandes en cours de saisie"
                        [value]="totalCmdEnCours"
                        *jhiHasAnyAuthority="['ROLE_RESPONSABLE', 'ROLE_NATIONAL', 'ROLE_AGENT_POINT_VENTE']"></wph-link-card>
                    <wph-link-card link="/achats-groupes/pharmacies/liste" label="Suggestions Traité" [value]="5"
                        *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']"></wph-link-card>

                </div>
            </div>

            <div class="row d-flex"
                *jhiHasAnyAuthority="['ROLE_RESPONSABLE', 'ROLE_NATIONAL', 'ROLE_AGENT_POINT_VENTE']">
                <div class="col-6 mb-2 mb-xl-0">
                    <wph-link-card link="/achats-groupes/bons-livraison/mes-bons" label="En attente de livraison"
                        [value]="totalLivEnAttente"></wph-link-card>
                </div>

                <div class="col-6 d-block d-xl-none">
                    <wph-link-card link="/achats-groupes/commandes/groupe?supporteur=self&statut=ENVOYEE" label="Commandes Supportées"
                        [value]="totalCmdSupporte"></wph-link-card>
                </div>

                <div class="col-6 d-block d-xl-none">
                    <wph-link-card link="/achats-groupes/commandes/liste?statut=ENVOYEE" label="Commandes Consommées"
                        [value]="totalCmdConsome"></wph-link-card>
                </div>

                <div class="col-6 d-none d-xl-block">
                    <div class="col-12 mb-2 px-0">
                        <wph-link-card link="/achats-groupes/commandes/groupe?supporteur=self&statut=ENVOYEE" label="Commandes Supportées"
                            [value]="totalCmdSupporte" [singleLine]="true"></wph-link-card>
                    </div>

                    <div class="col-12 px-0">
                        <wph-link-card link="/achats-groupes/commandes/liste?statut=ENVOYEE" label="Commandes Consommées"
                            [value]="totalCmdConsome" [singleLine]="true"></wph-link-card>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-12 mb-lg-0 mb-2" *ngIf="roleLAbortoireOnly">
            <div class="row d-flex mb-2">
                <div class="col-6">
                    <wph-link-card link="/achats-groupes/offres/liste" label="Nº des offres publiées"
                        [value]="laboratioreDashboardData?.offresActives"></wph-link-card>
                </div>
                <div class="col-6">
                    <wph-link-card link="/achats-groupes/commandes/liste/laboratoire?status=traite"
                        label="Nº des commandes traitées"
                        [value]="laboratioreDashboardData?.nbrCmdTraiteParLabo"></wph-link-card>
                </div>
                <div class="col-6  mt-2">
                    <wph-link-card link="/achats-groupes/commandes/liste/laboratoire?status=nontraite"
                        label="Nº de commandes non traitées"
                        [value]="laboratioreDashboardData?.nbrCmdNonTraiteParLabo"></wph-link-card>

                </div>
                <div class="col-6  mt-2">
                    <wph-link-card link="/achats-groupes/pharmacies/liste" label="Nº des Clients"
                        [value]="laboratioreDashboardData?.nbreClientLabo"></wph-link-card>
                </div>
            </div>

        </div>
        <div class="col-lg-6 col-12">
            <div class="card shadow-sm p-1 h-100 card-radius info-card">
                <div class="row w-100 h-100">
                    <swiper-container *ngIf="listePostes?.length; else: noPostesAvailable"
                        class="h-100 p-0 sw-container" speed="1500" autoplay="true" loop="false" navigation="true"
                        pagination="true" scrollbar="false" slidesPerView="1" centeredSlides="true"
                        (pointerenter)="toggleSwiperAutoplay()" (pointerleave)="toggleSwiperAutoplay()" #swiperRef>

                        <swiper-slide *ngFor="let post of listePostes">
                            <wph-news-card [titre]="post?.titre" [imgActionLabel]="post?.libelleUrl"
                                [videoUrl]="post?.videoUrl" [link]="post?.url ?? post?.imageUrl" [content]="post?.sujet"
                                [imgSrc]="post?.docImagePost?.idhash"></wph-news-card>
                        </swiper-slide>

                        <swiper-button-prev></swiper-button-prev>
                        <swiper-button-next></swiper-button-next>
                    </swiper-container>

                    <ng-template #noPostesAvailable>
                        <swiper-container class="h-100 p-0 sw-container" speed="1000" autoplay="true" loop="false"
                            navigation="false" pagination="true" scrollbar="false" slidesPerView="1"
                            centeredSlides="true" #swiperRef>
                            <swiper-slide>
                                <wph-news-card [titre]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'Bienvenue à WinPlus Groupe !' : 'Bienvenue à La Centrale Pharma !'"
                                    content="Passez des commandes groupées entre pharmaciens et accédez à des remises avantageuses sur vos commandes en gros. Simplifiez vos approvisionnements et boostez vos économies dès aujourd'hui !"></wph-news-card>
                            </swiper-slide>
                        </swiper-container>
                    </ng-template>

                </div>
            </div>
        </div>
    </div>

</div>

<!-- Floating action button -->
<button *ngIf="false" class="btn btn-lg info-fab d-flex align-items-center" (click)="openModal(helpModal)">
    <span style="font-size: 1.5rem;"><i class="bi bi-question-circle-fill mr-1"></i></span>
</button>

<ng-template #helpModal let-modal>
    <div class="modal-header border-0">
        <div class="row w-100 d-flex align-items-center justify-content-between">
            <h3 class="modal-title text-white mx-2" id="modal-basic-title">Besoin d'aide ?</h3>

            <button type="button" class="btn-close m-0" aria-label="Close" (click)="modal.dismiss('Cross click')">
                <span class="h3">&times;</span>
            </button>
        </div>
    </div>

    <div class="modal-body m-0 px-2 py-3">
        <div class="contact-container">
            <h4 class="w-100 text-dark d-flex align-items-center">
                <button class="btn btn-sm icn-btn mr-1">
                    <i class="bi bi-display"></i>
                </button> Contacter Support Technique
            </h4>
            <div class="row d-flex justify-content-between flex-wrap align-items-center my-3 mx-0 px-0" style="gap: 10px">
                <div class="col-12 col-md d-flex align-items-center contact-text">
                    <i class="bi bi-telephone" style="font-size: 1.3rem;"></i>
                    <a class="text-dark mx-1" href="tel:05 02 30 40 10">05 02 30 40 10</a>
                </div>

                <div class="col-12 col-md d-flex align-items-center contact-text">
                    <i class="bi bi-envelope-at" style="font-size: 1.3rem;"></i>
                    <a class="text-dark mx-1" href="mailto:<EMAIL>">test1&#64;sophatel.com</a>
                </div>
            </div>

            <h4 class="w-100 text-dark d-flex align-items-center">
                <button class="btn btn-sm icn-btn mr-1">
                    <i class="bi bi-person-circle"></i>
                </button> Contacter Support Fonctionnel
            </h4>
            <div class="row d-flex justify-content-between flex-wrap align-items-center my-3 mx-0 px-0" style="gap: 10px">
                <div class="col-12 col-md d-flex align-items-center contact-text">
                    <i class="bi bi-telephone" style="font-size: 1.3rem;"></i>
                    <a class="text-dark mx-1" href="tel:05 02 30 40 10">05 02 30 40 10</a>
                </div>

                <div class="col-12 col-md d-flex align-items-center contact-text">
                    <i class="bi bi-envelope-at" style="font-size: 1.3rem;"></i>
                    <a class="text-dark mx-1" href="mailto:<EMAIL>">test1&#64;sophatel.com</a>
                </div>
            </div>
        </div>

        <div class="contact-container mt-3">
            <h4 class="w-100 text-dark d-flex align-items-center">
                <button class="btn btn-sm icn-btn mr-1">
                    <i class="bi bi-keyboard"></i>
                </button> Raccourcis Clavier
            </h4>
            <div *ngFor="let shortcut of keyboardShortcuts"
                class="row d-flex justify-content-between align-items-center my-3 mx-0 px-0">
                <div class="col d-flex justify-content-start m-0 px-1">
                    <span class="text-dark" style="font-size: 1rem;">{{shortcut?.desc}}</span>
                </div>

                <div class="col d-flex justify-content-end m-0 px-1">
                    <div class="row d-flex align-items-center">
                        <span *ngFor="let item of shortcut?.combo; let i=index">
                            <span class="combo-key mx-1">{{ item }}</span>
                            <span *ngIf="i < shortcut?.combo?.length - 1" class="h4">+</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>