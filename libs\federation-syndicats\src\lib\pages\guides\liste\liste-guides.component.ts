import { Component, Inject, OnInit, TemplateRef } from '@angular/core';
import { Faq } from '../../../models/faq.model';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { faqs } from './data/faq';
import { AuthService } from '@wph/core/auth';
import { KeyboardShortcutService } from '../../../../../../web/shared/src/lib/services/keyboard-shortcut.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'wph-liste-guides',
  templateUrl: './liste-guides.component.html',
  styleUrls: ['./liste-guides.component.scss']
})
export class ListeGuidesComponent implements OnInit {

  activatedQuestion: number;
  filteredQuestions: any[] = [];  
  questions: any[] = [
    {
      question: "Comment créer une commande groupée ?",
      authorities: ['ROLE_RESPONSABLE'],
      id: 7,
     },
    {
      question: "Comment passer une commande individuelle ?",
      id: 1,
     },
     {
      question: "créer un bon de livraison (BL) pour une commande individuelle ?",
      id: 8,
     },
     {
      question: "Comment consulter les statistiques des groupes et des laboratoires ?",
      authorities: ['ROLE_NATIONAL'],
      id: 9,
     },
     {
      question: "Comment passer une commande unitaire dans une commande groupée ?",
      id: 2,
     },
     {
      question: "Comment modifier une commande unitaire ?",
      id: 3,
     },
     {
      question: "Comment créer un bon de livraison (BL) pour les commandes groupées ?",
      id: 4,
     },
     {
      question: "Comment repartir un bon de livraison (BL) pour les commandes groupées?",
      id: 10,
     },
     {
      question: "Comment consulter les statistiques des membres du groupe ?",
      authorities: ['ROLE_RESPONSABLE'],
      id: 5,
     },
     {
      question: "Comment consulter mes statistiques ?",
      id: 6,
     }
     
  ];
  
  cdnBaseUrl: string = this.environment.cdn_base_url;

  keyboardShortcuts: { desc: string, combo: string[] }[] | null = null;

  private faqs: Faq[] = faqs;


  constructor(
    private router: Router,   
    private route: ActivatedRoute, 
    private modalSerivce: NgbModal,
    private sanitizer: DomSanitizer,
    private authService: AuthService, 
    @Inject('ENVIROMENT') private environment: any,
    private keyboardShortcutService: KeyboardShortcutService, 
  ) {  }

  
  getCurrentQuestion(): Faq | undefined {
    return this.faqs.find(faq => faq.id == this.activatedQuestion);
  }




  getYouTubeEmbedUrl(youtubeId: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(`https://www.youtube.com/embed/${youtubeId}`);
  }

  questionidChanged(questionId: number) {
    this.activatedQuestion = questionId;
    this.router.navigate(['/achats-groupes/guides/liste'], { queryParams: { questionId } });
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      if (params['questionId']) {
        this.activatedQuestion = +params['questionId'];
      }
    });
    this.filteredQuestions = this.questions.filter(q => 
      !q.authorities || this.authService.hasAnyAuthority(q.authorities)
    );   
    
    this.keyboardShortcuts = this.keyboardShortcutService.getRegisteredShortcuts();
  }

  openModal(content: TemplateRef<any>, size = 'md') {
    this.modalSerivce.open(content, { size, centered: true });
  }


}
