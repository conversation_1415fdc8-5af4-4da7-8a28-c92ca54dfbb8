import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import { CommandeService } from '@wph/commandes-web/commande';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Fournisseur, OffresService, Pagination } from '@wph/data-access';
import { Principal, SocieteType } from '@wph/shared';
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map, takeUntil, Subject, Subscription, OperatorFunction, filter } from 'rxjs';
import { NgbModal, NgbNavChangeEvent } from '@ng-bootstrap/ng-bootstrap';
import * as moment from 'moment';
import { SortDescriptor } from '@progress/kendo-data-query';
import { UserInputService, getDynamicPageSize } from '@wph/web/shared';
import { AlertProduitDto } from '../../models/alert-produit.model';
import { ProduitFournisseurDto } from '../../models/ProduitFournisseurDto';
import { ProduitDTO } from '../../models/produitDTO';
import { AuthService } from '@wph/core/auth';

@Component({
    selector: 'wph-alerte-produits',
    templateUrl: './alerte-produits.component.html',
    styleUrls: ['./alerte-produits.component.scss'],
})
export class AlerteProduitsComponent implements OnInit {
    gridView: GridDataResult = { data: [], total: 0 };     
    gridSort: SortDescriptor[];
    unsubscribe$: Subject<boolean> = new Subject<boolean>();
    activeTabId: number;
    parentTabId: number = 1;  // Default parent tabs
    childTabId: number = 6;   // Default child tabs    
    mySelection: string[] = [];
    model: any;
    filteredData: any[] = []; 

    // eslint-disable-next-line @typescript-eslint/no-inferrable-types
    startsWith: RegExp = new RegExp('^[0-9]*$');
    filterProduitForm: FormGroup | null = null;
    AjouterLigneForm: FormGroup | null = null;

    navigation: Pagination = { skip: 0, pageSize: 15 };
    pageSizes: number[] = [5, 10, 15, 20];

    last30Days = this.srv.getLast30Days();
    listTypeAlert = [
        { label: 'Rentrant', value: 'R' },
        { label: 'Lancement', value: 'L' }
    ];
    searchData = new FormControl('');
    localSearchData = new FormControl('');

    private searchSubscription: Subscription;
    searchSelectedItem: any;
    dirty: boolean;

    codeSite: number;
    principal: Principal;
    subscriptions: Subscription = new Subscription();

    constructor(
        private srv: CommandeService,
        private router: Router,
        private fb: FormBuilder,
        private modalService: NgbModal,
        private offresService: OffresService,
        public authService: AuthService,
        private userInputService: UserInputService
    ) { }

    ngOnInit() {

        // Get the principal object
        this.principal = this.authService.getPrincipal();
        this.subscriptions.add(
        this.authService.principal$.subscribe((principal) => {
            this.principal = principal;
        })
        );

        // Get codeSite from localStorage
        const targetGrossiste = JSON.parse(
            sessionStorage.getItem('TARGET_GROSSISTE')
        );
        this.codeSite = targetGrossiste?.noeud?.codeSite;

     
        this.setPageSize();
        this.filterProduitForm = new FormGroup({
            dateAu: new FormControl(null),
            dateDu: new FormControl(null),
            typeAlert: new FormControl(null),
        });
        this.AjouterLigneForm = this.fb.group({
            designationProduit: [null],
            typeAlert: [null]
        });
        this.childTabId = 6;   // Default child tab
        this.activeTabId = this.childTabId;
        this.initSearch();
        // this.listenToRechercheProduitChanges();
        // this.listenToTypeAlerteChanges();
          this.localSearchData.valueChanges.pipe(
      debounceTime(300), 
      switchMap(searchTerm => {
        this.filterData(searchTerm);
        return this.filteredData; 
      })
    ).subscribe();
    }
    // listenToTypeAlerteChanges() {
    //     this.filterProduitForm.get('typeAlert')?.valueChanges
    //         .pipe(
    //             takeUntil(this.unsubscribe$),
    //             debounceTime(300),
    //             distinctUntilChanged()
    //         )
    //         .subscribe((selectedTypeAlerte: string) => {
    //             const newParentTabId = selectedTypeAlerte === 'L' ? 2 : 1;
    //             if (this.parentTabId !== newParentTabId) {
    //                 this.parentTabId = newParentTabId;
    //             }
    //         });
    // }

    setPageSize(currentHeight?: number): void {
        const dynamicSize = getDynamicPageSize(currentHeight);

        if (dynamicSize !== this.navigation.pageSize) {
            this.navigation.pageSize = dynamicSize;

            this.pageSizes.push(dynamicSize);
            this.pageSizes = this.pageSizes.sort((a, b) => a - b);

            currentHeight && this.initSearch();
        }
    }

    initSearch() {

    const { typeAlert, dateDu, dateAu,  ...values } = this.filterProduitForm.getRawValue();
    let searchParams = {
        ...values,
         codeSite: this.codeSite,
        allDeletedAlert:  false,
           
        typeAlert: this.parentTabId === 1 ? 'R' : 'L',
    }; 

    // this.childTabId = 3; 

    switch (this.childTabId) {
        case 3: 
        const { dateDu, dateAu } = this.filterProduitForm.getRawValue();
        let formattedDateDu = dateDu && dateDu.isValid() ? dateDu.startOf('day') : null;
        let formattedDateAu = dateAu && dateAu.isValid() ? dateAu.endOf('day') : null;
        searchParams.dateDu = formattedDateDu ? formattedDateDu.format('YYYY-MM-DD HH:mm:ss') : null;
        searchParams.dateAu = formattedDateAu ? formattedDateAu.format('YYYY-MM-DD HH:mm:ss') : null;
            break;
        case 4: // "Aujourd'hui"
            searchParams.dateDu = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss');
            searchParams.dateAu = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss');
            
            break;
        case 5: // "Cette semaine"
            searchParams.dateDu = moment().startOf('week').format('YYYY-MM-DD HH:mm:ss');
            searchParams.dateAu = moment().endOf('week').format('YYYY-MM-DD HH:mm:ss');

            break;
        case 6: // "Ce mois"
            searchParams.dateDu = moment().startOf('month').format('YYYY-MM-DD HH:mm:ss');
            searchParams.dateAu = moment().endOf('month').format('YYYY-MM-DD HH:mm:ss');

            break;
    }

        this.srv.searchAlertProduit(searchParams).subscribe(
            (response: any) => {
            
                this.gridView = {
                    data: response || [],  
                    total: response.total || 0,  
                };
                this.filterData(this.localSearchData.value); 

            },
           
        );  
    }

    onParentTabChange(newTabId: number): void {
     

        if (this.parentTabId !== newTabId) {
            this.parentTabId = newTabId;

            if (this.parentTabId === 1) {
                this.filterProduitForm.get('typeAlert')?.setValue('R');
            } else if (this.parentTabId === 2) {
                this.filterProduitForm.get('typeAlert')?.setValue('L');
            }
            this.childTabId = 6; 
            this.activeTabId = this.childTabId;

            this.initSearch();  
        }
    }

    onTabChange(newTabId: number): void {
            this.childTabId = newTabId;
            this.activeTabId = newTabId;
            this.initSearch();
    }

    
    formatter = (result: ProduitDTO) => result.libelle;
    search: OperatorFunction<string, readonly string[]> = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            filter((query) => query && query.length >= 3),
            switchMap((searchText) => this.srv.getCatalogueProduits({
                codeProduitSite: null,
                genericCriteria: searchText
            }, { skip: 0, pageSize: 10 })),
            map(res => res.content)
        );

        selectedProdact(event: any): void {
            const selectedProduct = event.item; 
            this.model = selectedProduct; 
            this.AjouterLigneForm.patchValue({
                designationProduit: selectedProduct
            });
        }   

        filterData(searchTerm: any): void {
            if (typeof searchTerm !== 'string') {
                searchTerm = '';
                this.localSearchData.setValue('');
            }
        
            const term = searchTerm.toLowerCase();
        
            this.filteredData = this.gridView.data.filter(item =>
                item.proLibelle.toLowerCase().includes(term) ||
                item.pph.toString().includes(term) ||
                item.ppv.toString().includes(term)
            );
        }
          goSearch(): void {
            this.filterData(this.localSearchData.value);  
          }
    ngOnDestroy() {
        if (this.searchSubscription) {
            this.searchSubscription.unsubscribe();
        }
    }

    filterList(searchQuery: string) {
        let criteriaKey: string, criteria: any = {};
         criteriaKey = this.startsWith.test(searchQuery) ? 'code' : 'raisonSociale';

        criteria = { ...criteria, [criteriaKey]: searchQuery, typeEntreprise: [SocieteType.FABRIQUANT] };

        return this.offresService.searchSociete(criteria);
    }

    searchSociete = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase())
                }
                return of({ content: [] });
            }),
            map(res => res.content.slice(0, 5))
        );

    societeFormatter = (result: Fournisseur) => {
        return (result instanceof Object) ?
            `${result?.code}: ${result?.raisonSociale}` : '';
    };

    pageChange(event: PageChangeEvent): void {
        if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.navigation.pageSize = event.take;

            this.initSearch();
        }
    }

    getPageNumber(skip, pageSize) {
        return Math.floor(skip / pageSize);
    }

    navige(e) {
        this.router.navigate([`commande-web/fiche-produit/${e}/produit-fournisseur`], {
            queryParams: { page: 'alert-produit' }
        });
    }

 recherche() {
    this.childTabId = 3;
    this.activeTabId = 3;
    if (this.filterProduitForm.valid) {
          const selectedTypeAlerte = this.filterProduitForm.get('typeAlert')?.value;
          const newParentTabId = (selectedTypeAlerte === 'L') ? 2 : 1;
  
          if (this.parentTabId !== newParentTabId) {
              this.parentTabId = newParentTabId;
          }
        this.navigation.skip = 0;
        this.initSearch();
        this.dismiss();
    } else {
        console.log('Form is invalid');
    }
}

    AjouterLigne(): void {
        if (this.AjouterLigneForm.valid) {
            const formValues = this.AjouterLigneForm.value;

            const alertProduitData: AlertProduitDto = {
                id: null,  
                codeSite: this.codeSite,  
                produitFournisseur: formValues.designationProduit,
                typeAlert: formValues.typeAlert,
                dateCreation: null,  
                pph:null,  
                ppv: null,  
                proCode: null,           
                proFamTarf: null,     
                proLibelle: null,        
                proPrixRay2:null, 

            };
            
            // Call the addAlertProduit method to send the data to the backend
            this.srv.addAlertProduit(alertProduitData).subscribe(
                (response) => {
                    const newParentTabId = formValues.typeAlert === 'L' ? 2 : 1;
                    if (this.parentTabId !== newParentTabId) {
                        this.parentTabId = newParentTabId;
    
                        this.filterProduitForm.get('typeAlert')?.setValue(formValues.typeAlert, { emitEvent: false });
                    }
                    
                    this.initSearch();
                    this.AjouterLigneForm.get('designationProduit')?.reset();
                    this.dismiss(); 
                                   
                    this.viderAjoutLigne(); 
                },
            );
        } else {
            console.log('Form is invalid');
        }
        
    }
    dismiss() {
        this.modalService.dismissAll();
    }

    deleteLine(id: number): void {
        

        this.userInputService
      .confirm(null, 'Êtes-vous sûr de vouloir supprimer cette ligne ?')
      .then(
        () => {
            this.srv.deleteAlertProduit(id).subscribe(
                (response: any) => {
    this.initSearch()
                }
               
            );
        },
        () => null
      );

    }
    
    vider() {
        if (this.filterProduitForm.dirty) {
            this.navigation.skip = 0;
            this.filterProduitForm.reset({
                dateDu: this.srv.getLast30Days(),
                dateAu: this.srv.getLast30Days(),
            });
            
            this.initSearch();
            this.dismiss();
        } else {
            this.dismiss();
        }
    }
    
    viderAjoutLigne() {
        if (this.AjouterLigneForm.dirty) {
            this.AjouterLigneForm.reset();
            this.navigation.skip = 0;
            this.dismiss();
        } else {
            this.dismiss();
        }
    }

    openModal(modalContent: any, size = 'lg') {
        const typeAlertValue = this.parentTabId === 1 ? 'R' : 'L';

        this.filterProduitForm.get('typeAlert')?.setValue(typeAlertValue);

        this.modalService.open(modalContent, {
            ariaLabelledBy: 'modal-basic-title',
            size,
            backdrop: 'static',
            centered: true
        })
    }
    openAjouterLigneModal(modalContent: any, size = 'md') {
        const typeAlertValue = this.parentTabId === 1 ? 'R' : 'L';
        this.AjouterLigneForm.get('typeAlert')?.setValue(typeAlertValue);
    
        // Now open the modal
        this.modalService.open(modalContent, {
            ariaLabelledBy: 'modal-basic-title',
            size,
            backdrop: 'static',
            centered: true
        });
    }


    gridSortChange(sort: SortDescriptor[]): void {
        this.gridSort = sort;

        if (this.gridSort && this.gridSort.length > 0 && this.gridSort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.initSearch();
    }
}
