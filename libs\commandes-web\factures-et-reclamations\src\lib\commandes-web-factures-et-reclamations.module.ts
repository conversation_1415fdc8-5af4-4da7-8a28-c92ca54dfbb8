import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CommandesWebFacturesEtReclamationsRoutingModule } from './commandes-web-factures-et-reclamations-routing.module';
import { ListeFacturesComponent } from './pages/liste-factures/liste-factures.component';
import { ListeReclamationComponent } from './pages/liste-reclamation/liste-reclamation.component';
import { SharedModule } from '@wph/commandes-web/shared';
import { DetailFactureComponent } from './pages/detail-facture/detail-facture.component';
import { ListeGuidesComponent } from './pages/liste/liste-guides.component';

@NgModule({
  declarations: [ListeFacturesComponent, ListeReclamationComponent, DetailFactureComponent, ListeGuidesComponent],
  imports: [CommonModule, SharedModule, CommandesWebFacturesEtReclamationsRoutingModule],
})
export class CommandesWebFacturesEtReclamationsModule { }
