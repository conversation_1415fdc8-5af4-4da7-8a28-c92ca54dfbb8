import { DatePipe, Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertService } from '@wph/shared';
import { CommandeService } from '../../services/commande.service';
import { GridDataResult, ScrollMode } from '@progress/kendo-angular-grid';
import { AuthService } from '@wph/core/auth';
import { ExportPdf, ExportPdfService } from '@wph/web/shared';

@Component({
    selector: 'wph-bl-commande',
    templateUrl: './bl-commande.component.html',
    styleUrls: ['./bl-commande.component.scss']
})
export class BlCommandeComponent implements OnInit {
    idParam: number;
    gridData: any;
    total = 0;
    mySelection: string[] = [];
    grid: GridDataResult;
    client: string | null = null;
    ville: string | null = null;
    scrollable: ScrollMode = 'scrollable';

    hasRoleAgentPointVente: boolean;

    exportPdfRef: ExportPdf;

    constructor(
        private router: Router,
        private datePipe: DatePipe,
        private location: Location,
        private srv: CommandeService,
        private route: ActivatedRoute,
        private authService: AuthService,
        private alertService: AlertService,
        private exportPdfServ: ExportPdfService,
    ) { }

    ngOnInit() {
        this.buildExport();

        this.route.params.subscribe((params) => {
            this.idParam = params['id'];
            this.initSearch();
        });

        const qParams = this.route.snapshot.queryParams;

        if (!!qParams['client'] && !!qParams['ville']) {
            this.ville = qParams['ville'];
            this.client = qParams['client'];
        } else if (this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])) {
            this.ville = this.authService.currentUser()?.societe?.ville;
            this.client = this.authService.currentUser()?.societe?.raisonSociale;
        }

        this.hasRoleAgentPointVente = this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']);
    }

    buildExport(): void {
        this.exportPdfRef = this.exportPdfServ
            .ref<any>()
            .setTitle('Bon de Livraison')
            .addColumn('designation', 'Produit', { width: 180, bold: true })
            .addColumn('qtiteCmde', 'Qté Commandée', { width: 80, type: 'integer', bold: true })
            .addColumn('qtiteLivr', 'Qté Livrée', { width: 80, type: 'integer', bold: true })
            .addColumn('ppv', 'PPV', { width: 100, type: 'decimal', bold: true })
            .addColumn('pph', 'PPH', { width: 100, type: 'decimal', bold: true })
            .addColumn('tva', 'Taxe (%)', { width: 100, type: 'decimal', bold: true })
            .addColumn('*', 'Total', {
                width: 100, type: 'decimal', bold: true, transform: (value) => {
                    return ((value?.pph && value?.qtiteLivr) ? value?.pph * value?.qtiteLivr : 0).toString();
                }
            })
            .setData([]);
    }

    initSearch() {
        this.srv.getBLCommande(this.idParam).subscribe((res) => {
            if (res) {
                this.gridData = res;
                this.grid = { data: res?.details, total: res?.details?.length };

                this.gridData.details.forEach((e) => {
                    this.total += Number((e.pph || 0) * (e.qtiteLivr || 0));
                });

                this.exportPdfRef
                    .setData(this.gridData?.details)
                    .addSyntheseElement('Total PPH Net', this.total, { alignment: 'right', type: 'decimal', suffix: ' DH', bold: true })
                    .setHeaderInfo([
                        {
                            title: 'Numéro du BL',
                            value: this.gridData?.numeroBl
                        },
                        {
                            title: 'Date du BL',
                            value: this.datePipe.transform(this.gridData?.dateBl, 'yyyy-MM-dd')
                        },
                        {
                            title: 'Code Client',
                            value: this.gridData?.codeClientSite
                        },
                        {
                            title: 'Client',
                            value: this.client
                        },
                        {
                            title: 'Ville',
                            value: this.ville
                        }
                    ]);

            } else {
                this.alertService.warning('Bl inexistant', 'MODAL');
            }
        });
    }

    goBack() {
        if (this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])) {
            this.router.navigateByUrl('/commande-web/list-commandes');
        } else if (this.authService.hasAnyAuthority(['ROLE_AGENT_COMMERCIAL', 'ROLE_AGENT_FOURNISSEUR'])) {
            this.router.navigateByUrl('/commande-web/list/normales');
        } else {
            this.location.back();
        }
    }

}
