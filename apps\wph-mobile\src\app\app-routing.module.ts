import { NgModule } from "@angular/core";
import { PreloadAllModules, RouterModule, Routes } from "@angular/router";
import { LayoutComponent } from "@wph/mobile/layout";
import { AuthGuardService } from "@wph/core/auth";

const routes: Routes = [
  {
    path: "",
    pathMatch: "full",
    redirectTo: "accueil"
  },
  {
    path: "auth",
    loadChildren: () => import("@wph/mobile/auth").then((m) => m.MobileAuthModule)
  },
  {
    path: "inscription",
    loadChildren: () => import("@wph/mobile/inscription/pages").then((m) => m.MobileInscriptionPagesModule)
  },
  {
    path: "",
    component: LayoutComponent,
    children: [
      {
        path: "accueil",
        loadChildren: () => import("@wph/mobile/accueil/pages").then((m) => m.MobileAccueilPagesModule)
      },
      {
        path: "commandes",
        loadChildren: () => import("@wph/mobile/commandes/pages").then((m) => m.MobileCommandesPagesModule)
      },
      {
        path: "offres",
        loadChildren: () => import("@wph/mobile/offres/pages").then((m) => m.MobileOffresPagesModule)
      },
      {
        path: "grossistes",
        loadChildren: () => import("@wph/mobile/grossistes/pages").then((m) => m.MobileGrossistesPagesModule)
      },
      {
        path: "account",
        loadChildren: () => import("@wph/mobile/account/pages").then((m) => m.MobileAccountPagesModule)
      }
    ],
    canActivate: [AuthGuardService]
  }


];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
