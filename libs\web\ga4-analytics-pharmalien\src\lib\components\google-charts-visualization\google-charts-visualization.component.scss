:host {
  display: block;
  width: 100%;
  flex-direction: column;
  height: 100%;
}

.google-chart-container {
  width: 100%;
  height: 430px;
  border-radius: 10px;
  padding: 10px;
  flex-grow: 1;
  overflow-x: auto;
  overflow-y: hidden;

  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-clip: padding-box;
    background-color: transparent !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
  }

  &::-webkit-scrollbar-thumb {
    width: 10px;
    height: 10px;
    background: #ffb53e;
    border-radius: 20px;
  }

  &::-webkit-scrollbar-track {
    border-radius: 15px;
    width: 10px !important;
    padding-inline: 5px;

  }
}

.chart-type-switcher {
  flex-shrink: 0;
}

.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.rotate-90-cw i,
i.rotate-90-cw {
  display: inline-block;
  transform: rotate(90deg);
}