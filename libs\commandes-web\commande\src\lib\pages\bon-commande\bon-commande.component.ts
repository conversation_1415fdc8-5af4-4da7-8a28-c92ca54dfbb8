import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommandeService } from '../../services/commande.service';
import { GridDataResult, ScrollMode } from '@progress/kendo-angular-grid';
import { AuthService } from '@wph/core/auth';
import { ExportPdf, ExportPdfService, MomentTimezonePipe } from '@wph/web/shared';
import { LigneCommandeDto } from '../../models/LigneCommandeDto';

@Component({
    selector: 'wph-bon-commande',
    templateUrl: './bon-commande.component.html',
    styleUrls: ['./bon-commande.component.scss']
})
export class BonCommandeComponent implements OnInit {
    idParam: string;
    gridData: any;
    total = 0;
    mySelection: string[] = [];
    scrollable: ScrollMode = 'scrollable';

    grid: GridDataResult = { data: [], total: 0 };
    showClientDetails: boolean;

    exportPdfRef: ExportPdf;

    constructor(
        private router: Router,
        private location: Location,
        private srv: CommandeService,
        private route: ActivatedRoute,
        private authService: AuthService,
        private exportPdfServ: ExportPdfService,
        private momentTzPipe: MomentTimezonePipe
    ) { }

    ngOnInit() {
        this.buildExport();
        this.showClientDetails = this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']);

        this.route.params.subscribe(params => {
            this.idParam = params['id'];
            this.initSearch();
        });
    }

    buildExport(): void {
        this.exportPdfRef = this.exportPdfServ
            .ref<LigneCommandeDto>()
            .setTitle('Bon de commande')
            .addColumn('libelleProduit', 'Produit', { width: 180, bold: true })
            .addColumn('qteCmd', 'Qté Validée', { width: 80, type: 'integer', bold: true })
            .addColumn('ppv', 'PPV', { width: 120, type: 'decimal', bold: true })
            .addColumn('prixVenteTtc', 'PPH', { width: 120, type: 'decimal', bold: true })
            .addColumn('tauxTva', 'Taxe (%)', { width: 120, type: 'decimal', bold: true })
            .addColumn('totalNetTtc', 'Total PPH Net TTC', { width: 120, type: 'decimal', bold: true })
            .setData([]);
    }

    initSearch() {
        this.srv.getBonCommande(this.idParam)
            .subscribe(res => {
                this.gridData = res;
                res.lignes.forEach(e => {
                    this.total += Number(e.totalNetTtc);
                });

                this.grid = { data: res?.lignes, total: res?.lignes?.length };

                this.exportPdfRef
                    .setData(res.lignes)
                    .addSyntheseElement('Total PPH Net TTC', this.total, { alignment: 'right', type: 'decimal', suffix: ' DH', bold: true })
                    .setHeaderInfo([
                        {
                            title: 'Crée le',
                            value: `${this.momentTzPipe.transform(this.gridData?.dateCommande, 'yyyy-MM-DD HH:mm', 'Africa/Casablanca')}`
                        },
                        {
                            title: 'Numéro de commande',
                            value: this.gridData?.codeCommande
                        },
                        {
                            title: 'Fournisseur',
                            value: this.gridData?.distributeur?.raisonSociale
                        },
                        {
                            title: 'Commandé le',
                            value: this.momentTzPipe.transform(this.gridData?.dateEnvoiToFournisseur, 'yyyy-MM-DD HH:mm', 'Africa/Casablanca')
                        },
                        {
                            title: 'Client',
                            value: this.gridData?.userCreateur?.entrepriseDTO?.raisonSociale
                        },
                        {
                            title: 'Ville',
                            value: this.gridData?.ville
                        }
                    ]);

            });
    }

    goBack() {
        if (this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])) {
            this.router.navigateByUrl('/commande-web/list-commandes');
        } else if (this.authService.hasAnyAuthority(['ROLE_AGENT_COMMERCIAL', 'ROLE_AGENT_FOURNISSEUR'])) {
            this.router.navigateByUrl('/commande-web/list/normales');
        } else {
            this.location.back();
        }
    }

}
