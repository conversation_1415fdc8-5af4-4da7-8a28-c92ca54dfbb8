<div id="fiche-produit mt-0">
    <!-- Start Of Header -->
    <div class="rowline mb-0">
        <div class="page-title-box row">
            <h4 class="page-title fw-4 ps-2 col-7">Détail du produit</h4>
            <div class="col-5 px-1">
                <div class="row justify-content-end align-items-center">

                    <button (click)="goBack()" class="btn btn-sm btn-dark rounded-pharma mx-1">
                        <i class="mdi mdi-close"></i>
                        Quitter
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- END HEADER -->

    <div class="box px-1">

        <div class="row d-flex justify-content-center p-0">
            <div class="col-xl-6 col-12 mb-2 pr-xl-0">
                <div class="card h-100">
                    <div class="card-header p-2">
                        <span class="h4 text-center my-0 text-uppercase">
                            Informations produit
                        </span>
                    </div>

                    <div class="card-body p-1">

                        <div class="table-responsive">
                            <table class="table tablewithoutborder mb-0">

                                <tbody>
                                    <tr class="no-border">
                                        <td><b>Libelle Complet</b></td>
                                        <td>{{dataProduit?.libelle}}</td>

                                    </tr>
                                    <tr>
                                        <td><b>Laboratoire</b></td>
                                        <td>{{dataProduit?.libelleLabo}}</td>

                                    </tr>
                                    <tr>
                                        <td><b>Code barre</b></td>
                                        <td>{{dataProduit?.codeAbarre}}</td>

                                    </tr>
                                    <tr>
                                        <td><b>Catégorie</b></td>
                                        <td>{{dataProduit?.libelleCategorie}}</td>

                                    </tr>

                                    <tr>
                                        <td><b>Forme</b></td>
                                        <td>{{dataProduit?.libelleForme}}</td>

                                    </tr>
                                    <tr>
                                        <td><b>PPV</b></td>
                                        <td>{{dataProduit?.ppv | number:'1.2-2':'fr-FR'}}</td>

                                    </tr>
                                    <tr>
                                        <td><b>PPH</b></td>
                                        <td>{{dataProduit?.pph | number:'1.2-2':'fr-FR'}}</td>

                                    </tr>
                                    <tr>
                                        <td><b>TVA</b></td>
                                        <td>{{dataProduit?.tva | number:'1.2-2':'fr-FR'}} %</td>

                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- end table-responsive -->

                    </div>
                </div>
            </div> <!-- end col -->

            <div *ngIf="isProduitFournisseur" class="col-xl-6 col-12 mx-0 mb-2">
                <div class="card px-1 py-0">

                    <div class="card-header p-2">
                        <span class="h4 text-center my-0 text-uppercase">
                            Disponibilité Chez Fournisseur
                        </span>
                    </div>

                    <div class="card-body">

                        <div class="row d-flex align-items-center justify-content-center">
                            <div class="col-sm-12 col-lg-4">
                                <span class="mr-1 d-flex align-items-center justify-content-center">
                                    Vérifier la quantité chez le fournisseur
                                </span>
                            </div>

                            <div class="col-sm-12 col-lg-8">
                                <div class="my-2 d-flex row align-items-center justify-content-center">

                                    <up-down-input class="mr-2 " (value)="searchAddQteCmd($event)"></up-down-input>
                                    <button class="s2 btn btn-success rounded-pharma text-white" (click)="dispoCheck()">Check
                                    </button>

                                    <span *ngIf="disponibiliteCode != null" class="ml-2 mr-1 mdi mdi-circle mdi-18px {{
                                        disponibiliteCode[0] === 'A'
                                            ? 'text-success'
                                            : disponibiliteCode[0] === 'B'
                                            ? 'text-warning'
                                            : disponibiliteCode[0] === 'C'
                                            ? 'text-warning'
                                            : 'text-danger'
                                        }}">
                                    </span>
                                    <span *ngIf="disponibiliteCode != null">
                                        {{ disponibiliteLibelle || (disponibiliteCode | produitDispo) }}
                                    </span>

                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="row">
                            <div class="col-12 ml-2 ml-sm-0">

                                <div class="fournisseur-commande row align-items-center justify-content-center">
                                    <button class="btn btn-primary rounded-pharma"
                                        (click)="commanderProduit()">Commander</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- end col -->
        </div>
        <!-- end row -->

    </div>
</div>