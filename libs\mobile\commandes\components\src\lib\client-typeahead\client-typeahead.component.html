<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="cancelChanges(); !selectedClient && (search.value = null);">Annuler</ion-button>
    </ion-buttons>

    <ion-buttons slot="end">
      <ion-button (click)="confirmChanges()">Confirmer</ion-button>
    </ion-buttons>
  </ion-toolbar>

  <ion-toolbar>
    <ion-searchbar [value]="searchBarValue"
      [placeholder]="isClientFournisseur ? 'Code Local ou Raison Sociale' : 'Code ou Raison Sociale, Ville'"
      (ionChange)="searchbarInput($event)" #search></ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding-top">
  <ion-list lines="full" id="modal-list" [inset]="true">
    <ion-radio-group [value]="selectedClient" (ionChange)="radioChange($event)">
      <ion-item [id]="client?.code" class="ion-no-padding" lines="full"
        *ngFor="let client of filteredListeClients; trackBy: trackItems">
        <ion-radio slot="start" [value]="client"></ion-radio>

        <ion-badge>{{ isClientFournisseur ? client?.code : 'G' + client?.code }}</ion-badge>
        <ion-label>{{ client?.raisonSociale }}</ion-label>
        <ion-badge color="dark" slot="end">{{ client?.ville }}</ion-badge>
      </ion-item>

      <ion-item
        *ngIf="!isLoading && isClientFournisseur && search.value?.length && !filteredListeClients?.length && !isLoading"
        id="client-fournisseur-wildcard" lines="full" class="ion-no-padding">
        <ion-radio slot="start" [value]="searchBarValue"></ion-radio>
        <ion-badge>{{ search.value }}</ion-badge>
        <ion-label>(Code local seulement)</ion-label>
      </ion-item>
    </ion-radio-group>

    <div class="first-load-container" *ngIf="isLoading">
      <ion-spinner></ion-spinner>
    </div>
  </ion-list>

  <wph-empty-list
    *ngIf="!isLoading && ((!filteredListeClients.length && !isClientFournisseur) || (!search.value?.length && isClientFournisseur))"
    [message]="(search?.value?.length < 3 && !isClientFournisseur) ? 'Veuillez saisir au moins 3 caractères' : 'Aucun résultat trouvé'">
  </wph-empty-list>
</ion-content>