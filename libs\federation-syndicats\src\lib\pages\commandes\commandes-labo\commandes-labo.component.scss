
.card, .card-header, .card-body {
    border-radius: var(--winoffre-base-border-radius) !important;
}

.b-radius {
    border-radius: var(--winoffre-base-border-radius) !important;
}
.btn {
    font-size: 1rem;
    font-weight: 600;
}

.input-group {
    .form-control {
      color: black;
      font-weight: 700;
        border-top-left-radius: var(--winoffre-base-border-radius);
        border-bottom-left-radius: var(--winoffre-base-border-radius);

    }
    .btn {
      border-top-right-radius: var(--winoffre-base-border-radius);
      border-bottom-right-radius: var(--winoffre-base-border-radius);
    }
  }

label {
    color: black !important;
    font-weight: 700 !important;
}

.picker-input {
    .form-control{
        border-radius: var(--winoffre-base-border-radius) !important;
    }
}

.selected-switch {
    color: #f3f3f3;
    background: var(--wf-primary-500);
}

.card-view {
    height: calc(100vh - 190px);
    overflow-x: hidden;
    overflow-y: auto;
}

.opacity-light {
  opacity: 0.6 !important;
}


.btn-success{
  background: var(--fs-success) !important;
  border-color: var(--fs-success) !important;
}

.btn-danger{
  background: var(--fs-danger) !important;
  border-color: var(--fs-danger) !important;
}