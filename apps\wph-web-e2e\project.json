{"name": "wph-web-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/wph-web-e2e/src", "projectType": "application", "targets": {"e2e": {"executor": "@nrwl/cypress:cypress", "options": {"cypressConfig": "apps/wph-web-e2e/cypress.config.ts", "devServerTarget": "wph-web:serve:development", "testingType": "e2e"}, "configurations": {"production": {"devServerTarget": "wph-web:serve:production"}}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/wph-web-e2e/**/*.{js,ts}"]}}}, "tags": [], "implicitDependencies": ["wph-web"]}