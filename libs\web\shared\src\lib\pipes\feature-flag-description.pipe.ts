import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
    pure: true,
    name: 'flagDescription'
})
export class FeatureFlagDescriptionPipe implements PipeTransform {
    transform(value: string): string {
        switch (value) {
            case 'supporterCanEditCommande':
                return `Un supporteur d'une commande consolidée peut modifier la commande`;
            case 'supporterCanValiderCommande':
                return `Un supporteur d'une commande consolidée peut valider la commande`;
            default:
                return value;
        }
    }
}
