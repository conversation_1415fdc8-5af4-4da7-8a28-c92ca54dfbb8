.see-more {
  text-decoration: underline;
  color: var(--winoffre-text-secondary);
  font-weight: 600;
}

.loading-container {
  height: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  background:red !important;
}

.b-radius {
  border-radius: 10px;
}

.offres-card {
  overflow-x: hidden !important;
  overflow-y: auto !important;
  -ms-overflow-style: none;
  scrollbar-color: var(--wo-primary-400) white !important;

  scrollbar-width: thin; 

  ::-webkit-scrollbar {
    display: none;
    width: 6.5px;
    height: 8.5px;
    border-radius: 5px;
    background-clip: padding-box;
    border: 1px solid transparent;
    background-color: transparent !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
  }
}

.card-row {
  padding: 5px 10px !important;
}

.card {
  border: none;
  border-radius: var(--winoffre-base-border-radius);
  background: transparent !important;

  .card-header {
    margin-bottom: 5px;
    border-radius: var(--winoffre-base-border-radius);

    button {
      border-radius: var(--winoffre-base-border-radius);
    }
  }
}

.opacity-light {
  opacity: .4 !important;
  pointer-events: none !important;
}


.btn-wo-selected {
  background: var(--wo-primary-400) !important;
}

.actions-icons.btn-success {
  background: green !important;
}

.actions-icons.btn-danger {
  background: darkred !important;
}

.type-offre-btn {
  font-weight: 600;
  border-radius: 5px;
  color: var(--wo-primary-500);
  border: 1px solid var(--wo-primary-500);

  &:hover {
    color: #fff;
    background: var(--wo-primary-500);
  }

  &.selected {
    color: #fff;
    background: var(--wo-primary-500);
  }
}