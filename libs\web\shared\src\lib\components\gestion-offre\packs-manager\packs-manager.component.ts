import { Component, Input, OnInit, OnDestroy, ViewChild, TemplateRef, OnChanges, SimpleChanges } from '@angular/core';
import { Subscription } from 'rxjs';
import { BlocOffre, Fournisseur, Offre, OffresService, Produit, toJSON } from '@wph/data-access';
import { UserInputService, BootstrapColorClasses } from '@wph/web/shared';
import { AlertService, OutputTAP, PlateformeService } from '@wph/shared';
import { SortDescriptor } from '@progress/kendo-data-query';
import { RechercheProduitComponent } from '../recherche-produit/recherche-produit.component';

@Component({
  selector: 'wph-packs-manager',
  templateUrl: './packs-manager.component.html',
  styleUrls: ['./packs-manager.component.scss'],
})
export class PacksManagerComponent implements OnInit, OnDestroy, OnChanges {
  @Input() listeBlocsOffreProduits: BlocOffre[];
  @ViewChild('blocGroupeTemplate') blocGroupeTemplate: TemplateRef<any>;

  @Input() set offre(offre: Offre) {
    this._offre = offre;
  }
  get offre() {
    return this._offre;
  }

  @Input() set readOnly(value: boolean) {
    this._readOnly = value;
  }
  get readOnly() {
    return this._readOnly;
  }

  @Input() activeHeaderIndex: number;

  activeTabId: number;
  selectedBlocElement: BlocOffre;
  @Input() listeOffreurs: Fournisseur[];
  @Input() listeFournisseursGrossiste: any[];
  @Input() listeFournisseursLabo: any[];
  @Input() validDateLivraison: boolean;
  activeItemId: any;

  _offre: Offre;
  typePack: string;
  blocOffre: BlocOffre;
  selectElementSubscription: Subscription = null;
  resultExisteFilsBlocsProduits: boolean = null;
  filsProduits: BlocOffre[] = null;
  filsNonProduits: BlocOffre[] = null;
  pathElementsToSelectedBloc: BlocOffre[] = null;
  
  nestedBlocActiveTabId: string;
  
  indexOfBlockCustom = 0;
  
  numberBlockInPage = 0;
  
  _readOnly: boolean;
  nestedBlocActiveTabIds: { [key: string]: string } = {};
  
  defaultProduitSortMap: { [key: string]: BlocOffre[] } = {};
    
  @ViewChild(RechercheProduitComponent) rechercheProduitComponent: RechercheProduitComponent;
  
  constructor(
    private alertService: AlertService,
    private offresService: OffresService,
    private userInputService: UserInputService,
    private plateformeService: PlateformeService,
  ) { }

  get currentPlateforme$() {
    return this.plateformeService.currentPlateforme$;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.offre) {
      if (!this.offre.listeBlocs.length) {
        setTimeout(() => {
          this.addBlock('P');
        });
      }
    }
  }

  ngOnInit(): void {
    if (!this.selectElementSubscription) {
      this.selectElementSubscription =
        this.offresService.subjectSelectedBlocElementInEditOffre.subscribe(
          (selectedBlocOffre) => {
            this.selectElement(selectedBlocOffre);
          }
        );
    }

    if (this.offre && this.offre.listeBlocs.length > 0 && this.offre.listeBlocs[0].typeBloc === 'P') {
      this.checkSelectionBloc(true, this.offre.listeBlocs[0]);
    }
  }

  ngOnDestroy(): void {
    if (this.selectElementSubscription) {
      this.selectElementSubscription.unsubscribe();
    }
  }

  onNestedTabChange(event: any, parentId: string) {
    setTimeout(() => {
      this.nestedBlocActiveTabIds[parentId] = event;
    });
  }

  openProduitModal(): void {
    this.rechercheProduitComponent.openModal();
  }

  ajouterProduitsSelectionnes(selectedProduits: Produit[]): void {
    if (this.selectedBlocElement && selectedProduits && selectedProduits?.length) {

      // verification qu'un produit n'est déjà existant
      for (const produit of selectedProduits) {
        if (this.selectedBlocElement.listeFils.find(ligne => ligne.codeProduitCatalogue === produit.codeProduitCatalogue)) {
          this.alertService.error("le produit '" + produit.libelleProduit + "' existe déjà", "MODAL");
          return;
        }
      }

      let isLastFrereProduit = true;
      let blocFictifCree = false;   // quand isLastFrereProduit  on cree un seul seul bloc fictif pour y ajouter les produits selectionnés

      if (this.selectedBlocElement.listeFils && this.selectedBlocElement.listeFils.length &&
        this.selectedBlocElement.listeFils[this.selectedBlocElement.listeFils.length - 1].typeBloc !== "F") {
        //isLastFrereProduit = false;
      }


      for (const produit of selectedProduits) {
        const newBloc: Partial<BlocOffre> = {
          titre: produit.libelleProduit,
          typeBloc: "F",
          listeFils: [] as any,
          blocObligatoire: "N",
          listOfBlocOffresPrerequis: [] as any,
          listOfBlocOffresExclus: [] as any,
          listePaliers: [] as any,
          parent: this.selectedBlocElement,
          offre: this.offresService.getOffreRacine(this.selectedBlocElement),
          catalogue: produit.catalogue,
          colisage: produit?.colisage,
          docImageBlocOffre: produit?.docImageBlocOffre,
          codeProduitCatalogue: produit.codeProduitCatalogue,
          libelleProduit: produit.libelleProduit,
          prixVenteHt: produit.prixVenteHt,
          prixVenteTtc: produit.prixVenteTtc,
          ppv: produit.ppv,
          toJSON,
          blocOrder: this.selectedBlocElement?.listeFils?.length + 1
        };

        if (!isLastFrereProduit) {
          if (!blocFictifCree) {
            this.addBlock(this.selectedBlocElement.listeFils[this.selectedBlocElement.listeFils.length - 1].typeBloc);
            blocFictifCree = true;
          }

          newBloc.parent = this.selectedBlocElement.listeFils[this.selectedBlocElement.listeFils.length - 1]; // re-set parent
          this.selectedBlocElement.listeFils[this.selectedBlocElement.listeFils.length - 1].listeFils.push(newBloc as BlocOffre);

        } else {
          this.selectedBlocElement.listeFils.push(newBloc as BlocOffre);
        }

      }

      this.defaultProduitSortMap[this.selectedBlocElement?.hash] = [...this.selectedBlocElement.listeFils];

    } else {
      this.alertService.error("Aucun produit sélectionné", 'MODAL');
    }

  }

  private sortSelectedProducts(sort: SortDescriptor[]) {
    if (sort && sort.length > 0 && sort[0].dir) {
      if (sort[0].dir === 'asc') {
        this.selectedBlocElement.listeFils = this.selectedBlocElement.listeFils.sort((a: BlocOffre, b: BlocOffre) => a?.titre?.localeCompare(b?.titre));
      } else {
        this.selectedBlocElement.listeFils = this.selectedBlocElement.listeFils.sort((a: BlocOffre, b: BlocOffre) => b?.titre?.localeCompare(a?.titre));
      }
    } else {
      this.selectedBlocElement.listeFils = [...this.defaultProduitSortMap[this.selectedBlocElement?.hash]];
    }
  }

  existeFilsBlocsProduits(currentBloc: BlocOffre = null): boolean {
    if (!currentBloc) {
      currentBloc = this.selectedBlocElement;
    }

    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc === 'F') {
          this.resultExisteFilsBlocsProduits = true;
          if (!this.defaultProduitSortMap[this.selectedBlocElement?.hash] && this.selectedBlocElement?.listeFils?.length) {
            this.defaultProduitSortMap[this.selectedBlocElement?.hash] = [...this.selectedBlocElement?.listeFils];
          }
          return true;
        }
      }
      this.resultExisteFilsBlocsProduits = false;
      return false;
    } else {
      this.resultExisteFilsBlocsProduits = false;
      return false;
    }
  }


  getListeFilsBlocsProduits(currentBloc: BlocOffre = null): BlocOffre[] {
    if (!currentBloc) {
      currentBloc = this.selectedBlocElement;
    }
    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      this.filsProduits = [];
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc === 'F') {
          this.filsProduits.push(bloc);
        }
      }
    } else {
      this.filsProduits = [];
    }
    return this.filsProduits;
  }

  getListeFilsBlocsNonProduits(currentBloc: BlocOffre = null, targetTypeBloc?: string): BlocOffre[] {
    if (!currentBloc) {
      currentBloc = this.selectedBlocElement;
    }
    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      this.filsNonProduits = [];
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc !== 'F' || bloc.typeBloc === targetTypeBloc) {
          this.filsNonProduits.push(bloc);
        }
      }
    } else {
      this.filsNonProduits = [];
    }
    return this.filsNonProduits;
  }

  selectElement(bloc: BlocOffre): void {
    this.selectedBlocElement = bloc;
    this.pathElementsToSelectedBloc = null;
  }

  handleBlocRemoval(value: { bloc: BlocOffre; index: number }): void {
    this.removeBloc(value.bloc, value.index);
  }


  handleActiveItemIdChange(value: string): void {
    this.activeItemId = value;
  }

  handleNestedActiveItemIdChange(value: string): void {
    this.nestedBlocActiveTabId = value;
  }

  handleProduitSortChange(event: { produitParent: BlocOffre, sort: SortDescriptor[] }) {
    if (event && event.produitParent && event.sort) {
      this.checkSelectionBloc(true, event.produitParent);

      this.sortSelectedProducts(event.sort);
    }
  }

  trackItems(index: number, item: BlocOffre): any {
    return item?.hash;
  }

  addBlock(typeBloc: string) {
    let newBloc: BlocOffre;
    this.indexOfBlockCustom = Math.floor(Math.random() * (999999 - 100000)) + 100000;


    if (this.selectedBlocElement) {
      newBloc = {
        titre: "",
        typeBloc,
        listeFils: [] as any,
        blocObligatoire: "N",
        listOfBlocOffresPrerequis: [] as any,
        listOfBlocOffresExclus: [] as any,
        hash: this.indexOfBlockCustom.toString(),
        listePaliers: [] as any,
        parent: this.selectedBlocElement,
        offre: this.offresService.getOffreRacine(this.selectedBlocElement),
        toJSON
      } as BlocOffre;
      this.selectedBlocElement.listeFils.push(newBloc);

      if (typeBloc === "P") {
        newBloc.titre = this.offre?.coffretEnabled ? "Pack Coffret" : "Pack " + this.selectedBlocElement.listeFils.length;
        newBloc.coffretEnabled = this.offre.coffretEnabled;

        this.activeTabId = this.offre.listeBlocs.length;
        this.markActivePackTabAsSelected();
      } else if (typeBloc === "R") {
        newBloc.titre = "Reference " + this.selectedBlocElement.listeFils.length;
      } else if (typeBloc === "G") {
        newBloc.titre = "Groupe " + this.selectedBlocElement.listeFils.length;
      }

      newBloc.blocOrder = newBloc?.parent?.listeFils?.length || newBloc?.offre?.listeBlocs?.length;
    } else {
      newBloc = {
        titre: "",
        typeBloc,
        listeFils: [] as any,
        blocObligatoire: "N",
        listOfBlocOffresPrerequis: [] as any,
        listOfBlocOffresExclus: [] as any,
        listePaliers: [] as any,
        parent: null as any,
        hash: this.indexOfBlockCustom.toString(),
        offre: this.offre,
        toJSON
      } as BlocOffre;

      this.offre.listeBlocs.push(newBloc);
      if (typeBloc === "P") {
        newBloc.titre = this.offre?.coffretEnabled ? "Pack Coffret" : "Pack " + this.offre.listeBlocs.length;
        newBloc.coffretEnabled = this.offre.coffretEnabled;

        this.activeTabId = this.offre.listeBlocs.length;
        this.markActivePackTabAsSelected();
      } else if (typeBloc === "R") {
        newBloc.titre = "Reference " + this.offre.listeBlocs.length;
      } else if (typeBloc === "G") {
        newBloc.titre = "Groupe " + this.offre.listeBlocs.length;
      }

      newBloc.blocOrder = newBloc?.parent?.listeFils?.length || newBloc?.offre?.listeBlocs?.length;
    }

    setTimeout(() => {
      this.setScrollToBlock(newBloc?.hash);
    });

  }

  dupliquerBloc(event: any) {
    const { bloc, index: indexPrd } = event;
    this.userInputService.confirmAlt("Confirmation", "Êtes vous sûr de vouloir dupliquer cet élément ?").then(
      () => {
        this.indexOfBlockCustom = (Math.floor(Math.random() * (999999 - 100000)) + 100000);

        let newBloc = this.offresService.dupliquerBlocOffre(bloc, null, true, 0, this.indexOfBlockCustom.toString());

        if (this.listeBlocsOffreProduits) {
          this.listeBlocsOffreProduits.splice(indexPrd + 1, 0, newBloc);
        }

        if (bloc.typeBloc === 'P') {
          this.activeTabId = this.offre.listeBlocs.length;
          this.markActivePackTabAsSelected();
        }

        setTimeout(() => {
          this.setScrollToBlock(newBloc?.hash)
        },);
      },
      () => null);
  }

  onPackTabChange(activeIndex: number) {
    setTimeout(() => {
      activeIndex && this.checkSelectionBloc(true, this.offre.listeBlocs[activeIndex - 1]);
    });
  }

  setScrollToBlock(blocHash: string) {
    (document.getElementById(blocHash) as HTMLElement)?.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  }

  markActivePackTabAsSelected(): void {
    this.checkSelectionBloc(true, this.offre.listeBlocs[this.activeTabId - 1]);
  }

  checkSelectionBloc(selectBoolean: boolean, bloc: BlocOffre = null) {
    if (selectBoolean) {
      this.offresService.subjectSelectedBlocElementInEditOffre.next(bloc ? bloc : this.blocOffre);
      this.activeItemId = bloc?.hash;
    } else {
      this.offresService.subjectSelectedBlocElementInEditOffre.next(null);
    }
  }

  removeBloc(bloc: BlocOffre, index: number = null) {
    this.userInputService.confirmAlt("Confirmation", "Êtes vous sûr de vouloir supprimer cet élément ?", "Supprimer", "Annuler", BootstrapColorClasses.danger).then(
      () => {

        if (bloc.typeBloc === 'G' || bloc.typeBloc === 'R') {
          this.offresService.supprimerBlocOffre(bloc);
        } else {
          this.offresService.supprimerBlocOffre(bloc, index);
        }

        if (this.listeBlocsOffreProduits) {
          this.listeBlocsOffreProduits.splice(index, 1);
        }

        // Adjust active tab id for the removed bloc and its nested blocs
        this.adjustActiveTabIdAfterRemoval(bloc, index);

        this.selectedBlocElement = null;
        this.pathElementsToSelectedBloc = null;
        this.numberBlockInPage > 0 ? this.numberBlockInPage-- : this.numberBlockInPage = 0;

        // Mark the new active block after removal
        this.markActivePackTabAsSelected();
      },
      () => null
    );
  }

  private adjustActiveTabIdAfterRemoval(bloc: BlocOffre, index: number): void {
    if (bloc.typeBloc === 'P' && this.activeTabId === index + 1) {
      this.activeTabId = this.activeTabId - 1 > 0 ? --this.activeTabId : this.activeTabId;
    } else if (bloc.typeBloc === 'P' && this.activeTabId > index + 1) {
      this.activeTabId--;
    }

    if (bloc.typeBloc === 'G' || bloc.typeBloc === 'R') {
      const nestedBlocIndexSplit = this.nestedBlocActiveTabIds[index]?.split('-');

      if (nestedBlocIndexSplit?.length === 2) {
        let nestedBlocIndex = +nestedBlocIndexSplit[1];

        if (nestedBlocIndex > 2) {
          this.nestedBlocActiveTabIds[index] = `${index}-${--nestedBlocIndex}`;
        } else {
          this.nestedBlocActiveTabIds[index] = `${index}-${2}`;
        }
      } else {
        if (nestedBlocIndexSplit) {
          let nestedBlocIndex = +nestedBlocIndexSplit[2];

          if (nestedBlocIndex > 2) {
            nestedBlocIndexSplit.pop();
            nestedBlocIndexSplit.push((--nestedBlocIndex).toString())
            this.nestedBlocActiveTabIds[index] = nestedBlocIndexSplit.join('-');
          } else {
            nestedBlocIndexSplit.pop();
            nestedBlocIndexSplit.push('2');
            this.nestedBlocActiveTabIds[index] = nestedBlocIndexSplit.join('-');
          }
        }
      }
    }

  }

  getSelect2DropdownDataFromAssociations(association: OutputTAP[]) {
    if (association?.length) {
      return association?.map(item => {
        return { label: item?.nom_base_winplus, value: item };
      });
    }

    return [];
  }

  getSousBlocId(typeBloc: string) {
    switch (this.plateformeService.getCurrentPlateforme()) {
      case 'WIN_OFFRE':
        return 'pack-tab-nav-wo';
      case 'WIN_GROUPE':
        return typeBloc === 'G' ? 'pack-tab-nav-groupe' : 'pack-tab-nav-reference';
      case 'FEDERATION_SYNDICAT':
        return typeBloc === 'G' ? 'pack-tab-nav-fs-groupe' : 'pack-tab-nav-fs-reference';
      default:
        return '';
    }
  }

}
