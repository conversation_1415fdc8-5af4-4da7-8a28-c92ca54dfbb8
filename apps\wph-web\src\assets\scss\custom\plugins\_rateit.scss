//
// _rateit.scss
//

ngb-rating {
    &:focus {
        outline: none !important;
    }
}

.star {
    color: $gray-400;
    font-size: 1.25rem;

    &.decimal {
        position: relative;
        display: inline-block;
    }

    .half {
        position: absolute;
        display: inline-block;
        overflow: hidden;
        color: $warning;
    }

    &.filled {
        color: $warning;
    }

    // filled variation
    @each $color, $value in $theme-colors {
        &.filled-#{$color} {
            color: $value;
        }
    }
}

