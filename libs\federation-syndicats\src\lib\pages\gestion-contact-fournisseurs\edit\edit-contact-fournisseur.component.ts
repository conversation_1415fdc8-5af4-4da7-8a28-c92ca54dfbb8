import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { FederationSyndicatService } from "../../../services/federation-syndicats.service";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { GroupeEntreprise } from "../../../models/groupe-entreprise.model";
import { EntrepriseCriteriaDto, EntrepriseDTO } from "../../../models/entreprise.model";
import { phoneValidator } from "../../../validators/phone-validator";
import { ContactFournisseurGroupe, OffresService } from "@wph/data-access";
import { AlertService, SocieteType } from "@wph/shared";
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map, Subject, takeUntil } from "rxjs";
import { UserInputService } from "@wph/web/shared";
import { GroupeEntrepriseCriteria } from "../../../models/groupe-entreprise-criteria.model";

@Component({
    selector: 'wph-edit-contact-fournisseur',
    templateUrl: './edit-contact-fournisseur.component.html',
    styleUrls: ['./edit-contact-fournisseur.component.scss']
})
export class EditContactFournisseurComponent implements OnInit, OnDestroy {
    contactId: number;
    isReadOnly: boolean;
    submitted: boolean;
    unsubscribe$: Subject<boolean> = new Subject<boolean>();

    searchFournInput: FormControl = new FormControl();
    monGroupe: GroupeEntreprise;
    editContactFournisseurForm: FormGroup;

    constructor(
        private fb: FormBuilder,
        private router: Router,
        private route: ActivatedRoute,
        private alertService: AlertService,
        private offreService: OffresService,
        private userInputService: UserInputService,
        private fedSyndicatService: FederationSyndicatService
    ) {
        this.initForm();
    }

    get f() {
        return this.editContactFournisseurForm.controls;
    }

    ngOnInit(): void {
        this.route.params.subscribe(params => {
            params['id'] && (this.contactId = +params['id']);

            if (this.contactId) {
                this.fedSyndicatService.getContactFournisseurGroupeById(this.contactId).subscribe(contactFournisseur => {
                    this.editContactFournisseurForm.patchValue(contactFournisseur);
                });
            } else {
                this.listenToSearchInputChanges();
            }
        });

        this.route.queryParams.subscribe(qParams => {
            qParams['readOnly'] && (this.isReadOnly = JSON.parse(qParams['readOnly']));
        });

        this.fedSyndicatService.getMyGroupe().then(groupe => {
            this.monGroupe = groupe;
            this.editContactFournisseurForm.patchValue({ groupe: this.monGroupe });
        });
    }

    listenToSearchInputChanges(): void {
        this.searchFournInput.valueChanges
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(value => {
                if (typeof value === 'object') {
                    this.editContactFournisseurForm.patchValue({ fournisseur: value });
                } else {
                    this.f['fournisseur'].reset();
                }
            });
    }

    initForm(): void {
        this.editContactFournisseurForm = this.fb.group({
            id: null,
            groupe: this.monGroupe,
            nomDelegue: [null],
            prenomDelegue: [null],
            gsmFournisseur: [null, [phoneValidator(true)]],
            fournisseur: this.fb.group(new EntrepriseDTO()),
            emailFournisseur: [null, [Validators.email, Validators.required]],
        });

        this.editContactFournisseurForm.get('fournisseur').setValidators(Validators.required);
    }

    submitContactFournisseurGroupe() {
        this.submitted = true;

        if (this.editContactFournisseurForm.invalid) {
            this.alertService.error(`Merci de compléter les informations suivantes: <ul>
                ${!this.f['fournisseur'].value?.id ? '<li>Fournisseur</li>' : ''}
                ${this.f['emailFournisseur'].invalid ? '<li>Email Fournisseur</li>' : ''}
                </ul>`, 'MODAL')
        } else {
            this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir ${this.contactId ? 'modifier' : 'enregistrer'} ce contact fournisseur ?`)
                .then(
                    () => this.saveContactFournisseurGroupe(),
                    () => null
                );
        }
    }

    saveContactFournisseurGroupe() {
        const payload: ContactFournisseurGroupe = this.editContactFournisseurForm.getRawValue();

        this.fedSyndicatService.saveContactFournisseurGroupe(payload).subscribe(res => {
            this.alertService.successAlt(
                `Le contact du fournisseur: <b>${payload?.fournisseur?.raisonSociale}</b> a été ${this.contactId ? 'modifié' : 'enregistré'} avec succès.`,
                `Contact Fournisseur ${this.contactId ? 'Modifié' : 'Enregistré'}`, 'MODAL'
            );
            this.back();
        });
    }

    filterList(searchQuery: string) {
        const criteria: EntrepriseCriteriaDto = new EntrepriseCriteriaDto({
            raisonSociale: searchQuery,
            fournisseurHasOffre: true,
            typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE],
        });

        return this.offreService.searchSociete(criteria);
    }

    filterGroupe(searchQuery: string) {
        const criteria: GroupeEntrepriseCriteria = new GroupeEntrepriseCriteria({ raisonSociale: searchQuery, typeEntreprises: [SocieteType.GROUPE_CLIENT] });

        return this.fedSyndicatService.searchGroupeEntreprise({ pageSize: 10, skip: 0 }, criteria);
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    fournisseurFormatter = (result: { raisonSociale: any }) =>
        result ? result.raisonSociale : null;

    searchGroupeEntreprise = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term?.length > 1) {
                    return this.filterGroupe(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content)
        );

    groupeEntrepriseFormatter = (result: GroupeEntreprise) => result ? result?.raisonSociale : null;

    back(): void {
        this.router.navigateByUrl('/achats-groupes/gestion-contact-fournisseurs/liste');
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }
}