import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import { CommandeService } from '@wph/commandes-web/commande';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Fournisseur, OffresService, Pagination } from '@wph/data-access';
import { SocieteType } from '@wph/shared';
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import * as moment from 'moment';
import { SortDescriptor } from '@progress/kendo-data-query';
import { getDynamicPageSize } from '@wph/web/shared';

@Component({
    selector: 'app-list-nouveaux-produits',
    templateUrl: './list-nouveaux-produits.component.html',
    styleUrls: ['./list-nouveaux-produits.component.scss'],
})
export class ListNouveauxProduitsComponent implements OnInit {
    gridView: GridDataResult;
    gridSort: SortDescriptor[] = [{ field: 'dateCreation', dir: 'desc' }];

    mySelection: string[] = [];

    startsWith: RegExp = new RegExp('^[0-9]*$');
    filterProduitForm: FormGroup | null = null;

    navigation: Pagination = { skip: 0, pageSize: 15, sortField: 'dateCreation', sortMethod: 'desc' };
    pageSizes: number[] = [5, 10, 15, 20];

    last30Days = this.srv.getLast30Days();
    constructor(
        private srv: CommandeService,
        private router: Router,
        private fb: FormBuilder,
        private modalService: NgbModal,
        private offresService: OffresService
    ) { }

    ngOnInit() {
        this.setPageSize();
        this.filterProduitForm = this.fb.group({
            libelleProduit: [null],
            fournisseur: [null],
            dateCreation: [this.last30Days]
        });
        this.initSearch();
    }

    setPageSize(currentHeight?: number): void {
        const dynamicSize = getDynamicPageSize(currentHeight);

        if (dynamicSize !== this.navigation.pageSize) {
            this.navigation.pageSize = dynamicSize;

            this.pageSizes.push(dynamicSize);
            this.pageSizes = this.pageSizes.sort((a, b) => a - b);

            currentHeight && this.initSearch();
        }
    }

    initSearch() {
        const { dateCreation, ...values } = this.filterProduitForm.getRawValue();
        const searchParams = {
            ...values,
            includedCategories: ["Complement Alimentaire", "Diététique", "Homéopathie", "Médicament", "Produit chimique", "Vétérinaire"],
            dateCreation: dateCreation ? moment(dateCreation) : this.last30Days,
        };

        this.gridView = {
            data: [],
            total: 0,
        };

        this.srv
            .getListeNouveauxProduits(
                searchParams,
                { ...this.navigation, skip: this.getPageNumber(this.navigation.skip, this.navigation.pageSize), pageSize: this.navigation.pageSize }
            )
            .subscribe((res) => {
                this.gridView = {
                    data: res.content,
                    total: res.totalElements
                };
            });
    }

    filterList(searchQuery: string) {
        let criteriaKey: string, criteria: any = {};
        criteriaKey = this.startsWith.test(searchQuery) ? 'code' : 'raisonSociale';

        criteria = { ...criteria, [criteriaKey]: searchQuery, typeEntreprise: [SocieteType.FABRIQUANT] };

        return this.offresService.searchSociete(criteria);
    }

    searchSociete = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase())
                }
                return of({ content: [] });
            }),
            map(res => res.content.slice(0, 5))
        );

    societeFormatter = (result: Fournisseur) => {
        return (result instanceof Object) ?
            `${result?.code}: ${result?.raisonSociale}` : '';
    };

    pageChange(event: PageChangeEvent): void {
        if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.navigation.pageSize = event.take;

            this.initSearch();
        }
    }

    getPageNumber(skip, pageSize) {
        return Math.floor(skip / pageSize);
    }

    navige(e) {
        this.router.navigate([`/commande-web/fiche-produit/${e}/produit-general`]);
    }

    recherche() {
        this.navigation.skip = 0;
        this.initSearch();

        this.dismiss();
    }

    dismiss() {
        this.modalService.dismissAll();
    }

    vider() {
        if (this.filterProduitForm.dirty) {
            this.navigation.skip = 0;
            this.filterProduitForm.reset({
                dateCreation: this.srv.getLast30Days()
            });
            this.initSearch();

            this.dismiss();
        } else {
            this.dismiss();
        }

    }

    openFilterModal(modalContent: any, size = 'lg') {
        this.modalService.open(modalContent, {
            ariaLabelledBy: 'modal-basic-title',
            size,
            backdrop: 'static',
            centered: true
        })
    }

    gridSortChange(sort: SortDescriptor[]): void {
        this.gridSort = sort;

        if (this.gridSort && this.gridSort.length > 0 && this.gridSort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.initSearch();
    }
}
