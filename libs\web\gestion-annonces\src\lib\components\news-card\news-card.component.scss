.info-card {
    min-height: 320px;
}

.alt-actu-container {
    max-height: 250px;
    width: calc(100% + 10px);

    .societe-label {
        font-size: clamp(.85rem, 2vw, 1rem);
        font-weight: 500;
        color: black;
    }

    .title {
        font-size: clamp(1.2rem, 2vw, 1.7rem);
        font-weight: 700;
        color: black;
    }

    .readMore {
        font-size: clamp(.85rem, 2vw, 1rem);
        font-weight: 700;
        color: var(--fs-secondary-shade);
    }

    .content {
        font-size: clamp(.85rem, 2vw, 1rem);
        font-weight: 500;
        color: black;
        display: inline-block;

        max-height: 100px;

        overflow: hidden;
        text-overflow: ellipsis;
    }

    .img-actu-alt {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }
}

.layered-element-height-override {
    max-height: calc((100vh - 265px) * 2/3) !important;
}

.layered-element {
    height: 100%;
    max-height: 480px;
    border-radius: var(--winoffre-base-border-radius);
    width: calc(100% - 12px);
    position: relative;
    background-image: var(--pharmalien-actu-default-img);
    background-color: #fff !important;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    transform-origin: center center;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: var(--winoffre-base-border-radius);
        background: linear-gradient(to top, rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.08));
    }

    .layered-element-content {
        z-index: 10;
        color: #fff;

        .video-container {
            z-index: 25 !important;
        }
    }

    // Shared styles for text elements
    .title,
    .content,
    .readMore {
        font-size: clamp(.85rem, 2vw, 1rem);
        font-weight: 500;
        color: #f3f3f3;
    }

    .title {
        font-size: clamp(1.2rem, 2vw, 1.7rem);
        font-weight: 700;
    }

    .readMore {
        font-weight: 600;
    }
}

@media (max-width: 1500px) {
    .layered-element {
        aspect-ratio: 16 / 9;
    }
}


@media (max-width: 991px) {
  .layered-element-height-override {
    max-height: none !important;
  }
}

@media (min-width: 992px) and (max-width: 1550px) {
  .layered-element-height-override {
    background-size: contain;
    max-height: 480px !important;
  }
}

#default-news-card-container.layered-element {
    background-size: 100% auto !important;
}