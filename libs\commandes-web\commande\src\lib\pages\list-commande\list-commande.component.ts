import { Router } from '@angular/router';
import { Component, OnInit, TemplateRef } from '@angular/core';
import {
    NgbDateStruct,
    NgbDate,
    NgbDateParserFormatter,
    NgbModal,
} from '@ng-bootstrap/ng-bootstrap';
import { GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import * as moment from 'moment';
import { CompositeFilterDescriptor, SortDescriptor } from '@progress/kendo-data-query';
import { AlertService } from '@wph/shared';
import { ListCommandesCriteria } from '../../models/listCommandesCriteria';
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import { CommandeService } from '../../services/commande.service';
import { CommandeNormaleCriteria, Pagination } from '@wph/data-access';
import { FormBuilder, FormGroup } from '@angular/forms';
import { getDynamicPageSize, UserInputService } from '@wph/web/shared';
import { StatutBlEnum } from '../../models/statut-bl.model';

@Component({
    selector: 'wph-list-commande',
    templateUrl: './list-commande.component.html',
    styleUrls: ['./list-commande.component.scss'],
})
export class ListCommandeComponent implements OnInit {
    model: NgbDateStruct;
    hoveredDate: NgbDate | null = null;
    fromDate: NgbDate;
    toDate: NgbDate | null = null;
    // filter
    isCollapsed = true;

    public categories: any[] = [
        { id: 'BROUILLON', name: 'En cours' },
        { id: 'VALIDE', name: 'Validée' },
        { id: 'ANNULE', name: 'Annulée' },
    ];

    public gridView: GridDataResult;
    public mySelection: string[] = [];
    searchParams: CommandeNormaleCriteria;
    navigation: Pagination = {
        skip: 0,
        pageSize: 15,
    };
    pageSizes: number[] = [5, 10, 15, 20];

    gridSort: SortDescriptor[];
    filter: CompositeFilterDescriptor;
    originalItems = [];

    cmdStatuts = ['A', 'B', 'V', 'TR'];
    blStatuts = ['CS', 'EX', 'V', 'PR'];

    filterForm: FormGroup;

    get controls() {
        return this.filterForm?.controls;
    }

    startsWith: RegExp = new RegExp('^[0-9]*$');

    listStatut = [
        { label: 'Annulée', value: 'A' },
        { label: 'Brouillon', value: 'B' },
        { label: "Classé Sans Suite", value: "CS" },
        { label: StatutBlEnum.ENREGISTREE, value: "V" },
        { label: StatutBlEnum.EXPEDITION, value: "EX" },
        { label: StatutBlEnum.PREPARATION_BL, value: "PR" },
        { label: StatutBlEnum.TRANSMIS, value: "TR" },
    ];

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private srv: CommandeService,
        private modalService: NgbModal,
        private srvAlert: AlertService,
        public formatter: NgbDateParserFormatter,
        private userInputService: UserInputService
    ) { }

    ngOnInit() {
        this.setPageSize();

        const today = new Date().setHours(0, 0, 0);

        this.filterForm = this.fb.group({
            statuts: [null],
            dateCommandeDu: [moment(today)],
            dateCommandeAu: [null]
        });

        this.initSearch();
    }

    setPageSize(currentHeight?: number): void {
        const dynamicSize = getDynamicPageSize(currentHeight);

        if (dynamicSize !== this.navigation.pageSize) {
            this.navigation.pageSize = dynamicSize;

            !this.pageSizes?.includes(dynamicSize) && this.pageSizes.push(dynamicSize);
            this.pageSizes = this.pageSizes.sort((a, b) => a - b);

            currentHeight && this.initSearch();
        }
    }

    /* -------------------------------------------------------------------------- */
    /*                                 init search                                */

    /* -------------------------------------------------------------------------- */
    initSearch() {
        const {statuts, ...formValues} = this.filterForm.getRawValue();
        const statutsBl = (statuts as string[])?.filter(statut => this.blStatuts?.includes(statut))?.map(statut => {
            if (statut === 'V') return 'EN';
            return statut;
        });
        const statutsCmd = (statuts as string[])?.filter(statut => this.cmdStatuts?.includes(statut));


        this.searchParams = new CommandeNormaleCriteria({ 
            statuts: statutsCmd?.length ? statutsCmd : null,
            statutTraiteBl: statutsBl?.length ? statutsBl : null
        });

        formValues['dateCommandeDu'] && (this.searchParams.dateDebut = moment(formValues['dateCommandeDu']));
        formValues['dateCommandeAu'] && (this.searchParams.dateFin = moment(formValues['dateCommandeAu']));

        this.gridView = {
            data: [],
            total: 0
        }

        this.srv
            .getListCommandes(
                this.searchParams,
                { ...this.navigation, skip: this.getPageNumber(this.navigation.skip, this.navigation.pageSize), pageSize: this.navigation.pageSize }
            )
            .subscribe((res: ListCommandesCriteria) => {
                this.gridView = {
                    data: res.content,
                    total: res.totalElements,
                };
                this.originalItems = res.content;
            });
    }

    /* -------------------------------------------------------------------------- */
    /*                          calc page for pagination                          */

    /* -------------------------------------------------------------------------- */
    getPageNumber(skip, pageSize) {
        return Math.floor(skip / pageSize);
    }

    /* -------------------------------------------------------------------------- */
    /*                              pagination event                              */

    /* -------------------------------------------------------------------------- */
    pageChange(event: PageChangeEvent): void {
        if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.navigation.pageSize = event.take;

            this.initSearch();
        }
    }

    gridSortChange(sort: SortDescriptor[]): void {
        this.gridSort = sort;

        if (this.gridSort && this.gridSort.length > 0 && this.gridSort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.initSearch();
    }

    /* -------------------------------------------------------------------------- */
    /*                                  navigate                                  */

    /* -------------------------------------------------------------------------- */
    goBonCommande(id) {
        this.router.navigate([`/commande-web/bon-commande/${id}`]);
    }

    goEditCommande(id: string) {
        this.router.navigate([`/commande-web/edit-commande/${id}`]);
    }

    goNewCommande() {
        this.router.navigate([`/commande-web/add-commande/new`]);
    }

    blCommande(id: any) {
        this.router.navigate([`/commande-web/bl-commande/${id}`]);
    }

    /* -------------------------------------------------------------------------- */
    /*                              validate commande                             */

    /* -------------------------------------------------------------------------- */
    goValiderCommande(id: string) {
        this.userInputService
            .confirm(null, 'Êtes-vous sûr de vouloir valider cette commande ?')
            .then(
                (_result) => {
                    this.srv.validerCommande(id).subscribe((res) => {
                        this.initSearch();
                        this.srvAlert.info('La commande a été validée avec succès.');
                    });
                }, () => null
            );
    }

    /* -------------------------------------------------------------------------- */
    /*                               delete commande                              */

    /* -------------------------------------------------------------------------- */
    goDeleteCommande(id: string) {
        this.userInputService
            .confirm(null, 'Êtes-vous sûr de vouloir annuler cette commande ?')
            .then(
                () => {
                    this.srv.deleteCommande(id).subscribe((_res) => {
                        this.initSearch();
                        this.srvAlert.info('La commande a été annulée avec succès.');
                    });
                }, () => null
            );
    }

    reload() {
        this.initSearch();
    }

    vider() {
        const today = new Date().setHours(0, 0, 0);

        this.filterForm.reset({ dateCommandeDu: moment(today) });
        this.navigation.skip = 0;
        this.initSearch();
    }

    openFilterModal(content: TemplateRef<any>) {
        this.modalService.open(content, { size: 'md', centered: true });
    }
}
