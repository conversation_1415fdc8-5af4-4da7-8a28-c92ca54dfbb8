import { AfterViewInit, Component, ElementRef, Input, ViewChild } from "@angular/core";
import { DomSanitizer, SafeUrl } from "@angular/platform-browser";
import { UploadFileServiceService } from "@wph/shared";
import { PosteService } from "@wph/web/gestion-annonces";

type ActuMode = 'DEFAULT' | 'ALT';

@Component({
    selector: 'wph-news-card',
    templateUrl: './news-card.component.html',
    styleUrls: ['./news-card.component.scss']
})
export class NewsCardComponent implements AfterViewInit {
    @Input() id?: string;
    @Input() titre: string;
    @Input() subtitle?: string;
    @Input() content: string;
    @Input() imgSrc?: string;
    @Input() link?: string | SafeUrl;
    @Input() mode: ActuMode = 'DEFAULT';
    @Input() videoUrl?: string;
    @Input() imgActionLabel?: string;

    youtubePlayerVars: YT.PlayerVars;


    @ViewChild('actu') actuRef: ElementRef;

    constructor(
        private sanitizer: DomSanitizer,
        private posteService: PosteService,
        private uploadService: UploadFileServiceService
    ) { 
        this.youtubePlayerVars = this.posteService.youtubePlayerVars;
    }

    ngAfterViewInit(): void {
        if (this.imgSrc) {
            const imageUrl = this.uploadService.fetchUploadedDocument(this.imgSrc);
            
            (this.actuRef.nativeElement as HTMLDivElement)
                .style.setProperty('background-image', `url('${imageUrl}')`);
        } else if (this.videoUrl) {
            (this.actuRef.nativeElement as HTMLDivElement).style.setProperty('background-image', `none`);
            (this.actuRef.nativeElement as HTMLDivElement).style.setProperty('background', `#000`);
        }

    }

    redirectToUrl(url: SafeUrl) {
        const sanitizedUrl: string = this.sanitizer.sanitize(4, url);
    
        url && (window.open(sanitizedUrl, '_blank'));
    }
}