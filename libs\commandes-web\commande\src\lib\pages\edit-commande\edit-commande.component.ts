import { ActivatedRoute, Router } from '@angular/router';
import { Component, AfterViewInit, OnInit, OnDestroy, ViewChild, TemplateRef, Inject } from '@angular/core';
import { Observable, of, OperatorFunction, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, switchMap, tap, catchError, shareReplay, startWith, takeUntil } from 'rxjs/operators';
import { CommandeDto } from '../../models/CommandeDto';
import { AnimateService } from '@wph/commandes-web/shared';
import { LigneCommandeDto, SanitizedLigneCommande } from '../../models/LigneCommandeDto';
import { CommandeService } from '../../services/commande.service';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { ProduitDTO } from '../../models/produitDTO';
import { CheckDispoItem, CheckDispoResponse } from '../../models/check-dispo.model';
import { DeferredActionButtonsService, UserInputService } from '@wph/web/shared';
import { AlertService, PlateformeService } from '@wph/shared';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { OffresService } from '../../../../../../data-access/src/lib/services/offres.service';
import { Offre } from '@wph/data-access';

@Component({
    selector: 'wph-edit-commande',
    templateUrl: './edit-commande.component.html',
    styleUrls: ['./edit-commande.component.scss']
})
export class EditCommandeComponent implements OnInit, AfterViewInit, OnDestroy {

    currentJustify = 'start';
    model: any;


    commande: CommandeDto;
    submitted: boolean;
    dirty: boolean;

    gridData: GridDataResult;

    pageType = '';
    searchSelectedItem: any;
    addQteCmd = 1;
    CmdNetTtc = 0;
    qteTotale = 0;
    listOfNews: any[] = [];
    gridProduitMap: Record<string, ProduitDTO> = {};
    offreIdToCodeProduitMap: Record<string, string[]> = {};
    disponibiliteProduitDansOffre: CheckDispoResponse[] = [];
    uniqueOffres: Partial<Offre>[] = [];
    disponibiliteCode: string = null;
    disponibiliteLibelle: string = null;

    editCmdInputElement: HTMLInputElement;
    unsubscribe$: Subject<boolean> = new Subject<boolean>();

    private readonly CACHE_TTL = 5 * 60 * 1000;
    private searchCache = new Map<string, { data: any[], timestamp: number }>();
    @ViewChild('disponibiliteProduitModal', { static: true }) produitDisponibiliteDansOffreModal: TemplateRef<any>;

    constructor(
        @Inject('ENVIROMENT') private env: any,
        private router: Router,
        private srv: CommandeService,
        private route: ActivatedRoute,
        private modalService: NgbModal,
        private animated: AnimateService,
        private offresService: OffresService,
        private alertService: AlertService,
        private userInputService: UserInputService,
        private plateformeService: PlateformeService,
        private deferredActionBtnService: DeferredActionButtonsService,
    ) { }

    ngAfterViewInit(): void {
        this.editCmdInputElement = document.getElementById('iptEditCmd') as HTMLInputElement;
        this.editCmdInputElement && this.editCmdInputElement.focus();
    }

    ngOnInit() {
        this.pageType = this.route.snapshot.params['id'];

        if (this.pageType === 'new') {
            this.initNewCommande(), this.pushMobileMenuOptions();
        } else if (this.pageType === 'panier') {
            this.getPanier();
        } else {
            this.getCommandeByCode();
        }

        this.dirty = false;
    }


    hasUnsavedData(): boolean {
        return !this.submitted && this.dirty;
    }


    formatter = (result: ProduitDTO) => result?.libelle;

    search: OperatorFunction<string, readonly string[]> = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(400),
            distinctUntilChanged(),
            switchMap((searchText) => {
                if (searchText?.length > 2) {
                    const trimmedText = searchText.trim().toLowerCase();

                    if (this.getCachedResult(trimmedText)) {
                        return of(this.getCachedResult(trimmedText));
                    }

                    return this.srv.getCatalogueProduits({
                        codeProduitSite: null,
                        genericCriteria: searchText
                    }, { skip: 0, pageSize: 10 }).pipe(
                        map(res => res.content),
                        tap(results => {
                            this.setCachedResult(trimmedText, results);
                        }),
                        catchError(_error => {
                            return of([]);
                        }),
                        shareReplay(1)
                    );
                } else {
                    return of([]);
                }
            }),
            startWith([]),
            takeUntil(this.unsubscribe$)
        );

    private getCachedResult(key: string): any[] | null {
        const cached = this.searchCache.get(key);
        if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
            return cached.data;
        }
        if (cached) {
            this.searchCache.delete(key);
        }
        return null;
    }

    private setCachedResult(key: string, data: any[]): void {
        this.searchCache.set(key, { data, timestamp: Date.now() });
    }

    selectedProduct(item: { item: ProduitDTO, query: string }) {
        if (!item || !item.item) return;

        this.searchSelectedItem = item.item; // new item selected
        this.searchSelectedItem.qteCmd = this.addQteCmd;
        document.getElementById('searchAdd').focus();
        this.dirty = true;
    }

    checkIfExsits(item: string) {
        if (this.commande?.lignes.length > 0) { // check length of lignes 0
            return this.commande?.lignes.find(x => x.codeProduitCatalogue === item);
        } else {
            return false;
        }
    }

    goBack(modified = false) {
        if (!modified) {
            if (this.submitted) {
                this.router.navigate(['/commande-web/list-commandes'], { state: { modified: true } });
                return;
            }
        }
        this.router.navigate(['/commande-web/list-commandes'], { state: { modified } });
    }

    getPanier() {
        this.srv.getPanier().subscribe(res => {
            this.commande = res;
            if (!this.commande) {
                this.initNewCommande();
                this.commande.isPanier = true;
            } else {
                this.setGridData();
            }

            this.updateLocalSynthese(this.commande.lignes);
            this.pushMobileMenuOptions();
        });
    }


    initNewCommande() {
        this.commande = new CommandeDto();
        this.commande.isPanier = false;
        this.commande.statut = 'BROUILLON';
    }


    updateCommandesLocal() {
        if (!this.model) {
            return this.animated.headShake('productContainerIpt');
        }

        if (!this.listOfNews.includes(this.searchSelectedItem.codeProduit)) {
            this.listOfNews.push(this.searchSelectedItem.codeProduit);
        }

        const ligneExistante = this.checkIfExsits(this.searchSelectedItem.codeProduit);

        if (!ligneExistante) {

            const newLine = new LigneCommandeDto();
            newLine.codeProduitCatalogue = this.searchSelectedItem.codeProduit;
            newLine.libelleProduit = this.searchSelectedItem.libelle;
            newLine.ppv = this.searchSelectedItem.ppv;
            newLine.prixVenteTtc = this.searchSelectedItem.pph;
            newLine.tauxTva = this.searchSelectedItem.tva;
            newLine.qteCmd = this.addQteCmd;
            newLine.totalNetTtc = newLine.qteCmd * newLine.prixVenteTtc;


            this.commande.lignes.push(newLine);
        } else {
            ligneExistante.qteCmd = Number(ligneExistante.qteCmd) + Number(this.addQteCmd);
        }


        this.setGridData();

        this.dirty = true;

        this.addQteCmd = 1;
        this.searchSelectedItem = null;
        this.model = null;
        if (this.commande && this.commande.lignes) {
            this.updateLocalSynthese(this.commande.lignes);
        }

        this.editCmdInputElement && this.editCmdInputElement.focus();

        (this.pageType === 'panier') && this.saveCommandes(false);
    }

    updateLocalSynthese(lignes: any[]) {
        if (!lignes || !lignes.length) {
            this.CmdNetTtc = 0;
            this.qteTotale = 0;
            return;
        }

        setTimeout(() => {
            this.CmdNetTtc = 0;
            this.qteTotale = 0;
            lignes.forEach(e => {
                this.CmdNetTtc = parseFloat((this.CmdNetTtc + parseFloat(e.totalNetTtc)).toFixed(2));
                this.qteTotale += Number(e.qteCmd);
            });
        }, 200);
    }

    getCommandeByCode() {
        this.srv.getBonCommande(this.pageType).subscribe(res => {
            this.commande = res;
            if (this.commande && this.commande.lignes) {
                this.setGridData();
                this.updateLocalSynthese(this.commande.lignes);
            }

            this.pushMobileMenuOptions();
        });
    }

    setGridData() {
        if (!this.commande || !this.commande.lignes) {
            this.gridData = {
                data: [],
                total: 0
            };
            return;
        }

        this.gridData = {
            data: [...this.commande.lignes],
            total: this.commande?.lignes?.length
        };
    }

    searchAddQteCmd(e: any) {
        if (e.id === 'searchAdd') {
            this.addQteCmd = Number(e.number);
        }
    }

    getCommandeNumber(e: { id: string, number: number }) {
        let somethingChanged = false;

        const targetIndex = this.commande.lignes.findIndex(x => x.codeProduitCatalogue === e.id);

        if (targetIndex !== -1) {
            if (!somethingChanged) {
                somethingChanged = this.commande.lignes[targetIndex].qteCmd !== e.number;
            }

            this.commande.lignes[targetIndex].qteCmd = e.number;
            this.commande.lignes[targetIndex].totalNetTtc = this.commande.lignes[targetIndex].qteCmd * this.commande.lignes[targetIndex].prixVenteTtc;

            this.commande.lignes = [...this.commande.lignes];
        }

        if (somethingChanged) {
            this.dirty = somethingChanged;

            (this.pageType === 'panier') && this.saveCommandes(false);
        }

        this.setGridData();
        this.updateLocalSynthese(this.commande.lignes);
    }

    saveCommandes(redirect = true) {
        const requestPayload: CommandeDto = {
            ...this.commande,
            origineCommande: 'CW',
            lignes: this.commande.lignes as SanitizedLigneCommande[]
        };

        this.srv.saveCommandes(requestPayload)
            .subscribe(res => {
                if (res.isPanier) {
                    this.srv.panierChanged(res);
                }

                this.commande = res;

                if (redirect) {
                    this.goBack(true);
                } else {
                    this.setGridData();
                }

                this.submitted = true;
            });


    }

    goValiderCommande() {
        if (!this.gridData?.data?.length) {
            this.alertService.error('Veuillez ajouter au moins un produit à la commande.', 'MODAL');
            return;
        }

        this.userInputService.confirm(null, `Êtes-vous sûr de vouloir valider ${this.pageType === 'panier' ? 'le panier' : 'cette commande'} ?`).then(
            () => {
                this.srv.saveCommandes({ ...this.commande, origineCommande: 'CW' })
                    .subscribe((res) => {
                        this.submitted = true;

                        if (res) {
                            this.pageType === 'new' && (this.pageType = res?.idhash);
                            this.commande = { ...this.commande, ...res, origineCommande: 'CW' };

                            this.validerCommande(res?.idhash);
                        }
                    });

            }, () => null
        );
    }

    validerCommande(idHash: string) {
        this.srv.validerCommande(idHash)
            .subscribe(() => {
                (this.pageType === 'panier') && this.srv.panierChanged(null);
                this.goBack(true);
            });
    }

    saveCommande() {
        if (!this.gridData?.data?.length) {
            this.alertService.error('Veuillez ajouter au moins un produit à la commande.', 'MODAL');
            return;
        }

        this.userInputService.confirm(null, 'Êtes-vous sûr de vouloir enregistrer cette commande ?').then(
            () => {
                this.srv.saveCommandes({ ...this.commande, origineCommande: 'CW' })
                    .subscribe(() => {
                        this.submitted = true;
                        this.goBack(true);
                    });
            }, () => null
        );
    }

    deleteCommande(codeProduitCatalogue: string) {
        this.userInputService.confirm(null, `Êtes-vous sûr de vouloir supprimer ce produit ${this.pageType === 'panier' ? 'du panier' : 'de la commande'} ?`).then(
            () => {
                const indexToDelete = this.commande.lignes.findIndex(x => x.codeProduitCatalogue === codeProduitCatalogue);

                if (indexToDelete !== -1) {
                    this.commande.lignes.splice(indexToDelete, 1);
                }

                this.dirty = true;

                if (this.pageType === 'panier') {
                    this.saveCommandes(false);
                } else {
                    this.setGridData();
                }

                this.updateLocalSynthese(this.commande.lignes);
            }, () => null
        );
    }

    deleteAllCommande() {
        if (!this.commande?.lignes?.length) {
            this.alertService.error('Le panier est déjà vide.', 'MODAL');
            return;
        }

        this.userInputService.confirm(null, 'Êtes-vous sûr de vouloir vider le panier ?').then(
            () => {
                this.commande.lignes = [];
                this.saveCommandes();
            }, () => null
        );
    }

    subs(e: unknown) {
        e && this.updateCommandesLocal();
    }

    checkDispoAtIndex(selectedItem: LigneCommandeDto, index: number): void {
        const dispoDemande = new CheckDispoItem({
            codeProduit: selectedItem?.codeProduitCatalogue,
            quantiteMin: selectedItem?.qteCmd
        });

        this.srv.checkDispoMultiProduit([dispoDemande]).subscribe(res => {
            if (res[0]?.codeRetour) {
                this.commande.lignes[index] = {
                    ...this.commande.lignes[index],
                    disponibiliteCode: 'D',
                    disponibiliteLibelle: 'Appelez votre grossiste'
                };
            } else {
                this.commande.lignes[index] = {
                    ...this.commande.lignes[index],
                    disponibiliteCode: res[0]?.listeReponses[0]?.dispo,
                    disponibiliteLibelle: res[0]?.listeReponses[0]?.dispoLibelle,
                };
            }

            this.setGridData();
        });
    }

    checkDispoMultiLigne(): void {
        if (this.gridData?.data?.length) {
            const listeDemandes = this.commande.lignes.map(ligne => {
                return new CheckDispoItem({ codeProduit: ligne.codeProduitCatalogue, quantiteMin: ligne.qteCmd });
            });

            this.srv.checkDispoMultiProduit(listeDemandes).subscribe((res: CheckDispoResponse[]) => {
                if (res[0]?.codeRetour) {
                    this.commande.lignes = this.commande?.lignes?.map(ligne => {
                        return {
                            ...ligne,
                            disponibiliteCode: 'D',
                            disponibiliteLibelle: 'Appelez votre grossiste'
                        };
                    });
                } else {
                    res[0]?.listeReponses?.map((dispoResponse, index) => {
                        this.commande.lignes[index].disponibiliteCode = dispoResponse.dispo;
                        this.commande.lignes[index].disponibiliteLibelle = dispoResponse.dispoLibelle;
                    });
                }

                this.setGridData();
            });
        }
    }

    private pushMobileMenuOptions(): void {
        this.deferredActionBtnService.pushPageOptions([
            {
                label: 'Enregistrer',
                iconClass: 'mdi mdi-content-save',
                shouldShow: this.commande?.statut === 'BROUILLON' && this.pageType !== 'panier',
                action: () => this.saveCommande(),
            },
            {
                label: 'Valider Commande',
                iconClass: 'mdi mdi-plus',
                shouldShow: this.commande?.statut === 'BROUILLON',
                action: () => this.goValiderCommande(),
            },
            {
                label: 'Vider Panier',
                iconClass: 'mdi mdi-cart-off',
                shouldShow: this.commande?.isPanier,
                action: () => this.deleteAllCommande(),
            },
            {
                label: 'Check Disponibilité Produits Commande',
                iconClass: 'mdi mdi-check',
                shouldShow: true,
                action: () => this.checkDispoMultiLigne(),
            },
            {
                label: 'Quitter',
                iconClass: 'mdi mdi-close',
                shouldShow: true,
                action: () => this.goBack(this.dirty),
            }
        ]);
    }

    public fetchDisponibiliteProduitCommandeDansOffresDisponibles() {
        const listeCodeProduits: string[] = this.commande?.lignes?.map(ligne => ligne.codeProduitCatalogue) || [];

        this.gridProduitMap = this.commande?.lignes?.reduce((acc, ligne) => {
            acc[ligne.codeProduitCatalogue] = ligne;
            return acc;
        }, {});

        if (listeCodeProduits?.length) {
            const codeSiteCible: number = this.plateformeService.getCurrentGrossiste()?.noeud?.codeSite;

            this.offresService.checkProductInOffre(codeSiteCible, listeCodeProduits).subscribe(res => {
                if (!res?.length) {
                    this.alertService.error(`Aucune offre disponible pour les produits de ${this.pageType === 'panier' ? 'votre panier' : 'cette commande'}.`, 'MODAL');
                } else {
                    const uniqueOffreIds = new Set();
                    const codeProduitToOffreMap: Record<string, number[]> = {};

                    const flattenedOffers = res?.map(item => {
                        codeProduitToOffreMap[item?.codeProduitFournisseur] = item?.listOffre?.map(offre => offre.id) || [];
                        return item?.listOffre;
                    }).flat();

                    flattenedOffers.forEach(offre => uniqueOffreIds.add(offre.id));

                    this.uniqueOffres = Array.from(uniqueOffreIds).map(id => flattenedOffers.find(offre => offre.id === id));
                    this.offreIdToCodeProduitMap = Object.keys(codeProduitToOffreMap).reduce((acc, codeProduit) => {
                        const offreIds = codeProduitToOffreMap[codeProduit];
                        offreIds.forEach(offreId => {
                            if (!acc[offreId]) {
                                acc[offreId] = [];
                            }
                            acc[offreId].push(codeProduit);
                        });
                        return acc;
                    }, {});

                    this.openDisponibiliteProduitModal();
                }
            });
        } else {
            this.alertService.error(`Veuillez ajouter au moins un produit dans votre ${this.pageType === 'panier' ? ' panier' : 'commande'}.`, 'MODAL');
        }
    }

    navigateToOffre(offreId: number) {
        window.open(`${this.env?.base_url}/win-offre/offres/edit/${offreId}?readOnly=true`, '_blank');
    }

    openDisponibiliteProduitModal() {
        this.modalService.open(
            this.produitDisponibiliteDansOffreModal,
            { centered: true, modalDialogClass: 'disponibilite-produit-modal', animation: false }
        ).result.then(() => null, () => null);
    }

    ngOnDestroy(): void {
        this.searchCache.clear();
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
        this.deferredActionBtnService.pushPageOptions([]);
    }
}
