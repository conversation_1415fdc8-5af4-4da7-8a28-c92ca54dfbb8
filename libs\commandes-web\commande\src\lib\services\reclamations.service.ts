// libs\commandes-web\commande\src\lib\services\reclamations.service.ts

import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Reclamation } from '../models/reclamation';
import { ReclamationCriteria } from '../models/reclamation';
import { Pagination } from '../models/reclamation';
import { ReclamationList } from '../models/reclamation';

@Injectable({
    providedIn: 'root'
})
export class ReclamationsService {
    private apiUrl: string | null = null;

    constructor(
        @Inject('ENVIROMENT') private env: any,
        private httpClient: HttpClient,
    ) {
        this.apiUrl = `${env.base_url}/api/reclamation`;
    }

    createOrEdit(reclamation: Reclamation): Observable<Reclamation> {
        return this.httpClient.post<Reclamation>(`${this.apiUrl}/createOrEdit`, reclamation);
    }

    // cancelOrCheck(id: number): Observable<Reclamation> {
    //     return this.httpClient.put<Reclamation>(`${this.apiUrl}/cancelOrCheck/${id}`, {});
    // }

    cancelReclamation(id: number): Observable<Reclamation> {
        return this.httpClient.put<Reclamation>(`${this.apiUrl}/cancel/${id}`, {});
    }

    checkReclamation(id: number): Observable<Reclamation> {
        return this.httpClient.put<Reclamation>(`${this.apiUrl}/check/${id}`, {});
    }

    getAllReclamation(criteria: ReclamationCriteria, pagination: Pagination): Observable<ReclamationList> {
        const sort = (pagination?.sortField && pagination?.sortMethod) ?
            pagination?.sortField + ',' + pagination?.sortMethod :
            null;
    
        const pageParams = { page: pagination.skip, size: pagination.pageSize, sort };

    
        return this.httpClient.post<ReclamationList>(`${this.apiUrl}/search`, criteria, { observe: 'body', params: pageParams });
    }
}