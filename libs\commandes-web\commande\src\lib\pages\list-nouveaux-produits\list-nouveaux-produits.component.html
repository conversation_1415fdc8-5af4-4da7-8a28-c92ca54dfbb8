<div id="list-nouveaux-produits" class="p-0">
    <!-- Start Of Header -->
    <div class="rowline mb-0">
        <div class="page-title-box row align-items-center">
            <h4 class="page-title fw-4 ps-2 col d-md-block d-none">Nouveaux Produits</h4>

            <div class="col px-0 py-2 mt-md-0 mt-1 py-md-0">
                <div class="row justify-content-end align-items-center">
                    <button (click)="openFilterModal(filterProduitModal, 'md')"
                        class="btn btn-sm btn-info rounded-pharma mx-1">
                        <i class="mdi mdi-filter-variant"></i> Filtrer
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- END HEADER -->

    <div class="card">
        <kendo-grid [data]="gridView" (pageChange)="pageChange($event)" [pageSize]="navigation.pageSize"
            [skip]="navigation.skip" [pageable]="{
                buttonCount: 5,
                info: true,
                type: 'numeric',
                pageSizes: pageSizes,
                previousNext: true,
                position: 'bottom'
              }" [sortable]="{mode: 'single'}" [sort]="gridSort" (sortChange)="gridSortChange($event)"
            [groupable]="false" [reorderable]="true" [resizable]="true" style="min-height: calc(100vh - 123px);">

            <kendo-grid-column media="(max-width: 768px)" title="Liste Nouveaux Produits">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <!---  Mobile Column Template  --->
                    <dl class="pt-1">
                        <dt class="my-2 limited-width"><span class="text-wrap m-0 p-0"><b>Libellé:</b> {{
                                dataItem?.libelleProduit }}</span></dt>

                        <dt class="my-2 limited-width">PPH: <span>{{ dataItem?.prixVenteTtc | number: "1.2-2":"fr-FR"
                                }}</span></dt>

                        <dt class="my-2 limited-width">PPV: <span>{{ dataItem?.ppv | number: "1.2-2":"fr-FR" }}</span>
                        </dt>

                        <dt class="my-2 limited-width">Fabriquant: <span>{{ dataItem?.fournisseur?.libelle }}</span>
                        </dt>

                        <dt class="my-2 limited-width">Date Création: <span>{{ dataItem?.dateCreation | momentTimezone:
                                'yyyy-MM-DD HH:mm' : 'Africa/Casablanca' }}</span>
                        </dt>

                        <dt class="action-btns">
                            <div class="d-flex row mt-4 mx-0 justify-content-start">
                                <button (click)="navige(dataItem?.id)" title="Consulter Produit"
                                    class="circle circle-alt btn btn-warning text-white">
                                    <i class="mdi mdi-eye"></i>
                                </button>
                            </div>
                        </dt>
                    </dl>
                </ng-template>

            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" class="text-wrap" field="libelleProduit" title="Libellé"
                [width]="220"></kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="prixVenteTtc" title="PPH" [width]="60"
                [resizable]="false">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    <div class="text-right">
                        {{ dataItem?.prixVenteTtc | number: "1.2-2":"fr-FR" }}
                    </div>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="ppv" title="PPV" [width]="60" [resizable]="false"
                filter="boolean">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    <div class="text-right">
                        {{ dataItem?.ppv | number: "1.2-2":"fr-FR" }}
                    </div>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="societe.raisonSociale" class="text-wrap"
                title="Fabriquant" [width]="110" [resizable]="false" filter="numeric">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.fournisseur?.libelle }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" field="dateCreation" class="text-wrap" title="Date Création"
                [width]="110" [resizable]="false" filter="date">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.dateCreation | momentTimezone: 'yyyy-MM-DD HH:mm' : 'Africa/Casablanca' }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column media="(min-width: 769px)" title="Action" [width]="60" [sortable]="false"
                [resizable]="false" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    <div class="d-flex w-100 justify-content-center align-items-center">
                        <button (click)="navige(dataItem?.id)" title="Consulter Produit"
                            class="circle btn btn-warning text-white">
                            <i class="mdi mdi-eye"></i>
                        </button>
                    </div>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
                pagerItemsPerPage="éléments par page"></kendo-grid-messages>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>
        </kendo-grid>
    </div>

</div>

<!-- Start filter modal-->
<ng-template #filterProduitModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Filtrer</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <form [formGroup]="filterProduitForm" (ngSubmit)="recherche()" wphFocusTrap>
        <div class="p-2">
            <div class="form-group">
                <div class="row p-1">
                    <div class="col-12 mb-3">
                        <label for="libelle" class="col-sm-6 form-label p-0">Libellé</label>
                        <input type="text" class="form-control form-control-md" id="libelle"
                            formControlName="libelleProduit">
                    </div>

                    <div class="col-12 mb-3">
                        <label for="search-fabriquant" class="col-sm-6 form-label p-0">Fabriquant</label>
                        <div id="client-picker-input" class="input-group picker-input">
                            <input type="text" class="form-control form-control-md" id="search-fabriquant"
                                formControlName="fournisseur" style="padding-left: 30px !important;"
                                [ngbTypeahead]="searchSociete" [resultTemplate]="clientSearchTemplate"
                                [inputFormatter]="societeFormatter" [resultFormatter]="societeFormatter">

                            <ng-template #clientSearchTemplate let-result="result">
                                <div>
                                    <span class="badge badge-info mr-2">{{result?.code}}</span>
                                    <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                                </div>
                                <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                            </ng-template>

                            <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
                        </div>
                    </div>

                    <div class="col-12">
                        <label for="dateDebut" class="col-sm-6 form-label p-0">Date Création Début</label>
                        <div class="input-group">
                            <input type="text" formControlName="dateCreation" class="form-control form-control-md"
                                id="dateDebut" ngbDatepicker #drange1="ngbDatepicker" (click)="drange1.toggle()">

                            <div class="input-group-append">
                                <button class="btn btn-md btn-light text-dark btn-outline-light calendar"
                                    (click)="drange1.toggle()" type="button" tabindex="-1">
                                    <i class="mdi mdi-calendar"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light"
                tabindex="-1">Fermer</button>
            <button type="button" class="btn btn-secondary text-white" (click)="vider()" tabindex="-1">Vider</button>
            <button type="button" type="submit" class="btn btn-primary ml-1 text-white"
                tabindex="-1">Rechercher</button>
        </div>
    </form>

</ng-template>