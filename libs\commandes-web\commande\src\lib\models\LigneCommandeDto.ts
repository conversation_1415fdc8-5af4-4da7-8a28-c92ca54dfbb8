export class LigneCommandeDto {
    id: number;
    produitId: number;
    codeProduitCatalogue: string;
    codeProduitSite: string;
    libelleProduit: string;
    ppv: number;
    tauxTva: number;
    prixVenteHt: number;
    prixVenteTtc: number;
    qteCmd: number;
    tauxRemise: number;
    montantRemise: number;
    tauxUg: number;
    qteUg: number;
    totalBrutHt: number;
    totalBrutTtc: number;
    totalNetHt: number;
    totalNetTtc: number;
    disponibiliteCode?: string;
    disponibiliteLibelle?: string;
}

export type SanitizedLigneCommande = Omit<LigneCommandeDto, 'disponibiliteCode' | 'disponibiliteLibelle'>;
 