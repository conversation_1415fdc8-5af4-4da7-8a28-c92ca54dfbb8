/// <reference lib="webworker" />

let timeLeftMap = {};
let intervals = {};

addEventListener('message', ({ data }) => {
  const { endTime, id } = data; // Expecting endTime as a timestamp

  timeLeftMap[id] = endTime;

  // Create a function to calculate the remaining time
  const calculateTimeLeft = () => {
    if (!timeLeftMap[id]) return;  // Early exit if ID has been deleted
    
    const now = new Date().getTime();
    const timeLeft = timeLeftMap[id] - now;

    if (timeLeft <= 0) {
      postMessage({ days: 0, hours: 0, minutes: 0, seconds: 0, done: true, id });
      delete timeLeftMap[id]; // Remove it since it's done
      clearInterval(intervals[id]); // Stop the interval for this ID
      delete intervals[id]; // Cleanup the interval reference
    } else {
      // Calculate remaining time components
      const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

      postMessage({ days, hours, minutes, seconds, done: false, id });
    }
  };

  // Clear existing interval if it exists
  if (intervals[id]) {
    clearInterval(intervals[id]);
  }

  // Start a new countdown for this ID
  intervals[id] = setInterval(() => {
    calculateTimeLeft();
  }, 1000);

  // Cleanup if the worker is terminated or finished
  addEventListener('message', (e) => {    
    if (e.data === 'stop') {
      Object.keys(intervals).forEach(id => clearInterval(intervals[id]));
      intervals = {};
      close();
    } else if (e?.data?.stopForId) {
      const { id } = e?.data;

      delete timeLeftMap[id];
      if (intervals[id]) {
        clearInterval(intervals[id]);
        delete intervals[id];
      }
    }
  });
});
