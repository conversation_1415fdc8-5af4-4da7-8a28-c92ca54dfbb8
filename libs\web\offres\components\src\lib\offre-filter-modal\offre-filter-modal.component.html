<form (click)="closeOpenSelect2Dropdowns()" [formGroup]="filterForm" class="filter-container" (ngSubmit)="applyFilter()" wphFocusTrap>
    <div class="row flex-wrap py-0 px-1 mx-0 my-2 k-gap-2 w-100">
        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="numero" class="col-12 px-0 col-form-label text-left">Numéro</label>
            <div class="col-12 px-0 input-group">
                <input type="text" class="form-control form-control-md" id="numero" formControlName="numero">
            </div>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="distributeur" class="col-12 px-0 col-form-label text-left">Distributeur</label>

            <div class="col-12 px-0 input-group picker-input">
                <input type="text" class="form-control form-control-md pl-4" id="distributeur"
                    formControlName="fournisseur" [ngbTypeahead]="searchFournisseur"
                    [resultFormatter]="fournisseurFormatter" [inputFormatter]="fournisseurFormatter" [editable]="false">

                <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
            </div>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="offreur" class="col-12 px-0 col-form-label text-left">Offreur</label>

            <div class="col-12 px-0 input-group picker-input">
                <input type="text" class="form-control form-control-md pl-4" id="offreur"
                    [ngbTypeahead]="searchFournisseur" [resultFormatter]="laboFormatter"
                    [inputFormatter]="laboFormatter" [editable]="false" formControlName="laboratoire" />

                <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
            </div>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="produit" class="col-12 px-0 col-form-label text-left">Produit</label>

            <div class="col-12 px-0 input-group picker-input">
                <input type="text" class="form-control form-control-md pl-4" id="produit"
                    [ngbTypeahead]="searchOffresByProduit" [resultFormatter]="offreFormatteur"
                    [inputFormatter]="offreFormatteur" [editable]="false" formControlName="libelleProduit" />

                <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
            </div>
        </div>

        <div class="col-12 col-lg-auto mt-1 mx-0 pt-1 pb-0 px-0">
            <label class="col-12 form-label px-0 pb-1 pt-0" for="selectstatut" style="margin-bottom: 0px;">Catégorie</label>

            <div (click)="$event.stopPropagation(); closeOpenSelect2Dropdowns(categorieSelect)" class="col-12 px-0 mx-0 input-group">
                <select2 id="selectstatut" formControlName="categoriePrdId" hideSelectedItems="false"
                    class="form-control-sm p-0 w-100" style="min-width: 205px; width: auto" multiple="false" [data]="categorieProduits" #categorieSelect></select2>
            </div>
        </div>

        <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
            <div class="col-12 col-lg-auto mt-1 mx-0 pt-1 pb-0 px-0">
                <label class="col-12 form-label p-0 pb-1 pt-0" for="selectstatut" style="margin-bottom: 0px;">Statut</label>

                <div (click)="$event.stopPropagation(); closeOpenSelect2Dropdowns(statutSelect)" class="col-12 px-0 mx-0 input-group">
                    <select2 id="selectstatut" formControlName="statut" (update)="statutSelect.isOpen = false" (removeOption)="statutSelect.isOpen = false" hideSelectedItems="true"
                        class="form-control-sm p-0 pr-1 w-100" style="min-width: 205px; width: auto" multiple="true" [data]="stautsLabelsValues" #statutSelect></select2>
                </div>
            </div>

            <div class="col-12 col-lg-auto mt-1 mx-0 pt-1 pb-0 px-0">
                <label class="col-12 form-label px-0 pb-1 pt-0" for="nonExpiree" style="margin-bottom: 0px;">Non
                    Expirées</label>

                <div (click)="$event.stopPropagation(); closeOpenSelect2Dropdowns(nonExpireeSelect)" class="col-12 mx-0 px-0 input-group">
                    <select2 id="nonExpiree" hideSelectedItems="false" formControlName="nonExpireesUniquement"
                        class="form-control-sm p-0 w-100" style="min-width: 205px; width: auto"
                        [data]="[{label: 'Oui', value: true}, {label: 'Non', value: false}]" multiple="false" #nonExpireeSelect></select2>
                </div>
            </div>
        </ng-container>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="dateDebut" class="col-12 px-0 col-form-label text-left">Date Debut</label>

            <app-date-picker formControlName="dateDebut"></app-date-picker>
        </div>

        <div class="col-12 col-lg-auto p-0 m-0">
            <label for="dateFin" class="col-12 px-0 col-form-label text-left">Date Fin</label>

            <app-date-picker formControlName="dateFin"></app-date-picker>
        </div>

        <div class="col-auto d-flex align-items-end p-0 m-0">
            <button (click)="clearFilters()" type="button" class="btn btn-sm btn-outline-primary b-radius">
                <i class="bi bi-arrow-clockwise mr-1"></i> <span>Vider</span>
            </button>

            <button type="submit" class="btn btn-sm btn-primary b-radius mx-1">
                <i class="mdi mdi-filter mr-1"></i> <span>Rechercher</span>
            </button>
        </div>
    </div>

</form>