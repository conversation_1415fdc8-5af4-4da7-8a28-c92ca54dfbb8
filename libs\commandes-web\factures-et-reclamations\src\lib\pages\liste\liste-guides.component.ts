import { Component, Inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { faqs } from './data/faq';
import { AuthService } from '@wph/core/auth';
import { Faq } from 'libs/federation-syndicats/src/lib/models/faq.model';

@Component({
  selector: 'wph-liste-guides',
  templateUrl: './liste-guides.component.html',
  styleUrls: ['./liste-guides.component.scss'],
})
export class ListeGuidesComponent implements OnInit {

  activatedQuestion: number;
  filteredQuestions: any[] = [];
  questions: any[] = [
    {
      question: "Comment passer une commande ?",
      authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'],
      id: 1,
    },
    {
      question: "Comment changer votre mot de passe ?",
      authorities: ['ROLE_AGENT_POINT_VENTE', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL'],
      id: 2,
    },

  ];

  cdnBaseUrl: string;
  private faqs: Faq[] = faqs;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private authService: AuthService,
    @Inject('ENVIROMENT') private environment: any
  ) {
    this.cdnBaseUrl = environment.cdn_base_url;
  }


  getCurrentQuestion(): Faq | undefined {
    return this.faqs.find(faq => faq.id == this.activatedQuestion);
  }


  questionidChanged(questionId: number) {
    this.activatedQuestion = questionId;
    this.router.navigate(['/commande-web/autres/guide'], { queryParams: { questionId } });
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      if (params['questionId']) {
        this.activatedQuestion = +params['questionId'];
      }
    });
    this.filteredQuestions = this.questions.filter(q =>
      !q.authorities || this.authService.hasAnyAuthority(q.authorities)
    );
  }

}
