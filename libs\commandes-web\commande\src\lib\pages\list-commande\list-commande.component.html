<div class="p-0">
  <!-- Start Of Header -->
  <div class="rowline mb-0">
    <div class="page-title-box row">
      <h4 class="page-title fw-4 ps-2 col-4 d-md-block d-none">Liste Commandes</h4>

      <div class="col-md-8 col-12 px-0 py-2 mt-md-0 mt-1 py-md-0">
        <div class="d-flex row justify-content-end align-items-center">
          <button *jhiHasAnyServiceOption="['PASSER_COMMANDE']" (click)="goNewCommande()"
            class="btn btn-sm btn-warning rounded-pharma mx-1 text-white d-flex row">
            <i class="mdi mdi-plus mr-sm-1"></i>
            <span>Nouvelle Commande</span>
          </button>

          <button (click)="openFilterModal(commandeFilter)" class="btn btn-sm btn-info rounded-pharma mx-1">
            <i class="mdi mdi-filter-variant"></i> Filtrer
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- END HEADER -->

  <div class="container-fluid m-0 p-0">
    <!-- ---------------------------- Grid Kendo UI ---------------------------- -->
    <div class="card">

      <kendo-grid [data]="gridView" [pageSize]="navigation.pageSize" [skip]="navigation.skip" [pageable]="{
        buttonCount: 5,
        info: true,
        type: 'numeric',
        pageSizes: pageSizes,
        previousNext: true,
        position: 'bottom'
      }" [groupable]="false" [reorderable]="true" scrollable="scrollable" [resizable]="true"
        style="min-height: calc(100vh - 123px);" (sortChange)="gridSortChange($event)" (pageChange)="pageChange($event)"
        [sort]="gridSort" [sortable]="{mode: 'single'}">

        <kendo-grid-column media="(max-width: 768px)" title="Liste Commandes">
          <ng-template kendoGridCellTemplate let-dataItem>
            <!---  Mobile Column Template  --->
            <dl>
              <dt class="my-2 limited-width">N° Commande: <span>{{ dataItem?.codeCommande }}</span></dt>

              <dt class="my-2 limited-width">Date Création: <span>{{ dataItem?.dateCommande | momentTimezone:
                  "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}</span></dt>

              <dt class="my-2 limited-width">Montant Total Brut: <span>{{ (dataItem?.bl?.montantTotalBrut ?? dataItem?.valeurCmdBruteTtc) | number:
                  "1.2-2":"fr-FR" }}</span>
              </dt>

              <dt class="my-2 limited-width">Date Enregistrement: <span>{{ dataItem?.dateValidation |
                  momentTimezone:
                  'yyyy-MM-DD HH:mm': "Africa/Casablanca" }}</span>
              </dt>

              <dt class="my-2 limited-width">Date En Prép: <span>{{ dataItem?.dateTraitementBl |
                  momentTimezone:
                  'yyyy-MM-DD HH:mm': "Africa/Casablanca" }}</span>
              </dt>

              <dt class="my-2 limited-width">Date En Expédition: <span>{{ dataItem?.bl?.dateExpedition |
                  momentTimezone:
                  'yyyy-MM-DD HH:mm': "Africa/Casablanca" }}</span>
              </dt>

              <dt class="my-2 limited-width">Origine Commande: <span>{{ dataItem?.origineCommande | origineCmd }}</span>
              </dt>

              <dt class="my-2 mx-0 d-flex row align-items-center limited-width">Statut: <span
                  *ngIf="(dataItem?.statut === 'BROUILLON' || dataItem?.statut === 'ANNULE'); else: statutBlTempMob"
                  class="ml-1 px-2 py-1 badge rounded-pill" [ngClass]="{
                'badge-grey': dataItem?.statut === 'BROUILLON',
                'badge-danger': dataItem?.statut === 'ANNULE'
                }">
                  {{ dataItem?.statut === "BROUILLON" ? "Brouillon" : "Annulée" }}
                </span>

                <ng-template #statutBlTempMob>
                  <div class="ml-1 badge-info px-2 py-1 badge rounded-pill"
                    *ngIf="((dataItem?.statut === 'VALIDE' || dataItem?.statut === 'ENREGISTREE') && !dataItem?.bl?.statutTraiteBl)">
                    {{ 'EN' | statutBl }}
                  </div>

                  <div class="ml-1 badge-success  px-2 py-1 badge rounded-pill"
                    *ngIf="(dataItem?.statut === 'TRANSMISE') && !dataItem?.bl?.statutTraiteBl">
                    {{ 'TR' | statutBl }}
                  </div>

                  <div class="ml-1 badge-warning p-1 badge rounded-pill"
                    *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'PR'">
                    {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                  </div>

                  <div class="ml-1 badge-primary px-2 py-1 badge rounded-pill"
                    *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'EX'">
                    {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                  </div>

                  <div class="ml-1 badge-success px-2 py-1 badge rounded-pill"
                    *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'TR'">
                    {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                  </div>

                  <div class="ml-1 badge-danger px-2 py-1 badge rounded-pill"
                    *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'CS'">
                    {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                  </div>
                </ng-template>
              </dt>

              <dt class="action-btns">
                <div class="d-flex row mx-0 justify-content-start">
                  <button (click)="goEditCommande(dataItem?.idhash)"
                    class="circle circle-alt btn btn-primary text-white" title="Modifier"
                    *ngIf="dataItem?.statut === 'BROUILLON'">
                    <i class="mdi mdi-pencil"></i>
                  </button>

                  <button (click)="goBonCommande(dataItem?.idhash)" title="Consulter Commande"
                    *ngIf="dataItem?.statut === 'VALIDE' || dataItem?.statut === 'ANNULE' || dataItem?.statut === 'ENREGISTREE' || dataItem?.statut === 'TRANSMISE'"
                    class="circle circle-alt btn btn-warning text-white">
                    <i class="mdi mdi-eye"></i>
                  </button>

                  <button (click)="blCommande(dataItem?.idhash)" title="Consulter BL"
                    class="circle circle-alt btn btn-info text-white mt-2" *ngIf="dataItem?.bl">
                    <i class="mdi mdi-clipboard-text"></i>
                  </button>

                  <button class="circle circle-alt btn btn-success text-white mt-2"
                    (click)="goValiderCommande(dataItem?.idhash)" title="Valider Commande Fournisseur"
                    *ngIf="dataItem?.statut === 'BROUILLON'">
                    <i class="mdi mdi-check"></i>
                  </button>

                  <button class="circle circle-alt btn btn-danger text-white mt-2"
                    (click)="goDeleteCommande(dataItem?.idhash)" title="Annuler"
                    *ngIf="dataItem?.statut === 'BROUILLON'">
                    <i class="mdi mdi-delete"></i>
                  </button>
                </div>
              </dt>
            </dl>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="numCommande" title="N°" [width]="110">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ dataItem?.codeCommande }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="dateCommande" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap">Date Création</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ dataItem?.dateCommande | momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [width]="180" [sortable]="false">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap">Origine Commande</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.origineCommande | origineCmd }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [sortable]="false" [resizable]="false" filter="numeric"
          class="text-right" [width]="190">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap text-right">Montant Total Brut</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ (dataItem?.bl?.montantTotalBrut ?? dataItem?.valeurCmdBruteTtc) | number: "1.2-2":"fr-FR" }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [width]="180">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap">Date Enregistrement</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.dateValidation | momentTimezone: 'yyyy-MM-DD HH:mm' : 'Africa/Casablanca' }}
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [width]="170">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap">Date En Prép</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.dateTraitementBl | momentTimezone: 'yyyy-MM-DD HH:mm' : 'Africa/Casablanca' }}
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [width]="170">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap">Date En Expédition</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.bl?.dateExpedition | momentTimezone: 'yyyy-MM-DD HH:mm' : 'Africa/Casablanca' }}
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="libelleLabo" class="text-center no-ellipsis"
          [sortable]="false" [width]="150" filter="numeric">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Statut</span>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div *ngIf="(dataItem?.statut === 'BROUILLON' || dataItem?.statut === 'ANNULE'); else: statutBlTemp"
              class="px-2 py-1 badge rounded-pill w-100" [ngClass]="{
                  'badge-grey': dataItem?.statut === 'BROUILLON',
                  'badge-danger': dataItem?.statut === 'ANNULE'
                  }">
              {{ dataItem?.statut === "BROUILLON" ? "Brouillon" : "Annulée" }}
            </div>

            <ng-template #statutBlTemp>
              <div class="badge-info px-2 py-1 badge rounded-pill w-100"
                *ngIf="((dataItem?.statut === 'VALIDE' || dataItem?.statut === 'ENREGISTREE') && !dataItem?.bl?.statutTraiteBl)">
                {{ 'EN' | statutBl }}
              </div>

              <div class="badge-success px-2 py-1 badge rounded-pill w-100"
                *ngIf="(dataItem?.statut === 'TRANSMISE') && !dataItem?.bl?.statutTraiteBl">
                {{ 'TR' | statutBl }}
              </div>

              <div class="badge-warning p-1 badge rounded-pill w-100"
                *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'PR'">
                {{ dataItem?.bl?.statutTraiteBl | statutBl }}
              </div>

              <div class="badge-primary px-2 py-1 badge rounded-pill w-100"
                *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'EX'">
                {{ dataItem?.bl?.statutTraiteBl | statutBl }}
              </div>

              <div class="badge-success px-2 py-1 badge rounded-pill w-100"
                *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'TR'">
                {{ dataItem?.bl?.statutTraiteBl | statutBl }}
              </div>

              <div class="badge-danger px-2 py-1 badge rounded-pill w-100"
                *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'CS'">
                {{ dataItem?.bl?.statutTraiteBl | statutBl }}
              </div>
            </ng-template>

          </ng-template>
          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
            let-filterService="filterService">
            <app-regex-filter [isPrimitive]="false" field="libelleLabo" [currentFilter]="filter"
              [filterService]="filterService" textField="name" valueField="id" [data]="categories">
            </app-regex-filter>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [sortable]="false" [width]="130" [resizable]="false"
          filter="numeric" class="no-ellipsis">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Action</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="gridbtn d-flex align-items-center justify-content-center">
              <div>
                <button (click)="goEditCommande(dataItem?.idhash)" class="circle btn btn-primary text-white"
                  title="Modifier" *ngIf="dataItem?.statut === 'BROUILLON'">
                  <i class="mdi mdi-pencil"></i>
                </button>

                <button (click)="goBonCommande(dataItem?.idhash)" title="Consulter Commande"
                  *ngIf="dataItem?.statut === 'VALIDE' || dataItem?.statut === 'ANNULE' || dataItem?.statut === 'ENREGISTREE' || dataItem?.statut === 'TRANSMISE'"
                  class="circle btn btn-warning text-white">
                  <i class="mdi mdi-eye"></i>
                </button>

                <button (click)="blCommande(dataItem?.idhash)" title="Consulter BL"
                  class="circle btn btn-info text-white mx-1" *ngIf="dataItem?.bl">
                  <i class="mdi mdi-clipboard-text"></i>
                </button>

                <button class="circle btn btn-success text-white mx-1" (click)="goValiderCommande(dataItem?.idhash)"
                  title="Valider Commande Fournisseur" *ngIf="dataItem?.statut === 'BROUILLON'">
                  <i class="mdi mdi-check"></i>
                </button>

                <button class="circle btn btn-danger text-white" (click)="goDeleteCommande(dataItem?.idhash)"
                  title="Annuler" *ngIf="dataItem?.statut === 'BROUILLON'">
                  <i class="mdi mdi-delete"></i>
                </button>
              </div>
            </div>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
          pagerItemsPerPage="éléments par page"></kendo-grid-messages>

        <ng-template kendoGridNoRecordsTemplate>
          <span>Aucun résultat trouvé.</span>
        </ng-template>
      </kendo-grid>
    </div>
  </div>
</div>

<!-- Filter Modal Start -->
<ng-template #commandeFilter let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Filtrer</h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <form [formGroup]="filterForm" (ngSubmit)="initSearch(); modal.dismiss()" wphFocusTrap>
    <div class="p-2">
      <div class="row mb-2">
        <label for="dateDebut" class="col-12 col-form-label text-left">Date Création Début</label>
        <div class="col-12 input-group">
          <input type="text" [readOnly]="true" name="dateCreationDebut" class="form-control form-control-md bg-white"
            id="dateDebut" ngbDatepicker #drange1="ngbDatepicker" (click)="drange1.toggle()"
            formControlName="dateCommandeDu">

          <div class="input-group-append">
            <button type="button" (click)="drange1.toggle()" tabindex="-1"
              class="btn btn-md btn-light text-dark btn-outline-light calendar">
              <i class="mdi mdi-calendar"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="row mb-2">
        <label for="dateFin" class="col-12 col-form-label text-left">Date Création Fin</label>
        <div class="col-12 input-group">
          <input type="text" [readOnly]="true" name="dateCreationFin" class="form-control form-control-md bg-white"
            id="dateFin" ngbDatepicker #drange2="ngbDatepicker" (click)="drange2.toggle()"
            formControlName="dateCommandeAu">

          <div class="input-group-append">
            <button type="button" (click)="drange2.toggle()" tabindex="-1"
              class="btn btn-md btn-light text-dark btn-outline-light calendar">
              <i class="mdi mdi-calendar"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="row py-1 px-0 mb-2">
        <div class="col-12 px-0">
          <label class="form-label col-12 p-0 ml-2" style="margin-bottom: 4px;">Statut</label>

          <div class="input-group picker-input position-relative">
            <select2 formControlName="statuts" (update)="selectInput.isOpen = false" (removeOption)="selectInput.isOpen = false" hideSelectedItems="true" style="width: 100%;"
              class="form-control-sm w-100" multiple="true" [data]="listStatut" #selectInput></select2>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light" tabindex="-1">Fermer</button>
      <button type="button" class="btn btn-secondary text-white" (click)="vider(); modal.dismiss()"
        tabindex="-1">Vider</button>
      <button type="button" type="submit" class="btn btn-primary ml-1 text-white" tabindex="-1">Rechercher</button>
    </div>
  </form>
</ng-template>