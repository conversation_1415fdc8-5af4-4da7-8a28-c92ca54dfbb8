// src/app/models/reclamation.ts

export interface Reclamation {
    id?: number;
    dateCreation?: string;
    dateModification?: string;
    dateSuppression?: string;
    dateCloture?: string;
    dateTraitement?: string;
    typeReclamationId?: string;
    typeReclamation?: TypeReclamation;
    statut?: ReclamationStatus;
    codeSite?: number;
    codeClient?: number;
    ville?: string;
    raisonSociale?: string;
    nomPharmacien?: string;
    message?: string;
    reponse?: string;
    clientId?: string;
    managerId?: string;
    userSuppressionId?: string;
}

export type ReclamationStatus = 'N' | 'R' | 'T' | 'S';

export interface TypeReclamation {
    code: string;
    label: string;
}

export class ReclamationCriteria {
    type?: TypeReclamation[];
    statut?: ReclamationStatus[];
    dateDu?: string;
    dateAu?: string;
    codeSite?: number;

    constructor(init?: Partial<ReclamationCriteria>) {
        Object.assign(this, init);
    }
}

export interface Pagination {
    skip: number;
    pageSize: number;
    sortField?: string;
    sortMethod?: 'asc' | 'desc';
}

export interface ReclamationList {
    content: Reclamation[];
    totalElements: number;
}