import { Component, Input } from "@angular/core";
import { Router } from "@angular/router";

@Component({
    selector: 'wph-simple-stat-card',
    templateUrl: './simple-stat-card.component.html',
    styleUrls: ['./simple-stat-card.component.scss']
})
export class SimpleStatCardComponent {
    @Input() label: string;
    @Input() value: number;
    
    // ? Stacked view variables
    @Input() stacked?: boolean = false;
    @Input() label2?: string;
    @Input() value2?: number;

    @Input() iconClass: string;
    @Input() link: string;

    @Input() isCurrency?: boolean;

    constructor(private router: Router) {}

    navigateToPage(): void {
        
        if (this.link) {
            this.router.navigateByUrl(this.link);
        }
    }
}