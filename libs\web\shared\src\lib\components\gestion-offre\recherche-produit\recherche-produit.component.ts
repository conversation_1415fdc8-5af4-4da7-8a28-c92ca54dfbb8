import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild } from "@angular/core";
import { FormGroup, FormControl, FormBuilder } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CellClickEvent, GridDataResult, RowSelectedFn, SelectionEvent } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { Offre, OffresService, Pagination, Produit, ProduitCriteria, StaticDataOffreService } from "@wph/data-access";
import { FsOffreService } from 'libs/federation-syndicats/src/lib/services/fs-offres.service';
import { AlertService, PlateformeService, ProduitListDto, SelectedProduitsAssociations, SocieteType } from "@wph/shared";
import { EntrepriseCriteriaDto } from "libs/federation-syndicats/src/lib/models/entreprise.model";
import { debounceTime, distinctUntilChanged, map, Observable, of, switchMap } from "rxjs";
@Component({
    selector: 'wph-recherche-produit',
    templateUrl: './recherche-produit.component.html',
    styleUrls: ['./recherche-produit.component.scss'],
})
export class RechercheProduitComponent implements OnInit {
    @Input() enableSousListeSearch: boolean = false;

    @Input() set offre(offre: Offre) {
        this._offre = offre;
    }

    get offre(): Offre | null {
        return this._offre;
    }

    produitForm: FormGroup;

    currentPlateforme$: Observable<any>;
    navigation: Pagination = { pageSize: 10, skip: 0 };
    produitsListeSelectionne: ProduitListDto | null = null;

    produitsView: GridDataResult;
    produitCriteria: ProduitCriteria | null = null;
    selectedProduits: Produit[] = [];

    selectedProduitIds: number[] = [];
    produitSort: SortDescriptor[] = []

    listeGammeTotal: any[] = [];
    listeGammeAutorises: any[] = [];
    listeforme: any[] = [];

    @Output() produitSelectionChange = new EventEmitter<Produit[]>();
    @ViewChild('modalProduit') modalProduit: TemplateRef<any>;

    _offre: Offre | null = null;
    constructor(
        private fb: FormBuilder,
        private modalService: NgbModal,
        private alertService: AlertService,
        private offresService: OffresService,
        private fsOffreService: FsOffreService,
        private plateformeService: PlateformeService,
        private staticDataOffreService: StaticDataOffreService
    ) {
        this.initFormProduit();
        this.currentPlateforme$ = this.plateformeService.currentPlateforme$;
    }

    get fournisseur() {
        return this.produitForm.get('fournisseur');
    }
    get libelleProduit() {
        return this.produitForm.get('libelleProduit');
    }
    get gamme() {
        return this.produitForm.get('gamme');
    }
    get forme() {
        return this.produitForm.get('forme');
    }

    ngOnInit(): void {
        this.listenToStaticDataObs();
    }

    public openModal(): void {
        this.produitsView = null;
        this.selectedProduits = [];
        this.selectedProduitIds = [];
        this.navigation.skip = 0;

        this.initFormProduit(), this.fournisseurchange();

        this.modalService.open(this.modalProduit, 
            { 
                ariaLabelledBy: 'modal-basic-title', 
                size: 'xl', 
                windowClass: 'custom-modal-width', 
                modalDialogClass: 'fs-radius-modal', 
                centered: true 
            }
        );
    }

    private initFormProduit() {
        if (!this.produitForm) {
            this.produitForm = this.fb.group({
                fournisseur: new FormControl(null, []),
                libelleProduit: new FormControl(null, []),
                gamme: new FormControl(null, []),
                forme: new FormControl(null, []),
                nomListeProduits: new FormControl(null, []),
                nomSousListe: new FormControl(null, [])
            });
        }

        if (this.offre?.laboratoire && this.offre.laboratoire.code !== 'DIVERS') {
            this.produitForm.get('fournisseur').setValue(this.offre.laboratoire);
        }
    }

    SearchProduit(sortTriggered = false): void {
        const formValues = this.produitForm.getRawValue();

        if (formValues?.nomListeProduits && !formValues?.nomSousListe) {
            this.navigation.skip = 0;
            this.offresService.getListeProduitsByNomListe(formValues?.nomListeProduits?.nom).subscribe(res => {
                const targetResult = res?.find(item => item?.id === formValues?.nomListeProduits?.id);
                this.produitsListeSelectionne = targetResult;
                if (res?.length) {
                    this.produitsView = {
                        total: targetResult?.detailProduitsList?.length,
                        data: targetResult?.detailProduitsList?.map(item => item?.produit)
                    };
                }
            });
        } else if (formValues?.nomListeProduits && formValues?.nomSousListe) {
            this.navigation.skip = 0;

            if (this.produitsListeSelectionne) {
                this.refreshProduitListeToMatchSousListe(formValues);
            } else {
                this.offresService.getListeProduitsByNomListe(formValues?.nomListeProduits?.nom).subscribe(res => {
                    const targetResult = res?.find(item => item?.id === formValues?.nomListeProduits?.id);
                    this.produitsListeSelectionne = targetResult;

                    this.refreshProduitListeToMatchSousListe(formValues);
                });
            }
        } else if (formValues?.libelleProduit || formValues?.fournisseur || formValues?.gamme || formValues?.forme) {
            this.produitsListeSelectionne = null;
            this.produitForm.get('nomListeProduits').reset(), this.produitForm.get('nomSousListe').reset();

            this.produitCriteria = new ProduitCriteria();
            this.produitCriteria.libelleProduit = formValues.libelleProduit;
            this.produitCriteria.gammeProduit = formValues.gamme;
            this.produitCriteria.formeProduit = formValues.forme;
            this.produitCriteria.fournisseur = formValues.fournisseur;

            if (!sortTriggered && !this.navigation.sortField && !this.navigation.sortMethod) {
                this.navigation.sortField = 'libelleProduit';
                this.navigation.sortMethod = 'asc';
            }

            this.offresService
                .searchProduitsPageable(this.produitCriteria, this.navigation)
                .subscribe(res => {
                    this.produitsView = { data: res.content, total: res.totalElements };
                });
        } else {
            this.alertService.error('Merci de spécifier au moins un critère de recherche de produit', 'MODAL');
        }
    }

    private refreshProduitListeToMatchSousListe(formValues: any) {
        const filteredProducts = this.produitsListeSelectionne?.detailProduitsList
            ?.filter(item => item?.tagProduitList?.toLowerCase()?.includes(formValues?.nomSousListe?.toLowerCase()));

        if (filteredProducts?.length) {
            this.produitsView = {
                total: filteredProducts?.length,
                data: filteredProducts?.map(item => item?.produit)
            };
        }
    }

    onCellClick(event: CellClickEvent): void {
        if (event.columnIndex === 0) {
            return;
        }

        const clickedItem = event.dataItem;
        if (!this.selectedProduitIds.includes(clickedItem.id)) {

            this.selectedProduits.push(clickedItem);
            this.selectedProduitIds = [...this.selectedProduitIds, clickedItem.id];
        } else {
            const targetIndex = this.selectedProduitIds?.indexOf(clickedItem?.id);

            if (targetIndex > -1) {
                this.selectedProduits?.splice(targetIndex, 1);
                this.selectedProduitIds?.splice(targetIndex, 1);

                this.selectedProduitIds = [...this.selectedProduitIds];
            }
        }
    }

    produitSortChange(sort: SortDescriptor[]): void {
        this.produitSort = sort;

        if (this.produitSort && this.produitSort.length > 0 && this.produitSort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.SearchProduit(true);
    }

    pageProduitChange(event: any): void {
        if (this.plateformeService.isPlateForme('WIN_OFFRE')) {
            if ((event.skip !== this.navigation.skip) || (event?.take !== this.navigation.pageSize)) {
                this.navigation.skip = event.skip;
                this.navigation.pageSize = event?.take;

                this.SearchProduit();
            }
        } else {
            this.SearchProduit();
        }
    }

    chooseProduit(item: SelectionEvent): void {
        const filteredSelectedRows = item.selectedRows;
        const filteredDeselectedRows = item.deselectedRows;

        filteredSelectedRows.forEach(row => {
            if (!this.selectedProduitIds.includes(row?.dataItem?.id)) {
                this.selectedProduits.push(row?.dataItem);
                this.selectedProduitIds.push(row?.dataItem?.id);
            }
        });

        filteredDeselectedRows.forEach(row => {
            if (this.selectedProduitIds.includes(row?.dataItem?.id)) {
                const targetIndex = this.selectedProduitIds.indexOf(row?.dataItem?.id);
                if (targetIndex > -1) {
                    this.selectedProduits.splice(targetIndex, 1);
                    this.selectedProduitIds.splice(targetIndex, 1);
                }
            }
        });
    }

    filterListe(criteria: EntrepriseCriteriaDto) {
        return this.fsOffreService.searchEntreprise(criteria, { skip: 0, pageSize: 10 });
    }

    searchlaboratoire = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    const criteria = new EntrepriseCriteriaDto({ raisonSociale: term, typeEntreprises: [SocieteType.FABRIQUANT] });
                    return this.filterListe(criteria);
                }
                return of({ content: [] });
            }),
            map(res => res.content?.slice(0, 10))
        );

    searchListeProduit = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.offresService.getListeProduitsByNomListe(term)
                }
                return of([]);
            }),
            map(res => res?.slice(0, 10))
        );


    searchSousListeProduit = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    const filterListe = this.produitsListeSelectionne?.detailProduitsList
                        ?.map(item => item?.tagProduitList)
                        ?.filter(item => item?.toLowerCase()?.includes(term?.toLowerCase()))

                    return of(Array.from(new Set(filterListe)))
                }
                return of([]);
            }),
            map((res: string[]) => res.sort())
        );


    sousListeFormatter = (result: string) => result ? result : null;
    listeProduitFormatter = (result: SelectedProduitsAssociations) => result ? result?.nom : null;
    laboratoireFormatter = (result: { raisonSociale: any; }) => result ? result.raisonSociale : null;
    laboFormatter = (result: { raisonSociale: string }) => result.raisonSociale;

    rowSelectedFn: RowSelectedFn = (row) => {
        return !!this.selectedProduits.find((item) => item.id === row.dataItem.id);
    };


    fournisseurchange() {
        if (this.produitForm.value.fournisseur) {
            this.listeGammeAutorises = this.listeGammeTotal.filter(
                (element) =>
                    element.fournisseur.id === this.produitForm.value.fournisseur.id
            );

            this.listeGammeAutorises = this.listeGammeAutorises.map((site) => ({
                value: site,
                label: site.libelle,
            }));

            this.listeGammeAutorises.unshift({ value: null, label: 'Toutes Gammes' });
        } else if (this.offre?.offreur) {
            this.listeGammeAutorises = this.listeGammeTotal.filter(
                (element) => element.fournisseur.id === this.offre.offreur.id
            );

            this.listeGammeAutorises = this.listeGammeAutorises.map((site) => ({
                value: site,
                label: site.libelle,
            }));

            this.listeGammeAutorises.unshift({ value: null, label: 'Toutes Gammes' });
        } else {
            this.listeGammeAutorises = [];
        }
    }

    private listenToStaticDataObs() {
        this.staticDataOffreService.staticDataObs$.subscribe((resp: any[]) => {
            if (!resp) return;

            [this.listeGammeTotal, this.listeforme] = resp;

            if (this.listeforme && this.listeforme.length > 0) {
                this.listeforme = this.listeforme.map((site) => ({ value: site, label: site.libelle }));
                this.listeforme.unshift({ label: 'Toutes Formes', value: null });
            }
        });
    }

    viderProduitForm(): void {
        this.produitForm.reset();
        this.produitsView = { data: [], total: 0 };
    }


    ajouterProduitsSelectionnes(): void {
        if (!this.selectedProduits || this.selectedProduits.length === 0) {
            this.alertService.error("Aucun produit sélectionné", 'MODAL');
            return;
        }

        this.produitSelectionChange.emit(this.selectedProduits);
        this.modalService.dismissAll();
    }
}