import { Pipe, PipeTransform } from "@angular/core";
import { StatutBl, StatutBlEnum } from "../models/statut-bl.model";

@Pipe({
    name: 'statutBl',
    pure: true
})
export class TraitementBlPipe implements PipeTransform {
    transform(value: StatutBl): string {
        switch(value) {
            case "EN": 
                return StatutBlEnum.ENREGISTREE;
            case "EX":
                return StatutBlEnum.EXPEDITION;
            case "PR":
                return StatutBlEnum.PREPARATION_BL;
            case "TR":
                return StatutBlEnum.TRANSMIS;
            case "CS":
                return StatutBlEnum.CLASSE_SANS_SUITE;
            default:
                return value;
        }
    }
    
}