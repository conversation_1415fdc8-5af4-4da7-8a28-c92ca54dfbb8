import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ListeOffresComponent } from "./liste/liste-offres.component";
import { EditOffresComponent } from "libs/web/shared/src/lib/components/gestion-offre/edit/edit-offres.component";
import { AuthoritiesGuard, CanDeactivateGuard } from "@wph/web/shared";
import { EditListeProduitsComponent } from "libs/web/shared/src/lib/components/gestion-offre/edit-liste-produits/edit-liste-produits.component";
import { ListeProduitsOcrComponent } from "libs/web/shared/src/lib/components/gestion-offre/liste-produits-ocr/liste-produits-ocr.component";

const routes: Routes = [
    {
        path: '',
        pathMatch: 'full',
        redirectTo: 'liste'
    },
    {
        path: 'liste',
        title: 'Liste Offres',
        component: ListeOffresComponent
    },
    {
        path: 'edit/:id',
        title: 'Consultation Offre',
        component: EditOffresComponent
    },
    {
        path: 'produits',
        title: 'Liste des produits',
        component: ListeProduitsOcrComponent
      },
      {
        path: 'ajouter-liste-produits',
        title: 'Ajouter Liste des produits',
        canDeactivate: [CanDeactivateGuard],
        component: EditListeProduitsComponent
      },
    {
        path: 'saisie',
        title: 'Création Offre',
        component: EditOffresComponent,
        canActivate: [AuthoritiesGuard],
        canDeactivate: [CanDeactivateGuard],
        data: { authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'saisie/:id',
        title: 'Création Offre',
        component: EditOffresComponent,
        canActivate: [AuthoritiesGuard],
        canDeactivate: [CanDeactivateGuard],
        data: { authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'] }
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class OffresRoutingModule  {}