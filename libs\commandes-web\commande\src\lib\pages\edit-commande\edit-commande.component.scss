#edit-commande {
  .actions {
    // width: calc(100vw - 30%);
    margin: 20px auto;

    .parent {
      display: grid;
      gap: 0.5rem;
      grid-template-columns:
        auto
        max-content
        max-content;
      grid-template-areas: "e1 e2 e3 ";
      height: 57px;
    }

    .e1 {
      grid-area: e1;
    }

    .e2 {
      grid-area: e2;
    }

    .e3 {
      grid-area: e3;
    }
  }

  .box {
    // width: calc(100vw - 30%);
    background-color: #fafafa;
    border-radius: 6px;
    border-width: 0;
    box-shadow: rgb(25 25 25 / 4%) 0 0 1px 0, rgb(0 0 0 / 10%) 0 3px 4px 0;
    font-family: Arial, sans-serif;
    font-size: 1em;
    padding: 10px;
    transition: all 200ms;
    // margin: 30px auto;
  }

  .gobackbtn {
    padding: 0.24rem 0.75rem !important;
  }

  .search_header {
    display: grid;
    gap: 0.5rem;
    grid-template-columns:
      auto
      max-content;
    grid-template-areas: "i1 i2";
    margin: 5px auto;
  }

  .i1 {
    grid-area: i1;
    text-align: center;

    div {
      display: inline-block;
      margin-left: 5px;
    }
  }

  .i2 {
    grid-area: i2;
  }

  .synthese {
    .synData {
      display: grid;
      gap: 0.5rem;
      grid-template-columns:
        auto
        auto;
      grid-template-areas: "syn1 syn2";
    }

    .syn1 {
      grid-area: auto;
    }

    .syn2 {
      grid-area: auto;
    }

    .title {
      font-size: 20px;
      margin: 15px 0;
      color: #8bc34a;
    }

    .synData-cus {
      width: 100%;

      .syn1,
      .syn2 {
        display: flex;
      }
    }
  }

  .total {
    text-align: end;

    .totalRes {
      border: 1px solid transparent;
      box-shadow: 0px 6px 6px -5px silver;
      border-radius: 3px;
      padding: 0px 10px;
      margin: 0 auto;
      display: inline-block;

      .totalnum {
        font-size: 15px;
        font-weight: bold;
        padding: 1px 5px;
        border-radius: 3px;
        background: #eeeeee;
      }
    }
  }
}

.bgNew {
  transition: 0.4s all ease-in-out !important;
  border-right: 6px solid #50BA71;
  // box-shadow: inset -23px -10px 7px -19px #6ab711;
}

.synthese-container {
  background: var(--cw-primary-200);
  border-radius: 5px;
  font-size: clamp(1.1rem, 2vw, 1.5rem);
}

.card-header {
  .form-control, .btn {
    font-size: 1rem;
    font-weight: 600 !important;
    border-radius: var(--winoffre-base-border-radius);
  }

  .form-control {
    color: black;
  }
}

.card-footer {
  overflow: hidden;
  position: relative;
}

#productContainerIpt {
  width: 340px !important;
}

.check-dispo {
  color: #fff;
  font-weight: 600;
  border-radius: var(--winoffre-base-border-radius);
  background: linear-gradient(to bottom, #f7a935,#d98200);

  &:hover {
    background: linear-gradient(to bottom, #d98200,#c87801);
  }
}

@media (max-width: 768px) {
  .safe-height {
    height: auto !important;
    min-height: calc(100vh - 65px) !important;
  }

  ::ng-deep #COMMANDE_WEB-container {
    #edit-commande-grid .k-grid-toolbar {
      display: none !important;
    }
  }
}

img.img-fluid {
  height: 100%;
  width: 100px;
  object-fit: cover;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease-in-out;
  &:hover {
    border: 2px solid var(--cw-primary-400);
  }
}

.img-wrapper {
  background: linear-gradient(173deg, #9eb7a8 18%, #bbcbc1 100%)
}

.badge-cstm {
  font-size: .75rem !important;
  border-radius: 10px;
  border: 2px dashed rgb(160, 160, 252);
}

.badge-alt {
  font-size: .85rem !important;
}

.b-radius {
  border-radius: 10px;
}

::ng-deep #COMMANDE_WEB-container {
  #edit-commande-grid .k-grid-toolbar {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom: none !important;
    background: var(--cw-primary-800) !important;
    border: 2px solid var(--cw-primary-800);
    border-bottom: 1px rgba(0, 0, 0, 0.08) solid !important;
  }
}

.winoffre-btn-container {
  font-weight: 600 !important;
  color: var(--wo-primary-800);
  background: #fff;
  // background: linear-gradient(173deg, #c5d5cb 18%, #9eb7a8 100%);

  &:hover {
    background: #e3e3e3;
  }
}