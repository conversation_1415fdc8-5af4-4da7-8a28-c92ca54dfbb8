import { SocieteType } from "@wph/shared";

export class GroupeEntrepriseCriteria {
    code?: string;
    fournisseurId?: number;
    gsm?: string;
    id?: number | string;
    isEnrolled?: boolean;
    nomResponsable?: string;
    raisonSociale?: string;
    statutEntreprise?: boolean;
    segmentEntreprise?: string;
    statutAccesClient?: boolean;
    typeEntreprise?: SocieteType;
    typeEntreprises?: SocieteType[];
    ville?: string;
    localite?: string;

    constructor(criteria?: Partial<GroupeEntrepriseCriteria>) {
        this.code = criteria?.code || null;
        this.fournisseurId = criteria?.fournisseurId || null;
        this.gsm = criteria?.gsm || null;
        this.id = criteria?.id || null;
        this.isEnrolled = criteria?.isEnrolled;
        this.nomResponsable = criteria?.nomResponsable || null;
        this.raisonSociale = criteria?.raisonSociale || null;
        this.segmentEntreprise = criteria?.segmentEntreprise || null;
        this.statutAccesClient = criteria?.statutAccesClient;
        this.typeEntreprise = criteria?.typeEntreprise || null;
        this.typeEntreprises = criteria?.typeEntreprises || null;
        this.ville = criteria?.ville || null;
        this.statutEntreprise = criteria?.statutEntreprise;
        this.localite = criteria?.localite;
    }
}

