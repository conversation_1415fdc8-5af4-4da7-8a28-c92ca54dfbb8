

export interface Ihoraire   {

	id?:  number;
  creneaux?: string;
  creneauxParsed?: Creneaux [];
  ferme?: boolean;
  jourSemaine?:  number;
	dateExceptionnelle?:string;
}

export interface Creneaux {
  id?: string;
  du?: string;
  au?: string;
} 



export class <PERSON><PERSON>re implements Ihoraire {
  constructor(public id?:  number,public jourSemaine?:  number,
    public creneaux?: string,public creneauxParsed?: Creneaux[],
    public ferme?: boolean, public dateExceptionnelle?: string) {}
}