import {
  Component,
  Input,
  <PERSON><PERSON><PERSON>,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AuthService } from '@wph/core/auth';
import { UserInputService, WorkerService } from '@wph/web/shared';
import { BlocOffre, Offre, OffresService } from '@wph/data-access';
import {
  AlertService,
  PlateformeService,
  Principal,
  UploadFileServiceService,
} from '@wph/shared';
import {
  Avis,
  AvisDTO,
  TypeAvis,
} from 'libs/federation-syndicats/src/lib/models/avis.model';
import { EnteteCommandeConsolideeMarche } from 'libs/federation-syndicats/src/lib/models/entete-commande.model';
import { GroupeEntreprise } from 'libs/federation-syndicats/src/lib/models/groupe-entreprise.model';
import { PharmacieEntreprise } from 'libs/federation-syndicats/src/lib/models/pharmacie-entreprise.model';
import { FederationSyndicatService } from 'libs/federation-syndicats/src/lib/services/federation-syndicats.service';
import { FsCommandesService } from 'libs/federation-syndicats/src/lib/services/fs-commandes.service';
import { FsOffreService } from 'libs/federation-syndicats/src/lib/services/fs-offres.service';
import { Observable, catchError, map, of } from 'rxjs';
import { SelectedPlateforme } from '@wph/web/layout';

type CardMode = 'A' | 'B' | 'C';

@Component({
  selector: 'wph-widgets-offres',
  templateUrl: './widgets-offres.component.html',
  styleUrls: ['./widgets-offres.component.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class WidgetsOffresComponent implements OnInit, OnDestroy {
  @Input('listeOffres')
  set offres(value: Offre[]) {
    if (!!value) {
      this._offres = value?.map((offre) => {
        this.canCurrentUserPlaceOrder(offre);
        
        return offre;
      });      
    }
  }

  @Input('mode') cardMode: CardMode = 'B';

  get offres() {
    return this._offres;
  }

  @Input('offre') set offre(value: Offre) {
    if (value) {
      this._offre = value;
      this.canCurrentUserPlaceOrder(this.offre);
    }
  }

  get offre() {
    return this._offre;
  }

  _offres: Offre[] | null = null;
  _offre: Offre | null = null;

  hasOffresDetails: boolean = false;
  plateformePrefix: string;

  monoProduitBlocOffre: BlocOffre | null = null;
  montantBrutCalcule: number;
  pageMode: string;
  currentUser: Principal | null = null;
  avis: Avis;
  monGroupe: any;
  membreId: number;
  estResponsable: boolean;
  membreDetails: any;
  offreId: number;
  enteteCommandeId: number;
  feedbackSent = false;
  totalViews:number = 0;
  currentPlateforme: SelectedPlateforme;
  @ViewChild('refusalModal', { static: true }) refusalModal: TemplateRef<any>;

  constructor(
    private router: Router,
    private ngZone: NgZone,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private authService: AuthService,
    private alertService: AlertService,
    private workerService: WorkerService,  
    private offresService: OffresService,
    private fsOffreService: FsOffreService,
    private userInputService: UserInputService,
    private plateformeService: PlateformeService,
    private fsCommandeService: FsCommandesService,
    private uploadService: UploadFileServiceService,
    private fedSyndicatService: FederationSyndicatService,
  ) {
    this.currentPlateforme = this.plateformeService.getCurrentPlateforme();
    this.currentUser = this.authService.currentUser();
    this.plateformePrefix = this.plateformeService.isPlateForme('WIN_OFFRE')
      ? 'win-offre'
      : 'achats-groupes';
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params: Params) => {
      this.pageMode = params['mode'];
    });
    this.membreDetails = this.authService.getPrincipal();
    this.membreId = this.authService.getPrincipal()?.societe?.id;
    this.estResponsable = this.authService.hasAnyAuthority([
      'ROLE_RESPONSABLE',
    ]);

    if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
      this.fedSyndicatService.getMyGroupe().then((myGroupe) => {
        this.monGroupe = myGroupe;
        this.initializeAvis();
      });
    }

  }

  toggleReason(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.avis.raison = inputElement.value;
  }

  initializeAvis(id?: number, enteteCommandeId?: number) {
    this.avis = {
      commentaire: null,
      estResponsable: this.estResponsable,
      id: null,
      typeAvis: TypeAvis.Negative,
      raison: '',
      enteteCommandeConsolideeMarche: {
        id: enteteCommandeId,
      } as EnteteCommandeConsolideeMarche,
      groupeEntreprise: { id: this.monGroupe?.id } as GroupeEntreprise,
      sondeurEntreprise: {
        id: this.membreDetails?.societe?.id,
      } as PharmacieEntreprise,
    };
  }

  openRefusalModal() {
  
    if (this.feedbackSent) {
      this.alertService.error('Un avis a déjà été envoyé pour cette offre.', 'MODAL');
      return;
    }
    this.modalService.open(this.refusalModal, { centered: true });
  }

  soumettreRefuserOffre(): void {
    this.userInputService
      .confirmAlt(
        'Confirmation',
        `Êtes vous sûr de vouloir réfuser cette offre ?`
      )
      .then(
        () => {
          this.avis.id = null;
          this.fsCommandeService.sauvegarderAvis(this.avis).subscribe(
            () => {
              const selectedOffre = this._offres?.find(offre => offre.id === this.offreId);
              const offreTitle = selectedOffre?.titre || 'Offre';
              this.feedbackSent = true;
              
              this.fsOffreService
                .refuserFsOffre(selectedOffre?.enteteCommandeId)
                .subscribe((res) => {
                  const modalRef = this.alertService.successAlt(
                    `L'offre "${offreTitle}" a été réfusée avec succès.`,
                    'Offre Réfusée',
                    'MODAL'
                  );
                  // Use setTimeout to wait for the modal to close before reloading
              setTimeout(() => {
                this.ngZone.run(() => {
                  window.location.reload();
                });
              }, 5000); 
              });
            },
            (error) => {
              const errorMessage =
                error?.error?.message ||
                "Erreur lors de l'enregistrement de l'avis.";
              this.alertService.error(errorMessage, 'MODAL');
              console.error('Error saving avis:', error);
            }
          );
          this.modalService.dismissAll();
        },
        () => null
      );
  }

  onThumbsUpClick(id): void {
    this.router.navigate([this.plateformePrefix + '/offres/edit/' + id], {
      queryParams: { readOnly: 'true', mode: this.pageMode },
    });
  }

  
  checkIfAvisExists(OffreId?: number): Observable<boolean> {
    const sondeurId = this.authService.getPrincipal()?.societe?.id;
    if (!sondeurId) {
      console.error('sondeurId is null');
      return of(false);
    }

    const cmdConsolideeId = null;
    const offreId = OffreId ?? null;

    return this.fsCommandeService
      .getBySondeurIdOffreIdCmdId(sondeurId, offreId, cmdConsolideeId)
      .pipe(
        map((avis: AvisDTO | AvisDTO[]) => {
          // Check if the response is an array
          if (Array.isArray(avis)) {
            return avis.length > 0;
          }
          // Check if the response is a single object
          return avis !== null;
        }),
        catchError((error) => {
          console.error('Error fetching avis:', error);
          return of(false);
        })
      );
  }

 

  onThumbsDownClick(offre: Partial<Offre>): void {
    this.offreId = offre.id;
    if (offre?.etatProposant === 'ANNULER' || !this.feedbackSent) {
      this.checkIfAvisExists(offre.id).subscribe((avisExists) => {
        console.log('Avis exists:', avisExists);
        if (avisExists ) {
          this.alertService.error('Un avis a déjà été envoyé pour cette offre.', 'MODAL');
          return;
        } else {
          this.initializeAvis(offre?.id, offre?.enteteCommandeId);
          this.openRefusalModal();
        }
      })
    } else {
      console.log('you can t send ');
      return
    }
  }

  Commander(id: number, qteCmd = 0) {
    let params = {};
    qteCmd && (params['qteCmd'] = qteCmd);
    this.router.navigate([this.plateformePrefix + '/commandes/edit/' + id], {
      state: { incr: true },
      queryParams: { offre: 'true', mode: 'cartes', ...params },
    });
  }

  Consulter(id: number) {
    this.router.navigate([this.plateformePrefix + '/offres/edit/' + id], {
      state: { incr: true },
      queryParams: { readOnly: 'true', mode: 'cartes' },
    });
  }

  consulterOffreAchatGroupe(item: Partial<Offre>) {
    this.router.navigate([this.plateformePrefix + '/offres/edit/' + item.id], {
      state: { incr: true },
      queryParams: { 
        readOnly: true, 
        mode: this.pageMode,
        natureOffre: item?.natureOffre,
        enteteCommandeId: item?.enteteCommandeId,
        etatCommandeAchatGroupe: item?.etatCommandeAchatGroupe,
      },
    });
  }

  getOffreImage(idHash: string) {
    return idHash ? this.uploadService.fetchUploadedDocument(idHash) : '';
  }

  cardClick(offre: Partial<Offre>): void {
    if (this.plateformeService.isPlateForme('WIN_OFFRE')) {
      if (
        !this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']) &&
        offre?.canPlaceOrder &&
        offre?.etat !== 'cloturee'
      ) {
        this.Commander(offre?.id, offre?.totalQteCmd);
      } else {
        this.Consulter(offre?.id);
      }
    } else {
      this.consulterOffreAchatGroupe(offre);
    }
  }

  updateMontantBrut(value: number, plus = false, minus = false): void {
    if (plus) {
      this.offre.totalQteCmd += 1;
    } else if (minus && (this.offre.totalQteCmd - 1) > 0) {
      this.offre.totalQteCmd -= 1;
    }

    if (value) {
      this.offre.totalQteCmd = value;
    }

    this.montantBrutCalcule = this.offre.totalQteCmd * (this.monoProduitBlocOffre?.prixVenteTtc);
  }

  isCardVisible(isVisible: boolean, offre: Offre): void {
    if (isVisible) {
      (this.cardMode === 'C' && !this.hasOffresDetails) ? this.fetchOffreById(offre?.id) : this.triggerCountdownTimer(offre);
    } else {
      this.workerService.stopCountDownOnOffre(offre?.id);
    }
  }

  private triggerCountdownTimer(offre: Offre) {
    this.workerService.stopCountDownOnOffre(offre?.id);
    this.workerService.startCountdownWorker(offre);
  }

  private fetchOffreById(id: number): void {
    this.offresService.getOffreById(id).subscribe(offre => {
      this.offre = { ...offre, etat: this.offre?.etat };

      this.hasOffresDetails = true;
      this.monoProduitBlocOffre = this.offre?.listeBlocs[0]?.listeFils[0];

      this.triggerCountdownTimer(this.offre);
    });
  }

  private canCurrentUserPlaceOrder(offre: Offre) {
    let canPlaceOrder: boolean;

    if (this.currentUser?.societe?.typeEntreprise === 'FABRIQUANT') {
      const matched = offre?.distributeurs?.filter(
        (dist) => dist?.id === this.currentUser?.societe?.id
      );

      canPlaceOrder = !!matched?.length;
    } else if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
      canPlaceOrder = false;
    }
    else {
      canPlaceOrder = true;
    }

    offre['canPlaceOrder'] = canPlaceOrder;
  }

  ngOnDestroy(): void {
    this.workerService.stopWorker();
  }
}
