import { AfterViewInit, Component, Input, OnDestroy, OnInit, ViewChild, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IonSearchbar, ModalController } from '@ionic/angular';
import { Fournisseur, OffresService } from '@wph/data-access';
import { AccesClientService, ClientFournisseur, SocieteType } from '@wph/shared';
import { Subject, debounceTime, distinctUntilChanged, of, switchMap, takeUntil, tap } from 'rxjs';

@Component({
    selector: 'wph-client-typeahead',
    templateUrl: './client-typeahead.component.html',
    styleUrls: ['./client-typeahead.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => ClientTypeaheadComponent),
            multi: true,
        }
    ]
})
export class ClientTypeaheadComponent implements OnInit, OnDestroy, ControlValueAccessor, AfterViewInit {

    @Input() isClientFournisseur: boolean = false;
    selectedClient: Fournisseur | string | null = null;
    listeClients: Fournisseur[] = [];
    filteredListeClients: Fournisseur[] = [];
    searchQuery$: Subject<string> = new Subject<string>();
    unsubscribe$: Subject<boolean> = new Subject<boolean>();
    isLoading: boolean = false;
    searchBarValue: string;

    startsWith: RegExp = new RegExp('^[gG]\\d+$');

    onChange = (_value: Fournisseur | string) => { };

    @ViewChild('search') searchbarElement: IonSearchbar;

    constructor(
        private offresService: OffresService,
        private modalController: ModalController,
        private accesClientService: AccesClientService
    ) { }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.searchbarElement?.setFocus();
        }, 300);
    }

    ngOnInit(): void {
        this.searchQuery$.pipe(
            takeUntil(this.unsubscribe$),
            tap(() => (this.isLoading = true)),
            debounceTime(400),
            distinctUntilChanged(),
            switchMap(query => {
                if (this.isClientFournisseur) {
                    return this.filterClientLocal(query?.toUpperCase());
                } else if (query.length > 2) {
                    return this.filterList(query.toLowerCase());
                }

                return of({ content: [] });
            })
        ).subscribe((filteredList) => {
            this.filteredListeClients = (this.isClientFournisseur ? filteredList : (filteredList as { content: any[] })?.content) as Fournisseur[];

            (
                this.searchBarValue &&
                ((filteredList as ClientFournisseur[])?.length || (filteredList as { content: any[] })?.content?.length)
            ) &&
                (
                    this.selectedClient =
                    this.filteredListeClients?.find(v => v?.code === (this.selectedClient as Fournisseur)?.code)
                );

            this.isLoading = false;
        });
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }

    writeValue(value: Fournisseur): void {
        if (value) {
            this.selectedClient = value;
            this.searchQuery$.next(value?.raisonSociale || (value as string));
            this.searchBarValue = value?.raisonSociale || (value as string);
        }
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    cancelChanges() {
        this.modalController.dismiss();
    }

    confirmChanges(): void {
        this.onChange(this.selectedClient);

        this.modalController.dismiss();
    }

    searchbarInput(ev: any) {
        this.searchBarValue = ev.detail.value;
        this.searchQuery$.next(this.searchBarValue);
    }

    filterList(searchQuery: string) {
        let criteriaKey: string, criteria: any = { segmentEntreprise: 'O' };

        this.startsWith.test(searchQuery) ?
            (criteriaKey = 'code', searchQuery = searchQuery.slice(1)) :
            (criteriaKey = 'raisonSociale');

        criteria = { ...criteria, [criteriaKey]: searchQuery, societeType: [SocieteType.CLIENT] };

        if (criteriaKey === 'raisonSociale') {
            const compoundQuery = searchQuery.split(',');

            compoundQuery.length > 1 &&
                (criteria = { ...criteria, raisonSociale: compoundQuery[0].trim(), ville: compoundQuery[1].trim() });
        }

        return this.offresService.searchSociete(criteria);
    }

    filterClientLocal(searchQuery: string) {
        return this.accesClientService.filterClientsLocal(searchQuery);
    }

    radioChange(ev) {
        this.selectedClient = ev.detail?.value;
    }

    trackItems(index: number, item: Fournisseur) {
        return item?.code;
    }

    registerOnTouched(fn: any): void { }

    setDisabledState?(isDisabled: boolean): void { }

}
