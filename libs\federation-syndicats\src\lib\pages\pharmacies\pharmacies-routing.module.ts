import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { EditPharmacieComponent } from "./edit-pharmacie/edit-pharmacie.component";
import { PharmaciesListComponent } from "./liste-pharmacies/pharmacies-list.component";
import { AuthoritiesGuard } from "@wph/web/shared";

const routes: Routes = [
    {
        path: 'edit/:id',
        component: EditPharmacieComponent
    },
    {
        path: 'suggerer',
        title: 'Suggérer Pharmacie',
        component: EditPharmacieComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_RESPONSABLE', 'ROLE_NATIONAL'] }
    },
    {
        path: 'liste',
        title: 'Suggestions Pharmacie',
        component: PharmaciesListComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_RESPONSABLE', 'ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'] }
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class PharmaciesRoutingModule {}