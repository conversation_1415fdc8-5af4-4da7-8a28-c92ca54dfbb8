$cyan: #44badc;
$primary: #1B75BB;
$warning: #f9bc0d;

.readonly-input-style {
  background: #00000008 !important;
  border-bottom: 1px solid #0000001f !important;
}

.disabled-style {
  background: #00000008 !important;
  border: 1px solid #0000001f !important;
}

.nav-tabs>li>a,
.nav-pills>li>a {
  font-size: 1rem !important;
  font-weight: normal;
}

.nav-tabs {
  position: relative;
  background: #fff;
  margin: 0;
  border-radius: var(--winoffre-base-border-radius);
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  background: var(--win-offre-bg-light-2)!important;
  border-color: #ffffff00 #ffffff00 var(--win-offre-primary-tint) rgba(255, 255, 255, 0) !important;
  color: var(--win-offre-primary-tint) !important;
  border-bottom-width: 2px;

}

.nav-tabs .nav-link {
  min-width: 60px;
  text-align: center;
  margin-right: 4px;
  border-top: unset !important;
  border-left: unset !important;
  border-right: unset !important;
}

// Hide Increase Up & Down Buttons for input type number
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;

}


input[type=number] {
  appearance: inherit;
  -moz-appearance: textfield;

  &::-webkit-outer-spin-button,
  ::-webkit-inner-spin-button {

    -webkit-appearance: none;
    margin: 0;
  }
}






// ---> Action button
.action-button {
  border: none;
  background-color: none;
  margin-left: 15px;
  width: 2rem;
  height: 2rem;
  border-radius: 1rem;
  // background-color: lighten($primary, 35);
}

.action-button i {
  padding: 0;
  margin: 0;
  font-size: 1.25rem;
  // color: $secondary;
}

// .warning-outlined{
//     color: #ffc107;
//     background-color: transparent;
//     background-image: none;
//     border-color: lighten($warning,30);

// }

.outlined {
  margin: inherit;
  padding: inherit;
  // width: 29px;
  width: 100%;
  height: 29px;
  font-size: 13px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  border-radius: 5px;
  box-shadow: unset !important;
  border: 0.05rem solid;
  line-height: 1;
}

.warning-outlined {
  color: darken($warning, 10);
  background: lighten($warning, 45) !important;
  border-color: $warning;

}

.info-outlined {
  color: darken($cyan, 10);
  background: lighten($cyan, 35) !important;
  border-color: $cyan;
}

.info-outlined:hover,
.info-outlined:active {
  background: lighten($cyan, 25) !important;
}

.warning-outlined:hover,
.warning-outlined:active {
  background: lighten($warning, 35) !important;
}


.card-synthese {
  background: #fff1e2 !important;
  color: black !important;
}



//// content height --> fix header position
.page-content {
  max-height: calc(100vh - 132px);
  min-height: calc(100vh - 132px);
  // padding:42px 6px 55px !important;
  overflow-y: scroll;
  overflow-x: hidden;
  padding-bottom: 20px !important;
  margin-bottom: 0 !important;
}

.federation-syndicat-content {
  .page-content {
    max-height: calc(100vh - 108px) !important;
    min-height: calc(100vh - 108px) !important;
  }
  
}
@media (min-width: 992px) {
  .page-content {
    // max-height: calc(100vh - 158px);
    min-height: calc(100vh - 158px);
  }
}

@media (min-width: 920px) {
  .page-content {
    -ms-overflow-style: none;
    /* for Internet Explorer, Edge */
    scrollbar-width: none;
    /* for Firefox */
    overflow-y: scroll;
  }

  .page-content::-webkit-scrollbar {
    display: none;
    /* for Chrome, Safari, and Opera */
  }
}


.round-square-btn {
  margin: 0;
  padding: 0;
  width: 29px;
  height: 29px;
  font-size: 16px;
  color: darken($primary, 10);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: lighten($primary, 45);
  border: 0.05rem solid;
  border-color: lighten($primary, 15);
  border-radius: 5px;
}

.round-square-btn:hover {
  background: lighten($primary, 35) !important;
}

// .container-fluid {
//   padding-left: 2px !important;
//   padding-right: 2px !important;
//   margin-left: 2px !important;
//   margin-right: 2px !important;
// }

.content-page {
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-bottom: 0px !important;
  margin-bottom: 0 !important;
}


// NEW MENU STYLES
.rd-wrapper {
  // background-color: #d3d4d5;
  background-color: var(--wf-primary-50);
  width: 98%;
  margin: 0 1%;
  border-radius: 25px;
  padding: 1px;
}

.cw-rd-wrapper {
  background-color: var(--cw-primary-100) !important;
}

.rd-wrapper>button {
  position: relative;
  border: none;
  flex: 1 2 auto;
  text-align: center;
  background-color: #fcfcfc;
  display: block;
  padding: 2px 2px;
  border-radius: 16px;
  margin: 2px;
  transition: background 0.2s ease-in;
  cursor: pointer;
  z-index: 0;

  &:before {
    content: "";
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: #dfe2ea;
    border-radius: 8px 8px 0 0;
    opacity: 0;
    transition: .3s;
  }

  &:last-child {
    margin-right: 2px !important;
  }

}

.row {
  --bs-gutter-x: 12px !important;
  --bs-gutter-y: 0px !important;
}


.rd-wrapper>button.active,
.sq-wrapper>button.active {
  color: #fff;
  background-color: lighten(#3389b9, 10);
}


.input-number-changing {
  background-color: lighten($warning, 46) !important;
  border-color: lighten($warning, 20) !important;
}

.has-enough-space {
  bottom: 44px;
}

.collapsed-buttons {
  border: 0;
  color: #5c635b;
  font-size: 22px;
  border-radius: 7px;
  background: #ffffff;
  color: #444245;
}

.collapsed-buttons:after {
  content: unset !important;
}

.badge-grey {
  color: darken(#676666, 0);
  background-color: #d9d9d9;
  font-size: 11px;
}

.badge-success {
  color: darken(#1bbb9a, 20);
  background-color: #8dddcdc7;
  font-size: 11px;
}

.badge-warning {
  color: #9a5c18;
  background-color: #fec07c;
  font-size: 11px;
}

.badge-danger {
  color: #ca020f;
  background-color: #f29ba188;
  font-size: 11px;
}

.badge-info {
  color: #066f80;
  background-color: #0eb4ce5e;
  font-size: 11px;
}

.badge-light {
  color: $gray-800;
  background-color: $gray-200;
  font-size: 11px;
}

.badge-primary {
  color: $primary;
  background-color: lighten($primary, 45);
  font-size: 11px;
}

.custom-select-padding {
  padding: 0.3rem 0.5rem 0.3rem 0.9rem !important;
}

.custom-search-input {
  padding: 0.45rem 1.9rem 0.45rem 0.9rem !important;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  font-size: 0.9rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  height: calc(2.25rem + 2px);
  min-width: 18rem !important;
  appearance: none;
}

.search-input-picker {
  width: 100% !important;
}

.custom-search-label {
  margin-top: 8px !important;
  margin-right: 8px !important;
}

.badge-outline-success {
  color: darken(#1bbb9a, 20);
  background-color: #8dddcd6b;
  font-size: 11px;
  border-color: darken(#1bbb9a, 20);
  border: 0.8px solid;
}

.badge-outline-warning {
  color: #eb6f0a;
  background-color: #ffcc0067;
  font-size: 11px;
  border-color: #FFCC00;
  border: 0.8px solid;
}

.badge-outline-danger {
  color: #ca020f;
  background-color: #f29ba154;
  font-size: 11px;
  border-color: #ca020f;
  border: 0.8px solid;
}

.badge-outline-info {
  color: #066f80;
  background-color: #0eb4ce3d;
  font-size: 11px;
  border-color: #066f80;
  border: 0.8px solid;

}

.badge-outline-primary {
  color: #1B75BB;
  background-color: #57a0d83d;
  font-size: 11px;
  border-color: #1B75BB;
  border: 0.8px solid;
}



// SCROLLING IN SUBMENU LEVEL
// body[data-leftbar-compact-mode=condensed]:not(.authentication-bg) .side-nav .side-nav-item:hover > .collapse > ul, body[data-leftbar-compact-mode=condensed]:not(.authentication-bg) .side-nav .side-nav-item:hover > .collapsing > ul{
//   max-height:200px;
//   overflow: scroll;
//   -ms-overflow-style: none; /* for Internet Explorer, Edge */
//   scrollbar-width: none; /* for Firefox */
//   overflow-y: scroll;
//   &::-webkit-scrollbar {
//     display: none; /* for Chrome, Safari, and Opera */
//   }
// }
@media (min-width: 992px) {
  .border-divider-left {
    border-left: 1px solid #dee2e6 !important;
  }

  .border-divider-right {
    border-right: 1px solid #dee2e6 !important;
  }
}

.tooltip-inner {
  padding: 0.2rem 0.5rem !important;
  color: #f1f3fa;
  text-align: center;
  font-size: 11px !important;
  border-radius: 0.2rem !important;
}

.link-hovered-class {
  background: #f1f8fd;
  transition: none;
  width: 260px !important;
  border-right: 3px solid #1B75BB;
}

.span-hovered-class {
  display: inline !important;
  color: #1B75BB !important;
}


// width classes with breakpoint
@media (min-width: 576px) {
  .w-sm-100 {
    width: 100% !important;
  }

  .w-sm-75 {
    width: 75% !important;
  }

  .w-sm-50 {
    width: 50% !important;
  }

  .w-sm-25 {
    width: 25% !important;
  }

  .h-sm-100 {
    height: 100% !important;
  }

  .h-sm-75 {
    height: 75% !important;
  }

  .h-sm-50 {
    height: 50% !important;
  }

  .h-sm-25 {
    height: 25% !important;
  }
}


/* Medium devices (tablets, 768px and up)*/
@media (min-width: 768px) {
  .w-md-100 {
    width: 100% !important;
  }

  .w-md-75 {
    width: 75% !important;
  }

  .w-md-50 {
    width: 50% !important;
  }

  .w-md-25 {
    width: 25% !important;
  }

  .h-md-100 {
    height: 100% !important;
  }

  .h-md-75 {
    height: 75% !important;
  }

  .h-md-50 {
    height: 50% !important;
  }

  .h-md-25 {
    height: 25% !important;
  }
}

/* Large devices (desktops, 992px and up)*/
@media (min-width: 992px) {
  .w-lg-100 {
    width: 100% !important;
  }

  .w-lg-75 {
    width: 75% !important;
  }

  .w-lg-50 {
    width: 50% !important;
  }

  .w-lg-25 {
    width: 25% !important;
  }

  .h-lg-100 {
    height: 100% !important;
  }

  .h-lg-75 {
    height: 75% !important;
  }

  .h-lg-50 {
    height: 50% !important;
  }

  .h-lg-25 {
    height: 25% !important;
  }
}

/* Extra large devices (large desktops, 1200px and up)*/
@media (min-width: 1200px) {
  .w-xl-100 {
    width: 100% !important;
  }

  .w-xl-75 {
    width: 75% !important;
  }

  .w-xl-50 {
    width: 50% !important;
  }

  .w-xl-25 {
    width: 25% !important;
  }

  .h-xl-100 {
    height: 100% !important;
  }

  .h-xl-75 {
    height: 75% !important;
  }

  .h-xl-50 {
    height: 50% !important;
  }

  .h-xl-25 {
    height: 25% !important;
  }
}

#cmd-statut,
#offre-statut {

  .badge-warning,
  .badge-danger,
  .badge-grey,
  .badge-success,
  .badge-info {
    font-size: 14px !important;
  }
}

#offre-statut {
  position: absolute;
  right: 1.5rem;
  top: 0.5rem;
  bottom: 0.5rem;
}

@include media-breakpoint-up(lg) {


  .left-homelandingcontdown .left-homelandingcontdown-body p:before {
    content: "";
    display: block;
    position: relative;
    margin-bottom: 36px;
    width: 89px;
    height: 1px;
    background: #6f7782;
  }


  .footer {
    position: absolute;
  }


  .left-homelanding {
    height: 100vh;
  }

  .left-homelandingcontup {
    min-height: 50%;
    display: grid;
    place-items: center;
  }


  .d-lg-grid {
    display: grid !important;
  }

}

.left-homelandingcontup {
  display: grid;
  place-items: center;
}

.blurred-img{
  position: absolute;
  top: 0;
  left: 0;
  background: url("../images/bghomeleft.png") no-repeat;
  background-size: auto;
  z-index: 0;
  width: 100%;
  height: 100%;
  background-position: top right;
}

.loginform{
  display: grid;
  place-content: center;
   
   
  } 
  
  .loginformbg{
    position: absolute;
    bottom: -.9rem;
    left: 55%;
    transform: translateX(-50%);
    background: url("../images/righthomebg.svg") no-repeat;
    background-size: 100%;
    z-index: 0;
    width: 110%;
    height: 110%;
    max-width: 850px;
    background-position: bottom;
    min-height: 700px;
  }

.pharma-hub-icon {
  content: url('../images/pharmalien_logo_only_dark.svg');
}

.pharma-hub-icon-dark {
  content: url('../images/pharmalien_logo_light.svg');
  width: auto;
  height: 30px;
  background-color: #3D8DA4;
}

.winpharm-logo-light {
  content: url('../images/winpharm_logo_light.png');
  width: auto;
  height: 30px;
  background-color: #3D8DA4;
}

.produits-card {
  border-radius: var(--winoffre-base-border-radius);
  overflow: auto !important;
  -ms-overflow-style: none;
  //scrollbar-color: #0F8FA3 white !important;

  scrollbar-width: thin; 

  ::-webkit-scrollbar {
    display: none;
    width: 6.5px;
    height: 8.5px;
    border-radius: 5px;
    background-clip: padding-box;
    border: 1px solid transparent;
    background-color: transparent !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
  }
}

#WIN_OFFRE-container {
  .k-grid .k-grid-aria-root {
    border-radius: 0px !important;
  }
  
}


.k-switch-md .k-switch-track {
  height: 35px;
  width: 100px;

  color: #f3f3f3;
  font-weight: 700 !important;
  padding-right: 5px;
  border: none;
}

.k-switch-on .k-switch-thumb {
  margin-left: 35px;
}

.k-switch-off .k-switch-thumb {
  margin-left: 2px;
}

.k-switch-on.k-focus .k-switch-track {
  outline: none !important;
}

.k-switch-on .k-switch-track {
  border-color: #5E9E71;
  background: #5E9E71;
} 

.k-switch-off .k-switch-track {
  background: #696C75;
}

.k-switch-md .k-switch-label-on, .k-switch-md .k-switch-label-off {
  font-size: .8rem;
  font-weight: 700 !important;
}

#edit-pharmacie-statut {
  .select2-container .select2-selection--single .select2-selection__rendered {
    margin-left: 20px !important;
  }
}












// .invalid-tooltip {
//   position: absolute;
//   top: 100%;
//   z-index: 5;
//   display: none;
//   max-width: 100%;
//   padding: 0.25rem 0.5rem;
//   margin-top: 0.1rem;
//   font-size: 0.875rem;
//   color: #fff !important;
//   background-color: rgba(254, 59, 101, 0.6) !important;
//   border-radius: 0.25rem;
// }