import { Component, OnInit, TemplateRef } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbDate, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GridDataResult, PageChangeEvent } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { CommandeService } from "@wph/commandes-web/commande";
import { AuthService } from "@wph/core/auth";
import { CommandeNormaleCriteria, Fournisseur, Pagination } from "@wph/data-access";
import { AccesClientService, ClientFournisseurCriteria } from "@wph/shared";
import * as moment from "moment";
import { Moment } from "moment";
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map } from "rxjs";
import { Content, ListCommandesCriteria } from "../../models/listCommandesCriteria";
import { getDynamicPageSize } from "@wph/web/shared";
import { StatutBlEnum } from "../../models/statut-bl.model";

@Component({
    selector: 'wph-list-commandes-normales',
    templateUrl: './list-commandes-normales.component.html',
    styleUrls: ['./list-commandes-normales.component.scss']
})
export class ListCommandesNormalesComponent implements OnInit {
    gridData: GridDataResult = { data: [], total: 0 };
    gridSort: SortDescriptor[];

    searchParams: CommandeNormaleCriteria;
    navigation: Pagination = { pageSize: 20, skip: 0 };
    pageSizes: number[] = [5, 10, 15, 20];

    dateCommandeDu: NgbDate | Moment | null = null;
    dateCommandeAu: NgbDate | Moment | null = null;
    isDateRangeApplied: boolean = false;
    codeSite: number;
    filterForm: FormGroup;

    listStatut = [
        { label: "Classé Sans Suite", value: "CS" },
        { label: StatutBlEnum.EXPEDITION, value: "EX" },
        { label: StatutBlEnum.PREPARATION_BL, value: "PR" },
    ];

    get controls() {
        return this.filterForm?.controls;
    }

    startsWith: RegExp = new RegExp('^[0-9]*$');

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private modalService: NgbModal,
        private authService: AuthService,
        private commandeService: CommandeService,
        private accesClientService: AccesClientService
    ) { }

    ngOnInit(): void {
        this.setPageSize();

        this.filterForm = this.fb.group({
            fournisseur: [null],
            dateCommandeDu: [null],
            dateCommandeAu: [null],
            statutTraiteBl: [null],
            nonEnvoyes: [false]
        });

        this.codeSite = this.authService.getPrincipal()?.societe?.noeud?.codeSite
    }

    setPageSize(currentHeight?: number): void {
        const dynamicSize = getDynamicPageSize(currentHeight);

        if (dynamicSize !== this.navigation.pageSize) {
            this.navigation.pageSize = dynamicSize;

            !this.pageSizes?.includes(dynamicSize) && this.pageSizes.push(dynamicSize);
            this.pageSizes = this.pageSizes.sort((a, b) => a - b);

            if (currentHeight && this.isDateRangeApplied) {
                this.getListeCommandesNormales();
            }
        }
    }

    getListeCommandesNormales(isFilter = false) {
        isFilter && (this.navigation.skip = 0);

        this.isDateRangeApplied = true
        const formValues = this.filterForm.getRawValue();

        this.searchParams = new CommandeNormaleCriteria({
            codeSite: this.codeSite,
            nonEnvoyes: formValues['nonEnvoyes'],
            codeClientLocal: formValues['fournisseur']?.code,
            statutTraiteBl: formValues?.statutTraiteBl
        });

        formValues['dateCommandeDu'] && (this.searchParams.dateValidationDebut = formValues['dateCommandeDu']);
        formValues['dateCommandeAu'] && (this.searchParams.dateValidationFin = formValues['dateCommandeAu']);

        this.commandeService.getListCommandes(
            this.searchParams,
            { ...this.navigation, skip: (Math.floor(this.navigation.skip / this.navigation.pageSize)), pageSize: this.navigation.pageSize }
        ).subscribe((res: ListCommandesCriteria) => {
            this.gridData = {
                data: res.content,
                total: res.totalElements
            };
        })
    }

    getTodaysCommandes() {
        const today = new Date();
        this.controls['dateCommandeAu'].setValue(moment(today));

        today.setHours(0, 0, 0);
        this.controls['dateCommandeDu'].setValue(moment(today));

        this.getListeCommandesNormales(true);
    }

    getYesterdaysCommandes() {
        const yesterday = new Date(new Date().setDate(new Date().getDate() - 1));
        this.controls['dateCommandeAu'].setValue(moment(yesterday));

        yesterday.setHours(0, 0, 0);
        this.controls['dateCommandeDu'].setValue(moment(yesterday));

        this.getListeCommandesNormales(true);
    }

    createBlFromCommande(cmdIdhash: string): void {
        this.commandeService.createBlFromCommande(cmdIdhash).subscribe(_res => {
            this.getListeCommandesNormales();
        });
    }

    pageChange(event: PageChangeEvent): void {
        if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.navigation.pageSize = event.take;

            this.getListeCommandesNormales();
        }
    }

    openFilterModal(content: TemplateRef<any>) {
        this.modalService.open(content, { size: 'md', centered: true });
    }

    vider() {
        this.isDateRangeApplied = false;
        this.filterForm.reset();
        this.gridData = { data: [], total: 0 };
    }

    gridSortChange(sort: SortDescriptor[]): void {
        if (this.isDateRangeApplied) {
            this.gridSort = sort;

            if (this.gridSort && this.gridSort.length > 0 && this.gridSort[0].dir) {
                this.navigation.sortField = sort[0].field;
                this.navigation.sortMethod = sort[0].dir;
            } else {
                this.navigation.sortField = null;
                this.navigation.sortMethod = null;
            }

            this.getListeCommandesNormales();
        }
    }

    consulterCommande(cmdId: string) {
        this.router.navigateByUrl(`/commande-web/bon-commande/${cmdId}`);
    }

    consulterBl(bl: Content) {
        this.router.navigate([`/commande-web/bl-commande/${bl?.idhash}`], { queryParams: { client: bl?.raisonSociale, ville: bl?.ville } });
    }

    filterList(searchQuery: string) {
        const criteria = new ClientFournisseurCriteria({ codeSite: this.codeSite, raisonOrCodeCSite: searchQuery, hasAccesClient: true });

        return this.accesClientService.searchClientFournisseur(criteria);
    }

    searchSociete = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase())
                }
                return of([]);
            }),
            map(res => res.slice(0, 5))
        );

    societeFormatter = (result: Fournisseur) => {
        return (result instanceof Object) ?
            `${result?.code}: ${result?.raisonSociale}` : '';
    };
}