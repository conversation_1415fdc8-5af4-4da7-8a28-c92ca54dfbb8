#pharmacy {
  padding: 10px;


  .header_search {
    display: grid;
    gap: 0.5rem;
    grid-template-columns: auto max-content;
    grid-template-areas: "left right";
    // height: 57px;
  }

  .left {
    grid-area: left;
  }

  .right {
    grid-area: right;
  }


  .s1 {
    grid-area: s1;
  }

  .s2 {
    grid-area: s2;
  }

  .s3 {
    grid-area: s3;
  }

  .s4 {
    grid-area: s4;
  }
}



.mfooter {
  text-align: right;
  margin: 0 18px 8px 0px;
}


label, .col-form-label {
  color: black !important;
  font-weight: 700;
}


.picker-input {
  .form-control {
    border-radius: var(--winoffre-base-border-radius) !important;
  }
}

.b-radius {
  border-radius: var(--winoffre-base-border-radius) !important;
}

.card-header {
  .btn {
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--winoffre-base-border-radius) !important;
  }
}

.btn {
  font-size: 1rem;
  font-weight: 600;
}

.input-group {
  .form-control {
    color: rgba(0, 0, 0, 0.432) !important;
    font-weight: 700;
      border-top-left-radius: var(--winoffre-base-border-radius);
      border-bottom-left-radius: var(--winoffre-base-border-radius);

  }
  .btn {
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-right-radius: var(--winoffre-base-border-radius);
  }

}

label {
  color: #7C7F86 !important;
  font-weight: 700 !important;
}

.picker-input {
  .form-control{
      border-radius: var(--winoffre-base-border-radius) !important;
  }
}

.card, .card-header, .card-body {
  border-radius: var(--winoffre-base-border-radius) !important;
}

.b-radius {
  border-radius: var(--winoffre-base-border-radius) !important;
}

.card-body{
  border-radius: var(--winoffre-base-border-radius) !important;
}



.btn-success{
  background: var(--fs-success) !important;
  border-color: var(--fs-success) !important;
}

.btn-danger{
  background: var(--fs-danger) !important;
  border-color: var(--fs-danger) !important;
}
