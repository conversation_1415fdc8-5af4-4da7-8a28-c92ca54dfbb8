export interface DashboardData {
    commandesConsommees?: number;
    commandesEnAttenteDeLivraison?: number;
    commandesEnCoursDeSaisie?: number;
    commandesSupportees?: number;
    nouvellesOffres?: number;
    offresActives?: number;
  }

export interface LaborationDashboardData {
    offresActives: number;
    nouvellesOffres: number;
    commandesEnCoursDeSaisie: number;
    commandesEnAttenteDeLivraison: number;
    commandesSupportees: number;
    commandesConsommees: number;
    nbrCmdTraiteParLabo: number;
    nbrCmdNonTraiteParLabo: number;
    chiffreDaffaireLaboCmdHt: number;
    chiffreDaffaireLaboCmdTtc: number;
    nbreClientLabo: number;
  };
