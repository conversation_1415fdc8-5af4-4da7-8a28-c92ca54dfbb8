import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ReclamationsService } from './reclamations.service';
import { ReclamationCriteria } from '../models/reclamation';

@Injectable({
  providedIn: 'root'
})
export class ReclamationBadgeService {
  private badgeCount = new BehaviorSubject<number>(0);
  badgeCount$ = this.badgeCount.asObservable();
  private lastUpdate: number = 0;
  private updateInterval: number = 5000; // 5 seconds

  constructor(private reclamationsService: ReclamationsService) {}

  updateBadgeCount(codeSite: number): void {
    const now = Date.now();
    // Only update if enough time has passed since last update
    if (now - this.lastUpdate > this.updateInterval) {
      const criteria: ReclamationCriteria = {
        codeSite: codeSite,
        statut: ['N']  // Only get new reclamations
      };

      this.reclamationsService.getAllReclamation(criteria, {
        skip: 0,
        pageSize: 1  // We only need the total count
      }).subscribe(result => {
        this.badgeCount.next(result.totalElements);
        this.lastUpdate = now;
      });
    }
  }

  getBadgeCount(): Observable<number> {
    return this.badgeCount$;
  }

  // Force update regardless of time interval
  forceUpdate(codeSite: number): void {
    const criteria: ReclamationCriteria = {
      codeSite: codeSite,
      statut: ['N']
    };

    this.reclamationsService.getAllReclamation(criteria, {
      skip: 0,
      pageSize: 1
    }).subscribe(result => {
      this.badgeCount.next(result.totalElements);
      this.lastUpdate = Date.now();
    });
  }
}