import { Router } from '@angular/router';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { DataBindingDirective, GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import { CompositeFilterDescriptor, SortDescriptor } from '@progress/kendo-data-query';
import { ProduitFournisseurCriteria } from '../../models/produit-fournisseur-criteria';
import { CommandeService } from '../../services/commande.service';
import { Pagination } from '@wph/data-access';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { FormControl } from '@angular/forms';
import { AlertService } from '@wph/shared';
import { getDynamicPageSize } from '@wph/web/shared';

@Component({
    selector: 'wph-catalogue-produit',
    templateUrl: './catalogue-produit.component.html',
    styleUrls: ['./catalogue-produit.component.scss']
})
export class CatalogueProduitComponent implements OnInit, OnDestroy {
    @ViewChild(DataBindingDirective) dataBinding: DataBindingDirective;
    // search
    searchData = new FormControl('');
    unsubscribe$: Subject<boolean> = new Subject<boolean>();

    originalItems = [];
    gridView: GridDataResult;
    mySelection: string[] = [];
    searchParams: ProduitFournisseurCriteria;
    gridSort: SortDescriptor[];
    navigation: Pagination = { skip: 0, pageSize: 15 };
    pageSizes: number[] = [5, 10, 15, 20];

    qteCmd = { id: '', qte: 1 };
    filter: CompositeFilterDescriptor;

    constructor(
        private router: Router,
        private srv: CommandeService,
        private alertService: AlertService
    ) { }

    ngOnInit() {
        this.setPageSize();

        if (window.innerWidth >= 920) {
            document.getElementById('goSearchCatalogue').focus();
        }

        this.initSearch();

        this.listenToRechercheProduitChanges();
    }

    listenToRechercheProduitChanges() {
        this.searchData.valueChanges
            .pipe(
                takeUntil(this.unsubscribe$),
                debounceTime(300),
                distinctUntilChanged()
            )
            .subscribe(_term => {
                (this.navigation.skip = 0), this.initSearch();
            });
    }

    setPageSize(currentHeight?: number): void {
        const dynamicSize = getDynamicPageSize(currentHeight, 48);

        if (dynamicSize !== this.navigation.pageSize) {
            this.navigation.pageSize = dynamicSize;

            this.pageSizes.push(dynamicSize);
            this.pageSizes = this.pageSizes.sort((a, b) => a - b);

            currentHeight && this.initSearch();
        }
    }

    initSearch() {
        this.gridView = {
            data: [],
            total: 0
        };
        this.searchParams = {
            codeProduitSite: null,
            genericCriteria: this.searchData.value
        };

        this.srv.getCatalogueProduits(
            this.searchParams,
            { ...this.navigation, skip: this.getPageNumber(this.navigation.skip, this.navigation.pageSize), pageSize: this.navigation.pageSize }
        )
            .subscribe(res => {
                for (let i = 0; i < res.content.length; i++) {
                    const obj = res.content[i];
                    obj.qteCmd = 1;
                }
                this.gridView = {
                    data: res.content,
                    total: res.totalElements
                };

                this.originalItems = res.content;
            });
    }

    goSearch() {
        this.navigation.skip = 0;
        this.initSearch();
    }

    getCommandeNumber(e: any) {
        for (let i = 0; i < this.gridView.data.length; i++) {
            if (this.gridView.data[i].codeProduit === e.id) {
                this.gridView.data[i].qteCmd = e.number;
            }
        }
    }

    commanderProduit(e: any) {
        const payload = {
            quantite: e.qteCmd,
            codeProduit: e.codeProduit
        };

        this.srv.dansPanier(payload)
            .subscribe(res => {
                this.alertService.info('Le produit a été ajouté au panier avec succès.');
                // update commande number in panier
                this.srv.panierChanged(res);
            });
    }

    navige(id: string) {
        this.router.navigate([`commande-web/fiche-produit/${id}/produit-fournisseur`]);
    }

    getPageNumber(skip: number, pageSize: number) {
        return Math.floor(skip / pageSize);
    }

    public pageChange(event: PageChangeEvent): void {
        if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.navigation.pageSize = event.take;

            this.initSearch();
        }
    }

    dispoCheck(produit: any) {
        this.srv.checkDispo({ codeProduit: produit.codeProduit, qteCmd: produit.qteCmd }).subscribe(
            res => {
                if (res[0].codeRetour) {
                    produit.disponibiliteCode = 'D';
                    produit.disponibiliteLibelle = 'Appelez votre grossiste';
                } else {
                    produit.disponibiliteCode = res[0].listeReponses[0].dispo;
                    produit.disponibiliteLibelle = res[0].listeReponses[0].dispoLibelle;
                }
            });
    }

    gridSortChange(sort: SortDescriptor[]): void {
        this.gridSort = sort;

        if (this.gridSort && this.gridSort.length > 0 && this.gridSort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.initSearch();
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }

}
