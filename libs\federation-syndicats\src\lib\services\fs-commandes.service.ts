import { HttpClient, HttpParams } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { BlocOffre, CommandeCriteria, Offre, Page, Pagination } from '@wph/data-access';
import { EnteteCommandeConsolideeMarche, EnteteCommandeUnitaireMarche, FsCommandeCriteria, SearchCommandeUnitaire, SearchCommandesConsolidee, SearchEnteteCommandeView } from '@wph/federation-syndicats';
import { DomainEnumeration } from '@wph/shared';
import { BehaviorSubject, Observable } from 'rxjs';
import { Avis, AvisCriteria, AvisDTO } from '../models/avis.model';
 interface AvisResponse {
  content: AvisDTO[];
  totalElements: number;
}
@Injectable({
  providedIn: 'root'
})
export class FsCommandesService {
  private baseUrl: string;
 
  selectedCmdsUnitaires$: BehaviorSubject<BlocOffre[]> = new BehaviorSubject<BlocOffre[]>(null);

  constructor(
    @Inject('ENVIROMENT') private env: any,
    private http: HttpClient
  ) {
    this.baseUrl = this.env?.base_url;
  }

  searchCommandesConsolidee(pagination: Pagination, criteria: FsCommandeCriteria): Observable<SearchCommandesConsolidee> {
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
    }

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return this.http.post<SearchCommandesConsolidee>(`${this.baseUrl}/api/v1/offre-achat-groupe/search-cmd-consolidee`, criteria, { params, observe: 'body' });
  }


  searchCommandesConsolideeProducts(pagination: Pagination, criteria: FsCommandeCriteria): Observable<Page<EnteteCommandeConsolideeMarche>> {
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
    }

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return this.http.post<Page<EnteteCommandeConsolideeMarche>>(`${this.baseUrl}/api/v1/offre-achat-groupe/search-cmd-consolidee-dto`, criteria, { params, observe: 'body' });
  }


  searchCommandesLabo(pagination: Pagination, criteria: CommandeCriteria): Observable<SearchEnteteCommandeView> {
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
    }

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return this.http.post<SearchEnteteCommandeView>(`${this.baseUrl}/api/v1/offre-achat-groupe/search-aggregated-cmds`, criteria, { params, observe: 'body' });
  }


  searchCommandesUnitaires(pagination: Pagination, criteria: FsCommandeCriteria): Observable<SearchCommandeUnitaire> {
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
    }

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return this.http.post<SearchCommandeUnitaire>(`${this.baseUrl}/api/v1/offre-achat-groupe/search-cmd-unitaire`, criteria, { params, observe: 'body' });
  }

  getCommandeUnitaireById(cmdId: number, groupeId: number): Observable<EnteteCommandeUnitaireMarche> {
    return this.http.get<EnteteCommandeUnitaireMarche>(`${this.baseUrl}/api/v1/offre-achat-groupe/get-cmd-unitaire-by-id`, { params: { cmdId, groupeId }, observe: 'body' });
  }


  getCommandeUnitaireByCmdConsolideeId(cmdConsolideeId: number): Observable<Offre[]> {
    return this.http.get<Offre[]>(`${this.baseUrl}/api/v1/offre-achat-groupe/get-cmds-unitaire-by-cmd-id`, { params: { cmdConsolideeId }, observe: 'body' });
  }

  saveCommandeUnitaire(payload: Offre, cmdConsolideeId?: number): Observable<any> {
    let params = {};
    cmdConsolideeId && (params['cmdConsolideeId'] = cmdConsolideeId);

    return this.http.post<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/save-cmd-unitaire`, payload, { params, observe: 'body' });
  }

  transmettreCommandeUnitaire(cmdId: number, groupeId: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/transmettre-commande-unitaire`, { params: { cmdId, groupeId }, observe: 'body' });
  }

  reactiverCommandeUnitaire(cmdId: number[], groupeId: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/reactiver-commande-unitaire`, { params: { cmdId: cmdId, groupeId }, observe: 'body' });
  }

  annulerCommandeUnitaire(cmdId: number, groupeId: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/annuler-commande-unitaire`, { params: { cmdId, groupeId }, observe: 'body' });
  }

  annulerCommandeConsolidee(cmdConsolideeId: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/annuler-cmd-consolidee`, { params: { cmdConsolideeId }, observe: 'body' });
  }

  attacherSupporteur(cmdConsolideeId : number, supporterId: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/attacher-supporter-cmd`, { params: { supporterId, cmdConsolideeId }, observe: 'body' });
  }

  buildCommandeConsolidee(cmdConsolideeId: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/build-cmd-consolidee`, { params: { cmdConsolideeId }, observe: 'body' });
  }

  getCommandeConsolideeById(cmdConsolideeId: number): Observable<EnteteCommandeConsolideeMarche> {
    const params = { cmdConsolideeId };

    return this.http.get<EnteteCommandeConsolideeMarche>(`${this.baseUrl}/api/v1/offre-achat-groupe/get-cmd-consolidee-dto`, { params, observe: 'body' });
  }

  obtenirAvisParCommandeConsolideeId(cmdConsolideeId: number): Observable<Avis> {
    return this.http.get<Avis>(`${this.baseUrl}/api/v1/avis/commandeConsalidee/${cmdConsolideeId}`);
  }

  rechercherAvisParCritere(criteria: AvisCriteria): Observable<AvisResponse> {
    return this.http.post<AvisResponse>(`${this.baseUrl}/api/v1/avis/recherche`, criteria);
  }

 
  getBySondeurIdOffreIdCmdId(sondeurId: number, offreId?: number, cmdConsolideeId?: any): Observable<AvisDTO[]> {
    if (!sondeurId) {
      throw new Error('sondeurId is required');
    }
  
    let params = new HttpParams().set('sondeurId', sondeurId.toString());
    if (offreId !== undefined && offreId !== null) {
      params = params.set('offreId', offreId.toString());
    }
  
    if (cmdConsolideeId !== undefined && cmdConsolideeId !== null) {
      params = params.set('cmdConsolideeId', cmdConsolideeId.toString());
    }
  

    return this.http.get<AvisDTO[]>(`${this.baseUrl}/api/v1/avis/get-by-sondeur-id`, { params });
  }


 
  

  sauvegarderAvis(avis: AvisDTO): Observable<AvisDTO> {
    return this.http.post<AvisDTO>(`${this.baseUrl}/api/v1/avis/sauvegarder`, avis);
  }

  imprimerCommandeConsolidee(cmdConsolideeId: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/print-cmd-consolidee`, { params: { cmdConsolideeId }, observe: 'body', responseType: 'blob' as 'json' });
  }


  buildCommandeUnitaireAsOffre(cmdUnitaireId?: number): Observable<Offre> {
    return this.http.get<Offre>(`${this.baseUrl}/api/v1/offre-achat-groupe/build-cmd-unitaire`, { params: { cmdUnitaireId }, observe: 'body' });
  }

  envoyerCommandeConsolidee(cmdConsolideeId: number, groupeId: number, emailFournisseur: string = null): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/envoyer-cmd-consolidee`, { params: { cmdConsolideeId, groupeId, emailFournisseur }, observe: 'body' });
  }

  envoyerCommandeIndividuelle(commandeId: number, emailFournisseur: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/envoyer-cmd/${commandeId}`, { observe: 'body', params: { emailFournisseur } })
  }

  validerCommandeConsolidee(payload: Offre): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/valider-cmd-consolidee`, payload, { observe: 'body' });
  }

  saveDetailCommandeUnitaire(payload: BlocOffre[], groupeId: number): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/save-detail-cmd-unitaire`, payload, { params: { groupeId }, observe: 'body' });
  }

  getCommandeUnitaireByMembre(membreId: number, cmdConsolideeId: number): Observable<any> {
    return this.http.get(`${this.baseUrl}/api/v1/offre-achat-groupe/get-cmd-unitaire-by-membre`, { params: { membreId, cmdConsolideeId }, observe: 'body' });
  }

  attacherModePaiement(cmdConsolideeId : number, delaiPaiementId: number): Observable<any> {
    let params = { cmdConsolideeId };
    delaiPaiementId && (params['delaiPaiementId'] = delaiPaiementId);

    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/attacher-delaipaiement-cmd`, { params, observe: 'body' });
  }

  clotureCommandeConsolidee(params: { cmdConsolideeId: number }): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/cloturer-cmd-consolide`, { params, observe: 'body' });
  }

  getPageNumber(skip: number, pageSize: number) {
    return (skip && pageSize) ? Math.floor(skip / pageSize) : 0;
  }
}
