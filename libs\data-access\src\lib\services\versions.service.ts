import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import * as CryptoJS from 'crypto-js';
import { Observable } from 'rxjs';



// function generateHMACSignature(method, path, body, timestamp, apiSecret) {
//   const stringToSign = `${method}${path}${timestamp}${body ?? ''}`;

//   console.log('Client-side details:');
//   console.log('Method:', method);
//   console.log('Path:', path);
//   console.log('Timestamp:', timestamp);
//   console.log('Body:', body);
//   console.log('API Secret:', apiSecret);
//   console.log('String to sign:', stringToSign);

//   const signature = CryptoJS.HmacSHA256(stringToSign, apiSecret).toString(CryptoJS.enc.Hex);

//   console.log('Client-side computed signature:', signature);

//   return signature;;
// }



@Injectable({
  providedIn: 'root'
})
export class VersionsService {
  private apiKey = 'test';
  private apiSecret = 'test';
  constructor(private http: HttpClient) { }

  // updateVersion(appName: string, newVersion: string) {
  //   const method = 'POST';
  //   const path = '/api/update-version';
  //   const body = { app_name: appName, new_version: newVersion };
  //   const timestamp = Math.floor(Date.now() / 1000).toString();

  //   const signature = generateHMACSignature(method, path, body, timestamp, this.apiSecret);

  //   const headers = new HttpHeaders({
  //     'Content-Type': 'application/json',
  //     'api-key': this.apiKey,
  //     'x-signature': signature,
  //     'x-timestamp': timestamp,
  //     'Authorization': 'test'
  //   });

  //   return this.http.post('http://localhost:8000/api/update-version', body, { headers });
  // }
  // getLatestVersion()  {
  //   const method = 'GET';
  //   const path = '/api/check-version/fs';
  //   const timestamp = Math.floor(Date.now() / 1000).toString();

  //   const signature = generateHMACSignature(method, path, null, timestamp, this.apiSecret);

  //   const headers = new HttpHeaders({
  //     'Content-Type': 'application/json',
  //     'api-key': this.apiKey,
  //     'x-signature': signature,
  //     'x-timestamp': timestamp,
  //     'petoken': 'test'
  //   });
  //   return this.http.get<{ latest_version: string }>('http://***************:8000/api/check-version/fs', { headers ,observe: 'body'});
  // }

  getLocalVersion() {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const packageJson = require('../../../../../package.json');
    return packageJson.version;
  }
}
