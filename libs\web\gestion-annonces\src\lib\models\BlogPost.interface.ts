import { SafeUrl } from "@angular/platform-browser";
import { DocMetaDataDto, Role, Societe } from "@wph/shared";
import { Moment } from "moment";

/* ------------------------- api list posts response ------------------------ */
export class BlogPostResponseInterface {
    content: BlogPostDto[];
    pageable: Pageable;
    last: boolean;
    totalPages: number;
    totalElements: number;
    size: number;
    number: number;
    sort: Sort;
    numberOfElements: number;
    first: boolean;
    empty: boolean;
}

class Pageable {
    sort: Sort;
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
}

class Sort {
    unsorted: boolean;
    sorted: boolean;
    empty: boolean;
}

export type PublicationScope = 'G' | 'F' | 'A';
export type PublicationType = 'A' | 'M';
export type ActualiteType = 'IMAGE' | 'VIDEO';

export class BlogPostDto {
    id?: number;
    sujet?: string;
    auteur?: string;
    imageUrl?: string;
    logoUrl?: string;
    readMore?: boolean;
    titre?: string;
    datePublication?: string;
    dateSuppression?: string;
    dateCreation?: string;
    categorie?: Categorie;
    createur?: Createur;
    paramAffichage?: string;
    docImagePost?: DocMetaDataDto;
    docLogoPost?: DocMetaDataDto;
    statut?: string;
    libelleUrl?: string;
    plateformes?: PlateformeItem[];
    listRolesCible?: Role[];
    typeActualite?: ActualiteType;
    url?: string | SafeUrl;
    videoUrl?: string;
    type?: PublicationType;
    fournisseur?: Societe;
    scopePublication?: PublicationScope;
    dateDebutVisibilite?: string;
    dateFinVisibilite?: string;

    isPinned?: boolean; // TODO: sync with backend defined property name
    contentItems?: ContentItem[];
}

export class Createur {
    audited: boolean;
    idhash: string;
    firstname: string;
    lastname: string;
    email?: string;
    entrepriseDTO?: Societe;
    city?: any;
}

/* -------------------------- api list posts params ------------------------- */
export class BlogPostCriteria {
    dateCreationDebut?: any;
    dateCreationFin?: any;
    dateDebutVisibilite?: any;
    dateFinVisibilite?: any;
    categorie?: Categorie;
    visible?: boolean;
    genericCriteria?: string;
    type?: string;
    plateformePostDtos?: PlateformeItem[];
    typeActualite?: ActualiteType;
    scopes?: PublicationScope[];
    statuts?: string[];
    fournisseursId?: number[] | string[] | null;
}

export class Categorie {
    id?: number | null;
    libelle?: string;
    dateCreation?: Moment;
    audited?: boolean;

    constructor() {
        this.id = null;
        this.libelle = null;
        this.audited = false;
    }
}

export class PlateformeItem {
    id?: number;
    libelle?: string;
}

/* ------------------------ api create poste response ----------------------- */
export class CreateBlogPostResponse {
    id: number;
    sujet: string;
    image?: any;
    imageType?: any;
    auteur: string;
    titre: string;
    datePublication: string;
    categoriePostDto?: any;
    createur: Createur;
}

export interface ContentItem {
  type: 'IMAGE' | 'VIDEO' | 'TEXT';
  url?: string;
  thumbnailUrl?: string;
  caption?: string;
  duration?: string; // For videos
}