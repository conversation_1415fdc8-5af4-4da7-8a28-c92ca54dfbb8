#catalogue {
  padding: 10px;



  .header_search {
    display: grid;
    gap: 0.5rem;
    grid-template-columns: auto max-content;
    grid-template-areas: "left right";
    height: 57px;
  }

  .left {
    grid-area: left;
  }

  .right {
    grid-area: right;

    .fix_position {
      transform: translate(0px, 10px);
    }
  }

  // kendo-grid {
  //   z-index: -1;
  // }


  .s1 {
    grid-area: s1;
  }

  .s2 {
    grid-area: s2;
  }

  .s3 {
    grid-area: s3;
  }

  .s4 {
    grid-area: s4;
  }
}

.b-radius {
    border-radius: 10px !important;
}

.grid-height {
  min-height: calc(100vh - 190px) !important;
}