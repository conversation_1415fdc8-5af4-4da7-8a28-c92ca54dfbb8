import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Template<PERSON><PERSON> } from '@angular/core';
import { FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { PageChangeEvent } from '@progress/kendo-angular-pager';
import { SortDescriptor } from '@progress/kendo-data-query';
import { Pagination } from '@wph/data-access';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { FederationSyndicatService } from '../../../services/federation-syndicats.service';
import { ActivatedRoute, Router } from '@angular/router';
import { SuggestionFicheClientCriteria } from '../../../models/suggestion-fiche-client-criteria.model';
import { GridDataResult, SelectionEvent } from '@progress/kendo-angular-grid';
import { AlertService } from '@wph/shared';
import { ExportPdf, ExportPdfService, UserInputService } from '@wph/web/shared';
import { PharmacieEntreprise } from '../../../models/pharmacie-entreprise.model';
import { SuggestionFicheClient } from '../../../models/suggestion-fiche-client.model';
import { AuthService } from '@wph/core/auth';
import { DatePipe } from '@angular/common';


@Component({
  selector: 'wph-pharmacies-list',
  templateUrl: './pharmacies-list.component.html',
  styleUrls: ['./pharmacies-list.component.scss'],
})
export class PharmaciesListComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  gridView: GridDataResult = { total: 0, data: [] };
  pharmacieSort: SortDescriptor[];
  editForm: FormGroup;
  isFormReady = false;
  filterList: FormControl = new FormControl();
  searchCriteria: SuggestionFicheClientCriteria =
    new SuggestionFicheClientCriteria();

  navigation: Pagination = {
    skip: 0,
    pageSize: 20,
    sortField: 'createdAt',
    sortMethod: 'desc',
  };
  displayFilter: boolean;
  filterForm: FormGroup;

  stautsLabelsValues: any[] = [
    { label: 'Tout', value: null },
    { label: 'Nouvelle', value: 'T' },
    { label: 'Validés', value: 'V' },
    { label: 'Rejetés', value: 'R' },
    { label: 'Annulés', value: 'A' }];

  exportPdfRef: ExportPdf;


  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private datePipe: DatePipe,
    private authService: AuthService,
    private exportPdfServ: ExportPdfService,
    private alertService: AlertService,
    private userInputService: UserInputService,
    private fedSyndicatService: FederationSyndicatService,
  ) {
    this.initFilterGroup();
    this.searchCriteria.etatSuggestion = ['T'];
  }

  get isInactive$() {
    return this.fedSyndicatService.inactiveAccount$;
  }

  ngOnInit() {
    this.buildExport();
    this.listenToSearchFilterChanges();
    this.searchSuggestionsFicheClient();
  }

  ngOnDestroy() {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  OnPageChange(event: number): void {
    this.searchSuggestionsFicheClient()

   }

  searchSuggestionsFicheClient(): void {

    this.fedSyndicatService
      .searchSuggestionsFichePharmacie(this.navigation, this.searchCriteria)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (res: any) => {
          this.gridView = {
            total: res?.totalElements ?? 0,
            data: res?.content ?? [],
          };

          this.exportPdfRef.setData(this.gridView.data);
        },
        (error) => {
          console.error('Error fetching data', error);
        }
      );
  }

  buildExport(): void {
    this.exportPdfRef = this.exportPdfServ.
      ref<SuggestionFicheClient>()
      .setTitle('Liste des Suggestions')

    if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
      this.exportPdfRef.addColumn('*', 'Suggéré Par', { width: 110,  transform: (value) => {
        return `${value?.userSuggereur?.lastname} ${value?.userSuggereur?.firstname}`;
      }})
    }

    this.exportPdfRef
      .addColumn('raisonSociale', 'Raison Sociale', { width: 110})
      .addColumn('*', 'Ville / Localité', { width: 100, transform: (value) => {
        return value?.ville || value?.localite;
      }})
      .addColumn('telephone', 'Téléphone', { width: 100 })
      .addColumn('createdAt', 'Date Soumission', { width: 100, transform: (value) => {
        return this.datePipe.transform(value, 'dd/MM/yyyy') || '--/--/----';
      }})
      .addColumn('dateTraitement', 'Date Traitement', { width: 100, transform: (value) => {
        return this.datePipe.transform(value, 'dd/MM/yyyy') || '--/--/----';
      }})
      .addColumn('typeSuggestion', 'Type', { width: 60, transform: (value) => {
        return (value === 'C') ? 'Création': 'Modification';
      }})
      .addColumn('etatSuggestion', 'Statut', { width: 60, transform: (value) => {
        switch(value) {
          case 'T':
            return 'En Attente';
          case 'V':
            return 'Validée'
          case 'R':
            return 'Rejetée';
          case 'A':
          return 'Annulé';
          default:
            return value;
        }
      }})
      .setData([]);

  }

  initFilterGroup(): void {
    this.filterForm = this.fb.group({
      raisonSociale: new FormControl(null),
      ville: new FormControl(null),
      localite: new FormControl(null),
      etatSuggestion: new FormControl('T'),
      createdAt: new FormControl(null),
      dateTraitement: new FormControl(null),
    });
  }

  listenToSearchFilterChanges(): void {
    this.filterList.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(200),
        distinctUntilChanged()
      )
      .subscribe((res: string) => {
        if (res?.length) {
          this.searchCriteria['raisonSociale'] = res;
          this.searchSuggestionsFicheClient();
        } else {
          this.searchCriteria.raisonSociale = res ? res : null;
          this.searchSuggestionsFicheClient();
        }
      });
  }

  appliquerFiltre(event: Event): void {
    event.preventDefault();
    if (this.filterForm.valid) {
      const formValue = this.filterForm.getRawValue();

      const cleanFormValue = Object.fromEntries(Object.entries(formValue).filter(([_, v]) => v != null));

      this.navigation.skip = 0;
      this.searchCriteria = new SuggestionFicheClientCriteria({
        ...cleanFormValue,
        villes: cleanFormValue['ville'] ? [cleanFormValue['ville'] as string] : null,
        etatSuggestion: cleanFormValue['etatSuggestion'] ? [cleanFormValue['etatSuggestion'] as string] : null,
      });

      this.searchSuggestionsFicheClient();
    } else {
      console.error('Filter form is invalid');
    }
  }

  vider(): void {
    this.navigation.skip = 0;
    this.searchCriteria = new SuggestionFicheClientCriteria();

    this.filterForm.reset();

    this.searchSuggestionsFicheClient();
  }

  get filterField() {
    return this.filterForm.controls;
  }

  /* -------------------------------------------------------------------------- */
  /*                          calc page for pagination                          */
  /* -------------------------------------------------------------------------- */
  getPageNamuber(skip: number, pageSize: number) {
    return Math.floor(skip / pageSize);
  }

  /* -------------------------------------------------------------------------- */
  /*                              pagination event                              */
  /* -------------------------------------------------------------------------- */
  pageChange(event: PageChangeEvent): void {
    if (
      event.skip !== this.navigation.skip ||
      event.take !== this.navigation.pageSize
    ) {
      this.navigation.skip = event.skip;
      this.navigation.pageSize = event.take;

      this.searchSuggestionsFicheClient();
    }
  }
  /* -------------------------------------------------------------------------- */
  /*                   check input ville valid or not on blure                  */
  /* -------------------------------------------------------------------------- */

  pharmacieGridSort(sort: SortDescriptor[]) {
    this.pharmacieSort = sort;
    if (
      this.pharmacieSort &&
      this.pharmacieSort.length > 0 &&
      this.pharmacieSort[0].dir
    ) {
      this.navigation.sortField = sort[0].field;
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
    }
    this.searchSuggestionsFicheClient();
  }

  suggererPharmacie(): void {
    this.router.navigateByUrl('/achats-groupes/pharmacies/suggerer');
  }


  consulterSuggestion(id: string, readOnly = true): void {
    this.router.navigate(['..', 'edit', id], {
      relativeTo: this.route,
      queryParams: { readOnly },
    });
  }

  onRowClick(event: SelectionEvent): void {
    const id = event.selectedRows[0].dataItem.id;
    this.consulterSuggestion(id, true);
  }
  formatter = (result: any) => {
    if (result.libelle) {
      return result.libelle.toUpperCase();
    }
  };

  onModifyClick(event: Event, dataItem: any): void {
    event.stopPropagation();
    this.router.navigate(['..', 'edit', dataItem.id], {
      relativeTo: this.route,
      queryParams: { readOnly: false }
    });
  }
  onAnnulerClick(event: Event, dataItem: any): void {
    event.stopPropagation();
    if (!dataItem.statutEntreprise) {
      this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir annuler la suggestion pour: <b>${dataItem?.raisonSociale ?? ''}</b> ?`)
        .then(
          () => {
            this.fedSyndicatService.annulerSuggestion(dataItem.id).subscribe(
              () => {
                this.alertService.successAlt(`La suggestion pour "${dataItem?.raisonSociale}" a été annulée avec succès!`, undefined, 'MODAL');
                this.searchSuggestionsFicheClient();
              },
              (error) => {
                console.error('Error deleting suggestion', error);
              }
            );
          },
          () => null
        );
    }
  }
}
