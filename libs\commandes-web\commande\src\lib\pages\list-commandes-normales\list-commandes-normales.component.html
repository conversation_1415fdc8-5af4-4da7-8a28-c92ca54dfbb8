<!-- Start Of Header -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-5 d-md-block d-none">Liste Commandes Normales</h4>

        <div class="col-md-7 col-12 px-1 py-2 py-md-0">
            <div class="row justify-content-end align-items-center">
                <button (click)="getTodaysCommandes()" type="button"
                    class="btn btn-sm btn-light text-dark m-1 d-flex row" title="Jour">
                    <i class="mdi mdi-calendar-today mr-1"></i>
                    <span>Jour</span>
                </button>

                <button (click)="getYesterdaysCommandes()" type="button"
                    class="btn btn-sm btn-light text-dark m-1 d-flex row" title="Jour - 1">
                    <i class="mdi mdi-calendar mr-1"></i>
                    <span>Jour - 1</span>
                </button>

                <button type="button" class="btn btn-sm btn-info m-1 ml-2" (click)="openFilterModal(commandeFilter)">
                    <i class="mdi mdi-filter-variant"></i>
                    Filtrer
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="card">
    <kendo-grid [selectable]="false" [pageable]="{
        buttonCount: 5,
        info: true,
        type: 'numeric',
        pageSizes: pageSizes,
        previousNext: true,
        position: 'bottom'
      }" [resizable]="true" [data]="gridData" [sort]="gridSort" [sortable]="{mode: 'single'}"
        [pageSize]="navigation.pageSize" [skip]="navigation.skip" (sortChange)="gridSortChange($event)"
        (pageChange)="pageChange($event)" style="min-height: calc(100vh - 123px);">

        <kendo-grid-column media="(max-width: 768px)" title="Liste Commandes Normales">
            <ng-template kendoGridCellTemplate let-dataItem>
                <!---  Mobile Column Template  --->
                <dl>
                    <dt class="my-2 limited-width">N° Commande: <span>{{ dataItem?.codeCommande }}</span></dt>

                    <dt class="my-2 limited-width">Client: <span>{{ dataItem?.userCreateur?.entrepriseDTO?.raisonSociale
                            }}</span></dt>

                    <dt class="my-2 limited-width">Ville Client: <span>{{ dataItem?.ville }}</span></dt>

                    <dt class="my-2 limited-width">Origine Commande: <span>{{ dataItem?.origineCommande | origineCmd
                            }}</span></dt>

                    <dt class="my-2 limited-width">Date Enregistrement:
                        <span>{{ dataItem?.dateValidation | momentTimezone: "yyyy-MM-DD HH:mm": "Africa/Casablanca"
                            }}</span>
                    </dt>

                    <dt class="my-2 limited-width">Date En Prép:
                        <span>{{ dataItem?.dateTraitementBl | momentTimezone: "yyyy-MM-DD HH:mm": "Africa/Casablanca"
                            }}</span>
                    </dt>

                    <dt class="my-2 limited-width">Date En Expédition:
                        <span>{{ dataItem?.bl?.dateExpedition | momentTimezone: "yyyy-MM-DD HH:mm": "Africa/Casablanca"
                            }}</span>
                    </dt>

                    <dt class="my-2 limited-width">Montant Total Brut: <span>{{ (dataItem?.bl?.montantTotalBrut ??
                            dataItem?.valeurCmdBruteTtc)| number:
                            "1.2-2":"fr-FR" }}</span></dt>

                    <dt class="my-2 mx-0 d-flex row align-items-center limited-width">Statut: <div
                            class="ml-1 badge-info px-2 py-1 badge rounded-pill"
                            *ngIf="((dataItem?.statut === 'VALIDE' || dataItem?.statut === 'ENREGISTREE') && !dataItem?.bl?.statutTraiteBl)">
                            {{ 'EN' | statutBl }}
                        </div>

                        <div class="ml-1 badge-info px-2 py-1 badge rounded-pill"
                            *ngIf="(dataItem?.statut === 'TRANSMISE' && dataItem?.dateTraitementBl && !dataItem?.bl?.statutTraiteBl)">
                            {{ 'TR' | statutBl }}
                        </div>

                        <div class="ml-1 badge-warning p-1 badge rounded-pill"
                            *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'PR'">
                            {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                        </div>

                        <div class="ml-1 badge-primary px-2 py-1 badge rounded-pill"
                            *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'EX'">
                            {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                        </div>

                        <div class="ml-1 badge-success px-2 py-1 badge rounded-pill"
                            *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'TR'">
                            {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                        </div>

                        <div class="ml-1 badge-danger px-2 py-1 badge rounded-pill"
                            *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'CS'">
                            {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                        </div>
                    </dt>

                    <dt class="action-btns">
                        <div class="d-flex row mx-0 justify-content-start">
                            <button class="circle circle-alt btn btn-warning text-white"
                                (click)="consulterCommande(dataItem?.idhash)" title="Consulter Commande">
                                <i class="mdi mdi-eye"></i>
                            </button>

                            <button *ngIf="!!dataItem?.bl" class="circle circle-alt btn btn-info text-white mt-2"
                                title="Consulter BL" (click)="consulterBl(dataItem)">
                                <i class="mdi mdi-clipboard-text"></i>
                            </button>

                            <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']">
                                <button *ngIf="!dataItem?.bl && dataItem?.statut === 'TRANSMISE'"
                                    class="circle circle-alt btn btn-success text-white mt-2" title="Créer BL"
                                    (click)="createBlFromCommande(dataItem?.idhash)">
                                    <i class="bi bi-bag-plus-fill"></i>
                                </button>
                            </ng-container>

                        </div>
                    </dt>

                </dl>

            </ng-template>

        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="numCommande" title="N°" [width]="110">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="text-left">
                    {{ dataItem?.codeCommande }}
                </div>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="userCreateur" title="Client" [width]="180"
            class="text-left text-wrap">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.userCreateur?.entrepriseDTO?.raisonSociale }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="ville" [width]="180" [sortable]="false"
            class="text-left text-wrap">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Ville Client</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.ville }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [width]="180" [sortable]="false">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Origine Commande</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.origineCommande | origineCmd }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="dateValidation" [width]="180">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Date Enregistrement</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="text-left">
                    {{ dataItem?.dateValidation | momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}
                </div>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="dateTraitementBl" [width]="170">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Date En Prép</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="text-left">
                    {{ dataItem?.dateTraitementBl | momentTimezone: "yyyy-MM-DD HH:mm": "Africa/Casablanca" }}
                </div>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [width]="170">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap">Date En Expédition</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="text-left">
                    {{ dataItem?.bl?.dateExpedition | momentTimezone: "yyyy-MM-DD HH:mm": "Africa/Casablanca" }}
                </div>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [sortable]="false" [width]="190">
            <ng-template kendoGridHeaderTemplate>
                <span class="text-wrap text-right">Montant Total Brut</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="text-right">
                    {{ (dataItem?.bl?.montantTotalBrut ?? dataItem?.valeurCmdBruteTtc) | number: "1.2-2":"fr-FR" }}
                </div>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="libelleLabo" class="text-center no-ellipsis"
            [sortable]="false" [width]="180" filter="numeric">
            <ng-template kendoGridHeaderTemplate>
                <span class="d-flex justify-content-center w-100">Statut</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <div class="badge-info px-2 py-1 badge rounded-pill w-100"
                    *ngIf="((dataItem?.statut === 'VALIDE' || dataItem?.statut === 'ENREGISTREE') && !dataItem?.bl?.statutTraiteBl)">
                    {{ 'EN' | statutBl }}
                </div>

                <div class="badge-success px-2 py-1 badge rounded-pill w-100"
                    *ngIf="(dataItem?.statut === 'TRANSMISE') && !dataItem?.bl?.statutTraiteBl">
                    {{ 'TR' | statutBl }}
                </div>

                <div class="badge-warning p-1 badge rounded-pill w-100"
                    *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'PR'">
                    {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                </div>

                <div class="badge-primary px-2 py-1 badge rounded-pill w-100"
                    *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'EX'">
                    {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                </div>

                <div class="badge-success px-2 py-1 badge rounded-pill w-100"
                    *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'TR'">
                    {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                </div>

                <div class="badge-danger px-2 py-1 badge rounded-pill w-100"
                    *ngIf="dataItem?.bl && dataItem?.bl?.statutTraiteBl === 'CS'">
                    {{ dataItem?.bl?.statutTraiteBl | statutBl }}
                </div>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" [sortable]="false" [width]="130" class="text-center no-ellipsis">
            <ng-template kendoGridHeaderTemplate>
                <span class="d-flex justify-content-center w-100">Action</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
                <button class="circle btn btn-warning text-white" (click)="consulterCommande(dataItem?.idhash)"
                    title="Consulter Commande">
                    <i class="mdi mdi-eye"></i>
                </button>

                <button *ngIf="!!dataItem?.bl" class="circle btn btn-info text-white ml-1" title="Consulter BL"
                    (click)="consulterBl(dataItem)">
                    <i class="mdi mdi-clipboard-text"></i>
                </button>

                <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']">
                    <button *ngIf="!dataItem?.bl && dataItem?.statut === 'TRANSMISE'"
                        class="circle btn btn-success text-white ml-1" title="Créer BL"
                        (click)="createBlFromCommande(dataItem?.idhash)">
                        <i class="bi bi-bag-plus-fill"></i>
                    </button>
                </ng-container>

            </ng-template>
        </kendo-grid-column>

        <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
            pagerItemsPerPage="éléments par page"></kendo-grid-messages>

        <ng-template kendoGridNoRecordsTemplate>
            <span *ngIf="!isDateRangeApplied" id="no-records"
                class="d-flex row align-items-center justify-content-center p-2">
                <i class="mdi mdi-information mr-1 my-0"></i>
                <span class="no-records-alt">Veuillez sélectionner la date des commandes à consulter: soit aujourd'hui
                    ou hier </span>
            </span>

            <span *ngIf="isDateRangeApplied && !gridData?.total">Aucun résultat trouvé.</span>
        </ng-template>
    </kendo-grid>
</div>


<!-- Filter Modal Start -->
<ng-template #commandeFilter let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Filtrer</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>
    <form [formGroup]="filterForm" (ngSubmit)="getListeCommandesNormales(true); modal.dismiss()" wphFocusTrap>
        <div class="p-2">
            <div class="form-group row">
                <div class="col-12">
                    <label for="search-client" class="col-form-label pl-0 col-12 text-left">Client</label>
                    <div id="client-picker-input" class="input-group picker-input">
                        <input type="text" class="form-control form-control-md" id="search-client"
                            formControlName="fournisseur" style="padding-left: 30px !important;"
                            [ngbTypeahead]="searchSociete" [resultTemplate]="clientSearchTemplate"
                            [inputFormatter]="societeFormatter" [resultFormatter]="societeFormatter">

                        <ng-template #clientSearchTemplate let-result="result">
                            <div>
                                <span class="badge badge-info mr-2">{{result?.code}}</span>
                                <ngb-highlight [result]="result?.raisonSociale"></ngb-highlight>
                            </div>
                            <span class="badge badge-dark ml-2 pt-1">{{result?.ville}}</span>
                        </ng-template>

                        <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 mx-0 px-0">
                    <label for="dateDebut" class="col-12 col-form-label text-left">Date Enregistrement Début</label>
                    <div class="col-12 input-group">
                        <input type="text" [readOnly]="true" name="dateValidationDebut"
                            class="form-control form-control-md bg-white" id="dateDebut" ngbDatepicker
                            #drange1="ngbDatepicker" (click)="drange1.toggle()" formControlName="dateCommandeDu">

                        <div class="input-group-append">
                            <button type="button" (click)="drange1.toggle()" tabindex="-1"
                                class="btn btn-md btn-light text-dark btn-outline-light calendar">
                                <i class="mdi mdi-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-12 mx-0 px-0 mt-2">
                    <label for="dateFin" class="col-12 col-form-label text-left">Date Enregistrement Fin</label>
                    <div class="col-12 input-group">
                        <input type="text" [readOnly]="true" name="dateValidationFin"
                            class="form-control form-control-md bg-white" id="dateFin" ngbDatepicker
                            #drange2="ngbDatepicker" (click)="drange2.toggle()" formControlName="dateCommandeAu">

                        <div class="input-group-append">
                            <button type="button" (click)="drange2.toggle()" tabindex="-1"
                                class="btn btn-md btn-light text-dark btn-outline-light calendar">
                                <i class="mdi mdi-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-12 mx-0 px-0 mt-2 mb-4">
                    <label class="col-form-label col-12 p-0 ml-2" style="margin-bottom: 4px;">Statut</label>

                    <div class="input-group picker-input">
                        <select2 formControlName="statutTraiteBl" hideSelectedItems="true" class="form-control-sm w-100"
                            multiple="true" [data]="listStatut"></select2>
                    </div>
                </div>

                <div class="col-12 d-flex row align-items-center my-3 pl-3">
                    <div class="custom-control custom-switch">
                        <input type="checkbox" formControlName="nonEnvoyes" class="custom-control-input"
                            id="nonEnvoyeeUniquement">
                        <label class="custom-control-label" for="nonEnvoyeeUniquement">Non Envoyées Uniquement</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light"
                tabindex="-1">Fermer</button>
            <button type="button" class="btn btn-secondary text-white" (click)="vider(); modal.dismiss()"
                tabindex="-1">Vider</button>
            <button type="button" type="submit" class="btn btn-primary ml-1 text-white"
                tabindex="-1">Rechercher</button>
        </div>
    </form>
</ng-template>