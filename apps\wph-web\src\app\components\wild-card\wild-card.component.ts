import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { PlateformeService } from "@wph/shared";

@Component({
    standalone: true,
    selector: 'wph-wild-card',
    template: ``
})
export class WildCardComponent implements OnInit {
    constructor(
        private router: Router,
        private plateformeService: PlateformeService
    ) { }

    ngOnInit(): void {
        const currentPlateforme = this.plateformeService.getCurrentPlateforme();
        
        switch (currentPlateforme) {
            case 'WIN_OFFRE':
                this.router.navigate(['win-offre', 'accueil']);
                break;
            case 'COMMANDE_WEB':
                this.router.navigate(['commande-web', 'accueil']);
                break;
            case 'WIN_GROUPE':
            case 'FEDERATION_SYNDICAT':
                this.router.navigate(['achats-groupes', 'accueil']);
                break;
            case 'DEFAULT':
            default:
                this.router.navigate(['pharma-lien']);
                break;
        }
    }
}