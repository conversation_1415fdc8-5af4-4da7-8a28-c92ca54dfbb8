<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>{{'test'}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
<!--    <ion-toolbar>-->
<!--      <ion-title size="large">Blank</ion-title>-->
<!--    </ion-toolbar>-->
  </ion-header>

  <div id="container">
    <h1>Welcome wph-mobile!</h1>
    <strong>Ready to create an app?</strong>
    <p>
      Start with Ionic
      <a
        target="_blank"
        rel="noopener noreferrer"
        href="https://ionicframework.com/docs/components"
        >UI Components</a
      >
    </p>
  </div>
</ion-content>
