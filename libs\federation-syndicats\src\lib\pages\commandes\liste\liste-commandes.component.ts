import { DatePipe } from '@angular/common';
import {
  <PERSON>mponent,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import {
  CellClickEvent,
  GridDataResult,
  PageChangeEvent,
  SelectionEvent,
} from '@progress/kendo-angular-grid';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AuthService } from '@wph/core/auth';
import {
  Commande,
  Fournisseur,
  Offre,
  OffresService,
  Pagination,
} from '@wph/data-access';
import {
  FederationSyndicatService,
  FsCommandeCriteria,
  FsCommandesService,
  GroupeEntreprise,
  PharmacieEntreprise,
} from '@wph/federation-syndicats';
import { AlertService, SocieteType } from '@wph/shared';
import {
  ExportPdf,
  ExportPdfService,
  UserInputService,
  getDynamicPageSize,
} from '@wph/web/shared';
import {
  Observable,
  Subject,
  catchError,
  debounceTime,
  distinctUntilChanged,
  map,
  of,
  switchMap,
  takeUntil,
} from 'rxjs';
import { Avis, AvisDTO, TypeAvis } from '../../../models/avis.model';

@Component({
  selector: 'wph-ag-liste-commandes',
  templateUrl: './liste-commandes.component.html',
  styleUrls: ['./liste-commandes.component.scss'],
})
export class ListeCommandesComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<boolean> = new Subject<boolean>();

  gridData: GridDataResult;
  displayFilter: boolean;

  searchCriteria: FsCommandeCriteria = new FsCommandeCriteria();

  filterForm: FormGroup;
  searchFilter: FormControl = new FormControl();
  startsWith: RegExp = new RegExp('^CD-\\d*$', 'i');

  exportPdfRef: ExportPdf;
  commandeSort: SortDescriptor[];
  pageSizes: number[] = [5, 10, 15, 20];
  navigation: Pagination & { originalSortField: string } = {
    pageSize: 15,
    skip: 0,
    originalSortField: '',
  };

  monGroupe: GroupeEntreprise;
  isResponsable: boolean
  selectedFournisseurs: Fournisseur[];

  stautsLabelsValues: any[] = [
    { label: 'Tout', value: null },
    { label: 'Annulée', value: 'ANNULEE' },
    { label: 'Brouillon', value: 'BROUILLON' },
    { label: 'Cloturée', value: 'CLOTUREE' },
    { label: 'Envoyée', value: 'ENVOYEE' },
    { label: 'En Livraison', value: 'EN_LIVRAISON' },
    { label: 'Livrée', value: 'LIVREE' },
    { label: 'Transmise', value: 'TRANSMIS' },
    { label: 'Validée', value: 'VALIDEE' },
  ];

  selectedItem: Commande;
  titreOffre: string;
  laboratoire: any;
  modalRef: NgbModalRef;

  @ViewChild('satisfactionModal') satisfactionModal: TemplateRef<any>;
  avis: Avis;
  feedbackSent: boolean = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private datePipe: DatePipe,
    private modalService: NgbModal,
    private authService: AuthService,
    private alertService: AlertService,
    private offresService: OffresService,
    private exportPdfServ: ExportPdfService,
    private userInputServ: UserInputService,
    private commandeService: FsCommandesService,
    private fedSyndicatService: FederationSyndicatService
  ) {
    this.initFilterForm();
  }

  get isInactive$() {
    return this.fedSyndicatService.inactiveAccount$;
  }

  ngOnInit(): void {
    this.buildExport();
    this.listenToSearchFilterChanges();

    this.isResponsable = this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']);

    this.fedSyndicatService.getMyGroupe().then((myGroupe) => {
      this.monGroupe = myGroupe;
      
      this.fetchCommandeUnitaireGroupe();
    });

  }

  fetchCommandeUnitaireGroupe(): void {
    const statutSelectionne = this.route.snapshot.queryParams['statut'];

    if (statutSelectionne) {
      this.filterForm.get('statut').setValue(statutSelectionne);
    }
    
    this.searchCriteria = new FsCommandeCriteria({
      client: this.authService.getPrincipal()?.societe,
      etatCommande: statutSelectionne ? [statutSelectionne] : null,
    });

    this.searchCommmandesUnitaire();
  }

  setPageSize(currentHeight?: number): void {
    const dynamicSize = getDynamicPageSize(currentHeight, 44);

    if (dynamicSize !== this.navigation.pageSize) {
      this.navigation.pageSize = dynamicSize;

      this.pageSizes.push(dynamicSize);
      this.pageSizes = this.pageSizes.sort((a, b) => a - b);

      this.searchCommmandesUnitaire();
    }
  }

  searchCommmandesUnitaire(): void {
    this.commandeService
      .searchCommandesUnitaires(this.navigation, this.searchCriteria)
      .subscribe((res) => {
        this.gridData = {
          data: res?.content,
          total: res?.totalElements,
        };

        this.exportPdfRef.setData(res.content);
      });
  }

  initFilterForm(): void {
    this.filterForm = this.fb.group({
      offreur: [null],
      distributeur: [null],
      statut: [null],
      dateDebut: [null],
      dateFin: [null],
    });
  }

  listenToSearchFilterChanges(): void {
    this.searchFilter.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(200),
        distinctUntilChanged()
      )
      .subscribe((term: string) => {
        const { codeCommande, titreOffre, ...criteria } = this.searchCriteria;
        const criteriaKey = this.startsWith.test(term) ? 'codeCommande' : 'titreOffre';

        this.searchCriteria = new FsCommandeCriteria({
          ...criteria,
          [criteriaKey]: term,
        });

        this.searchCommmandesUnitaire();
      });
  }

  sortChange(sort: SortDescriptor[]): void {
    this.commandeSort = sort;

    if (
      this.commandeSort &&
      this.commandeSort.length > 0 &&
      this.commandeSort[0].dir
    ) {

      let sortableField = '';
      if (['offreur.raisonSociale', 'titre'].includes(sort[0].field)) {
        sortableField = 'offre.' + sort[0].field;
      } else if (
        sort[0].field?.endsWith('Cmd') &&
        sort[0].field !== 'totalValeurTtcNetteCmd'
      ) {
        sortableField = sort[0].field.replace('Cmd', '');
      } else if (sort[0].field === 'totalValeurTtcNetteCmd') {
        sortableField = 'valeurCmdNetTtc';
      } else {
        sortableField = sort[0].field;
      }

      this.navigation.originalSortField = sort[0].field;
      this.navigation.sortField = sortableField;
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
      this.navigation.originalSortField = '';
    }

    this.searchCommmandesUnitaire();
  }

  OnPageChange(event: number): void {
    this.searchCommmandesUnitaire();
  }

  pageChange(event: PageChangeEvent): void {
    if (
      event.skip !== this.navigation.skip ||
      event.take !== this.navigation.pageSize
    ) {
      this.navigation.skip = event.skip;
      this.navigation.pageSize = event.take;

      this.searchCommmandesUnitaire();
    }
  }

  selectionChange(event: SelectionEvent): void {
    const selectedRows = event.selectedRows?.filter(
      (row) => row?.dataItem?.statut === 'B'
    );
    const deselectedRows = event.deselectedRows?.filter(
      (row) => row?.dataItem?.statut === 'B'
    );

    if (selectedRows?.length) {
      this.selectedItem = selectedRows[0]?.dataItem;
    }

    if (deselectedRows) {
      this.selectedItem = null;
    }
  }

  cellClickHandler(event: CellClickEvent): void {
    if (event?.column.title !== 'Action') {
      this.consulterCommande(event?.dataItem, true);
    }
  }

  consulterCommande(item: Offre | Commande, readOnly = false): void {
    this.router.navigate(
      [`/achats-groupes/commandes/edit/cmd-unitaire`, item?.enteteCommandeId],
      { 
        state: { incr: true },
        queryParams: { readOnly, offreId: item?.id } 
      }
    );
  }

  buildExport(): void {
    this.exportPdfRef = this.exportPdfServ
      .ref<any>()
      .setTitle('Liste des Commandes Unitaires')
      .addColumn('codeCommande', 'Code Commande', { width: 80 })
      .addColumn('titre', "Titre de l'offre", { width: 200 })
      .addColumn('*', 'Offreur', {
        width: 100,
        transform: (value) => {
          return value?.offreur?.raisonSociale;
        },
      })
      .addColumn('*', 'Distributeur', {
        width: 100,
        transform: (value) => {
          return value?.distributeur?.raisonSociale;
        },
      })
      .addColumn('dateCreationCmd', 'Date Création', {
        width: 80,
        transform: (value) => {
          return this.datePipe.transform(value, 'dd/MM/yyyy') || '--/--/----';
        },
      })
      .addColumn('totalValeurTtcBruteCmd', 'Montant Brut (DH)', {
        width: 80,
        type: 'decimal',
      })
      .addColumn('etatCommandeAchatGroupe', 'Statut', { width: 80 })
      .setData([]);
  }

  initializeAvis(item: Offre) {
    this.avis = {
      commentaire: null,
      estResponsable: this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']),
      id: null,
      typeAvis: TypeAvis.Positive,
      entCmdUnitaireMarcheId: item.enteteCommandeId ?? null,
      enteteCommandeConsolideeMarche: null,
      groupeEntreprise: this.authService.getPrincipal()?.groupe ?? null,
      sondeurEntreprise: {
        id: this.authService.getPrincipal()?.societe?.id,
      } as PharmacieEntreprise,
      raison: null,
      laboratoire: 0,
      livraison: 0,
      paiement: 0,
      qualite: 0,
      reduction: 0,
    };
    this.titreOffre = item.titre;
    this.laboratoire = item.laboratoire.raisonSociale;
  }

  checkIfAvisExists(OffreId?: number): Observable<boolean> {
    const sondeurId = this.authService.getPrincipal()?.societe?.id;
    if (!sondeurId) {
      console.error('sondeurId is null');
      return of(false);
    }

    const cmdConsolideeId = null;
    const offreId = OffreId ?? null;

    return this.commandeService
      .getBySondeurIdOffreIdCmdId(sondeurId, offreId, cmdConsolideeId)
      .pipe(
        map((avis: AvisDTO | AvisDTO[]) => {
          // Check if the response is an array
          if (Array.isArray(avis)) {
            return avis.length > 0;
          }
          // Check if the response is a single object
          return avis !== null;
        }),
        catchError((error) => {
          console.error('Error fetching avis:', error);
          return of(false);
        })
      );
  }

  openSatisfactionModal(item: Offre) {
    if (this.feedbackSent) {
      this.alertService.error(
        `Un avis a déjà été envoyé pour cette commande.`,
        'MODAL'
      );
      return;
    }

    this.initializeAvis(item);
    this.modalService.open(this.satisfactionModal, { centered: true });
  }

  soumettreSatisfactionModal(): void {
    if (this.feedbackSent) {
      this.alertService.error(
        `Un avis a déjà été envoyé pour cette commande.`,
        'MODAL'
      );
      return;
    }

    this.userInputServ
      .confirmAlt(
        'Confirmation',
        `Êtes vous sûr de vouloir envoyer cet avis ?`
      )
      .then(
        () => {
          this.commandeService.sauvegarderAvis(this.avis).subscribe(
            () => {
              this.feedbackSent = true;
              this.alertService.successAlt(
                `Votre avis a été envoyé avec succès.`,
                'Avis envoyé',
                'MODAL'
              );
            },
            (error) => {
              const errorMessage =
                error?.error?.message ||
                "Erreur lors de l'enregistrement de l'avis.";
              this.alertService.error(errorMessage, 'MODAL');
              console.error('Error saving avis:', error);
            }
          );
          this.modalService.dismissAll();
        },
        () => null
      );
  }

  transmettreCommande(item: Offre): void {
    if (item?.etatCommandeAchatGroupe === 'BROUILLON') {
      this.userInputServ
        .confirmAlt('Confirmation', `Êtes vous sûr de vouloir transmettre la commande sur l'offre: <b>${item?.titre}</b> ?`)
        .then(
          () => {
            this.commandeService
              .transmettreCommandeUnitaire(
                item?.enteteCommandeId,
                this.monGroupe?.id
              )
              .subscribe((res) => {
                this.navigation.skip = 0;
                this.searchCommmandesUnitaire();
                this.alertService.successAlt(
                  `La commande sur l'offre: <b>${item?.titre}</b> a été transmise avec succès!`,
                  'Commande Transmise',
                  'MODAL'
                );

                setTimeout(() => {
                  if (this.modalService.hasOpenModals()) {
                    this.modalService.dismissAll();
                  }
                  this.checkIfAvisExists(item.id).subscribe((avisExists) => {
                    if (avisExists || this.isResponsable) {
                      return;
                    } else {
                      this.openSatisfactionModal(item);
                    }
                  });
                }, 3500);
              });
          },
          () => null
        );
    }
  }

  annulerCommande(item: Offre): void {
    this.userInputServ
      .confirmAlt('Confirmation', `Êtes vous sûr de vouloir annuler la commande sur l'offre: <b>${item?.titre}</b> ?`)
      .then(
        () => {
          this.commandeService
            .annulerCommandeUnitaire(item?.enteteCommandeId, this.monGroupe?.id)
            .subscribe((res) => {
              this.navigation.skip = 0;
              this.searchCommmandesUnitaire();

              this.alertService.successAlt(
                `La commande sur l'offre: <b>${item?.titre}</b> a été annulée avec succès!`,
                'Commande Annulée',
                'MODAL'
              );
            });
        },
        () => null
      );
  }

  filterList(searchQuery: string) {
    return this.offresService.searchSociete({
      raisonSociale: searchQuery,
      typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE],
    });
  }

  searchFournisseur = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap((term) => {
        if (term.length > 1) {
          return this.filterList(term.toLowerCase());
        }
        return of({ content: [] });
      }),
      map((res) => res?.content.slice(0, 5))
    );

  fournisseurFormatter = (result: { raisonSociale: any }) =>
    result ? result.raisonSociale : null;

  laboFormatter = (result: { raisonSociale: any }) =>
    result
      ? result.raisonSociale === 'DIVERS'
        ? null
        : result.raisonSociale
      : null;

  appliquerFiltre(): void {
    const payload = this.filterForm?.getRawValue();

    this.searchCriteria = new FsCommandeCriteria({
      ...this.searchCriteria,
      etatCommande: payload?.statut ? [payload?.statut] : null,
      offreurId: payload?.offreur?.id,
      distributeurId: payload?.distributeur?.id,
      dateCreationDebut: payload?.dateDebut,
      dateCreationFin: payload?.dateFin,
    });

    this.navigation.skip = 0;

    this.searchCommmandesUnitaire();
  }

  viderFiltre(): void {
    this.filterForm.reset();

    this.navigation.skip = 0;
    this.searchCriteria = new FsCommandeCriteria({
      client: this.authService.getPrincipal()?.societe,
    });

    this.searchCommmandesUnitaire();
  }

  openDistributeurs(fournisseurs: Fournisseur[], content) {
    this.selectedFournisseurs = fournisseurs;
    this.modalService
      .open(content, {
        ariaLabelledBy: 'modal-basic-title',
        windowClass: 'fs-cstm-modal',
      })
      .result.then(
        (result) => {
          console.log(`Closed with: ${result}`);
        },
        (reason) => {
          console.log(`Dismissed ${reason}`);
        }
      );
  }

  reload() {
    this.fetchCommandeUnitaireGroupe();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
