import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { FilterOutput, Fournisseur, OffresService } from "@wph/data-access";
import { SocieteType } from "@wph/shared";
import { Select2 } from "ng-select2-component";
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map } from "rxjs";

@Component({
    selector: 'wph-commandes-filter',
    styleUrls: ['./commandes-filter-modal.component.scss'],
    templateUrl: './commandes-filter-modal.component.html'
})
export class CommandesFilterModalComponent implements OnInit {
    filterForm: FormGroup;
    startsWith: RegExp = new RegExp('^(\\d+|[gG][0-9]*)$');

    @Input() set forceCloseAllDropdowns(value: boolean) {
        this._forceCloseAllDropdowns = value;
        if (value) {
            this.closeOpenSelect2Dropdowns();
        }
    }
    get forceCloseAllDropdowns(): boolean {
        return this._forceCloseAllDropdowns;
    }

    _forceCloseAllDropdowns: boolean = false;

    listStatuts = [
        { label: 'Annulée', value: 'A' }, //user only
        { label: 'Refusée', value: 'R' },
        { label: 'Acceptée', value: 'AC' },
        { label: 'Confirmée', value: 'N' },
        { label: 'Envoyée', value: 'E' },
        { label: 'Supprimée', value: 'S' },
        { label: 'Brouillon', value: 'B' }, //user only
    ];

    @ViewChild('statutSelect') statutSelect: Select2;
    @ViewChild('nonExpireeSelect') nonExpireeSelect: Select2;
    @Output() modalAction: EventEmitter<FilterOutput> = new EventEmitter<FilterOutput>();
    
    constructor(
        private fb: FormBuilder,
        private modalService: NgbModal,
        private offresService: OffresService,
        private controlContainer: ControlContainer
    ) { }
    
    ngOnInit(): void {
        this.filterForm = this.controlContainer.control as FormGroup<any>;
    }
    
    closeOpenSelect2Dropdowns(except = null): void {
        if (this.statutSelect?.isOpen && this.statutSelect !== except) {
            this.statutSelect.isOpen = false;
        } else if (this.nonExpireeSelect?.isOpen && this.nonExpireeSelect !== except) {
            this.nonExpireeSelect.isOpen = false;
        }
    }

    applyFilter(): void {
        this.applyChanges();
        this.modalAction.emit({ filter: true });
    }

    clearFilters(): void {
        this.applyChanges();
        this.modalAction.emit({ clear: true });
    }

    applyChanges(): void {
        this.controlContainer.control.setValue(this.filterForm.value);

        this.controlContainer.control.markAsDirty();
        this.controlContainer.control.markAsTouched();
    }

    dismiss(): void {
        this.modalService.dismissAll();
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase(), [SocieteType.GROSSISTE, SocieteType.FABRIQUANT])
                }
                return of({ content: [] });
            }),
            map(res => res.content.slice(0, 5))
        );


    filterList(searchQuery: string, societeType: SocieteType[], includeSegmentSociete = false) {
        let criteriaKey: string, criteria: any = {};

        if (includeSegmentSociete) {
            criteria = { segmentEntreprise: 'O' };
        }

        criteriaKey = this.startsWith.test(searchQuery) ? 'code' : 'raisonSociale';

        criteria = { ...criteria, [criteriaKey]: criteriaKey === 'code' ? searchQuery?.toUpperCase() : searchQuery, typeEntreprises: societeType };

        return this.offresService.searchSociete(criteria);
    }

    searchlaboratoire = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase(), [SocieteType.FABRIQUANT, SocieteType.GROSSISTE])
                }
                return of({ content: [] });
            }),
            map(res => res.content.slice(0, 5))
        );

    fournisseurFormatter = (result: { raisonSociale: any }) =>
        result ? result.raisonSociale : null;

    laboFormatter = (result: { raisonSociale: string }) =>
        result
            ? result.raisonSociale === 'DIVERS'
                ? null
                : result.raisonSociale
            : null;

    searchClient = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase(), [SocieteType.CLIENT], true)
                }
                return of({ content: [] });
            }),
            map(res => res.content.slice(0, 5))
        );

    clientFormatter = (result: Fournisseur) => result ? `${result?.code} : ${result?.raisonSociale}` : null;

}