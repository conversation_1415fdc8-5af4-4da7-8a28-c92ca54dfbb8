.btn-switch {
    // font-size: 1rem;
    border-radius: 5px;
    font-weight: 600;

    border-color: #a4a4a4;
    color: #a4a4a4;
    background: #fff;

    &:hover {
        background: #a4a4a4;
        color: #fff;
    }

    &:active,
    &:focus {
        outline: none !important;
    }

    &.active {
        background: var(--win-offre-primary);
        border-color: var(--win-offre-primary);
        color: #fff;

        .emphasis-value {
            color: #fff !important;
        }

        &:hover {
            background: var(--win-offre-primary);
            color: #fff;
        }
    }
}

.form-control {
    color: black;
    font-size: 1rem;
    border-radius: var(--winoffre-base-border-radius);
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    font-weight: 600;
}

.input-group {
    .btn {
        border-top-right-radius: var(--winoffre-base-border-radius);
        border-bottom-right-radius: var(--winoffre-base-border-radius);
    }
}

.btn-left {
    border-top-left-radius: var(--winoffre-base-border-radius);
    border-bottom-left-radius: var(--winoffre-base-border-radius);
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;

    color: #fff;
    background: var(--win-offre-primary);
    outline: none;
    border: none;
}

.btn-right {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-right-radius: var(--winoffre-base-border-radius);

    color: #fff;
    background: var(--win-offre-primary);
    outline: none;
    border: none;
}

.emphasis-value {
    font-size: 1.1rem;
    color:rgb(192, 24, 24);
    font-weight: 800;
}

.dash-container {
    height: calc(100vh - 155px);
}

.date-container {
    padding: 1.8px 10px;
    border: 1px solid var(--win-offre-primary);

    font-size: 1rem;
    color: black;
    font-weight: 600;
    cursor: pointer;
}

.tab-content-container {
    height: calc(100vh - 170px);
    overflow-y: auto;

    scrollbar-width: thin !important;
    scrollbar-color: var(--win-offre-primary) white !important;
}

.error-highlight {
    cursor: pointer;
    padding: 2px 6px;
    font-weight: 800;
    border-radius: 5px;
    color: rgb(192, 24, 24) !important;
    background: rgba(229, 31, 31, 0.4) !important;
}

.warning-highlight {
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 5px;
    font-weight: 800;
    color: rgb(181, 118, 0);
    background: rgba(240, 161, 15, 0.3);
}

.balance-padding {
    text-align: center;
    width: 50px;
    padding: 2px 6px;
}

.btn-semaine {
    background: rgba(43, 152, 184, .2);
}

.sep {
    margin: 0 3px;
}

::ng-deep .k-grid-footer {
    padding: 0 !important;
}

::ng-deep .k-touch-action-auto.text-center {
    padding: 2px 4px !important;
}

::ng-deep .k-touch-action-auto.text-wrap {
    padding: 2px 4px !important;
}

::ng-deep .k-touch-action-auto.text-wrap.text-right {
    padding: 2px 4px !important;
}

@media(max-width: 699px) {
    .grid-title {
        display: none;
    }
}

.dash-item-container {
    height: auto;
}

@media (min-width: 700px) and (min-height: 900px) {
    .dash-item-container {
        min-height: calc(50vh - 98px) !important;
        max-height: calc(50vh - 98px) !important;
    }
}

@media (max-width: 699px) and (min-height: 650px) {
    .dash-item-container {
        min-height: calc(100vh - 273px) !important;
    }
}