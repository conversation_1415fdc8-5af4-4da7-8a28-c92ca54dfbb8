import { Pagination } from "./PaginationDTO.ts";

export class EnteteFacture {
    id?: number;
    codeSite?: number;
    dateMaj?: string;
    dateFacture?: string;
    alternativeDatefac?: string;
    dateEcheance?: string;
    codeClientSite?: string;
    typeCommande?: string;
    codeRegion?: string;
    codeFacture?: number;
    montantBrutTtc?: number;
    montantRemise?: number;
    montantTva?: number;
    tauxRemise?: number;
    lignes?: DetailFacture[];
}

export class DetailFacture {
    id?: number;
    codeSite?: number;
    dateMaj?: string;
    dateFacture?: string;
    codeFacture?: number;
    codeFamilleTarifaire?: string;
    libelleFamilleTarifaire?: string;
    montantBrutTtc?: number;
    montantRemise?: number;
    montantTva?: number;
}

export class FactureCriteria {
    dateFactureDebut?: string;
    dateFactureFin?: string;
    codeClientSite?: string;
    codeFacture?: number;
}


export interface EnteteFacturePageable extends Pagination {
    content?: EnteteFacture[];
    totalElements?: number;
}