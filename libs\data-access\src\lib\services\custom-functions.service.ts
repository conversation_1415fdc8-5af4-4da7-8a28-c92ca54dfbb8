import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CustomFunctionsService {

  constructor() { }


  /* --------- Casting a STRING types boolean value to a boolean value -------- */
  isTrue(val: string): boolean {
    val = val?.trim().toLowerCase();  // return val === "true"? true : false;
    if (val === "true") {
      return true;
    } else if (val === "false") {
      return false;
    } else {
      return null;
    }
  }


}
