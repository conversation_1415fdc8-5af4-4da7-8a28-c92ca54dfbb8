<!-- src/app/components/reclamations/reclamations.component.html -->

<div id="reclamations-list" class="p-0">
    <!-- Header -->
    <div class="rowline mb-0">
        <div class="page-title-box row align-items-center">
            <h4 class="page-title fw-4 ps-2 col d-md-block d-none">Réclamations</h4>
            <div class="col px-0 py-2 mt-md-0 mt-1 py-md-0">
                <div class="row justify-content-end align-items-center">
                    <button *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']"
                        (click)="openAddReclamationModal(addReclamationModal)"
                        class="btn btn-sm btn-warning text-white rounded-pharma mx-1">
                        <i class="mdi mdi-plus"></i> Nouvelle Réclamation
                    </button>
                    <button (click)="openFilterModal(filterModal)"
                        class="btn btn-sm btn-info rounded-pharma mx-1">
                        <i class="mdi mdi-filter-variant"></i> Filtrer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Grid -->
    <div class="card">
        <kendo-grid [data]="gridView" 
            (pageChange)="pageChange($event)" 
            [pageSize]="navigation.pageSize"
            [skip]="navigation.skip" 
            (cellClick)="openViewReclamationModal($event, viewReclamationModal)"
            [pageable]="{
                buttonCount: 5,
                info: true,
                type: 'numeric',
                pageSizes: pageSizes,
                previousNext: true,
                position: 'bottom'
            }" 
            [sortable]="{mode: 'single'}" 
            [sort]="gridSort"
            (sortChange)="gridSortChange($event)"
            [groupable]="false" 
            [reorderable]="true" 
            [resizable]="true"
            style="min-height: calc(100vh - 123px);">



            <!-- Mobile Column -->
            <kendo-grid-column media="(max-width: 768px)" title="Réclamations">
                <ng-template kendoGridCellTemplate let-dataItem>
                  <dl class="mb-0">
                    <dt class="limited-width my-2 limited-width">Date Création:
                      <span>{{ dataItem.dateCreation | momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}</span>
                    </dt>
                    <dt class="limited-width my-2 limited-width" *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']">Code Client:
                      <span>{{ dataItem.codeClient }}</span>
                    </dt>
                    <dt class="limited-width my-2 limited-width">Type:
                      <span>{{ dataItem.typeReclamation?.label || getTypeReclamationLabel(dataItem.typeReclamationId) }}</span>
                    </dt>
                    <dt class="limited-width my-2 limited-width">Message:
                      <span>{{ truncateText(dataItem.message, 30) }}</span>
                    </dt>
                    <dt class="limited-width my-2 limited-width">Réponse:
                      <span>{{ truncateText(dataItem.reponse, 30) }}</span>
                    </dt>
                    <dt class="limited-width my-2 limited-width">Statut:
                      <span class="badge ml-2 rounded-pill py-1 px-2" [ngClass]="{
                        'badge-primary': dataItem.statut === 'N',
                        'badge-warning': dataItem.statut === 'R',
                        'badge-success': dataItem.statut === 'T',
                        'badge-danger': dataItem.statut === 'S'
                      }">
                        {{ getStatutLabel(dataItem.statut) }}
                      </span>
                    </dt>
                    <dt class="action-btns h-100" (click)="$event.stopPropagation()">
                      <div class="d-flex flex-column justify-content-evenly align-items-center h-100">
                        <!-- Actions for Pharmacien -->
                        <div *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']" class="d-flex flex-column justify-content-evenly align-items-center h-100">
                            <button *ngIf="canEdit(dataItem)" 
                                data-toggle="tooltip" 
                                title="Modifier une réclamtion" 
                                (click)="openEditReclamationModal(editReclamationModal, dataItem)"
                                class="circle circle-alt btn btn-primary">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button *ngIf="dataItem.statut === 'N'" 
                                data-toggle="tooltip" 
                                title="Annuler une réclamtion" 
                                (click)="cancelReclamation(dataItem.id)"
                                class="circle circle-alt btn btn-warning">
                                <i class="mdi mdi-cancel"></i>
                            </button>
                        </div>
                        <!-- Actions for Agent Fournisseur -->
                        <div *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']" class="d-flex flex-column justify-content-evenly align-items-center h-100">
                            <button 
                                (click)="openReplyReclamationModal(replyReclamationModal, dataItem)" 
                                data-toggle="tooltip" 
                                title="Répondre à une réclamtion" 
                                class="circle circle-alt btn btn-primary">
                                <i class="mdi mdi-reply"></i>
                            </button>
                        </div>
                      </div>
                    </dt>
                  </dl>
                </ng-template>
              </kendo-grid-column>
              
            

            <!-- Date Creation Column -->
            <kendo-grid-column media="(min-width: 769px)" field="dateCreation" title="Date Création" [width]="170">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem.dateCreation | momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}
                </ng-template>
            </kendo-grid-column>

            <!-- CodeClient Column (only for Agent Fournisseur) -->
            <kendo-grid-column media="(min-width: 769px)" *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']"
                field="codeClient" title="Code Client" [width]="120">
            </kendo-grid-column>

            <!-- Pharmacien Column (only for Agent Fournisseur) -->
            <kendo-grid-column media="(min-width: 769px)" *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']"
                field="nomPharmacien" title="Pharmacien" class="text-wrap" [width]="150">
            </kendo-grid-column>

            <!-- Raison Sociale Column (only for Agent Fournisseur) -->
            <kendo-grid-column media="(min-width: 769px)" *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']"
                field="raisonSociale" title="Raison Sociale" class="text-wrap" [width]="150">
            </kendo-grid-column>

            <!-- Ville Column (only for Agent Fournisseur) -->
            <kendo-grid-column media="(min-width: 769px)" *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']"
                field="ville" class="text-wrap" title="Ville" [width]="140">
            </kendo-grid-column>

            <!-- Type Reclamation Column -->
            <kendo-grid-column media="(min-width: 769px)" field="typeReclamation.label" title="Type" [width]="150">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem.typeReclamation?.label || getTypeReclamationLabel(dataItem.typeReclamationId) }}
                </ng-template>
            </kendo-grid-column>

            <!-- Message Column -->
            <kendo-grid-column media="(min-width: 769px)" field="message" title="Message" [width]="250">
            </kendo-grid-column>

            <!-- Response Column -->
            <kendo-grid-column media="(min-width: 769px)" field="reponse" title="Réponse" [width]="250">
            </kendo-grid-column>

            <!-- Status Column -->
            <kendo-grid-column media="(min-width: 769px)" field="statut" title="Statut" class="no-ellipsis" [width]="120">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span class="badge rounded-pill py-1 px-2 fixed-width-badge-reclamation" [ngClass]="{
                        'badge-primary': dataItem.statut === 'N',
                        'badge-warning': dataItem.statut === 'R',
                        'badge-success': dataItem.statut === 'T',
                        'badge-danger': dataItem.statut === 'S'
                    }">
                        {{ getStatutLabel(dataItem.statut) }}
                    </span>
                </ng-template>
            </kendo-grid-column>

            <!-- Actions Column -->
            <kendo-grid-column media="(min-width: 769px)" title="Actions" class="no-ellipsis" [width]="isFournisseur ? 140 : 80">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <div class="action-btns d-flex" (click)="$event.stopPropagation()"> <!-- Prevent row click -->
                        <!-- Actions for Pharmacien -->
                        <div *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']">
                            <button *ngIf="canEdit(dataItem)" 
                                data-toggle="tooltip" 
                                title="Modifier une réclamtion" 
                                (click)="openEditReclamationModal(editReclamationModal, dataItem)"
                                class="circle circle-alt btn btn-primary">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button *ngIf="dataItem.statut === 'N'" 
                                data-toggle="tooltip" 
                                title="Annuler une réclamtion" 
                                (click)="cancelReclamation(dataItem.id)"
                                class="circle circle-alt btn btn-warning ml-1 text-white">
                                <i class="mdi mdi-cancel"></i>
                            </button>
                        </div>
                        <!-- Actions for Agent Fournisseur -->
                        <div *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']">
                            <button (click)="openReplyReclamationModal(replyReclamationModal, dataItem)" 
                                data-toggle="tooltip" 
                                title="Répondre à une réclamtion" 
                                class="btn btn-primary btn-sm">
                                Répondre
                                <i class="mdi mdi-reply"></i>
                            </button>
                            <!-- Removed cancel button for ROLE_AGENT_FOURNISSEUR -->
                            <!-- <button (click)="checkReclamation(dataItem.id)" 
                                data-toggle="tooltip" 
                                title="Changer status à EN COURS" 
                                class="circle circle-alt btn btn-info mx-1">
                                <i class="mdi mdi-clock"></i>
                            </button> -->
                        </div>
                    </div>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-messages noRecords="Aucune réclamation trouvée"
                               pagerItems="réclamation(s)" 
                               pagerOf="de" 
                               pagerItemsPerPage="éléments par page">
            </kendo-grid-messages>
        </kendo-grid>
    </div>

    <!-- Add Reclamation Modal -->
    <ng-template #addReclamationModal let-modal>
        <div class="modal-header">
            <h4 class="modal-title">Nouvelle Réclamation</h4>
            <button type="button" class="close" (click)="modal.dismiss()">
                <i class="mdi mdi-close"></i>
            </button>
        </div>
        <form [formGroup]="addReclamationForm" (ngSubmit)="onAddReclamation()">
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Type de Réclamation</label>
                    <select2 [data]="types" placeholder="Sélectionner un type" formControlName="typeReclamationId" class="form-control-md w-100"></select2>
                </div>

                <div class="form-group mb-3">
                    <label class="form-label">Message</label>
                    <textarea formControlName="message" class="form-control" rows="4"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" (click)="modal.dismiss()">Fermer</button>
                <button type="submit" class="btn btn-primary text-white" [disabled]="!addReclamationForm.valid">
                    Ajouter
                </button>
            </div>
        </form>
    </ng-template>

    <!-- Edit Reclamation Modal -->
    <ng-template #editReclamationModal let-modal>
        <div class="modal-header">
            <h4 class="modal-title">Modifier Réclamation</h4>
            <button type="button" class="close" (click)="modal.dismiss()">
                <i class="mdi mdi-close"></i>
            </button>
        </div>
        <form [formGroup]="editReclamationForm" (ngSubmit)="onEditReclamation()">
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Type Réclamation</label>
                    <select2 [data]="types" placeholder="Sélectionner un type" formControlName="typeReclamationId" class="form-control-md w-100"></select2>
                </div>


                <div class="form-group mb-3">
                    <label class="form-label">Message</label>
                    <textarea formControlName="message" class="form-control" rows="4"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" (click)="modal.dismiss()">Fermer</button>
                <button type="submit" class="btn btn-primary text-white" [disabled]="!editReclamationForm.valid">
                    Enregistrer
                </button>
            </div>
        </form>
    </ng-template>

    <!-- Reply Reclamation Modal -->
    <ng-template #replyReclamationModal let-modal>
        <div class="modal-header">
            <h4 class="modal-title">Répondre à la Réclamation</h4>
            <button type="button" class="close" (click)="modal.dismiss()">
                <i class="mdi mdi-close"></i>
            </button>
        </div>
        <form [formGroup]="replyReclamationForm" (ngSubmit)="onReplyReclamation()">
            <div class="modal-body">
                <!-- Disabled Type field -->
                <div class="form-group mb-3">
                    <label class="form-label">Type de Réclamation</label>
                    <input type="text" formControlName="typeReclamationLabel" class="form-control" [disabled]="true">
                </div>
                
                <!-- Disabled Message field -->
                <div class="form-group mb-3">
                    <label class="form-label">Message</label>
                    <textarea formControlName="message" class="form-control" rows="4" [disabled]="true"></textarea>
                </div>
    
                <!-- Reply field -->
                <div class="form-group mb-3">
                    <label class="form-label">Réponse</label>
                    <textarea formControlName="reponse" class="form-control" rows="4"></textarea>
                </div>
    
                <!-- Status field (only Sans Suite and Traité) -->
                <div class="form-group mb-3">
                    <label class="form-label">Statut</label>
                    <select2 [data]="managerStatuses" placeholder="Sélectionner un statut" formControlName="statut" class="form-control-md w-100"></select2>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" (click)="modal.dismiss()">Fermer</button>
                <button type="submit" class="btn btn-primary text-white">
                    {{ replyReclamationForm.get('statut')?.value === 'N' ? 'Commencer' : 'Envoyer' }}
                </button>
            </div>
        </form>
    </ng-template>

    <!-- View Reclamation Modal -->
    <ng-template #viewReclamationModal let-modal>
        <div class="modal-header">
            <h4 class="modal-title">Détails de la Réclamation</h4>
            <button type="button" class="close" (click)="modal.dismiss()">
                <i class="mdi mdi-close"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="reclamation-details">
                <!-- Header Section -->
                <div class="header-info mb-3">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label><b>Code Client:</b></label>
                                <p>{{ currentReclamation.codeClient }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label><b>Raison Sociale:</b></label>
                                <p>{{ currentReclamation.raisonSociale }}</p>
                            </div>
                        </div>
                        
                        <!-- Type Section -->
                        <div class="col-md-4">
                            <div class="info-group">
                                <label><b>Ville:</b></label>
                                <p>{{ currentReclamation.ville }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label><b>Date Création:</b></label>
                                <p>{{ currentReclamation.dateCreation | momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label><b>Statut:</b></label>
                                <span class="badge rounded-pill py-1 px-2" [ngClass]="{
                                    'badge-primary': currentReclamation.statut === 'N',
                                    'badge-warning': currentReclamation.statut === 'R',
                                    'badge-success': currentReclamation.statut === 'T',
                                    'badge-danger': currentReclamation.statut === 'S'
                                }">
                                    {{ getStatutLabel(currentReclamation.statut) }}
                                </span>
                            </div>
                        </div>
                        
                        <!-- Type Section -->
                        <div class="col-md-4">
                            <div class="info-group">
                                <label><b>Type de Réclamation:</b></label>
                                <p>{{ currentReclamation.typeReclamation?.label || getTypeReclamationLabel(currentReclamation.typeReclamationId) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Message Section -->
                <div class="content-section mb-2">
                    <div class="info-group">
                        <label><b>Message:</b></label>
                        <!-- <div class="message-content">
                            {{ currentReclamation.message }}
                        </div> -->
                        <textarea disabled class="form-control" rows="4">{{ currentReclamation.message }}</textarea>
                    </div>
                </div>

                <!-- Response Section -->
                <div class="content-section mb-2" *ngIf="currentReclamation.reponse">
                    <div class="info-group">
                        <label><b>Réponse:</b></label>
                        <textarea disabled class="form-control" rows="4">{{ currentReclamation.reponse || 'Aucune réponse' }}</textarea>
<!-- 
                        <div class="message-content">
                            {{ currentReclamation.reponse || 'Aucune réponse' }}
                        </div> -->
                    </div>
                </div>

                <!-- Footer Info -->
                <div class="footer-info mt-2" *ngIf="currentReclamation.reponse || currentReclamation.statut !== 'N'">
                    <div class="info-group">
                        <label><b>Dernière modification:</b></label>
                        <p>{{ currentReclamation.dateModification | momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-light" (click)="modal.dismiss()">Fermer</button>
        </div>
    </ng-template>

    <!-- Filter Modal -->
    <ng-template #filterModal let-modal>
        <div class="modal-header">
            <h4 class="modal-title">Filtrer les Réclamations</h4>
            <button type="button" class="close" (click)="modal.dismiss()">
                <i class="mdi mdi-close"></i>
            </button>
        </div>
        <form [formGroup]="filterForm" (ngSubmit)="onFilterReclamations()">
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Type de Réclamation</label>
                        <select2 formControlName="type" 
                                hideSelectedItems="true" 
                                style="width: 100%;"
                                class="form-control-sm w-100" 
                                [multiple]="true" 
                                [data]="types">
                        </select2>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Statut</label>
                        <select2 formControlName="statut" 
                                hideSelectedItems="true" 
                                style="width: 100%;"
                                class="form-control-sm w-100" 
                                [multiple]="true" 
                                [data]="statuses">
                        </select2>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Date Début</label>
                        <div class="input-group">
                            <input (click)="dateDebut.toggle()" type="text" readonly formControlName="dateDu" placeholder="jj/mm/aaaa" class="form-control bg-white" ngbDatepicker #dateDebut="ngbDatepicker">

                            <div class="input-group-append">
                                <button class="btn btn-md btn-light text-dark btn-outline-light calendar" (click)="dateDebut.toggle()"
                                  type="button">
                                  <i class="mdi mdi-calendar"></i>
                                </button>
                              </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Date Fin</label>
                        <div class="input-group">
                            <input (click)="dateFin.toggle()" type="text" formControlName="dateAu" readonly placeholder="jj/mm/aaaa" class="form-control bg-white" ngbDatepicker #dateFin="ngbDatepicker">
                            <div class="input-group-append">
                                <button class="btn btn-md btn-light text-dark btn-outline-light calendar" (click)="dateFin.toggle()"
                                  type="button">
                                  <i class="mdi mdi-calendar"></i>
                                </button>
                              </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" (click)="modal.dismiss()">Fermer</button>
                <button type="button" class="btn btn-secondary text-white" (click)="vider()">Réinitialiser</button>
                <button type="submit" class="btn btn-primary text-white">Rechercher</button>
            </div>
        </form>
    </ng-template>
</div>