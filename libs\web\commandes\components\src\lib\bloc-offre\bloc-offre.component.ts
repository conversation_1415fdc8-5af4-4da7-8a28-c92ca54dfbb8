import { Component, ElementRef, Input, NgZone, OnChanges, OnDestroy, OnInit, SimpleChanges, TemplateRef, ViewChild, ViewContainerRef } from "@angular/core";

import { GridComponent, GridDataResult, RowClassArgs } from "@progress/kendo-angular-grid";
import { FormGroup, FormBuilder } from "@angular/forms";

import { NgbModal, NgbTooltip } from "@ng-bootstrap/ng-bootstrap";
import { Observable, Subject, debounceTime, distinctUntilChanged, map, of, switchMap, takeUntil, tap } from "rxjs";
import { BlocOffre, DetailValeurPalier, Fournisseur, FournisseurDropdownItem, Offre, OffresService, TOAST_TIMEOUT } from "@wph/data-access";
import { AccesClientService, AlertService, ClientFournisseur, DomainEnumeration, HasAccessService, Principal, SocieteType, UploadFileServiceService } from "@wph/shared";
import { AuthService } from "@wph/core/auth";
import { SelectedTypeRemiseEnum } from "libs/data-access/src/lib/models/selected-type-remise.enum";
import { WorkerService, memoize } from "@wph/web/shared";
import { ActivatedRoute } from "@angular/router";
import { ApexOptions } from "ng-apexcharts";
import { consommationChartOptions } from "../const/statistiques-consommation.const";
import { ToastrService } from "ngx-toastr";

export function compareObjetFn(a, b) {
  if (a && b)
    return a.id === b.id;
  else if (a && !b)
    return false;
  else if (!a && b)
    return false;
  else
    return true;
}

@Component({
  selector: "wph-bloc-offre",
  templateUrl: "./bloc-offre.component.html",
  styleUrls: ["./bloc-offre.component.scss"]
})
export class BlocOffreComponent implements OnInit, OnDestroy, OnChanges {
  /******* Fin Partie Liste Porduit **********/


  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  activeIds: string[] = [];
  activesSet = false;
  startsWith: RegExp = new RegExp('^[Gg][0-9]*$');
  forceSelectDistributeur: boolean = false;
  showCodeLocalWarning: boolean = false;
  popoverContent: string;
  extraConditionsTargetBloc: BlocOffre | null = null;

  sideBarState: string;
  contentContainerWidth: number = 0;
  fixedPackSyntheseHeight: number;
  dynamicImageMaxHeight: string;
  currentUser: Principal;
  displayDummy: boolean | undefined = undefined;

  hasAnyProduitConditions: boolean;
  monoProduitBlocOffre: BlocOffre | null = null;

  offreImageUrl: string;
  largeImageTargetUrl: string;
  rechercheProduitUnitModel: string;
  activePackIndex: number = 1;
  chartOptions: ApexOptions;
  afficherSynthese: boolean;

  private resizeObserver: ResizeObserver;
  parentElementRef: Element | null = null;

  modePaiementValuePair: any[] = [{ label: 'Aucune', value: null }];
  transporteurValuePair: any[] = [{ label: 'Aucun', value: null }];

  statsProduitData1: GridDataResult = { data: [], total: 0 };
  statsProduitData2: GridDataResult = { data: [], total: 0 };

  @ViewChild('fixedPackSynthese') fixedPackSynthese: ElementRef;

  constructor(
    private ngZone: NgZone,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder,
    private modalService: NgbModal,
    private authService: AuthService,
    private alertService: AlertService,
    private toastrService: ToastrService,
    private offresService: OffresService,
    private workerService: WorkerService,
    private hasAccesService: HasAccessService,
    private viewContainerRef: ViewContainerRef,
    private accesClientService: AccesClientService,
    private uploadService: UploadFileServiceService,
  ) {
    this.parentElementRef = this.viewContainerRef?.element?.nativeElement?.parentElement;
  }

  ngOnChanges(_changes: SimpleChanges): void {
    if (this.offre) {
      const qParams = this.route.snapshot.queryParams;
      const qteCmd = qParams['qteCmd'];

      if (qteCmd && this.offre?.nombreProduitsProposes === 1) {
        this.monoProduitBlocOffre.qteCmd = +qteCmd;
        this.fireRefreshEtatBlocOffre(this.monoProduitBlocOffre, null);
      }
    }
  }

  ngOnInit(): void {
    this.currentUser = this.authService.getPrincipal();

    this.offresService.produitsAvecStockInsuffisant$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(res => {
        if (res) {
          this.productIds = res.map(val => val.code);
          this.blocIds = res.map(val => val.blocId);
        }
      });

    this.listenToSidebarStateChanges();
    this.listenToPageContainerResizeEvents();
    this.listenToActivePackIndexChanges();
  }

  listenToPageContainerResizeEvents(): void {
    this.resizeObserver = new ResizeObserver(entries => {
      this.ngZone.run(() => {
        for (let entry of entries) {
          const { width, height } = entry.contentRect;
          this.contentContainerWidth = width;

          if (window.innerWidth > 919) {
            this.fixedSyntheseLeft =
              (this.sideBarState === 'condensed') ? '70px' : '260px';

            this.fixedSyntheseWidth =
              (this.sideBarState === 'condensed') ? 'calc(100vw - 70px)' : 'calc(100vw - 260px)';
          } else {
            (this.fixedSyntheseLeft = '0'), (this.fixedSyntheseWidth = '100vw');
          }

          this.afficherSynthese = (window.innerWidth > 991) ? true : false;
        }
      });
    });

    if (this.parentElementRef) {
      this.resizeObserver.observe(this.parentElementRef);
    }
  }

  listenToActivePackIndexChanges(): void {
    this.offresService.activeTabIndex$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(activeIndex => {
        this.activePackIndex = activeIndex;
      });
  }

  setFixedSyntheseHeight(): void {
    const syntheseHeight: number = this.fixedPackSynthese?.nativeElement?.clientHeight;

    if (syntheseHeight) {
      this.fixedPackSyntheseHeight = syntheseHeight + 15;
    }
  }

  hasRatioUg(listPalier: DetailValeurPalier[]): boolean {
    if (!listPalier || !listPalier?.length) return false;

    for (let palier of listPalier) {
      if (!!palier?.ratioUg) return true;
    }

    return false;
  }

  hasTauxUg(listPalier: DetailValeurPalier[]): boolean {
    if (!listPalier || !listPalier?.length) return false;

    for (let palier of listPalier) {
      if (!!palier?.tauxUg) return true;
    }

    return false;
  }

  hasTypeSelectionRfUg(listePalier: DetailValeurPalier[]): boolean {
    if (!listePalier || !listePalier?.length) return false;

    for (let palier of listePalier) {
      if (palier?.typeSelectionRfUg === 'OR') return true;
    }

    return false;
  }

  blocHasTauxUg(blocOffre: BlocOffre): boolean {
    if (!blocOffre || !blocOffre?.listeFils) return false;

    if (this.hasTauxUg(blocOffre?.listePaliers)) {
      return true;
    } else {
      for (const blocFils of blocOffre?.listeFils) {
        if (this.hasTauxUg(blocFils?.listePaliers)) return true;
      }
    }

    return false;
  }

  blocHasRatioUg(blocOffre: BlocOffre): boolean {
    if (!blocOffre || !blocOffre?.listeFils) return false;

    if (this.hasRatioUg(blocOffre?.listePaliers)) {
      return true;
    } else {
      for (const blocFils of blocOffre?.listeFils) {
        if (this.hasRatioUg(blocFils?.listePaliers)) return true;
      }
    }

    return false;
  }

  hasAnyProduitsConditions(listeFils: BlocOffre[]): boolean {
    if (!listeFils || !listeFils?.length) return false;

    for (let fils of listeFils) {
      if (this.hasConditions(fils)) return true;
    }

    return false;
  }

  canSelectTypeRfUg(listeFils: BlocOffre[]): boolean {
    if (!listeFils || !listeFils?.length) return false;

    for (let fils of listeFils) {
      if (this.hasTypeSelectionRfUg(fils?.listePaliers)) return true;
    }

    return false;
  }

  setOffreImageMaxHeight(): void {
    let rowMultiplier: number = 0;

    (this.offre?.commandStatut !== 'NOUVELLE' &&
      this.offre?.listePaliersRemisesAdditionnels?.length
    ) && (rowMultiplier++);

    if (!this.readOnly || this.offre?.commandStatut === 'BROUILLON') {
      rowMultiplier += this.authService
        .hasAnyAuthority([
          'ROLE_AGENT_FOURNISSEUR',
          'ROLE_AGENT_COMMERCIAL'
        ]) ? 4 : 3;
    } else {
      rowMultiplier += this.authService
        .hasAnyAuthority([
          'ROLE_AGENT_FOURNISSEUR',
          'ROLE_AGENT_COMMERCIAL'
        ]) ? 5 : 4;
    }

    this.dynamicImageMaxHeight = (rowMultiplier * 65) + 'px';
  }

  getBlocImage(idHash: string) {
    if (idHash)
      return this.uploadService.fetchUploadedDocument(idHash);
    return '';
  }

  appliquerLaSaisieAccelere(qteCmd: HTMLInputElement, blocParent: BlocOffre, type: string) {
    if (!qteCmd || isNaN(+qteCmd.value) || +qteCmd.value < 0) {
      qteCmd.value = '0';
      return;
    }
    this.offresService.accelerateurDeSaisie(+qteCmd.value, blocParent, type);
    this.checkifvalid(blocParent);
    this.offresService.subject.next(null);

    this.offreAlerteTraitement(blocParent);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();

    this.workerService.stopWorker();

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  @Input("offre")
  set _offre(offre: Offre) {
    if (!this.activesSet && offre?.listeBlocs) {

      this.activesSet = true;
      offre?.listeBlocs?.forEach((bloc, index) => {
        if (bloc?.totalQteCmd > 0) {
          this.activeIds.push('custom-panel-' + index);
        } else {
          this.activeIds.push('custom-panel-' + index);
        }
      });
    }
    if (offre !== this.offre) {
      // offre.distributeur = offre.distributeur;
      this.resultExisteFilsBlocsProduits = {};
      this.filsProduits = {};
      this.filsNonProduits = {};

    }

    this.offre = offre;

    if (this.authService.currentUser()?.societe?.typeEntreprise === SocieteType.GROSSISTE &&
      this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL'])
    ) {
      const forceSelectedDistributeur = this.offre?.distributeurs?.find(dist => dist?.id === this.authService.currentUser()?.societe?.id);

      if (forceSelectedDistributeur) {
        this.forceSelectDistributeur = true;
        this.offre.distributeur = forceSelectedDistributeur;
      }
    }

    if (this.offre) {
      this.coffretEnabled = this.offre.coffretEnabled;
      this.offreImageUrl = this.getBlocImage(this.offre?.docImageOffre?.idhash);

      if (this.coffretEnabled) {
        this.qteCmdPackCoffret = this.offre?.listeBlocs[0]?.totalQteCmd / this.offre?.listeBlocs[0]?.totalQtePrdFixe;
      }

      this.transporteurValuePair = [{ label: 'Aucun', value: null }];

      if (this.offre.raisonSocialeTransporteur) {
        this.offre?.raisonSocialeTransporteur?.split(',').map(transporteur => {
          const transporteurCmd = transporteur.trim();
          this.transporteurValuePair.push({ label: transporteurCmd, value: transporteurCmd });
        });
      }

      this.modePaiementValuePair = [{ label: 'Aucun', value: null }];

      if (this.offre.listDelaiPaiements && this.offre.listDelaiPaiements.length) {
        this.offre.listDelaiPaiements?.map(item => {
          if (this.offre.delaiPaiement && (item.id === this.offre?.delaiPaiement?.id)) {
            this.modePaiementValuePair.push({ label: item.label, value: this.offre.delaiPaiement });
          } else {
            this.modePaiementValuePair.push({ label: item.label, value: item });
          }
        });
      }

      // ? CAS: OFFRE MONOPRODUIT
      if (this.offre?.nombreProduitsProposes === 1) {
        this.monoProduitBlocOffre = this.offre?.listeBlocs[0]?.listeFils[0];
        this.canSelectTypeRfOuUg = this.canSelectTypeRfUg(this.offre?.listeBlocs[0]?.listeFils);
      }

      this.workerService.stopCountDownOnOffre(this.offre?.id);
      this.workerService.startCountdownWorker(this.offre);
    }

    //? SET OFFRE IMAGE MAX HEIGHT
    // this.setOffreImageMaxHeight();


    //? CHECK IF WE HAVE ONLY ONE DISTRIBUTEUR
    if (this.offre?.distributeurs?.length === 1) {
      this.offre.distributeur = this.offre.distributeurs[0]
    }

    //? FILTER OUT DISTRIBUTEURS TO WHICH THE CURRENT USER HAS NO ACCESS: ['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']
    if (this.offre && !this.readOnly && this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])) {
      const fournAvecAccesIds = this.hasAccesService
        .getListeFournisseurAvecAcces()
        ?.filter(fourn => fourn.hasAccess).map(fourn => fourn?.fournisseurId);

      const filteredDistributeurs = this.offre?.distributeurs.filter(
        distributeur => fournAvecAccesIds?.includes(distributeur.id) || distributeur?.typeEntreprise === SocieteType.FABRIQUANT
      );

      if (filteredDistributeurs.length > 1) {
        this.offre.distributeurs = filteredDistributeurs;
      } else {
        this.offre.distributeurs = filteredDistributeurs;
        this.offre.distributeur = filteredDistributeurs[0];
      }

    }

    // SET SELECTED SOCIETE
    if (this.offre?.client) {
      this.selectedSociete = {
        label: this.offre?.client?.raisonSociale,
        value: this.offre?.client,
        code: this.offre?.client?.code
      };
    }

    //SET CLIENT LOCAL
    if (
      this.readOnly &&
      (this.offre?.commandStatut === 'NOUVELLE' || this.offre?.commandStatut === 'BROUILLON') &&
      this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL'])
    ) {
      this.societeFormatter(this.offre?.client);
    }
  }

  @Input("readOnly")
  set _readOnly(readOnly: boolean) {
    this.readOnly = readOnly;
  }


  @Input("listeBlocsOffreProduits")
  set _listeBlocsOffreProduits(blocsProduits: BlocOffre[]) {
    this.listeBlocsOffreProduits = blocsProduits;

    if (this.listeBlocsOffreProduits && this.listeBlocsOffreProduits.length > 0) {
      this.parentOfProduits = this.listeBlocsOffreProduits[0].parent;
      this.hasAnyProduitConditions = this.hasAnyProduitsConditions(this.listeBlocsOffreProduits);
    }

    this.loadItems();

    let currentbloc = this.parentOfProduits;
    while (currentbloc) {
      if (currentbloc.listePaliers && currentbloc.listePaliers.length > 0) {
        for (const iterator of currentbloc.listePaliers) {
          if (iterator.ratioUg || iterator.tauxUg) {
            iterator.tauxUg && (this.EnableSaisiUG = "f");
            iterator.ratioUg && (this.EnableSaisiUG = "p");
            break;
          }
        }

      }
      currentbloc = currentbloc.parent;
    }

    if (this.parentOfProduits) {
      this.canSelectTypeRfOuUg = this.canSelectTypeRfUg(this.parentOfProduits?.listeFils);
    }

    if (blocsProduits?.length) {
      this.coffretEnabled = this.offresService.getPackParent(blocsProduits[0]).coffretEnabled;

      if (this.coffretEnabled) {
        this.qteCmdPackCoffret = this.parentOfProduits?.totalQteCmd / this.parentOfProduits?.totalQtePrdFixe;
      }
    }
  }

  offre: Offre;

  selectedSociete: FournisseurDropdownItem | null = null;
  selectedClientLocal: ClientFournisseur | string;
  isClientLocalReadOnly: boolean = false;

  productIds: string[];
  blocIds: number[];

  coffretEnabled: boolean;
  qteCmdPackCoffret: number = 0;
  montantUnitaireBrutCoffret: number = 0

  canSelectTypeRfOuUg: boolean;

  @Input()
  blocOffre: BlocOffre;

  /********** Partie Liste Porduit **********/
  listeBlocsOffreProduits: BlocOffre[];
  EnableSaisiUG: string | null = null;
  parentOfProduits: BlocOffre;
  readOnly: boolean;
  gridData: GridDataResult;

  public formGroup: FormGroup;
  validCommande = true;
  private resultExisteFilsBlocsProduits = {};

  fixedSyntheseLeft: string;
  fixedSyntheseWidth: string;


  private filsProduits = {};


  private filsNonProduits = {};

  compareFn = compareObjetFn.bind(this);

  existeFilsBlocsProduits(currentBloc: BlocOffre = null) {

    if (!currentBloc) {
      currentBloc = this.blocOffre;
    }

    if (this.resultExisteFilsBlocsProduits[currentBloc.id] != null) {
      return this.resultExisteFilsBlocsProduits[currentBloc.id];
    }


    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc === "F") {
          this.resultExisteFilsBlocsProduits[currentBloc.id] = true;
          return true;
        }
      }
      this.resultExisteFilsBlocsProduits[currentBloc.id] = false;
      return false;
    } else {
      this.resultExisteFilsBlocsProduits[currentBloc.id] = false;
      return false;
    }
  }

  memoizedExisteFilsBlocsProduits = memoize(this.existeFilsBlocsProduits);

  getListeFilsBlocsProduits(currentBloc: BlocOffre = null) {

    if (!currentBloc) {
      currentBloc = this.blocOffre;
    }

    if (this.filsProduits[currentBloc.id]) {
      return this.filsProduits[currentBloc.id];
    }


    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      this.filsProduits[currentBloc.id] = [];
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc === "F") {
          bloc.conditionsString = this.buildDataItemConditions(bloc);
          this.filsProduits[currentBloc.id].push(bloc);
        }
      }

    } else {
      this.filsProduits[currentBloc.id] = [];
    }

    return this.filsProduits[currentBloc.id];
  }

  getListeFilsBlocsNonProduits(currentBloc: BlocOffre = null) {

    if (!currentBloc) {
      currentBloc = this.blocOffre;
    }

    if (this.filsNonProduits[currentBloc.id]) {
      return this.filsNonProduits[currentBloc.id];
    }


    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      this.filsNonProduits[currentBloc.id] = [];
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc !== "F") {
          this.filsNonProduits[currentBloc.id].push(bloc);
        }
      }

    } else {
      this.filsNonProduits[currentBloc.id] = [];
    }

    return this.filsNonProduits[currentBloc.id];
  }


  private loadItems(): void {
    this.gridData = {
      data: this.listeBlocsOffreProduits,
      total: this.listeBlocsOffreProduits.length
    };
  }

  isIntersecting(isIntersecting: boolean, el: HTMLDivElement) {
    if (isIntersecting && this.displayDummy === undefined) {
      setTimeout(() => {
        this.displayDummy = (el?.children?.length % 2 !== 0);
      }, 200);
    }
  }

  isFixedPackSyntheseIntersecting(isIntersecting: boolean) {
    if (isIntersecting) {
      this.setFixedSyntheseHeight();
    }
  }

  blocIsIntersecting(isIntersecting: boolean, tooltip: NgbTooltip) {
    !isIntersecting && tooltip?.close();
  }


  open(content) {
    this.modalService.open(content, { ariaLabelledBy: "modal-basic-title", centered: true }).result.then((result) => null, (reason) => null);
  }

  fireEvent(e) {
    e.stopPropagation();
    e.preventDefault();
    return false;
  }

  public cellClickHandler({ sender, rowIndex, columnIndex, column, dataItem, isEdited }) {
    if (column?.field !== 'qteCmd' && column?.field !== 'qteUgSaisie') {
      return;
    }


    if (!isEdited) {
      sender.editCell(rowIndex, columnIndex, this.createFormGroup(dataItem));
    }
  }

  public cellCloseHandler(args: any) {
    const { formGroup, dataItem } = args;

    if (!formGroup.valid) {
      args.preventDefault();
    } else if (formGroup.dirty) {
      this.fireRefreshEtatBlocOffre(dataItem, formGroup);
    }
  }

  private fireRefreshEtatBlocOffre(dataItem: any, formGroup: FormGroup) {
    if (formGroup) {
      dataItem.qteCmd = formGroup.value.qteCmd ? +formGroup.value.qteCmd : null;
      dataItem.qteUgSaisie = formGroup.value.qteUgSaisie ? +formGroup.value.qteUgSaisie : null;
    }

    dataItem.totalQteUg = dataItem.qteUgSaisie;

    this.offresService.refreshEtatBlocOffre(dataItem);
    this.checkifvalid(dataItem);
    this.offresService.palierOffreTraitement(this.offresService.getOffreRacine(dataItem));
    this.offresService.subject.next(null);

    this.offreAlerteTraitement(dataItem);
  }
  
  private offreAlerteTraitement(dataItem: BlocOffre): void {
    this.offresService.showQteCmdAlertForNextValidRemise(dataItem);

    const [lastRaisedMsgEtat, blocOffreCible] = this.offresService.findMessageEtatOfNearestInvalidBlocOffre(dataItem);
    
    if (lastRaisedMsgEtat?.length) {
      this.toastrService.error(lastRaisedMsgEtat);
      this.triggerPopoverForTargetWithId(blocOffreCible?.id);
    }
    
    // ? Gérer le cas d'une erreur sur les conditions de l'offre
    if (this.offre?.messageEtatCmdOffre) {
      this.toastrService.error(this.offre?.messageEtatCmdOffre);
      this.triggerPopoverForTargetWithId(this.offre?.id);
    }
  }

  private triggerPopoverForTargetWithId(targetId: number): void {
    setTimeout(() => {
        const targetPopoverContainer = document.getElementById('popover-container-' + targetId);
        const targetPopover = document.getElementById('popover-' + targetId);

        if (targetPopoverContainer && !targetPopover) {
          targetPopoverContainer?.click();
        }
      }, TOAST_TIMEOUT);
  }

  qteCmdChangeOffreMonoProduit(dataItem: BlocOffre, qteCmd: number): void {
    dataItem.qteCmd = qteCmd;

    this.fireRefreshEtatBlocOffre(dataItem, null);
  }

  listenToSidebarStateChanges(): void {
    this.offresService.currentEtatBar
      .pipe(
        takeUntil(this.unsubscribe$)
      )
      .subscribe(etatBar => {
        this.sideBarState = etatBar;
        if (window.innerWidth > 919 && window.innerWidth < 1000) {
          this.fixedSyntheseLeft =
            (this.sideBarState === 'condensed') ? '70px' : '260px';

          this.fixedSyntheseWidth =
            (this.sideBarState === 'condensed') ? 'calc(100vw - 70px)' : 'calc(100vw - 260px)';
        }
        setTimeout(() => this.setFixedSyntheseHeight(), 800);
      });
  }


  public createFormGroup(dataItem: any): FormGroup {
    return this.formBuilder.group({
      qteCmd: dataItem.qteCmd,
      qteUgSaisie: dataItem.qteUgSaisie
    });
  }

  modePaiementValueChange(selected: DomainEnumeration): void {
    if (this.offre.delaiPaiement?.id !== selected?.id)
      this.offre.delaiPaiement = selected;

    for (let bloc of this.offre?.listeBlocs) {
      this.offresService.refreshEtatBlocOffre(bloc);
      this.offresService.palierOffreTraitement(this.offresService.getOffreRacine(bloc));

      this.checkifvalid(bloc);
      this.offresService.subject.next(null);
    }
  }


  public checkifvalid(currentbloc: BlocOffre) {
    const currentstat = currentbloc.parent;

    if (currentstat && currentstat?.etat === "I") {
      this.validCommande = false;
    } else {
      this.validCommande = true;
    }
  }

  public rowClassCallback(context: RowClassArgs) {
    if (context.dataItem.etat === "I") {
      return { badQte: true };
    } else {
      return {};
    }
  }

  openLargImageModal(content: TemplateRef<any>, target: string) {
    this.largeImageTargetUrl = target;
    this.modalService.open(content, { centered: true, size: 'xl' });
  }

  rowClassProduitUnitaire = (context: RowClassArgs) => {
    if (context?.dataItem?.parent?.designationProduit?.length) {
      return !context?.dataItem?.libelleProduit?.toLowerCase()?.includes(context?.dataItem?.parent?.designationProduit?.toLowerCase()) ? 'hidden-row' : '';
    }
    return '';
  };


  hasConditions(blocOffre: BlocOffre) {
    return ((blocOffre.qteMin ?? false) || (blocOffre.qteMax ?? false) || (blocOffre.valeurMin ?? false) || (blocOffre.valeurMax ?? false)
      || (blocOffre.nbrObjFilsMin ?? false) || (blocOffre.nbrObjFilsMax ?? false) || (blocOffre.nombreProduitsMin ?? false) || (blocOffre.nombreProduitsMax ?? false)
      || (blocOffre.plafondRemiseSpeciale ?? false) || (blocOffre.plafondUgSpeciale ?? false) || (blocOffre.multipleQtePrdCmd ?? false)
    );
  }

  memoizedHasConditions = memoize(this.hasConditions);

  hasOffreConditions(offre: Offre) {
    return ((offre?.qteMin ?? false) || (offre?.qteMax ?? false) || (offre?.valeurMin ?? false) || (offre?.valeurMax ?? false));
  }

  memoizedHasOffreConditions = memoize(this.hasOffreConditions);

  hasPaliers(blocOffre: BlocOffre) {
    return blocOffre?.listePaliers?.length;
  }

  filterList(searchQuery: string) {
    let criteriaKey: string, criteria: any = { segmentEntreprise: 'O' };
    criteriaKey = this.startsWith.test(searchQuery) ? 'code' : 'raisonSociale';

    criteria = {
      ...criteria,
      typeEntreprises: [SocieteType.CLIENT],
      [criteriaKey]: criteriaKey === 'code' ? searchQuery.toUpperCase() : searchQuery
    };

    return this.offresService.searchSociete(criteria);
  }

  searchSociete = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap(term => {
        if (term.length > 1) {
          return this.filterList(term.toLowerCase())
        } else {
          this.isClientLocalReadOnly &&
            (
              this.isClientLocalReadOnly = false,
              (this.offre.clientLocal = this.selectedClientLocal = null)
            );

          return of({ content: [] });
        }
      }),
      map(res => res.content.slice(0, 5))
    );

  societeFormatter = (result: Fournisseur | any) => {
    if (result instanceof Object) {
      let codeL: number;

      this.accesClientService.searchPharmacyByCode(result?.code)
        .pipe(
          map(pharmacies => pharmacies?.content[0]?.accesClient?.codeClientLocal),
          tap(codeLocal => codeL = codeLocal),
          switchMap(codeLocal => this.accesClientService.filterClientsLocal(codeLocal?.toString())),
          map(clientsFournisseur => clientsFournisseur[0])
        )
        .subscribe(async (res: ClientFournisseur) => {
          if (res) {
            this.isClientLocalReadOnly = true;
            this.offre.clientLocal = this.selectedClientLocal = res;
          } else {
            this.isClientLocalReadOnly = false;
            this.offre.clientLocal = this.selectedClientLocal = codeL?.toString() || this.offre?.codeClientLocal || null;

            !!codeL && (this.isClientLocalReadOnly = true);

            if (!this.isClientLocalReadOnly && this.offre.codeClientLocal) {
              this.showCodeLocalWarning = true;
              this.popoverContent = "Code client local saisi manuellement. Veuillez le vérifier avant d\'accepter la commande.";
            }
          }

          if (this.readOnly && this.isClientLocalReadOnly && !(await this.hasAccesService.estClient(this.offre.client.code))) {
            this.showCodeLocalWarning = true;
            this.popoverContent = "L’accès du client sélectionné est actuellement désactivé, veuillez activer l’accès pour pouvoir accepter la commande."
          }
        });

      return `${result?.code}: ${result?.raisonSociale || result?.label}`;
    }

    return '';
  };

  searchClientLocal = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap(term => {
        if (term.length > 1) {
          return this.accesClientService.filterClientsLocal(term?.trim());
        }

        return of([]);
      }),
      map(res => res?.slice(0, 5))
    );

  clientLocalFormatter = (result: ClientFournisseur | string) => {
    return (result instanceof Object) ?
      `${result?.code}: ${result?.raisonSociale}` : result;
  }

  hasManyConditions(bloc: BlocOffre): boolean {
    const conditions = [
      bloc?.qteMin,
      bloc?.qteMax,
      bloc?.valeurMin,
      bloc?.valeurMax,
      bloc?.nbrObjFilsMin,
      bloc?.nbrObjFilsMax,
      bloc?.nombreProduitsMin,
      bloc?.nombreProduitsMax
    ];

    const truthyConditionsCount = conditions?.filter(cond => !!cond)?.length;

    return (truthyConditionsCount > 3);
  }

  openExtraConditionsModal(content: TemplateRef<any>, targetBloc: BlocOffre, size = 'md'): void {
    this.extraConditionsTargetBloc = targetBloc;
    this.modalService.open(content, { size, centered: true });
  }

  selectedRemiseChange(newSelecteTypeRemise: SelectedTypeRemiseEnum, dataItem: BlocOffre) {
    dataItem.selectedTypeRemiseEnum = newSelecteTypeRemise;

    // Actualiser l'état du commande groupe
    this.offresService.refreshEtatBlocOffre(dataItem);
    this.offresService.palierOffreTraitement(this.offresService.getOffreRacine(dataItem));

    // ? Set qteUgSaisie after palier selection
    dataItem.qteUgSaisie = dataItem?.totalQteUg;

    this.checkifvalid(dataItem);
    this.offresService.subject.next(null);
  }

  buildDataItemConditions(dataItem: BlocOffre): string {
    let conditionsString: string = '';

    dataItem?.qteMin && (conditionsString += ` Qté Min: ${dataItem?.qteMin}`);
    dataItem?.qteMax && (conditionsString += `${conditionsString?.length ? ',' : ''} Qté Max: ${dataItem?.qteMax}`);

    dataItem?.valeurMin && (conditionsString += `${conditionsString?.length ? ',' : ''} Valeur Min: ${dataItem?.valeurMin}`);
    dataItem?.valeurMax && (conditionsString += `${conditionsString?.length ? ',' : ''} Valeur Max: ${dataItem?.valeurMax}`);

    dataItem?.nbrObjFilsMin && (conditionsString += `${conditionsString?.length ? ',' : ''} Sous blocs CMD Min: ${dataItem?.nbrObjFilsMin}`);
    dataItem?.nbrObjFilsMax && (conditionsString += `${conditionsString?.length ? ',' : ''} Sous blocs CMD Max: ${dataItem?.nbrObjFilsMax}`);

    dataItem?.nombreProduitsMin && (conditionsString += `${conditionsString?.length ? ',' : ''} Nbr Prd CMD Min: ${dataItem?.nombreProduitsMin}`);
    dataItem?.nombreProduitsMax && (conditionsString += `${conditionsString?.length ? ',' : ''} Nbr Prd CMD Max: ${dataItem?.nombreProduitsMax}`);

    dataItem?.plafondUgSpeciale && (conditionsString += `${conditionsString?.length ? ',' : ''} Plafond Taux UG: ${dataItem?.plafondUgSpeciale}%`);
    dataItem?.plafondRemiseSpeciale && (conditionsString += `${conditionsString?.length ? ',' : ''} Plafond Taux RF: ${dataItem?.plafondRemiseSpeciale}%`);
    dataItem?.multipleQtePrdCmd && (conditionsString += `${conditionsString?.length ? ',' : ''} Saisir seulement les multiples: ${dataItem?.multipleQtePrdCmd}`);

    return conditionsString;
  }

  qteCmdPackCoffretValueChange(value: any, increment = false, sub = false): void {
    value && (this.qteCmdPackCoffret = value);

    increment && (++this.qteCmdPackCoffret);

    if (sub && (this.qteCmdPackCoffret - 1) > -1) {
      --this.qteCmdPackCoffret;
    }

    this.refreshCalculMontantUnitaireBrutCoffret();
  }

  private refreshCalculMontantUnitaireBrutCoffret() {
    this.montantUnitaireBrutCoffret = 0;


    for (const blocPrd of this.offre?.listeBlocs[0]?.listeFils) {
      this.montantUnitaireBrutCoffret += (blocPrd.qteFixePrdInCoffret ?? 0) * (blocPrd.prixVenteTtc ?? 0);

      if (this.qteCmdPackCoffret >= 0) {
        blocPrd['qteCmd'] = blocPrd.qteFixePrdInCoffret * this.qteCmdPackCoffret;
        this.offresService.refreshEtatBlocOffre(blocPrd);

        this.checkifvalid(blocPrd);
        this.offresService.palierOffreTraitement(this.offresService.getOffreRacine(blocPrd));
        this.offresService.subject.next(null);
      }
    }

    if (this.qteCmdPackCoffret) {
      this.montantUnitaireBrutCoffret *= this.qteCmdPackCoffret;
    }

  }

  selectOpenInput(index = 0, isGroupe = false, memberIndex = 0, initPrefix = 'member') {
    if (isGroupe) {
      //console.log(`input[data-prefix="member-${memberIndex}-${index}"]`);

      setTimeout(() => {
        const opendInput = document.querySelector(`input[data-prefix="${initPrefix}-${memberIndex}-${index}"]`) as HTMLInputElement;
        //console.log(opendInput)

        opendInput?.focus();
        opendInput?.select();

      });
      return;
    }
    setTimeout(() => {
      const opendInput = document.querySelector(`input[data-prefix="${initPrefix}-${index}"]`) as HTMLInputElement;
      opendInput?.focus();
      opendInput?.select();

    }, 0);
  }

  handleEnterOnIOnput(event, isFormGroup: boolean = false, initPrefix = 'qteCMD') {

    if (isFormGroup) {
      const target = event.target;
      const prefix = target.getAttribute('data-prefix');

      const blocIndex = parseInt(prefix.split('-')[2]);
      const memberIndex = parseInt(prefix.split('-')[1]);

      if (isNaN(blocIndex) || isNaN(memberIndex)) {
        console.warn('blocIndex or memberIndex is not a number')
        return;
      }

      const nextElement = document.querySelector(`div[data-prefix="${initPrefix}-${memberIndex}-${blocIndex + 1}"]`) as HTMLElement;

      if (!nextElement) {
        const firstElement = document.querySelector(`div[data-prefix="${initPrefix}-${memberIndex}-0"]`) as HTMLElement;
        firstElement?.click();
        this.selectOpenInput(0, true, memberIndex);
        console.log("cant find next");

        return;
      }

      nextElement?.click();

      this.selectOpenInput(blocIndex + 1, true, memberIndex);




      return;
    }



    const target = event.target;
    const prefix = target.getAttribute('data-prefix');


    if (!prefix) {
      console.warn('prefix is setted on the input')
      return;
    }


    // extract index from prefix
    const index = parseInt(prefix.split('-')[1]);

    // console.log({index, prefix});


    const nextElement = document.querySelector(`div[data-prefix="${initPrefix}-${index + 1}"]`) as HTMLElement;
    // console.log(nextElement)


    if (!nextElement) {
      const firstElement = document.querySelector(`div[data-prefix="${initPrefix}-0"]`) as HTMLElement;
      firstElement?.click();
      this.selectOpenInput(0, false, 0, initPrefix);
      return;
    }

    nextElement?.click();
    this.selectOpenInput(index + 1, false, 0, initPrefix);

  }

  onKeyDown(event: KeyboardEvent, grid: GridComponent, isFormGroup: boolean = false, initPrefix = 'qteUGSaisie'): void {
    if (event.key === 'Escape' || event.key === 'Enter' || event.key === 'ArrowDown' || (event.key === "Tab" && !event.shiftKey)) {
      event.preventDefault();
      grid.closeCell();
      this.handleEnterOnIOnput(event, isFormGroup, initPrefix)
    }
    else if ((event.key === 'Tab' && event.shiftKey) || event.key === 'ArrowUp') {
      event.preventDefault();
      this.handleEnterOnIOnputReverse(event, isFormGroup, initPrefix)
    }
  }

  handleEnterOnIOnputReverse(event, isFormGroup: boolean = false, initPrefix = 'qteCMD') {

    if (isFormGroup) {
      const target = event.target;
      const prefix = target.getAttribute('data-prefix');

      const blocIndex = parseInt(prefix.split('-')[2]);
      const memberIndex = parseInt(prefix.split('-')[1]);

      if (isNaN(blocIndex) || isNaN(memberIndex)) {
        console.warn('blocIndex or memberIndex is not a number')
        return;
      }

      const nextElement = document.querySelector(`div[data-prefix="${initPrefix}-${memberIndex}-${blocIndex - 1}"]`) as HTMLElement;

      if (!nextElement) {
        const firstElement = document.querySelector(`div[data-prefix="${initPrefix}-${memberIndex}-0"]`) as HTMLElement;
        firstElement?.click();
        this.selectOpenInput(0, true, memberIndex);
        console.log("cant find next");

        return;
      }

      nextElement?.click();

      this.selectOpenInput(blocIndex - 1, true, memberIndex);




      return;
    }



    const target = event.target;
    const prefix = target.getAttribute('data-prefix');


    if (!prefix) {
      console.warn('prefix is setted on the input')
      return;
    }


    // extract index from prefix
    const index = parseInt(prefix.split('-')[1]);

    // console.log({index,prefix});


    const nextElement = document.querySelector(`div[data-prefix="${initPrefix}-${index - 1}"]`) as HTMLElement;
    // console.log(nextElement)


    if (!nextElement) {
      const firstElement = document.querySelector(`div[data-prefix="${initPrefix}-0"]`) as HTMLElement;
      firstElement?.click();
      this.selectOpenInput(0, false, 0, initPrefix);
      return;
    }

    nextElement?.click();
    this.selectOpenInput(index - 1, false, 0, initPrefix);

  }

  private fetchStatistiquesConsommationForSelectedProduct(produit: BlocOffre): Promise<void> {
    return new Promise((resolve, reject) => {
      if (produit?.codeProduitCatalogue) {
        const hasSourceNavigation = this.authService.getNavigationSourceAndTarget() !== null;

        if (!hasSourceNavigation && !this.offre?.distributeur) {
          reject(new Error("No distributeur selected for the product."));
          return;
        }

        if (!hasSourceNavigation && !(this.offre?.distributeur?.noeud?.codeSite === 999 ||
          (this.offre?.distributeur?.noeud?.codeSite >= 1 && this.offre?.distributeur?.noeud?.codeSite <= 14))
        ) {
          reject(new Error("Invalid code site for the distributor."));
          return;
        }

        this.offresService.fetchIndicateurConsommationWinPlus(
          [produit.codeProduitCatalogue],
          hasSourceNavigation ? null : this.offre?.distributeur?.noeud?.codeSite
        ).subscribe({
          next: (res) => {
            if (res && res.length) {
              const gridFormattedData = this.offresService.getFormattedIndicateurConsommationData(res[0]);

              this.statsProduitData1 = { data: gridFormattedData?.slice(0, 6), total: 6 };
              this.statsProduitData2 = { data: gridFormattedData?.slice(6), total: 6 };

              this.chartOptions = consommationChartOptions(
                gridFormattedData?.map(item => item?.consommation ?? 0),
                gridFormattedData?.map(item => item?.libellePeriode)
              );

              resolve();
            } else {
              reject(new Error("No data found for the selected product."));
            }
          },
          error: (err) => {
            reject(err);
          }
        });
      } else {
        reject(new Error("Invalid product: Missing codeProduitCatalogue."));
      }
    });
  }

  forceExpandOrCollapseAllSousBlocs(bloc: BlocOffre, collapse: boolean) {
    this.offresService.forceCollapseOrExpandAllOnBlocOffre(bloc, collapse);
  }


  openProduitStatistiques(content: TemplateRef<any>, size = 'xl', produit: BlocOffre) {
    this.fetchStatistiquesConsommationForSelectedProduct(produit).then(
      () => {
        this.modalService.open(content,
          { size, windowClass: 'custom-modal-width', modalDialogClass: 'fs-radius-modal', centered: true })
          .result.then(() => null, () => null);
      },
      (err: Error) => {
        if (err?.message?.includes('No data')) {
          this.alertService.error(`Il n'y a pas des statistiques de consommation pour le produit: ${produit?.libelleProduit}`, 'MODAL');
        } else if (err?.message?.includes('No distributeur')) {
          this.alertService.error(`Veuillez sélectionner un <b>distributeur</b>.`, 'MODAL');
        } else if (err?.message?.includes('Invalid code site')) {
          this.alertService.error(`Les statistiques de consommation ne sont pas disponibles pour le distributeur sélectionné.`, 'MODAL');
        } else {
          this.alertService.error(`Impossible de récupérer les statistiques de consommation pour le produit: ${produit?.libelleProduit}`, 'MODAL');
        }
      }
    );
  }

}
