<!-- Start Of Header -->
<!-- <div class="rowline mb-2">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 col-12">ACCUEIL</h4>
  </div>
</div> -->
<!-- <PERSON><PERSON> HEADER -->

<div class="page-content d-flex justify-content-center">
  <div class="row w-100 m-1 d-flex flex-wrap justify-content-center page-content-inner">
    <div class="col-12 col-xl-3 mx-auto">

      <div class="card mb-2 plat-selection-card p-0 bg-btn-container">
        <div class="row w-100 m-0 p-0">
          <div class="col-12 m-0 p-0">
            <div *jhiHasTargetPlateformeFromWinplus="[null]" class="pt-2 pb-3 px-2 m-0 bg-btn-container btn-radius">
              <span class="form-text text-center btn-section h5">{{'Accès Offres & Marché' | uppercase}}</span>

              <div (click)="navigateToAccueil('WIN_OFFRE')"
                class="d-flex justify-content-start align-items-center logo-wrapper w-100 rounded rounded-1 pointer-cus btn-radius win-offre-btn animated">
                <div class="mx-2 my-1 px-1 winplus-offre-container">
                  <img class="logo text-center" loading="eager" src="assets/images/winplus_offre_mini.svg"
                    style="height: 36px !important;" />
                </div> <span class="h4" style="font-weight: 800; color: #315A32">{{'WinPlus Offre' | uppercase}}</span>
              </div>
            </div>

            <ng-container
              *ngIf="user?.societe?.typeEntreprise === 'GROSSISTE' || user?.societe?.typeEntreprise === 'CLIENT'">
              <hr *jhiHasTargetPlateformeFromWinplus="[null]" class="mx-2 my-0 bg-transparent platform-selection-sep">

              <div class="py-1 px-2 m-0 bg-btn-container btn-radius" style="min-height: 150px">
                <span class="form-text text-center btn-section py-0 px-1 h5">{{'Accès Grossiste / Commande Web' |
                  uppercase}}</span>

                <div class="row">
                  <div *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']" class="col-12 px-0">
                    <div class="d-flex flex-wrap justify-content-center w-100 px-2">
                      <button *ngFor="let grossiste of listeAccesClient"
                        (click)="btnTooltip.open(); setSelectedGrossiste(grossiste); navigateToAccueil('COMMANDE_WEB')"
                        class="btn my-1 d-flex justify-content-evenly align-items-center w-100 btn-radius"
                        [style.background]="baseRGB[grossiste?.fournisseur?.noeud?.codeSite] ? 
                        'linear-gradient(173deg, 
                          rgba('+ baseRGB[grossiste?.fournisseur?.noeud?.codeSite] +', 0.8) 0%, 
                          rgb('+ baseRGB[grossiste?.fournisseur?.noeud?.codeSite] +') 100%
                        )' : 
                        'linear-gradient(173deg, rgb(0, 157, 79) 0%, rgb(1, 131, 71) 100%)'"
                        [ngClass]="{ 'btn-cmd-grossiste': !grossiste?.dateDesactivation, 'btn-acces-client-deactivated': grossiste?.dateDesactivation }"
                        [disabled]="grossiste?.disabled" tooltipClass="cstm-tooltip-container" container="body"
                        [ngbTooltip]="grossiste?.dateDesactivation ? disabledFournisseurTooltip : null"
                        #btnTooltip="ngbTooltip">
                        <div class="fournisseur-img"
                          [style.background]="baseRGB[grossiste?.fournisseur?.noeud?.codeSite] ? 
                            'rgba('+baseRGB[grossiste?.fournisseur?.noeud?.codeSite] +', 0.85)' : 'rgba(1, 131, 71, 0.85)'">
                          <img src="assets/images/ugp-logo.svg" loading="eager" alt="fournisseur-img" />
                        </div>
                        <span class="ms-2 h5 fournisseur-text">{{ grossiste?.fournisseur?.raisonSociale }}</span>

                      </button>

                      <ng-template #disabledFournisseurTooltip>
                        <div class="d-flex align-items-start k-gap-1">
                          <i class="mdi mdi-alert-circle-outline mdi-18px text-warning"></i>
                          <span class="text-white" style="font-size: .82rem; padding-top: 4px">Le service Commande Web
                            est désactivé chez ce fournisseur</span>
                        </div>
                      </ng-template>
                    </div>
                    <!-- No active services container -->
                    <div *ngIf="!(listeAccesClient && listeAccesClient?.length)"
                      class="row justify-content-center px-2">
                      <div class="row justify-content-center">
                        <div class="col-12">
                          <i style="color: #f4a424" class="mdi mdi-alert-box mdi-36px mdi-warning"></i>
                        </div>
                      </div>

                      <div class="text-center px-2 my-2 col-12">
                        <span>Vous n'avez aucun service actif chez un fournisseur. Nous vous
                          invitons à demander l'activation du service commande web.</span>
                      </div>
                    </div>
                  </div>

                  <div *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL', 'ROLE_AGENT_ACHAT']"
                    class="col-12 px-0">
                    <div class="d-flex flex-wrap justify-content-center w-100 px-2">
                      <button (click)="setSelectedGrossiste(user?.societe); navigateToAccueil('COMMANDE_WEB')"
                        [style.background]="baseRGB[user?.societe?.noeud?.codeSite] ? 'linear-gradient(173deg, rgba('+ baseRGB[user?.societe?.noeud?.codeSite] +', 0.8) 0%, rgb('+ baseRGB[user?.societe?.noeud?.codeSite] +') 100%)' : 'linear-gradient(173deg, rgb(0, 157, 79) 0%, rgb(1, 131, 71) 100%)'"
                        class="btn my-1 d-flex justify-content-evenly align-items-center w-100 btn-radius btn-cmd-grossiste">
                        <div class="fournisseur-img"
                          [style.background]="baseRGB[user?.societe?.noeud?.codeSite] ? 'rgba('+baseRGB[user?.societe?.noeud?.codeSite] +', 0.85)' : 'rgba(1, 131, 71, 0.85)'">
                          <img src="assets/images/ugp-logo.svg" loading="eager" alt="fournisseur-img" />
                        </div>
                        <span class="ms-2 h5 fournisseur-text">{{ user?.societe?.raisonSociale }}</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </div>
        </div>

      </div>
    </div>

    <div class="col-12 col-xl-5 mx-auto">
      <div class="position-relative h-xl-100 h-auto actualites-col pr-xl-1 pr-0">
        <wph-liste-postes [targetPlateformeId]="3" [isPinnedPostList]="true" [overrideMaxHeight]="true" mode="swiper"
          [listeFournisseur]="listeFournisseur"></wph-liste-postes>
          
        <div class="my-2 position-relative"></div>

        <wph-liste-postes [targetPlateformeId]="3" mode="liste"
          [listeFournisseur]="listeFournisseur"></wph-liste-postes>
      </div>
    </div>

    <div class="col-12 col-xl-4 mx-auto pb-4 pb-xl-0">
      <div class="d-flex row m-0 p-0 w-100">
        <div *jhiHasTargetPlateformeFromWinplus="[null]" class="px-0 px-lg-1 px-xl-0 mx-0 mt-xl-0 mt-2 mb-2 col-12">
          <wph-offre-slider height="auto" maxHeight="calc((100vh - 205px) * 1/3)" [truncateTitle]="true"
            objectFit="cover" [listeOffres]="listeOffres"></wph-offre-slider>
        </div>

        <div class="px-0 px-lg-1 px-xl-0 col-12">
          <a href="https://www.sophatel.com/demo_contact.html" target="_blank" class="pointer-cus"
            title="Demander démo">
            <div class="card w-100 w-xl-100 p-2 card-win-plus my-xl-0 my-2 shadow" wphTrackInteraction
              [eventData]="{name: 'Publicité: WinPlus Pharma', type: 'Publicité', author: 'Sophatel'}">
            </div>
          </a>
        </div>

        <div class="px-0 px-lg-1 px-xl-0 col-12">
          <a href="https://sophatel.com/materiels.html" target="_blank" title="Achetez maintenant">
            <div class="card w-100 w-xl-100 p-2 gradient_card shadow my-2" wphTrackInteraction
              [eventData]="{name: 'Publicité: Vente de matériel informatique', type: 'Publicité', author: 'Sophatel'}">
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>