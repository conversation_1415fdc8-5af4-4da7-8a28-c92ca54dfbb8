<!-- Start Of Header -->
<div class="rowline mb-2">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 col-4">Statistiques des membres</h4>

        <div class="row d-flex flex-row  justify-content-end   col-8 px-1" style="gap: 25px;">
            <div class="row  d-flex align-items-center">
                <p class="mb-0 mr-2 fw-bold">Du:</p>
                <button (click)="backwardOneMonth('start')" class="btn btn-sm btn-left px-1">
                    <i class="mdi mdi-chevron-left mdi-18px"></i>
                </button>
                <ng-container>
                    <input [formControl]="datePicker" style="visibility: hidden; position: absolute; right: 10px;">
                    <span class="mx-0 my-0 d-flex align-items-center date-container">
                        <i class="mdi mdi-calendar mdi-18px"></i>
                        <span class="mx-1">{{ displayedStartDate$ | async | date: 'MMMM yyyy' | titlecase }}</span>
                    </span>
                </ng-container>
                <button (click)="forwardOneMonth('start')" class="btn btn-sm btn-right px-1"
                [disabled]="((displayedStartDate$ | async)?.getMonth() === now?.getMonth()) && ((displayedStartDate$ | async)?.getFullYear() === now?.getFullYear())"
                >
                    <i class="mdi mdi-chevron-right mdi-18px"></i>
                </button>
            </div>
            <div class="row  d-flex  align-items-center ">
                <p class="mb-0 mr-2 fw-bold">Au:</p>
                <button (click)="backwardOneMonth('end')" class="btn btn-sm btn-left px-1">
                    <i class="mdi mdi-chevron-left mdi-18px"></i>
                </button>

                <ng-container>
                    <input [formControl]="datePicker2" style="visibility: hidden; position: absolute; right: 10px;">

                    <span class="mx-0 my-0 d-flex align-items-center date-container">
                        <i class="mdi mdi-calendar mdi-18px"></i>
                        <span class="mx-1">{{ (displayedEndDate$ | async | date: 'MMMM yyyy') | titlecase}}</span>
                    </span>
                </ng-container>

                <button (click)="forwardOneMonth('end')"
                    [disabled]="((displayedEndDate$ | async)?.getMonth() === now?.getMonth()) && ((displayedEndDate$ | async)?.getFullYear() === now?.getFullYear())"
                    class="btn btn-sm btn-right px-1">
                    <i class="mdi mdi-chevron-right mdi-18px"></i>
                </button>
            </div>

            <div>
                <button (click)="getMyStatistique()" class="btn btn-sm btn-info px-1">
                    Filtrer
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="">
    <div class="row d-flex flex-row justify-content-between mx-lg-3 mt-1" >
     <div class="col-12">
      <div class="card" style="width: 100%;">
        <!-- <div class="card-header">
          <h4 class="my-0 text-uppercase text-dark">Statistique par membre </h4>
        </div> -->
        <div class="card-body">
          <apx-chart *ngIf="chartOptions"
              style="width: 500px;"
              [series]="chartOptions?.series"
              [chart]="chartOptions?.chart"
              [dataLabels]="chartOptions?.dataLabels"
              [plotOptions]="chartOptions?.plotOptions"
              [yaxis]="chartOptions?.yaxis"
              [legend]="chartOptions?.legend"
              [fill]="chartOptions?.fill"
              [stroke]="chartOptions?.stroke"
              [tooltip]="chartOptions?.tooltip"
              [xaxis]="chartOptions?.xaxis"></apx-chart>
        </div>
      </div>
     </div>
     <div class="col-12 mt-2">
      <kendo-grid [data]="gridData" class="fs-grid fs-listing-grid" [sortable]="false" [pageable]="false" style="height: 100%">
        <kendo-grid-column field="pharmacien.raisonSociale" class="text-wrap" title="Pharmacien(ne)" [width]="150">
          <ng-template kendoGridCellTemplate let-dataItem>
            DR. {{dataItem?.pharmacien?.nomResponsable}}
           </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="pharmacien.ville" class="text-wrap" title="Ville" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="periode" title="Période" [width]="150"></kendo-grid-column>
        <kendo-grid-column field="montantSupportee" class="text-right" title="Montant Supporté" [width]="150">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.montantSupportee | number: '1.2-2' }}
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="montantConsomme" class="text-right" title="Montant Consommé" [width]="150">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.montantConsomme | number: '1.2-2' }}
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="balance" class="text-right" title="Balance" [width]="150">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.balance | number: '1.2-2' }}
            </ng-template>
        </kendo-grid-column>
      </kendo-grid>
     </div>
        <!-- <div class="col-lg-6 bg-white rounded-lg shadow-lg mb-lg-3">
                 <canvas
          baseChart
          [datasets]="lineChartData"
          [labels]="lineChartMembres"
          [options]="lineChartOptions"
          [legend]="lineChartLegend"
          class="chart-canvas">
        </canvas>
        </div>
        <div class="col-lg-5 bg-white rounded-lg shadow-lg mb-lg-3">
            <canvas baseChart [data]="pieChartData" [type]="pieChartType" [options]="pieChartOptions"
                class="chart-canvas"></canvas>
        </div> -->

    </div>
