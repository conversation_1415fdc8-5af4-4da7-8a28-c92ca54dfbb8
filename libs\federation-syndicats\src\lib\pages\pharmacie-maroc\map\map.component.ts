import {
  Component,
  AfterViewInit,
  Input,
  EventEmitter,
  Output,
} from "@angular/core";
import * as L from "leaflet";

@Component({
  selector: "app-map",
  templateUrl: "./map.component.html",
  styleUrls: ["./map.component.scss"],
})
export class MapComponent implements AfterViewInit {

  searchPharmaCriteria: any;

  @Output("coords") coords = new EventEmitter<any[]>();
  @Output("markerSelected") markerSelected = new EventEmitter<any>();

  public _data: any;

  markercentre;
  markers = [];
  lat;
  lng;
  map;


  smallIcon = new L.Icon({
    iconUrl: "assets/images/pin-map-blue.svg",
    iconRetinaUrl: "assets/images/pin-map-blue.svg",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  });

  private _chosenMarker: any;

  constructor() { }

  ngAfterViewInit(): void {
    this.createMap();
  }

  @Input("data") set data(data: any)
  {
    this._data = data;

    this.clearMarkers();
    this.loadMarkers();
  }

  @Input("highlightMarker") set highlightMarker(data: any)
  {
    if (this.map) { // init the view to avoid zoom bug in select pharamacy 🐱‍👤
      this.map.setView(new L.LatLng(32.13352863555273,-6.6552767157554635));
    }
    
    this._chosenMarker = data;
    this.clearMarkers();
    this.loadMarkers();
  }

  createMap() {
    if (this.map) return
    const parcThabor = {
      lat: 32.13352863555273,
      lng: -6.6552767157554635,
    };

    const zoomLevel = 6;

    this.map = L.map("map", {
      center: [parcThabor.lat, parcThabor.lng],
      zoom: zoomLevel,
      zoomAnimation:false,
      attributionControl: false,
    });

    
    const mainLayer = L.tileLayer(
      "https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
      {
        minZoom: 5,
        maxZoom: 18,
        zoomSnap: 0.5
      }
      );
      
      mainLayer.addTo(this.map);
      
      this.markercentre = L.marker(this.map.getCenter(), {
        draggable: true,
        icon: L.icon({
          iconUrl: "assets/images/cercle.svg",
          iconSize: [25, 35],
          iconAnchor: [30 / 2, 35],
        }),
      }).addTo(this.map);
      
    if (
      this.searchPharmaCriteria &&
      this.searchPharmaCriteria.latitude &&
      this.searchPharmaCriteria.longitude
    ) {
      this.markercentre.setLatLng(
        new L.LatLng(
          this.searchPharmaCriteria.latitude,
          this.searchPharmaCriteria.longitude
        )
      );
      this.map.panTo(
        new L.LatLng(
          this.searchPharmaCriteria.latitude,
          this.searchPharmaCriteria.longitude
        )
      );
    }
    this.map.on("click", (e) => this.onMapClick(e));

    let markerPositionBeforeDrag = this.markercentre.getLatLng();

    this.markercentre.on('dragend', (e) => {
      const markerPositionAfterDrag = e.target.getLatLng();
      if (
        markerPositionBeforeDrag.lat !== markerPositionAfterDrag.lat ||
        markerPositionBeforeDrag.lng !== markerPositionAfterDrag.lng
      ) {
          this.lat = markerPositionAfterDrag.lat;
          this.lng = markerPositionAfterDrag.lng;
      
          this.markercentre.setLatLng(new L.LatLng(markerPositionAfterDrag.lat, markerPositionAfterDrag.lng));
          this.map.panTo(new L.LatLng(markerPositionAfterDrag.lat, markerPositionAfterDrag.lng));
          this.getMycoords();
       }
    });
  }

  clearMarkers() {
    for (const marker of this.markers) {
      marker.remove();
    }

    this.markers = [];
  }

  loadMarkers() {
    this.createMap()
    
    if (this._data && this._data.length) {
      let firstone = true;
      let coords
      for (let i = 0; i < this._data.length; i++) {
        const pharmacy = this._data[i];
        
        if (pharmacy.latitude && pharmacy.longitude) {
          if (firstone) {
            this.map.setZoomAround([pharmacy.latitude, pharmacy.longitude],14,true)
            this.map.panTo(new L.LatLng(pharmacy.latitude, pharmacy.longitude));
            coords = pharmacy
            firstone = false;
          }
          this.addMarker({
            coords: { lat: pharmacy.latitude, lng: pharmacy.longitude },
            text: pharmacy.raisonSociale,
            open: false,
          });
        }
      }

      this.markers.forEach(marker => {
        marker.addEventListener("click",() => {
          this.markerSelected.emit(marker._latlng)
        })
      });
    }
  }
  merkerClickedOnMAP(coordMarkerClicked)
  {
    if(!coordMarkerClicked) return
    this._data.forEach(obj => {
      
      if (obj.longitude == coordMarkerClicked.lng && obj.latitude == coordMarkerClicked.lat) {
        this._chosenMarker = obj;
        this.clearMarkers();
        this.loadMarkers();
      }

    });

  }
  addMarker({ coords, text, open }) {
    const chosenOne = (this._chosenMarker && this._chosenMarker.latitude == coords.lat && this._chosenMarker.longitude == coords.lng)
    if (chosenOne) { // if the chosen marker exist ✔
      this.map.setView(new L.LatLng(coords.lat, coords.lng), 16);
      const datamarker = L.marker([coords.lat, coords.lng], { icon: this.smallIcon }).bindPopup().addTo(this.map).openPopup(); // open title 
      this.markers.push(datamarker);
    } else {
      this.markers.push(
        L.marker([coords.lat, coords.lng], { icon: this.smallIcon }).addTo(this.map)
      );
    }

    if (open) {
      this.markers[this.markers.length - 1].bindPopup(text).openPopup();
    } else {
      this.markers[this.markers.length - 1].bindPopup(text);
    }
  }

  onMapClick(e) {
    this.lat = e.latlng.lat;
    this.lng = e.latlng.lng;

    this.markercentre.setLatLng(new L.LatLng(e.latlng.lat, e.latlng.lng));
    this.map.panTo(new L.LatLng(e.latlng.lat, e.latlng.lng));
    this.getMycoords();
  }

  getMycoords() {
    this.coords.emit([this.lat, this.lng]);
  }

  receiveMap(map: L, id: number) {
    alert('receiveMap')
    this.map[id] = map;
  }
}

