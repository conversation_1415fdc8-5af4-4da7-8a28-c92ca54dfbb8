import { Compo<PERSON>, ElementR<PERSON>, Inject, <PERSON><PERSON><PERSON>, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AuthService } from '@wph/core/auth';
import { Fournisseur, Offre, OffreCriteria, OffresService, Produit, ProduitCriteria } from "@wph/data-access";
import { AccesClientService, PlateformeService, Principal, Societe, SocieteType, UploadFileServiceService } from '@wph/shared';
import { WorkerService } from '@wph/web/shared';
import * as moment from 'moment';
import { Select2 } from 'ng-select2-component';
import { debounceTime, distinctUntilChanged, map, Observable, of, startWith, switchMap } from 'rxjs';
import Swiper from 'swiper';

type dateInterval = 'day' | 'week' | 'month' | 'year';

@Component({
  selector: 'wph-accueil',
  templateUrl: './accueil.component.html',
  styleUrls: ['./accueil.component.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class AccueilComponent implements OnInit, OnDestroy {
  nombreMesOffresPubliees: any;
  nombreMesCommandes: any;
  currentUser: Principal;
  currentUserRole: string;

  searchOffreForm: FormGroup;

  listeFournisseur: Societe[];
  listeOffres: Offre[] = [];
  listeOffresByDateInterval: Offre[];

  offresByDateIntervalHasMore: boolean = false;
  isLoadingOffresByDateInterval: boolean = true;
  selectedInterval: dateInterval = 'month';

  listeCategorie: any[] = [];
  isPlateformeWinOffre: boolean;
  isPlateformeCommandeWeb: boolean;

  listeOffreCriteria = [
    { label: 'Catégorie', value: 'C' },
    { label: 'Produit', value: 'P' },
    { label: 'Fournisseur', value: 'L' },
  ];

  listeLaboLogoSrc = [
    // { src: 'assets/images/logo/logo_cooper_pharma.png', libelle: 'COOPER PHARMA', id: 39559 },
    { src: 'assets/images/logo/GSK_Logo_Full_Colour_RGB.png', libelle: 'GSK - GLAXO SMITHKLINE MAROC', id: 44096 },
    { src: 'assets/images/logo/hikma-logo.png', libelle: 'HIKMA', id: 38337 },
    { src: 'assets/images/logo/bayer-logo.png', libelle: 'BAYER', id: 43479 },
    { src: 'assets/images/logo/aventis-logo.png', libelle: 'AVENTIS', id: 44051 },
    { src: 'assets/images/logo/zenith-sante.png', libelle: 'ZENITH PHARMA', id: 39734 },
    { src: 'assets/images/logo/LAPR.png', libelle: 'LAPROPHAN', id: 39664 },
  ];

  hideShadowRight: boolean;
  hideShadowLeft: boolean = true;

  hideLogoShadowRight: boolean;
  hideLogoShadowLeft: boolean = true;

  spaceBetween: number;
  slidesPerView: number;

  logoSpaceBetween: number;
  logoSlidesPerView: number;
  currentGrossiste: Societe;

  dynamicObjectFit: string = 'fill';

  prodOnly: boolean;

  private resizeObserver: ResizeObserver;

  targetPlateformeId: number;
  @ViewChild('horizontalSwiperRef', { static: false }) horizontalSwiperRef: ElementRef;
  @ViewChild('laboLogoSwiperRef', { static: false }) laboLogoSwiperRef: ElementRef;
  @ViewChild('resizeTarget', { static: true }) resizeTarget: ElementRef;
  @ViewChild('typeCriteriaSelect', { static: false }) typeCriteriaSelect: Select2;
  @ViewChild('categorieSelect', { static: false }) categorieSelect: Select2;

  constructor(
    @Inject('ENVIROMENT') private env: any,
    private router: Router,
    private ngZone: NgZone,
    private fb: FormBuilder,
    private modalService: NgbModal,
    private authService: AuthService,
    private offresService: OffresService,
    private workerService: WorkerService,
    private plateformeService: PlateformeService,
    private accesClientService: AccesClientService,
    private uploadService: UploadFileServiceService,
  ) {
    this.prodOnly = this.env?.production;

    this.currentUser = this.authService.getPrincipal();
    this.currentUserRole = authService.getUserRole(this.currentUser);
    this.authService.setTitle('Accueil');

    this.isPlateformeWinOffre = this.plateformeService.isPlateForme('WIN_OFFRE');
    this.isPlateformeCommandeWeb = this.plateformeService.isPlateForme('COMMANDE_WEB');

    this.currentGrossiste = this.plateformeService.getCurrentGrossiste();

    if (this.isPlateformeWinOffre) {
      this.targetPlateformeId = 2;
    } else if (this.isPlateformeCommandeWeb) {
      this.targetPlateformeId = 4;
    }
  }

  ngOnInit() {
    this.initSearchOffreForm();
    this.setSwiperSpaceBetween();
    this.getListeFournisseurAvecAcces();
    this.fetchListeCategorie();

    this.isPlateformeWinOffre && this.offresService.getDashboard().subscribe((data => {
      this.nombreMesOffresPubliees = data.nombreMesOffresPubliees;
      this.nombreMesCommandes = data.nombreMesCommandes;
    }));

    this.searchOffres();
    this.applyDateInterval('month');

    this.listenToPageContainerResizeEvents();

    this.dynamicObjectFit = (window.innerWidth < 576 || window.innerWidth > 1366) ? 'fill' : 'cover';
  }

  listenToPageContainerResizeEvents(): void {
    this.resizeObserver = new ResizeObserver(entries => {
      this.ngZone.run(() => {
        for (let entry of entries) {
          const { width, height } = entry.contentRect;
          this.setSwiperSpaceBetween(width);
          this.dynamicObjectFit = (window.innerWidth < 576 || window.innerWidth > 1366) ? 'fill' : 'cover';
        }
      });
    });

    this.resizeObserver.observe(this.resizeTarget.nativeElement);
  }

  closeOpenSelect2Dropdowns(except = null): void {
    if (this.typeCriteriaSelect?.isOpen && this.typeCriteriaSelect !== except) {
      this.typeCriteriaSelect.isOpen = false;
    } else if (this.categorieSelect?.isOpen && this.categorieSelect !== except) {
      this.categorieSelect.isOpen = false;
    }
  }

  initSearchOffreForm(): void {
    this.searchOffreForm = this.fb.group({
      typeCriteria: ['C', []],
      libelleProduit: [null, []],
      categoriePrdId: [null, []]
    })
  }

  get f() {
    return this.searchOffreForm.controls;
  }

  resetOffreForm(): void {
    this.searchOffreForm.reset();
    this.searchOffreForm.markAsPristine();
  }

  submitSearchOffreForm(): void {
    if (this.searchOffreForm.dirty) {
      let params = {};
      const values = this.searchOffreForm.getRawValue();

      values['categoriePrdId'] && (params['categorie'] = values['categoriePrdId']);

      if (values['libelleProduit']) {
        (this.f['typeCriteria']?.value === 'P') && (params['libelleProduit'] = (values['libelleProduit'] as Produit)?.libelleProduit);
        (this.f['typeCriteria']?.value === 'L') && (params['laboratoire'] = JSON.stringify(values['libelleProduit']));
      }

      this.router.navigate(['win-offre/offres/list'], { queryParams: { mode: 'cartes', ...params } });
    }
  }

  setSwiperSpaceBetween(width?: number) {
    if ((width || window.innerWidth) >= 1700) {
      this.slidesPerView = 4, this.logoSlidesPerView = 5;
      this.spaceBetween = -160, this.logoSpaceBetween = -60;
    } else if ((width || window.innerWidth) >= 1200) {
      this.slidesPerView = 3, this.logoSlidesPerView = 4;
      this.spaceBetween = -140, this.logoSpaceBetween = -80;
    } else if ((width || window.innerWidth) >= 992) {
      this.slidesPerView = 2, this.logoSlidesPerView = 4;
      this.spaceBetween = -110, this.logoSpaceBetween = -10;
    } else if ((width || window.innerWidth) >= 768) {
      this.slidesPerView = 2, this.logoSlidesPerView = 5;
      this.spaceBetween = -80, this.logoSpaceBetween = -80;
    } else if ((width || window.innerWidth) >= 690) {
      this.slidesPerView = 2, this.logoSlidesPerView = 3;
      this.spaceBetween = -60, this.logoSpaceBetween = -80;
    } else if ((width || window.innerWidth) >= 576) {
      this.slidesPerView = 1, this.logoSlidesPerView = 3;
      this.spaceBetween = -180, this.logoSpaceBetween = -80;
    } else if ((width || window.innerWidth) > 360) {
      this.slidesPerView = 1, this.logoSlidesPerView = 3;
      this.spaceBetween = 5, this.logoSpaceBetween = 20;
    }  else if ((width || window.innerWidth) < 360) {
      this.slidesPerView = 1, this.logoSlidesPerView = 2;
      this.spaceBetween = 5, this.logoSpaceBetween = 5;
    } else {
      this.slidesPerView = 1, this.logoSlidesPerView = 2;
      this.spaceBetween = -60, this.logoSpaceBetween = -80;
    }
  }


  getListeFournisseurAvecAcces() {
    if (this.authService.hasAnyAuthority(['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT'])) {
      this.accesClientService.getListeAccesClient()
        .pipe(
          map(listeAccesClient =>
            listeAccesClient?.filter(accesClient => !accesClient?.dateDesactivation)
          ),
          map(listeAvecAcces =>
            listeAvecAcces?.map(accesClient => accesClient?.fournisseur)
          )
        )
        .subscribe(res => {
          this.listeFournisseur = res;
        });
    }
  }

  searchOffres() {
    this.offresService.searchOffres({ pageSize: 10, skip: 0 }, new OffreCriteria({ nonExpireesUniquement: 'O', statut: ['P'] })).subscribe(res => {
      if (res?.content?.length) {
        res.content.forEach((offre: Offre) => {
          offre['canPlaceOrder'] = this.canPlaceOrder(offre);
        });
      }
      this.listeOffres = res?.content;
    });
  }

  filterList(searchQuery: string) {
    return this.offresService.searchProduitsPageable(
      new ProduitCriteria({ libelleProduit: searchQuery?.toLowerCase() }), { pageSize: 5, skip: 0 });
  }

  filterLaboratoire(searchQuery: string) {
    const criteria = { raisonSociale: searchQuery, typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE] };

    return this.offresService.searchSociete(criteria, { pageSize: 5, skip: 0 });
  }

  searchOffresByProduit = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap(term => {
        if (term.length > 1) {
          return this.f['typeCriteria']?.value === 'P' ?
            this.filterList(term?.toLowerCase()) : this.filterLaboratoire(term?.toLowerCase());
        }
        return of({ content: [] });
      }),
      map(res => res.content)
    );


  offreFormatteur = (result: Produit) => result ? result?.libelleProduit : null;
  laboFormatteur = (result: Fournisseur) => result ? result?.raisonSociale : null;

  getOffreImage(idHash: string) {
    return idHash ? this.uploadService.fetchUploadedDocument(idHash) : '';
  }

  open(content: any) {
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      console.log(`Closed with: ${result}`);
    }, (reason) => {
      console.log(`Dismissed ${reason}`);
    });
  }

  fetchListeCategorie(): void {
    this.offresService.getListeCategories().subscribe(res => {
      this.listeCategorie = res?.map(cat => {
        return { label: cat?.libelle, value: cat?.id };
      });
    });
  }

  goTo(url: string) {
    this.router.navigateByUrl(url);
  }

  nextSlide() {
    const swiperInstance: Swiper = this.horizontalSwiperRef?.nativeElement?.swiper;

    if (swiperInstance) {
      swiperInstance.slideNext();

      setTimeout(() => {
        this.hideShadowRight = swiperInstance.isEnd;
        this.hideShadowLeft = swiperInstance.isBeginning;
      }, 200);
    }
  }

  prevSlide() {
    const swiperInstance: Swiper = this.horizontalSwiperRef?.nativeElement?.swiper;

    if (swiperInstance) {
      swiperInstance.slidePrev();

      setTimeout(() => {
        this.hideShadowRight = swiperInstance.isEnd;
        this.hideShadowLeft = swiperInstance.isBeginning;
      }, 200);
    }
  }

  nextLogoSlide() {
    const swiperInstance: Swiper = this.laboLogoSwiperRef?.nativeElement?.swiper;

    if (swiperInstance) {
      swiperInstance.slideNext();

      setTimeout(() => {
        this.hideLogoShadowRight = swiperInstance.isEnd;
        this.hideLogoShadowLeft = swiperInstance.isBeginning;
      }, 200);
    }
  }

  prevLogoSlide() {
    const swiperInstance: Swiper = this.laboLogoSwiperRef?.nativeElement?.swiper;

    if (swiperInstance) {
      swiperInstance.slidePrev();

      setTimeout(() => {
        this.hideLogoShadowRight = swiperInstance.isEnd;
        this.hideLogoShadowLeft = swiperInstance.isBeginning;
      }, 200);
    }
  }

  Consulter(id: number) {
    this.router.navigate(['win-offre/offres/edit/' + id], {
      queryParams: { readOnly: 'true', mode: 'cartes' },
    });
  }

  canPlaceOrder(offre: Offre): boolean {
    let canPlaceOrder: boolean;

    if (this.currentUser?.societe?.typeEntreprise === 'FABRIQUANT') {
      const matched = offre?.distributeurs?.filter(
        (dist) => dist?.id === this.currentUser?.societe?.id
      );

      canPlaceOrder = !!matched?.length;
    } else if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
      canPlaceOrder = false;
    }
    else {
      canPlaceOrder = true;
    }

    return canPlaceOrder;
  }

  isCardVisible(isVisible: boolean, offre: Offre): void {
    if (isVisible) {
      this.workerService.stopCountDownOnOffre(offre?.id);
      this.workerService.startCountdownWorker(offre);
    } else {
      this.workerService.stopCountDownOnOffre(offre?.id);
    }
  }

  applyDateInterval(interval: dateInterval) {
    const [startDate, endDate] = this.getDateIntervalLabel(interval);

    this.selectedInterval = interval, this.hideShadowLeft = true;

    const criteria = new OffreCriteria({ dateDebut: startDate, dateFin: endDate, nonExpireesUniquement: 'O', statut: ['P'] });

    this.searchOffresByDateInterval(criteria);
  }

  getDateIntervalLabel(interval: dateInterval) {
    let startDate: moment.Moment, endDate: moment.Moment;

    switch (interval) {
      case 'day':
        startDate = moment().startOf('day'), endDate = moment().endOf('day');
        break;
      case 'week':
        startDate = moment().subtract(7, 'days').startOf('day'), endDate = moment();
        break;
      case 'month':
        startDate = moment().subtract(1, 'month').startOf('day'), endDate = moment();
        break;
      default:
        startDate = moment().startOf('day'), endDate = moment().endOf('day');
        break;
    }

    return [startDate, endDate];
  }

  searchOffresByDateInterval(criteria: OffreCriteria) {
    this.offresService.searchOffres({ pageSize: 10, skip: 0 }, criteria).pipe(
      startWith(() => this.isLoadingOffresByDateInterval = true),
      debounceTime(500)
    ).subscribe({
      next: (res) => {
        this.listeOffresByDateInterval = res?.content;
        this.offresByDateIntervalHasMore = !res?.last;
      },
      error: (err) => {
        console.log(err);
      },
      complete: () => {
        this.isLoadingOffresByDateInterval = false;
      }
    });
  }

  navigateToOffresByDateInterval() {
    const [from, to] = this.getDateIntervalLabel(this.selectedInterval);

    this.router.navigate(['win-offre/offres/list'], {
      queryParams: {
        mode: 'cartes',
        dateDebut: from.format('YYYY-MM-DD'),
        dateFin: to.format('YYYY-MM-DD')
      }
    });
  }

  navigateToOffresWithSelectedCategory(categoryId: number | null) {
    this.router.navigate(['win-offre/offres/list'], {
      queryParams: {
        mode: 'cartes',
        categorie: categoryId
      }
    });
  }

  navigateToOffresWithSelectedLabo(raisonSociale?: string): void {
    this.offresService.searchSociete(
      {
        raisonSociale: raisonSociale,
        typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE]
      },
      { pageSize: 5, skip: 0 }
    ).subscribe(res => {
      if (res?.totalElements) {
        const targetSociete = res?.content?.find(soc => soc?.raisonSociale?.includes(raisonSociale));
        const laboParam = JSON.stringify(targetSociete);

        this.router.navigate(['win-offre/offres/list'], { queryParams: { laboratoire: laboParam } });
      }
    });
  }

  navigateToOffresWithSelectedLaboId(laboId: number): void {
    this.offresService.getSocieteById(laboId).subscribe(res => {
      const laboParam = JSON.stringify(res);

      this.router.navigate(['win-offre/offres/list'], { queryParams: { laboratoire: laboParam } });
    });
  }

  navigateToListeCommandes(): void {
    this.router.navigateByUrl('win-offre/commandes/list');
  }

  ngOnDestroy(): void {
    this.workerService.stopWorker();

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }
}
