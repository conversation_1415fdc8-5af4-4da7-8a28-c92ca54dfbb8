import { DatePipe } from '@angular/common';
import {
  Component,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import {
  CellClickEvent,
  GridDataResult,
  PageChangeEvent,
  SelectionEvent,
} from '@progress/kendo-angular-grid';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AuthService } from '@wph/core/auth';
import {
  Commande,
  CommandeCriteria,
  Fournisseur,
  Offre,
  OffresService,
  Pagination,
} from '@wph/data-access';
import {
  FederationSyndicatService,
  FsCommandeCriteria,
  FsCommandesService,
  GroupeEntreprise,
  PharmacieEntreprise,
} from '@wph/federation-syndicats';
import { AlertService, SocieteType } from '@wph/shared';
import {
  ExportPdf,
  ExportPdfService,
  UserInputService,
  getDynamicPageSize,
} from '@wph/web/shared';
import {
  Observable,
  Subject,
  catchError,
  debounceTime,
  distinctUntilChanged,
  map,
  of,
  switchMap,
  takeUntil,
} from 'rxjs';
import { Avis, AvisDTO, TypeAvis } from '../../../models/avis.model';

@Component({
  selector: 'wph-ag-liste-commandes-individuelle',
  templateUrl: './liste-commandes-individuelle.component.html',
  styleUrls: ['./liste-commandes-individuelle.component.scss'],
})
export class ListeCommandesComponentIndividuelle implements OnInit, OnDestroy {
  unsubscribe$: Subject<boolean> = new Subject<boolean>();

  gridData: GridDataResult;
  displayFilter: boolean;

  searchCriteria: CommandeCriteria = new CommandeCriteria();

  filterForm: FormGroup;
  searchFilter: FormControl = new FormControl();
  startsWith: RegExp = new RegExp('^C(D|MD)-\\d*$', 'i');

  exportPdfRef: ExportPdf;
  commandeSort: SortDescriptor[];
  pageSizes: number[] = [5, 10, 15, 20];
  navigation: Pagination & { originalSortField: string } = {
    pageSize: 15,
    skip: 0,
    originalSortField: '',
  };

  monGroupe: GroupeEntreprise;
  isResponsable: boolean
  selectedFournisseurs: Fournisseur[];

  stautsLabelsValues: any[] = [
    { label: 'Annulé', value: 'A' },
    { label: 'Brouillon', value: 'B' },
    { label: 'Envoyée', value: 'E' },
    { label: 'En Livraison', value: 'EL' },
    { label: 'Livrée', value: 'L' },
    { label: 'Supprimé', value: 'S' },
  ];

  selectedItem: Commande;
  titreOffre: string;
  laboratoire: any;
  modalRef: NgbModalRef;

  @ViewChild('satisfactionModal') satisfactionModal: TemplateRef<any>;
  avis: Avis;
  feedbackSent: boolean = false;

  constructor(
    private router: Router,
    private fb: FormBuilder,
    private datePipe: DatePipe,
    private modalService: NgbModal,
    private authService: AuthService,
    private alertService: AlertService,
    private offresService: OffresService,
    private exportPdfServ: ExportPdfService,
    private userInputServ: UserInputService,
    private commandeService: FsCommandesService,
    private fedSyndicatService: FederationSyndicatService
  ) {
    this.initFilterForm();
  }

  get isInactive$() {
    return this.fedSyndicatService.inactiveAccount$;
  }

  ngOnInit(): void {
    this.buildExport();
    this.listenToSearchFilterChanges();

    this.isResponsable = this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']);

    this.fedSyndicatService.getMyGroupe().then((myGroupe) => {
      this.monGroupe = myGroupe;
      this.fetchCommandeUnitaireIndividuelle();
    });

  }

  fetchCommandeUnitaireIndividuelle(): void {
    this.searchCriteria = new FsCommandeCriteria({
      client: this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']) ? null : this.authService.getPrincipal()?.societe,
    });

    this.searchCommandeIndividuelle();
  }

  searchCommandeIndividuelle() {
    this.offresService.searchCommandes(this.searchCriteria, this.navigation).subscribe(res => {
      this.gridData = {
        data: res?.content,
        total: res?.totalElements,
      };

      this.exportPdfRef.setData(res.content);
    });
  }

  setPageSize(currentHeight?: number): void {
    const dynamicSize = getDynamicPageSize(currentHeight, 44);

    if (dynamicSize !== this.navigation.pageSize) {
      this.navigation.pageSize = dynamicSize;

      this.pageSizes.push(dynamicSize);
      this.pageSizes = this.pageSizes.sort((a, b) => a - b);

      this.searchCommandeIndividuelle();
    }
  }

  initFilterForm(): void {
    this.filterForm = this.fb.group({
      offreur: [null],
      distributeur: [null],
      statut: [null],
      dateDebut: [null],
      dateFin: [null],
      client: [null]
    });
  }

  listenToSearchFilterChanges(): void {
    this.searchFilter.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(200),
        distinctUntilChanged()
      )
      .subscribe((term: string) => {
        const { codeCommande, titre, ...criteria } = this.searchCriteria;
        const criteriaKey = this.startsWith.test(term) ? 'codeCommande' : 'titre';

        this.searchCriteria = new CommandeCriteria({
          ...criteria,
          [criteriaKey]: term,
        });

        this.searchCommandeIndividuelle();
      });
  }

  sortChange(sort: SortDescriptor[]): void {
    this.commandeSort = sort;

    if (
      this.commandeSort &&
      this.commandeSort.length > 0 &&
      this.commandeSort[0].dir
    ) {

      let sortableField = '';
      if (['offreur.raisonSociale', 'titre'].includes(sort[0].field)) {
        sortableField = 'offre.' + sort[0].field;
      } else if (
        sort[0].field?.endsWith('Cmd') &&
        sort[0].field !== 'totalValeurTtcNetteCmd'
      ) {
        sortableField = sort[0].field.replace('Cmd', '');
      } else if (sort[0].field === 'totalValeurTtcNetteCmd') {
        sortableField = 'valeurCmdNetTtc';
      } else {
        sortableField = sort[0].field;
      }

      this.navigation.originalSortField = sort[0].field;
      this.navigation.sortField = sortableField;
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
      this.navigation.originalSortField = '';
    }

    this.fetchCommandeUnitaireIndividuelle()
  }

  OnPageChange(event: number): void {
    this.searchCommandeIndividuelle();
  }

  pageChange(event: PageChangeEvent): void {
    if (
      event.skip !== this.navigation.skip ||
      event.take !== this.navigation.pageSize
    ) {
      this.navigation.skip = event.skip;
      this.navigation.pageSize = event.take;

      this.searchCommandeIndividuelle();
    }
  }

  selectionChange(event: SelectionEvent): void {
    const selectedRows = event.selectedRows?.filter(
      (row) => row?.dataItem?.statut === 'B'
    );
    const deselectedRows = event.deselectedRows?.filter(
      (row) => row?.dataItem?.statut === 'B'
    );

    if (selectedRows?.length) {
      this.selectedItem = selectedRows[0]?.dataItem;
    }

    if (deselectedRows) {
      this.selectedItem = null;
    }
  }

  cellClickHandler(event: CellClickEvent): void {
    if (event?.column.title !== 'Action') {
      this.consulterCommande(event?.dataItem, true);
    }
  }

  consulterCommande(item: Offre | Commande, readOnly = false): void {
    this.router.navigate(
      [`/achats-groupes/commandes/edit/cmd-individuelle`, item?.id],
      { 
        state: { incr: true },
        queryParams: { readOnly, offreId: (item as Commande)?.offre?.id } 
      }
    );
  }

  buildExport(): void {
    this.exportPdfRef = this.exportPdfServ
      .ref<any>()
      .setTitle('Liste des Commandes Individuelles')
      .addColumn('codeCommande', 'Code Commande', { width: 80 })
      .addColumn('*', "Titre de l'offre", {
        width: 200, transform: (value: Commande) => {
          return value?.offre?.titre;
        }
      })
      .addColumn('*', 'Offreur', {
        width: 100,
        transform: (value: Commande) => {
          return value?.offre?.offreur?.raisonSociale;
        },
      })
      .addColumn('*', 'Distributeur', {
        width: 100,
        transform: (value) => {
          return value?.distributeur?.raisonSociale;
        },
      })
      .addColumn('dateCreation', 'Date Création', {
        width: 80,
        transform: (value) => {
          return this.datePipe.transform(value, 'dd/MM/yyyy') || '--/--/----';
        },
      })
      .addColumn('valeurCmdBruteTtc', 'Montant Brut (DH)', {
        width: 80,
        type: 'decimal',
      })
      .addColumn('statut', 'Statut', {
        width: 80, transform: (value: string) => {
          switch (value) {
            case 'S':
              return 'Supprimée'.toUpperCase();
            case 'B':
              return 'BROUILLON';
            case 'AC':
              return 'Acceptée'.toUpperCase();
            default:
              return value;
          }
        }
      })
      .setData([]);
  }

  initializeAvis(item: Offre) {
    this.avis = {
      commentaire: null,
      estResponsable: this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']),
      id: null,
      typeAvis: TypeAvis.Positive,
      entCmdUnitaireMarcheId: item.enteteCommandeId ?? null,
      enteteCommandeConsolideeMarche: null,
      groupeEntreprise: this.authService.getPrincipal()?.groupe ?? null,
      sondeurEntreprise: {
        id: this.authService.getPrincipal()?.societe?.id,
      } as PharmacieEntreprise,
      raison: null,
      laboratoire: 0,
      livraison: 0,
      paiement: 0,
      qualite: 0,
      reduction: 0,
    };
    this.titreOffre = item.titre;
    this.laboratoire = item.laboratoire.raisonSociale;
  }

  checkIfAvisExists(OffreId?: number): Observable<boolean> {
    const sondeurId = this.authService.getPrincipal()?.societe?.id;
    if (!sondeurId) {
      console.error('sondeurId is null');
      return of(false);
    }

    const cmdConsolideeId = null;
    const offreId = OffreId ?? null;

    return this.commandeService
      .getBySondeurIdOffreIdCmdId(sondeurId, offreId, cmdConsolideeId)
      .pipe(
        map((avis: AvisDTO | AvisDTO[]) => {
          // Check if the response is an array
          if (Array.isArray(avis)) {
            return avis.length > 0;
          }
          // Check if the response is a single object
          return avis !== null;
        }),
        catchError((error) => {
          console.error('Error fetching avis:', error);
          return of(false);
        })
      );
  }

  openSatisfactionModal(item: Offre) {
    if (this.feedbackSent) {
      this.alertService.error(
        `Un avis a déjà été envoyé pour cette commande.`,
        'MODAL'
      );
      return;
    }

    this.initializeAvis(item);
    this.modalService.open(this.satisfactionModal, { centered: true });
  }

  soumettreSatisfactionModal(): void {
    if (this.feedbackSent) {
      this.alertService.error(
        `Un avis a déjà été envoyé pour cette commande.`,
        'MODAL'
      );
      return;
    }

    this.userInputServ
      .confirmAlt(
        'Confirmation',
        `Êtes vous sûr de vouloir envoyer cet avis ?`
      )
      .then(
        () => {
          this.commandeService.sauvegarderAvis(this.avis).subscribe(
            () => {
              this.feedbackSent = true;
              this.alertService.successAlt(
                `Votre avis a été envoyé avec succès.`,
                'Avis envoyé',
                'MODAL'
              );
            },
            (error) => {
              const errorMessage =
                error?.error?.message ||
                "Erreur lors de l'enregistrement de l'avis.";
              this.alertService.error(errorMessage, 'MODAL');
              console.error('Error saving avis:', error);
            }
          );
          this.modalService.dismissAll();
        },
        () => null
      );
  }

  annulerOuSupprimerCommandeIndividuelle(item: Commande) {
    if (item?.statut === 'B') {
      this.supprimerCommandeIndividuelle(item);
    } else {
      this.annulerCommandeIndividuelle(item);
    }
  }

  annulerCommandeIndividuelle(item: Commande) {
    this.userInputServ.confirmAlt('Confirmation', 'Êtes vous sûr de vouloir annuler cette commande ?').then(
      (result) => {
        this.offresService.annulerCommandeById(item?.id)
          .subscribe(() => {
            this.fetchCommandeUnitaireIndividuelle();

            this.alertService.successAlt(
              `La commande individuelle a été annulée avec succès.`,
              `Commande Annulée`, 'MODAL'
            );
          });
      }, () => null);
  }

  supprimerCommandeIndividuelle(item: Commande): void {
    this.userInputServ.confirmAlt('Confirmation', 'Êtes vous sûr de vouloir supprimer cette commande ?').then(
      (result) => {
        this.offresService.supprimerCommandeById(item?.id)
          .subscribe(() => {
            this.fetchCommandeUnitaireIndividuelle();

            this.alertService.successAlt(
              `La commande individuelle a été supprimée avec succès.`,
              `Commande Supprimée`, 'MODAL'
            );
          })
      },
      () => null
    );
  }

  filterList(searchQuery: string) {
    return this.offresService.searchSociete({
      raisonSociale: searchQuery,
      typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE],
    });
  }

  filterListePharmacies(searchQuery: string) {
    const navigation: Pagination = { pageSize: 5, skip: 0 };
    return this.fedSyndicatService.searchPharmacieEntreprise(navigation, {
      raisonSociale: searchQuery,
      typeEntreprises: [SocieteType.CLIENT],
    });
  }

  searchFournisseur = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap((term) => {
        if (term.length > 1) {
          return this.filterList(term.toLowerCase());
        }
        return of({ content: [] });
      }),
      map((res) => res?.content.slice(0, 5))
    );

  searchClient = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap((term) => {
        if (term.length > 1) {
          return this.filterListePharmacies(term.toLowerCase());
        }
        return of({ content: [] });
      }),
      map((res) => res?.content.slice(0, 5))
    );

  clientFormatter = (result: { raisonSociale: any }) =>
    result ? `PH. ${result.raisonSociale}` : null;

  fournisseurFormatter = (result: { raisonSociale: any }) =>
    result ? result.raisonSociale : null;

  laboFormatter = (result: { raisonSociale: any }) =>
    result
      ? result.raisonSociale === 'DIVERS'
        ? null
        : result.raisonSociale
      : null;

  appliquerFiltre(): void {
    const payload = this.filterForm?.getRawValue();

    this.searchCriteria = new CommandeCriteria({
      ...this.searchCriteria,
      statut: payload?.statut ? [payload?.statut] : null,
      fournisseur: payload?.distributeur,
      laboratoire: payload?.offreur,
      dateDebutCommande: payload?.dateDebut,
      dateFinCommande: payload?.dateFin,
      client: payload?.client
    });

    this.navigation.skip = 0;

    this.searchCommandeIndividuelle();
  }

  viderFiltre(): void {
    this.filterForm.reset();

    this.navigation.skip = 0;
    this.searchCriteria = new FsCommandeCriteria({
      client: this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']) ? null : this.authService.getPrincipal()?.societe,
    });

    this.searchCommandeIndividuelle();
  }

  openDistributeurs(fournisseurs: Fournisseur[], content) {
    this.selectedFournisseurs = fournisseurs;
    this.modalService
      .open(content, {
        ariaLabelledBy: 'modal-basic-title',
        windowClass: 'fs-cstm-modal',
      })
      .result.then(
        (result) => {
          console.log(`Closed with: ${result}`);
        },
        (reason) => {
          console.log(`Dismissed ${reason}`);
        }
      );
  }

  reload() {
    this.fetchCommandeUnitaireIndividuelle();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
