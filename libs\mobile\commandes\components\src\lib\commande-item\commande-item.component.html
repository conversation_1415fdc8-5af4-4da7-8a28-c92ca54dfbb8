<ion-item>
  <div class="wrapper-item ion-padding-horizontal">
    <div class="inner-wrapper-item">
      <span class="date-cmd">N° : {{ item.codeCommande }}</span>

      <span *ngIf="item.statut !== 'B'" class="badge-cmd">
        <ion-badge class="ion-float-end {{item.statut | statutBadgeColor}}">{{ item.statut | commandeStatut
          }}</ion-badge>
      </span>

      <span *ngIf="item.statut === 'B'" class="date-cmd">
        Créée le : {{ $any(item.dateCreation) | date: 'dd/MM/yyyy HH:mm' }}
      </span>
    </div>

    <div id="date-wrapper" *ngIf="item.statut !== 'B'" class="inner-wrapper-item">
      <span class="date-cmd">Créée le : {{ $any(item.dateCreation) | date: 'dd/MM/yyyy HH:mm' }}</span>

      <span class="date-cmd">
        {{ item.statut | commandeStatut }} le : {{ item.statut === 'N' ? ($any(item.dateConfirmation) | date:
        'dd/MM/yyyy HH:mm') :
        ($any(item.dateAcceptation) || $any(item.dateAnnulation) || $any(item.dateRefus) || $any(item.dateSuppression))
        | date: 'dd/MM/yyyy HH:mm'
        }}
      </span>
    </div>

    <h1 class="title-cmd">{{ item.offre.titre }}</h1>

    <ng-container #simple>
      <div class="inner-wrapper-item">
        <span class="labo-cmd {{item?.offre?.offreur | societeType}}">Offre de : <span>{{
            item?.offre?.offreur?.raisonSociale | uppercase }}</span></span>
        <span class="mnt-cmd">Montant Net :</span>
      </div>

      <div class="inner-wrapper-item">
        <span *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']"
          class="fournisseur-cmd {{item?.distributeur | societeType}}">
          Distribuée par : <span>{{ item.distributeur?.raisonSociale | uppercase }}</span>
        </span>

        <span *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']"
          class="fournisseur-cmd client-label">
          Client : <span>{{ (item.client?.raisonSociale === societe?.raisonSociale ? 'non spécifié' :
            item?.client?.raisonSociale) | uppercase }}</span>
        </span>

        <span class="mnt-cmd">{{ item.valeurCmdNetTtc | number: '1.2-2':'fr-FR' }} Dh</span>
      </div>
    </ng-container>

  </div>

</ion-item>