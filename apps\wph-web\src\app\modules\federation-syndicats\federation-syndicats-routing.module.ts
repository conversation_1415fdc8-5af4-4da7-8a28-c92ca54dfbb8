import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

const routes: Routes = [
    // TODO: Add routes for reused components
    {
        path: 'account',
        loadChildren: () => import('@wph/web/account/pages')
            .then(m => m.WebAccountPagesModule)
    },
    {
        path: '',
        loadChildren: () => import('@wph/federation-syndicats').then(m => m.FederationSyndicatsModule)
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class FederationSyndicatsRoutingModule { }