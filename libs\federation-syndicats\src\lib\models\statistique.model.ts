import { EntrepriseDTO } from "./entreprise.model";
import { GroupeEntreprise } from "./groupe-entreprise.model";
import { PharmacieEntreprise } from "./pharmacie-entreprise.model";


interface IStatisticsCriteriaDTO {
  dateDebut: string; // ISO 8601 date string
  dateFin: string; // ISO 8601 date string
  idMembre: number | null;
  idGroupe: number | null;
  idLabo: number | null; // TODO: AGAD if needed then go for cmdunit.offre....
  listGroupByfields: GroupByField[];
  groupByLabo: boolean | null;
  offreId: number | null;
}

interface IStatistiqueDTO {
  pharmacien: PharmacieEntreprise | null;
  periode: string;
  offreur: EntrepriseDTO | null;
  groupe: GroupeEntreprise | null;
  montantSupportee: number; // Using string to represent BigDecimal
  montantConsomme: number; // Using string to represent BigDecimal
  countCommandeSupportee: number | null;
  countCommandeConsommee: number | null;
  balance: number; // Using string to represent BigDecimal
  countClient: number;
  countCmdPasse: number;
}


export enum GroupByField {
  YEAR = "YEAR",
  YEAR_MONTH = "YEAR_MONTH",
  PHARMACIEN = "PHARMACIEN",
  GROUP = "GROUP",
  OFFREUR = "OFFREUR",
  OFFRE = "OFFRE",
  ADMIN = "ADMIN",
}


export class StatisticsCriteriaDTO implements IStatisticsCriteriaDTO {
  dateDebut: string;
  dateFin: string;
  idMembre: number | null;
  idGroupe: number | null;
  idLabo: number | null;
  listGroupByfields: GroupByField[];
  groupByLabo: boolean | null;
  offreId: number | null;

  constructor(criteria?: Partial<IStatisticsCriteriaDTO>) {
    this.dateDebut = criteria?.dateDebut ?? null;
    this.dateFin = criteria?.dateFin ?? null;
    this.idMembre = criteria?.idMembre ?? null;
    this.idGroupe = criteria?.idGroupe ?? null;
    this.idLabo = criteria?.idLabo ?? null;
    this.listGroupByfields = criteria?.listGroupByfields ?? [];
    this.groupByLabo = criteria?.groupByLabo ?? null;
    this.offreId = criteria?.offreId ?? null;
  }
}



export class StatistiqueDTO implements IStatistiqueDTO {
  pharmacien: PharmacieEntreprise | null;
  periode: string;
  offreur: EntrepriseDTO | null;
  groupe: GroupeEntreprise | null;
  montantSupportee: number;
  montantConsomme: number;
  countCommandeSupportee: number | null;
  countCommandeConsommee: number | null;
  balance: number;
  countClient: number;
  countCmdPasse: number;

  constructor(statistique?: Partial<IStatistiqueDTO>) {
    this.pharmacien = statistique?.pharmacien ?? null;
    this.periode = statistique?.periode ?? null;
    this.offreur = statistique?.offreur ?? null;
    this.groupe = statistique?.groupe ?? null;
    this.montantSupportee = statistique?.montantSupportee ?? null;
    this.montantConsomme = statistique?.montantConsomme ?? null;
    this.countCommandeSupportee = statistique?.countCommandeSupportee ?? null;
    this.countCommandeConsommee = statistique?.countCommandeConsommee ?? null;
    this.balance = statistique?.balance ?? null;
    this.countClient = statistique?.countClient ?? 0;
    this.countCmdPasse = statistique?.countCmdPasse ?? 0;
  }
}
