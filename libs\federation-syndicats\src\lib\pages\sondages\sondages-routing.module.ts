import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { SondagesGroupesListeComponent } from "./liste/sondages-liste-groupes.component";
import { MesSondagesListeComponent } from "./ma-liste/mes-sondages-liste.component";
import { SondagesMembresListeComponent } from "./Membres-liste/sondages-liste-membres.component";
import { AuthoritiesGuard } from "@wph/web/shared";

const routes: Routes = [
    {
        path: 'liste',
        title: 'Liste Sondages',
        component: SondagesGroupesListeComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_NATIONAL', 'ROLE_SUPER_ADMIN'] }
    },
    {
        path: 'membres-liste',
        title: 'Liste Sondages',
        component: SondagesMembresListeComponent,
        canActivate: [AuthoritiesGuard],
        data: { authorities: ['ROLE_RESPONSABLE'] }
    },
    {
        path: 'ma-liste',
        title: 'Liste Sondages',
        component: MesSondagesListeComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class SondagesRoutingModule {}