.card-radius {
  border-radius: var(--winoffre-base-border-radius);
}

.info-card {
  min-height: 220px;
  overflow: hidden;
}

.sw-container {
  width: calc(100% + 10px);
}

::ng-deep #WIN_GROUPE-container {
  .btn-left {
    border-top-left-radius: var(--winoffre-base-border-radius);
    border-bottom-left-radius: var(--winoffre-base-border-radius);
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  
    color: #fff;
    background: var(--wf-primary-400);
    outline: none;
    border: none;
  }
  
  .btn-right {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-right-radius: var(--winoffre-base-border-radius);
  
    color: #fff;
    background: var(--wf-primary-400);
    outline: none;
    border: none;
  }
  
  .date-container {
    padding: 1.8px 10px;
    border: 1px solid var(--wf-primary-400);
  
    font-size: 1rem;
    color: black;
    font-weight: 600;
    cursor: pointer;
  }
}

::ng-deep #FEDERATION_SYNDICAT-container {
  .btn-left {
    border-top-left-radius: var(--winoffre-base-border-radius);
    border-bottom-left-radius: var(--winoffre-base-border-radius);
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  
    color: #fff;
    background: var(--fs-primary-600);
    outline: none;
    border: none;
  }
  
  .btn-right {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-right-radius: var(--winoffre-base-border-radius);
  
    color: #fff;
    background: var(--fs-primary-600);
    outline: none;
    border: none;
  }
  
  .date-container {
    padding: 1.8px 10px;
    border: 1px solid var(--fs-primary-600);
  
    font-size: 1rem;
    color: black;
    font-weight: 600;
    cursor: pointer;
  }
}


.btn-close {
  background-color: #d3dcf7;
  color: #2C3E50;
  border-radius: 50%;
  height: 40px;
  width: 40px;
  border: none;
}

.contact-container {
  background: #eff4fe;
  padding: 8px;
  border-radius: 10px;
}

.icn-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: .95rem;
  border-radius: 8px;
  color: #fff;
  width: 35px;
  height: 35px;
  background: #393c9b;
}

.contact-text {
  font-size: .9rem;
  font-weight: 600;

  a:hover {
    text-decoration: underline;
  }
}

.info-fab {
  z-index: 999;
  position: absolute;
  right: 10px;
  bottom: 55px;
  width: 55px;
  color: #fff;
  padding: 2px 10px;
  border-radius: 10px;
  background: #393c9b;
  border: 5px solid #fff;
  transition: width .3s ease-in-out;
  overflow: hidden;

  &::after {
    content: "Besoin d'aide";
    display: block;
    opacity: 0;
    font-size: 1.5rem;
    white-space: nowrap;
    color: #fff;
    padding-left: 4px;
    transition: opacity 0.3s ease-in-out;
  }

  &:hover {
    width: 210px;
    padding-right: 16px;
    
    &::after {
      opacity: 1;
    }
  }
}


.combo-key {
  font-weight: 600;
  color: #fff;
  background: #393c9b;
  border-radius: 5px;
  padding: 2px 5px;
}