import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl } from '@angular/forms';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { ChartType } from 'chart.js';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { AlertService } from '@wph/shared';
import { FsStatistiqueService } from '../../../services/fs-statistique.service';
import * as moment from 'moment';
import { GroupByField, StatisticsCriteriaDTO } from '../../../models/statistique.model';
import { AuthService } from '@wph/core/auth';
import { ChartOptions } from '@wph/web/shared';
import { GridDataResult } from '@progress/kendo-angular-grid';

@Component({
  selector: 'wph-members-statistiques',
  templateUrl: './members-statistiques.component.html',
  styleUrls: ['./members-statistiques.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class MembresStatistiquesComponent implements OnInit {
  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  lineChartData: any[] = [];
  pieChartData: any = {
    labels: [],
    datasets: []
  };
  gridData:GridDataResult = {data:[],total:0}
  displayedStartDate$ = new BehaviorSubject<Date>(new Date());
  displayedEndDate$ = new BehaviorSubject<Date>(new Date());
  statistiqueCriteria: StatisticsCriteriaDTO;
  startDate = new FormControl();
  endDate = new FormControl();
  now = new Date();
  datePicker = new FormControl();
  datePicker2 = new FormControl();
  minEndDate: NgbDateStruct | null = null;
  lineChartLabels =[]

  chartOptions:ChartOptions;

  public lineChartMembres: string[] = [

  ];

  public lineChartOptions = {
    responsive: true,
    scales: {
      x: {
        title: {
          display: true,
          text: 'Membres'
        }
      },
      y: {
        title: {
          display: true,
          text: 'Valeurs en Dh'
        }
      }
    }
  };

  public lineChartColors: Array<any> = [
    {
      backgroundColor: 'rgba(93, 57, 181, 0.2)',  // Dark violet background
      borderColor: 'rgba(93, 57, 181, 1)',       // Dark violet border
      pointBackgroundColor: 'rgba(93, 57, 181, 1)',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#fff',
      pointHoverBorderColor: 'rgba(93, 57, 181, 0.8)'
    },
    {
      backgroundColor: 'rgba(72, 219, 251, 0.2)', // Light blue background
      borderColor: 'rgba(72, 219, 251, 1)',      // Light blue border
      pointBackgroundColor: 'rgba(72, 219, 251, 1)',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#fff',
      pointHoverBorderColor: 'rgba(72, 219, 251, 0.8)'
    }
  ];

  public lineChartLegend = true;
  public lineChartType: ChartType = 'line';
  public pieChartLabels: string[] = ['Consommé', 'Supporté'];
  public pieChartType: ChartType = 'pie';
  public pieChartOptions: any = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: (tooltipItem: any) => {
            const value = tooltipItem.raw;
            const total = this.pieChartData.datasets[0].data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(2) + '%';
            return `${this.pieChartLabels[tooltipItem.dataIndex]}: ${percentage}`;
          }
        }
      }
    }
  };

  public pieChartColors: Array<any> = [
    {
      backgroundColor: ['rgba(93, 57, 181, 0.8)', 'rgba(72, 219, 251, 0.8)'],
      borderColor: ['rgba(93, 57, 181, 1)', 'rgba(72, 219, 251, 1)'],
      hoverBackgroundColor: ['rgba(93, 57, 181, 1)', 'rgba(72, 219, 251, 1)'],
      hoverBorderColor: ['rgba(93, 57, 181, 1)', 'rgba(72, 219, 251, 1)']
    }
  ];


  getMyStatistique(): void {
     const groupeID = this.authService.getPrincipal()?.groupe?.id;
    this.statistiqueCriteria = new StatisticsCriteriaDTO({
      listGroupByfields: [GroupByField.PHARMACIEN],
      idGroupe: groupeID,
      dateDebut: moment(this.displayedStartDate$.value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      dateFin: moment(this.displayedEndDate$.value).endOf('month').format('YYYY-MM-DD HH:mm:ss'),

    });
    this.fsStatistiqueService.getStatictiques(this.statistiqueCriteria).subscribe(res => {
      res.map(ligne=>{
        const startDate = moment(this.displayedStartDate$.value).startOf('month');
        const endDate = moment(this.displayedEndDate$.value).endOf('month');
        ligne.periode = startDate.isSame(endDate, 'month') ? startDate.format('MM/YYYY') : `${startDate.format('MM/YYYY')} - ${endDate.format('MM/YYYY')}`;

        ligne.pharmacien.raisonSociale = 'PH. ' + ligne.pharmacien.raisonSociale
        ligne.balance = ligne.balance ?? 0;
        ligne.montantConsomme = ligne.montantConsomme ?? 0;
        ligne.montantSupportee = ligne.montantSupportee ?? 0;
      })
      this.gridData.data = res;
      this.gridData.total = res.length;
      this.chartOptions = {

        series: [
          {
            name: "Consomé",
            data: res.map(stat=>stat.montantConsomme ?? 0),
            color: "#007bff"
          },
          {
            name: "Supporté",
            data: res.map(stat=>stat.montantSupportee ?? 0),
            color: "#dc3545"

          },
          {
            name: "Balance",
            data: res.map(stat=>stat.balance ?? 0),
            color: "#6c757d"
          }
        ],
        chart: {
          type: "bar",
          height: 350,
          foreColor: "#000000",
          zoom:{
            enabled: true
          },
          toolbar:{
            show:true,
            autoSelected:"zoom",
            tools:{
              zoomin:true
            }

          }

        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: "30%",
            borderRadius: 10,


            // endingShape: "rounded"
          }
        },
        dataLabels: {
          enabled: false,
          background:{
            foreColor: "#000000"
          }
        },
        stroke: {
          show: true,
          width: 2,
          colors: ["transparent"]
        },
        xaxis: {
          categories: res.map(item => item.pharmacien.raisonSociale),
          // tickPlacement: "on",
          tickPlacement: 'between',

          type: "category",
          tickAmount: 10,

        },
        yaxis: [
          {
            // show:true,
            tickAmount:8,
            title: {
              text: "(montant DH)",
              style: {
                color: "#000000"
              }
            }
          },

        ],
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function(val) {
              return  val + " DH";
            }
          }
        },
        grid:{},
        legend:{},
        annotations: {}
      };
    });

  }

  ngOnInit(): void {
    this.initializeDefaultDates();
    this.setFormControlValues();
    this.updateChartData(this.displayedStartDate$.value, this.displayedEndDate$.value);
    this.listenToDatePickerChanges();
    this.getMyStatistique()
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  initializeDefaultDates(): void {
    const defaultStartDate = new Date();
    defaultStartDate.setMonth(defaultStartDate.getMonth() - 6); // 6 months ago
    const defaultEndDate = new Date();

    this.displayedStartDate$.next(defaultStartDate);
    this.displayedEndDate$.next(defaultEndDate);
  }

  setFormControlValues(): void {
    const startDateValue = this.displayedStartDate$.value;
    const endDateValue = this.displayedEndDate$.value;

    this.startDate.setValue({
      year: startDateValue.getFullYear(),
      month: startDateValue.getMonth() + 1,
      day: startDateValue.getDate()
    });

    this.endDate.setValue({
      year: endDateValue.getFullYear(),
      month: endDateValue.getMonth() + 1,
      day: endDateValue.getDate()
    });
  }

  forwardOneMonth(picker: 'start' | 'end'): void {
    if (picker === 'start') {
      const newDate = new Date(this.displayedStartDate$.value);
      newDate.setMonth(newDate.getMonth() + 1);
      this.displayedStartDate$.next(newDate);
      this.setFormControlValues();
    } else if (picker === 'end') {
      const newDate = new Date(this.displayedEndDate$.value);
      newDate.setMonth(newDate.getMonth() + 1);
      this.displayedEndDate$.next(newDate);
      this.setFormControlValues();
    }
    this.getMyStatistique()

  }

  backwardOneMonth(picker: 'start' | 'end'): void {
    if (picker === 'start') {
      const newDate = new Date(this.displayedStartDate$.value);
      newDate.setMonth(newDate.getMonth() - 1);
      this.displayedStartDate$.next(newDate);
      this.setFormControlValues();
    } else if (picker === 'end') {
      const newDate = new Date(this.displayedEndDate$.value);
      newDate.setMonth(newDate.getMonth() - 1);
      this.displayedEndDate$.next(newDate);
      this.setFormControlValues();
    }
    this.getMyStatistique()
  }
  constructor(private alertService: AlertService,
    private authService:AuthService,
    private fsStatistiqueService:FsStatistiqueService) {
  }

  validateDatePeriod(): void {
    const startDate = this.startDate.value
      ? new Date(
          this.startDate.value.year,
          this.startDate.value.month - 1,
          this.startDate.value.day
        )
      : null;
    const endDate = this.endDate.value
      ? new Date(
          this.endDate.value.year,
          this.endDate.value.month - 1,
          this.endDate.value.day
        )
      : null;

    if (startDate && endDate && startDate > endDate) {
      this.alertService.error(
        'La date de début ne peut pas être postérieure à la date de fin',
        'MODAL'
      );
      return;
    }

    this.displayedStartDate$.next(startDate);
    this.displayedEndDate$.next(endDate);

    this.updateChartData(startDate, endDate);
  }

  updateChartData(startDate: Date, endDate: Date): void {
    if (!startDate || !endDate) {
      this.lineChartData = [];
      this.pieChartData = { labels: [], datasets: [] };
      return;
    }

    // Generate fake data for the given date range
    const periodLength = this.getMonthsBetween(startDate, endDate).length;
    const consomméData = this.generateFakeData(this.lineChartMembres.length);
    const supportéData = this.generateFakeData(this.lineChartMembres.length);

    this.lineChartData = [
      {
        data: consomméData,
        label: 'Consommé',
        borderColor: 'rgba(105,159,177,1)',
        backgroundColor: 'rgba(105,159,177,0.2)',
        pointBackgroundColor: 'rgba(105,159,177,1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(105,159,177,0.8)'
      },
      {
        data: supportéData,
        label: 'Supporté',
        borderColor: 'rgba(255,206,86,1)',
        backgroundColor: 'rgba(255,206,86,0.2)',
        pointBackgroundColor: 'rgba(255,206,86,1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(255,206,86,0.8)'
      }
    ];

    const totalConsommé = consomméData.reduce((a: number, b: number) => a + b, 0);
    const totalSupporté = supportéData.reduce((a: number, b: number) => a + b, 0);

    this.pieChartData = {
      labels: ['Consommé', 'Supporté'],
      datasets: [{
        data: [totalConsommé, totalSupporté],
        backgroundColor: ['rgba(105,159,177,0.8)', 'rgba(255,206,86,0.8)'],
        hoverBackgroundColor: ['rgba(105,159,177,1)', 'rgba(255,206,86,1)'],
        borderColor: ['rgba(105,159,177,1)', 'rgba(255,206,86,1)'],
        hoverBorderColor: ['rgba(105,159,177,1)', 'rgba(255,206,86,1)']
      }]
    };
  }
  getMonthsBetween(startDate: Date, endDate: Date): string[] {
    const months = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    start.setDate(1);
    end.setDate(1);

    while (start <= end) {
      months.push(start.toLocaleString('default', { month: 'long', year: 'numeric' }));
      start.setMonth(start.getMonth() + 1);
    }

    return months;
  }

  generateFakeData(length: number): number[] {
    return Array.from({ length }, () => Math.floor(Math.random() * 100));
  }

  listenToDatePickerChanges(): void {
    this.datePicker.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(res => {
        const selectedDate = new Date(res);

        if (
          (selectedDate?.getMonth() <= this.now?.getMonth()) ||
          (selectedDate?.getFullYear() !== this.now?.getFullYear())
        ) {
          this.displayedStartDate$.next(selectedDate);
          this.updateChartData(this.displayedStartDate$.value, this.displayedEndDate$.value);
        }
      });

    this.datePicker2.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(res => {
        const selectedDate = new Date(res);

        if (
          (selectedDate?.getMonth() <= this.now?.getMonth()) ||
          (selectedDate?.getFullYear() !== this.now?.getFullYear())
        ) {
          this.displayedEndDate$.next(selectedDate);
          this.updateChartData(this.displayedStartDate$.value, this.displayedEndDate$.value);
        }
      });
  }
}
