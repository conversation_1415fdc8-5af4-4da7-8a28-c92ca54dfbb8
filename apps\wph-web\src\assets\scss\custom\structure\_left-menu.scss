//
// menu.scss
//

// Logo
.logo {
  display    : block;
  line-height: $topbar-height;
  width      : $leftbar-width;
  position   : fixed;
  top        : 0;

  span.logo-lg {
      display: block;
      background: #276578 !important;
  }

  span.logo-sm {
      display: none;
  }

  &.logo-light {
      display: block;
  }

  &.logo-dark {
      display: none;
  }
}

// Wrapper
.wrapper {
  height  : 100%;
  overflow: hidden;
  width   : 100%;
}

//Content Page
.content-page {
  margin-left: $leftbar-width;
  overflow   : hidden;
  padding    : $topbar-height 6px 55px;
  min-height : 100vh;

}

.federation-syndicat-content {
    padding    : 0px !important;

}

// Left Side-menu (Default dark)
.leftside-menu {
  width      : $leftbar-width;
  z-index    : 10;
  background : $bg-leftbar;
  bottom     : 0;
  position   : fixed;
  top        : -2px;
  // padding-top: $topbar-height;
  box-shadow : $box-shadow;
  transition : all 0.2s ease-in-out;
}

.left-menu-wrapper {
  background : darken(#33859f, 10%);
  padding-top: $topbar-height;
  &.federation-syndicat-sidebar {
    padding-top: 0 !important;
  }
}

// Side-nav
.side-nav {
  padding   : 10px;
  list-style-type: none;
  overflow-y: auto;
  max-height: calc(100vh - 180px);
  scrollbar-width: none;


  ul {

    list-style-type: none;
  }

  .side-nav-link {
      color        : #ffffff;
      display      : block;
      padding      : 10px 15px;
      font-size    : 1rem;
      font-weight: 600;
      position     : relative;
      // transition: all 0.4s;
      list-style   : none;

      &:hover,
      &:focus,
      &:active {
          color          : $menu-item-hover;
          text-decoration: none;
      }

      span {
          vertical-align: middle;
          color         : white;
      }

      i {
          display       : inline-block;
          line-height   : .5rem;
          margin        : 0 5px 0 0;
          font-size     : 1.1rem;
          vertical-align: middle;
          width         : 20px;
      }
  }

  .menu-arrow {
      transition    : transform .15s;
      position      : absolute;
      right         : 30px;
      display       : inline-block;
      font-family   : 'Material Design Icons';
      text-rendering: auto;
      line-height   : 1.5rem;
      font-size     : 1.1rem;
      transform     : translate(0, 0);

      &:before {
          content: "\203A";
      }
  }

  .badge {
      margin-top: 3px;
  }

  .side-nav-item {
      >a[aria-expanded="true"] {
          >span.menu-arrow {
              transform: rotate(90deg);
          }
      }

      // &.menuitem-active {
      //     > a:not(.collapsed) {
      //         > span.menu-arrow {
      //             transform: rotate(90deg);
      //         }
      //     }
      // }
  }

  .side-nav-title {
      padding       : 12px 30px;
      letter-spacing: .05em;
      pointer-events: none;
      cursor        : default;
      font-size     : 0.6875rem;
      text-transform: uppercase;
      color         : $menu-item;
      font-weight   : $font-weight-bold;
  }

  .menuitem-active {
      >a {
          color: $menu-item-active !important;
      }
  }
}

*[dir="ltr"] {
  .side-nav {
      .side-nav-item {
          .menu-arrow {
              &:before {
                  content: "\F0141" !important;
              }
          }
      }
  }
}

.side-nav-second-level,
.side-nav-third-level,
.side-nav-forth-level {
  padding-left: 0;

  li {
      a {
          padding    : 8px 15px 8px 35px;
          color      : #f5f5f5 !important;
          display    : block;
          position   : relative;
          transition : all 0.4s;
          font-size  : 0.73rem;
          font-weight: 600;

          &:focus,
          &:hover {
              color: $menu-item-hover;
          }

          .menu-arrow {
              line-height: 1.3rem;
          }
      }

      &.active {
          >a {
              color: $menu-item-active;
          }
      }
  }
}

.side-nav-third-level {
  li {
      a {
          padding: 8px 15px 8px 48px;
      }
  }
}

.side-nav-forth-level {
  li {
      a {
          padding: 8px 30px 8px 100px;
      }
  }
}

div.simplebar-content {
    height: calc(100vh - 240px);
    width: 100%;
}

// Enlarge menu
body[data-leftbar-compact-mode="condensed"]:not(.authentication-bg) {
  // min-height: 1600px;

  &[data-layout="detached"] {
      min-height: 950px;
  }



  .wrapper {

      // Side menu
      .leftside-menu {
          // position: absolute;
          // padding-top: 0;
          width      : 70px;
          z-index    : 5;
          padding-top: $topbar-height;

          .simplebar-mask,
          .simplebar-content-wrapper {
              overflow: visible !important;
          }

          .simplebar-scrollbar {
              display: none !important;
          }

          .simplebar-offset {
              bottom: 0 !important;
          }

          .logo {
              width     : 70px;
              z-index   : 1;
              background: $bg-leftbar;
          }
      }
      .custom-leftside-menu {
        padding-top: 0 !important;
      }

      // Help Box
      .help-box {
          display: none;
      }

      // Content Page
      .content-page {
          margin-left: 70px;
          & .federation-syndicat-content{
            top: 0px !important;
          }
      }
      .federation-syndicat-content{
        top: 0px !important;
        margin-top: 0;
      }


      //Navbar & Footer
      .footer {
          left: 70px;
      }

      .navbar-custom {
        left: 0px;
      }
  }

  // Sidebar Menu
  .side-nav {
      .side-nav-title,
      .badge,
      .menu-arrow {
          display: none !important;
      }

      .collapse,
      .collapsing {
          visibility: visible;
          opacity: 1;
          height    : inherit !important;


          .side-nav-second-level,
          .side-nav-third-level,
          .side-nav-forth-level {
              display: none !important;
          }
      }

      .side-nav-item.logout-link {
        position: absolute;
        right: 0;
        left: 10px;
        bottom: 8px;
      }

      .side-nav-item.change-plateforme {
        position: absolute;
        right: 0;
        left: 10px;
        bottom: 68px;
      }

      .side-nav-item.guide-link {
        position: absolute;
        right: 0;
        left: 10px;
        bottom: 128px;
      }

      .side-nav-item {
          position   : relative;
          white-space: nowrap;

          .side-nav-link {
              padding   : auto;
              min-height: 44px;
              transition: none;

              &:hover,
              &:active,
              &:focus {
                  // color: $menu-item-hover;
                  color: #fff;
              }

              i {
                  font-size   : 1.125rem;
                  margin-right: 20px;
                  margin-left : 1px;
              }

              span {
                  display     : none;
                  padding-left: 10px;
                  transition  : display .3s ease !important;
              }
          }

          &.showMenuItemTest {

              .side-nav-link {
                  position    : relative;
                  width       : 260px;
                  transition  : none;
                  border-right: 3px solid;

                  span {
                      display: inline-block;
                  }
              }

              >ul {
                  display   : block;
                  left      : 70px;
                  position  : absolute;
                  width     : 190px;
                  height    : 100% !important;
                  padding   : 5px 0;
                  z-index   : 9999;
                  background: $bg-leftbar;
                  box-shadow: $box-shadow;

                  a {
                      padding : 8px 20px;
                      position: relative;
                      width   : 190px;

                      &:hover {
                          color: $menu-item-hover;
                      }
                  }
              }

              >.collapse,
              >.collapsing {
                  visibility: visible;
                  opacity: 1;
                  height    : auto !important;

                  >ul {
                      display   : block !important;
                      left      : 70px;
                      position  : absolute;
                      width     : 188px;
                      background: white;
                      box-shadow: 2px 0px 1px #0000000f;

                      a {
                          box-shadow: none;
                          padding   : 8px 20px;
                          position  : relative;
                          width     : 190px;
                          z-index   : 6;

                          &:hover {
                              color: $menu-item-hover;
                          }
                      }

                      li {

                          // &:hover {
                          >.collapse {
                              display      : block !important;
                              height       : auto !important;
                              // transition: none !important;

                              >ul {
                                  // display: block;
                                  left       : 189px;
                                  top        : 0;
                                  // position: absolute;
                                  // width: 190px;
                              }
                          }

                          // }
                      }
                  }
              }
          }
      }
  }

  .logo {
      span.logo-lg {
          display: none;
      }

      span.logo-sm {
          display    : none;
          line-height: 70px;
          color      : $primary;
          background: #276578;
      }
  }
}

@include media-breakpoint-down(md) {
  body {
      overflow-x: hidden;
  }

  .leftside-menu {
      box-shadow: $box-shadow;
      display   : none;
      z-index   : 10 !important;
  }

  .custom-leftside-menu{
    display: block;
  }

  .sidebar-enable {
      .leftside-menu {
          display: block;
      }
  }

  .navbar-nav.navbar-right {
      float: right;
  }

  .content-page {
      margin-left: 0 !important;
      padding    : 55px 10px 65px;
  }

  body[data-leftbar-compact-mode="condensed"] {
      .leftside-menu {
          margin-left: 0px;
      }
  }

  .logo {
      span.logo-lg {
          display: block;
      }

      span.logo-sm {
          display: none;
      }
  }
}

/// Help-box
.help-box {
  border-radius   : 5px;
  padding         : 20px;
  margin          : 65px 25px 25px;
  position        : relative;
  background-color: $help-box-light-bg;

  .close-btn {
      position: absolute;
      right   : 10px;
      top     : 10px;
  }
}

// Light
body[data-leftbar-theme="light"] {
  .help-box {
      background-color: $primary;
  }

  .logo {
      &.logo-light {
          display: none;
      }

      &.logo-dark {
          display: block;
      }
  }
}

// Layout -topnav
body[data-layout="topnav"] {
  .content-page {
      margin-left: 0 !important;
      padding    : 0 0 60px 0;
  }
}

// Layout-boxed
body[data-layout-mode="boxed"] {
  background-color: $boxed-layout-bg;

  .wrapper {
      max-width       : $boxed-layout-width;
      margin          : 0 auto;
      background-color: $body-bg;
      box-shadow      : $box-shadow;
  }

  &[data-leftbar-compact-mode="condensed"] {
      .logo {
          position  : relative;
          margin-top: -$topbar-height;
      }
  }
}

// Scrollable Layout

@include media-breakpoint-up(xl) {
  body[data-leftbar-compact-mode="scrollable"]:not([data-layout="topnav"]) {
      padding-bottom: 0;

      .wrapper {
          display: flex;
      }

      .leftside-menu {
          position      : relative;
          min-width     : $leftbar-width;
          max-width     : $leftbar-width;
          // padding-top: 0;
          height        : 100%;
      }

      .logo {
          position  : relative;
          margin-top: 0;
      }

      .content-page {
          margin-left   : 0;
          width         : 100%;
          padding-bottom: 60px;
      }
  }
}

// Detached Left sidebar

body[data-layout="detached"] {
  padding-bottom: 0;

  &:not([data-leftbar-compact-mode="condensed"]) {
      @include media-breakpoint-down(md) {
          .content-page {
              margin-top: 70px;
          }
      }
  }

  @include media-breakpoint-up(lg) {
      .container-fluid {
          max-width: 95%;
      }
  }

  &[data-layout-mode="boxed"] {
      .wrapper {
          max-width: 100%;
      }
  }


  .wrapper {
      display : flex;
      overflow: inherit;
  }

  .content-page {
      margin-left   : 0;
      overflow      : hidden;
      padding       : 0 15px 5px 30px;
      position      : relative;
      margin-right  : -15px;
      width         : 100%;
      padding-bottom: 60px;
  }



  .leftside-menu {
      position   : relative;
      background : $bg-detached-leftbar !important;
      min-width  : $leftbar-width;
      max-width  : $leftbar-width;
      box-shadow : $box-shadow;
      margin-top : 30px;
      padding-top: 55px !important;
      z-index    : 1001 !important;

      .side-nav {
          .side-nav-link {
              color: $menu-item-dark !important;

              &:hover,
              &:focus,
              &:active {
                  color: $menu-item-dark-hover !important;
              }
          }

          .side-nav-second-level,
          .side-nav-third-level,
          .side-nav-forth-level {
              li {
                  a {
                      color: $menu-item-dark;

                      &:focus,
                      &:hover {
                          color: $menu-item-dark-hover;
                      }
                  }

                  &.mm-active {
                      >a {
                          color: $menu-item-dark-active;
                      }
                  }
              }
          }

          .menuitem-active {
              >a {
                  color: $menu-item-dark-active !important;
              }
          }

          .side-nav-title {
              color: $menu-item-dark;
          }
      }
  }

  .leftbar-user {
      background: url("../../../images/waves.png") no-repeat;
      padding   : 30px 20px;
      text-align: center;

      .leftbar-user-name {
          font-weight: 700;
          color      : $dark;
          margin-left: 12px;
          margin-top : 8px;
          display    : block;
      }
  }

  @include media-breakpoint-down(md) {

      &.sidebar-enable {
          .leftside-menu {
              position  : fixed;
              left      : 0;
              overflow-y: hidden;
              margin-top: 70px;
          }
      }

      .wrapper {
          max-width: 100%;
      }

      .content-page {
          margin-left: 0 !important;
          padding    : 0 10px 60px 10px;
      }

      .lang-switch {
          display: none;
      }

      &[data-leftbar-compact-mode="condensed"] {
          &.sidebar-enable {
              .leftside-menu {
                  // margin-top: 0px;
                  height: 100%;
              }
          }
      }
  }

  // Enlarge menu
  &[data-leftbar-compact-mode="condensed"] {
      .wrapper {

          // Side menu
          .leftside-menu {
              max-width: 70px;
              min-width: 70px;
              position : relative;
              bottom   : 0;
              height   : 100% !important;
          }

          .leftbar-user {
              display: none;
          }

          // Content Page
          .content-page {
              margin-left: 0;
          }

          //Footer
          .footer {
              left: 0;
          }
      }

      // Sidebar Menu
      .side-nav {
          .side-nav-item {
              .side-nav-link {

                  &:hover,
                  &:active,
                  &:focus {
                      // color: $menu-item-hover;
                      color: $menu-item-light
                  }
              }

              &:hover {
                  .side-nav-link {
                      background: $primary;
                      color     : $white !important;
                      transition: none;
                  }

                  ul {
                      background: $bg-leftbar-light !important;
                      box-shadow: $box-shadow;

                      a {
                          &:hover {
                              color: $menu-item-light-hover !important;
                          }
                      }
                  }
              }
          }
      }
  }
}

// reaponsive detached mode
@include media-breakpoint-up(lg) {
  body[data-layout="detached"] {
      &[data-leftbar-compact-mode="scrollable"] {
          .wrapper {
              padding-top: 70px;
          }
      }
  }
}

.button-menu-mobile {
  .lines {
      width      : 18px;
      display    : block;
      position   : relative;
      height     : 16px;
      transition : all .5s ease;
      margin-top : 26px;
      margin-left: 10px;
  }

  span {
      height          : 2px;
      width           : 100%;
      background-color: rgba($white, 0.8);
      display         : block;
      margin-bottom   : 5px;
      transition      : transform .5s ease;

      &:nth-of-type(2) {
          width: 24px;
      }
  }

  &.disable-btn {
      display: none;
  }
}

// Light sidebar

body[data-leftbar-theme="light"] {
  .leftside-menu {
      background: $bg-leftbar-light;

      .logo {
          background: $bg-leftbar-light !important;
      }
  }

  .side-nav {
      .side-nav-link {
          color: $menu-item-light;

          &:hover,
          &:focus,
          &:active {
              // color: $menu-item-light-hover;
              color: $menu-item-light;
          }
      }

      .menuitem-active {
          >a {
              color: $menu-item-dark-active !important;
          }
      }

      .side-nav-title {
          color: $menu-item-light;
      }

      .side-nav-second-level,
      .side-nav-third-level,
      .side-nav-forth-level {
          li {
              a {
                  color: $menu-item-light;

                  &:focus,
                  &:hover {
                      color: $menu-item-light-hover;
                  }
              }

              &.active {
                  >a {
                      color: $menu-item-light-active;
                  }
              }
          }
      }
  }

  // Enlarge menu
  &[data-leftbar-compact-mode="condensed"] {

      // Sidebar Menu
      .side-nav {
          .side-nav-item {

              .side-nav-link {

                  &:hover,
                  &:active,
                  &:focus {
                      color: $menu-item-hover;
                  }
              }

              &:hover {
                  .side-nav-link {
                      background: $primary;
                      color     : $white !important;
                      transition: none;
                  }

                  >ul {
                      background: $bg-leftbar-light;
                      box-shadow: $box-shadow;

                      a {
                          &:hover {
                              color: $menu-item-light-hover;
                          }
                      }
                  }

                  >.collapse,
                  >.collapsing {

                      >ul {
                          background: $bg-leftbar-light;

                          a {
                              &:hover {
                                  color: $menu-item-light-hover;
                              }
                          }
                      }
                  }
              }
          }
      }
  }
}

// Dark sidenav
body[data-leftbar-theme="dark"] {
  .leftside-menu {
      background: $bg-leftbar-light;

      .logo {
          background: $bg-leftbar-dark !important;
      }
  }

  .side-nav {
      /*.side-nav-link {
          // color: $menu-item-light;

          &:hover,
          &:focus,
          &:active {
               color: $menu-item-dark-active;
          }
      }*/

      // .menuitem-active {
      //     >a {
      //         color: $menu-item-dark-active !important;
      //     }
      // }

      .side-nav-title {
          color: $sidebar-dark-menu-item;
      }

      .side-nav-second-level,
      .side-nav-third-level,
      .side-nav-forth-level {
          li {
              a {
                  color: $menu-item-light;

                  &:focus,
                  &:hover {
                      color: $sidebar-dark-menu-item-hover;
                  }
              }

              &.active {
                  >a {
                      color: $sidebar-dark-menu-item-active;
                  }
              }
          }
      }
  }

  // Enlarge menu
  &[data-leftbar-compact-mode="condensed"] {

    .bg-inventaire i{
      padding:3px !important;
    }

    .bg-achat i {
      padding: 4px !important;
    }

    .bg-tiers  i {
      padding: 3px !important;
    }

    .bg-statistique i {
          padding-top   : 3px !important;
    }
      // Sidebar Menu
      .side-nav {
          .side-nav-item {

              // .side-nav-link {

              //     // &:hover,
              //     // &:active,
              //     // &:focus {
              //     //     // color: $sidebar-dark-menu-item-hover;
              //     // }
              // }

              &:hover {

                  >ul {
                      background: $bg-leftbar-dark;
                      box-shadow: $box-shadow;

                      // a {
                      //     // &:hover {
                      //     //     // color: $sidebar-dark-menu-item-hover;
                      //     // }
                      // }
                  }

                  >.collapse,
                  >.collapsing {

                      >ul {
                          background: $bg-leftbar-light;
                      }
                  }
              }
          }
      }
  }
}



.side-nav-link.active,
.mm-active>a {
  border-right    : 3.5px solid #1B75BB !important;
  background-color: lighten(#1B75BB, 55%) !important;
  transition      : border-right 0.2s linear !important;

  i {
      color: #1B75BB !important;
  }
}

.has-enough-space {
  bottom: 44px;
}

.bg-item {
  i {
      width        : 28px !important;
      height       : 26px !important;
      text-align   : center;
      line-height  : 100px;
      border-radius: 4px;
  }

  color:white;

  .collapsing {
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s, opacity 0.5s linear;
      height    : 0 !important;
      overflow  : hidden !important;
      // transition: height .2s ease-in !important;
  }
}

// .bg-vente{
//     i{
//         border:.5px solid lighten(#b6473f,22%) !important;
//         background: darken(#b6473f,12%) !important;
//         color:lighten(#b6473f,22%) !important;
//     }

//     .side-nav {
//         .menuitem-active{
//             color: #b6473f !important;
//         }
//     }


// }
// .bg-ventes{
//     i{

//   border:.5px solid lighten(#84429f,22%)  !important;

//   background:darken(#84429f,12%) !important;
//   color:lighten(#84429f,22%);
//     }}
// .bg-achat{
//     i{
//         border:1px solid lighten(#e0a83c,22%) !important;
//         background:darken(#e0a83c,12%) !important;
//         color:lighten(#e0a83c,22%);
//     }
// }
// .bg-tiers{
//     i{
//         border:1px solid lighten(#4677cd,22%) !important;
//         background:darken(#4677cd,12%) !important;
//         color:lighten(#4677cd,22%);
//     }
// }
// .bg-inventaire{
//     i{
//         border:1px solid lighten(#9c27b0,22%) !important;
//         background:darken(#9c27b0,12%) !important;
//         color:lighten(#9c27b0,22%);
//     }
// }
// .bg-statistique{
//     i{
//         border:1px solid lighten(#31b4a9,22%) !important;
//         background:darken(#31b4a9,12%) !important;
//         color:lighten(#31b4a9,22%);
//     }
// }
// .bg-echange{
//     i{
//         border:1px solid lighten(#56508C,22%) !important;
//         background:darken(#56508C,12%) !important;
//         color:lighten(#56508C,22%);
//     }
// }
// .bg-assurance{
//     i{
//         border:1px solid lighten(#00bcd4,22%) !important;
//         background:darken(#00bcd4,12%) !important;
//         color:lighten(#00bcd4,22%);
//     }
// }
// .bg-stock{
//     i{
//         border:1px solid lighten(#8A2F57,22%) !important;
//         background:darken(#8A2F57,12%) !important;
//         color:lighten(#8A2F57,22%);
//     }
// }
// .bg-comptabilite{
//     i{
//         border:1px solid lighten(#2fa042,22%) !important;
//         background:darken(#2fa042,12%) !important;
//         color:lighten(#2fa042,22%);
//     }
// }
// .bg-reporting{
//     i{
//         border:1px solid lighten(#113F59,22%) !important;
//         background:darken(#113F59,12%) !important;
//         color:lighten(#113F59,22%);
//     }
// }
// .bg-produit{
//     i{
//         border:1px solid lighten(#006466,22%) !important;
//         background:darken(#006466,12%) !important;
//         color:lighten(#006466,22%);
//     }
// }
// .bg-config{
//     i{
//         border:1px solid lighten(#4c6f00,22%) !important;
//         background:darken(#4c6f00,12%) !important;
//         color:lighten(#4c6f00,22%);
//     }
// }
// .bg-config-platform{
//     i{
//         border:1px solid lighten(#2196f3,22%) !important;
//         background:darken(#2196f3,12%) !important;
//         color:lighten(#2196f3,22%);
//     }
// }




.bg-vente {
  i {
      padding   : 4px !important;
      background: #b6473f !important;
      color     : white !important;
  }


}

.bg-ventes {
  i {
      padding-top   : 5px !important;
      padding-left  : 4px !important;
      padding-right : 4px !important;
      padding-bottom: 4px !important;
      background    : #84429f !important;
      color         : white;
  }
}

.bg-achat {
  i {
      padding: 5px !important;
      background: #e0a83c !important;
      color     : white;
  }
}

.bg-tiers {
  i {
      padding: 4px !important;
      background: #4677cd !important;
      color     : white;
  }
}

.bg-inventaire {
  i {
      padding: 5px !important;
      background: #9c27b0 !important;
      color     : white;
  }
}

.bg-statistique {
  i {
      padding-top   : 3px !important;
      background: #31b4a9 !important;
      color     : white;
  }
}

.bg-echange {
  i {
      padding-top   : 4px !important;
      background: #56508C !important;
      color     : white;
  }
}

.bg-assurance {
  i {
      padding-top   : 4px !important;
      background: #00bcd4 !important;
      color     : white;
  }
}

.bg-stock {
  i {
      padding-top   : 4px !important;
      background: #8A2F57 !important;
      color     : white;
  }
}

.bg-comptabilite {
  i {
      padding-top   : 4px !important;
      background: #2fa042 !important;
      color     : white;
  }
}

.bg-reporting {
  i {
      padding-top   : 4px !important;
      background: #113F59 !important;
      color     : white;
  }
}

.bg-produit {
  i {

      padding-top   : 4px !important;
      background: #006466 !important;
      color     : white;
  }
}

.bg-config {
  i {
      padding-top   : 4px !important;
      background: #4c6f00 !important;
      color     : white;
  }
}

.bg-config-platform {
  i {
      padding-top   : 4px !important;
      background: #2196f3 !important;
      color     : white;
  }
}

.bg-border {
    border-color: darken(#276578, 5) !important;
}
