import { Component, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { GridDataResult, PageChangeEvent, CellClickEvent } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { Pagination, Offre, OffresService } from "@wph/data-access";
import { FederationSyndicatService, FsCommandeCriteria, FsCommandesService, FsOffreCriteria, FsOffreService, GroupeEntreprise } from "@wph/federation-syndicats";
import { PlateformeService, SocieteType, UploadFileServiceService } from "@wph/shared";
import { debounceTime, distinctUntilChanged, map, Observable, of, Subject, switchMap, takeUntil } from "rxjs";
import { GroupeEntrepriseCriteria } from "../../../models/groupe-entreprise-criteria.model";
import { SelectedPlateforme } from "@wph/web/layout";

@Component({
    selector: 'wph-commande-groupe-admin',
    templateUrl: './commande-groupe-admin.component.html',
    styleUrls: ['./commande-groupe-admin.component.scss']
})
export class CommandeGroupeAdminComponent implements OnInit, OnDestroy {
    unsubscribe$: Subject<boolean> = new Subject<boolean>();
    pageSizes: number[] = [5, 10, 15, 20];
    navigation: Pagination = {
        pageSize: 15,
        skip: 0,
        sortMethod: 'desc'
    };
    displayFilter: boolean;
    filterForm: FormGroup;
    gridData: GridDataResult = { total: 0, data: [] };
    commandeSort: SortDescriptor[] = [];
    searchCriteria: FsCommandeCriteria = new FsCommandeCriteria();
    offres: Offre[] = [];
    selectedOffre: Offre | null = null;
    searchControl: FormControl = new FormControl('');
    filteredOffres: Offre[] = [];
    stautsLabelsValues: any[] = [
        { "label": 'Tout', "value": null },
        { "label": "Ouverte", "value": "ACCEPTEE" },
        { "label": "Annulée", "value": "ANNULEE" },
        { "label": "Cloturée", "value": "CLOTUREE" },
        { "label": "Commandé", "value": "VALIDEE" },
        { "label": "Envoyée", "value": "ENVOYEE" },
        { "label": "En Attente", "value": "EN_ATTENTE" },
        { "label": "En Livraison", "value": "EN_LIVRAISON" },
        { "label": "Fin de saisie", "value": "FIN_SAISIE" },
        { "label": "Livrée", "value": "LIVREE" },
        { "label": "Réfusée", "value": "REFUSEE" },
    ];

    currentPlateforme: SelectedPlateforme;

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private offreService: OffresService,
        private fsOffresService: FsOffreService,
        private plateformeService: PlateformeService,
        private fsCommandeService: FsCommandesService,
        private uploadService: UploadFileServiceService,
        private fedSyndicatService: FederationSyndicatService
    ) {
        this.initFilterForm();
        this.currentPlateforme = this.plateformeService.getCurrentPlateforme();
    }

    ngOnInit(): void {
        this.searchOffres();
        this.listenToSearchFilterChanges();
    }

    listenToSearchFilterChanges(): void {
        this.searchControl.valueChanges.pipe(
            takeUntil(this.unsubscribe$),
            debounceTime(300),
            distinctUntilChanged()
        ).subscribe(value => {
            this.selectedOffre = null;
            this.filteredOffres = this.offres.filter(offre =>
                offre.titre.toLowerCase().includes(value.toLowerCase())
            );
        });
    }

    initFilterForm(): void {
        this.filterForm = this.fb.group({
            offreur: [null],
            distributeur: [null],
            statut: [null],
            dateDebut: [null],
            dateFin: [null],
            client: [null],
            groupeEntreprise: [null]
        });
    }

    searchOffres(): void {
        const criteria = new FsOffreCriteria({ statut: ['P', 'C'], natureOffre: 'G' });

        const pagination = { pageSize: 100, skip: 0, sortMethod: 'desc' };

        this.fsOffresService.searchFsOffres(pagination, criteria).subscribe((response) => {
            this.offres = (response?.content as Offre[])?.filter(offre => offre?.natureOffre === 'G');
            this.filteredOffres = [...this.offres];
        });
    }

    updateGridData(): void {
        this.gridData = { data: [], total: 0 };
        this.searchCriteria = new FsCommandeCriteria({ 
            titreOffre: this.selectedOffre?.titre, 
            offre: { id: this.selectedOffre?.id } 
        });
        this.searchCommandeConsolidee();
    }

    pageChange(event: PageChangeEvent): void {
        this.navigation.skip = event.skip;
        this.navigation.pageSize = event.take;
        if (this.selectedOffre) {
            this.updateGridData();
        }
    }

    sortChange(sort: SortDescriptor[]): void {
        this.commandeSort = sort;
        if (sort.length) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        if (this.selectedOffre) {
            this.updateGridData();
        }
    }

    searchCommandeConsolidee() {
        this.fsCommandeService.searchCommandesConsolidee(this.navigation, this.searchCriteria).subscribe(res => {
            this.gridData = {
                data: res?.content,
                total: res?.totalElements,
            };
        });
    }

    cellClickHandler($event: CellClickEvent) {
        const commande = $event?.dataItem;
        this.consulterCommande(commande?.id, commande?.enteteCommandeId);
    }

    consulterCommande(offreId: number, cmdId: number): void {
        this.router.navigate(['/achats-groupes/commandes/edit/cmd-groupe', cmdId], { queryParams: { readOnly: true, offreId: offreId } });
    }

    toggleOffre(offre: Offre): void {
        this.selectedOffre = this.selectedOffre === offre ? null : offre;
        if (this.selectedOffre) {
            this.updateGridData();
        }
    }

    OnPageChange(event: number): void {
        if (this.selectedOffre) {
            this.updateGridData();
        }
    }

    getImageUrl(offre: Offre): string | null {
        return offre?.docImageOffre?.idhash
            ? this.uploadService.fetchUploadedDocument(offre.docImageOffre.idhash)
            : null;
    }

    filterList(searchQuery: string) {
        return this.offreService.searchSociete({
            raisonSociale: searchQuery,
            typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE],
        });
    }

    filterListeGroupe(searchQuery: string) {
        const criteria: GroupeEntrepriseCriteria = new GroupeEntrepriseCriteria({
            raisonSociale: searchQuery,
            typeEntreprises: [SocieteType.GROUPE_CLIENT]
        });

        return this.fedSyndicatService.searchGroupeEntreprise({ pageSize: 5, skip: 0 }, criteria);
    }

    filterListePharmacies(searchQuery: string) {
        const navigation: Pagination = { pageSize: 5, skip: 0 };
        return this.fedSyndicatService.searchPharmacieEntreprise(navigation, {
            raisonSociale: searchQuery,
            typeEntreprises: [SocieteType.CLIENT],
        });
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    searchClient = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterListePharmacies(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    searchGroupeEntreprise = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterListeGroupe(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    clientFormatter = (result: { raisonSociale: any }) =>
        result ? `PH. ${result.raisonSociale}` : null;

    fournisseurFormatter = (result: { raisonSociale: any }) =>
        result ? result.raisonSociale : null;

    groupeEntrepriseFormatter = (result: GroupeEntreprise) => result ? result?.raisonSociale : null;

    laboFormatter = (result: { raisonSociale: any }) =>
        result
            ? result.raisonSociale === 'DIVERS'
                ? null
                : result.raisonSociale
            : null;

    appliquerFiltre(): void {
        const payload = this.filterForm?.getRawValue();

        this.searchCriteria = new FsCommandeCriteria({
            titreOffre: this.selectedOffre?.titre,
            etatCommande: payload?.statut ? [payload?.statut] : null,
            distributeurId: payload?.distributeur ? payload?.distributeur?.id : null,
            offreurId: payload?.offreur ? payload?.offreur?.id : null,
            dateCreationDebut: payload?.dateDebut,
            dateCreationFin: payload?.dateFin,
            groupeEntreprise: payload?.groupeEntreprise ? payload?.groupeEntreprise : null,
        });

        this.navigation.skip = 0;

        this.searchCommandeConsolidee();
    }

    viderFiltre(): void {
        this.filterForm.reset();

        this.navigation.skip = 0;
        this.searchCriteria = new FsCommandeCriteria({
            titreOffre: this.selectedOffre?.titre
        });

        this.searchCommandeConsolidee();
    }


    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }
}