<!-- Start Of Header -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 col ps-2 d-md-block d-none">Liste des Factures</h4>

        <div class="col px-0 py-2 py-md-0">
            <div class="row justify-content-end align-items-center">
                <button (click)="openModal(factureFilter)" type="button" class="btn btn-sm btn-info m-1 ml-2">
                    <i class="mdi mdi-filter-variant"></i>
                    Filtrer
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="card m-0 p-0">
    <kendo-grid [selectable]="false" [pageSize]="navigation.pageSize" [skip]="navigation.skip" [data]="listeFactures"
        [pageable]="{
        buttonCount: 5,
        info: true,
        type: 'numeric',
        pageSizes: pageSizes,
        previousNext: true,
        position: 'bottom'
      }" [resizable]="true" [sort]="gridSort" [sortable]="{mode: 'single'}" (pageChange)="pageChange($event)"
        (sortChange)="gridSortChange($event)" style="height: calc(100vh - 123px);">
        <kendo-grid-column media="(max-width: 768px)" title="Liste des Factures">
            <ng-template kendoGridCellTemplate let-dataItem>
                <!---  Mobile Column Template  --->
                <dl>
                    <dt class="my-2 limited-width">N° Facture: <span>{{ dataItem?.codeFacture }}</span></dt>

                    <dt class="my-2 limited-width">Date Facture: <span>{{ dataItem?.dateFacture | date: 'dd-MM-yyyy'
                            }}</span></dt>

                    <dt class="my-2 limited-width">Date Échéance: <span>{{ dataItem?.dateEcheance | date: 'dd-MM-yyy'
                            }}</span></dt>

                    <dt class="my-2 limited-width">TVA: <span>{{ dataItem?.montantTva | number:
                            "1.2-2":"fr-FR"
                            }}</span></dt>

                    <dt class="my-2 limited-width">Remise: <span>{{ dataItem?.montantRemise | number:
                            "1.2-2":"fr-FR"
                            }}</span></dt>

                    <dt class="my-2 limited-width">Brut TTC: <span>{{ dataItem?.montantBrutTtc | number:
                            "1.2-2":"fr-FR"
                            }}</span></dt>

                    <dt class="action-btns">
                        <div class="d-flex row mx-0 justify-content-start">
                            <button (click)="navigateToDetailFacture(dataItem?.id)"
                                class="circle circle-alt btn btn-warning text-white" title="Consulter Facture">
                                <i class="mdi mdi-eye"></i>
                            </button>
                        </div>
                    </dt>
                </dl>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="codeFacture" title="N° Facture" [width]="120">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ dataItem?.codeFacture }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="dateFacture" title="Date Facture" [width]="180">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ dataItem?.dateFacture | date: 'dd-MM-yyy' }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" field="dateEcheance" title="Date Échéance" [width]="180">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ dataItem?.dateEcheance | date: 'dd-MM-yyyy' }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" class="text-right" field="montantTva" title="TVA" [width]="150">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ dataItem?.montantTva | number: "1.2-2":"fr-FR" }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" class="text-right" field="montantRemise" title="Remise"
            [width]="150">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ dataItem?.montantRemise | number: "1.2-2":"fr-FR" }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" class="text-right" field="montantBrutTtc" title="Brut TTC"
            [width]="150">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ dataItem?.montantBrutTtc | number: "1.2-2":"fr-FR" }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" class="text-right" [sortable]="false" title="Net TTC"
            [width]="150">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ (dataItem?.montantBrutTtc - dataItem?.montantRemise) | number: "1.2-2":"fr-FR" }}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column media="(min-width: 769px)" class="text-center" title="Action" [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem>
                <button (click)="navigateToDetailFacture(dataItem?.id)" class="s2 circle btn btn-warning text-white"
                    title="Consulter Facture">
                    <i class="mdi mdi-eye"></i>
                </button>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de" pagerItemsPerPage="éléments par page"></kendo-grid-messages>

        <ng-template kendoGridNoRecordsTemplate>
            <span *ngIf="!listeFactures?.total">Aucun résultat trouvé.</span>
        </ng-template>
    </kendo-grid>
</div>

<ng-template #factureFilter let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Filtrer</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <form [formGroup]="factureFilterForm" (ngSubmit)="appliquerFiltre()">
        <div class="modal-body px-0">

            <div class="col-12 mx-0 px-2">
                <label for="codeFacture" class="col-12 col-form-label text-left px-0">N° Facture</label>
                <input id="codeFacture" type="text" formControlName="codeFacture"
                    class="form-control form-control-md bg-white">
            </div>

            <div class="col-12 mx-0 px-0 mt-2">
                <label for="dateDebut" class="col-12 col-form-label text-left">Date Facture Début</label>
                <div class="col-12 input-group">
                    <input type="text" [readOnly]="true" name="dateFactureDebut"
                        class="form-control form-control-md bg-white" id="dateDebut" ngbDatepicker
                        #drange1="ngbDatepicker" (click)="drange1.toggle()" formControlName="dateFactureDebut">

                    <div class="input-group-append">
                        <button type="button" (click)="drange1.toggle()"
                            class="btn btn-md btn-light text-dark btn-outline-light calendar">
                            <i class="mdi mdi-calendar"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-12 mx-0 px-0 mt-2">
                <label for="dateFin" class="col-12 col-form-label text-left">Date Facture Fin</label>
                <div class="col-12 input-group">
                    <input type="text" [readOnly]="true" name="dateFactureFin"
                        class="form-control form-control-md bg-white" id="dateFin" ngbDatepicker
                        #drange2="ngbDatepicker" (click)="drange2.toggle()" formControlName="dateFactureFin">

                    <div class="input-group-append">
                        <button type="button" (click)="drange2.toggle()"
                            class="btn btn-md btn-light text-dark btn-outline-light calendar">
                            <i class="mdi mdi-calendar"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light">Fermer</button>
            <button type="button" class="btn btn-secondary text-white" (click)="vider(); modal.dismiss()">Vider</button>
            <button type="button" type="submit" class="btn btn-primary ml-1 text-white">Rechercher</button>
        </div>
    </form>
</ng-template>