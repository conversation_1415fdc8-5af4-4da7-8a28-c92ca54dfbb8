import { Catalogue } from './catalogue.model';

export class Fournisseur {
    id?: number;
    catalogue?: Catalogue;
    code?: string;
    raisonSociale?: string;
    nomResponsable?: string;
    libelle?: string;
    typeFrn?: string[];
    typeEntreprise?: string | string[];
    typeEntreprises?: string[];
    adresse?: string;
    localite?: string;
    segmentEntreprise?: string;
    ville?: string;
    numIce?: string;
    email?: string;
    noeud?: any;
    gsm?: string; 
    gsm1?: string;
    adresse2?: string;
    isEnrolled?: boolean;

    constructor(partialFournData?: Partial<Fournisseur>) {
        this.id = partialFournData?.id || null;
        this.catalogue = partialFournData?.catalogue || null;
        this.code = partialFournData?.code || null;
        this.raisonSociale = partialFournData?.raisonSociale || null;
        this.nomResponsable = partialFournData?.nomResponsable || null;
        this.libelle = partialFournData?.libelle || null;
        this.typeFrn = partialFournData?.typeFrn || null;
        this.typeEntreprise = partialFournData?.typeEntreprise || null;
        this.typeEntreprises = partialFournData?.typeEntreprises || null;
        this.adresse = partialFournData?.adresse || null;
        this.adresse2 = partialFournData?.adresse2 || null;
        this.localite = partialFournData?.localite || null;
        this.segmentEntreprise = partialFournData?.segmentEntreprise || null;
        this.ville = partialFournData?.ville || null;
        this.numIce = partialFournData?.numIce || null;
        this.email = partialFournData?.email || null;
        this.noeud = partialFournData?.noeud || null;
        this.isEnrolled = partialFournData?.isEnrolled || null;
        this.gsm = partialFournData?.gsm || null;
        this.gsm1 = partialFournData?.gsm1 || null;
    }
}

export class FournisseurDropdownItem {
    label?: string;
    value?: Fournisseur;
    code?: string;
}
