import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Template<PERSON><PERSON> } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { PageChangeEvent } from "@progress/kendo-angular-pager";
import { SortDescriptor } from "@progress/kendo-data-query";
import { DemandeAccesClient, EtatDemandeAcces, Fournisseur, OffresService, Pagination, TacClientAssociationMatches } from "@wph/data-access";
import { AccesClientService, AlertService, ClientFournisseur, ClientView, ClientViewCriteria, SocieteType } from "@wph/shared";
import { BootstrapColorClasses, UserInputService } from "@wph/web/shared";
import { phoneValidator } from "libs/federation-syndicats/src/lib/validators/phone-validator";
import { debounceTime, distinctUntilChanged, map, Observable, of, Subject, switchMap, takeUntil } from "rxjs";
import { DemandeAccesClientService } from "../../services/demande-acces-client.service";
import { AuthService } from "@wph/core/auth";

@Component({
    selector: 'wph-demandes-acces',
    templateUrl: './demandes-acces.component.html',
    styleUrls: ['./demandes-acces.component.scss']
})
export class DemandesAccesComponent implements OnInit, OnDestroy {
    sort: SortDescriptor[];
    unsubscribe$: Subject<boolean> = new Subject<boolean>();
    searchControl: FormControl = new FormControl();
    displayFilter: boolean = false;
    startsWith: RegExp = new RegExp('^[0-9]*$');
    navigation: Pagination = { pageSize: 15, skip: 0 }
    clientGroupeData: GridDataResult = { data: [], total: 0 };
    pageSizes: number[] = [5, 10, 15, 20];

    searchClientLocal: Fournisseur | string = null;
    filterForm: FormGroup | null = null;
    clientLocalForm: FormGroup | null = null;
    selectedPharmacie: Fournisseur | null = null;
    searchCriteria: ClientViewCriteria | null = null;
    currentSocieteCodeSite: number | null = null;
    etatDemandeAccesOfSelectedPharmacie: EtatDemandeAcces | null = null;
    rechercheClientLocalSubmitted: boolean = false;
    rechercheClientLocalForm: FormGroup | null = null;
    clientLocalTacGridData: GridDataResult = { data: [], total: 0 };
    selectedClientLocalTac: TacClientAssociationMatches | null = null;

    constructor(
        private fb: FormBuilder,
        private modalService: NgbModal,
        private authService: AuthService,
        private alertService: AlertService,
        private offresService: OffresService,
        private userInputService: UserInputService,
        private accesClientService: AccesClientService,
        private demandeAccesService: DemandeAccesClientService,
    ) {
        this.filterForm = this.fb.group({
            ville: [null],
            localite: [null],
            raisonSociale: [null],
            nomResponsable: [null],
        });

        this.searchCriteria = new ClientViewCriteria({
            segmentEntreprise: 'O',
            isClientHasTransco: true,
            ignoreClassification: false,
            fournisseurId: this.authService.getPrincipal()?.societe?.id
        });

        this.rechercheClientLocalForm = this.fb.group({
            code_client: [null],
            code_site: [this.authService.getPrincipal()?.societe?.noeud?.codeSite, Validators.required],
            nom_pharmacien: [null, Validators.required],
            raison_sociale: [null, Validators.required],
            ville: [null, Validators.required],
        })

        this.currentSocieteCodeSite = this.authService.getPrincipal()?.societe?.noeud?.codeSite;
    }

    ngOnInit(): void {
        this.searchClientGroupe();
        this.listenToSearchControlChanges();
    }

    searchClientGroupe(): void {
        this.accesClientService.getListePharmacies(this.searchCriteria, this.navigation).subscribe(res => {
            this.clientGroupeData = { data: res?.content, total: res?.totalElements };
        });
    }

    listenToSearchControlChanges(): void {
        this.searchControl.valueChanges.pipe(
            takeUntil(this.unsubscribe$),
            debounceTime(200),
            distinctUntilChanged(),
            map(term => {
                this.navigation.skip = 0;

                if (term.length > 1) {
                    this.filterForm.get('raisonSociale').setValue(term);
                    this.appliquerFiltrer();
                } else {
                    const { nomResponsable, localite, ville } = this.filterForm.getRawValue();
                    this.filterForm.get('raisonSociale').setValue(null);

                    this.appliquerFiltrer(!!nomResponsable || !!localite || !!ville);
                }
            })
        ).subscribe();
    }

    async openModal(content: TemplateRef<any>, item: Fournisseur, size = 'xl') {
        if (!item) {
            await this.verifyExistingAccesClient(this.selectedClientLocalTac).then(
                (existingClient) => {
                    item = existingClient?.clientGroupe;
                },
                () => {
                    this.alertService.error(`Il se peut que le client <b>${this.selectedClientLocalTac?.raison_sociale}</b> a déjà accès. Si ce n'est pas le cas, veuillez contacter SOPHATEL.`, 'MODAL');
                }
            );

            if (!item) return;
        }

        this.selectedPharmacie = item;
        this.clientLocalForm = this.fb.group(new ClientFournisseur());

        this.demandeAccesService.getDemandeAccessByCodeGroupe(this.currentSocieteCodeSite, this.selectedPharmacie?.code).subscribe(res => {
            if (res) {
                this.clientLocalForm.patchValue({
                    id: res?.id,
                    gsm: res?.gsm,
                    email: res?.email,
                    code: res?.codeClientLocal,
                    adresse: res?.adresseLocal,
                    localite: res?.localiteLocal,
                    ville: res?.villeLocal,
                    classification: res?.classification,
                    raisonSociale: res?.raisonSocialeLocal,
                    nomPharmacien: res?.nomPharmacienLocal,
                });

                this.etatDemandeAccesOfSelectedPharmacie = res?.etatDemandeAcces;
            }

            this.clientLocalForm.get('email').setValidators(Validators.email);
            this.clientLocalForm.get('raisonSociale').setValidators(Validators.required);
            this.clientLocalForm.get('gsm').setValidators([Validators.required, phoneValidator()]);
        });

        this.modalService.open(content, { modalDialogClass: 'demande-acces-modal', animation: false, windowClass: 'custom-modal-width' }).result.then(
            () => this.resetDemandeAccesModalData(),
            () => this.resetDemandeAccesModalData()
        );
    }

    openRechercheClientLocalModal(content: TemplateRef<any>, size = 'lg'): void {
        this.modalService.open(content, { size, centered: true, modalDialogClass: 'fs-radius-modal', windowClass: 'custom-modal-width' }).result.then(
            () => { this.rechercheClientLocalSubmitted = false; },
            () => { this.rechercheClientLocalSubmitted = false; }
        );
    }

    openFilterModal(content: TemplateRef<any>, size = 'lg'): void {
        this.modalService.open(content, { size, modalDialogClass: 'fs-radius-modal' }).result.then(
            () => null,
            () => null
        );
    }

    verifyExistingAccesClient(targetClientLocalTac: TacClientAssociationMatches): Promise<ClientView | null> {
        const criteria = new ClientViewCriteria({
            segmentEntreprise: 'O',
            isClientHasTransco: true,
            ignoreClassification: true,
            code: targetClientLocalTac?.code_client_groupe?.startsWith('G') ? targetClientLocalTac?.code_client_groupe : `G${targetClientLocalTac?.code_client_groupe}`,
            fournisseurId: this.authService.getPrincipal()?.societe?.id
        });

        return new Promise<ClientView | null>((resolve, reject) => {
            this.accesClientService.getListePharmacies(criteria, { pageSize: 15, skip: 0 }).subscribe(res => {
                if (res?.content?.length) {
                    resolve(res.content[0]);
                } else {
                    reject(null);
                }
            });
        });
    }

    resetDemandeAccesModalData(): void {
        this.clientLocalForm.reset();
        this.selectedPharmacie = this.searchClientLocal = this.etatDemandeAccesOfSelectedPharmacie = null;
    }

    resetClientLocalForm(): void {
        this.clientLocalForm.reset();
        this.searchClientLocal = null;
    }

    sortChange(sort: SortDescriptor[]) {
        this.sort = sort;
        if (this.sort && this.sort.length > 0 && this.sort[0].dir) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }

        this.searchClientGroupe();
    }

    pageChange(event: PageChangeEvent) {
        if ((event.skip !== this.navigation.skip) || (event?.take !== this.navigation.pageSize)) {
            this.navigation.skip = event.skip;
            this.searchClientGroupe();
        }
    }

    clientLocalValueChange(value: Fournisseur): void {
        this.searchClientLocal = value;

        if (typeof value === 'object') {
            this.clientLocalForm.setValue(new ClientFournisseur(value));
        }
    }

    envoyerDemandeAccess(modal: any): void {
        this.userInputService.confirm('Confirmation', `Êtes-vous sûr de vouloir envoyer la demande d'accès pour le client: <b>${this.clientLocalForm.get('raisonSociale').value}</b>`, 'Envoyer', 'Abandonner', BootstrapColorClasses.primary).then(
            () => {
                const clientLocalData: ClientFournisseur = this.clientLocalForm.getRawValue();

                const payload: DemandeAccesClient = new DemandeAccesClient({
                    id: this.etatDemandeAccesOfSelectedPharmacie === 'E' ? clientLocalData?.id : null,
                    fournisseur: this.authService.getPrincipal()?.societe,
                    gsm: clientLocalData?.gsm,
                    codeSite: this.currentSocieteCodeSite,
                    email: clientLocalData?.email,
                    classification: clientLocalData?.classification,
                    clientGroupe: this.selectedPharmacie,
                    adresseLocal: clientLocalData?.adresse,
                    adresseGroupe: this.selectedPharmacie?.adresse,
                    localiteLocal: clientLocalData?.localite,
                    localiteGroupe: this.selectedPharmacie?.localite,
                    villeLocal: clientLocalData?.ville,
                    villeGroupe: this.selectedPharmacie?.ville,
                    codeClientLocal: clientLocalData?.code,
                    codeClientGroupe: this.selectedPharmacie?.code,
                    nomPharmacienGroupe: this.selectedPharmacie?.nomResponsable,
                    nomPharmacienLocal: clientLocalData?.nomPharmacien,
                    raisonSocialeLocal: clientLocalData?.raisonSociale,
                    raisonSocialeGroupe: this.selectedPharmacie?.raisonSociale,
                });

                this.demandeAccesService.associerTac(payload).subscribe(res => {
                    if (!res?.match || res?.score < 80) {
                        this.userInputService.confirm(
                            'Confirmation',
                            `Il semble que les informations du client local ne correspondent pas. Vous pouvez vérifier ou continuer si cela est intentionnel.`,
                            'Envoyer quand même', 'Annuler'
                        ).then(
                            () => {
                                this.continuerEnvoyerDemandeAcces(payload, modal);
                            },
                            () => null
                        );
                    } else {
                        this.continuerEnvoyerDemandeAcces(payload, modal);
                    }
                });
            },
            () => null
        );
    }

    private continuerEnvoyerDemandeAcces(payload: DemandeAccesClient, modal: any) {
        this.demandeAccesService.saveDemandeAccesClient(payload).subscribe(_res => {
            this.searchClientGroupe(), modal.dismiss();
            this.alertService.success("La demande d'accès client a été envoyée avec succès", 'MODAL');
        });
    }

    appliquerFiltrer(ignoreClass = true): void {
        const { localite, ...filterPayload } = this.filterForm.getRawValue();

        this.navigation.skip = 0;
        this.searchCriteria = {
            ...this.searchCriteria,
            ...filterPayload,
            localite: localite ? [localite] : null,
            ignoreClassification: ignoreClass
        }

        this.searchClientGroupe();
    }

    viderFiltrer(): void {
        this.navigation.skip = 0;
        this.filterForm.reset();

        this.searchCriteria = {
            segmentEntreprise: 'O',
            isClientHasTransco: true,
            ignoreClassification: false,
            fournisseurId: this.authService.getPrincipal()?.societe?.id
        };

        this.searchClientGroupe();
    }

    filterList(searchQuery: string) {
        const criteria = { codeSite: this.currentSocieteCodeSite, raisonOrCodeCSite: searchQuery };

        return this.accesClientService.searchClientFournisseur(criteria);
    }

    filterFournList(searchQuery: string) {
        const criteria = {
            raisonSociale: searchQuery,
            isClientHasTransco: true,
            ignoreClassification: true,
            typeEntreprises: [SocieteType.CLIENT, SocieteType.SOCIETE]
        };

        return this.offresService.searchSociete(criteria, { pageSize: 15, skip: 0 });
    }

    viderRechercheClientLocalForm(): void {
        this.rechercheClientLocalForm.reset({
            code_site: this.authService.getPrincipal()?.societe?.noeud?.codeSite
        });

        this.rechercheClientLocalSubmitted = false;
        this.clientLocalTacGridData = { data: [], total: 0 };
    }

    appliquerRechercheClientLocal(): void {
        this.rechercheClientLocalSubmitted = true;

        if (this.rechercheClientLocalForm.invalid) return;

        const criteria = this.rechercheClientLocalForm.getRawValue();

        this.demandeAccesService.checkAssociationTac(criteria).subscribe(res => {
            if (res && res?.length) {
                this.clientLocalTacGridData = { data: res[0]?.matches, total: res[0]?.matches?.length || 0 };
            }
        });
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterFournList(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map(res => res.content.slice(0, 5))
        );

    searchClientFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase());
                }
                return of([]);
            }),
            map(res => res)
        );

    clientFournisseurFormatter = (result: Fournisseur) => result ? `${result?.code}: ${result?.raisonSociale}` : null;

    fournisseurFormatter = (result: Fournisseur) => result ? `${result?.raisonSociale}` : null;

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }
}