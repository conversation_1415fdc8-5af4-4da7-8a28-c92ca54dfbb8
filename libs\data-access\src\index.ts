export * from './lib/data-access.module';
// import { existsSync } from 'fs';
//
// if(!existsSync('./lib/generated/api.module')) {
//   throw 'Can\'t find it';
// }
// export * from './lib/generated/api.module';
// export * from './lib/generated/models';
// export * from './lib/generated/services';
export * from './lib/services/offres.service';

export * from './lib/services/static-data-offre.service';
export * from './lib/services/custom-functions.service';
export * from './lib/services/user.service';
export * from './lib/services/demandes-inscriptions.service';
export * from './lib/services/auth-log.service';


export * from './lib/models/acceuil.model';
export * from './lib/models/bloc-offre.model';
export * from './lib/models/catalogue.model';
export * from './lib/models/commande.model';
export * from './lib/models/offre.model';
export * from './lib/models/detail-valeur-palier.model';
export * from './lib/models/forme-produit.model';
export * from './lib/models/fournisseur.model';
export * from './lib/models/produit.model';
export * from './lib/models/produitCriteria.model';
export * from './lib/models/statsSearch.model';
export * from './lib/models/PaginationDTO.ts';
export * from './lib/models/gamme-produit.model';
export * from './lib/models/userDTO';
export * from './lib/models/userCriteria.model';
export * from './lib/models/filter.model';
export * from './lib/models/demandes-incriptions.model';
export * from './lib/models/demadeInscriptionCriteria.model';
export * from './lib/models/facture.model';
export * from './lib/models/generic.model';
export * from './lib/models/page.model';
export * from './lib/models/mode-paiement.enum';
export * from './lib/models/days-left.model';
export * from './lib/models/tableau-de-bord-dto.model';
export * from './lib/models/contact-fournisseur-groupe.model';
export * from './lib/models/demandes-acces-client.model';
export * from './lib/models/GA4-events.model';
export * from './lib/models/GA4-analytics-response.model';
export * from './lib/models/feature-flag.model';
export * from './lib/models/page-options.model';
export * from './lib/models/util.constant';
