{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../../dist/out-tsc", "declaration": true, "declarationMap": true, "inlineSources": true, "types": [], "strictNullChecks": false, "noImplicitThis": false, "strictPropertyInitialization": false, "noPropertyAccessFromIndexSignature": false}, "exclude": ["src/test-setup.ts", "**/*.spec.ts", "jest.config.ts", "**/*.test.ts"], "include": ["**/*.ts"]}