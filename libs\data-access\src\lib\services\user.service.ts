import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { User } from '../models/userDTO';
import { Observable } from 'rxjs';
import { Pagination } from '../models/PaginationDTO.ts';
import { UserCriteria } from '../models/userCriteria.model';
import { Societe } from '@wph/shared';

@Injectable({
  providedIn: 'root'
})
export class UserService {

  baseUrl: string;

  constructor(private httpClient: HttpClient,  @Inject('ENVIROMENT') private environment: any) {
    this.baseUrl = environment.base_url;
  }

  getListUsers() {
    return this.httpClient.get<User[]>(this.baseUrl + '/api/v1/users', { observe: 'body' });
  }

  //SAVE USER INFORMATIONS
  saveUserParams(user: User){
    return this.httpClient.post<User>(this.baseUrl + '/api/v1/users', user, { observe: 'body' });
  }

  //GET USER BY ID
  getUserById(idUser: string):Observable<User> {
    return this.httpClient.get<User>(this.baseUrl + `/api/v1/users/${idUser}`, { observe: 'body' });
  }

  activerUserAccount(user:User){
    return this.httpClient.put<User[]>(this.baseUrl + '/api/v1/users/active-user', user, { observe: 'body' });
  }

  desactiverUserAccount(user:User){
    return this.httpClient.put<User[]>(this.baseUrl + '/api/v1/users/desactive-user',user, { observe: 'body' });
  }

  getListSocietes(){
    return this.httpClient.get<Societe[]>(this.baseUrl + `/api/v1/entreprise`, { observe: 'body' });
  }

  editSociete(payload: Societe): Observable<any> {
    return this.httpClient.post<Societe>(this.baseUrl + '/api/v1/entreprise', payload, { observe: 'body' });
  }

  searchUsers(criteria: UserCriteria, pagination:Pagination=null){
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize) ?? 20,
      sort: pagination.sortField ? pagination.sortField+','+pagination.sortMethod : null,
    }
    return this.httpClient.post<any>(this.baseUrl + `/api/v1/users/search`, criteria, { observe: 'body', params });
  }

  getPageNumber(skip: number, pageSize: number) {
    return (skip && pageSize) ? Math.floor(skip / pageSize) : 0;
  }

  attacherUserToSociete(societe:any, user: User){
    return this.httpClient.put<any>(this.baseUrl + `/api/v1/users/attache-user-to-societe/${societe?.id}`, user, { observe: 'body' });
  }

  uploadUsersFile(payload: FormData): Observable<any> {
    return this.httpClient.post(this.baseUrl + '/api/v1/entreprise/upload-societe-excel', payload, { observe: 'body', responseType: 'text' });
  }
}
