{"name": "core-auth", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/core/auth/src", "prefix": "wph", "targets": {"test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/core/auth"], "options": {"jestConfig": "libs/core/auth/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["libs/core/auth/**/*.ts", "libs/core/auth/**/*.html"]}}}, "tags": []}