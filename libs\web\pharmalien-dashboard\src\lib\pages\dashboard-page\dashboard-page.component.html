<!-- Start Of Header -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <h4 class="page-title fw-4 ps-2 col-auto">Tableau de bord <span *ngIf="!displayTitle">de <PERSON>armalien</span></h4>

        <div class="col-auto px-1 d-lg-flex d-none">
            <div class="row px-md-0 py-md-0 py-2 px-2 justify-content-end align-items-center">
                <button (click)="today()" class="btn btn-light btn-sm mr-1 d-flex" style="gap: 5px">
                    <i class="mdi mdi-calendar"></i>
                    <span>{{ displayTitle ? 'J' : 'Jour' }}</span>
                </button>

                <button (click)="yesterday()" class="btn btn-light btn-sm mr-1 d-flex" style="gap: 5px">
                    <i class="mdi mdi-calendar"></i>
                    <span>{{ displayTitle ? 'J - 1' : 'Jour - 1' }}</span>
                </button>

                <button (click)="thisWeek()" class="btn btn-semaine btn-sm mr-1 d-flex" style="gap: 5px;">
                    <i class="mdi mdi-calendar text-dark"></i>
                    <span class="text-dark">{{ displayTitle ? 'S' : 'Semaine' }}</span>
                </button>

                <button (click)="lastWeek()" class="btn btn-semaine btn-sm mr-1 d-flex" style="gap: 5px">
                    <i class="mdi mdi-calendar text-dark"></i>
                    <span class="text-dark">{{ displayTitle ? 'S - 1' : 'Semaine - 1' }}</span>
                </button>

                <button (click)="openFilterModal(dashboardFilter)" class="btn btn-info btn-sm mr-1 d-flex"
                    style="gap: 5px">
                    <i class="mdi mdi-filter-variant"></i>
                    <span *ngIf="!displayTitle">Filtrer</span>
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="container-fluid dash-container px-1">
    <div class="card shadow-none bg-transparent mb-0">
        <ul ngbNav #dasboardNav="ngbNav" [activeId]="activeGridIndex" (activeIdChange)="activeTabChange($event)"
            class="nav-tabs">
            <li [ngbNavItem]="1">
                <a ngbNavLink>
                    <span class="d-flex row align-items-center justify-content-center h5 m-0">
                        <i class="bi bi-building"></i>
                        <b class="mx-1">{{ (displayTitle ? 'Business' : 'Tableau de bord Business') | uppercase }}</b>
                    </span>
                </a>
                <ng-template ngbNavContent>
                    <div *ngIf="displayTitle" class="row my-1 mx-0 p-0 d-flex align-items-center" style="gap: 8px;">
                        <button (click)="updateActiveGrid('STATS-COMMANDEWEB')"
                            class="btn btn-sm btn-outline btn-switch"
                            [ngClass]="{'active': activeGrid === 'STATS-COMMANDEWEB'}">
                            {{ 'STATS COMMANDE WEB' | uppercase }}
                        </button>

                        <button (click)="updateActiveGrid('STATS-WINOFFRE')" class="btn btn-sm btn-outline btn-switch"
                            [ngClass]="{'active': activeGrid === 'STATS-WINOFFRE'}">
                            {{ 'STATS WIN OFFRE' | uppercase }}
                        </button>

                        <button (click)="updateActiveGrid('ETATS-DEMANDES-ACCES')"
                            class="btn btn-sm btn-outline btn-switch"
                            [ngClass]="{'active': activeGrid === 'ETATS-DEMANDES-ACCES'}">
                            {{ 'STATS DEMANDES ACCES' | uppercase }}
                        </button>
                    </div>

                    <div class="row flex-wrap mx-0">
                        <!-- STATISTIQUES ATTENTE D'ENVOIE/ATTENTE BL -->
                        <div *ngIf="!displayTitle || (displayTitle && activeGrid === 'STATS-COMMANDEWEB')"
                            class="col-12 my-1 mx-0 p-0 dash-item-container">
                            <div class="card shadow-lg p-1 m-0 h-100">
                                <kendo-grid [data]="dureeTraitementData" [pageSize]="dureeTraitementData?.total || 20"
                                    [skip]="0" class="dashboard-grid" [resizable]="true" [sortable]="{mode: 'single'}"
                                    [sort]="statsCmdWebSort" [rowClass]="rowClassStatsCmdWeb"
                                    (sortChange)="statsCmdWebSortChange($event)" style="height: 100%">

                                    <ng-template kendoGridToolbarTemplate>
                                        <div class="row d-flex justify-content-between align-items-center w-100 m-0 p-0"
                                            style="overflow: visible !important;">
                                            <div *ngIf="!displayTitle" class="col m-0 p-0">
                                                <span class="h5 text-dark grid-title">{{'Statistiques commande web' |
                                                    uppercase}}</span>
                                            </div>

                                            <div class="col col-xl-3 m-0 p-0">
                                                <select2 class="form-control-md" multiple="false"
                                                    [(ngModel)]="selectedStatsCmdWebFournisseur"
                                                    [data]="statsCmdWebSelectFournissuer"
                                                    placeholder="Sélectionner Fournisseur"></select2>
                                            </div>
                                        </div>
                                    </ng-template>

                                    <kendo-grid-column [width]="170" title="Fournisseur" field="grossiste.raisonSociale"
                                        class="text-wrap" footerClass="pharmalien-dashboard-footer">

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-right text-white"><b>Totaux: </b></span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="80" class="text-center" field="nmbrCmd" title="Nbr Cmds"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap">Nbr Cmds</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ (dataItem?.nmbrCmd || 0) | number }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-center text-white">{{
                                                dureeTraitementAggregate?.['nmbrCmd']?.sum |
                                                number : '1.0-0' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="80" class="text-center" title="Nbr PH" field="nbbrPh"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap">Nbr PH</span>
                                        </ng-template>


                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ (dataItem?.nbbrPh || 0) | number }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-center text-white">{{
                                                dureeTraitementAggregate?.['nbbrPh']?.sum |
                                                number : '1.0-0' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="180" class="text-wrap" field="differencesEnvoi"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span
                                                class="w-100 d-flex justify-content-center align-items-center text-wrap">Envoie
                                                Cmd</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <div *ngIf="dataItem?.differencesEnvoi"
                                                class="d-flex row w-100 m-0 p-0 align-items-center justify-content-center">
                                                <span title="MIN" class="balance-padding" [ngClass]="{
                                                    'warning-highlight': dataItem?.differencesEnvoi[0] | outOfRange: 5, 
                                                    'error-highlight': dataItem?.differencesEnvoi[0] | outOfRange: 300 
                                                    }">{{
                                                    dataItem?.differencesEnvoi[0] | elapsedTime }}</span>

                                                <span class="sep">|</span>

                                                <span title="MOY" class="balance-padding" [ngClass]="{
                                                    'warning-highlight': dataItem?.differencesEnvoi[2] | outOfRange: 5, 
                                                    'error-highlight': dataItem?.differencesEnvoi[2] | outOfRange: 300 
                                                    }">{{
                                                    dataItem?.differencesEnvoi[2] | elapsedTime }}</span>

                                                <span class="sep">|</span>

                                                <span title="MAX" class="balance-padding" [ngClass]="{
                                                    'warning-highlight': dataItem?.differencesEnvoi[1] | outOfRange: 15, 
                                                    'error-highlight': dataItem?.differencesEnvoi[1] | outOfRange: 600 
                                                    }">{{
                                                    dataItem?.differencesEnvoi[1] | elapsedTime }}</span>
                                            </div>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="180" class="text-wrap" field="differencesReception"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span
                                                class="w-100 d-flex justify-content-center align-items-center text-wrap">Réception
                                                BL</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <div *ngIf="dataItem?.differencesReception"
                                                class="d-flex row w-100 m-0 p-0 align-items-center justify-content-center">
                                                <span title="MIN" class="balance-padding" [ngClass]="{
                                                    'warning-highlight': dataItem?.differencesReception[0] | outOfRange: 30, 
                                                    'error-highlight': dataItem?.differencesReception[0] | outOfRange: 600 
                                                    }">{{
                                                    dataItem?.differencesReception[0] | elapsedTime
                                                    }}</span>

                                                <span class="sep">|</span>

                                                <span title="MOY" class="balance-padding" [ngClass]="{
                                                    'warning-highlight': dataItem?.differencesReception[2] | outOfRange: 30, 
                                                    'error-highlight': dataItem?.differencesReception[2] | outOfRange: 600 
                                                    }">{{
                                                    dataItem?.differencesReception[2] | elapsedTime
                                                    }}</span>

                                                <span class="sep">|</span>

                                                <span title="MAX" class="balance-padding" [ngClass]="{
                                                    'warning-highlight': dataItem?.differencesReception[1] | outOfRange: 40, 
                                                    'error-highlight': dataItem?.differencesReception[1] | outOfRange: 900 
                                                    }">{{
                                                    dataItem?.differencesReception[1] | elapsedTime
                                                    }}</span>
                                            </div>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="180" class="text-wrap" field="differencesExpedition"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span
                                                class="w-100 d-flex justify-content-center align-items-center text-wrap">Expédition
                                                BL</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <div *ngIf="dataItem?.differencesExpedition"
                                                class="d-flex row w-100 m-0 p-0 align-items-center justify-content-center">
                                                <span title="MIN" class="balance-padding">
                                                    {{ dataItem?.differencesExpedition[0] | elapsedTime }}
                                                </span>

                                                <span class="sep">|</span>

                                                <span title="MOY" class="balance-padding">
                                                    {{dataItem?.differencesExpedition[2] | elapsedTime }}
                                                </span>

                                                <span class="sep">|</span>

                                                <span title="MAX" class="balance-padding">
                                                    {{ dataItem?.differencesExpedition[1] | elapsedTime }}
                                                </span>
                                            </div>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="120" class="text-wrap text-right" title="Totale TTC"
                                        field="valTotalCmd" footerClass="pharmalien-dashboard-footer">

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ dataItem?.valTotalCmd | number: '1.2-2' }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-right text-white">{{
                                                (dureeTraitementAggregate?.['valTotalCmd']?.sum || 0) | number : '1.2-2'
                                                }}</span>
                                        </ng-template>

                                    </kendo-grid-column>

                                    <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

                                    <ng-template kendoGridNoRecordsTemplate>
                                        <span>Aucun résultat trouvé.</span>
                                    </ng-template>
                                </kendo-grid>
                            </div>
                        </div>

                        <!-- STATISTIQUES ETATS DEMANDES ACCES -->
                        <div *ngIf="!displayTitle || (displayTitle && activeGrid === 'ETATS-DEMANDES-ACCES')"
                            class="col-12 my-1 mx-0 p-0 dash-item-container">
                            <div class="card shadow-lg p-1 m-0 h-100">
                                <kendo-grid [data]="etatsDemandesAccesData" [rowClass]="rowClassEtatsDemandesAcces"
                                    [pageSize]="etatsDemandesAccesData?.total || 20" [skip]="0" class="dashboard-grid"
                                    [sort]="statsDemandesAccesSort" [sortable]="{ mode: 'single' }"
                                    (sortChange)="statsDemandeAccesSortChange($event)" [resizable]="true"
                                    style="height: 100%">

                                    <ng-template kendoGridToolbarTemplate>
                                        <div class="row d-flex justify-content-between align-items-center w-100 m-0 p-0"
                                            style="overflow: visible !important;">
                                            <div *ngIf="!displayTitle" class="col m-0 p-0">
                                                <span class="h5 text-dark grid-title">{{'Statistiques demandes accès' |
                                                    uppercase}}</span>
                                            </div>

                                            <div class="col col-xl-3 m-0 p-0">
                                                <select2 class="form-control-md" multiple="false"
                                                    [(ngModel)]="selectedStatsDemandesAcces"
                                                    [data]="statsSelectDemandeAcces"
                                                    placeholder="Sélectionner Fournisseur"></select2>
                                            </div>
                                        </div>
                                    </ng-template>

                                    <kendo-grid-column [width]="170" title="Fournisseur" field="raisonSociale"
                                        class="text-wrap" footerClass="pharmalien-dashboard-footer">

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-right text-white"><b>Totaux: </b></span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="100" class="text-center" field="countDemandeEnAttente"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap">Nbr Demandes En Attente</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ (dataItem?.countDemandeEnAttente || 0) | number }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-center text-white">{{
                                                etatsDemandesAccesAggregate?.['countDemandeEnAttente']?.sum |
                                                number : '1.0-0' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="100" class="text-center" title="Nbr PH"
                                        field="countDemandeRefusee" footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap">Nbr Demandes Refusée</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ (dataItem?.countDemandeRefusee || 0) | number }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-center text-white">{{
                                                etatsDemandesAccesAggregate?.['countDemandeRefusee']?.sum |
                                                number : '1.0-0' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="100" class="text-center" field="countDemandeTotal"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span
                                                class="w-100 d-flex justify-content-center align-items-center text-wrap">Totale
                                                Demandes</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ (dataItem?.countDemandeTotal || 0) | number }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-center text-white">{{
                                                etatsDemandesAccesAggregate?.['countDemandeTotal']?.sum |
                                                number : '1.0-0' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="60" media="(min-width: 992px)"
                                        class="text-center no-ellipsis" footerClass="pharmalien-dashboard-footer"
                                        [sortable]="false">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span
                                                class="w-100 d-flex justify-content-center align-items-center text-wrap">Action</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <div class="d-flex justify-content-center w-100">
                                                <span (click)="navigateToDemandesAcces(dataItem)"
                                                    class="badge badge-warning rounded-pill py-1 px-2 pointer-cus">Consulter</span>
                                            </div>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="120" media="(max-width: 991px)"
                                        class="text-center no-ellipsis" footerClass="pharmalien-dashboard-footer"
                                        [sortable]="false">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span
                                                class="w-100 d-flex justify-content-center align-items-center text-wrap">Action</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <div class="d-flex justify-content-center w-100">
                                                <span (click)="navigateToDemandesAcces(dataItem)"
                                                    class="badge badge-warning rounded-pill py-1 px-2 pointer-cus">Consulter</span>
                                            </div>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

                                    <ng-template kendoGridNoRecordsTemplate>
                                        <span>Aucun résultat trouvé.</span>
                                    </ng-template>
                                </kendo-grid>
                            </div>
                        </div>

                        <!-- STATISTIQUES WINOFFRE -->
                        <div *ngIf="!displayTitle || (displayTitle && activeGrid === 'STATS-WINOFFRE')"
                            class="col-12 my-1 mx-0 p-0 dash-item-container">
                            <div class="card shadow-lg p-1 m-0 h-100">
                                <kendo-grid [data]="statistiquesWinoffreData" [sortable]="{ mode: 'single' }"
                                    [sort]="statsCmdWinOffreSort" (sortChange)="statsWinoffreSortChange($event)"
                                    [pageSize]="statistiquesWinoffreData?.total || 20" [skip]="0" class="dashboard-grid"
                                    [resizable]="true" style="height: 100%">

                                    <ng-container *ngIf="!displayTitle">
                                        <ng-template kendoGridToolbarTemplate>
                                            <div
                                                class="row d-flex justify-content-start align-items-center w-100 m-0 p-0">
                                                <span class="h5 text-dark grid-title">{{'Statistiques WINOFFRE' |
                                                    uppercase}}</span>
                                            </div>
                                        </ng-template>
                                    </ng-container>

                                    <kendo-grid-column title="Offre" field="offreDto.titre"
                                        footerClass="pharmalien-dashboard-footer" class="text-wrap" [width]="200">
                                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                                            {{dataItem?.offreDto?.titre}}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-right text-white"><b>Totaux: </b></span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column field="nbrCommande" footerClass="pharmalien-dashboard-footer"
                                        title="Nombre Commandes" [width]="80" class="text-left">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap">Nbr Cmds</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                                            {{ dataItem?.nbrCommande | number: '1.0-0' }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-left text-white">{{
                                                statistiquesWinoffreResponse?.nbrCommande | number : '1.0-0' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column field="nmbrPh" footerClass="pharmalien-dashboard-footer"
                                        title="Nbr PH" [width]="80" class="text-left">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap">Nbr PH</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                                            {{ dataItem?.nmbrPh | number: '1.0-0' }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-left text-white">{{
                                                statistiquesWinoffreResponse?.nmbrPh | number : '1.0-0' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column field="qteVedues" footerClass="pharmalien-dashboard-footer"
                                        title="Qté" [width]="80" class="text-left">
                                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                                            {{ dataItem?.qteVedues | number: '1.0-0' }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-left text-white">{{
                                                statistiquesWinoffreResponse?.qteVedues | number : '1.0-0' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column field="caBrut" footerClass="pharmalien-dashboard-footer"
                                        title="CA Brut" [width]="120" class="text-right">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap">CA Brut</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex"
                                            let-column="column">
                                            {{dataItem.caBrut | number: "1.2-2":"fr-FR"}}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-right text-white">{{
                                                statistiquesWinoffreResponse?.caBrut | number: '1.2-2' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column field="caNet" footerClass="pharmalien-dashboard-footer"
                                        title="CA Net" [width]="120" class="text-right">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap">CA Net</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex"
                                            let-column="column">
                                            {{dataItem.caNet | number: "1.2-2":"fr-FR"}}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-right text-white">{{
                                                statistiquesWinoffreResponse?.caNet | number: '1.2-2' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column field="qteUG" footerClass="pharmalien-dashboard-footer"
                                        title="Qté UG" [width]="80" class="text-left">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap">Qté UG</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                                            {{ dataItem?.qteUG | number : '1.0-0' }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-left text-white">{{
                                                statistiquesWinoffreResponse?.qteUG | number: '1.0-0' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column field="mtRemise" footerClass="pharmalien-dashboard-footer"
                                        title="Mnt Remise" [width]="120" class="text-right">
                                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                                            {{dataItem?.mtRemise | number: "1.2-2":"fr-FR"}}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-right text-white">{{
                                                statistiquesWinoffreResponse?.mtRemise | number: '1.2-2' }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <ng-template kendoGridNoRecordsTemplate>
                                        <span>Aucun résultat trouvé.</span>
                                    </ng-template>

                                    <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

                                    <ng-template kendoGridNoRecordsTemplate>
                                        <span>Aucun résultat trouvé.</span>
                                    </ng-template>
                                </kendo-grid>
                            </div>
                        </div>
                    </div>
                </ng-template>
            </li>

            <li [ngbNavItem]="2">
                <a ngbNavLink>
                    <span class="d-flex row align-items-center justify-content-center h5 m-0">
                        <i class="bi bi-pc-display-horizontal"></i>
                        <b class="mx-1">{{ (displayTitle ? 'Technique' : 'Tableau de bord Technique') | uppercase }}</b>
                    </span>
                </a>
                <ng-template ngbNavContent>
                    <div *ngIf="displayTitle" class="row my-1 mx-0 p-0 d-flex align-items-center" style="gap: 8px;">
                        <button (click)="updateActiveGrid('CMDS-RETARD')" class="btn btn-sm btn-outline btn-switch"
                            [ngClass]="{'active': activeGrid === 'CMDS-RETARD'}">
                            {{ 'cmds en rétard' | uppercase }}
                        </button>

                        <button (click)="updateActiveGrid('ETATS-CONSOEXT')" class="btn btn-sm btn-outline btn-switch"
                            [ngClass]="{'active': activeGrid === 'ETATS-CONSOEXT'}">
                            {{ 'conso-ext' | uppercase }}
                        </button>

                        <button (click)="updateActiveGrid('ETATS-FLUX')" class="btn btn-sm btn-outline btn-switch"
                            [ngClass]="{'active': activeGrid === 'ETATS-FLUX'}">
                            {{ 'états flux' | uppercase }}
                        </button>
                    </div>

                    <div class="row mx-0 flex-wrap">
                        <!-- COMMANDES AVEC RETARD -->
                        <div *ngIf="!displayTitle || (displayTitle && activeGrid === 'CMDS-RETARD')"
                            class="col-12 my-1 mx-0 p-0 dash-item-container">
                            <div class="card shadow-lg p-1 m-0 h-100">

                                <kendo-grid [data]="cmdsEnRetardData" [skip]="0" [sort]="cmdAvecRetardSort"
                                    [sortable]="{ mode: 'single' }" (sortChange)="cmdAvecRetardSortChange($event)"
                                    [pageSize]="cmdsEnRetardData?.total || 20" class="dashboard-grid" [resizable]="true"
                                    style="height: 100%">

                                    <ng-template kendoGridToolbarTemplate>
                                        <div
                                            class="row d-flex justify-content-between align-items-center w-100 m-0 p-0">
                                            <span class="h5 text-dark grid-title">{{'Commandes avec rétard' |
                                                uppercase}}</span>

                                            <div class="col-lg-6 col-12 d-flex justify-content-end m-0 p-0"
                                                style="gap: 8px">
                                                <button
                                                    (click)="toggleCmdType('NON-ENVOYEES'); updateDisplayedCmdsAvecRetard('NON-ENVOYEES')"
                                                    class="btn btn-sm btn-outline btn-switch d-flex align-items-center justify-content-between"
                                                    [ngClass]="{'active': switchTypeCommande === 'NON-ENVOYEES'}">
                                                    <b [ngClass]="{'emphasis-value': cmdsNonEnvoyeesCount > 0}">{{
                                                        cmdsNonEnvoyeesCount | number }} &nbsp;</b>
                                                    {{ "Non-Envoyées" | uppercase }}
                                                </button>

                                                <button
                                                    (click)="toggleCmdType('SANS-BL'); updateDisplayedCmdsAvecRetard('SANS-BL')"
                                                    class="btn btn-sm btn-outline btn-switch d-flex align-items-center justify-content-between"
                                                    [ngClass]="{'active': switchTypeCommande === 'SANS-BL'}">
                                                    <b [ngClass]="{'emphasis-value': cmdsSansBlCount > 0}">{{
                                                        cmdsSansBlCount | number }} &nbsp;</b>
                                                    {{ "Sans Bl" | uppercase }}
                                                </button>
                                            </div>
                                        </div>
                                    </ng-template>

                                    <kendo-grid-column [width]="150" title="Fournisseur"
                                        field="distributeur.raisonSociale" class="text-wrap">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ dataItem?.distributeur?.raisonSociale }}
                                        </ng-template>

                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="80" field="nbrCmds" class="text-center">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span
                                                class="text-wrap d-flex align-items-center w-100 row justify-content-center">Nbr
                                                Cmds</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ dataItem?.nbrCmds | number }}
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="110"
                                        [field]="switchTypeCommande === 'NON-ENVOYEES' ? 'dateValidation' : 'dateEnvoiToFournisseur'"
                                        title="Dépuis" class="text-wrap">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <span class="pointer-cus" *ngIf="switchTypeCommande === 'NON-ENVOYEES'"
                                                container="body"
                                                [ngbTooltip]="dataItem?.dateValidation | momentTimezone : 'yyyy-MM-DD HH:mm' : 'Africa/Casablanca'">
                                                {{ dataItem?.dateValidation | timeAgo : 'Africa/Casablanca' }}
                                            </span>

                                            <span class="pointer-cus" *ngIf="switchTypeCommande === 'SANS-BL'"
                                                container="body"
                                                [ngbTooltip]="dataItem?.dateEnvoiToFournisseur | momentTimezone : 'yyyy-MM-DD HH:mm' : 'Africa/Casablanca'">
                                                {{ dataItem?.dateEnvoiToFournisseur | timeAgo : 'Africa/Casablanca' }}
                                            </span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="60" title="" class="text-center" [sortable]="false">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <button title="Liste commandes"
                                                (click)="openModal(detailsCmdsTemplate, dataItem)"
                                                class="circle btn btn-success text-white mx-1">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

                                    <ng-template kendoGridNoRecordsTemplate>
                                        <span>Aucun résultat trouvé.</span>
                                    </ng-template>
                                </kendo-grid>
                            </div>
                        </div>

                        <!-- ETATS CONSO-EXT -->
                        <div *ngIf="!displayTitle || (displayTitle && activeGrid === 'ETATS-CONSOEXT')"
                            class="col-12 my-1 mx-0 p-0 dash-item-container">
                            <div class="card shadow-lg p-1 m-0 h-100">
                                <kendo-grid [data]="etatsConsoExtData" [pageSize]="etatsConsoExtData?.total || 20"
                                    [skip]="0" class="dashboard-grid" [resizable]="true" [sortable]="{mode: 'single'}"
                                    [sort]="etatsConsoExtSort" (sortChange)="etatsConsoExtSortChange($event)"
                                    style="height: 100%">

                                    <ng-container *ngIf="!displayTitle">
                                        <ng-template kendoGridToolbarTemplate>
                                            <div
                                                class="row d-flex justify-content-start align-items-center w-100 m-0 p-0">
                                                <span class="h5 text-dark">{{'États Conso-Ext' | uppercase}}</span>
                                            </div>
                                        </ng-template>
                                    </ng-container>

                                    <kendo-grid-column [width]="150" field="fournisseur.libelle" title="Fournisseur"
                                        class="text-wrap" footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ dataItem?.fournisseur?.libelle }}
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-right text-white"><b>Totaux: </b></span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="82" class="text-wrap" [sortable]="false"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap d-flex align-items-center">État CmdWeb</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <span *ngIf="dataItem?.etatCmdWeb === true"
                                                class="row d-flex align-items-center text-wrap mx-1" style="gap: 5px">
                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                <span class="text-success h5">ON</span>
                                            </span>

                                            <span
                                                *ngIf="dataItem?.etatCmdWeb === null || dataItem?.etatCmdWeb === undefined"
                                                class="row d-flex align-items-center text-wrap mx-1" style="gap: 5px">
                                                <i class="bi bi-exclamation-circle-fill text-warning"></i>
                                                <span class="text-warning h5">N/A</span>
                                            </span>

                                            <span *ngIf="dataItem?.etatCmdWeb === false"
                                                class="row d-flex align-items-center text-wrap mx-1" style="gap: 5px">
                                                <i class="bi bi-dash-circle-fill text-danger"></i>
                                                <span class="text-danger h5">OFF</span>
                                            </span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="80" [sortable]="false"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap d-flex align-items-center">Liaison Sarphix</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <span *ngIf="dataItem?.liaisonSarphix === true"
                                                class="row d-flex align-items-center text-wrap mx-1" style="gap: 5px">
                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                <span class="text-success h5">ON</span>
                                            </span>

                                            <span
                                                *ngIf="dataItem?.liaisonSarphix === null || dataItem?.liaisonSarphix === undefined"
                                                class="row d-flex align-items-center text-wrap mx-1" style="gap: 5px">
                                                <i class="bi bi-exclamation-circle-fill text-warning"></i>
                                                <span class="text-warning h5">N/A</span>
                                            </span>

                                            <span *ngIf="dataItem?.liaisonSarphix === false"
                                                class="row d-flex align-items-center text-wrap mx-1" style="gap: 5px">
                                                <i class="bi bi-dash-circle-fill text-danger"></i>
                                                <span class="text-danger h5">OFF</span>
                                            </span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column field="cmdNonTraiteEdi" [width]="80" class="text-center"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap d-flex align-items-center">Cmds Non-Traitées
                                                EDI</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <span class="pointer-cus"
                                                [ngbTooltip]="dataItem?.timestampNonTraiteEdi ? (dataItem?.timestampNonTraiteEdi | momentTimezone: 'yyyy-MM-DD HH:mm': 'Africa/Casablanca') : ''"
                                                [ngClass]="{'warning-highlight': dataItem?.cmdNonTraiteEdi > 0, 'error-highlight': dataItem?.cmdNonTraiteEdi > 10}">{{
                                                dataItem?.cmdNonTraiteEdi | number: '1.0-0' }}</span>
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-center text-white">{{
                                                aggregateCmdNonTraite()['cmdNonTraiteEdi']?.sum }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column field="cmdNonTraiteSarphix" [width]="100" class="text-center"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="text-wrap d-flex align-items-center">Cmds Non-Traitées
                                                Sarphix</span>
                                        </ng-template>

                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <span class="pointer-cus"
                                                [ngbTooltip]="dataItem?.timestampNonTraiteSarphix ? (dataItem?.timestampNonTraiteSarphix | momentTimezone: 'yyyy-MM-DD HH:mm': 'Africa/Casablanca') : ''"
                                                [ngClass]="{'warning-highlight': dataItem?.cmdNonTraiteSarphix > 0, 'error-highlight': dataItem?.cmdNonTraiteSarphix > 10}">{{
                                                dataItem?.cmdNonTraiteSarphix | number: '1.0-0' }}</span>
                                        </ng-template>

                                        <ng-template kendoGridFooterTemplate>
                                            <span class="d-block text-center text-white">{{
                                                aggregateCmdNonTraite()['cmdNonTraiteSarphix']?.sum }}</span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column title="Dépuis" field="timeStamp" [width]="110"
                                        footerClass="pharmalien-dashboard-footer">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <span *ngIf="dataItem?.timeStamp" class="pointer-cus" container="body"
                                                [ngClass]="{'error-highlight': 0.5 | elapsedTimeOutOfRange : dataItem?.timeStamp }"
                                                [ngbTooltip]="dataItem?.timeStamp | momentTimezone: 'yyyy-MM-DD HH:mm': 'Africa/Casablanca'">
                                                {{ dataItem?.timeStamp | timeAgo : 'Africa/Casablanca' }}
                                            </span>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

                                    <ng-template kendoGridNoRecordsTemplate>
                                        <span>Aucun résultat trouvé.</span>
                                    </ng-template>
                                </kendo-grid>
                            </div>
                        </div>

                        <!-- ETAT DES FLUX PHARMALIEN/CONSO-EXT -->
                        <div *ngIf="!displayTitle || (displayTitle && activeGrid === 'ETATS-FLUX')"
                            class="col-12 my-1 mx-0 p-0 dash-item-container">
                            <div class="card shadow-lg p-1 m-0 h-100">
                                <kendo-grid [data]="etatsFluxData" [pageSize]="etatsFluxData?.total || 20" [skip]="0"
                                    [sort]="etatsFluxSort" [sortable]="{mode: 'single'}" [rowClass]="rowClassEtatsFlux"
                                    (sortChange)="etatsFluxSortChange($event)" class="dashboard-grid" [resizable]="true"
                                    style="height: 100%">

                                    <ng-template kendoGridToolbarTemplate>
                                        <div
                                            class="row d-flex flex-wrap justify-content-between align-items-center w-100 m-0 p-0 k-gap-2">
                                            <span class="h5 text-dark grid-title">{{'États des Flux' |
                                                uppercase}}</span>

                                            <div
                                                class="col d-flex flex-wrap justify-content-even justify-content-lg-end align-items-center k-gap-2 p-0 m-0">
                                                <ng-container *ngIf="etatsFluxData.total">
                                                    <button (click)="toggleFlux(null)" class="btn btn-sm"
                                                        [ngClass]="{'btn-outline-info': selectedTypeFlux !== null, 'btn-info': selectedTypeFlux === null}">
                                                        {{ 'Tout' | uppercase }}
                                                    </button>
                                                    <button *ngFor="let item of etatsFlux" (click)="toggleFlux(item)"
                                                        class="btn btn-sm"
                                                        [ngClass]="(selectedTypeFlux === item ? 'btn-' : 'btn-outline-') + etatFlux2StateMap.get(item)">
                                                        {{ item | flux }}
                                                    </button>
                                                </ng-container>
                                            </div>
                                        </div>
                                    </ng-template>

                                    <kendo-grid-column [width]="180" field="raisonSociale" class="text-wrap"
                                        title="Site Émetteur">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ dataItem?.raisonSociale }}
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="180" field="typeFlux" title="Flux" class="text-wrap">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{ dataItem?.typeFlux | flux }}
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-column [width]="110" field="dateReceptionFlux" title="Dépuis"
                                        class="text-wrap">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <div class="my-1">
                                                <span class="pointer-cus" container="body" [ngClass]="{
                                                        'warning-highlight': dataItem?.dateReceptionFlux && (dataItem?.typeFlux | fluxRange | elapsedTimeOutOfRange : dataItem?.dateReceptionFlux),
                                                        'error-highlight': dataItem?.dateReceptionFlux && (dataItem?.typeFlux | fluxRange : true | elapsedTimeOutOfRange : dataItem?.dateReceptionFlux)
                                                        }"
                                                    [ngbTooltip]="dataItem?.dateReceptionFlux | momentTimezone: 'yyyy-MM-DD HH:mm': 'Africa/Casablanca'">{{
                                                    dataItem?.dateReceptionFlux | timeAgo : 'Africa/Casablanca'
                                                    }}</span>
                                            </div>
                                        </ng-template>
                                    </kendo-grid-column>

                                    <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

                                    <ng-template kendoGridNoRecordsTemplate>
                                        <span>Aucun résultat trouvé.</span>
                                    </ng-template>
                                </kendo-grid>
                            </div>
                        </div>
                    </div>
                </ng-template>
            </li>
        </ul>

        <div [ngbNavOutlet]="dasboardNav" class="tab-content-container"></div>
    </div>
</div>

<!-- DETAILS DES COMMANDES MODAL TEMPLATE -->
<ng-template #detailsCmdsTemplate let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">{{ 'Liste des commandes avec rétard du fournisseur' | uppercase
            }}: {{selectedFournisseur?.distributeur?.raisonSociale }}</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span>&times;</span>
        </button>
    </div>

    <div id="DEFAULT-container" class="modal-content">
        <kendo-grid [data]="selectedFournisseurCmds" [pageSize]="selectedFournisseurCmds?.total" [skip]="0"
            [style.max-height]="'80vh'" [resizable]="true" [sort]="listeCmdSort" [sortable]="{mode: 'single'}"
            (sortChange)="listeCmdSortChange($event)">

            <kendo-grid-column [width]="120" field="codeCommande" title="Num Cmd">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap d-flex align-items-center">Num Cmd</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.codeCommande }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="dateValidation" [hidden]="switchTypeCommande !== 'NON-ENVOYEES'" [width]="180"
                title="Date Validation">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap d-flex align-items-center">Date Validation</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.dateValidation | momentTimezone: 'yyyy-MM-DD HH:mm' : "Africa/Casablanca" }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="dateEnvoiToFournisseur" [hidden]="switchTypeCommande !== 'SANS-BL'" [width]="180"
                title="Date d'envoie">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap d-flex align-items-center">Date d'envoie</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.dateEnvoiToFournisseur | momentTimezone: 'yyyy-MM-DD HH:mm' : "Africa/Casablanca" }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="raisonSociale" [width]="200" title="Raison Sociale" class="text-wrap">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap d-flex align-items-center">Raison Sociale</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.raisonSociale }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="ville" [width]="180" title="Ville" class="text-wrap">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap d-flex align-items-center">Ville</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.ville }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="origineCommande" [width]="180" title="Origine Cmd">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap d-flex align-items-center">Origine Cmd</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.origineCommande | origineCmd }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="valeurCmdNetTtc" class="text-right" [width]="120" title="Mnt PPH Net">
                <ng-template kendoGridHeaderTemplate>
                    <span class="text-wrap d-flex align-items-center">Mnt PPH Net</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    {{ dataItem?.valeurCmdNetTtc | number: "1.2-2" }}
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"></kendo-grid-messages>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>
        </kendo-grid>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">Fermer
        </button>
    </div>
</ng-template>

<!-- Filter Modal Start -->
<ng-template #dashboardFilter let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Filtrer</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <form [formGroup]="dashboardFilterFormGroup"
        (ngSubmit)="dashboardFilterFormGroup.valid && applyDateFilterChanges(dashboardFilterFormGroup); modal.dismiss()"
        wphFocusTrap>
        <div class="p-2">
            <div class="row" wphFocusTrap>
                <div class="col-12 mx-0 px-0">
                    <label for="dateDebut" class="col-12 col-form-label text-dark text-left">Date Début <span
                            class="text-danger">*</span></label>

                    <div class="col-12">
                        <app-date-picker formControlName="dateDebut"></app-date-picker>
                    </div>
                </div>

                <div class="col-12 mx-0 px-0 mt-2">
                    <label for="dateFin" class="col-12 col-form-label text-dark text-left">Date Fin <span
                            class="text-danger">*</span></label>
                    <div class="col-12">
                        <app-date-picker formControlName="dateFin"></app-date-picker>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" (click)="modal.dismiss('Cross click')" class="btn btn-light"
                tabindex="-1">Fermer</button>
            <button type="button" class="btn btn-secondary text-white" (click)="vider(); modal.dismiss()"
                tabindex="-1">Vider</button>
            <button type="button" type="submit" class="btn btn-primary ml-1 text-white"
                [disabled]="dashboardFilterFormGroup.invalid" tabindex="-1">Rechercher</button>
        </div>
    </form>
</ng-template>