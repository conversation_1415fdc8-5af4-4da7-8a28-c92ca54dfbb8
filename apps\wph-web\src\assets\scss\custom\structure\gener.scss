$scroll-color: #313a46;
.offrelogo{
    object-fit: contain; 
    top: 12%; 
    max-width: 150px; 
    left: 6%; 
    max-height: 70px;
    width: 20vw;
  
  }


    
 


  .remiseplacement{
      display: flex;
      justify-content: flex-end;
  }

  div.d-flex.mb-2.blockoffretable {
    transform: translateX(-13px) !important;
}


 .grouptab{
     margin-top: 17px;
 }
  .grouptable{
      background: grey;
    //   writing-mode: vertical-rl; 
    //   background: grey; 
    //   text-orientation: mixed; 
    //   text-align: center;  
      padding: 0px; 
      width: 5px;
      border-radius: 0;

      position: relative;

      &:first-of-type{
          margin-left: 0;
      }


   



      .upgrouptable,.downgrouptable{
        content: "";
        position: absolute;
        background: #6c757d;
        width: 35px;
        height: 4px;
        right: 0;
        transform: translateX(35px);
          }
        .upgrouptable{
            top: 0; 
        }
        .downgrouptable{
            bottom: 0; 
        }
          
  }


.bg-success.card.grouptable {
    &:before {

    border-left: 6px solid $success;
      }

    }


    .Actionbreadcrumcus .d-flex .position-relative button:last-child,.Actionbreadcrumfix{
      margin-right: 0 !important;

    }

    .offrecmd ul{
      background-color:  #eef2f7;
    }




    

.check-is-disabled{
  position: relative;
  height: 15px;
  width: 15px;
    &:before {
      content: "";
      background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='utf-8'%3F%3E%3C!-- Generator: Adobe Illustrator 25.2.3, SVG Export Plug-In . SVG Version: 6.00 Build 0) --%3E%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' style='enable-background:new 0 0 512 512;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:%23FFFFFF;%7D .st1%7Bfill:%2398A6AD;%7D%0A%3C/style%3E%3Crect class='st0' width='512' height='512'/%3E%3Cg%3E%3Cpath class='st1' d='M508.8,79.6l-59.2-59.2c-2.1-2.1-4.9-3.3-7.8-3.3s-5.8,1.2-7.8,3.3l-214,214l-41.7-41.7 c-4.3-4.3-11.3-4.3-15.7,0L103.2,252c-2.1,2.1-3.3,4.9-3.3,7.8s1.2,5.8,3.3,7.8L211.6,376c2.1,2.1,4.9,3.2,7.8,3.2l0.4-0.1l0.3,0.1 c2.8,0,5.7-1.1,7.8-3.2L508.8,95.3C513.1,91,513.1,84,508.8,79.6z'/%3E%3Cpath class='st1' d='M371.2,280.9v145.4H68.6V123.7H282l68.6-68.6H35.7l0,0h-0.5C16,55.1,0.5,70.4,0.1,89.5H0v371.1h0.1 c0.5,18.7,15.5,33.8,34.3,34.3v0.1h0.9l0,0h369.3l0,0h0.9v-0.1c18.8-0.5,33.8-15.5,34.3-34.3h0.1V212.3L371.2,280.9z'/%3E%3C/g%3E%3C/svg%3E%0A");      display: block;
      height: 100%;
      width: 100%;
    }

}




.picker-input>.picker-icons, .picker-input>.picker-icons>i {
  display: flex;

  align-items: center;
  height: 100%;
  margin-right: 4px;
}

.picker-input>.picker-icons {
  position: absolute;
  white-space: nowrap;
  top: 0;
  right: 3px;
  z-index: 3;

  i{
    font-size: 18px;
    opacity: .85;
    &:hover {
      opacity: 1;
    }
  }
  

}

.picker-input > .picker-icons-alt {
  right: 0 !important;
  left: 8px;
  width: 10px;
  color: black;
}

.picker-input > .picker-icons-right {
  position: absolute;
  white-space: nowrap;
  top: 5px;
  right: 10px;
  //z-index: 3;

  i{
    font-size: 18px;
    opacity: .85;
    &:hover {
      opacity: 1;
    }
  }
}


.carouselaccueil{
  .carousel-control-next,.carousel-control-prev{
    height: 20px;
    transform: translateY(-50%);
    top: 50%;
  }

  
}





.simplebar-scrollbar:before {
  position: absolute;
  content: '';
  background: $scroll-color;
  border-radius: 7px;
  left: 4px;
  width: 5px;
  opacity: 0;
  transition: opacity 0.2s linear;
  z-index: 1;
}



.simplebar-scrollbar.simplebar-visible:before {
  opacity: 1;
  transition: opacity 0s linear;
}
.simplebar-content{
  position: relative;
}

.test-line{
  
  position: absolute;
  background: #fff;
  opacity: 0.3;
  border-radius: 7px;
  right: 2px;
  width: 4px;
  height: 100%;
  z-index: 2;
 
}

/******** Color choice styles *********/
.bg-TURQUOISE {
  background-color: #1abc9c !important;
  color: #fff !important;

}

.bg-EMERALD {
  background-color: #2ecc71 !important;
  color: #fff !important;

}

.bg-GREENSEA {
  background-color: #16a085 !important;
  color: #fff !important;

}

.bg-PETERRIVER {
  background-color: #3498db !important;
  color: #fff !important;

}

.bg-BELIZEHOLE {
  background-color: #2980b9 !important;
  color: #fff !important;

}

.bg-AMETHYST {
  background-color: #8e44ad !important;
  color: #fff !important;

}


.bg-WETASPHALT {
  background-color: #34495e !important;
  color: #fff !important;

}

.bg-SUNFLOWER {
  background-color: #f1c40f !important;
  color: #fff !important;

}

.bg-PUMPKIN {
  background-color: #d35400 !important;
  color: #fff !important;

}

.bg-ALIZARIN {
  background-color: #e74c3c !important;
  color: #fff !important;

}

.bg-POMEGRANATE {
  background-color: #c0392b !important;
  color: #fff !important;

}


.bg-CLOUDS {
  background-color: #ecf0f1 !important;

  color: gray !important;

}

.bg-ASBESTOS {
  background-color: #7f8c8d !important;

  color: #ecf0f1 !important;

}
