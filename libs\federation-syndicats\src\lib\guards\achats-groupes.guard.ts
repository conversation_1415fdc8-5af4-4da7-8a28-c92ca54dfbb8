import { Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, CanActivateChild, Router, UrlTree } from "@angular/router";
import { PlateformeService } from "@wph/shared";

@Injectable({
    providedIn: 'root'
})
export class AchatsGroupesGuard implements CanActivateChild {
    constructor(private router: Router, private plateformeService: PlateformeService) { }

    canActivateChild(_childRoute: ActivatedRouteSnapshot): boolean | UrlTree {        
        if (this.plateformeService.isPlateForme('FEDERATION_SYNDICAT') || this.plateformeService.isPlateForme('WIN_GROUPE')) {
            return true;
        }

        const currentPlateforme = this.plateformeService.getCurrentPlateforme();

        switch(currentPlateforme) {
            case 'WIN_OFFRE':
                return this.router.createUrlTree(['accueil']);
            case 'COMMANDE_WEB':
                return this.router.createUrlTree(['commande-web', 'accueil']);
            case 'DEFAULT':
            default:
                return this.router.createUrlTree(['pharma-lien']);
        }
        
    }
}