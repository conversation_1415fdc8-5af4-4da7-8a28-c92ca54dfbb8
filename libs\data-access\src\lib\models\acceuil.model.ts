import { SafeUrl } from "@angular/platform-browser";
import { Moment } from "moment";

export class ArticleAcceuil {
  id?: number;
  titre?: string;
  sousTitre?: string;
  soustitre?: string; //TODO ADDED CHECK IF CORRECT REMOVE THE OTHER
  message: Text;
  image: ImageLogo;
}

export class MessageAcceuil {
  id?: number;
  date?: Moment;
  source?: string;
  message?: string;
  text: Text;
  messageEncoded: string; // TODO: Added check please
  dateHeure: string;      // TODO: Added check please
}

export class ImageLogo {
  id?: number;
  data: SafeUrl;
}

export class MessageCriteria {
  id?: number;
  source?: string;
  dateDebut?: Moment;
  dateFin?: Moment;
}
