import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { AuthService } from "@wph/core/auth";
import { EntrepriseDTO } from "../../../models/entreprise.model";
import { AlertService, ICity, SocieteType, StaticDataService } from "@wph/shared";
import { ActivatedRoute, Router } from "@angular/router";
import { OperatorFunction, Observable, debounceTime, distinctUntilChanged, switchMap, of, map } from "rxjs";
import { Localite } from "libs/shared/src/lib/models/localite.model";
import { OffresService } from "@wph/data-access";
import { UserInputService } from "@wph/web/shared";
import { phoneValidator } from "../../../validators/phone-validator";

@Component({
    selector: 'wph-edit-fournisseur',
    templateUrl: './edit-fournisseur.component.html',
    styleUrls: ['./edit-fournisseur.component.scss']
})
export class EditFournisseurComponent implements OnInit {
    isReadOnly: boolean;
    isLoading: boolean;
    submitted: boolean;
    model: EntrepriseDTO;
    editForm: FormGroup | null = null;
    ville: ICity[] = [];
    localites: Localite[] = [];
    fournisseurId: number;

    typeEntrepriseValues: any[] = [
        { label: SocieteType.SOCIETE, value: SocieteType.SOCIETE },
        { label: SocieteType.GROSSISTE, value: SocieteType.GROSSISTE },
        { label: SocieteType.FABRIQUANT, value: SocieteType.FABRIQUANT },
    ];

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private authService: AuthService,
        private alertService: AlertService,
        private offresService: OffresService,
        private userInputService: UserInputService,
        private staticDataService: StaticDataService
    ) {
        this.initForm();
    }

    ngOnInit(): void {
        this.route.params.subscribe(params => {
            this.fournisseurId = +params['id'];
            this.fournisseurId && this.fetchSocieteById(this.fournisseurId);
        });

        const qParams = this.route.snapshot.queryParams;
        this.isReadOnly = JSON.parse(qParams['readOnly']);

        this.getListeVilles();
        this.getListeLocalites();
    }

    get f() { return this.editForm?.controls; }

    initForm() {
        this.editForm = this.fb.group({
            id: [null],
            code: [null, [Validators.required]], // ? Disabled in edit-mode
            raisonSociale: [null, [Validators.required, Validators.maxLength(80)]],
            ville: [null, [Validators.maxLength(40)]],
            localite: [null, []],
            gsm1: [null, [phoneValidator(true)]],
            telephone: [null, [phoneValidator(true)]],
            adresse: [null, [Validators.maxLength(80)]],
            adresse2: [null, [Validators.maxLength(40)]],
            nomResponsable: [null, [Validators.maxLength(40)]],
            email: [null, []],
            numIce: [null, []],
            typeEntreprise: [null, [Validators.required]]
        });
    }

    patchForm(): void {
        if (this.editForm && this.model) {
            this.editForm.patchValue({
                id: this.model?.id || null,
                code: this.model?.code || null,
                raisonSociale: this.model?.raisonSociale || null,
                ville: this.ville?.find(v => v.labelFr === this.model?.ville),
                localite: this.localites?.find(loc => loc?.localite === this.model?.localite),
                adresse: this.model?.adresse || null,
                adresse2: this.model?.adresse2 || null,
                nomResponsable: this.model?.nomResponsable || null,
                typeEntreprise: this.model?.typeEntreprise || null,
                email: this.model?.email || null,
                numIce: this.model?.numIce || null,
                gsm1: this.model?.gsm1 || null,
                telephone: this.model?.telephone || null
            });

            this.f['code'].disable();
            this.f['numIce'].disable();
            this.f['adresse'].disable();
            this.f['adresse2'].disable();
            this.f['ville'].disable();
            this.f['localite'].disable();
        }
    }

    saveChanges(): void {
        this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir ${this.model ? 'modifier' : 'ajouter'} ce fournisseur ?`)
            .then(
                () => {
                    this.submitted = true;

                    if (this.editForm.valid) {
                        this.isLoading = true;

                        const formValues = this.editForm.getRawValue();
                        const ville = (typeof formValues?.ville === 'object') ? formValues?.ville?.labelFr : formValues?.ville;

                        const payload = { ...this.model, ...formValues, ville };

                        this.offresService.saveSociete(payload).subscribe(res => {
                            this.back(), (this.isLoading = false);
                            this.alertService.successAlt(`Le fournisseur <b>${payload?.raisonSociale}</b> a été enregistré avec succès.`, 'Fournisseur Enregistré', 'MODAL')
                        });
                    }
                },
                () => null
            );
    }

    fetchSocieteById(id: number): void {
        this.offresService.getSocieteById(id).subscribe(res => {
            this.model = res;
            this.patchForm();
        })
    }

    back(): void {
        this.router.navigate(['achats-groupes/gestion-fournisseurs/liste']);
    }

    getListeVilles(): void {
        this.staticDataService.getListCitiesByCountryId(1).subscribe((res) => {
            this.ville = res;
        });
    }

    getListeLocalites(): void {
        this.staticDataService.getListLocalitiesByCountryId().subscribe((res) => {
            this.localites = res;
        });
    }

    searchVille: OperatorFunction<string, readonly string[]> = (
        text$: Observable<string>
    ) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                return term?.length < 1
                    ? of([])
                    : of(
                        this.ville?.filter((ville) =>
                            ville?.labelFr?.toLowerCase()?.includes(term.toLowerCase())
                        )
                    );
            }),
            map((res) => res?.slice(0, 5))
        );

    searchLocalite: OperatorFunction<any, readonly any[]> = (
        text$: Observable<string>
    ) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            map((term) =>
                term === ''
                    ? []
                    : this.localites
                        .filter(
                            (v) => v.localite.toLowerCase().indexOf(term.toLowerCase()) > -1
                        )
                        .slice(0, 5)
            )
        );

    villeFormatter = (result: ICity) => result?.labelFr;
    localiteFormatter = (result: Localite) => result?.localite;
}