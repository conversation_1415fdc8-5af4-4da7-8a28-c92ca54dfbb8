import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Offre, Pagination } from "@wph/data-access";
import { BehaviorSubject, Observable } from "rxjs";
import { FsOffreCriteria, SearchOffre } from "../models/fs-offre.model";
import { ConditionBlocOffreCommandeConsolidee } from "../models/fs-offre-util.model";
import { DomainEnumeration } from "@wph/shared";
import { EntrepriseCriteriaDto, EntrepriseDTO, SearchEntrepriseDto } from "../models/entreprise.model";

@Injectable({
    providedIn: 'root'
})
export class FsOffreService {
    private baseUrl: string;

    selectedGroupes$: BehaviorSubject<number[]> = new BehaviorSubject<number[]>(null);

    constructor(@Inject('ENVIROMENT') private env: any, private http: HttpClient) {
        this.baseUrl = this.env?.base_url
    }

    searchFsOffres(pagination: Pagination, criteria: FsOffreCriteria): Observable<SearchOffre> {
        let params = {
            page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
            size: String(pagination.pageSize) ?? 20,
        }

        if (pagination.sortField) {
            params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
        }

        return this.http.post<SearchOffre>(`${this.baseUrl}/api/v1/offre-achat-groupe/search`, criteria, { params, observe: 'body' });
    }

    accepterFsOffre(cmdConsolideeId: number): Observable<any> {
        return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/accepter-offre`, { params: { cmdConsolideeId }, observe: 'body' });
    }

    finSaisieFsOffre(cmdConsolideeId: number): Observable<any> {
        return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/fin-saisie-offre`, { params: { cmdConsolideeId }, observe: 'body' });
    }

    publierFsOffre(offreId: number, groupes: number[]): Observable<any> {
        let params = {};
        groupes && (params['groupes'] = groupes);

        return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/${offreId}/publier-offre`, { params, observe: 'body' });
    }

    reactiverFsOffre(cmdConsolideeId: number): Observable<any> {
        return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/reactiver-offre`, { params: { cmdConsolideeId }, observe: 'body' });
    }

    refuserFsOffre(cmdConsolideeId: number): Observable<any> {
        return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/refuser-offre`, { params: { cmdConsolideeId }, observe: 'body' });
    }

    saveConditionsCommandeConsolidee(cmdConsolideeId: number, payload: ConditionBlocOffreCommandeConsolidee[]): Observable<any> {
        return this.http.post<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/save-conditions-cmd-consolidee`, payload, { params: { cmdConsolideeId }, observe: 'body' });
    }

    supprimerConditionsCommandeConsolidee(conditionCmdConsolideeId: number[]): Observable<any> {
        return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/delete-conditions-cmd-consolidee`, { params: { conditionCmdConsolideeId }, observe: 'body' });
    }

    attacherDistributeurCommandeConsolidee(cmdConsolideeId: number, distributeurId: number): Observable<any> {
        return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/attacher-distributeur-cmd`, { params: { distributeurId, cmdConsolideeId }, observe: 'body' });
    }

    getDomaineEnumeration(domaine: string): Observable<DomainEnumeration[]> {
        return this.http.get<DomainEnumeration[]>(`${this.baseUrl}/api/domainenumeration/list`, { params: { domaine }, observe: 'body' });
    }

    searchEntreprise(criteria: Partial<EntrepriseCriteriaDto>, pagination: Pagination): Observable<SearchEntrepriseDto> {
        const params = {
            pageNumber: pagination ? String(this.getPageNumber(pagination?.skip, pagination?.pageSize)) : 0,
            pageSize: pagination ? String(pagination?.pageSize) : 20
        };

        return this.http.post<SearchEntrepriseDto>(`${this.baseUrl}/api/v1/entreprise/search`, criteria, { params, observe: 'body' });
    }

    getSocietesGrossiste(): Observable<EntrepriseDTO[]> {
        return this.http.get<EntrepriseDTO[]>(`${this.baseUrl}/api/v1/entreprise/societes-grossite`, { observe: 'body' });
    }

    getSocietesTransporteur(): Observable<EntrepriseDTO[]> {
        return this.http.get<EntrepriseDTO[]>(`${this.baseUrl}/api/v1/entreprise/societes-transporteur`, { observe: 'body' });
    }

    reouvrirCommandeConsolidee(offreId: number): Observable<any> {
        return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/${offreId}/reouvrir-cmd-consolidee`, { observe: 'body' });
    }

    creerCommandeConsolidee(offreId: number, groupeId: number): Observable<Offre> {
        return this.http.get<Offre>(`${this.baseUrl}/api/v1/offre-achat-groupe/${offreId}/creer-cmd-consolidee`, { observe: 'body', params: { groupeId } })
    }

    getOpenedCmdConsolideeIdByOffre(groupeId: number, offreId: number): Observable<any> {
        return this.http.get<any>(`${this.baseUrl}/api/v1/offre-achat-groupe/get-opened-cmd-consolidee-id-by-offre`, { observe: 'body', params: { groupeId, offreId } })
    }

    getPageNumber(skip: number, pageSize: number) {
        return (skip && pageSize) ? Math.floor(skip / pageSize) : 0;
    }

    getCommandeGroupeParameters(): Observable<{ [key: string]: boolean }> {
        return this.http.get<{ [key: string]: boolean }>(`${this.baseUrl}/api/commande-groupe/parameters`, { observe: 'body' });
    }

    updateCommandeGroupeParameters(parameters: { [key: string]: boolean }): Observable<{ [key: string]: boolean }> {
        return this.http.post<{ [key: string]: boolean }>(`${this.baseUrl}/api/commande-groupe/parameters`, parameters, { observe: 'body' });
    }
}