<!-- start page title -->
<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-5">Liste Pharmacies du Maroc</h4>

    <div class="col-7 px-1" *ngIf="!(isInactive$ | async)">
      <div class="row d-flex justify-content-end align-items-center" style="gap: 10px">
        <button *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" (click)="synchroniser()" class="btn btn-sm btn-success"
          title="Ajouter un produit" style="padding-block: 6px;">
          <i class="bi bi-arrow-repeat"></i>
          Synchroniser
        </button>

        <button (click)="suggererPharmacie()" class="btn btn-sm btn-primary" title="Ajouter un produit"
          style="padding-block: 6px;">
          <i class="mdi mdi-assistant"></i>
          Ajouter Pharmacie
        </button>
      </div>
    </div>
  </div>
</div>
<!-- end page title -->

<div class="row d-flex m-0 px-1">
  <div class="card m-0 w-100 p-0" style="height: calc(100vh - 60px);">
    <div class="card-header py-1 pl-2 bg-white">
      <div class="row p-0">
        <div class="col-12 p-0 d-flex justify-content-end">
          <div class="row p-0 justify-content-center justify-content-sm-end">
            <div class="col-sm p-0 m-1">
              <div class="input-group picker-input">
                <input type="search" placeholder="Raison sociale" [formControl]="filterList"
                  class="form-control form-control-md pl-4" id="groupeCritere" />

                <div class="picker-icons picker-icons-alt">
                  <i class="mdi mdi-magnify pointer"></i>
                </div>
              </div>
            </div>


            <button type="button" (click)="displayFilter = !displayFilter" class="btn btn-sm search-btn m-1">
              <span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
                <i class="bi bi-sliders"></i>
                <span class="mx-1">Recherche Avancé</span>
              </span>

              <ng-template #closeFilter>
                <span class="d-flex align-items-center">
                  <i class="mdi mdi-close"></i>
                  <span class="mx-1">Fermer la recherche</span>
                </span>
              </ng-template>

            </button>

            <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
          </div>
        </div>
      </div>
    </div>

    <form [formGroup]="filterForm" (ngSubmit)="appliquerFiltre($event)" wphFocusTrap>
      <div id="advanced-search" *ngIf="displayFilter" class="row p-0 flex-wrap d-flex px-1 my-2 gap-1">
        <div class="col-auto p-0 m-0">
          <label for="nomResponsable" class="col-12 col-form-label text-left">Nom Résponsable</label>

          <div class="col-12 input-group">
            <input type="text" name="nomResponsable" formControlName="nomResponsable"
              class="form-control form-control-md b-radius bg-white" id="nomResponsable">
          </div>
        </div>

        <div class="col-auto p-0 m-0">
          <label for="raisonSociale" class="col-12 col-form-label text-left">Raison sociale</label>

          <div class="col-12 input-group">
            <input type="text" name="raisonSociale" formControlName="raisonSociale"
              class="form-control form-control-md b-radius bg-white" id="raisonSociale">
          </div>
        </div>

        <div class="col-auto p-0 m-0">
          <label for="ville" class="col-12 col-form-label text-left">Ville</label>

          <div class="col-12 input-group">
            <input type="text" name="ville" formControlName="ville"
              class="form-control form-control-md b-radius bg-white" id="ville">
          </div>
        </div>

        <div class="col-auto p-0 m-0">
          <label for="localite" class="col-12 col-form-label text-left">Localité</label>

          <div class="col-12 input-group">
            <input type="text" name="localite" formControlName="localite"
              class="form-control form-control-md b-radius bg-white" id="localite">
          </div>
        </div>

        <div class="col-lg-2 col-5 p-0 m-0">
          <label for="filterOptions" class="col-12 col-form-label text-left">Filtrer par</label>
          <div class="col-12 input-group">
            <select2 (focus)="selectFilterOptions.toggleOpenAndClose()" formControlName="filterOptions" [data]="selectFilterData"
              class="form-control-md b-radius bg-white w-100" #selectFilterOptions>
            </select2>
          </div>
        </div>

        <div class="col d-flex align-items-end p-0">
          <button type="button" class="btn btn-sm btn-outline-primary b-radius" (click)="vider()">
            <i class="bi bi-arrow-clockwise"></i>
          </button>
          <button type="submit" class="btn btn-sm btn-primary b-radius mx-1">
            <i class="mdi mdi-filter"></i>
          </button>
        </div>
      </div>
    </form>

    <div class="card-body m-0 p-0 bg-white mt-1" style="height:calc(100vh - 125px)">
      <kendo-grid class="fs-grid fs-listing-grid" [data]="gridView" (pageChange)="pageChange($event)"
        [pageSize]="navigation.pageSize" [skip]="navigation.skip" [pageable]="{
                  buttonCount: 5,
                  info: true,
                  type: 'numeric',
                  previousNext: true,
                  position: 'bottom'
                }" [sortable]="{ mode: 'single' }" [sort]="pharmacieSort" (sortChange)="pharmacieGridSort($event)"
        style="height: 100%" [selectable]="true" (selectionChange)="onRowClick($event)">
        <kendo-grid-column field="isMembreGroupe" [width]="50">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap">Membre Groupe</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            <div class="d-flex justify-content-center align-items-center">
              <span class="d-flex align-items-center actions-icons"
                [ngClass]="dataItem.isMembreGroupe ? 'btn-success' : 'btn-danger'">
                <i class="bi" [ngClass]="dataItem.isMembreGroupe ? 'bi-check' : 'bi-x'"></i>
            </span>
            </div>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" field="userSuggereur" title="Résponsable"
          [width]="170" class="text-wrap">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ dataItem?.nomResponsable }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="raisonSociale" [width]="170" class="text-wrap">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            PH. {{ dataItem?.raisonSociale }}
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Raison sociale</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'alpha'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="ville" [width]="100" class="text-wrap">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ dataItem?.ville ?? dataItem?.localite }}
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Ville / Localité</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'alpha'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="telephone" title="Téléphone" [width]="120">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <span *ngIf="!dataItem?.telephone" class="d-flex align-items-center justify-center k-gap-1 text-warning">
              <i class="bi bi-shield-fill-exclamation"></i>
              indisponible
            </span>
            {{ dataItem?.telephone }}
          </ng-template>
          
          <ng-template kendoGridHeaderTemplate let-column>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column *ngIf="!searchCriteria.deletedOnly" title="Statut" class="text-center" [width]="80"
          filter="numeric">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span (click)="activerOuDesactiverPH($event, dataItem)" class="pr-4">
              <kendo-switch [readonly]="true" size="medium" onLabel="Actif" [checked]="dataItem?.statutEntreprise"
                offLabel="Inactif" class="status-switch-align">
              </kendo-switch>
            </span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column title="Action" [width]="60" class="no-ellipsis">
          <ng-template kendoGridCellTemplate let-dataItem>
            <div class="d-flex justify-content-between flex-row-reverse" style="width: 70px;">

              <!-- Toggle Delete/Restore Action -->
              <span class="actions-icons pointer-cus"
                [ngClass]="{ 'btn-danger': !searchCriteria.deletedOnly, 'btn-primary': searchCriteria.deletedOnly }"
                [title]="searchCriteria.deletedOnly ? 'Restaurer' : 'Supprimer'"
                (click)="toggleSupprimer($event, dataItem)">
                <i [class]="searchCriteria.deletedOnly ? 'bi bi-arrow-clockwise' : 'bi bi-trash'"></i>
              </span>

              <!-- Edit Action (Hidden if Pharmacy is Deleted) -->
              <span *ngIf="!searchCriteria.deletedOnly" class="actions-icons btn-success pointer-cus"
                title="Modifier Groupe" (click)="onModifyClick($event, dataItem)">
                <i class="bi bi-pencil-square"></i>
              </span>
            </div>
          </ng-template>
        </kendo-grid-column>

        <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
          <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
            [navigation]="navigation" style="width: 100%;" (pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
        </ng-template>


        <ng-template kendoGridNoRecordsTemplate>
          <span>Aucun résultat trouvé.</span>
        </ng-template>
      </kendo-grid>
      <ng-template #emptyDate>
        <span>--/--/----</span>
      </ng-template>
    </div>
  </div>

</div>