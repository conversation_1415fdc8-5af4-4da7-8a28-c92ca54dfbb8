<!-- Start Of Header -->
<wph-pdf-viewer [src]="blobUrl" title="Imprimer Bon de Sortie"></wph-pdf-viewer>
<div class="rowline mb-0" *ngIf="!isLoading">
  <div class="page-title-box row">
    <div class="d-flex k-gap-2 align-items-center ml-2">
      <button class="actions-icons action-back btn text-white" (click)="back()">
        <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
      </button>
      <h4 class="page-title fw-4 ps-2">
        <span *ngIf="!isConsult &&  !enteteBl && !isUnitaire && !isIndividuelle">
          <span class="d-md-none">Nouveau BL</span>
          <span class="d-none d-md-inline">Nouveau Bon de Livraison</span>
        </span>
        <span *ngIf="isConsult && idBL && !isUnitaire">
          <span class="d-md-none">Consultation BL-{{enteteBl?.numeroBl}}</span>
          <span class="d-none d-md-inline">Consultation Bon de Livraison {{enteteBl?.numeroBl}}</span>
        </span>
        <span *ngIf="!isConsult && enteteBl?.etatBl === 'BROUILLON'">
          <span class="d-md-none">Modification BL-{{enteteBl?.numeroBl}}</span>
          <span class="d-none d-md-inline">Modification Bon de Livraison {{enteteBl?.numeroBl}}</span>
        </span>
        <span *ngIf="isUnitaire">
          <span class="d-md-none">Consultation BS-{{enteteBl?.numeroBl}}</span>
          <span class="d-none d-md-inline">Consultation Bon de Sortie BL-{{enteteBl?.numeroBl}} - PH. {{enteteBl?.enteteCommandeAchatGroupe?.client?.raisonSociale}}</span>
        </span>
        <span *ngIf="isIndividuelle && !enteteBl">
          <span class="d-md-none">Nouveau BL Individuelle</span>
          <span class="d-none d-md-inline">Nouveau Bon de Livraison Individuel</span>
        </span>
      </h4>
    </div>

    <div class="px-1 d-none d-lg-flex">
      <div class="row justify-content-end align-items-center">
        <ng-container *ngIf="!(isInactive$ | async)">
          <button *ngIf="!isConsult && !isUnitaire" type="button"
            class="btn btn-sm btn-primary m-1 btn-fs-size  d-flex align-items-center justify-content-center k-gap-1"
            (click)="processSaveBl()">
            <i class="bi bi-bookmark-check-fill"></i>
            <span class="d-none d-md-inline">Enregistrer Brouillon</span>
          </button>

          <button *ngIf="enteteBl?.etatBl === 'BROUILLON' && !isUnitaire" type="button" (click)="isIndividuelle ? AnullerBlIndividuel() : AnullerBl()"
            class="btn btn-sm btn-danger m-1 btn-fs-size  d-flex align-items-center justify-content-center k-gap-1">
            <i class="bi bi-trash"></i>
            <span class="d-none d-md-inline">Supprimer</span>
          </button>

          <button type="button" *ngIf="!isUnitaire && enteteBl?.etatBl === 'BROUILLON' || !enteteBl"
            class="btn btn-sm btn-success m-1 btn-fs-size  d-flex align-items-center justify-content-center k-gap-1"
            (click)="isIndividuelle ? validateBlIndividuel() : processSaveBl(true)">
            <i class="bi bi-check"></i>
            <span class="d-none d-md-inline">Valider</span>
          </button>

        <ng-container *ngIf="!isIndividuelle">
          <button type="button"
            *ngIf="isConsult && !isUnitaire && enteteBl?.etatBl === 'VALIDE' || !isUnitaire && enteteBl?.etatBl === 'REPARTI'"
            (click)="dispatchBL()"
            class="btn btn-sm btn-success m-1 btn-fs-size  d-flex align-items-center justify-content-center k-gap-1">
            <i [class]="enteteBl.etatBl === 'REPARTI' ? 'bi bi-file-earmark-text' : 'bi bi-distribute-horizontal'"></i>

            <span class="d-none d-md-inline">{{ enteteBl?.etatBl === 'REPARTI' ? 'Consulter Repartition' : 'Répartitir'
              }}</span>
          </button>
        </ng-container>

        </ng-container>

        <button type="button"
          *ngIf="enteteBl?.etatBl === 'REPARTI' ||  enteteBl?.etatBl === 'VALIDE' || this.saisieBLForm.get('enteteCommandeAchatGroupe')?.value"
          class="btn btn-sm btn-success m-1 btn-fs-size  d-flex align-items-center justify-content-center k-gap-1"
          (click)="consulterCommande()">
          <i class="bi bi-eye" style="line-height: 1;"></i>
          <span class="d-none d-md-inline">Consulter Commande</span>
        </button>


        <button type="button" *ngIf="isUnitaire" (click)="Imprimer()"
          class="btn btn-md Imprimer-btn m-1 b-radius  d-flex align-items-center justify-content-center k-gap-1">
          <i class="bi bi-printer-fill"></i>
          <span class="d-none d-md-inline">Imprimer</span>
        </button>

        <button (click)="back()" type="button" style="padding-block: 6px;" class="btn btn-sm btn-dark text-white m-1">
          <i class="mdi mdi-close"></i> Quitter
        </button>
      </div>
    </div>
  </div>
</div>
<!-- END HEADER -->

<div class="row mx-2" *ngIf="!isLoading">
  <div class="card bg-transparent my-1 w-100">
    <div>
      <ng-container [ngTemplateOutlet]="BLPage"></ng-container>
    </div>
  </div>
</div>

<ng-template #BLPage>
  <div class="card" *ngIf="!isLoading">
    <div class="card">
      <div class="card-body p-1">
        <form class="p-0 m-0" autocomplete="off" [formGroup]="saisieBLForm">
          <div class="px-1 bg-white mb-sm-0">
            <div class="row divider-y" wphFocusTrap>
              <div class="col-md-4">
                <div class="col-sm-12  my-1 p-0">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">
                      Distributeur <span class="text-danger">*</span>
                    </label>
                    <div class="input-group picker-input">
                      <input type="text" id="raisonSociale" [ngClass]="{'is-invalid': hasError('fournisseur')}"
                        class="form-control pl-4 " placeholder="Entrez Le Distribiteur" formControlName="fournisseur"
                        [ngbTypeahead]="searchFournisseur" [resultFormatter]="formatter" [inputFormatter]="formatter"
                        [editable]="false" [readOnly]="enteteBl" />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-truck"></i>
                      </div>
                    </div>
                    <div class="text-danger" *ngIf="hasError('fournisseur')">
                      champ obligatoire, veuillez saisir un Distribiteur
                    </div>
                  </div>
                </div>
                <div class="col-sm-12  my-1 p-0">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">
                      Commande <span class="text-danger">*</span>
                    </label>
                    <div class="input-group picker-input">
                      <input type="text" id="raisonSociale"
                        [ngClass]="{'is-invalid': hasError('enteteCommandeAchatGroupe')}" class="form-control pl-4"
                        [ngbTypeahead]="isIndividuelle ? searchForCommandeIndividuel : searchForCommande" (selectItem)="commandeSelected($event)"
                        [resultFormatter]="isIndividuelle ? commandFormatterIndividuel : commandFormatter" [inputFormatter]="isIndividuelle ? commandFormatterIndividuel : commandFormatter" [editable]="false"
                        formControlName="enteteCommandeAchatGroupe" placeholder="veuillez donner le code commande"
                        [readOnly]="enteteBl" #commandeInput wphAutoFocus />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-receipt"></i>
                      </div>
                      <div class="picker-icons picker-icons-end"
                        *ngIf="saisieBLForm.get('enteteCommandeAchatGroupe').value && !enteteBl && !idCommande">
                        <i class="bi bi-x" style="font-size: 22px; cursor: pointer; line-height: 1;"
                          (click)="clearCommande(commandeInput)"></i>
                      </div>
                    </div>
                    <div class="text-danger" *ngIf="hasError('enteteCommandeAchatGroupe')">
                      champ obligatoire, veuillez choisir une Commande
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-8">
                <div class="row">
                  <div class="col-12  my-1">
                    <div class="row">
                      <div class="col-md-4">
                        <div class="form-group mb-0">
                          <label for="raisonSociale" class="form-label p-0 col-12">
                            Montant {{isUnitaire ? 'BS' : 'BL' }} <span class="text-danger">*</span>
                          </label>
                          <div class="input-group picker-input">
                            <input type="text" id="raisonSociale" [ngClass]="{'is-invalid': hasError('montantSaisi')}"
                              formControlName="montantSaisi" class="form-control pl-4" wphAllowOnlyNumbers [allowDecimal]="true"
                               [errorMessage]="'Le Montant BL doit être valide'" placeholder="Entrez Montant BL" [maxValue]="99999999999" />
                            <div class="picker-icons picker-icons-alt">
                              <i class="bi bi-cash"></i>
                            </div>
                          </div>
                          <div class="text-danger" *ngIf="hasError('montantSaisi')">
                            champ obligatoire, veuillez saisir Le Montant BL
                          </div>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group mb-0">
                          <label for="raisonSociale" class="form-label p-0 col-12">
                            Remise <span class="text-danger">*</span>
                          </label>
                          <div class="input-group picker-input">
                            <input type="text" id="raisonSociale" [ngClass]="{'is-invalid': hasError('tauxRf')}"
                              class="form-control pl-4" formControlName="tauxRf" placeholder="Entrez Remise"
                              wphAllowOnlyNumbers [allowDecimal]="true" [maxValue]="100" [errorMessage]="'La remise doit être un nombre entre 0 et 100'"

                              />
                            <div class="picker-icons picker-icons-alt">
                              <i class="bi bi-gift"></i>
                            </div>
                            <div class="picker-icons picker-icons-end" style="right: 2px;">
                              <i class="bi bi-percent" style="font-size: 15px;opacity: 0.85;color: black;"></i>
                            </div>
                            <div class="text-danger" *ngIf="hasError('tauxRf')">
                              champ obligatoire, veuillez saisir une Remise
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group mb-0">
                          <label for="raisonSociale" class="form-label p-0 col-12">
                            Montant BL Calculé <span class="text-danger">*</span>
                          </label>
                          <div class="input-group picker-input">
                            <input type="text" id="raisonSociale" [ngClass]="{'is-invalid': hasError('cumulBl')}"
                              class="form-control pl-4" formControlName="cumulBl" placeholder="Entrez Cumul BL"
                              [readOnly]="true" />
                            <div class="picker-icons picker-icons-alt">
                              <i class="bi bi-cash text-dark"></i>
                            </div>
                          </div>
                          <div class="text-danger" *ngIf="hasError('cumulBl')">
                            champ obligatoire, veuillez saisir un Cumul
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group mb-0">
                      <label for="transporteur" class="form-label p-0 col-12">Transporteur</label>
                      <div class="input-group picker-input">
                        <input type="text" id="transporteur" formControlName="raisonSocialeTransporteur" class="form-control pl-4" placeholder="Entrez Transporteur" />
                        <div class="picker-icons picker-icons-alt">
                          <i class="bi bi-hash text-dark"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group mb-0">
                      <label for="raisonSociale" class="form-label p-0 col-12 d-flex justify-content-between position-relative">
                        <span>N° BL<span class="text-danger">*</span></span>

                        <i *ngIf="!isReadOnly && saisieBLForm?.get('numeroBl')?.value?.length" class="char-count char-count-pos">{{ saisieBLForm?.get('numeroBl')?.value?.length || 0 }}/255</i>
                      </label>
                      <div class="input-group picker-input">
                        <input type="text" id="raisonSociale" class="form-control pl-4" formControlName="numeroBl"
                          placeholder="Entrez N° BL" [ngClass]="{'is-invalid': hasError('numeroBl')}" maxlength="255" />
                        <div class="picker-icons picker-icons-alt">
                          <i class="bi bi-hash text-dark"></i>
                        </div>
                      </div>
                      <div class="text-danger" *ngIf="hasError('numeroBl')">
                        champ obligatoire, veuillez saisir un N° BL
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group mb-0">
                      <label for="raisonSociale" class="form-label p-0 col-12">
                        Date De BL<span class="text-danger">*</span>
                      </label>
                      <div class="input-group">
                        <input type="text" [readOnly]="true" class="form-control form-control-md" ngClq
                          formControlName="dateReceptionBl" id="dateDebut" ngbDatepicker #drange1="ngbDatepicker"
                          (click)="drange1.toggle()" [ngClass]="{'bg-white': !isConsult}">

                        <div class="input-group-append">
                          <button *ngIf="!isConsult" type="button" (click)="drange1.toggle()" tabindex="-1"
                            class="btn btn-md btn-light text-dark btn-outline-light calendar">
                            <i class="mdi mdi-calendar"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="card-footer p-0">
      <div class="card bg-transparent -mt-3 text-dark rounded">
        <div class="card-body bg-white px-1 pt-1">

          <ng-template #noCommande>
            <div class="d-flex justify-content-center align-items-center w-100 h-100 my-3">
              <h3 class="text-dark font-20 m-0">
                <div *ngIf="saisieBLForm.get('enteteCommandeAchatGroupe').value && !products.length && !enteteBl"
                  class="d-flex flex-column align-items-center justify-content-center k-gap-4">
                  <div>
                    <svg width="185" height="113" viewBox="0 0 185 113" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M176.64 16.575C180.908 16.575 184.368 20.0381 184.368 24.31C184.368 28.5819 180.908 32.045 176.64 32.045H132.48C136.748 32.045 140.208 35.5081 140.208 39.78C140.208 44.0519 136.748 47.515 132.48 47.515H156.768C161.036 47.515 164.496 50.9781 164.496 55.25C164.496 59.5219 161.036 62.985 156.768 62.985H145.536C140.155 62.985 135.792 66.4481 135.792 70.72C135.792 73.568 138 76.1463 142.416 78.455C146.684 78.455 150.144 81.9181 150.144 86.19C150.144 90.4619 146.684 93.925 142.416 93.925H50.784C46.5159 93.925 43.056 90.4619 43.056 86.19C43.056 81.9181 46.5159 78.455 50.784 78.455H7.728C3.45994 78.455 0 74.9919 0 70.72C0 66.4481 3.45994 62.985 7.728 62.985H51.888C56.1561 62.985 59.616 59.5219 59.616 55.25C59.616 50.9781 56.1561 47.515 51.888 47.515H24.288C20.0199 47.515 16.56 44.0519 16.56 39.78C16.56 35.5081 20.0199 32.045 24.288 32.045H68.448C64.1799 32.045 60.72 28.5819 60.72 24.31C60.72 20.0381 64.1799 16.575 68.448 16.575H176.64ZM176.64 47.515C180.908 47.515 184.368 50.9781 184.368 55.25C184.368 59.5219 180.908 62.985 176.64 62.985C172.372 62.985 168.912 59.5219 168.912 55.25C168.912 50.9781 172.372 47.515 176.64 47.515Z" fill="#950CFF" fill-opacity="0.21"/>
                      <path d="M121.078 2.65515V15.47C121.078 17.3008 122.561 18.785 124.39 18.785H133.149" stroke="#8E3ACF" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M117.774 15.47L128.036 90.43L128.958 97.9503C129.255 100.373 127.533 102.578 125.112 102.876L60.4547 110.828C58.0337 111.125 55.8303 109.403 55.5333 106.98L45.5876 25.8494C45.4391 24.638 46.3 23.5353 47.5105 23.3865C47.5181 23.3855 47.5258 23.3846 47.5334 23.3838L52.8977 22.781M57.2339 22.2878L62.2981 21.7189L57.2339 22.2878Z" fill="white"/>
                      <path d="M119.012 15.3002C118.919 14.6162 118.288 14.1378 117.604 14.2316C116.92 14.3254 116.442 14.9559 116.536 15.6399L119.012 15.3002ZM128.036 90.43L129.277 90.2776C129.276 90.2718 129.276 90.266 129.275 90.2602L128.036 90.43ZM128.958 97.9503L130.199 97.798L128.958 97.9503ZM125.112 102.876L125.265 104.117L125.112 102.876ZM60.4547 110.828L60.607 112.068L60.4547 110.828ZM55.5333 106.98L56.774 106.828L55.5333 106.98ZM45.5876 25.8494L44.3469 26.0017L45.5876 25.8494ZM47.5334 23.3838L47.6727 24.626L47.5334 23.3838ZM53.037 24.0232C53.723 23.9462 54.2168 23.3275 54.1398 22.6415C54.0629 21.9554 53.4444 21.4617 52.7584 21.5388L53.037 24.0232ZM57.0946 21.0456C56.4085 21.1227 55.9147 21.7413 55.9917 22.4274C56.0686 23.1134 56.6871 23.6071 57.3731 23.53L57.0946 21.0456ZM62.4373 22.9611C63.1234 22.884 63.6172 22.2654 63.5402 21.5793C63.4633 20.8933 62.8448 20.3996 62.1588 20.4767L62.4373 22.9611ZM116.536 15.6399L126.798 90.5998L129.275 90.2602L119.012 15.3002L116.536 15.6399ZM126.796 90.5824L127.718 98.1027L130.199 97.798L129.277 90.2776L126.796 90.5824ZM127.718 98.1027C127.931 99.841 126.695 101.422 124.96 101.635L125.265 104.117C128.372 103.735 130.58 100.905 130.199 97.798L127.718 98.1027ZM124.96 101.635L60.3024 109.587L60.607 112.068L125.265 104.117L124.96 101.635ZM60.3024 109.587C58.5674 109.8 56.9871 108.566 56.774 106.828L54.2927 107.132C54.6736 110.24 57.5 112.451 60.607 112.068L60.3024 109.587ZM56.774 106.828L46.8282 25.697L44.3469 26.0017L54.2927 107.132L56.774 106.828ZM46.8282 25.697C46.7636 25.17 47.1382 24.6917 47.6628 24.6272L47.3582 22.1458C45.4618 22.379 44.1145 24.106 44.3469 26.0017L46.8282 25.697ZM47.6628 24.6272C47.6661 24.6267 47.6694 24.6264 47.6727 24.626L47.3941 22.1415C47.3821 22.1429 47.3701 22.1443 47.3582 22.1458L47.6628 24.6272ZM47.6727 24.626L53.037 24.0232L52.7584 21.5388L47.3941 22.1415L47.6727 24.626ZM57.3731 23.53L62.4373 22.9611L62.1588 20.4767L57.0946 21.0456L57.3731 23.53ZM62.1588 20.4767L57.0946 21.0456L57.3731 23.53L62.4373 22.9611L62.1588 20.4767Z" fill="#8E3ACF"/>
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M114.971 20.1875L124.266 88.127L125.102 94.9429C125.371 97.1386 123.832 99.1346 121.665 99.401L63.7687 106.516C61.6009 106.783 59.6252 105.219 59.3558 103.023L50.3326 29.4683C50.1981 28.372 50.9779 27.374 52.0742 27.2393L59.443 26.3337" fill="#AF65E8" fill-opacity="0.24"/>
                      <path d="M69.8777 1.25H120.76C121.489 1.25 122.189 1.53984 122.704 2.05574L137.791 17.1462C138.306 17.6619 138.596 18.3613 138.596 19.0905V88.82C138.596 90.3388 137.364 91.57 135.846 91.57H69.8777C68.3589 91.57 67.1277 90.3388 67.1277 88.82V4C67.1277 2.48122 68.3589 1.25 69.8777 1.25Z" fill="white" stroke="#8E3ACF" stroke-width="2.5"/>
                      <path d="M78.384 75.14H107.088M78.384 18.785H107.088H78.384ZM78.384 32.045H125.856H78.384ZM78.384 46.41H125.856H78.384ZM78.384 60.775H125.856H78.384Z" stroke="#AF65E8" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>


                  </div>
                  <p class="mb-0">Cette commande n'a pas de produits, veuillez choisir une autre commande.</p>
                  <button class="btn btn-danger rounded-pill" (click)="clearCommande(commandeInput,false)">
                    vider la commande
                  </button>
                </div>
                <div class="d-flex flex-column k-gap-8 text-center mt-4" *ngIf="!saisieBLForm.get('enteteCommandeAchatGroupe').value && !products.length">
                  <div>
                    <svg width="185" height="113" viewBox="0 0 185 113" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M176.64 16.575C180.908 16.575 184.368 20.0381 184.368 24.31C184.368 28.5819 180.908 32.045 176.64 32.045H132.48C136.748 32.045 140.208 35.5081 140.208 39.78C140.208 44.0519 136.748 47.515 132.48 47.515H156.768C161.036 47.515 164.496 50.9781 164.496 55.25C164.496 59.5219 161.036 62.985 156.768 62.985H145.536C140.155 62.985 135.792 66.4481 135.792 70.72C135.792 73.568 138 76.1463 142.416 78.455C146.684 78.455 150.144 81.9181 150.144 86.19C150.144 90.4619 146.684 93.925 142.416 93.925H50.784C46.5159 93.925 43.056 90.4619 43.056 86.19C43.056 81.9181 46.5159 78.455 50.784 78.455H7.728C3.45994 78.455 0 74.9919 0 70.72C0 66.4481 3.45994 62.985 7.728 62.985H51.888C56.1561 62.985 59.616 59.5219 59.616 55.25C59.616 50.9781 56.1561 47.515 51.888 47.515H24.288C20.0199 47.515 16.56 44.0519 16.56 39.78C16.56 35.5081 20.0199 32.045 24.288 32.045H68.448C64.1799 32.045 60.72 28.5819 60.72 24.31C60.72 20.0381 64.1799 16.575 68.448 16.575H176.64ZM176.64 47.515C180.908 47.515 184.368 50.9781 184.368 55.25C184.368 59.5219 180.908 62.985 176.64 62.985C172.372 62.985 168.912 59.5219 168.912 55.25C168.912 50.9781 172.372 47.515 176.64 47.515Z" fill="#950CFF" fill-opacity="0.21"/>
                      <path d="M121.078 2.65515V15.47C121.078 17.3008 122.561 18.785 124.39 18.785H133.149" stroke="#8E3ACF" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M117.774 15.47L128.036 90.43L128.958 97.9503C129.255 100.373 127.533 102.578 125.112 102.876L60.4547 110.828C58.0337 111.125 55.8303 109.403 55.5333 106.98L45.5876 25.8494C45.4391 24.638 46.3 23.5353 47.5105 23.3865C47.5181 23.3855 47.5258 23.3846 47.5334 23.3838L52.8977 22.781M57.2339 22.2878L62.2981 21.7189L57.2339 22.2878Z" fill="white"/>
                      <path d="M119.012 15.3002C118.919 14.6162 118.288 14.1378 117.604 14.2316C116.92 14.3254 116.442 14.9559 116.536 15.6399L119.012 15.3002ZM128.036 90.43L129.277 90.2776C129.276 90.2718 129.276 90.266 129.275 90.2602L128.036 90.43ZM128.958 97.9503L130.199 97.798L128.958 97.9503ZM125.112 102.876L125.265 104.117L125.112 102.876ZM60.4547 110.828L60.607 112.068L60.4547 110.828ZM55.5333 106.98L56.774 106.828L55.5333 106.98ZM45.5876 25.8494L44.3469 26.0017L45.5876 25.8494ZM47.5334 23.3838L47.6727 24.626L47.5334 23.3838ZM53.037 24.0232C53.723 23.9462 54.2168 23.3275 54.1398 22.6415C54.0629 21.9554 53.4444 21.4617 52.7584 21.5388L53.037 24.0232ZM57.0946 21.0456C56.4085 21.1227 55.9147 21.7413 55.9917 22.4274C56.0686 23.1134 56.6871 23.6071 57.3731 23.53L57.0946 21.0456ZM62.4373 22.9611C63.1234 22.884 63.6172 22.2654 63.5402 21.5793C63.4633 20.8933 62.8448 20.3996 62.1588 20.4767L62.4373 22.9611ZM116.536 15.6399L126.798 90.5998L129.275 90.2602L119.012 15.3002L116.536 15.6399ZM126.796 90.5824L127.718 98.1027L130.199 97.798L129.277 90.2776L126.796 90.5824ZM127.718 98.1027C127.931 99.841 126.695 101.422 124.96 101.635L125.265 104.117C128.372 103.735 130.58 100.905 130.199 97.798L127.718 98.1027ZM124.96 101.635L60.3024 109.587L60.607 112.068L125.265 104.117L124.96 101.635ZM60.3024 109.587C58.5674 109.8 56.9871 108.566 56.774 106.828L54.2927 107.132C54.6736 110.24 57.5 112.451 60.607 112.068L60.3024 109.587ZM56.774 106.828L46.8282 25.697L44.3469 26.0017L54.2927 107.132L56.774 106.828ZM46.8282 25.697C46.7636 25.17 47.1382 24.6917 47.6628 24.6272L47.3582 22.1458C45.4618 22.379 44.1145 24.106 44.3469 26.0017L46.8282 25.697ZM47.6628 24.6272C47.6661 24.6267 47.6694 24.6264 47.6727 24.626L47.3941 22.1415C47.3821 22.1429 47.3701 22.1443 47.3582 22.1458L47.6628 24.6272ZM47.6727 24.626L53.037 24.0232L52.7584 21.5388L47.3941 22.1415L47.6727 24.626ZM57.3731 23.53L62.4373 22.9611L62.1588 20.4767L57.0946 21.0456L57.3731 23.53ZM62.1588 20.4767L57.0946 21.0456L57.3731 23.53L62.4373 22.9611L62.1588 20.4767Z" fill="#8E3ACF"/>
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M114.971 20.1875L124.266 88.127L125.102 94.9429C125.371 97.1386 123.832 99.1346 121.665 99.401L63.7687 106.516C61.6009 106.783 59.6252 105.219 59.3558 103.023L50.3326 29.4683C50.1981 28.372 50.9779 27.374 52.0742 27.2393L59.443 26.3337" fill="#AF65E8" fill-opacity="0.24"/>
                      <path d="M69.8777 1.25H120.76C121.489 1.25 122.189 1.53984 122.704 2.05574L137.791 17.1462C138.306 17.6619 138.596 18.3613 138.596 19.0905V88.82C138.596 90.3388 137.364 91.57 135.846 91.57H69.8777C68.3589 91.57 67.1277 90.3388 67.1277 88.82V4C67.1277 2.48122 68.3589 1.25 69.8777 1.25Z" fill="white" stroke="#8E3ACF" stroke-width="2.5"/>
                      <path d="M78.384 75.14H107.088M78.384 18.785H107.088H78.384ZM78.384 32.045H125.856H78.384ZM78.384 46.41H125.856H78.384ZM78.384 60.775H125.856H78.384Z" stroke="#AF65E8" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>


                  </div>
                  <p class="p-0">Aucune Commande Choisie</p>
                </div>
              </h3>
            </div>
          </ng-template>
          <div *ngIf="enteteBl?.lignes.length === 0" class="mt-4">
            <div class="d-flex flex-column align-items-center justify-content-center k-gap-2">
              <h3 class="text-dark font-20 m-0">
                <div class="d-flex flex-column align-items-center justify-content-center k-gap-2">
                    Cette BL n'a pas de produits.
                </div>
              </h3>
            </div>
            </div>


          <!-- Recherche avancée -->
          <div class="row d-flex m-0 px-1" >
            <div class="card m-0 w-100 p-0" style="max-height: calc(100vh - 123px);">
              <div class="card-header py-1 pl-2 bg-white">
                <div class="row p-0">
                  <div class="col-12 p-0 d-flex justify-content-between align-items-center">
                    <button *ngIf="!isConsult && !enteteBl" type="button" (click)="displayFilter = !displayFilter" class="btn btn-sm search-btn m-1">
                      <span *ngIf="!displayFilter; else closeFilter" class="d-flex align-items-center">
                        <i class="bi bi-sliders"></i>
                        <span class="mx-1">Recherche Avancée</span>
                      </span>
                      <ng-template #closeFilter>
                        <span class="d-flex align-items-center">
                          <i class="mdi mdi-close"></i>
                          <span class="mx-1">Fermer la recherche</span>
                        </span>
                      </ng-template>
                    </button>
                    <button *ngIf="!isConsult" (click)="openRechercheProduitModal()" type="button" class="btn  btn-success m-1 ml-auto rounded-pill">
                    <i class="bi bi-plus"></i>
                    Ajouter un produit
                  </button>
                  </div>
                  <div class="d-flex align-items-center">
                    <form [formGroup]="advFilterForm" (ngSubmit)="filterCommandes()" class="form-inline form-search-bl">
                      <div id="advanced-search" *ngIf="displayFilter" class="d-flex flex-wrap align-items-center">
                        <div class="form-group mx-2">
                          <label for="codeCommande" class="col-form-label text-left text-nowrap mr-2">N°
                            Commande</label>
                          <input type="text" id="codeCommande" class="form-control form-control-md b-radius bg-white"
                            formControlName="codeCommande" placeholder="Choisir la Commande" />
                        </div>
                        <div class="form-group mx-2">
                          <label for="offreur" class="col-form-label text-left text-nowrap mr-2">Offreur</label>
                          <input type="text" id="offreur" class="form-control form-control-md b-radius bg-white"
                            [ngbTypeahead]="searchForOffreur" formControlName="offreur"
                            [resultFormatter]="offreurFormatter" [inputFormatter]="offreurFormatter"
                            placeholder="Choisir l'offreur" [readOnly]="enteteBl" />
                        </div>
                        <div class="form-group mx-2 my-1">
                          <label for="distributeur"
                            class="col-form-label text-left text-nowrap mr-2">Distributeur</label>
                          <input type="text" id="distributeur" class="form-control form-control-md b-radius bg-white"
                            [ngbTypeahead]="searchForCommande" formControlName="distributeur"
                            [resultFormatter]="distributeurFormatter" [inputFormatter]="distributeurFormatter"
                            placeholder="Choisir le distributeur" [readOnly]="enteteBl" />
                        </div>
                        <div class="form-group mx-2 my-1">
                          <label for="comman7de" class="col-form-label text-left text-nowrap mr-2">Date Commande</label>
                          <div class="input-group">
                            <input type="text" [readOnly]="true" class="form-control form-control-md" ngClq
                              formControlName="dateCreationDebut" id="commande" ngbDatepicker #drange3="ngbDatepicker"
                              (click)="drange3.toggle()" [ngClass]="{'bg-white': !isConsult}">

                            <div class="input-group-append">
                              <button *ngIf="!isConsult" type="button" (click)="drange3.toggle()" tabindex="-1"
                                class="btn btn-md btn-light text-dark btn-outline-light calendar">
                                <i class="mdi mdi-calendar"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                        <div class="form-group d-flex align-items-end">
                          <button type="button" class="btn btn-sm btn-outline-primary b-radius mx-1"
                            (click)="clearFilterCommandes()">
                            <i class="bi bi-arrow-clockwise"></i>
                          </button>
                          <button type="submit" class="btn btn-sm btn-primary b-radius mx-1">
                            <i class="mdi mdi-filter"></i>
                            <span class="mx-1">Appliquer</span>
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>


          <!-- filter Results Grid -->


          <kendo-grid [data]="searchResults" class="fs-grid mb-3" (cellClick)="onRowSelect($event)"
            *ngIf="!isLoading && searchResults.length > 0 && !isConsult && displayFilter">
            <kendo-grid-column field="codeCommande" title="Code Commande"></kendo-grid-column>
            <kendo-grid-column title="Offre">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem?.offre?.titre ?? '---'}}
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column title="Offreur">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem?.offre?.offreur?.raisonSociale ?? '---'}}
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="distributeur.raisonSociale" title="Distributeur"></kendo-grid-column>
            <kendo-grid-column field="dateCreation" title="Date Commande">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.dateCreation | date: 'dd/MM/yyyy'}}
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="etatCommande" title="Etat Commande">
              <ng-template kendoGridCellTemplate let-dataItem>
                <app-element-status [state]="isIndividuelle ? dataItem.statut : dataItem.etatCommande"></app-element-status>
              </ng-template>
            </kendo-grid-column>
          </kendo-grid>

          <!-- Detailed Command Grid -->

          <kendo-grid [data]="gridData" class="fs-grid fs-grid-white" wphFocusTrapPrefixed prefix="ppv-" wphScrollCheck
            *ngIf="saisieBLForm.get('enteteCommandeAchatGroupe').value && products.length; else noCommande"
            [sortable]="{ mode: 'single'}" style="min-height:200px;max-height:calc(100vh - 340px);"
            [rowClass]="choseRowClass">
            <ng-template kendoGridToolbarTemplate>
              <div
                class="d-flex justify-content-center justify-content-md-between px-2 align-items-center w-100 flex-wrap k-gap-2">
                <div>
                  <h3 class="text-white font-20 m-0 text-center">Liste Des Produits</h3>
                </div>
                <div
                  class="m-0 d-flex justify-content-center align-items-center k-gap-2 bl-header-actions  bl-input-search-wrapper">

                  <div class="input-group picker-input">
                    <input [ngModel]="filter" (ngModelChange)="onFilterBLProduit($event)" type="search"
                      placeholder="Rechercher par Designation" class="form-control form-control-md pl-4 bl-input-search"
                      id="groupeCritere" />

                    <div class="picker-icons picker-icons-alt">
                      <i class="mdi mdi-magnify pointer"></i>
                    </div>
                  </div>
                </div>
              </div>

            </ng-template>
            <kendo-grid-column  title="" [width]="50" [hidden]="currentPlateforme !== 'WIN_GROUPE' || isConsult">
              <ng-template kendoGridCellTemplate let-dataItem>
                <div class="d-flex justify-content-center k-gap-2" *ngIf="dataItem.isExtraLine && !isConsult">
                  <span (click)="deleteInsertedLine(dataItem)" class="actions-icons btn-danger pointer-cus" title="Supprimer">
                    <i class="bi bi-trash"></i>
                  </span>
                </div>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="designation" title="Désignation" class="text-wrap" [sortable]="false"
              [width]="300">
              <ng-template kendoGridCellTemplate let-dataItem>
                <div class="d-flex align-items-center k-gap-1" >
                 {{dataItem.designation}}  <span *ngIf="dataItem.isCadeau" class="text-success font-weight-bold">(Offert)</span>
                  <!-- <ng-container *ngIf="!isUnitaire && !isConsult">
                    <span *ngIf="dataItem?.quantiteLivree <= dataItem?.qteReliquatBl;else: inValidTemplate" class="text-danger font-weight-bold ml-auto">
                      <i class="bi bi-check-circle-fill" style="color: #007514; font-size: 20px; line-height: 1;"></i>
                    </span>
                    <ng-template #inValidTemplate>
                      <span class="text-danger font-weight-bold ml-auto" ngbTooltip="la quantité livrée ne peut pas être supérieure à la quantité qui reste non recupérable" [placement]="'bottom'">
                        <i class="bi bi-question-circle-fill" style="color: #D92D20; font-size: 20px; line-height: 1;"

                        ></i>
                      </span>
                    </ng-template>
                  </ng-container> -->
                </div>
               </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [hidden]="!isCoffretEnabled" title="Qté Fixe" [width]="100">
              <ng-template kendoGridCellTemplate let-dataItem>
                <input type="number" class="form-control py-0 px-1  text-right" [readOnly]="true"
                  [ngModel]="dataItem.qteFixePrdInCoffret" />
              </ng-template>

            </kendo-grid-column>
            <kendo-grid-column field="quantiteCommandee" [width]="80" title="Qté Cmd" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem>
                <input type="number" class="form-control py-0 px-1" *ngIf="!dataItem.isCadeau" tabindex="-1"
                  [readOnly]="true" value="{{dataItem.quantiteCommandee}}"  />
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="pph" title="PPH" [width]="80" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <input type="number" class="form-control py-0 px-1  text-right" [readOnly]="true"
                  [ngModel]="dataItem.pph" />
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="ppv" title="PPV" [width]="80" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <input type="number" class="form-control py-0 px-1  text-right" [readOnly]="true"
                  [ngModel]="dataItem.ppv" />
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="quantiteUgCmd" title="UG Cmd" [width]="80" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem>
                <input type="number" class="form-control py-0 px-1  text-right" tabindex="-1" [readOnly]="true"
                  [(ngModel)]="dataItem.quantiteUgCmd" />
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="qteReliquatBl" [width]="80" title="Reliquat" [sortable]="false"  class="end-phase"
            headerClass="end-phase" [hidden]="isUnitaire">
              <ng-template kendoGridCellTemplate let-dataItem>
                <input type="text" class="form-control py-0 px-1" *ngIf="!dataItem.isCadeau" tabindex="-1"
                  [readOnly]="true" value="{{dataItem.qteReliquatBl}}" />
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="quantiteLivree" title="Qté Livré" [width]="80" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem let-column="column" let-rowIndex="rowIndex">
                <input type="text" class="form-control py-0 px-1 text-right" data-prefix="livree"
                  [id]="'input-livree-'+rowIndex"
                  [readOnly]="isConsult" [(ngModel)]="dataItem.quantiteLivree" wphAllowOnlyNumbers
                  [maxValue]="9999999" [errorMessage]="'La quantité livrée erronée'"
                  (input)="onLivreeChanged('quantiteLivree', $event, dataItem)" *ngIf="!dataItem.isCadeau"
                  (focus)="selectInput($event)" />
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="quantiteUg" title="UG Livré" [width]="80" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <input type="text" class="form-control py-0 px-1  text-right"
                  [attr.data-prefix]="'ug'"
                  [id]="'input-ug-'+rowIndex" wphAllowOnlyNumbers
                  [maxValue]="999999999" [errorMessage]="'La quantité UG erronée'"
                  (input)="onLivreeChanged('quantiteUg', $event, dataItem)"
                  [readOnly]="isConsult" (focus)="selectInput($event)"
                  [(ngModel)]="dataItem.quantiteUg" />
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column class="text-right"   [hidden]="!(currentPlateforme === 'WIN_GROUPE')" field="oldPphRemise"  title="old PPH.R" [width]="60" [sortable]="false">

            </kendo-grid-column>
            <kendo-grid-column field="pphRemise"  title="PPH.R" [width]="80" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <input type="text" class="form-control py-0 px-1  text-right" [readOnly]="isConsult"
                  [(ngModel)]="dataItem.pphRemise" wphAllowOnlyNumbers [maxValue]="dataItem.pph" [allowDecimal]="true"
                  [errorMessage]="'PPH Remise ne peut pas être supérieur au PPH'"
                  (input)="onLivreeChanged('pphRemise', $event, dataItem)" (focus)="selectInput($event)"
                  *ngIf="!dataItem.isCadeau" data-prefix="pph-remise" [id]="'input-pph-remise-'+rowIndex" />
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="tauxRf" title="Remise" [width]="70" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <!-- picker-input-->
                <div class="input-group picker-input" [ngbTooltip]="dataItem.oldTauxRf && dataItem.oldTauxRf !== dataItem.tauxRf ? 'Ancienne Remise : ' + dataItem.oldTauxRf + '%' : ''" [placement]="'bottom-right'">
                  <input type="text" class="form-control py-0 px-1  text-right pr-3" [(ngModel)]="dataItem.tauxRf"
                    *ngIf="!dataItem.isCadeau" wphAllowOnlyNumbers [maxValue]="100" [allowDecimal]="true"
                    [errorMessage]="'La remise erronée'" [readOnly]="isConsult" data-prefix="remise"
                    [id]="'input-remise-'+rowIndex" (input)="onLivreeChanged('tauxRf', $event, dataItem)"
                    (focus)="selectInput($event)" />
                  <div class="picker-icons picker-icons-end" style="right: -4px;" *ngIf="!dataItem.isCadeau">
                    <i class="bi bi-percent" style="font-size: 15px;opacity: 0.85;color: black;"></i>
                  </div>
                </div>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="montant" title="Total" [width]="100" [sortable]="false" [hidden]="!isUnitaire">
              <ng-template kendoGridCellTemplate let-dataItem>
                <input type="text" class="form-control py-0 px-1  text-right" [(ngModel)]="dataItem.montant"
                  [readOnly]="true" />
              </ng-template>
            </kendo-grid-column>
            <!-- <kendo-grid-column field="montant" title="Montant" [width]="100" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem>
                <input type="text" class="form-control py-0 px-1" [(ngModel)]="dataItem.montant" />
              </ng-template>
            </kendo-grid-column> -->
            <!-- <kendo-grid-column field="datePeremption" title="Data peromption" [sortable]="false">
              <ng-template kendoGridCellTemplate let-dataItem>
                <input type="text" class="form-control py-0 px-1" value="{{dataItem.datePeremption | date: 'dd/MM/yyyy'}}" />
              </ng-template>
            </kendo-grid-column> -->
            <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
              pagerItemsPerPage="éléments par page"></kendo-grid-messages>

            <ng-template kendoGridNoRecordsTemplate>
              <span>Aucun résultat trouvé.</span>
            </ng-template>
          </kendo-grid>
          <div *ngIf="enteteBl || products.length">
            <div class="d-flex mt-2 align-items-center synthese-container flex-wrap ">
              <div class="border-right px-3 flex-grow-1 py-2 m-0 d-flex justify-content-between">
                <span class="font-18">Quantité Livré :</span> <span
                  class="font-18 font-weight-bold">{{synthese?.qLivree}}</span>
              </div>
              <div class="border-right px-3 flex-grow-1 py-2 m-0 d-flex justify-content-between">
                <span class="font-18">Montant Brut :</span> <span class="font-18 font-weight-bold">{{synthese?.mntBrut |
                  number:'1.2-2':'en-US'}}</span>
              </div>
              <div class="border-right px-3 flex-grow-1 py-2 m-0 d-flex justify-content-between">
                <span class="font-18">Montant Net :</span> <span class="font-18 font-weight-bold">{{synthese?.mntNet |
                  number:'1.2-2':'en-US'}}</span>
              </div>
              <div class=" px-3 flex-grow-1 py-2 m-0 d-flex justify-content-between">
                <span class="font-18">Montant Remise :</span> <span
                  class="font-18 font-weight-bold">{{synthese?.mntRemise | number:'1.2-2':'en-US'}}</span>
              </div>
            </div>
          </div>

          <!-- table footer -->
        </div>
      </div>
    </div>
  </div>
</ng-template>



<!-- Add New Prtoduct Modal -->


<ng-template #addProductModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Ajouter un produit</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span>&times;</span>
    </button>
  </div>
  <div class="modal-body">


    <ng-container [ngTemplateOutlet]="produitModal" ></ng-container>
    </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary"  >
          <i class="bi bi-x-circle me-1"></i>
          Annuler
        </button>
        <button type="button"
        [disabled]="!produitSearchResult?.data?.length"
                class="btn btn-success">
          <i class="bi bi-plus-circle me-1"></i>
          Ajouter 0 produit(s)
        </button>
      </div>

  </ng-template>


<ng-template  #produitModal let-modal>

    <div class="p-0 m-0" [id]="currentPlateforme  === 'WIN_OFFRE' ? 'WIN_OFFRE-container' : currentPlateforme  === 'FEDERATION_SYNDICAT' ? 'FEDERATION_SYNDICAT-container' : 'WIN_GROUPE-container'">
    <kendo-grid class="fs-grid fs-listing-grid"  [pageable]="true"
      [sortable]="{ mode: 'single'}"
      [data]="produitSearchResult"
      [pageSize]="searchProduitNavigation.pageSize"
      [skip]="searchProduitNavigation.skip"
      [resizable]="true" [selectable]="{mode: 'multiple', checkboxOnly: true}" kendoGridSelectBy="id">
      <kendo-grid-checkbox-column class="no-ellipsis" headerClass="no-ellipsis" [width]="40"
        [showSelectAll]="true"></kendo-grid-checkbox-column>

      <kendo-grid-column [width]="120" field="codeProduitCatalogue" title="Code">

      </kendo-grid-column>

      <kendo-grid-column [width]="350" field="libelleProduit" class="text-wrap" title="Libellé">

      </kendo-grid-column>

      <kendo-grid-column [width]="150" title="Laboratoire" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.fournisseur?.libelle }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="120" field="ppv" title="PPV" class="text-end">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.ppv | number:'1.2-2':'fr-FR'}}
        </ng-template>

      </kendo-grid-column>

      <kendo-grid-column [width]="120" field="prixVenteTtc" title="PPH" class="text-end">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.prixVenteTtc | number:'1.2-2':'fr-FR'}}
        </ng-template>


      </kendo-grid-column>

      <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de" pagerItemsPerPage="éléments par page"></kendo-grid-messages>

      <ng-template kendoGridNoRecordsTemplate>
        <span>Aucun résultat trouvé.</span>
      </ng-template>

      <!-- <ng-container *ngIf="(currentPlateforme$ | async) === 'WIN_OFFRE'">
        <kendo-grid-messages pagerItems="lignes" pagerOf="de" pagerItemsPerPage="éléments par page"></kendo-grid-messages>
      </ng-container> -->

      <!-- <ng-container *ngIf="(currentPlateforme$ | async) !== 'WIN_OFFRE'">
        <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
          <wph-grid-custom-pager [totalElements]="total" [totalPages]="!produitsListeSelectionne ? totalPages : 1" [currentPage]="currentPage"
            [navigation]="!produitsListeSelectionne ? navigation : {pageSize: produitsView?.total || 20, skip: 0}" style="width: 100%;"
            (pageChange)="pageProduitChange($event)"></wph-grid-custom-pager>
        </ng-template>
      </ng-container> -->

    </kendo-grid>
  </div>

</ng-template>




<wph-recherche-produit (produitSelectionChange)="onProduitSelected($event)"></wph-recherche-produit>
