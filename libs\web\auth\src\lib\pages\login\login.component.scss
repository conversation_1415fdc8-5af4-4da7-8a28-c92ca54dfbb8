.input-group-text{
  background-color: #e1e7f9 !important;
  border-top-left-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
  border:none !important;
  cursor: pointer !important;
}

.fs-bg {
  color: #f3f3f3 !important;
  background: var(--fs-primary-400) !important;
}

.wf-bg {
  color: #f3f3f3 !important;
  background: var(--wf-primary-400) !important;
}

.fs-text {
  color: var(--wf-primary-400);
}

.login-title {
  font-weight: 800;
  line-height: 1.8rem;
  font-size: clamp(1.6rem, 2vw, 2rem);
}

.card {
  border-radius: var(--winoffre-base-border-radius);
}

.inner-card {
  border: 1px solid #fff !important;
  background: transparent;

  .content {
    text-align: justify;
    color: #fff;
    font-weight: 700;
    font-size: clamp(2rem, 2vw, 2.5rem);
  }
}

button {
  font-size: 1rem;
  font-weight: 600;
  border-color: transparent !important;
  border-radius: var(--winoffre-base-border-radius);
}

.bg-gradient-wf {
  width: calc(100% - 10px); 
  height: calc(100% - 15px);
  background: linear-gradient(315deg, var(--wf-primary-600) 0%, var(--wf-primary-400) 75.03%);
}

.bg-gradient-fs {
  width: calc(100% - 10px); 
  height: calc(100% - 15px);
  background: linear-gradient(315deg, #3A589B 0%, #A573CA 75.03%);
}

.sw-container {
  width: calc(100% + 6px);
}

  @media (max-width: 768px) {
    .display-4 {
      font-size: 2.2rem !important;
    }
  }