import { Injectable, NgZone } from '@angular/core';
import { Capacitor } from '@capacitor/core';

import {
  ActionPerformed,
  PushNotifications, PushNotificationSchema,
  Token
} from '@capacitor/push-notifications';
import { NavController } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class FcmService {

  constructor(private ngZone: NgZone, private navController: NavController) { }

  initPush() {
    if (Capacitor.platform !== 'web') {
      this.registerPush();
    }
  }

  private registerPush() {
    PushNotifications.requestPermissions().then((permission) => {
      if (permission.receive === 'granted') {
        // Register with Apple / Google to receive push via APNS/FCM
        PushNotifications.register();
      } else {
        // No permission for push granted
      }
    });

    PushNotifications.addListener(
      'registration',
      (token: Token) => {
        localStorage.setItem('fcmToken', token.value);
        console.log('My token: ' + JSON.stringify(token));
      }
    );

    PushNotifications.addListener('registrationError', (error: any) => {
      console.log('Error: ' + JSON.stringify(error));
    });

    PushNotifications.addListener(
      'pushNotificationReceived',
      async (notification: PushNotificationSchema) => {
        console.log('Push received: ' + JSON.stringify(notification));
      }
    );

    PushNotifications.addListener(
      'pushNotificationActionPerformed',
      async (notification: ActionPerformed) => {        
        const data = notification.notification.data;

        if(data && data?.data1){
          switch (data?.data1) {
            case 'COMMANDES':
              this.ngZone.run(() => {
                this.navController.navigateRoot(`/commandes/${data.data2}?readOnly=true`, { replaceUrl: true });
              });
              break;
            case 'OFFRES':
              this.ngZone.run(() => {
                this.navController.navigateRoot(`/offres/labos?offre=${data.data2}`, { replaceUrl: true });
              });
              break;
            default:
              break;
          }
        }

      }
    );
  }
}
