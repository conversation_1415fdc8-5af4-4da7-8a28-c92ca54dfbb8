<!-- Start Of Header -->
<div class="rowline mb-0">
	<div class="page-title-box row">
		<h4 class="page-title fw-4 ps-2 col-6 col-sm-4" *ngIf="!isUnitaire && !isIndividuelle">
			<span class="d-none d-md-inline">Liste des Bons de Livraison</span>
			<span class="d-md-none">List Des BLS</span>
		</h4>
		<h4 class="page-title fw-4 ps-2 col-6 col-sm-4" *ngIf="isUnitaire">Mes Bons de sortie</h4>
    <h4 class="page-title fw-4 ps-2 col-6 col-sm-4" *ngIf="isIndividuelle">Mes Bons de Livraison individuelle</h4>



		<div class="col-6 col-sm-8 px-1" *ngIf="!(isInactive$ | async)">
			<div class="row justify-content-end align-items-center">
				<button (click)="ajouterBonLivraison()" *ngIf="!isUnitaire" type="button"
					class="btn btn-sm btn-primary m-1  d-flex align-items-center justify-content-center k-gap-1"
					style="padding-block: 6px;">
					<i class="bi bi-plus-circle-fill"></i>
					<span class="d-none d-md-inline">Nouveau BL</span>
				</button>
			</div>
		</div>
	</div>
</div>
<!-- END HEADER -->
<div class="row d-flex m-0 px-1">
	<div class="card m-0 w-100 p-0" style="height: calc(100vh - 60px);">
		<div class="card-header py-1 pl-2 bg-white">
			<div class="row p-0">
				<div class="col-12 p-0 d-flex justify-content-end">
					<div class="row p-0 justify-content-center justify-content-sm-end">
						<div class="col-sm p-0 m-1">
							<div class="input-group picker-input">
								<input type="search" placeholder="N° BL" class="form-control form-control-md pl-4"
									id="groupeCritere" [formControl]="filterList" />

								<div class="picker-icons picker-icons-alt">
									<i class="mdi mdi-magnify pointer"></i>
								</div>
							</div>
						</div>


						<button type="button" (click)="displayFilter = !displayFilter"
							class="btn btn-sm search-btn m-1">
							<span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
								<i class="bi bi-sliders"></i>
								<span class="mx-1">Recherche Avancé</span>
							</span>

							<ng-template #closeFilter>
								<span class="d-flex align-items-center">
									<i class="mdi mdi-close"></i>
									<span class="mx-1">Fermer la recherche</span>
								</span>
							</ng-template>

						</button>

						<app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
					</div>
				</div>
			</div>

			<form [formGroup]="BLFilterForm">
				<div id="advanced-search" *ngIf="displayFilter" class="row p-0 flex-wrap px-1 my-2 gap-1"
					style="gap: 8px">
					<div class="col-sm p-0 m-0">
						<label for="numBl" class="col-form-label text-left">N° BL</label>

						<div class="input-group">
							<input type="text" name="raisonSociale"
								class="form-control form-control-md b-radius bg-white" id="numBl"
								formControlName="numBl">
						</div>
					</div>

					<div class="col-sm p-0 m-0">
						<label for="numCommande" class="col-form-label text-left">N° Commande</label>

						<div class="input-group">
							<input type="text" name="raisonSociale"
								class="form-control form-control-md b-radius bg-white" formControlName="numcommande"
								id="numCommande">
						</div>
					</div>

					<div class="col-sm p-0 m-0">
						<label for="dateDebut" class="col-form-label text-left">Date Début</label>
						<div class="input-group picker-input">
							<input type="text" [readOnly]="true" class="form-control form-control-md bg-white"
								id="dateDebut" ngbDatepicker #drange1="ngbDatepicker" (click)="drange1.toggle()"
								formControlName="dateDebut" placeholder="jj/mm/aaaa">

							<div class="picker-icons text-dark"><i (click)="drange1.toggle()" class="mdi mdi-calendar"></i></div>
						</div>
					</div>

					<div class="col-md p-0 m-0">
						<label for="dateFin" class="col-form-label text-left">Date Fin</label>
						<div class="input-group picker-input">
							<input type="text" [readOnly]="true" class="form-control form-control-md bg-white"
								id="dateFin" ngbDatepicker #drange2="ngbDatepicker" (click)="drange2.toggle()"
								formControlName="dateFin" placeholder="jj/mm/aaaa">
							<div class="picker-icons text-dark"><i (click)="drange2.toggle()" class="mdi mdi-calendar"></i></div>
						</div>
					</div>

					<div class="col d-flex align-items-end py-0 mt-2 mt-md-0">
						<button type="button" class="btn btn-sm btn-outline-primary b-radius"
							(click)="clearFilterCommandes()">
							<i class="bi bi-arrow-clockwise"></i>
						</button>

						<button type="submit" class="btn btn-sm btn-primary b-radius mx-1 d-flex flex-shrink-0"
							(click)="filterCommandes()">
							<i class="mdi mdi-filter"></i>
							<span class="mx-1">Appliquer</span>
						</button>
					</div>

				</div>
			</form>
		</div>
		<div class="card-body m-0 px-1 bg-white pt-1 pb-0">
			<kendo-grid [data]="gridData" [pageable]="{
		buttonCount: 5,
		info: true,
		type: 'numeric',
		pageSizes: pageSizes,
		previousNext: true,
		position: 'bottom'
	  }" [pageSize]="navigation.pageSize" class="fs-grid fs-listing-grid" (pageChange)="pageChange($event)"
				(sortChange)="sortChange($event)" [sortable]="{ mode: 'single'}" [sort]="groupeSort" [resizable]="true"
				[skip]="navigation.skip" style="height: 100%" (selectionChange)="onRowClick($event)"
				[selectable]="true">

				<kendo-grid-column field="numeroBl" title="N° BL" [width]="150">
					<ng-template kendoGridHeaderTemplate let-column>
						<app-grid-sort-header [direction]="navigation.sortMethod!"
							[active]="navigation.sortField === column.field" [title]="column.title"
							[type]="'numeric'"></app-grid-sort-header>
					</ng-template>
				</kendo-grid-column>

				<kendo-grid-column field="codeCommande" title="N° Commande" [width]="150">
					<ng-template kendoGridHeaderTemplate let-column>
						<app-grid-sort-header [direction]="navigation.sortMethod!"
							[active]="navigation.sortField === column.field" [title]="column.title"
							[type]="'numeric'"></app-grid-sort-header>
					</ng-template>
				</kendo-grid-column>

				<kendo-grid-column field="dateCommande" title="Date Commande" [width]="160">

					<ng-template kendoGridCellTemplate let-dataItem>
						{{dataItem.dateCommande | date: 'dd/MM/yyyy'}}
					</ng-template>
					<ng-template kendoGridHeaderTemplate let-column>
						<app-grid-sort-header [direction]="navigation.sortMethod!"
							[active]="navigation.sortField === column.field" [title]="column.title"
							[type]="'numeric'"></app-grid-sort-header>
					</ng-template>
				</kendo-grid-column>

				<kendo-grid-column field="dateReceptionBl" title="Date BL" [width]="120">
					<ng-template kendoGridCellTemplate let-dataItem>
						{{dataItem.dateReceptionBl | date: 'dd/MM/yyyy'}}
					</ng-template>
					<ng-template kendoGridHeaderTemplate let-column>
						<app-grid-sort-header [direction]="navigation.sortMethod!"
							[active]="navigation.sortField === column.field" [title]="column.title"
							[type]="'numeric'"></app-grid-sort-header>
					</ng-template>
				</kendo-grid-column>

				<kendo-grid-column field="dateCreation" title="Crée le" [width]="120">
					<ng-template kendoGridCellTemplate let-dataItem>
						{{dataItem.dateCreation | date: 'dd/MM/yyyy'}}
					</ng-template>
					<ng-template kendoGridHeaderTemplate let-column>
						<app-grid-sort-header [direction]="navigation.sortMethod!"
							[active]="navigation.sortField === column.field" [title]="column.title"
							[type]="'numeric'"></app-grid-sort-header>
					</ng-template>
				</kendo-grid-column>

				<kendo-grid-column [field]="isIndividuelle ? 'enteteCommandeIndividuelleDTO.offre.offreur.raisonSociale' : 'enteteCommandeAchatGroupe.offre.offreur.raisonSociale'" title="Offreur"
					[width]="120" [sortable]="false"></kendo-grid-column>


				<kendo-grid-column field="fournisseur.raisonSociale" class="text-wrap" title="Distributeur" [width]="160">
					<ng-template kendoGridHeaderTemplate let-column>
						<app-grid-sort-header [direction]="navigation.sortMethod!"
							[active]="navigation.sortField === column.field" [title]="column.title"
							[type]="'alpha'"></app-grid-sort-header>
					</ng-template>
				</kendo-grid-column>

				<kendo-grid-column field="montantSaisi" [title]="isUnitaire ? 'Montant BS' : 'Montant BL'" [width]="120" class="text-right">
					<ng-template kendoGridCellTemplate let-dataItem>
						{{ dataItem?.montantSaisi | number: '1.2-2'}}
					</ng-template>

					<ng-template kendoGridHeaderTemplate let-column>
						<app-grid-sort-header [direction]="navigation.sortMethod!"
							[active]="navigation.sortField === column.field" [title]="column.title"
							[type]="'numeric'"></app-grid-sort-header>
					</ng-template>
				</kendo-grid-column>


				<kendo-grid-column field="etatBl" title="Statut" [width]="110">
					<ng-template kendoGridCellTemplate let-dataItem>
						<app-element-status
							[state]="(dataItem?.etat === 'cloturee') ? 'C' : dataItem.etatBl === 'ANNULE' ? 'BANNULE' : dataItem.etatBl === 'VALIDE' ? 'BVALIDE' : dataItem.etatBl"></app-element-status>
					</ng-template>
					<ng-template kendoGridHeaderTemplate let-column>
						<app-grid-sort-header [direction]="navigation.sortMethod!"
							[active]="navigation.sortField === column.field" [title]="column.title"
							[type]="'alpha'"></app-grid-sort-header>
					</ng-template>
				</kendo-grid-column>

				<kendo-grid-column title="Action" [hidden]="(isInactive$ | async) === true" [width]="100">
					<ng-template kendoGridCellTemplate let-dataItem>
						<div class="d-flex justify-content-center k-gap-2" *ngIf="dataItem?.etatBl === 'BROUILLON'">
							<span class="actions-icons btn-success pointer-cus" title="Modifier Offre"
								(click)="modifierBonLivraison(dataItem,$event)">
								<i class="bi bi-pencil-square"></i>
							</span>
							<span class="actions-icons btn-danger pointer-cus" title="Annuler BL"
								(click)="isIndividuelle ? AnullerBlIndividuel(dataItem,$event) : AnullerBl(dataItem,$event)">
								<i class="bi bi-trash"></i>
							</span>
						</div>

					</ng-template>

				</kendo-grid-column>

				<kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
					pagerItemsPerPage="éléments par page"></kendo-grid-messages>

				<ng-template kendoGridNoRecordsTemplate>
					<span>Aucun résultat trouvé.</span>
				</ng-template>
				<ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
					let-total="total">
					<wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
						[navigation]="navigation" style="width: 100%;"
						(pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
				</ng-template>
			</kendo-grid>
		</div>
	</div>
</div>


<ng-template #emptyDate>
	<span>--/--/----</span>
</ng-template>
