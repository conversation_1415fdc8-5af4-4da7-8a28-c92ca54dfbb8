<div class="container-fluid bg-white" style="min-height: calc(100vh - 78px);">
  <div class="row" style="min-height: calc(100vh - 78px); overflow: hidden;">
      <!-- Sidebar -->
      <nav class="col-md-3 col-lg-3 d-md-block  sidebar h-100 border-left bg-light"  style="min-height: calc(100vh - 78px); position: sticky; top: 0;">
          <div class="sidebar-sticky">
              <h4 class="mt-3 mb-3 mx-4 text-center">Commande Web Guide</h4>
              <ul class="nav flex-column">
                <li *ngFor="let question of filteredQuestions" class="nav-item">
                  <a class="nav-link k-cursor-pointer" (click)="questionidChanged(question.id)" [ngClass]="{'active': activatedQuestion === question.id}">
                    {{ question.question }}
                  </a>
                </li>
              </ul>
          </div>
      </nav>

      <!-- Main content -->
      <main role="main" class="col-md-9 ml-sm-auto col-lg-9 px-2 py-1 content lacentrale-scroll"  style="height: calc(100vh - 78px);overflow-y: scroll;">
          <ng-container *ngIf="!activatedQuestion">
            <div class="d-flex flex-column align-items-center justify-content-start text-center k-gap-2 h-100 text-dark mt-4">
              <h1 class="mb-2 font-weight-bold">Bienvenue sur le guide de Commande Web</h1>
              <p class="mb-2">
                Cette page vous permet de consulter les questions fréquemment posées sur le site.
              </p>
              <div>

                <i class="mdi mdi-help-circle-outline" style="font-size: 6rem;" [style.color]="'var(--cw-primary-700)'"></i>
              </div>
              <button class="btn rounded-pill btn-lg mt-1 text-white" [style.background]="'var(--cw-primary-700)'" (click)="activatedQuestion = 1">
                Commencer
              </button>
            </div>
          </ng-container>

            <div class="faq-container text-dark" *ngIf="getCurrentQuestion()">
              <!-- <span class="font-18 font-weight-bold"  style="text-decoration: underline;" >{{ getCurrentQuestion()?.category }}</span> -->
              <div *ngFor="let answer of getCurrentQuestion().answers" class="faq-item mt-1">
                <h3 class="mb-4">{{ answer.question }} ?</h3>

                <!-- Text Content -->
                <p *ngIf="answer.textContent">{{ answer.textContent }}</p>

                <!-- Steps -->
                <div class="d-flex k-gap-4  flex-wrap">
                  <ng-container *ngIf="answer.steps && answer.steps.length > 0">
                    <div class="">


                    <h4>Étapes:</h4>
                    <ol>
                      <ng-container *ngFor="let step of answer.steps">
                        <li class="mb-1 font-16">
                          {{ step.text }}
                          <ul *ngIf="step.substeps && step.substeps.length > 0">
                            <li *ngFor="let substep of step.substeps">{{ substep.text }}</li>
                          </ul>
                        </li>
                      </ng-container>
                    </ol>
                   </div>
                  </ng-container>
                  <!-- Images -->
                  <div *ngIf="answer.images && answer.images.length > 0" class="image-container d-flex justify-content-center align-items-center w-100">
                    <ng-container *ngFor="let image of answer.images">
                      <video *ngIf="image.url" class="mx" style="max-height: 600px; width: 100%;" controls
                      [src]="cdnBaseUrl + image.url"
                      ></video>
                     </ng-container>
                  </div>
                
                </div>
              </div>
            </div>

            <ng-container *ngIf="!getCurrentQuestion() && activatedQuestion">
              <h2 class="text-center mt-4">Aucune Content n'a été trouvée.</h2>
            </ng-container>
      </main>
  </div>
</div>

