import { Component, Input, OnInit } from '@angular/core';
import { CustomFunctionsService } from '@wph/data-access';

class ObjectStatut {
  label: string
  value: boolean
}

@Component({
  selector: 'app-element-status',
  templateUrl: './element-status.component.html',
  styleUrls: ['./element-status.component.scss']
})
export class ElementStatusComponent implements OnInit {
  isActive: boolean = null;
  statut: string = null;
  _objectStatut: ObjectStatut;

  @Input() type: string;

  @Input('state')
  set _state(val: any) {
    if (typeof val === 'boolean') {
      this.isActive = val;
    } else {
      let isTrue = this.customServ.isTrue(val);
      if (isTrue != null) {
        this.isActive = isTrue;
      } else {
        this.statut = val;
      }
    }
  }

  @Input("objectStatut")
  set objectStatut(val: ObjectStatut) {
    this._objectStatut = val
  }

  get objectStatut(): ObjectStatut {
    return this._objectStatut
  }

  constructor(private customServ: CustomFunctionsService) { }

  ngOnInit(): void { }

}
