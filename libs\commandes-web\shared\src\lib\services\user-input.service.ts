import {Injectable} from '@angular/core';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import { ConfirmComponent } from '../components/confirm/confirm.component';
import { InputReaderComponent } from '../components/input-reader/input-reader.component';

@Injectable({
    providedIn: 'root'
})
export class UserInputService {


    constructor(private modalService: NgbModal,) {
    }


    getStringInput(title: string, body: string) {
        const modalRef = this.modalService.open(InputReaderComponent);
        if (title) {
            modalRef.componentInstance.title = title;
        }
        if (body) {
            modalRef.componentInstance.body = body;
        }

        return modalRef.result;
    }


    confirm(title: string, body: string, imageUrl?: string) {
        const modalRef = this.modalService.open(ConfirmComponent, { centered: true });
        if (title) {
            modalRef.componentInstance.confirmTitle = title;
        }
        if (body) {
            modalRef.componentInstance.confirmBody = body;
        }
        if (imageUrl) {
            modalRef.componentInstance.confirmImageUrl = imageUrl;
        }
        
        return modalRef.result;
    }

}
