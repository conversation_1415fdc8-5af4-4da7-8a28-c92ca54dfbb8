#list-nouveaux-produits {
  padding: 10px;

  .search {
    margin: 10px 0;
  }

  .header_search {
    display: grid;
    gap: 0.5rem;
    grid-template-columns: auto;
    grid-template-areas: "right";
    // height: 45px;
    justify-content: right;
  }

  // .left {
  //   grid-area: left;
  // }
  .right {
    grid-area: right;

    /*.form-inline {
      // transform: translateY(10px);
    }*/
  }

  //   kendo-grid {
  //     z-index: -1;
  //   }
}

.form-control  {
  color: black;
  font-size: 1rem;
  border-radius: var(--winoffre-base-border-radius);
  font-weight: 600;
}

.modal-footer {
  .btn  {
    color: black;
    font-size: 1rem;
    border-radius: var(--winoffre-base-border-radius);
    font-weight: 600;
  }
}

.input-group {
  .btn {
    border-top-right-radius: var(--winoffre-base-border-radius);
    border-bottom-right-radius: var(--winoffre-base-border-radius);
  }
}

label {
  color: var(--winoffre-text-light-shade);
  font-weight: 600;
}

.picker-input {
  .form-control {
    border-radius: var(--winoffre-base-border-radius) !important;
  }
}
