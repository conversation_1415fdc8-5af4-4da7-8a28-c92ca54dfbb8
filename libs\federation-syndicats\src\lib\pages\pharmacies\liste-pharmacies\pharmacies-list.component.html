<!-- start page title -->
<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-5">Liste des Suggestions</h4>

    <div class="col-7 px-1" *ngIf="!(isInactive$ | async)">
      <div class="row d-flex justify-content-end align-items-center">
        <button *jhiHasAnyAuthority="['ROLE_NATIONAL', 'ROLE_RESPONSABLE']" (click)="suggererPharmacie()"
          class="btn btn-sm btn-primary" title="Ajouter un produit" style="padding-block: 6px;">
          <i class="mdi mdi-assistant"></i>
          Suggérer Pharmacie
        </button>
      </div>
    </div>
  </div>
</div>
<!-- end page title -->

<div class="row d-flex m-0 px-1">
  <div class="card m-0 w-100 p-0" style="height: calc(100vh - 60px);">
    <div class="card-header py-1 pl-2 bg-white">
      <div class="row p-0">
        <div class="col-12 p-0 d-flex justify-content-end">
          <div class="row p-0 justify-content-center justify-content-sm-end">
            <div class="col-sm p-0 m-1">
              <div class="input-group picker-input">
                <input type="text" placeholder="Raison sociale" [formControl]="filterList"
                  class="form-control form-control-md pl-4" id="groupeCritere" />

                <div class="picker-icons picker-icons-alt">
                  <i class="mdi mdi-magnify pointer"></i>
                </div>
              </div>
            </div>


            <button type="button" (click)="displayFilter = !displayFilter" class="btn btn-sm search-btn m-1">
              <span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
                <i class="bi bi-sliders"></i>
                <span class="mx-1">Recherche Avancé</span>
              </span>

              <ng-template #closeFilter>
                <span class="d-flex align-items-center">
                  <i class="mdi mdi-close"></i>
                  <span class="mx-1">Fermer la recherche</span>
                </span>
              </ng-template>

            </button>

            <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
          </div>
        </div>
      </div>
    </div>

    <form [formGroup]="filterForm" (ngSubmit)="appliquerFiltre($event)" wphFocusTrap>
      <div id="advanced-search" *ngIf="displayFilter" class="row p-0 flex-wrap px-1 mx-1 my-2 gap-1">
        <div class="col-auto p-0 m-0">
          <label for="ville" class="col-12 py-0 col-form-label text-left">Ville</label>

          <div class="col-12 input-group">
            <input type="text" name="ville" formControlName="ville"
              class="form-control form-control-md b-radius bg-white" id="ville">
          </div>
        </div>

        <div class="col-auto p-0 m-0">
          <label for="localite" class="col-12 py-0 col-form-label text-left">Localité</label>

          <div class="col-12 input-group">
            <input type="text" name="localite" formControlName="localite"
              class="form-control form-control-md b-radius bg-white" id="localite">
          </div>
        </div>

        <div class="col-auto p-0 my-0 mx-1">
          <label for="createdAt" class="col-12 p-0 col-form-label text-left">Date de Creation</label>

          <app-date-picker formControlName="createdAt"></app-date-picker>
        </div>

        <div class="col-auto p-0 my-0 mx-1">
          <label for="dateTraitement" class="col-12 p-0 col-form-label text-left">Date de Traitement</label>

          <app-date-picker formControlName="dateTraitement"></app-date-picker>
        </div>

        <div class="col-2 p-0 my-0 mx-1">
          <label class="col-12 py-0 col-form-label p-0" for="etatSuggestion" style="margin-bottom: -4px;">Statut</label>
          <div class="input-group">
            <select2 id="etatSuggestion" formControlName="etatSuggestion" [data]="stautsLabelsValues"
              hideSelectedItems="false" class="form-control-sm p-0 w-100" multiple="false">
            </select2>
          </div>
        </div>

        <div class="col-auto d-flex align-items-end pt-1">
          <button type="button" class="btn btn-sm btn-outline-primary b-radius" (click)="vider()">
            <i class="bi bi-arrow-clockwise"></i>
          </button>

          <button type="submit" class="btn btn-sm btn-primary b-radius mx-1">
            <i class="mdi mdi-filter"></i>
          </button>
        </div>
      </div>
    </form>

    <div class="card-body m-0 p-0 bg-white mt-1">
      <kendo-grid class="fs-grid fs-listing-grid" [data]="gridView" (pageChange)="pageChange($event)"
        [pageSize]="navigation.pageSize" [skip]="navigation.skip" [pageable]="{
                  buttonCount: 5,
                  info: true,
                  type: 'numeric',
                  previousNext: true,
                  position: 'bottom'
                }" [sortable]="{ mode: 'single' }" [sort]="pharmacieSort" (sortChange)="pharmacieGridSort($event)"
        style="height: 100%" [selectable]="true" (selectionChange)="onRowClick($event)">
        <kendo-grid-column class="text-wrap" *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" field="userSuggereur"
          title="Suggéré par" [width]="170">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap">Suggéré par</span>
          </ng-template>
          
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ dataItem?.userSuggereur?.lastname }} {{ dataItem?.userSuggereur?.firstname }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="raisonSociale" class="text-wrap" title="" [width]="170">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            PH. {{ dataItem?.raisonSociale }}
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Raison Sociale</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'alpha'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="ville" class="text-wrap" title="" [width]="100" [resizable]="false">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ dataItem?.ville ?? dataItem?.localite }}
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Ville / Localité</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'alpha'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="telephone" title="Téléphone" [width]="90" [resizable]="false">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ dataItem?.telephone }}
          </ng-template>
          <ng-template kendoGridHeaderTemplate let-column>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="createdAt" title="" [width]="110" [resizable]="false">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <span *ngIf="dataItem?.createdAt; else: emptyDate">
              {{ dataItem?.createdAt | date: 'dd/MM/yyyy' }}
            </span>
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Date Soumission</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="dateTraitement" title="" [width]="110" [resizable]="false">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <span *ngIf="dataItem?.dateTraitement; else: emptyDate">
              {{ dataItem?.dateTraitement | date: 'dd/MM/yyyy' }}
            </span>
          </ng-template>

          <ng-template kendoGridHeaderTemplate let-column>
            <span class="text-wrap">Date Traitement</span>
            <app-grid-sort-header [direction]="navigation.sortMethod!" [active]="navigation.sortField === column.field"
              [title]="column.title" [type]="'numeric'"></app-grid-sort-header>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="type" title="Type" [width]="100" [resizable]="false" [sortable]="false">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <app-element-status [type]="dataItem?.typeSuggestion"></app-element-status>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column title="Statut" [width]="100" [resizable]="false" filter="numeric">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <app-element-status [state]="dataItem?.etatSuggestion"></app-element-status>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column title="Action" [width]="70" [hidden]="(isInactive$ | async) === true"
          *jhiHasAnyAuthority="['ROLE_NATIONAL', 'ROLE_RESPONSABLE']">
          <ng-template kendoGridCellTemplate let-dataItem>
            <div class="d-flex justify-content-between flex-row-reverse" style="width: 70px;"
              *ngIf="dataItem?.etatSuggestion === 'T' || dataItem?.etatSuggestion === 'EN_ATTENTE'">
              <span [ngClass]="{ 'opacity-light': dataItem?.statutEntreprise }"
                class="actions-icons btn-danger pointer-cus" title="Annuler" (click)="onAnnulerClick($event, dataItem)">
                <i class="bi bi-x"></i>
              </span>
              <span class="actions-icons btn-success pointer-cus" title="Modifier Suggestion"
                (click)="onModifyClick($event, dataItem)">
                <i class="bi bi-pencil-square"></i>
              </span>
            </div>
          </ng-template>
        </kendo-grid-column>

        <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
          <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
            [navigation]="navigation" style="width: 100%;" (pageChange)="OnPageChange($event)"></wph-grid-custom-pager>
        </ng-template>


        <ng-template kendoGridNoRecordsTemplate>
          <span>Aucun résultat trouvé.</span>
        </ng-template>
      </kendo-grid>
      <ng-template #emptyDate>
        <span>--/--/----</span>
      </ng-template>
    </div>
  </div>

</div>