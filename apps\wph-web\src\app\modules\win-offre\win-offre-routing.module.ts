import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

const routes: Routes = [
    {
        path: '',
        pathMatch: 'full',
        redirectTo: 'accueil'
    },
    {
        path: 'offres',
        loadChildren: () => import('@wph/web/offres/pages')
            .then(m => m.WebOffresPagesModule)
    },
    {
        path: 'commandes',
        loadChildren: () => import('@wph/web/commandes/pages')
            .then(m => m.WebCommandesPagesModule)
    },
    {
        path: 'account',
        loadChildren: () => import('@wph/web/account/pages')
            .then(m => m.WebAccountPagesModule)
    },
    {
        path: 'accueil',
        loadChildren: () => import('@wph/web/accueil/pages')
            .then(m => m.WebAccueilPagesModule)
    },
    {
        path: 'statistiques',
        loadChildren: () => import('@wph/web/statistiques/pages')
            .then(m => m.WebStatistiquesPagesModule)
    },
    {
        path: 'guide',
        loadChildren: () => import('@wph/web/guide-winoffre')
            .then(m => m.WebGuideWinoffreModule)
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class WinOffreRoutingModule { }