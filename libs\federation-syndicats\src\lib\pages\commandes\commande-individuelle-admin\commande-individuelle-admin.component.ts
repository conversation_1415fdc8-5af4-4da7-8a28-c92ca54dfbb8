import { Component, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { CellClickEvent, GridDataResult, PageChangeEvent } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { CommandeCriteria, Offre, OffresService, Pagination } from "@wph/data-access";
import { PlateformeService, SocieteType, UploadFileServiceService } from "@wph/shared";
import { debounceTime, distinctUntilChanged, map, Observable, of, Subject, switchMap, takeUntil } from "rxjs";
import { Router } from "@angular/router";
import { FederationSyndicatService } from "../../../services/federation-syndicats.service";
import { FsOffreCriteria, FsOffreService } from "@wph/federation-syndicats";
import { SelectedPlateforme } from "@wph/web/layout";

@Component({
    selector: 'wph-cmd-individuelle-admin',
    templateUrl: './commande-individuelle-admin.component.html',
    styleUrls: ['./commande-individuelle-admin.component.scss']
})
export class CmdIndividuelleAdminComponent implements OnInit, OnDestroy {
    unsubscribe$: Subject<boolean> = new Subject<boolean>();
    pageSizes: number[] = [5, 10, 15, 20];
    navigation: Pagination = {
        pageSize: 15,
        skip: 0,
        sortMethod: 'desc'
    };
    displayFilter: boolean;
    filterForm: FormGroup;
    gridData: GridDataResult = { total: 0, data: [] };
    commandeSort: SortDescriptor[] = [];
    searchCriteria: CommandeCriteria = new CommandeCriteria();
    offres: Offre[] = [];
    selectedOffre: Offre | null = null;
    searchControl: FormControl = new FormControl('');
    filteredOffres: Offre[] = [];
    stautsLabelsValues: any[] = [
        { label: 'Annulé', value: 'A' },
        { label: 'Brouillon', value: 'B' },
        { label: 'Envoyée', value: 'E' },
        { label: 'En Livraison', value: 'EL' },
        { label: 'Livrée', value: 'L' },
        { label: 'Supprimé', value: 'S' },
    ];

    currentPlateforme: SelectedPlateforme;

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private offreService: OffresService,
        private fsOffresService: FsOffreService,
        private plateformeService: PlateformeService,
        private uploadService: UploadFileServiceService,
        private fedSyndicatService: FederationSyndicatService
    ) {
        this.initFilterForm();
        this.currentPlateforme = this.plateformeService.getCurrentPlateforme();
    }

    ngOnInit(): void {
        this.searchOffres();
        this.listenToSearchFilterChanges();
    }

    listenToSearchFilterChanges(): void {
        this.searchControl.valueChanges.pipe(
            takeUntil(this.unsubscribe$),
            debounceTime(300),
            distinctUntilChanged()
        ).subscribe(value => {
            this.selectedOffre = null;
            this.filteredOffres = this.offres.filter(offre =>
                offre.titre.toLowerCase().includes(value.toLowerCase())
            );
        });
    }

    initFilterForm(): void {
        this.filterForm = this.fb.group({
            offreur: [null],
            distributeur: [null],
            statut: [null],
            dateDebut: [null],
            dateFin: [null],
            client: [null]
        });
    }

    searchOffres(): void {
        const criteria = new FsOffreCriteria({ statut: ['P', 'C'], natureOffre: 'I' });

        const pagination = { pageSize: 100, skip: 0, sortMethod: 'desc' };

        this.fsOffresService.searchFsOffres(pagination, criteria).subscribe((response) => {
            this.offres = (response?.content as Offre[])?.filter(offre => offre?.natureOffre === 'I');
            this.filteredOffres = [...this.offres];
        });
    }

    updateGridData(): void {
        this.gridData = { data: [], total: 0 };
        this.searchCriteria = new CommandeCriteria({ titre: this.selectedOffre?.titre, offreId: this.selectedOffre?.id });
        this.searchCommandeIndividuelle();
    }

    pageChange(event: PageChangeEvent): void {
        this.navigation.skip = event.skip;
        this.navigation.pageSize = event.take;
        if (this.selectedOffre) {
            this.updateGridData();
        }
    }

    sortChange(sort: SortDescriptor[]): void {
        this.commandeSort = sort;
        if (sort.length) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }
        if (this.selectedOffre) {
            this.updateGridData();
        }
    }

    searchCommandeIndividuelle() {
        this.offreService.searchCommandes(this.searchCriteria, this.navigation).subscribe(res => {
            this.gridData = {
                data: res?.content,
                total: res?.totalElements,
            };
        });
    }

    cellClickHandler($event: CellClickEvent) {
        const commande = $event?.dataItem;
        this.consulterCommande(commande?.offre?.id, commande?.id);
    }

    consulterCommande(offreId: number, cmdId: number): void {
        this.router.navigate(
            [`/achats-groupes/commandes/edit/cmd-individuelle`, cmdId],
            { queryParams: { readOnly: true, offreId } }
        );
    }

    toggleOffre(offre: Offre): void {
        this.selectedOffre = this.selectedOffre === offre ? null : offre;
        if (this.selectedOffre) {
            this.updateGridData();
        }
    }

    OnPageChange(event: number): void {
        if (this.selectedOffre) {
            this.updateGridData();
        }
    }

    getImageUrl(offre: Offre): string | null {
        return offre?.docImageOffre?.idhash
            ? this.uploadService.fetchUploadedDocument(offre.docImageOffre.idhash)
            : null;
    }

    filterList(searchQuery: string) {
        return this.offreService.searchSociete({
            raisonSociale: searchQuery,
            typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE],
        });
    }

    filterListePharmacies(searchQuery: string) {
        const navigation: Pagination = { pageSize: 5, skip: 0 };
        return this.fedSyndicatService.searchPharmacieEntreprise(navigation, {
            raisonSociale: searchQuery,
            typeEntreprises: [SocieteType.CLIENT],
        });
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    searchClient = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap((term) => {
                if (term.length > 1) {
                    return this.filterListePharmacies(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map((res) => res?.content.slice(0, 5))
        );

    clientFormatter = (result: { raisonSociale: any }) =>
        result ? `PH. ${result.raisonSociale}` : null;

    fournisseurFormatter = (result: { raisonSociale: any }) =>
        result ? result.raisonSociale : null;

    laboFormatter = (result: { raisonSociale: any }) =>
        result
            ? result.raisonSociale === 'DIVERS'
                ? null
                : result.raisonSociale
            : null;

    appliquerFiltre(): void {
        const payload = this.filterForm?.getRawValue();

        this.searchCriteria = new CommandeCriteria({
            ...this.searchCriteria,
            statut: payload?.statut ? [payload?.statut] : null,
            fournisseur: payload?.distributeur,
            laboratoire: payload?.offreur,
            dateDebutCommande: payload?.dateDebut,
            dateFinCommande: payload?.dateFin,
            client: payload?.client ? { ...payload?.client, segmentEntreprise: payload?.client?.segmentEntreprise[0] } : null
        });

        this.navigation.skip = 0;

        this.searchCommandeIndividuelle();
    }

    viderFiltre(): void {
        this.filterForm.reset();

        this.navigation.skip = 0;
        this.searchCriteria = new CommandeCriteria({
            titre: this.selectedOffre?.titre
        });

        this.searchCommandeIndividuelle();
    }


    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }

}