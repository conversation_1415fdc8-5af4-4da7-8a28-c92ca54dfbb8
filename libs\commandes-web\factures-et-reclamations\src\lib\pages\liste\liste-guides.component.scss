.sidebar .nav-link {
  font-weight: 500;
  color: #333;
  padding: 10px 15px;
  margin-bottom: 5px;
  border-radius: 5px;
  transition: all 0.3s ease;
}
.sidebar .nav-link:hover {
  background-color: #e9ecef;
  color: #007bff;
}
.sidebar .nav-link.active {
  background-color: var(--cw-primary-600);
  color: white;
}

.faq-container {
  padding-bottom: 0 !important;
}

.btn-faq-cm {
  background-color: var(--wf-primary-400);
  color: white;
}


 .lacentrale-scroll::-webkit-scrollbar {
  width: 13.5px;
  height: 13.5px;
  border-radius: 5px;
  background-clip: padding-box;
  background-color: transparent !important;
  margin-right: 0 !important;
  padding-right: 0 !important;
}

 .lacentrale-scroll::-webkit-scrollbar-thumb {
  width: 13.5px;
  height: 13.5px;
  background: var(--cw-primary-700) !important;
  border-radius: 5px;
}
 .lacentrale-scroll::-webkit-scrollbar-track {
  border-radius: 10px;
  width: 10px !important;
  padding-inline: 5px;
}

.btn-close {
  background-color: #EAEDF5;
  color: #2C3E50;
  border-radius: 50%;
  height: 40px;
  width: 40px;
  border: none;
}

.info-fab {
  z-index: 999;
  position: absolute;
  right: 25px;
  bottom: 10px;
  color: #fff;
  padding: 4px 12px;
  border-radius: 10px;
  color: #fff;
  background: #393c9b;
  border: 5px solid #fff;
}

.combo-key {
  font-weight: 600;
  color: #fff;
  background: #393c9b;
  border-radius: 5px;
  padding: 2px 5px;
}
