<!-- Start Of Header -->
<div class="rowline mb-0">
  <div class="page-title-box row">
    <div class="d-flex  align-items-center col-auto k-gap-4">
      <button class="actions-icons action-back btn text-white" (click)="back(false)">
        <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
      </button>
      <h4 class="page-title fw-4 ps-2">
        <span [ngSwitch]="commandeType">
          <ng-container *ngSwitchCase="TYPE_CMD.GROUPE">
            Commande groupe
          </ng-container>

          <ng-container *ngSwitchCase="TYPE_CMD.UNITAIRE">
            <ng-container *ngIf="readOnly">
              <span>Consultation Commande Unitaire</span>
            </ng-container>

            <ng-container *ngIf="!readOnly">
              <span>{{ commandeId ? 'Modifier Commande Unitaire' : 'Saisir Commande Unitaire' }}</span>
            </ng-container>
          </ng-container>

          <ng-container *ngSwitchCase="TYPE_CMD.INDIVIDUELLE">
            <ng-container *ngIf="readOnly">
              <span>Consultation Commande Individuelle</span>
            </ng-container>

            <ng-container *ngIf="!readOnly">
              <span>{{ commandeId ? 'Modifier Commande Individuelle' : 'Saisir Commande Individuelle' }}</span>
            </ng-container>
          </ng-container>


          <ng-container *ngSwitchDefault>
            <span *ngIf="readOnly">Consultation Commande</span>
            <span *ngIf="!readOnly">{{ commandeId ? 'Modifier Commande' : 'Saisir Commande' }}</span>
          </ng-container>
        </span>
      </h4>

      <app-element-status *ngIf="readOnly"
        [state]="selectedOffre?.etatCommandeAchatGroupe || selectedOffre?.commandStatut"></app-element-status>
    </div>

    <div class="col-auto d-lg-flex d-none p-1">
      <div class="row justify-content-end align-items-center">
        <wph-recherche-produit-global [selectedOffre]="selectedOffre"
          #rechercherProduitRef></wph-recherche-produit-global>

        <ng-container *ngIf="commandeType === TYPE_CMD.UNITAIRE">
          <ng-container *ngIf="!(isInactive$ | async)">
            <ng-container *ngIf="!readOnly">
              <button
                *ngIf="commande?.etatCommandeAchatGroupe === 'ACCEPTEE' || commande?.etatCommandeAchatGroupe === 'BROUILLON'"
                [disabled]="!validCommande || isLoading || !selectedOffre?.totalQteCmd"
                (click)="enregistrerEtEnvoyerCommande()" type="button" class="btn btn-sm btn-primary text-white m-1">
                <i class="bi bi-send-fill"></i>
                Envoyer
              </button>

              <button [disabled]="!validCommande || isLoading || !selectedOffre?.totalQteCmd" type="button"
                class="btn btn-sm btn-primary m-1" (click)="enregistrerCommande()">
                <i class="mdi mdi-content-save" *ngIf="!isLoading"></i>
                <span class="spinner-border spinner-border-sm" style="margin-bottom:-2px;" role="status"
                  *ngIf="isLoading">
                </span>
                Enregistrer
              </button>

              <button type="button" class="btn btn-sm btn-warning m-1 text-white"
                (click)="offreForm.reset({resetOption: 'O'}); openModal(resetModal, 'md')">
                <i class="mdi mdi-refresh"></i>
                Réinitialiser
              </button>
            </ng-container>

            <ng-container *ngIf="readOnly">
              <button *ngIf="!cmdConsolideeId" (click)="imprimerCommandeUnitaire()" type="button"
                class="btn btn-sm text-white m-1" title="Imprimer"
                [style.background]="currentPlateforme === 'WIN_GROUPE' ? 'var(--wf-primary-400)' : 'var(--fs-primary-500)'">
                <i class="bi bi-printer-fill"></i>
              </button>

              <button *ngIf="commande?.etatCommandeAchatGroupe === 'BROUILLON'" (click)="annulerCommande()"
                type="button" class="btn btn-sm btn-warning text-white m-1">
                <i class="bi bi-backspace-fill"></i>
                Annuler
              </button>

              <button
                *ngIf="commande?.etatCommandeAchatGroupe !== 'ENVOYEE' &&
                commande?.etatCommandeAchatGroupe !== 'TRANSMIS' && commande?.etatCommandeAchatGroupe !== 'ANNULEE'
                && commande?.etatCommandeAchatGroupe !== 'EN_LIVRAISON' && commande?.etatCommandeAchatGroupe !== 'LIVREE'"
                (click)="envoyerCommandeUnitaire()" type="button" class="btn btn-sm btn-primary text-white m-1">
                <i class="bi bi-send-fill"></i>
                Envoyer
              </button>

              <button (click)="consulterBLS()"
                *ngIf="canConsultBls && (commande?.etatCommandeAchatGroupe === 'EN_LIVRAISON' || commande?.etatCommandeAchatGroupe === 'LIVREE')"
                type="button" class="btn btn-sm btn-success text-white m-1"
                style="background: var(--fs-success); border-color: var(--fs-success);">
                <i class="bi bi-receipt"></i>
                Consulter BLS
              </button>
            </ng-container>
          </ng-container>
        </ng-container>

        <ng-container *ngIf="commandeType === TYPE_CMD.INDIVIDUELLE">
          <ng-container *ngIf="!readOnly">
            <button (click)="enregistrerEtEnvoyerCommandeIndividuelle()" type="button"
              [disabled]="!validCommande || isLoading || !selectedOffre?.totalQteCmd"
              class="btn btn-sm btn-primary text-white m-1 d-flex justify-content-between">
              <i class="bi bi-send-fill mr-lg-1 mr-0"></i>
              <span class="d-none d-lg-block">Envoyer</span>
            </button>

            <button [disabled]="!validCommande || isLoading || !selectedOffre?.totalQteCmd"
              class="btn btn-sm btn-primary m-1 d-flex justify-content-between"
              (click)="enregistrerCommandeIndividuelle()">
              <i class="mdi mdi-content-save mr-lg-1 mr-0" *ngIf="!isLoading"></i>
              <span class="spinner-border spinner-border-sm" style="margin-bottom:-2px;" role="status"
                *ngIf="isLoading">
              </span>
              <span class="d-none d-lg-block">Enregistrer</span>
            </button>

            <button *ngIf="!readOnly" type="button"
              class="btn btn-sm btn-warning m-1 text-white d-flex justify-content-between"
              (click)="offreForm.reset({resetOption: 'O'}); openModal(resetModal, 'md')">
              <i class="mdi mdi-refresh mr-lg-1 mr-0"></i>
              <span class="d-none d-lg-block">Réinitialiser</span>
            </button>
          </ng-container>

          <ng-container *ngIf="readOnly">
            <ng-container *ngIf="commande?.commandStatut === 'BROUILLON'">
              <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_RESPONSABLE', 'ROLE_NATIONAL']">
                <button (click)="supprimerCommandeIndividuelle()" type="button"
                  class="btn btn-sm btn-danger text-white m-1 d-flex justify-content-between">
                  <i class="bi bi-trash mr-lg-1 mr-0"></i>
                  <span class="d-none d-lg-block">Supprimer</span>
                </button>

                <button *ngIf="commande?.commandStatut !== 'ENVOYEE'"
                  (click)="envoyerCommandeGeneric(shouldSkipRequiredFieldsCheck())" type="button"
                  class="btn btn-sm btn-primary text-white m-1 d-flex justify-content-between">
                  <i class="bi bi-send-fill mr-lg-1 mr-0"></i>
                  <span class="d-none d-lg-block">Envoyer</span>
                </button>
              </ng-container>

            </ng-container>

            <button *ngIf="commande?.commandStatut === 'ENVOYEE' || commande?.commandStatut === 'EN_LIVRAISON'"
              (click)="saisirBl()" type="button"
              class="btn btn-sm btn-primary text-white m-1 d-flex justify-content-between">
              <i class="bi bi-plus-circle-fill mr-lg-1 mr-0"></i>
              <span class="d-none d-lg-block">Saisir BL</span>
            </button>

            <button (click)="consulterBLS()"
              *ngIf="commande?.commandStatut === 'EN_LIVRAISON' || commande?.commandStatut === 'LIVREE'" type="button"
              class="btn btn-sm btn-success text-white m-1 d-flex justify-content-between"
              style="background: var(--fs-success); border-color: var(--fs-success);">
              <i class="bi bi-receipt mr-lg-1 mr-0"></i>
              <span class="d-none d-lg-block">Consulter BLS</span>
            </button>

            <button
              *ngIf="commande?.commandStatut === 'ENVOYEE' || commande?.commandStatut === 'EN_LIVRAISON' || commande?.commandStatut === 'LIVREE'"
              (click)="imprimerCommandeIndividuelle()" type="button" class="btn btn-sm text-white m-1" title="Imprimer"
              [style.background]="currentPlateforme === 'WIN_GROUPE' ? 'var(--wf-primary-400)' : 'var(--fs-primary-500)'">
              <i class="bi bi-printer-fill"></i>
            </button>

          </ng-container>
        </ng-container>

        <ng-container *ngIf="commandeType === TYPE_CMD.GROUPE">
          <button *ngIf="canPrintCmdConsolidee()" (click)="imprimerCommande()" type="button"
            class="btn btn-sm text-white m-1" title="Imprimer"
            [style.background]="currentPlateforme === 'WIN_GROUPE' ? 'var(--wf-primary-400)' : 'var(--fs-primary-500)'">
            <i class="bi bi-printer-fill"></i>
          </button>

          <ng-container *ngIf="!(isInactive$ | async)">
            <ng-container *jhiHasAnyAuthority="['ROLE_RESPONSABLE']">
              <button (click)="annulerCommande()" type="button"
                *ngIf="readOnly && (commande?.etatCommandeAchatGroupe === 'ACCEPTEE' || commande?.etatCommandeAchatGroupe === 'FIN_SAISIE')"
                class="btn btn-sm btn-warning text-white m-1 d-flex justify-content-between">
                <i class="bi bi-backspace-fill mr-lg-1 mr-0"></i>
                <span class="d-none d-lg-block">Annuler</span>
              </button>

              <button *ngIf="commande?.etatCommandeAchatGroupe === 'ACCEPTEE'" (click)="rendreFinSaisie()" type="button"
                class="btn btn-sm btn-danger m-1 d-flex justify-content-between">
                <i class="bi bi-shield-lock-fill mr-lg-1 mr-0"></i>
                <span class="d-none d-lg-block">Fin de saisie</span>
              </button>
            </ng-container>


            <ng-container *ngIf="commande?.etatCommandeAchatGroupe === 'FIN_SAISIE'">
              <button *jhiHasAnyAuthority="['ROLE_RESPONSABLE']"
                (click)="cmdsUnitaires?.length ? openModal(reactivationSaisie, 'xl'): reactiverOffre()" type="button"
                class="btn btn-sm text-white m-1 d-flex justify-content-between"
                style="background: var(--fs-success); outline: none">
                <i class="bi bi-unlock-fill mr-lg-1 mr-0"></i>
                <span class="d-none d-lg-block">Réactiver la saisie</span>
              </button>

              <button *jhiHasFeatureFlag="{
                  feature: FEATURE_KEY.SUPPORTEUR_CAN_VALIDER_COMMANDE, 
                  roles: ['ROLE_RESPONSABLE'], 
                  canAccess: checkIsSupporteur(selectedOffre?.supporterEntreprise)
                  }" type="button" class="btn btn-sm btn-primary text-white m-1"
                [disabled]="!validCmdGroupe || !selectedOffre?.totalQteCmd" (click)="validerCommandeConsolidee()">
                <i class="bi bi-send-fill"></i>
                Envoyer
              </button>
            </ng-container>

            <ng-container *ngIf="commande?.etatCommandeAchatGroupe === 'VALIDEE'">
              <button *jhiHasFeatureFlag="{
                  feature: FEATURE_KEY.SUPPORTEUR_CAN_VALIDER_COMMANDE, 
                  roles: ['ROLE_RESPONSABLE'], 
                  canAccess: checkIsSupporteur(selectedOffre?.supporterEntreprise)
                }" type="button" class="btn btn-sm btn-primary text-white m-1"
                [disabled]="!validCmdGroupe || !selectedOffre?.totalQteCmd" (click)="envoyerCommandeGeneric()">
                <i class="bi bi-send-fill"></i>
                Envoyer
              </button>
            </ng-container>

            <ng-container *ngIf="commande?.etatCommandeAchatGroupe === 'ACCEPTEE' && !commandeUnitaireExists">
              <button (click)="saisirCommandeUnitaire()" type="button"
                class="btn btn-sm btn-primary text-white m-1 d-flex justify-content-between">
                <i class="bi bi-plus-circle-fill mr-lg-1 mr-0"></i>
                <span class="d-none d-lg-block">Saisir Commande</span>
              </button>
            </ng-container>
          </ng-container>

          <ng-container
            *ngIf="isSupporteur && (commande?.etatCommandeAchatGroupe === 'ENVOYEE' || commande?.etatCommandeAchatGroupe === 'EN_LIVRAISON' || commande?.etatCommandeAchatGroupe === 'LIVREE') ">

            <ng-container *ngIf="!(isInactive$ | async)">
              <button *ngIf="commande?.etatCommandeAchatGroupe !== 'LIVREE'" (click)="cloturerCommande()" type="button"
                class="btn btn-sm btn-warning text-white m-1 d-flex justify-content-between">
                <i class="bi bi-backspace-fill mr-lg-1 mr-0"></i>
                <span class="d-none d-lg-block">Clotûrer</span>
              </button>

              <button *ngIf="commande?.etatCommandeAchatGroupe !== 'LIVREE'" (click)="saisirBl()" type="button"
                class="btn btn-sm btn-primary text-white m-1 d-flex justify-content-between">
                <i class="bi bi-plus-circle-fill mr-lg-1 mr-0"></i>
                <span class="d-none d-lg-block">Saisir BL</span>
              </button>
            </ng-container>

            <button (click)="consulterBLS()"
              *ngIf="readOnly && (commande?.etatCommandeAchatGroupe === 'EN_LIVRAISON' || commande?.etatCommandeAchatGroupe === 'LIVREE')"
              type="button" class="btn btn-sm btn-success text-white m-1"
              style="background: var(--fs-success); border-color: var(--fs-success);">
              <i class="bi bi-receipt"></i>
              Consulter BLS
            </button>

          </ng-container>

          <ng-container *jhiHasAnyAuthority="['ROLE_RESPONSABLE']">
            <ng-container *ngIf="selectedOffre?.etatCommandeAchatGroupe === 'EN_ATTENTE'">
              <button (click)="refuserOffre()" *ngIf="selectedOffre?.etatCommandeAchatGroupe !== 'REFUSEE'"
                type="button" class="btn btn-sm btn-danger m-1 d-flex justify-content-between">
                <i class="bi bi-backspace-fill mr-lg-1 mr-0"></i>
                <span class="d-none d-lg-block">Réfuser</span>
              </button>

              <button *ngIf="selectedOffre?.etatCommandeAchatGroupe === 'EN_ATTENTE'" (click)="activerLaSaisie()"
                type="button" style="background: var(--fs-success); outline: none"
                class="btn btn-sm text-white m-1 d-flex justify-content-between">
                <i class="bi bi-unlock-fill mr-lg-1 mr-0"></i>
                <span class="d-none d-lg-block">Activer la saisie</span>
              </button>
            </ng-container>

          </ng-container>

        </ng-container>

        <button (click)="back(false)" type="button" class="btn btn-sm btn-dark text-white m-1">
          <i class="mdi mdi-close"></i>
          Quitter
        </button>
      </div>
    </div>
  </div>
</div>
<!-- END HEADER -->

<div class="container-fluid mx-0 p-2">
  <wph-bloc-offre [cmdsUnitaires]="cmdsUnitaires" [managedGroupe]="managedGroupe" [selectedCommande]="commande"
    [readOnly]="readOnly" [offre]="selectedOffre" [reinitNbrCoffret]="reinitNbrCoffretCmd"
    (modePaiementChange)="modePaiementValueChange($event)"></wph-bloc-offre>
  <wph-pdf-viewer [src]="blobUrl" [title]="pdfViewerTitle"></wph-pdf-viewer>
</div>

<ng-template #cmdsBrouillonModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Liste des pharmaciens ayant des commandes unitaires en état brouillon
    </h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss()">
      <i class="mdi mdi-close"></i>
    </button>
  </div>

  <div class="modal-body">
    <kendo-grid [hideHeader]="true" [data]="{data: cmdsUnitairesBrouillon, total: cmdsUnitairesBrouillon?.length}"
      class="fs-grid fs-grid-white">
      <kendo-grid-column>
        <ng-template kendoGridCellTemplate let-dataItem>
          Dr. {{ dataItem?.client?.raisonSociale }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column>
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.client?.nomResponsable }}
        </ng-template>
      </kendo-grid-column>

    </kendo-grid>
  </div>

  <div class="modal-footer">
    <button type="button" (click)="modal.dismiss()" class="btn btn-light">Fermer</button>
  </div>
</ng-template>

<ng-template #resetModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Réinitialiser</h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss()">
      <i class="mdi mdi-close"></i>
    </button>
  </div>

  <form [formGroup]="offreForm">
    <div class="p-2">
      <div class="form-group">
        <div class="row p-1 mb-2">
          <div class="col-12">
            <input id="offre-only-radio" type="radio" [value]="'O'" formControlName="resetOption">
            <label for="offre-only-radio" class="form-label px-2 py-0">Réinitialiser la commande</label>
          </div>
        </div>

        <div class="row p-1 mb-2">
          <div class="col-12">
            <input id="pack-only-radio" type="radio" [value]="'P'" formControlName="resetOption" #packRadio>
            <label for="pack-only-radio" class="form-label px-2 py-0">Réinitialiser un pack</label>
          </div>

          <div class="col-12" *ngIf="packRadio.checked">
            <select2 formControlName="pack" placeholder="Sélectionner un pack" class="form-control-sm" multiple="false"
              [data]="selectPack"></select2>
          </div>
        </div>

      </div>
    </div>

    <div class="modal-footer">
      <button type="button" (click)="modal.dismiss()" class="btn btn-light">Fermer</button>
      <button type="button" (click)="applyOffreReset()"
        [disabled]="resetCtrls['resetOption'].value === 'P' && !resetCtrls['pack'].value"
        class="btn btn-primary ml-1">Appliquer</button>
    </div>

  </form>

</ng-template>

<ng-template #reactivationSaisie let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Réactiver la saisie</h4>
    <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss()">
      <i class="mdi mdi-close"></i>
    </button>
  </div>

  <div [id]="currentPlateforme + '-container'" class="modal-body">
    <div class="row w-100">
      <div class="col-3"></div>

      <div class="col-6 my-0 mx-auto">
        <div class="form-group">
          <div class="input-group picker-input">
            <input type="text" autocomplete="off" [formControl]="searchMember" placeholder="Nom complet"
              class="form-control form-control-md">
            <div class="picker-icons">
              <i class="mdi mdi-magnify pointer"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="col-3 mx-0 px-0 d-flex align-items-center justify-content-end">
        <span class="h5 mx-2">Sélectionner tout:</span>
        <input (click)="selectAll(ckAll.checked)" type="checkbox" style="width: 25px; height: 25px; margin-right: -4px"
          #ckAll>
      </div>
    </div>

    <div class="row p-1 table-container">
      <div class="table-row w-100 selected-member">

        <span class="table-cell">
          <span class="mx-1">Code CMD</span>
        </span>

        <span class="table-cell">
          <span class="mx-1">Nom Responsable</span>
        </span>

        <span class="table-cell">
          <b>Date Création</b>
        </span>

        <span class="table-cell">
          <b>Montant Brût</b>
        </span>

        <span class="table-cell">
          <b>Total Qté CMD</b>
        </span>

        <span class="table-cell-alt">
        </span>

      </div>

      <div class="table-row w-100" *ngFor="let result of filterResult"
        [ngClass]="{'selected-member': selectedMemberIds?.includes(result?.enteteCommandeId)}">

        <span class="table-cell">
          <span class="mx-1">{{ result?.codeCommande }}</span>
        </span>

        <span class="table-cell">
          <span class="mx-1">Dr. {{ result?.client?.nomResponsable }}</span>
        </span>

        <span class="table-cell">
          <b>{{ $any(result)?.dateCreationCmd | date: 'dd/MM/yyyy' }}</b>
        </span>

        <span class="table-cell">
          <b>{{ $any(result)?.totalValeurTtcBruteCmd | number: '1.2-2' }}</b>
        </span>


        <span class="table-cell">
          <b>{{ $any(result)?.totalQteCmd | number: '1.0-0' }}</b>
        </span>

        <span class="table-cell-alt">
          <input (click)="markMemberAsSelected(result?.enteteCommandeId)"
            [checked]="selectedMemberIds?.includes(result?.enteteCommandeId)" type="checkbox"
            style="width: 25px; height: 25px">
        </span>

      </div>
    </div>

    <div class="row w-100 d-flex justify-content-center">
      <button (click)="reactiverLaSaisie()" [disabled]="!selectedMemberIds?.length" type="button"
        class="btn btn-md mx-1 text-white px-4"
        [style.background]="currentPlateforme === 'WIN_GROUPE' ? 'var(--fs-group-grid)' : 'var(--fs-grid-primary)'">Réactiver</button>

      <button (click)="modal.dismiss()" type="button"
        class="btn  px-4 btn-md mx-1 btn-outline-light text-dark">Annuler</button>
    </div>
  </div>
</ng-template>

<ng-template #satisfactionModal let-modal>
  <div class="card">
    <div class="modal-header border-bottom-0 " style="gap: 20px;">
      <h5 class="modal-title d-flex align-items-center" style="color: #110C22; font-weight: 600; font-size: 20px;"
        class="mb-0">
        <img src="../assets/images/decision-making.png" style="height: 40px; width: 40px;" alt="Sondages">

        Sondage de Satisfaction
      </h5>
      <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"><i class="bi bi-x"
          style="font-size: 28px;"></i>
      </button>
    </div>
    <div
      [style.backgroundColor]="currentPlateforme === 'WIN_GROUPE' ? 'var(--wf-primary-400)' : 'var(--fs-primary-500)'"
      class="text-white p-2  d-flex justify-content-between">
      <div class="d-flex align-items-center" style="gap: 20px; ">
        <i class="bi bi-bag-check " style="font-size: 30px;"></i>
        <span style="font-size: 16px; font-weight: 500;"> {{ selectedOffre?.titre }}</span>
      </div>
      <div class="d-flex align-items-center" style="gap: 20px;">
        <i class="bi bi-shop " style="font-size: 30px;"></i>
        <span style="font-size: 20px; font-weight: 600;">
          {{ selectedOffre?.laboratoire?.raisonSociale }}
        </span>
      </div>
    </div>
    <div [id]="currentPlateforme + '-container'" class="py-2  px-4">
      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-basket2-fill " style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Qualité et variété des produits proposés</label>
        </div>
        <ngb-rating [(rate)]="avis.qualite" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-tags-fill" style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Attrait et ampleur des réductions proposées</label>
        </div>
        <ngb-rating [(rate)]="avis.reduction" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-cash-stack" style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Souplesse des conditions de paiement</label>
        </div>
        <ngb-rating [(rate)]="avis.paiement" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-truck" style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Options et rapidité des délais de livraison</label>
        </div>
        <ngb-rating [(rate)]="avis.livraison" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="satisfaction-option">
        <div class="d-flex align-items-center">
          <i class="bi bi-shop" style="margin-right: 10px;"></i>
          <label class="form-label mb-0">Evaluation du laboratoire</label>
        </div>
        <ngb-rating [(rate)]="avis.laboratoire" [max]="5" [readonly]="false">
          <ng-template let-fill="fill">
            <span class="star" [class.filled]="fill === 100">&#9733;</span>
          </ng-template>
        </ngb-rating>
      </div>

      <div class="">
        <label for="feedback" class="form-label">Avez-vous des réflexions que vous aimeriez partager ?</label>
        <textarea class="form-control satisfaction-textarea" id="feedback" rows="3"
          [(ngModel)]="avis.commentaire"></textarea>
      </div>
      <div class="d-flex justify-content-center" style="gap: 10px;">
        <button type="button" class="btn"
          style="color: #FFFFFF; border-width: 2px; border-color: #A9B2CA; color: #717B94; border-radius: 10px;"
          (click)="modal.dismiss()">Annuler</button>
        <button type="button" class="btn"
          [style.backgroundColor]="currentPlateforme === 'WIN_GROUPE' ? 'var(--wf-primary-400)' : 'var(--fs-primary-500)'"
          style="color: #FFFFFF; border-radius: 10px;" (click)="soumettreSatisfactionModal()">Soumettre</button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #refusalModal let-modal>
  <div class="modal-header border-bottom-0 ">
    <h5 class="modal-title d-flex align-items-center" style="gap: 15px;">
      <img src="../assets/images/decision-making.png" style="height: 50px; width: 50px;" alt="Sondages">
      <span style="color: #110C22; font-weight: 600; font-size: 24px;">Sondage d'Intérêt</span>
    </h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"><i class="bi bi-x"
        style="font-size: 28px;"></i>
    </button>
  </div>
  <div [id]="currentPlateforme + '-container'" class="modal-body">
    <p class="mb-4" style="color: #696C75;">Veuillez sélectionner la ou les raisons de votre refus parmi les options
      ci-dessous :</p>
    <form #avisForm="ngForm">
      <div class="mb-3">
        <div class="form-check d-flex align-items-center mb-2 cursor-pointer">
          <input class="form-check-input" type="radio" id="reason1" name="reason" (change)="toggleReason($event)"
            value="Je ne m’intéresse pas.">
          <label class="form-check-label" for="reason1">
            Je ne m’intéresse pas.
          </label>
        </div>
        <div class="form-check d-flex align-items-center mb-2 cursor-pointer">
          <input class="form-check-input" type="radio" id="reason2" name="reason" (change)="toggleReason($event)"
            value="J’ai encore du stock.">
          <label class="form-check-label" for="reason2">
            J’ai encore du stock.
          </label>
        </div>
        <div class="form-check d-flex align-items-center mb-2 cursor-pointer">
          <input class="form-check-input" type="radio" id="reason3" name="reason" (change)="toggleReason($event)"
            value="L'offre ne répond pas à mes besoins/exigences.">
          <label class="form-check-label" for="reason3">
            L'offre ne répond pas à mes besoins/exigences.
          </label>
        </div>
        <div class="form-check d-flex align-items-center mb-2 cursor-pointer">
          <input class="form-check-input" type="radio" id="reason4" name="reason" (change)="toggleReason($event)"
            value="Le labo ne m’intéresse pas.">
          <label class="form-check-label" for="reason4">
            Le labo ne m’intéresse pas.
          </label>
        </div>
        <div class="form-check d-flex align-items-center mb-2 cursor-pointer">
          <input class="form-check-input" type="radio" id="reason5" name="reason" (change)="toggleReason($event)"
            value="Autre.">
          <label class="form-check-label" for="reason5">
            Autre.
          </label>
        </div>
      </div>
      <p class="mb-3" style="color: #696C75;">Si vous avez des commentaires ou suggestions supplémentaires, n'hésitez
        pas à les partager ci-dessous.</p>
      <textarea class="form-control" rows="4" [(ngModel)]="avis.commentaire" name="commentaire"></textarea>
    </form>
  </div>
  <div class="d-flex justify-content-center border-top-0 pb-2" style="gap: 10px;">
    <button type="button" class="btn"
      style="color: #FFFFFF; border-width: 2px; border-color: #A9B2CA; color: #717B94; border-radius: 10px;"
      (click)="modal.dismiss()">Annuler</button>
    <button type="button" class="btn"
      [style.backgroundColor]="currentPlateforme === 'WIN_GROUPE' ? 'var(--wf-primary-400)' : 'var(--fs-primary-500)'"
      style="color: #FFFFFF; border-radius: 10px;" (click)="soumettreRefuserOffre()">Soumettre</button>
  </div>
</ng-template>

<ng-template #envoyerCommandeConsolideeWinGroupe let-modal>
  <div
    class="fs-modal bg-white d-flex align-items-center justify-content-center k-gap-2 flex-column p-2 pt-3 position-relative">
    <div class="cross-button">
      <i class="bi bi-x pointer" (click)="modal.dismiss()"></i>
    </div>

    <div class="d-flex align-items-center justify-content-center k-gap-2">
      <img src="assets/icons/info-warning.png" height="40" width="40" alt="">
      <p class="success-text text-warning m-0">Confirmation</p>
    </div>

    <div class="text-center mt-2">
      <p class="message-text">Merci de sélectionner les contacts fournisseurs auxquels cette commande doit être envoyée.
      </p>

      <div class="row mx-0 w-100">
        <kendo-grid [data]="contactFournisseurGroupeData" class="fs-grid" [resizable]="true" [style.maxHeight]="'200px'"
          [selectable]="{mode: 'single', checkboxOnly: true}" [(selectedKeys)]="selectedContactFournIds"
          kendoGridSelectBy="id">
          <kendo-grid-checkbox-column class="no-ellipsis" headerClass="no-ellipsis" [width]="50"
            [showSelectAll]="false"></kendo-grid-checkbox-column>
          <kendo-grid-column class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Raison Sociale</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
              {{ dataItem?.fournisseur?.raisonSociale }}
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">Délégué</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
              {{ (dataItem?.prenomDelegue + ' ' + dataItem?.nomDelegue) | uppercase }}
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column class="text-wrap">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">E-mail</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
              {{ dataItem?.emailFournisseur }}
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column class="text-wrap" [width]="140">
            <ng-template kendoGridHeaderTemplate>
              <span class="text-wrap">GSM</span>
            </ng-template>

            <ng-template kendoGridCellTemplate let-dataItem>
              {{ dataItem?.gsmFournisseur }}
            </ng-template>
          </kendo-grid-column>

          <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun résultat trouvé.</span>
          </ng-template>

        </kendo-grid>
      </div>

      <p *ngIf="contactFournSubmitted && !selectedContactFournIds?.length" class="text-danger">Veuillez sélectionner au
        moins un contact fournisseur.</p>
    </div>
    <div class="actions d-flex w-100 justify-content-center align-items-center k-gap-2">
      <button class="btn btn-fs-confirm   btn-block" tabindex="-1"
        (click)="validerContactFournisseurSelectionne(modal)">Confirmer</button>
      <button class="btn btn-block btn-fs-cancel m-0" tabindex="-1"
        (click)="modal.dismiss('cancel click')">Annuler</button>
    </div>

  </div>
</ng-template>