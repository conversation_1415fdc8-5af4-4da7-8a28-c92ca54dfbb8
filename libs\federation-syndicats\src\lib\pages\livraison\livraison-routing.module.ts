import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ListeBonsLivraisonComponent } from "./liste/liste-bons-livraison.component";
import { EditBonLivraisonComponent } from "./edit/edit-bon-livraison.component";
import { BLDispatchComponent } from "./dispatch/bl-dispatch.component";
import { CommandeListBlsComponent } from "./commande-list-bls/commande-list-bls.component";

const routes: Routes = [
    {
        path: '',
        pathMatch: 'full',
        redirectTo: 'liste'
    },
    {
        path: 'liste',
        title: 'Liste Bons de Livraison',
        component: ListeBonsLivraisonComponent
    },
    {
        path: 'liste/individuelle',
        title: 'Liste Bons de Livraison individuelle',
        component: ListeBonsLivraisonComponent
    },
    {
        path: 'mes-bons',
        title: 'Mes Bon<PERSON>',
        component: ListeBonsLivraisonComponent
    },
    {
        path: 'edit',
        title: 'Nouveau Bon de Livraison',
        component: EditBonLivraisonComponent
    },
    {
        path: 'edit/:id',
        component: EditBonLivraisonComponent
    },
    //
    {
      path: 'individuelle/:cmd/edit/:id',
      title: 'Bon de Livraison individuel',
      component: EditBonLivraisonComponent
    }
    ,
    {
        path: 'individuelle/:cmd/edit',
        title: 'Nouveau BL individuel',
        component: EditBonLivraisonComponent
    },

    {
      path: 'individuelle/edit',
      title: 'Nouveau BL individuel',
      component: EditBonLivraisonComponent
    },
    {
        path: 'individuelle/edit/:id',
        title: 'Bon de Livraison individuel',
        component: EditBonLivraisonComponent
    },




    //
    {
        path: 'edit/:id/unitaire',
        title: 'Bon de Sortie',
        component: EditBonLivraisonComponent
    },
    {
        path: 'edit/:id/dispatch',
        title: 'Repartition',
        component: BLDispatchComponent
    },
    {
        path: ':cmd/edit/:id',
        title: 'Bon de Livraison',
        component: EditBonLivraisonComponent
    }
    ,
    {
        path: ':cmd/edit',
        title: 'Nouveau Bon de Livraison',
        component: EditBonLivraisonComponent
    },
    {
        path: 'commande/:cmd',
        title: 'Consultation des Bons de Livraison',
        component: CommandeListBlsComponent
    },
    {
        path: 'commande/:cmd/unitaire',
        title: 'Consultation des Bons de Livraison',
        component: CommandeListBlsComponent
    },
    {
      path: 'commande/:cmd/individuelle',
      title: 'Consultation des Bons individuelle',
      component: CommandeListBlsComponent
  },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class LivraisonRoutingModule {}
