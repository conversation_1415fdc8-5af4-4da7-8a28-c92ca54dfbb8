<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/"></ion-back-button>
    </ion-buttons>

    <ion-title>{{'Actualités'}}</ion-title>

    <ion-buttons slot="end">
      <ion-button id="filter-btn" (click)="isFilterModalOpen = true">
        <ion-icon name="filter" size="small"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-refresher slot="fixed" (ionRefresh)="refresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-grid>
    <ion-row class="ion-no-margin ion-no-padding">
      <ion-col sizeMd="2" size="0" class="ion-no-margin ion-no-padding"></ion-col>

      <ion-col size="12" sizeMd="8" class="ion-no-padding ion-no-margin">
        <ion-list class="ion-no-margin ion-padding-top ion-flex-row ion-justify-content-center ion-no-padding"
          lines="none">

          <ion-item-divider
            *ngIf="isFilterApplied && (controls['dateDebutVisibilite']?.dirty || controls['dateFinVisibilite']?.dirty)"
            sticky class="sticky-item-list">
            <ion-item class="selected-date-banner">
              <ion-item class="ion-padding-start">
                <ion-label>Actualités du : </ion-label>
                <ion-badge class="selected-segment-badge-alt" (click)="isFilterModalOpen = true">
                  <span *ngIf="controls['dateDebutVisibilite']?.dirty; else: dateNotFilled"> {{
                    controls['dateDebutVisibilite']?.value | date : 'dd/MM/y' }} </span>
                </ion-badge>
              </ion-item>

              <ion-item>
                <ion-label>au : </ion-label>
                <ion-badge class="selected-segment-badge-alt" (click)="isFilterModalOpen = true">
                  <span *ngIf="controls['dateFinVisibilite']?.dirty; else: dateNotFilled"> {{
                    controls['dateFinVisibilite']?.value | date : 'dd/MM/y' }} </span>
                </ion-badge>
              </ion-item>

              <ng-template #dateNotFilled>
                <span>jj/mm/aaaa</span>
              </ng-template>

            </ion-item>
          </ion-item-divider>

          <!-- Card -->
          <ion-card class="ion-margin-top ion-no-margin ion-no-padding-bottom"
            *ngFor="let item of postes; let i= index; trackBy: trackItems">
              <ion-row style="border-bottom: 0.5px solid #e1e1e1; margin-bottom: 10px">
                <ion-col size="8" class="ion-no-margin">
                  <ion-row class="userInfo">
                    <!--<ion-avatar>
                    <img alt="Default avatar icon" src="https://ionicframework.com/docs/img/demos/avatar.svg" />
                  </ion-avatar> -->
                    <p>
                      <span>{{ item?.createur?.entrepriseDTO?.raisonSociale | titlecase }}</span> <br>
                      <span *ngIf="item?.logoUrl" class="date-txt">
                        {{ item?.dateDebutVisibilite | date: 'YYYY-MM-dd' }}
                      </span>
                    </p>
                  </ion-row>
                </ion-col>

                <ion-col size="4" class="ion-flex ion-align-items-center ion-justify-content-end">
                  <ion-button *ngIf="!item?.logoUrl; else: hasPostLogo" class="date-btn">
                    {{ item?.dateDebutVisibilite | date:'YYYY-MM-dd' }}
                  </ion-button>

                  <ng-template #hasPostLogo>
                    <img [src]="item?.logoUrl" style="width: 100%; height: auto" alt="poste-logo">
                  </ng-template>
                </ion-col>

              </ion-row>

            <ion-card-content class="transparent ion-no-margin ion-no-padding" style="margin-bottom: 10px;">
              <h1 class="ion-text-wrap font-bold padding-top padding-bottom text-color-primary text-size-md"
                style="padding: 5px 10px; text-align: left">
                {{item?.titre}}</h1>
            </ion-card-content>

            <ion-card-content *ngIf="item?.imageUrl" class="transparent ion-no-margin ion-no-padding">
              <img [src]="item?.imageUrl" class="border-radius" style="width: 100%; height: auto;" />

              <ion-row *ngIf="item?.type === 'A' && item?.url && item?.libelleUrl"
                class="ion-no-padding ion-no-margin-top ion-margin-bottom ion-flex ion-justify-content-center">
                <ion-button class="read-more ion-padding" (click)="openLink(item?.url)">
                  {{ item?.libelleUrl }}
                </ion-button>
              </ion-row>
            </ion-card-content>

            <ion-card-content *ngIf="item?.type === 'M'"
              class="transparent ion-no-margin ion-margin-bottom ion-no-padding ion-flex ion-justify-content-center">
              <p class="text-size-p text-color-secondary font-regular"
                style="padding: 5px 15px; text-align:justify; margin-bottom:15px;">
                <span *ngIf="!item?.readMore; else: expanded">
                  {{ item?.sujet | slice: 0:150 }}
                </span>

                <ng-template #expanded>
                  <span>
                    {{ item?.sujet }}
                    <a *ngIf="item?.url" (click)="openLink(item?.url)"><u>Ouvrir le lien</u></a>
                  </span>
                </ng-template>

                <span *ngIf="item?.sujet?.length > 150">
                  <a (click)="readMore(item)" class="text-info pointer-cus">
                    {{ !item?.readMore ? '... (Lire la suite)': '(Montre moins)' }}
                  </a>
                </span>
              </p>
            </ion-card-content>

            <ion-card-content *ngIf="item?.videoUrl" class="ion-no-padding ion-no-margin" #cardContent>
              <youtube-player [showBeforeIframeApiLoads]="false" [disableCookies]="true" [playerVars]="youtubePlayerVars"
                [videoId]="item?.videoUrl"></youtube-player>
            </ion-card-content>
          </ion-card>
        </ion-list>

        <ion-infinite-scroll (ionInfinite)="loadMore($event)" [disabled]="page > totalPages">
          <ion-infinite-scroll-content *ngIf="!firstLoad"></ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-col>
    </ion-row>
  </ion-grid>

  <div class="first-load-container" *ngIf="firstLoad || isLoading">
    <ion-spinner></ion-spinner>
  </div>

  <wph-empty-list *ngIf="!firstLoad && !isLoading && !hasError && !postes?.length"></wph-empty-list>
  <wph-error *ngIf="!firstLoad && hasError"></wph-error>
</ion-content>

<!-- Post filter modal -->
<ion-popover id="post-filter-popup" [keepContentsMounted]="true" alignment="center" trigger="filter-btn"
  [isOpen]="isFilterModalOpen" (didDismiss)="isFilterModalOpen = false" #popover>
  <ng-template>
    <ion-content id="commande-filter-cstm" class="ion-padding">
      <ion-item lines="full">
        <ion-label class="filter-section-title ion-no-padding">Date Actualité</ion-label>
      </ion-item>

      <ion-row class="ion-padding-top ion-padding-bottom">
        <ion-col>
          <ion-row class="ion-flex ion-align-items-center ion-justify-content-start">
            <ion-label class="innner-section-label">Du : </ion-label>
            <ion-datetime-button [class]="controls['dateDebutVisibilite']?.dirty ? 'is-dirty' : ''"
              datetime="dateCreationDebut" color="primary">
              <span *ngIf="!controls['dateDebutVisibilite'].dirty" slot="date-target">jj/mm/aaaa</span>
            </ion-datetime-button>
          </ion-row>
        </ion-col>

        <ion-col>
          <ion-row class="ion-flex ion-align-items-center ion-justify-content-start">
            <ion-label class="innner-section-label">Au : </ion-label>
            <ion-datetime-button [class]="controls['dateFinVisibilite']?.dirty ? 'is-dirty' : ''"
              datetime="dateCreationFin" color="primary">
              <span *ngIf="!controls['dateFinVisibilite'].dirty" slot="date-target">jj/mm/aaaa</span>
            </ion-datetime-button>
          </ion-row>
        </ion-col>
      </ion-row>

      <ion-item lines="full">
        <ion-label class="filter-section-title ion-no-padding">Filtrer par type d'actualité</ion-label>
      </ion-item>

      <ion-row class="ion-padding-top">
        <ion-chip (click)="selectChip('TEXTE'); $event.stopPropagation()" class="chip-default"
          [ngClass]="{'chip-checked': selectedChip === 'TEXTE'}">
          <span>
            <ion-icon name="text-outline" size="small"></ion-icon>
          </span>
          <ion-label>Texte</ion-label>
        </ion-chip>

        <ion-chip (click)="selectChip('IMAGE'); $event.stopPropagation()" class="chip-default"
          [ngClass]="{'chip-checked': selectedChip === 'IMAGE'}">
          <span>
            <ion-icon name="image-outline" size="small"></ion-icon>
          </span>
          <ion-label>Image</ion-label>
        </ion-chip>

        <ion-chip (click)="selectChip('VIDEO'); $event.stopPropagation()" class="chip-default"
          [ngClass]="{'chip-checked': selectedChip === 'VIDEO'}">
          <span>
            <ion-icon name="play-circle-outline" size="small"></ion-icon>
          </span>
          <ion-label>Vidéo</ion-label>
        </ion-chip>

      </ion-row>

      <ion-item class="ion-margin-bottom" lines="full"></ion-item>

      <ion-row class="ion-flex ion-align-items-center ion-justify-content-end">
        <ion-button class="popover-btn" shape="round" (click)="popover.dismiss(); vider()">Vider</ion-button>
        <ion-button class="popover-btn popover-btn-alt" shape="round"
          (click)="popover.dismiss(); applyFilter()">Appliquer</ion-button>
      </ion-row>
    </ion-content>
  </ng-template>
</ion-popover>

<ng-container [formGroup]="filterForm">
  <ion-modal [keepContentsMounted]="true" #dateCreationDebut>
    <ng-template>
      <ion-datetime id="dateCreationDebut" (ionChange)="dateDebutChanged = true" [showDefaultButtons]="true"
        cancelText="Annuler" doneText="Confirmer" locale="fr-FR" presentation="date"
        formControlName="dateDebutVisibilite" [preferWheel]="true"></ion-datetime>
    </ng-template>
  </ion-modal>

  <ion-modal [keepContentsMounted]="true" #dateCreationFin>
    <ng-template>
      <ion-datetime id="dateCreationFin" (ionChange)="dateFinChanged = true" [showDefaultButtons]="true"
        cancelText="Annuler" doneText="Confirmer" locale="fr-FR" formControlName="dateFinVisibilite" presentation="date"
        [preferWheel]="true">
      </ion-datetime>
    </ng-template>
  </ion-modal>
</ng-container>