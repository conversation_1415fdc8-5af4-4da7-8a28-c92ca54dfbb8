<ng-container *ngIf="offre">

  <div class="row mb-2" #offreHeader>
    <div class="container-fluid">
      <div id="pack-tab-nav" class="card border-0 shadow-md bg-transparent w-100">
        <ul ngbNav #blocOffreHeaderNav="ngbNav" [(activeId)]="activeIndex" class="nav-tabs mb-0"
          style="gap: 0 !important;">
          <ng-container *ngIf="cmdType === TYPE_CMD.GROUPE">
            <ng-container *ngIf="offre?.etatCommandeAchatGroupe !== 'EN_ATTENTE'">
              <li [ngbNavItem]="1">
                <a ngbNavLink
                  class="d-flex {{offre?.etatCmdOffre === 'I' ? 'justify-content-between' : 'justify-content-center'}}"
                  [ngClass]="{'active-tab-alt': activeIndex !== 1,  'pack-error': offre?.etatCmdOffre === 'I'}">
                  <div class="col-auto d-flex align-items-center">
                    <i class="mdi mdi-package-variant-closed mdi-18px mr-1"></i>
                    <b class="d-lg-block d-none">{{'Les commandes de groupe' | uppercase}}</b>
                    <b class="d-block d-lg-none">{{'cmds groupe' | uppercase}}</b>
                  </div>

                  <div class="col-auto d-flex align-items-center">
                    <button *ngIf="offre.etatCmdOffre === 'I'" type="button" (click)="fireEvent($event)"
                      popoverClass="increase-popover-width " placement="left"
                      [ngbPopover]="offre.messageEtatCmdOffre ? cstmPopoverTemplate : null" container="body"
                      #ngbPopover="ngbPopover">
                      <i class="bi bi-patch-exclamation-fill" style="font-size: 18px;"></i>

                      <ng-template #cstmPopoverTemplate>
                        <wph-popover-template [popoverRef]="ngbPopover"
                          [popoverContent]="offre.messageEtatCmdOffre"></wph-popover-template>
                      </ng-template>
                    </button>

                  </div>
                </a>

                <ng-template ngbNavContent>
                  <div class="card-body p-1 w-100 mt-0 bg-white"
                    [style.margin-bottom]="fixedPackSyntheseHeight ? (fixedPackSyntheseHeight + 'px') : '105px'"
                    style="height: 100%; min-height: calc(100vh - 310px)">
                    <div class="row d-flex justify-content-between mx-0 mb-0">
                      <div class="col-xl-4 col-md-6 col-12">
                        <div class="form-group d-flex row flex-wrap align-items-center">
                          <label for="listerpar" class="form-label col-auto h4 p-1 m-0">Lister par: </label>

                          <select2 [ngModel]="selectedOption" (ngModelChange)="selectedOptionChange($event)"
                            [data]="cmdGroupeSelectOptions" class="form-control-md col-8 px-0"
                            [hideSelectedItems]="false" [multiple]="false" #groupeSelection></select2>
                        </div>
                      </div>

                      <div class="col-md-6 col-12 d-flex justify-content-start">
                        <ng-container *ngIf="selectedOption === 'F' || selectedOption === 'M'">
                          <div class="col-12">
                            <div class="form-group d-flex flex-wrap row align-items-center justify-content-end">
                              <label for="modePaiement" class="form-label col-auto h4 p-1 m-0">Mode de paiement <span
                                  class="text-danger">*</span></label>

                              <select2 [ngModel]="offre.delaiPaiement" (ngModelChange)="modePaiementValueChange($event)"
                                [data]="modePaiementValuePair" class="form-control-md col-8 px-0"
                                [disabled]="(offre?.etatCommandeAchatGroupe !== 'ACCEPTEE' && offre?.etatCommandeAchatGroupe !== 'FIN_SAISIE') || (isInactive$ | async) === true || isAdminUser"
                                [hideSelectedItems]="false" [multiple]="false"></select2>
                            </div>
                          </div>
                        </ng-container>
                      </div>

                    </div>

                    <div *ngIf="selectedOption !== 'P'" class="row mx-0 w-100 mb-1 d-flex justify-content-end">
                      <ng-container *jhiHasAnyAuthority="['ROLE_RESPONSABLE']">
                        <div *ngIf="canModifyGroupConditions && selectedOption === 'M' && !(isInactive$ | async)"
                          class="col-auto my-1 mx-0 d-flex align-items-end">
                          <button (click)="saveConditions()" class="btn btn-success btn-lg text-white"
                            style="background: var(--fs-success); border-radius: 10px">
                            <i class="bi bi-check"></i>
                            Enregistrer les conditions
                          </button>
                        </div>

                      </ng-container>

                      <ng-container *jhiHasFeatureFlag="{
                          feature: FEATURE_KEY.SUPPORTEUR_CAN_EDIT_COMMANDE, 
                          roles: ['ROLE_RESPONSABLE'], 
                          canAccess: checkIsSupporteur(offre?.supporterEntreprise)
                        }">
                        <div
                          *ngIf="selectedOption === 'M' && offre?.etatCommandeAchatGroupe === 'FIN_SAISIE' && !(isInactive$ | async)"
                          class="col-auto my-1 mx-0 d-flex align-items-end">
                          <button (click)="validerLesChangements()" class="btn btn-success btn-lg text-white"
                            style="background: var(--fs-success); border-radius: 10px">
                            <i class="bi bi-check"></i>
                            Enregistrer les changements
                          </button>
                        </div>
                      </ng-container>

                      <ng-container [ngTemplateOutlet]="badgeBlocConditionsTemplate"
                        [ngTemplateOutletContext]="{bloc: offre, displaySimple: true}">
                      </ng-container>
                    </div>

                    <div class="row mx-0 w-100">
                      <ng-container *ngIf="selectedOption === 'F'" [ngTemplateOutlet]="produitsCommande"></ng-container>

                      <ng-container *ngIf="selectedOption === 'M'"
                        [ngTemplateOutlet]="produitsParMembre"></ng-container>

                      <ng-container *ngIf="selectedOption === 'P'" [ngTemplateOutlet]="packsParMembre"></ng-container>
                    </div>
                  </div>
                </ng-template>

              </li>

              <li class="tabs-separate flex-grow-0"></li>

              <li [ngbNavItem]="2">
                <a ngbNavLink [ngClass]="{'active-tab-alt': activeIndex !== 2}"
                  class="d-lg-flex d-block align-items-center justify-content-center">
                  <i class="mdi mdi-package-variant-closed mdi-18px mr-1"></i>
                  <b class="d-lg-block d-none">{{"Configuration Groupe" | uppercase}}</b>
                  <b class="d-block d-lg-none">{{"Config Groupe" | uppercase}}</b>
                </a>

                <ng-template ngbNavContent>
                  <div class="card-body p-1 w-100 mt-0 bg-white"
                    style="height: calc(100vh - 220px); overflow-y: auto; overflow-x: hidden;">
                    <ng-container [ngTemplateOutlet]="informationsOffreTemp"></ng-container>
                  </div>
                </ng-template>

              </li>
            </ng-container>

            <ng-container *ngIf="offre?.etatCommandeAchatGroupe === 'EN_ATTENTE'">
              <li [ngbNavItem]="1">
                <a ngbNavLink>
                  <i class="mdi mdi-package-variant-closed mdi-18px mr-1"></i>
                  <b>{{"Configuration Groupe" | uppercase}}</b>
                </a>

                <ng-template ngbNavContent>
                  <div class="card-body px-1 py-2 w-100 mt-0 bg-white"
                    style="height: 100%; min-height: calc(100vh - 135px); overflow-y: auto; overflow-x: hidden;">
                    <ng-container [ngTemplateOutlet]="informationsOffreTemp"></ng-container>

                    <ng-container [ngTemplateOutlet]="produitsParMembre"></ng-container>
                  </div>
                </ng-template>

              </li>
            </ng-container>

          </ng-container>

          <ng-container *ngIf="(cmdType === TYPE_CMD.UNITAIRE) || (cmdType === TYPE_CMD.INDIVIDUELLE)">
            <li [ngbNavItem]="1">
              <a ngbNavLink style="gap: 20px" class="d-flex justify-content-center align-items-center"
                [ngClass]="{'active-tab': activeIndex !== 1, 'pack-error': (offre?.etatCmdOffre === 'I' && cmdType === TYPE_CMD.INDIVIDUELLE)}">
                <b>{{offre?.titre | uppercase}}</b>

                <button *ngIf="(offre?.etatCmdOffre === 'I' && cmdType === TYPE_CMD.INDIVIDUELLE)" type="button"
                  (click)="fireEvent($event)" popoverClass="increase-popover-width " placement="left"
                  [ngbPopover]="offre.messageEtatCmdOffre" container="body">
                  <i class="bi bi-patch-exclamation-fill" style="font-size: 18px;"></i>
                </button>
              </a>

              <ng-template ngbNavContent>
                <div class="card-body p-1 w-100 mt-0 bg-white" [style.height]="firstCardHeight" #firstCard>
                  <ng-container [ngTemplateOutlet]="informationsOffreTemp"></ng-container>
                </div>
              </ng-template>

            </li>

          </ng-container>

        </ul>

        <div [ngbNavOutlet]="blocOffreHeaderNav" class="bg-white" style="overflow-y: auto; overflow-x: hidden;"></div>

      </div>

    </div>
  </div>

  <!-- Total des packs par membre template -->
  <ng-template #packsParMembre>
    <div id="packsParMembre" class="p-0 m-0" style="overflow-x: auto;">
      <kendo-grid class="fs-grid fs-grid-white w-100 h-100"
        [data]="{ data: offre?.listeBlocs, total: offre?.listeBlocs?.length }">
        <ng-template kendoGridToolbarTemplate>
          <div class="d-flex row mx-0 w-100 align-items-center justify-content-between grid-top-radius">
            <span class="h4 text-dark">Total commandé des packs par membre</span>
          </div>
        </ng-template>

        <kendo-grid-column field="titre" title="" class="text-wrap" [width]="180">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{ dataItem?.titre | uppercase }}
          </ng-template>

          <ng-container *ngIf="cmdsUnitaires?.length">
            <ng-template kendoGridFooterTemplate>
              <div class="d-flex justify-content-end">
                <span class="text-right text-dark h4">Totaux:</span>
              </div>
            </ng-template>
          </ng-container>
        </kendo-grid-column>

        <kendo-grid-column field="totalQteCmd" footerClass="text-start" class="text-start" [width]="120">
          <ng-template kendoGridHeaderTemplate>
            <div class="d-flex align-items-center justify-content-center w-100">
              <span class="text-wrap">Totale Qté Cmd</span>
            </div>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="d-flex justify-content-center">
              <span class="bg-white px-2 py-1 text-right"
                style="border-radius: 10px; width: 150px; border: 1px solid #ccc">
                {{ dataItem?.totalQteCmd | number: '1.0-0' }}
              </span>
            </div>
          </ng-template>

          <ng-container *ngIf="cmdsUnitaires?.length">
            <ng-template kendoGridFooterTemplate>
              <div class="d-flex justify-content-center">
                <span class="bg-white px-2 py-1 text-right input-shell">
                  {{ (offre?.totalQteCmd ?? 0) | number: '1.0-0' }}
                </span>
              </div>
            </ng-template>
          </ng-container>
        </kendo-grid-column>

        <kendo-grid-column field="nbrPackCmd" footerClass="text-center" title="Nbr Pack Cmd" class="text-center"
          [width]="120" *ngIf="coffretEnabled">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="d-flex justify-content-start">
              <span class="bg-white px-2 py-1 text-right"
                style="border-radius: 10px; width: 150px; border: 1px solid #ccc">
                {{ (dataItem?.totalQteCmd / dataItem?.totalQtePrdFixe) | number: '1.0-0' }}
              </span>
            </div>
          </ng-template>
        </kendo-grid-column>

        <ng-container *ngIf="cmdsUnitaires?.length">
          <kendo-grid-column [width]="50"
            [hidden]="(membersPerPage > membresSupporteurs?.length) || !canGoToPreviousPagePacksParMembre">
            <ng-template kendoGridHeaderTemplate>
              <button *ngIf="canGoToPreviousPagePacksParMembre" (click)="previousPagePacksParMembre()"
                class="btn w-100 mx-1 d-flex align-items-center justify-content-center px-2 py-1"
                title="Voir d'autres membres"
                [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-500)' : 'var(--fs-primary-500)'"
                style="border-radius: 5px">
                <i class="bi bi-chevron-double-left" style="color: white;"></i>
              </button>
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column-group
            *ngFor="let membre of currentMembersPacksParMembre; let membreIndex=index; trackBy: trackMembres"
            headerClass="text-wrap" class="text-center" [width]="280"
            [headerClass]="{'even-row': membreIndex % 2 === 0, 'odd-row': membreIndex % 2 !== 0}">
            <ng-template kendoGridHeaderTemplate>
              <div class="d-flex row w-100 justify-content-center">
                <span class="text-wrap">
                  Dr. {{ membre?.nomResponsable | uppercase }}
                </span>
              </div>
            </ng-template>

            <kendo-grid-column [width]="120" class="text-right"
              [footerClass]="{'fs-montant-total-membre-footer': membreIndex % 2 === 0, 'odd-row': membreIndex % 2 !== 0}"
              [headerClass]="{'even-row': membreIndex % 2 === 0, 'odd-row': membreIndex % 2 !== 0}"
              [class]="membreIndex % 2 === 0 ? 'even-row' : 'odd-row'">
              <ng-template kendoGridHeaderTemplate>
                <div class="d-flex align-items-center justify-content-center w-100">
                  <span class="text-wrap">Qté Cmd</span>
                </div>
              </ng-template>

              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex>
                <div class="d-flex justify-content-center px-1">
                  <span class="bg-white px-2 py-1 text-right input-shell">
                    {{ (dataItem.mapBlocsByMemberId[membre.id]?.totalQteCmd ?? 0) | number: '1.0-0' }}
                  </span>
                </div>
              </ng-template>

              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex>
                <div class="d-flex justify-content-center px-1">
                  <span class="bg-white px-2 py-1 text-right input-shell">
                    {{ (membresParPackAggregate[membre?.id]?.totalQteCmdForMember ?? 0) | number: '1.0-0' }}
                  </span>
                </div>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="120" class="text-right"
              [footerClass]="{'fs-montant-total-membre-footer': membreIndex % 2 === 0, 'odd-row': membreIndex % 2 !== 0}"
              [headerClass]="{'even-row': membreIndex % 2 === 0, 'odd-row': membreIndex % 2 !== 0}"
              [class]="membreIndex % 2 === 0 ? 'even-row' : 'odd-row'">
              <ng-template kendoGridHeaderTemplate>
                <div class="d-flex align-items-center justify-content-center w-100">
                  <span class="text-wrap">Mnt. Brut</span>
                </div>
              </ng-template>

              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex>
                <div class="d-flex justify-content-center px-1">
                  <span class="bg-white px-2 py-1 text-right input-shell">
                    {{ (dataItem.mapBlocsByMemberId[membre?.id]?.totalValeurBruteCmd ?? 0) | number: '1.2-2' }}
                  </span>
                </div>
              </ng-template>

              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex>
                <div class="d-flex justify-content-center px-1">
                  <span class="bg-white px-2 py-1 text-right input-shell">
                    {{ (membresParPackAggregate[membre?.id]?.totalValeurBruteCmdMember ?? 0) | number: '1.2-2' }}
                  </span>
                </div>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [width]="120" class="text-right"
              [footerClass]="{'fs-montant-total-membre-footer': membreIndex % 2 === 0, 'odd-row': membreIndex % 2 !== 0}"
              [headerClass]="{'even-row': membreIndex % 2 === 0, 'odd-row': membreIndex % 2 !== 0}"
              [class]="membreIndex % 2 === 0 ? 'even-row' : 'odd-row'">
              <ng-template kendoGridHeaderTemplate>
                <div class="d-flex align-items-center justify-content-center w-100">
                  <span class="text-wrap">Mnt. Net</span>
                </div>
              </ng-template>

              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex>
                <div class="d-flex justify-content-center px-1">
                  <span class="bg-white px-2 py-1 text-right input-shell">
                    {{ (dataItem.mapBlocsByMemberId[membre?.id]?.totalValeurNetteCmd ?? 0) | number: '1.2-2' }}
                  </span>
                </div>
              </ng-template>

              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex>
                <div class="d-flex justify-content-center px-1">
                  <span class="bg-white px-2 py-1 text-right input-shell">
                    {{ (membresParPackAggregate[membre?.id]?.totalValeurNetteCmdMember ?? 0) | number: '1.2-2' }}
                  </span>
                </div>
              </ng-template>
            </kendo-grid-column>
          </kendo-grid-column-group>

          <kendo-grid-column [width]="50"
            [hidden]="(membersPerPage > membresSupporteurs?.length) || !canGoToNextPagePacksParMembre">
            <ng-template kendoGridHeaderTemplate>
              <button *ngIf="canGoToNextPagePacksParMembre" (click)="nextPagePacksParMembre()"
                title="Voir d'autres membres"
                class="btn w-100 mx-1 d-flex align-items-center justify-content-center px-2 py-1"
                [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-500)' : 'var(--fs-primary-500)'"
                style="border-radius: 5px">
                <i class="bi bi-chevron-double-right" style="color: white; "></i>
              </button>
            </ng-template>
          </kendo-grid-column>
        </ng-container>

      </kendo-grid>
    </div>
  </ng-template>

  <!-- Total des produits par membre template -->
  <ng-template #produitsParMembre>
    <div id="pack-tab-nav-commande-pack" class="card card-border card-bloc-radius bg-transparent w-100">
      <ul ngbNav #produitsParMembreNav="ngbNav" [(activeId)]="activePackIndex"
        (activeIdChange)="cmdGroupeTabChange($event, offre?.listeBlocs[$event])" class="nav-tabs">
        <li [ngbNavItem]="indexBloc" *ngFor="let bloc of offre?.listeBlocs; let indexBloc=index; trackBy: trackFn">
          <a ngbNavLink class="d-flex row align-items-center" style="gap: 8px; min-width: 250px;" [ngClass]="{
            'pack-error': bloc?.etat === 'I', 'pack-success': bloc?.etat === 'V'
          }">
            <div class="col w-100 p-0 m-0 d-flex align-items-center justify-content-between" style="min-width: 150px;">
              <span class="truncate-two-lines"
                [title]="bloc?.titre + (bloc?.blocObligatoire === 'O' ? '(Obligatoire**)' : '')">{{ bloc?.titre }} <b
                  *ngIf="bloc?.blocObligatoire === 'O'">(Obligatoire**)</b></span>
            </div>

            <div class="d-block">
              <button *ngIf="bloc?.etat === 'I' " type="button" (click)="fireEvent($event)"
                popoverClass="increase-popover-width " placement="left"
                [ngbPopover]="bloc.messageEtat ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover">
                <i class="bi bi-patch-exclamation-fill" style="font-size: 20px; vertical-align: middle;"></i>

                <ng-template #cstmPopoverTemplate>
                  <wph-popover-template [popoverRef]="ngbPopover"
                    [popoverContent]="bloc?.messageEtat"></wph-popover-template>
                </ng-template>
              </button>

              <button
                *ngIf="bloc?.etat !== 'I' && (bloc?.totalQteCmd > 0 && hasRatioUg(bloc?.listePaliers) && (bloc?.qteUgSaisie === bloc?.totalQteUg)) || (bloc.etat === 'V' && bloc.totalQteCmd > 0)"
                type="button" (click)="fireEvent($event)">
                <i class="bi bi-patch-check-fill " style="font-size: 20px; vertical-align: middle;"></i>
              </button>
            </div>

            <span *ngIf="(bloc?.listeFils[0]?.typeBloc === 'G' || bloc?.listeFils[0]?.typeBloc === 'R')"
              (click)="(bloc.displayDetailsSousBloc = !bloc.displayDetailsSousBloc); forceExpandOrCollapseAllSousBlocs(bloc, bloc.displayDetailsSousBloc)"
              [title]="(bloc.displayDetailsSousBloc ? 'Cacher' : 'Afficher') + ' les détails des produits'"
              class="collapse-expand-icon mx-1 d-flex align-items-center">
              <i *ngIf="!bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-right"></i>
              <i *ngIf="bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-down"></i>
            </span>
          </a>

          <ng-template ngbNavContent>
            <div class="row d-flex m-0 p-0 w-100 justify-content-between align-items-center flex-wrap"
              style="font-weight: 700;">
              <div class="col-{{bloc?.displayConditionsEtRemisesRow ? '12' : 'auto'}} p-0 m-0">
                <div class="row m-0 p-0">
                  <button (click)="bloc.displayConditionsEtRemisesRow = !bloc.displayConditionsEtRemisesRow"
                    *ngIf="!bloc?.displayConditionsEtRemisesRow && (hasConditions(bloc) || bloc?.listePaliers?.length)"
                    class="btn bg-cstm-info-fs">
                    <span>
                      Voir Remises & Conditions <i class="bi bi-arrows-angle-expand"></i>
                    </span>
                  </button>

                  <div class="row ml-0 mr-1 mt-2 mb-1 px-1 py-0 w-100 h-100 d-flex flex-wrap"
                    *ngIf="(hasConditions(bloc) || bloc?.listePaliers?.length)"
                    [ngClass]="{'hidden-container': !bloc?.displayConditionsEtRemisesRow}"
                    [style.background-color]="(currentPlateforme$ | async)  === 'FEDERATION_SYNDICAT' ? 'var(--fs-grid-light)' : 'var(--wf-primary-100)'"
                    style="border-radius: 8px;">
                    <span (click)="bloc.displayConditionsEtRemisesRow = false" class="remises-row-close"
                      style="top: 5px !important">
                      <i class="mdi mdi-close"></i> Fermer
                    </span>
                    <ng-container [ngTemplateOutlet]="badgeBlocConditionsTemplate"
                      [ngTemplateOutletContext]="{ bloc: bloc }"></ng-container>
                  </div>
                </div>

              </div>

              <ng-template #noConditions>
                <div class="col-auto p-0 m-0 mt-1"></div>
              </ng-template>

              <div *ngIf="canModifyGroupConditions && !(isInactive$ | async)"
                class="col-auto px-0 mx-0  my-1 d-flex align-items-center">
                <button class="btn btn-sm bg-offer bg-obligatoire py-1 px-2  mr-2 d-flex align-items-center pointer-cus"
                  style="border-radius: 10px;">
                  <label for="cb5-{{bloc?.hash}}" class="my-0 ml-0 mr-1">Obligatoire</label>

                  <input type="checkbox" class="m-0" id="cb5-{{bloc?.hash}}" #ckObligatoire
                    (change)="updateValueObligatoire(bloc, ckObligatoire.checked)"
                    [checked]="bloc.conditionCmdUnitaireSpecGroup.blocObligatoire === 'O' ? true : false"
                    style="width: 20px; height: 20px;">
                </button>


                <button (click)="openConditionsModal(groupeConditions, bloc)"
                  class="btn btn-sm bg-offer px-2 py-1 bg-cstm-info-fs"
                  style="border-radius: 10px; font-size: .9rem !important">
                  <span class="mx-1">Conditions</span>
                  <i class="bi bi-arrows-angle-expand"></i>
                </button>
              </div>
            </div>

            <div *ngIf="coffretEnabled && offre?.etatCommandeAchatGroupe !== 'EN_ATTENTE'" id="produitsParMembre"
              class="p-0 mt-0 mb-2">
              <ng-container *ngIf="existeFilsBlocsProduits(bloc)">
                <kendo-grid class="fs-grid fs-grid-white w-100 h-100" (cellClick)="nbrPackGroupeCellClick($event)"
                  (cellClose)="nbrPackGroupeCellClose($event)" [data]="dataNbrPacksParMembre" #nbrPackMembre>
                  <ng-template kendoGridToolbarTemplate>
                    <div class="d-flex row mx-0 w-100 align-items-center justify-content-between grid-top-radius">
                      <span class="h4 text-dark">Nombre de pack coffret</span>
                    </div>
                  </ng-template>

                  <kendo-grid-column field="titre" title="Pack" [width]="300"></kendo-grid-column>

                  <kendo-grid-column [width]="50" class="text-center"
                    [hidden]="(membersPerPage > membresSupporteurs?.length) || !canGoToPreviousPagePacks">
                    <ng-template kendoGridHeaderTemplate>
                      <button *ngIf="canGoToPreviousPagePacks" (click)="previousPagePacks()"
                        title="Voir d'autres membres"
                        class="btn w-100 mx-1 d-flex align-items-center justify-content-center px-2 py-1"
                        [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-500)' : 'var(--fs-primary-500)'"
                        style="border-radius: 5px">
                        <i class="bi bi-chevron-double-left" style="color: white;"></i>
                      </button>
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column [title]="'Dr.' + membre?.nomResponsable" [editable]="true"
                    *ngFor="let membre of currentMembersPacks; let membreIndex=index; trackBy: trackMembres"
                    field="_{{membre?.id}}" [width]="180">
                    <ng-template kendoGridHeaderTemplate>
                      <div class="d-flex text-wrap text-center w-100 align-items-center justify-content-center">
                        <b>Dr. {{ membre?.nomResponsable }}</b>
                      </div>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                      <div class="d-flex justify-content-center">
                        <span class="bg-white px-2 py-1 text-right"
                          style="border-radius: 10px; width: 120px; border: 1px solid #ccc">
                          {{(dataItem.mapBlocsByMemberId[membre.id].totalNbrPackCmd ?? 0) | number:'1.0-0'}}</span>
                      </div>
                    </ng-template>

                    <ng-template kendoGridEditTemplate let-rowIndex="rowIndex" let-dataItem="dataItem"
                      let-column="column" let-formGroup="formGroup">
                      <div class="d-flex justify-content-center" style="width: auto">
                        <input [formControl]="formGroup.get('totalNbrPackCmd')" type="number" wphAllowOnlyNumbers
                          class="form-control d-block text-right mx-1" id="qteCmd" placeholder="Totale Qté Cmd"
                          data-toggle="input-mask" mask="separator.0" thousandSeparator=" " data-reverse="true"
                          (blur)="nbrPackMembre?.closeCell()" autocomplete="off"
                          [readOnly]="offre?.etatCommandeAchatGroupe !== 'FIN_SAISIE'" style="max-width: 120px;">
                      </div>
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column [width]="50" class="text-center"
                    [hidden]="(membersPerPage > membresSupporteurs?.length) || !canGoToNextPagePacks">
                    <ng-template kendoGridHeaderTemplate>
                      <button *ngIf="canGoToNextPagePacks" (click)="nextPagePacks()" title="Voir d'autres membres"
                        class="btn w-100 mx-1 d-flex align-items-center justify-content-center px-2 py-1"
                        [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-500)' : 'var(--fs-primary-500)'"
                        style="border-radius: 5px">
                        <i class="bi bi-chevron-double-right" style="color: white; "></i>
                      </button>
                    </ng-template>
                  </kendo-grid-column>

                </kendo-grid>
              </ng-container>

              <ng-container *ngIf="getListeFilsBlocsNonProduits(bloc)?.length">
                <div class="row mx-auto my-2 w-100"
                  *ngFor="let blocFils of getListeFilsBlocsNonProduits(bloc); let blocFilsIndex=index; trackBy: trackFn">
                  <ng-container [ngTemplateOutlet]="blocGroupeTemplate"
                    [ngTemplateOutletContext]="{bloc: blocFils, blocGroupeIndex: blocFilsIndex}">
                  </ng-container>
                </div>
              </ng-container>

            </div>

            <ng-container *ngIf="existeFilsBlocsProduits(bloc)">
              <ng-container [ngTemplateOutlet]="produitParMembreBlocCmdGroupe" [ngTemplateOutletContext]="{bloc: bloc}">
              </ng-container>
            </ng-container>

            <ng-container *ngIf="getListeFilsBlocsNonProduits(bloc)?.length">
              <div class="row mx-auto my-2 w-100"
                *ngFor="let blocFils of getListeFilsBlocsNonProduits(bloc); let blocFilsIndex=index; trackBy: trackFn">
                <ng-container [ngTemplateOutlet]="blocGroupeTemplate"
                  [ngTemplateOutletContext]="{bloc: blocFils, blocGroupeIndex: blocFilsIndex}">
                </ng-container>
              </div>
            </ng-container>

            <!-- Synthèse de commande groupe -->
            <ng-container *ngIf="cmdType === TYPE_CMD.GROUPE && selectedOption === 'M'">
              <div class="col-12 pt-0 pb-2 px-2 bg-white rounded shadow-lg fixed-synthese"
                [style.border]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? '2px solid var(--wf-primary-500)' : '2px solid var(--fs-primary-500)'"
                [style.left]="fixedSyntheseLeft" [style.width]="fixedSyntheseWidth" #fixedPackSynthese>
                <div (click)="afficherSynthese = !afficherSynthese" class="row d-flex d-lg-none w-100 m-0">
                  <div class="col-12 d-flex justify-content-center align-items-center">
                    <i class="bi bi-chevron-compact-{{afficherSynthese ? 'down' : 'up'}} h3 m-0 icon-bounce"></i>
                  </div>

                  <div class="col-12 m-0 p-0 d-flex justify-content-center align-items-start">
                    <h4 class="m-0 fs-5 text-center">{{ afficherSynthese ? 'Cacher Synthèse Commande' : 'Afficher
                      Synthèse Commande'}}</h4>
                  </div>
                </div>

                <div *ngIf="afficherSynthese" class="row d-flex flex-wrap synthese-transition">
                  <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5 text-center">Qte Cmd Pack</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">
                      {{bloc.totalQteCmd | number : '1.0-0'}}
                    </p>
                  </div>

                  <ng-container>


                    <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                      <h4 class="mb-0 fs-5  text-center">Qte UG Pack</h4>
                      <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{ bloc.totalQteUg > 0 ? (bloc.totalQteUg |
                        number :'1.0-0') : 0 }}</p>
                    </div>
                  </ng-container>

                  <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5 text-center">Montant Brut Pack</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{bloc.totalValeurBruteCmd | number
                      :
                      '1.2-2'}} DH</p>
                  </div>

                  <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5  text-center">Réduction Pack</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold text-center">{{(bloc.totalValeurBruteCmd -
                      bloc.totalValeurNetteCmd) | number : '1.2-2'}} DH</p>
                  </div>

                  <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5 text-center">Montant Net Pack</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{bloc.totalValeurNetteCmd | number
                      :
                      '1.2-2'}} DH</p>
                  </div>
                </div>

              </div>
            </ng-container>

          </ng-template>
        </li>
      </ul>

      <div [ngbNavOutlet]="produitsParMembreNav" class="px-1 pb-1 pt-0 card-border-alt b-radius"></div>

    </div>
  </ng-template>

  <!-- Total des produits commandés template -->
  <ng-template #produitsCommande>
    <div id="pack-tab-nav-commande-pack" class="card card-border card-bloc-radius bg-white w-100">
      <ul ngbNav #produitsCommandeNav="ngbNav" [(activeId)]="activePackIndex"
        (activeIdChange)="cmdGroupeTabChange($event, offre?.listeBlocs[$event])" class="nav-tabs">
        <li [ngbNavItem]="indexBloc" *ngFor="let bloc of offre?.listeBlocs; let indexBloc=index; trackBy: trackFn">
          <a ngbNavLink class="d-flex row align-items-center" [ngClass]="{
            'pack-error': bloc.etat === 'I', 'pack-success': bloc.etat === 'V'
          }" style="gap: 8px; min-width: 250px;">
            <span class="col p-0 m-0 w-100 d-flex align-items-center" style="min-width: 150px;">
              <span class="truncate-two-lines"
                [title]="bloc?.titre + (bloc?.blocObligatoire === 'O' ? '(Obligatoire**)' : '')">{{ bloc?.titre }} <b
                  *ngIf="bloc?.blocObligatoire === 'O'">(Obligatoire**)</b></span>
            </span>

            <div class="d-block">
              <button *ngIf="bloc.etat === 'I' " type="button" (click)="fireEvent($event)"
                popoverClass="increase-popover-width" placement="left"
                [ngbPopover]="bloc.messageEtat ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover">
                <i class="bi bi-patch-exclamation-fill" style="font-size: 20px; vertical-align: middle;"></i>

                <ng-template #cstmPopoverTemplate>
                  <wph-popover-template [popoverRef]="ngbPopover"
                    [popoverContent]="bloc?.messageEtat"></wph-popover-template>
                </ng-template>
              </button>

              <button *ngIf="bloc?.etat === 'V'" type="button" (click)="fireEvent($event)">
                <i class="bi bi-patch-check-fill " style="font-size: 20px; vertical-align: middle;"></i>
              </button>

            </div>

            <span *ngIf="(bloc?.listeFils[0]?.typeBloc === 'G' || bloc?.listeFils[0]?.typeBloc === 'R')"
              (click)="(bloc.displayDetailsSousBloc = !bloc.displayDetailsSousBloc); forceExpandOrCollapseAllSousBlocs(bloc, bloc.displayDetailsSousBloc)"
              [title]="(bloc.displayDetailsSousBloc ? 'Cacher' : 'Afficher') + ' les détails des produits'"
              class="collapse-expand-icon mx-1 d-flex align-items-center">
              <i *ngIf="!bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-right"></i>
              <i *ngIf="bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-down"></i>
            </span>
          </a>

          <ng-template ngbNavContent>
            <div class="row d-flex m-0 p-0 w-100 justify-content-start align-items-center"
              style="font-size: 18px; font-weight: 700;">
              <button (click)="bloc.displayConditionsEtRemisesRow = !bloc.displayConditionsEtRemisesRow"
                *ngIf="!bloc?.displayConditionsEtRemisesRow && (hasConditions(bloc) || bloc?.listePaliers?.length)"
                class="btn bg-cstm-info-fs">
                <span>
                  Voir Remises & Conditions <i class="bi bi-arrows-angle-expand"></i>
                </span>
              </button>

              <div class="row ml-0 mr-1 mt-2 mb-1 px-1 py-0 w-100 h-100 d-flex flex-wrap position-relative"
                *ngIf="(hasConditions(bloc) || bloc?.listePaliers?.length)"
                [ngClass]="{'hidden-container': !bloc?.displayConditionsEtRemisesRow}"
                [style.background-color]="(currentPlateforme$ | async)  === 'FEDERATION_SYNDICAT' ? 'var(--fs-grid-light)' : 'var(--wf-primary-100)'"
                style="border-radius: 8px;">
                <span (click)="bloc.displayConditionsEtRemisesRow = false" class="remises-row-close"
                  style="top: -5px !important; right: 0 !important">
                  <i class="mdi mdi-close"></i> Fermer
                </span>
                <ng-container [ngTemplateOutlet]="badgeBlocConditionsTemplate"
                  [ngTemplateOutletContext]="{ bloc: bloc }"></ng-container>

                <span class="d-flex col-12 align-items-center p-1 m-0">
                  <span class="actions-icons actions-icons-ov text-white pointer-cus bg-cstm-info-fs-icon-only">
                    <i class="bi bi-arrows-angle-expand text-white"></i>
                  </span>
                  <span class="text-dark mx-1">Conditions ou remises appliquées sur la ligne de produit</span>
                </span>
              </div>
            </div>

            <ng-template #noConditions>
              <div class="col-auto p-0 m-0 mt-1"></div>
            </ng-template>

            <ng-container *ngIf="existeFilsBlocsProduits(bloc)">
              <ng-container *ngIf="cmdType === TYPE_CMD.GROUPE" [ngTemplateOutlet]="produitBlocCmdGroupe"
                [ngTemplateOutletContext]="{bloc: bloc}">
              </ng-container>

              <ng-container *ngIf="cmdType === TYPE_CMD.INDIVIDUELLE" [ngTemplateOutlet]="produitBlocCmdIndividuelle"
                [ngTemplateOutletContext]="{bloc: bloc}">
              </ng-container>
            </ng-container>

            <ng-container *ngIf="getListeFilsBlocsNonProduits(bloc)?.length;">
              <div class="row mx-auto my-2 w-100"
                *ngFor="let blocFils of getListeFilsBlocsNonProduits(bloc); let blocFilsIndex=index; trackBy: trackFn">
                <ng-container [ngTemplateOutlet]="blocGroupeTemplate"
                  [ngTemplateOutletContext]="{bloc: blocFils, blocGroupeIndex: blocFilsIndex}">
                </ng-container>
              </div>
            </ng-container>

            <!-- Synthèse de commande unitaire -->
            <ng-container *ngIf="cmdType === TYPE_CMD.INDIVIDUELLE">
              <div class="col-12 pt-0 pb-2 px-2 bg-white rounded shadow-lg fixed-synthese"
                [style.border]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? '2px solid var(--wf-primary-500)' : '2px solid var(--fs-primary-500)'"
                [style.left]="fixedSyntheseLeft" [style.width]="fixedSyntheseWidth" #fixedPackSynthese>
                <div (click)="afficherSynthese = !afficherSynthese" class="row d-flex d-lg-none w-100 m-0">
                  <div class="col-12 d-flex justify-content-center align-items-center">
                    <i class="bi bi-chevron-compact-{{afficherSynthese ? 'down' : 'up'}} h3 m-0 icon-bounce"></i>
                  </div>

                  <div class="col-12 m-0 p-0 d-flex justify-content-center align-items-start">
                    <h4 class="m-0 fs-5 text-center">{{ afficherSynthese ? 'Cacher Synthèse Commande' : 'Afficher
                      Synthèse Commande'}}</h4>
                  </div>
                </div>

                <div *ngIf="afficherSynthese" class="row d-flex flex-wrap synthese-transition">
                  <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5 text-center">Total Qte Cmd</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">
                      {{offre?.totalQteCmd | number : '1.0-0'}}
                    </p>
                  </div>

                  <div *ngIf="offre?.listeBlocs?.length > 1" class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5 text-center">Qte Cmd Pack</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">
                      {{ bloc?.totalQteCmd | number : '1.0-0'}}
                    </p>
                  </div>

                  <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5  text-center">Total Qte UG</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre?.totalQteUg | number :
                      '1.0-0'}}</p>
                  </div>

                  <div *ngIf="offre?.listeBlocs?.length > 1" class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5 text-center">Montant Brut Pack</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{bloc?.totalValeurBruteCmd | number
                      :
                      '1.2-2'}} DH</p>
                  </div>

                  <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5 text-center">Total Montant Brut</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre?.totalValeurBruteCmd | number
                      :
                      '1.2-2'}} DH</p>
                  </div>

                  <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5  text-center">Total Réduction</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold text-center">{{(offre?.totalValeurBruteCmd -
                      offre?.totalValeurNetteCmd) | number : '1.2-2'}} DH</p>
                  </div>

                  <div *ngIf="offre?.listeBlocs?.length > 1" class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5 text-center">Montant Net Pack</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{bloc?.totalValeurNetteCmd | number
                      :
                      '1.2-2'}} DH</p>
                  </div>

                  <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                    <h4 class="mb-0 fs-5 text-center">Total Montant Net</h4>
                    <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre?.totalValeurNetteCmd | number
                      :
                      '1.2-2'}} DH</p>
                  </div>
                </div>

              </div>
            </ng-container>

          </ng-template>


        </li>
      </ul>

      <div [ngbNavOutlet]="produitsCommandeNav" class="px-1 pb-1 pt-0 card-border-alt b-radius"></div>

    </div>
  </ng-template>

  <div *ngIf="cmdType === TYPE_CMD.UNITAIRE" class="row mx-0 p-0 w-100"
    [style.margin-bottom]="fixedPackSyntheseHeight ? (fixedPackSyntheseHeight + 'px') : '105px'">
    <div id="pack-tab-nav-commande-pack"
      class="col-12 mx-0 p-0 card card-bloc-radius card-border bloc-card bg-transparent w-100">
      <ul ngbNav #packNav="ngbNav" [(activeId)]="activePackIndex" class="nav-tabs mb-0">
        <li [ngbNavItem]="ib + 1" *ngFor="let bloc of offre.listeBlocs; let ib=index; trackBy: trackFn">
          <a ngbNavLink [ngClass]="{
              'pack-error': bloc?.etat === 'I', 'pack-success': bloc?.etat === 'V', 'pack-warning': blocIds?.includes(bloc.id)
            }" class="d-flex row align-items-center" style="gap: 8px; min-width: 250px;">
            <div class="col p-0 m-0 w-100 d-flex align-items-center" style="min-width: 150px;">
              <span class="truncate-two-lines"
                [title]="bloc?.titre + (bloc?.conditionCmdUnitaireSpecGroup?.blocObligatoire === 'O' ? '(Obligatoire**)' : '')">
                {{bloc?.titre}} <b
                  *ngIf="bloc?.conditionCmdUnitaireSpecGroup?.blocObligatoire === 'O'">(Obligatoire**)</b>
              </span>
            </div>

            <div class="d-block">
              <button *ngIf="bloc.etat === 'I' " type="button" (click)="fireEvent($event)"
                popoverClass="increase-popover-width " placement="left"
                [ngbPopover]="bloc.messageEtat ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover">
                <i class="bi bi-patch-exclamation-fill" style="font-size: 20px; vertical-align: middle"></i>

                <ng-template #cstmPopoverTemplate>
                  <wph-popover-template [popoverRef]="ngbPopover"
                    [popoverContent]="bloc?.messageEtat"></wph-popover-template>
                </ng-template>
              </button>

              <button *ngIf="bloc?.etat === 'V'" type="button" (click)="fireEvent($event)">
                <i class="bi bi-patch-check-fill " style="font-size: 20px; vertical-align: middle"></i>
              </button>
            </div>

            <span *ngIf="(bloc?.listeFils[0]?.typeBloc === 'G' || bloc?.listeFils[0]?.typeBloc === 'R')"
              (click)="(bloc.displayDetailsSousBloc = !bloc.displayDetailsSousBloc); forceExpandOrCollapseAllSousBlocs(bloc, bloc.displayDetailsSousBloc)"
              [title]="(bloc.displayDetailsSousBloc ? 'Cacher' : 'Afficher') + ' les détails des produits'"
              class="collapse-expand-icon mx-1 d-flex align-items-center">
              <i *ngIf="!bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-right"></i>
              <i *ngIf="bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-down"></i>
            </span>
          </a>

          <ng-template ngbNavContent>
            <ngb-accordion #b="ngbAccordion" activeIds="custom-panel-{{ib}}" class="bg-white">
              <ngb-panel id="custom-panel-{{ib}}"
                cardClass="{{ bloc.etat === 'V' ? 'border border-success' : (bloc.etat === 'I' ? 'border border-danger' : '') }}">
                <ng-template ngbPanelHeader let-opened="opened">
                  <div class="row d-flex mx-0 w-100 m-0 p-0">

                    <button (click)="bloc.displayConditionsEtRemisesRow = !bloc.displayConditionsEtRemisesRow"
                      *ngIf="!bloc?.displayConditionsEtRemisesRow && (hasGroupeConditions(bloc?.conditionCmdUnitaireSpecGroup) || bloc?.listePaliers?.length)"
                      class="btn bg-cstm-info-fs">
                      <span>
                        Voir Remises & Conditions <i class="bi bi-arrows-angle-expand"></i>
                      </span>
                    </button>

                    <div class="row ml-0 mr-1 mt-2 mb-1 px-1 py-0 w-100 h-100 d-flex flex-wrap"
                      *ngIf="(hasGroupeConditions(bloc?.conditionCmdUnitaireSpecGroup) || bloc?.listePaliers?.length)"
                      [ngClass]="{'hidden-container': !bloc?.displayConditionsEtRemisesRow}"
                      [style.background-color]="(currentPlateforme$ | async)  === 'FEDERATION_SYNDICAT' ? 'var(--fs-grid-light)' : 'var(--wf-primary-100)'"
                      style="border-radius: 8px;">
                      <span (click)="bloc.displayConditionsEtRemisesRow = false" class="remises-row-close"
                        style="top: 5px !important">
                        <i class="mdi mdi-close"></i> Fermer
                      </span>
                      <ng-container [ngTemplateOutlet]="badgeBlocConditionsTemplate"
                        [ngTemplateOutletContext]="{ bloc: bloc, isGroupeConditions: true }"></ng-container>

                      <span class="d-flex col-12 align-items-center p-1 m-0">
                        <span class="actions-icons actions-icons-ov text-white pointer-cus bg-cstm-info-fs-icon-only">
                          <i class="bi bi-arrows-angle-expand text-white"></i>
                        </span>
                        <span class="text-dark mx-1">Conditions ou remises appliquées sur la ligne de produit</span>
                      </span>
                    </div>
                  </div>
                </ng-template>

                <ng-template ngbPanelContent>
                  <div id="panelContentRow" class="row">

                    <div id="produits-container" class="col-12 m-0 p-0 bg-white" style="overflow: auto;">
                      <ng-container *ngIf="existeFilsBlocsProduits(bloc)">
                        <wph-bloc-offre [readOnly]="readOnly"
                          [listeBlocsOffreProduits]="getListeFilsBlocsProduits(bloc)"></wph-bloc-offre>
                      </ng-container>

                      <ng-container *ngIf="getListeFilsBlocsNonProduits(bloc)?.length">
                        <div class="row my-2 mx-auto w-100"
                          *ngFor="let blocFils of getListeFilsBlocsNonProduits(bloc); let blocFilsIndex=index; trackBy: trackFn">
                          <ng-container [ngTemplateOutlet]="blocGroupeTemplate"
                            [ngTemplateOutletContext]="{bloc: blocFils, blocGroupeIndex: blocFilsIndex}">
                          </ng-container>
                        </div>
                      </ng-container>
                    </div>

                    <div class="col-12 pt-0 pb-2 px-2 bg-white rounded shadow-lg fixed-synthese"
                      [style.border]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? '2px solid var(--wf-primary-500)' : '2px solid var(--fs-primary-500)'"
                      [style.left]="fixedSyntheseLeft" [style.width]="fixedSyntheseWidth" #fixedPackSynthese>
                      <div (click)="afficherSynthese = !afficherSynthese" class="row d-flex d-lg-none w-100 m-0">
                        <div class="col-12 d-flex justify-content-center align-items-center">
                          <i class="bi bi-chevron-compact-{{afficherSynthese ? 'down' : 'up'}} h3 m-0 icon-bounce"></i>
                        </div>

                        <div class="col-12 m-0 p-0 d-flex justify-content-center align-items-start">
                          <h4 class="m-0 fs-5 text-center">{{ afficherSynthese ? 'Cacher Synthèse Commande' : 'Afficher
                            Synthèse Commande'}}</h4>
                        </div>
                      </div>

                      <div *ngIf="afficherSynthese" class="row d-flex flex-wrap synthese-transition">
                        <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                          <h4 class="mb-0 fs-5 text-center">Total Qte Cmd</h4>
                          <p class="mb-0 fs-5 h4 fw-semibold  text-center">
                            {{offre.totalQteCmd | number : '1.0-0'}}
                          </p>
                        </div>

                        <div *ngIf="offre?.listeBlocs?.length > 1"
                          class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                          <h4 class="mb-0 fs-5 text-center">Qte Cmd Pack</h4>
                          <p class="mb-0 fs-5 h4 fw-semibold  text-center">
                            {{bloc.totalQteCmd | number : '1.0-0'}}
                          </p>
                        </div>

                        <div *ngIf="offre?.listeBlocs?.length > 1"
                          class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                          <h4 class="mb-0 fs-5 text-center">Montant Brut Pack</h4>
                          <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{bloc.totalValeurBruteCmd | number
                            :
                            '1.2-2'}} DH</p>
                        </div>

                        <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                          <h4 class="mb-0 fs-5 text-center">Total Montant Brut</h4>
                          <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre.totalValeurBruteCmd | number
                            :
                            '1.2-2'}} DH</p>
                        </div>

                        <div *ngIf="false" class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
                          <h4 class="mb-0 fs-5 text-center">Total Montant Net</h4>
                          <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{bloc.totalValeurNetteCmd | number
                            :
                            '1.2-2'}} DH</p>
                        </div>
                      </div>

                    </div>

                  </div>
                </ng-template>
              </ngb-panel>


            </ngb-accordion>
          </ng-template>

        </li>
      </ul>

      <div [ngbNavOutlet]="packNav" class="px-1 pb-1 pt-0 bg-white b-radius w-100"></div>

    </div>
  </div>

  <div *ngIf="cmdType === TYPE_CMD.INDIVIDUELLE" class="row mx-0 p-0 w-100"
    [style.margin-bottom]="fixedPackSyntheseHeight ? (fixedPackSyntheseHeight + 'px') : '100px'">
    <ng-container [ngTemplateOutlet]="produitsCommande"></ng-container>
  </div>

  <!-- Synthèse de commande groupe -->
  <ng-container
    *ngIf="((cmdType === TYPE_CMD.GROUPE) && (selectedOption !== 'M') && (offre?.etatCommandeAchatGroupe !== 'EN_ATTENTE')) || activeIndex === 2">
    <div class="col-12 pt-0 pb-2 px-2 bg-white rounded shadow-lg fixed-synthese"
      [style.border]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? '2px solid var(--wf-primary-500)' : '2px solid var(--fs-primary-500)'"
      [style.left]="fixedSyntheseLeft" [style.width]="fixedSyntheseWidth" #fixedPackSynthese>
      <div (click)="afficherSynthese = !afficherSynthese" class="row d-flex d-lg-none w-100 m-0">
        <div class="col-12 d-flex justify-content-center align-items-center">
          <i class="bi bi-chevron-compact-{{afficherSynthese ? 'down' : 'up'}} h3 m-0 icon-bounce"></i>
        </div>

        <div class="col-12 m-0 p-0 d-flex justify-content-center align-items-start">
          <h4 class="m-0 fs-5 text-center">{{ afficherSynthese ? 'Cacher Synthèse Commande' : 'Afficher
            Synthèse Commande'}}</h4>
        </div>
      </div>

      <div *ngIf="afficherSynthese" class="row d-flex flex-wrap synthese-transition">
        <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
          <h4 class="mb-0 fs-5 text-center">Total Qte Cmd</h4>
          <p class="mb-0 fs-5 h4 fw-semibold  text-center">
            {{offre.totalQteCmd | number : '1.0-0'}}
          </p>
        </div>

        <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
          <h4 class="mb-0 fs-5  text-center">Total Qte UG</h4>
          <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre.totalQteUg | number :
            '1.0-0'}}</p>
        </div>

        <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
          <h4 class="mb-0 fs-5 text-center">Total Montant Brut</h4>
          <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre.totalValeurBruteCmd | number
            :
            '1.2-2'}} DH</p>
        </div>

        <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
          <h4 class="mb-0 fs-5  text-center">Total Réduction</h4>
          <p class="mb-0 fs-5 h4 fw-semibold text-center">{{(offre.totalValeurBruteCmd -
            offre.totalValeurNetteCmd) | number : '1.2-2'}} DH</p>
        </div>

        <div class="col bg-cstm-info bg-min-width col-cstm-radius m-1">
          <h4 class="mb-0 fs-5 text-center">Total Montant Net</h4>
          <p class="mb-0 fs-5 h4 fw-semibold  text-center">{{offre.totalValeurNetteCmd | number
            :
            '1.2-2'}} DH</p>
        </div>
      </div>

    </div>
  </ng-container>

  <!-- Informations de l'offre header template -->
  <ng-template #informationsOffreTemp>
    <div class="row py-0" style="height: auto;"
      [style.margin-bottom]="fixedPackSyntheseHeight ? (fixedPackSyntheseHeight + 'px') : '105px'">
      <div class="col-md-3 col-12 mx-auto d-flex justify-content-start align-items-stretch">
        <div class="row w-100 m-0 p-0">
          <div id="img-cnt" class="rounded-2 col-12 m-o p-0 d-flex justify-content-center align-items-start">
            <img *ngIf="!offre?.docImageOffre" class="img-fluid rounded-2" src="assets/images/default-img-alt.png"
              alt="{{ offre.titre }}" [style.max-height]="dynamicImageMaxHeight">

            <img *ngIf="offre?.docImageOffre" class="img-fluid rounded-2"
              (click)="openLargImageModal(largeImgModal, getBlocImage(offre?.docImageOffre?.idhash))"
              [src]="getBlocImage(offre?.docImageOffre?.idhash)" alt="{{ offre.titre }}"
              [style.max-height]="dynamicImageMaxHeight">
          </div>

          <ng-container
            *ngIf="!readOnly || 
            (cmdType === TYPE_CMD.INDIVIDUELLE && (offre?.commandStatut === 'BROUILLON')) ||
            (cmdType === TYPE_CMD.UNITAIRE && (offre?.etatCommandeAchatGroupe === 'BROUILLON')) ||
            (cmdType === TYPE_CMD.GROUPE && (offre?.etatCommandeAchatGroupe === 'ACCEPTEE' || offre?.etatCommandeAchatGroupe === 'FIN_SAISIE'))">
            <div
              *ngIf="(offre?.daysLeft?.days || offre?.daysLeft?.hours || offre?.daysLeft?.minutes || offre?.daysLeft?.seconds)"
              class="col-12 mx-0 p-0">
              <wph-countdown-timer [offre]="offre" [separator]="false" mode="D" gap="5px"
                class="row w-100 mx-0 my-2 px-2 d-flex justify-content-between align-items-center">
              </wph-countdown-timer>
            </div>
          </ng-container>
        </div>
      </div>

      <div class="col mx-1  d-flex flex-column">
        <div class="row px-1 py-0">

          <div *ngIf="cmdType === TYPE_CMD.GROUPE" class="col-12 col-md-8 my-0">
            <div class="form-group m-0">
              <label for="offreurLabel" class="form-label fs-5 h5 text-sub">Titre de l'offre
              </label>

              <div class="input-group">
                <input id="offreurLabel" [readonly]="true" type="text" class="form-control form-control-md"
                  name="fournisseur" [value]="offre?.titre">
              </div>
            </div>
          </div>

          <div class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="offreurLabel" class="form-label fs-5 h5 text-sub">Offreur
              </label>

              <div class="input-group">
                <input id="offreurLabel" [readonly]="true" type="text" class="form-control form-control-md"
                  name="fournisseur" [value]="offre?.offreur?.raisonSociale" [title]="offre?.offreur?.raisonSociale">
              </div>
            </div>
          </div>

          <div *ngIf="cmdType === TYPE_CMD.GROUPE || cmdType === TYPE_CMD.UNITAIRE" class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="distributeur" class="form-label fs-5 h5 text-sub">Distributeur</label>

              <div class="input-group">
                <input id="distributeur" [readonly]="true" type="text" class="form-control form-control-md"
                  name="fournisseur" [value]="offre?.distributeur?.raisonSociale"
                  [title]="offre?.distributeur?.raisonSociale">
              </div>
            </div>
          </div>

          <div *ngIf="cmdType === TYPE_CMD.INDIVIDUELLE" class="col-md-4 col-12 my-0">
            <div class="form-group">
              <label for="distributeurs" class="form-label fs-5 h5 text-sub">Distributeur <span *ngIf="!readOnly"
                  class="text-danger">*</span></label>
              <select2 *ngIf="!readOnly" id="distributeurs" [readonly]="readOnly" [data]="distributeursValuePair"
                [(ngModel)]="offre.distributeur" class="form-control-md" placeholder="Sélectionner distributeur">
              </select2>

              <div class="input-group" *ngIf="readOnly">
                <input id="distributeurs" [readonly]="true" type="text" class="form-control form-control-md"
                  name="fournisseur" [value]="offre?.distributeur?.raisonSociale"
                  [title]="offre?.distributeur?.raisonSociale">
              </div>
            </div>
          </div>


          <div class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="laboratoire" class="form-label fs-5 h5 text-sub">Laboratoire
              </label>

              <div class="input-group">
                <input [readonly]="true" type="text" class="form-control form-control-md" name="laboratoire"
                  [value]="offre.laboratoire?.raisonSociale" [title]="offre.laboratoire?.raisonSociale">
              </div>
            </div>
          </div>

          <div class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="dateDebut" class="form-label fs-5 h5 text-sub">Date Début
              </label>

              <div class="input-group picker-input">
                <input [readonly]="true" type="text" class="form-control form-control-md" id="dateDebut"
                  [value]="$any(offre).dateDebut | date: 'dd/MM/yyyy'" autocomplete="off">

                <div class="picker-icons">
                  <i class="mdi mdi-calendar-month pointer"></i>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="dateFin" class="form-label fs-5 h5 text-sub">Date Fin</label>

              <div class="input-group picker-input">
                <input [readonly]="true" type="text" class="form-control form-control-md" id="dateFin"
                  [value]="$any(offre).dateFin | date : 'dd/MM/yyyy'" autocomplete="off">
                <div class="picker-icons">
                  <i class="mdi mdi-calendar-month pointer"></i>
                </div>
              </div>
            </div>
          </div>

          <div *ngIf="offre?.delaiLivraison" class="col-md-4 col-12 mt-0 mb-1">
            <div class="form-group m-0">
              <label for="dateLivraison" class="form-label fs-5 h5 text-sub">Délai de Livraison (jours)</label>

              <div class="input-group picker-input">
                <input [readonly]="true" type="text" class=" form-control form-control-md" id="dateLivraison"
                  style="width: auto;" [value]="$any(offre).delaiLivraison" autocomplete="off">
              </div>
            </div>
          </div>

          <div
            *ngIf="cmdType === TYPE_CMD.UNITAIRE || (cmdType === TYPE_CMD.GROUPE && (offre?.etatCommandeAchatGroupe !== 'ACCEPTEE' && offre?.etatCommandeAchatGroupe !== 'FIN_SAISIE'))"
            class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="supporteur" class="form-label fs-5 h5 text-sub">Supporteur
              </label>

              <div class="input-group">
                <input id="supporteur" [readonly]="true" type="text" class="form-control form-control-md"
                  name="supporteur"
                  [title]="offre?.supporterEntreprise ? ('Dr. ' + (offre?.supporterEntreprise?.nomResponsable || '') + ' | ' + (offre?.supporterEntreprise?.raisonSociale || '')) : ''"
                  [value]="offre?.supporterEntreprise ? ('Dr. ' + (offre?.supporterEntreprise?.nomResponsable || '') + ' | ' + (offre?.supporterEntreprise?.raisonSociale || '')) : ''">
              </div>
            </div>
          </div>

          <div *ngIf="offre?.delaiPaiement && (cmdType === TYPE_CMD.GROUPE || cmdType === TYPE_CMD.UNITAIRE)"
            class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="modePaiement" class="form-label fs-5 h5 text-sub">Mode de paiement</label>

              <input id="modePaiement" [readonly]="true" type="text" class="form-control form-control-md"
                name="modePaiement" [value]="offre?.delaiPaiement && offre?.delaiPaiement?.label">

            </div>
          </div>

          <ng-container *ngIf="cmdType === TYPE_CMD.INDIVIDUELLE">
            <div *ngIf="offre?.listDelaiPaiements?.length && !readOnly" class="col-md-4 col-12 my-0">
              <div class="form-group m-0">
                <label for="modePaiement" class="form-label fs-5 h5 text-sub">Mode de paiement <span
                    class="text-danger">*</span></label>

                <select2 [(ngModel)]="offre.delaiPaiement" (ngModelChange)="fireModePaiementValueChange()"
                  [data]="modePaiementValuePair" class="form-control-md px-0" [readonly]="readOnly" listPosition="above"
                  [hideSelectedItems]="false" [multiple]="false">
                </select2>
              </div>
            </div>

            <div *ngIf="readOnly" class="col-md-4 col-12 my-0">
              <div class="form-group m-0">
                <label for="modePaiement" class="form-label fs-5 h5 text-sub">Mode de paiement</label>

                <input id="modePaiement" [readonly]="true" type="text" class="form-control form-control-md"
                  name="modePaiement" [value]="offre?.delaiPaiement ? offre?.delaiPaiement?.label : 'AUCUN'">
              </div>
            </div>
          </ng-container>

          <div *ngIf="!readOnly && offre.raisonSocialeTransporteur && cmdType === TYPE_CMD.INDIVIDUELLE"
            class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="raisonSocialeTransporteur" class="form-label fs-5 h5 text-sub">Transporteur <span
                  class="text-danger">*</span></label>

              <select2 [(ngModel)]="offre.transporteurCommande" [data]="transporteurValuePair"
                class="form-control-md px-0" [readonly]="readOnly" listPosition="above" [hideSelectedItems]="false"
                [multiple]="false">
              </select2>
            </div>
          </div>

          <div
            *ngIf="transporteurValuePair?.length > 1 && cmdType === TYPE_CMD.GROUPE && (offre?.etatCommandeAchatGroupe === 'ACCEPTEE' || offre?.etatCommandeAchatGroupe === 'FIN_SAISIE')"
            class="col-md-4 col-12">
            <div class="form-group m-0">
              <label for="raisonSocialeTransporteur" class="form-label fs-5 h5 text-sub">Transporteur <span
                  class="text-danger">*</span></label>

              <select2 [(ngModel)]="offre.transporteurCommande" [data]="transporteurValuePair"
                placeholder="Sélectionner transporteur"
                [disabled]="(offre?.etatCommandeAchatGroupe !== 'ACCEPTEE' && offre?.etatCommandeAchatGroupe !== 'FIN_SAISIE') || (isInactive$ | async) === true"
                class="form-control-md px-0" [readonly]="readOnly" listPosition="below" [hideSelectedItems]="false"
                [multiple]="false">
              </select2>
            </div>
          </div>

          <div *ngIf="offre.raisonSocialeTransporteur && ((
              (cmdType === TYPE_CMD.GROUPE || cmdType === TYPE_CMD.UNITAIRE) && 
              (offre?.etatCommandeAchatGroupe !== 'ACCEPTEE' && offre?.etatCommandeAchatGroupe !== 'FIN_SAISIE' && offre?.etatCommandeAchatGroupe !== 'EN_ATTENTE')
            ) || (readOnly && cmdType === TYPE_CMD.INDIVIDUELLE))" class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="raisonSocialeTransporteur" class="form-label fs-5 h5 text-sub">Transporteur</label>

              <input id="raisonSocialeTransporteur" [readonly]="true" type="text" class="form-control form-control-md"
                name="raisonSocialeTransporteur" [value]="offre?.transporteurCommande || 'AUCUN'">
            </div>
          </div>

          <div
            *ngIf="offre?.raisonSocialeTransporteur && offre?.etatCommandeAchatGroupe === 'EN_ATTENTE' && cmdType === TYPE_CMD.GROUPE"
            class="col-md-4 col-12 my-0">
            <div class="form-group m-0">
              <label for="raisonSocialeTransporteur" class="form-label fs-5 h5 text-sub">Transporteur</label>

              <input id="raisonSocialeTransporteur" [readonly]="true" type="text" class="form-control form-control-md"
                name="raisonSocialeTransporteur" [value]="offre?.raisonSocialeTransporteur">
            </div>
          </div>

          <ng-container *ngIf="cmdType === TYPE_CMD.GROUPE || cmdType === TYPE_CMD.INDIVIDUELLE">
            <div class="col-md-4 col-12 mt-0 mb-1 py-1">
              <div class="form-group m-0 d-flex align-items-center">
                <input id="utiliserValeurHt" type="checkbox" [disabled]="true"
                  [checked]="offre.utiliserValeurHt === 'O'" style="width: 20px; height: 20px">
                <label for="utiliserValeurHt" class="col-form-label fs-5 text-dark ml-1"><b>Utiliser Valeur
                    HT</b></label>
              </div>

              <div class="form-group m-0 d-flex align-items-center">
                <input id="palierTestValeurBrut" type="checkbox" [disabled]="true"
                  [checked]="offre.palierTestValeurBrut === 'O'" style="width: 20px; height: 20px">
                <label for="palierTestValeurBrut" class="col-form-label fs-5 text-dark ml-1"><b>Condition sur
                    Brut</b></label>
              </div>
            </div>
          </ng-container>

          <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
            <div *ngIf="offre?.dynamicScriptCondition" class="col-auto d-flex align-items-end pr-0 mx-0">
              <wph-conditions-dynamiques [readOnly]="true"
                [ngModel]="offre.dynamicScriptCondition"></wph-conditions-dynamiques>
            </div>
          </ng-container>

          <ng-container *ngIf="cmdType === TYPE_CMD.INDIVIDUELLE">
            <div *ngIf="hasOffreConditions(offre)"
              class="col-auto my-1 mx-1 rounded-lg d-flex justify-content-start p-1"
              [style.background-color]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-grid-light)'">
              <div class="col-auto px-2">
                <p class="text-start mb-0 h5 text-dark">Conditions de l'offre</p>
                <div class="d-flex gap-2 flex-wrap px-1 m-0 w-100">
                  <div class="conditions-row">
                    <button *ngIf="offre?.qteMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                      <span>Qté Min</span>
                      &nbsp;

                      <i class="mdi mdi-arrow-right mdi-16px"></i>
                      &nbsp;

                      <span>{{ offre?.qteMin | number: '1.0-0' }}</span>
                    </button>

                    <button *ngIf="offre?.qteMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                      <span>Qté Max</span>
                      &nbsp;

                      <i class="mdi mdi-arrow-right mdi-16px"></i>
                      &nbsp;

                      <span>{{ offre?.qteMax | number: '1.0-0' }}</span>
                    </button>

                    <button *ngIf="offre?.valeurMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                      <span>Valeur Min</span>
                      &nbsp;

                      <i class="mdi mdi-arrow-right mdi-16px"></i>
                      &nbsp;

                      <span>{{ offre?.valeurMin | number: '1.2-2' }}</span>
                    </button>

                    <button *ngIf="offre?.valeurMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                      <span>Valeur Max</span>
                      &nbsp;

                      <i class="mdi mdi-arrow-right mdi-16px"></i>
                      &nbsp;

                      <span>{{ offre?.valeurMax | number: '1.2-2' }}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div *ngIf="offre.listePaliersRemisesAdditionnels?.length;"
              class="col-auto my-1 mx-1 rounded-lg d-flex justify-content-start p-1"
              [style.background-color]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-grid-light)'">
              <div class="col-auto">
                <p class="h5 mb-0 text-dark">Remises Additionnelles de l'Offre
                </p>
                <div class="d-flex gap-2 flex-wrap ">
                  <wph-paliers-view viewMode="badge"
                    [paliers]="mergeListePaliers(offre.listePaliersRemisesAdditionnels)"></wph-paliers-view>
                </div>
              </div>
            </div>
          </ng-container>

          <div
            *ngIf="(offre.listePaliersRemisesAdditionnels.length || hasOffreConditions(offre)) && cmdType === TYPE_CMD.UNITAIRE"
            class="col-md-4 col-12 p-0 mx-1 my-0 conditions-row d-flex align-items-end">
            <ng-container>
              <button *ngIf="hasOffreConditions(offre)" (click)="open(popConditions)"
                class="btn btn-sm bg-cstm-info-fs mx-1 mt-2 mb-0 px-2 py-1 b-radius">Conditions d'Offre <i
                  class="bi bi-arrows-angle-expand ml-1"></i></button>
              <button *ngIf="offre.listePaliersRemisesAdditionnels.length " (click)="open(popPaliers)"
                class="btn btn-sm bg-cstm-info-fs mx-1 mt-2 mb-0 px-2 py-1 b-radius">Remises d'Offre <i
                  class="bi bi-arrows-angle-expand ml-1"></i></button>

              <ng-template #popConditions let-modal>
                <div class="modal-header">
                  <h4 class="modal-title" id="modal-basic-title">{{"Conditions d'Offre" | uppercase}}</h4>

                  <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                    <span class="h3">&times;</span>
                  </button>
                </div>

                <div class="modal-body">
                  <div class="row d-flex justify-content-between p-1 mx-0 w-100 align-items-center"
                    [style.background-color]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-grid-light)'"
                    style="border-radius: 5px">
                    <span class="h5 text-dark">Conditions d'Offre</span>

                    <div class="d-flex gap-2 flex-wrap px-1 m-0 w-100">
                      <div class="conditions-row" [id]="(currentPlateforme$ | async) + '-container'">
                        <button *ngIf="offre?.qteMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                          <span>Qté Min</span>
                          &nbsp;

                          <i class="mdi mdi-arrow-right mdi-16px"></i>
                          &nbsp;

                          <span>{{ offre?.qteMin | number }}</span>
                        </button>

                        <button *ngIf="offre?.qteMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                          <span>Qté Max</span>
                          &nbsp;

                          <i class="mdi mdi-arrow-right mdi-16px"></i>
                          &nbsp;

                          <span>{{ offre?.qteMax | number }}</span>
                        </button>

                        <button *ngIf="offre?.valeurMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                          <span>Valeur Min</span>
                          &nbsp;

                          <i class="mdi mdi-arrow-right mdi-16px"></i>
                          &nbsp;

                          <span>{{ offre?.valeurMin | number: '1.2-2' }}</span>
                        </button>

                        <button *ngIf="offre?.valeurMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
                          <span>Valeur Max</span>
                          &nbsp;

                          <i class="mdi mdi-arrow-right mdi-16px"></i>
                          &nbsp;

                          <span>{{ offre?.valeurMax | number: '1.2-2' }}</span>
                        </button>
                      </div>
                    </div>
                  </div>

                </div>

                <div class="modal-footer">
                  <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">
                    Fermer
                  </button>
                </div>
              </ng-template>

              <ng-template #popPaliers let-modal>
                <div class="modal-header">
                  <h4 class="modal-title" id="modal-basic-title">{{"Remises d'Offre" | uppercase}}</h4>

                  <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                    <span class="h3">&times;</span>
                  </button>
                </div>

                <div class="modal-body">
                  <div class="row d-flex justify-content-between p-1 mx-0 w-100 align-items-center"
                    [style.background-color]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-grid-light)'"
                    style=" border-radius: 5px">
                    <span class="h5 text-dark">Remises d'Offre</span>

                    <div class="col-12 px-0 d-flex gap-2 flex-wrap justify-content-start align-items-center">
                      <wph-paliers-view [viewMode]="'badge'"
                        [paliers]="mergeListePaliers(offre.listePaliersRemisesAdditionnels)"
                        #palierViewBloc></wph-paliers-view>
                    </div>
                  </div>

                </div>

                <div class="modal-footer">
                  <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">
                    Fermer
                  </button>
                </div>
              </ng-template>
            </ng-container>
          </div>

          <div *ngIf="offre?.attachments && offre?.attachments?.length" class="col-auto m-1 p-0 d-flex align-items-end">
            <button (click)="openModal(attachmentsModal, 'md')" class="btn attachement-btn mx-1 py-1 px-2">
              Voir l'offre originale <i class="mdi mdi-paperclip mdi-16px"></i>
            </button>
          </div>
        </div>

        <ng-container *jhiHasAnyAuthority="['ROLE_RESPONSABLE']">
          <div
            *ngIf="cmdType === TYPE_CMD.GROUPE && (offre?.etatCommandeAchatGroupe === 'ACCEPTEE' || offre?.etatCommandeAchatGroupe === 'FIN_SAISIE' || offre?.etatCommandeAchatGroupe === 'EN_ATTENTE') && !(isInactive$ | async)"
            class="row px-1 py-0 mb-1">
            <div class="col-md-4 col-12">
              <div class="form-group">
                <label for="distributeurs" class="form-label fs-5 h5 text-sub">Distributeur <span
                    class="text-danger">*</span></label>
                <select2 id="distributeurs" [data]="distributeursValuePair" [(ngModel)]="offre.distributeur"
                  class="form-control-md" placeholder="Sélectionner distributeur">
                </select2>
              </div>
            </div>

            <div class="col-md-4 col-12">
              <div class="form-group">
                <label for="supporteurs" class="form-label fs-5 h5 text-sub">Supporteur <span
                    class="text-danger">*</span></label>
                <select2 id="supporteurs" [data]="supporteursValuePair" [(ngModel)]="offre.supporterEntreprise"
                  class="form-control-md" placeholder="Sélectionner supporteur">
                </select2>
              </div>
            </div>

            <div *ngIf="offre?.etatCommandeAchatGroupe !== 'EN_ATTENTE'"
              class="col-md-4 col-12 d-flex align-items-center">
              <button (click)="enregistrerLesModifications()" type="button" class="btn btn-lg text-white mt-2"
                style="background: var(--fs-success); border-radius: 10px">
                <i class="bi bi-check"></i>
                Enregistrer les modifications
              </button>
            </div>
          </div>
        </ng-container>

      </div>

    </div>
  </ng-template>

</ng-container>


<ng-container *ngIf="listeBlocsOffreProduits">
  <div id="produitsCmdUnitaire" style="padding: 0 !important;" [wphGridCellNavigation]="['qteCMD']"
    (cellNavigationCommit)="handleCellCommit($event, produitsGridTemp)">
    <kendo-grid [data]="gridData" [resizable]="true" class="w-100 h-100 fs-grid fs-grid-white"
      (cellClick)="cellClickHandler($event)" (cellClose)="cellCloseHandler($event)" [rowClass]="rowClassProduitUnitaire"
      #produitsGridTemp>

      <ng-template kendoGridToolbarTemplate>
        <div class="d-flex row mx-0 w-100 align-items-center justify-content-between grid-top-radius">
          <div class="col-auto p-0 m-0 d-flex align-items-center">
            <span class="h4 text-dark">Liste des produits</span>
          </div>

          <div class="col-auto p-0 m-0 d-flex justify-content-end">
            <div class="row mx-0 d-flex align-items-center">
              <div *ngIf="!coffretEnabled && !readOnly" class="col-auto p-0 mx-2 my-0">
                <span class="accelerator-container d-flex align-items-center">
                  <input type="number" [readOnly]="readOnly" placeholder="0" min="0" [value]="0"
                    (focus)="accQteCmd.select()" autocomplete="off"
                    (keydown.enter)="appliquerLaSaisieAccelere(accQteCmd.value, parentOfProduits, 'unitaire')"
                    class="form-control form-control-md text-center" style="width: 60px;" #accQteCmd>

                  <span (click)="!readOnly && appliquerLaSaisieAccelere(accQteCmd.value, parentOfProduits, 'unitaire')"
                    class="px-2 py-1 pointer-cus d-flex align-items-center text-white">
                    <i class="bi bi-save" style="font-size: 16px;"></i>
                  </span>
                </span>
              </div>

              <div *ngIf="coffretEnabled" class="col-auto p-0 mx-2 my-0">
                <span class="coffret-qte-container d-flex align-items-center">
                  <span (click)="!readOnly && qteCmdPackCoffretValueChange(null, false, true)"
                    class="px-2 py-1 pointer-cus d-flex align-items-center text-white">
                    <i class="mdi mdi-minus"></i>
                  </span>

                  <input type="number" [readOnly]="readOnly" (ngModelChange)="qteCmdPackCoffretValueChange($event)"
                    [ngModel]="qteCmdPackCoffret" class="form-control form-control-sm text-center" style="width: 60px;">

                  <span (click)="!readOnly && qteCmdPackCoffretValueChange(null, true)"
                    class="px-2 py-1 pointer-cus d-flex align-items-center text-white">
                    <i class="mdi mdi-plus"></i>
                  </span>
                </span>
              </div>

              <div class="col-auto p-0 m-0">
                <div class="input-group picker-input">
                  <input type="search" placeholder="Rechercher par Designation" [(ngModel)]="rechercheProduitUnitModel"
                    class="form-control form-control-md pl-4 bl-input-search" id="groupeCritere" style="width: 270px"
                    autocomplete="off" />

                  <div class="picker-icons picker-icons-alt">
                    <i class="mdi mdi-magnify pointer"></i>
                  </div>
                </div>
              </div>

            </div>

          </div>
        </div>
      </ng-template>

      <kendo-grid-column [footerStyle]="{'white-space': 'normal'}" class="text-wrap" [footerClass]="'text-right'"
        [style]="{'white-space': 'normal'}" field="libelleProduit" title="Libellé produit" [width]="400">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <div class="d-flex text-wrap align-items-center" [id]="dataItem?.parent?.id + '-' + dataItem?.id">
            <img
              (click)="dataItem?.docImageBlocOffre && openLargImageModal(largeImgModal, getBlocImage(dataItem?.docImageBlocOffre?.idhash))"
              [src]="dataItem?.docImageBlocOffre ? getBlocImage(dataItem?.docImageBlocOffre?.idhash) : 'assets/images/default-img-alt.png'"
              class="img-fluid-alt mx-1">
            <span class="mx-1">{{dataItem.libelleProduit}} <span
                *ngIf="dataItem.conditionCmdUnitaireSpecGroup?.blocObligatoire === 'O'"
                class="text-danger">(Obligatoire**)</span> </span>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [footerClass]="'text-right'" class="text-right" field="ppv" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">PPV</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.ppv | number:'1.2-2':'fr-FR'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [footerClass]="'text-right'" class="text-right" field="prixVenteTtc" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">PPH</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.prixVenteTtc | number:'1.2-2':'fr-FR'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column *ngIf="!coffretEnabled" [footerClass]="'text-center'" class="text-center" field="colisage"
        [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Colisage</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.colisage | number:'1.0-0':'fr-FR'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column *ngIf="!coffretEnabled" [footerClass]="'text-center'" class="text-center" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Qté Min</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{ dataItem.conditionCmdUnitaireSpecGroup?.qteMin | number: '1.0-0' }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column *ngIf="!coffretEnabled" [footerClass]="'text-center'" class="text-center" field="qteCmd"
        [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Qté Cmd</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <div class="d-flex justify-content-center"
            [attr.data-prefix]="'qteCMD' + parentOfProduits?.id + '-' + rowIndex">
            <span class="bg-white px-2 py-1 text-right columnQte" (click)="readOnly && fireEvent($event)"
              popoverClass="increase-popover-width " placement="left"
              [ngbPopover]="dataItem.messageEtat ? cstmPopoverTemplate : null" container="body" [ngClass]="{
              'column-success': (dataItem.qteCmd > 0 && dataItem?.etat !== 'I'),
              'column-error': ((dataItem.qteCmd > 0 || dataItem?.conditionCmdUnitaireSpecGroup?.blocObligatoire === 'O') && dataItem?.etat === 'I')
            }" style="border-radius: 10px; width: 120px; border: 1px solid #ccc" #ngbPopover="ngbPopover">
              {{dataItem.qteCmd || 0 | number:'1.0-0':'fr-FR'}}

              <ng-template #cstmPopoverTemplate>
                <wph-popover-template [popoverRef]="ngbPopover"
                  [popoverContent]="dataItem?.messageEtat"></wph-popover-template>
              </ng-template>
            </span>
          </div>
        </ng-template>

        <ng-template kendoGridEditTemplate let-dataItem="dataItem" let-column="column" let-formGroup="formGroup"
          let-rowIndex="rowIndex">
          <div class="d-flex justify-content-center" style="width: auto">
            <input [formControl]="formGroup.get('qteCmd')" wphAllowOnlyNumbers type="number"
              (blur)="produitsGridTemp?.closeCell()" class="form-control d-block text-right mx-1" id="qteCmd"
              placeholder="Qté Cmd" (focus)="ngbPopover.open()" data-toggle="input-mask" mask="separator.0"
              thousandSeparator=" " popoverClass="increase-popover-width" placement="left" autocomplete="off"
              [ngbPopover]="dataItem.messageEtat ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover"
              [attr.data-prefix]="'qteCMD' + parentOfProduits?.id + '-' + rowIndex" data-reverse="true"
              [readOnly]="readOnly" style="max-width: 120px;">

            <ng-template #cstmPopoverTemplate>
              <wph-popover-template [popoverRef]="ngbPopover"
                [popoverContent]="dataItem?.messageEtat"></wph-popover-template>
            </ng-template>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column *ngIf="coffretEnabled" [footerClass]="'text-center'" class="text-center"
        field="qteFixePrdInCoffret" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Qté Fixe</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <div class="d-flex justify-content-start">
            <span class="bg-white px-2 py-1 text-right"
              style="border-radius: 10px; width: 120px; border: 1px solid #ccc">
              {{dataItem.qteFixePrdInCoffret | number:'1.0-0':'fr-FR'}}
            </span>
          </div>
        </ng-template>
      </kendo-grid-column>

      <ng-container *ngIf="!coffretEnabled">
        <kendo-grid-column [footerClass]="'text-left'" class="text-left" [width]="100">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Paliers</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="row d-flex justify-content-center">
              <span (click)="dataItem?.listePaliers?.length && open(popPaliers)"
                class="actions-icons text-white pointer-cus"
                [ngClass]="{'bg-cstm-info-alt': !dataItem?.listePaliers?.length, 'bg-only-cstm-info': dataItem?.listePaliers?.length}"
                title="Afficher Paliers">
                <i class="bi bi-arrows-angle-expand"
                  [ngClass]="{'opacity-light': !dataItem?.listePaliers?.length }"></i>
              </span>
            </div>

            <ng-template #popPaliers let-modal>
              <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Paliers du produit</h4>

                <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                  <span class="h4">&times;</span>
                </button>
              </div>

              <div class="modal-body">
                <div class="row d-flex justify-content-between p-1 mx-0 w-100 align-items-center"
                  [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-grid-light)'"
                  style="border-radius: 5px">
                  <span class="h5 text-dark">Remises</span>

                  <div class="col-12 px-0 d-flex gap-2 flex-wrap justify-content-start align-items-center">
                    <wph-paliers-view [viewMode]="'badge'" [bloc]="dataItem" #palierViewBloc></wph-paliers-view>
                  </div>
                </div>

              </div>

              <div class="modal-footer">
                <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">
                  Fermer
                </button>
              </div>
            </ng-template>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-center'" class="text-center" [width]="50">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="row d-flex justify-content-center" *ngIf="hasGroupConditions(dataItem)">
              <span
                [ngClass]="{'bg-cstmm-info-wf': (currentPlateforme$ | async) === 'WIN_GROUPE', 'bg-cstmm-info-fs': (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT'}"
                class="actions-icons text-white pointer-cus"
                [popoverClass]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'info-popover-container-wf' : 'info-popover-container-fs'"
                placement="left" container="body" [ngbPopover]="dataItem?.conditionsString">
                <i class="bi bi-info-lg"></i>
              </span>
            </div>
          </ng-template>
        </kendo-grid-column>
      </ng-container>

      <ng-template kendoGridNoRecordsTemplate>
        <span>Aucun résultat trouvé.</span>
      </ng-template>

    </kendo-grid>
  </div>
</ng-container>


<ng-template #blocGroupeTemplate let-bloc="bloc" let-blocGroupeIndex="blocGroupeIndex">
  <div [id]="bloc?.typeBloc === 'G' ? 'pack-tab-nav-commande-groupe' : 'pack-tab-nav-commande-reference'"
    class="card bloc-card card-border card-bloc-radius bg-transparent w-100">
    <ul ngbNav #blocGroupeNav="ngbNav" class="nav-tabs">
      <li [ngbNavItem]="blocGroupeIndex">
        <a ngbNavLink class="row d-flex align-items-center" style="gap: 8px; min-width: 250px;" [ngClass]="{
            'pack-error': bloc?.etat === 'I', 'pack-success': bloc?.etat === 'V'
          }">
          <div class="col p-0 m-0 d-flex align-items-center">
            <span class="truncate-two-lines"
              [title]="bloc?.titre + (bloc?.blocObligatoire === 'O' ? '(Obligatoire**)' : '')">{{ bloc?.titre }} <b
                *ngIf="bloc?.blocObligatoire === 'O'">(Obligatoire**)</b></span>
          </div>

          <div class="d-block">
            <button *ngIf="bloc.etat === 'I' " type="button" (click)="fireEvent($event)"
              popoverClass="increase-popover-width " placement="left"
              [ngbPopover]="bloc.messageEtat ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover">
              <i class="bi bi-patch-exclamation-fill" style="font-size: 20px; vertical-align: middle;"></i>

              <ng-template #cstmPopoverTemplate>
                <wph-popover-template [popoverRef]="ngbPopover"
                  [popoverContent]="bloc?.messageEtat"></wph-popover-template>
              </ng-template>
            </button>

            <button *ngIf="bloc?.etat === 'V'" type="button" (click)="fireEvent($event)">
              <i class="bi bi-patch-check-fill " style="font-size: 20px; vertical-align: middle;"></i>
            </button>
          </div>

          <span
            (click)="(bloc.displayDetailsSousBloc = !bloc.displayDetailsSousBloc); forceExpandOrCollapseAllSousBlocs(bloc, bloc.displayDetailsSousBloc)"
            [title]="(bloc.displayDetailsSousBloc ? 'Cacher' : 'Afficher') + ' les détails des produits'"
            class="collapse-expand-icon mx-1 d-flex align-items-center">
            <i *ngIf="!bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-right"></i>
            <i *ngIf="bloc.displayDetailsSousBloc" class="bi bi-28px bi-chevron-down"></i>
          </span>
        </a>

        <ng-template ngbNavContent>
          <div *ngIf="bloc.displayDetailsSousBloc" @fade class="row mx-0 w-100">
            <div *ngIf="(cmdType === TYPE_CMD.GROUPE || cmdType === TYPE_CMD.INDIVIDUELLE)"
              class="row d-flex m-0 p-0 w-100 justify-content-between align-items-center " style="font-weight: 700;">
              <div class="col-{{bloc.displayConditionsEtRemisesRow ? '12' : 'auto'}} m-0 p-0">
                <div class="row m-0 p-0">
                  <button
                    *ngIf="!bloc?.displayConditionsEtRemisesRow && (hasConditions(bloc) || bloc?.listePaliers?.length)"
                    (click)="bloc.displayConditionsEtRemisesRow = !bloc.displayConditionsEtRemisesRow"
                    class="btn bg-cstm-info-fs">
                    <span>
                      Voir Remises & Conditions <i class="bi bi-arrows-angle-expand"></i>
                    </span>
                  </button>

                  <div class="row ml-0 mr-1 mt-2 mb-1 px-1 py-0 w-100 h-100 d-flex flex-wrap"
                    [ngClass]="{'hidden-container': !bloc?.displayConditionsEtRemisesRow}"
                    *ngIf="(hasConditions(bloc) || bloc?.listePaliers?.length)"
                    [style.background-color]="(currentPlateforme$ | async)  === 'FEDERATION_SYNDICAT' ? 'var(--fs-grid-light)' : 'var(--wf-primary-100)'"
                    style="border-radius: 8px;">
                    <span (click)="bloc.displayConditionsEtRemisesRow = false" class="remises-row-close"
                      style="top: 5px !important">
                      <i class="mdi mdi-close"></i> Fermer
                    </span>
                    <ng-container [ngTemplateOutlet]="badgeBlocConditionsTemplate"
                      [ngTemplateOutletContext]="{ bloc: bloc }"></ng-container>

                    <span *ngIf="selectedOption !== 'M' && (offre?.etatCommandeAchatGroupe !== 'EN_ATTENTE')"
                      class="d-flex col-auto align-items-center p-1 m-0">
                      <span class="actions-icons actions-icons-ov text-white pointer-cus bg-cstm-info-fs-icon-only">
                        <i class="bi bi-arrows-angle-expand text-white"></i>
                      </span>
                      <span class="text-dark mx-1">Conditions ou remises appliquées sur la ligne de produit</span>
                    </span>
                  </div>
                </div>
              </div>

              <div
                *ngIf="(canModifyGroupConditions && !(isInactive$ | async) && selectedOption === 'M') || (offre?.etatCommandeAchatGroupe === 'EN_ATTENTE')"
                class="col-auto px-0 py-1 m-0 d-flex align-items-center">
                <button class="btn btn-sm bg-offer bg-obligatoire py-1 px-2  mr-2 d-flex align-items-center pointer-cus"
                  style="border-radius: 10px;">
                  <label for="cb5-{{bloc?.hash}}" class="my-0 ml-0 mr-1">Obligatoire</label>

                  <input type="checkbox" class="m-0" id="cb5-{{bloc?.hash}}" #ckObligatoire
                    (change)="updateValueObligatoire(bloc, ckObligatoire.checked)"
                    [checked]="bloc.conditionCmdUnitaireSpecGroup.blocObligatoire === 'O' ? true : false"
                    style="width: 20px; height: 20px;">
                </button>


                <button (click)="openConditionsModal(groupeConditions, bloc)"
                  class="btn btn-sm px-2 py-1 bg-offer bg-cstm-info-fs"
                  style="border-radius: 10px; font-size: .9rem !important">
                  <span class="mx-1">Conditions</span>
                  <i class="bi bi-arrows-angle-expand"></i>
                </button>
              </div>

              <span *ngIf="(!hasConditions(bloc) && !bloc?.listePaliers?.length)" class="mt-1"></span>
            </div>

            <ng-container *ngIf="cmdType === TYPE_CMD.UNITAIRE">
              <div class="row d-flex m-0 w-100 p-0 justify-content-between conditions-row">

                <div class="col-12 m-0 p-0">
                  <div class="row m-0 p-0 w-100">
                    <button (click)="bloc.displayConditionsEtRemisesRow = !bloc.displayConditionsEtRemisesRow"
                      *ngIf="!bloc?.displayConditionsEtRemisesRow && (hasGroupeConditions(bloc?.conditionCmdUnitaireSpecGroup) || bloc?.listePaliers?.length)"
                      class="btn bg-cstm-info-fs">
                      <span>
                        Voir Remises & Conditions <i class="bi bi-arrows-angle-expand"></i>
                      </span>
                    </button>

                    <div class="row mx-0 mt-2 mb-1 px-1 py-0 w-100 h-100 d-flex flex-wrap"
                      *ngIf="(hasGroupeConditions(bloc?.conditionCmdUnitaireSpecGroup) || bloc?.listePaliers?.length)"
                      [ngClass]="{'hidden-container': !bloc?.displayConditionsEtRemisesRow}"
                      [style.background-color]="(currentPlateforme$ | async)  === 'FEDERATION_SYNDICAT' ? 'var(--fs-grid-light)' : 'var(--wf-primary-100)'"
                      style="border-radius: 8px;">
                      <span (click)="bloc.displayConditionsEtRemisesRow = false"
                        class="remises-row-close d-flex align-items-center"
                        style="top: 5px !important; right: 0 !important">
                        <i class="mdi mdi-close"></i> Fermer
                      </span>
                      <ng-container [ngTemplateOutlet]="badgeBlocConditionsTemplate"
                        [ngTemplateOutletContext]="{ bloc: bloc, isGroupeConditions: true }"></ng-container>

                      <span class="d-flex col-12 align-items-center p-1 m-0">
                        <span class="actions-icons actions-icons-ov text-white pointer-cus bg-cstm-info-fs-icon-only">
                          <i class="bi bi-arrows-angle-expand text-white"></i>
                        </span>
                        <span class="text-dark mx-1">Conditions ou remises appliquées sur la ligne de produit</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <span *ngIf="!hasGroupeConditions(bloc?.conditionCmdUnitaireSpecGroup) && !bloc?.listePaliers?.length"
                class="mt-1"></span>
            </ng-container>

            <ng-template #noConditions>
              <div class="col-auto p-0 m-0 mt-1"></div>
            </ng-template>

            <ng-container *ngIf="existeFilsBlocsProduits(bloc)">
              <wph-bloc-offre *ngIf="cmdType === TYPE_CMD.UNITAIRE" [readOnly]="readOnly"
                [listeBlocsOffreProduits]="getListeFilsBlocsProduits(bloc)"></wph-bloc-offre>

              <ng-container>
                <!-- Total des produits commandés grid -->
                <div style="overflow-x: auto;">
                  <ng-container *ngIf="cmdType === TYPE_CMD.GROUPE">
                    <ng-container *ngIf="selectedOption === 'F' && offre?.etatCommandeAchatGroupe !== 'EN_ATTENTE'"
                      [ngTemplateOutlet]="produitBlocCmdGroupe" [ngTemplateOutletContext]="{bloc: bloc}">
                    </ng-container>

                    <!-- Total des produits commandés par membre grid -->
                    <ng-container *ngIf="selectedOption === 'M' || offre?.etatCommandeAchatGroupe === 'EN_ATTENTE'"
                      [ngTemplateOutlet]="produitParMembreBlocCmdGroupe" [ngTemplateOutletContext]="{bloc: bloc}">
                    </ng-container>
                  </ng-container>

                  <!-- Liste des produits pour une commande individuelle -->
                  <ng-container *ngIf="cmdType === TYPE_CMD.INDIVIDUELLE"
                    [ngTemplateOutlet]="produitBlocCmdIndividuelle" [ngTemplateOutletContext]="{bloc: bloc}">
                  </ng-container>
                </div>

              </ng-container>
            </ng-container>

            <ng-container *ngIf="getListeFilsBlocsNonProduits(bloc)?.length">
              <div class="row mx-auto my-2 w-100"
                *ngFor="let blocFils of getListeFilsBlocsNonProduits(bloc); let blocFilsIndex=index; trackBy: trackFn">
                <ng-container [ngTemplateOutlet]="blocGroupeTemplate"
                  [ngTemplateOutletContext]="{bloc: blocFils, blocGroupeIndex: blocFilsIndex}">
                </ng-container>
              </div>
            </ng-container>
          </div>
        </ng-template>

      </li>
    </ul>

    <div [ngbNavOutlet]="blocGroupeNav" class="px-1 pb-1 pt-0 card-border-alt mx-0 w-100"></div>
  </div>
</ng-template>


<!-- Produits of bloc commande groupe grid template -->
<ng-template #produitBlocCmdGroupe let-bloc="bloc">
  <div id="produitsCommande" class="p-0" [wphGridCellNavigation]="['qteUGSaisie']"
    (cellNavigationCommit)="handleCellCommit($event, produitsCmdGroupe)">
    <kendo-grid class="fs-grid fs-grid-white w-100 h-100" (cellClick)="cellClickHandler($event)"
      [rowClass]="rowClassGeneric" (cellClose)="cellCloseHandler($event)"
      [data]="{ data: bloc?.listeFils, total: bloc?.listeFils?.length}" #produitsCmdGroupe>

      <ng-template kendoGridToolbarTemplate>
        <div class="d-flex row mx-0 w-100 align-items-center justify-content-between grid-top-radius">
          <div class="col-auto m-0 p-0 d-flex align-items-center">
            <span class="h4 text-dark">Liste des produits</span>
          </div>

          <div class="col-auto p-0 m-0 d-flex align-items-center">
            <div class="input-group picker-input">
              <input type="search" placeholder="Rechercher par Designation" [(ngModel)]="bloc.designationProduit"
                class="form-control form-control-md pl-4 bl-input-search" id="groupeCritere" style="width: 270px"
                autocomplete="off" />

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>
        </div>
      </ng-template>

      <kendo-grid-column field="libelleProduit" [sticky]="true" class="text-wrap" title="Libellé de produit"
        [width]="300">
        <ng-template kendoGridCellTemplate let-dataItem>
          <div [id]="bloc?.id + '-' + dataItem?.id">
            {{ dataItem?.libelleProduit }} <span class="text-danger"
              *ngIf="dataItem?.blocObligatoire === 'O'">(Obligatoire**)</span>
          </div>
        </ng-template>

      </kendo-grid-column>

      <kendo-grid-column field="ppv" [width]="100" class="text-right">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">PPV</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.ppv | number: '1.2-2'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="prixVenteTtc" class="text-right" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">PPH</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.prixVenteTtc | number: '1.2-2' }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="colisage" class="text-center" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Colisage</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.colisage | number: '1.0-0' }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [editable]="false" [footerClass]="'text-center'" class="text-center" field="qteCmd"
        [width]="120">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Total CMD</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="d-flex justify-content-center">
            <span class="bg-white px-2 py-1 text-right columnQte" popoverClass="increase-popover-width "
              placement="left" [ngbPopover]="dataItem.messageEtat ? cstmPopoverTemplate : null" container="body"
              [ngClass]="{
            'column-success': (dataItem.qteCmd > 0 && dataItem?.etat !== 'I'),
            'column-error': ((dataItem.qteCmd > 0 || dataItem?.blocObligatoire === 'O') && dataItem?.etat === 'I')
          }" style="border-radius: 10px; width: 120px; border: 1px solid #ccc" #ngbPopover="ngbPopover">{{
              (dataItem?.qteCmd ?? 0) |
              number: '1.0-0' }}</span>

            <ng-template #cstmPopoverTemplate>
              <wph-popover-template [popoverRef]="ngbPopover"
                [popoverContent]="dataItem?.messageEtat"></wph-popover-template>
            </ng-template>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [footerClass]="'text-center'" class="text-center" field="tauxRemise" [width]="120">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Remise(%)</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{ dataItem.qteCmd ? (dataItem.tauxRemise | number:'1.0-0':'fr-FR') : ' '}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [footerClass]="'text-center'" class="text-center" field="totalQteUg" [width]="120">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Qté UG</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{ dataItem.totalQteUg > 0 ? (dataItem.totalQteUg | number:'1.0-0':'fr-FR') : ' '}}
        </ng-template>

      </kendo-grid-column>

      <kendo-grid-column *ngIf="offre?.etatCommandeAchatGroupe === 'FIN_SAISIE' && hasRatioUg(bloc?.listePaliers)"
        [footerClass]="'text-center'" class="text-center" field="qteUgSaisie" [width]="130">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Qté UG Saisie</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <div class="d-flex justify-content-center" [attr.data-prefix]="'qteUGSaisie' + bloc?.id + '-' + rowIndex">
            <span [ngClass]="{
            'column-success': (dataItem?.qteUgSaisie > 0 && dataItem?.etat !== 'I'),
            'column-error': (dataItem?.qteUgSaisie > 0 && dataItem?.etat === 'I')
          }" class="bg-white px-2 py-1 text-right columnQte"
              style="border-radius: 10px; width: 120px; border: 1px solid #ccc">
              {{ dataItem?.qteUgSaisie ? (dataItem?.qteUgSaisie | number:'1.0-0':'fr-FR') : 'Saisir' }}
            </span>
          </div>
        </ng-template>

        <ng-template kendoGridEditTemplate let-dataItem="dataItem" let-column="column" let-formGroup="formGroup"
          let-rowIndex="rowIndex">
          <div class="d-flex justify-content-center" style="width: auto">
            <input (blur)="produitsCmdGroupe?.closeCell()" [readonly]="offre?.etatCommandeAchatGroupe !== 'FIN_SAISIE'"
              wphAllowOnlyNumbers [formControl]="formGroup.get('qteUgSaisie')" type="text" autocomplete="off"
              class="form-control d-block mx-1 text-right" id="qteUgSaisie" placeholder="Qté UG saisie"
              data-toggle="input-mask" mask="separator.0" thousandSeparator=" " data-reverse="true"
              style="max-width: 120px;" [attr.data-prefix]="'qteUGSaisie' + bloc?.id + '-' + rowIndex">
          </div>
        </ng-template>

      </kendo-grid-column>

      <ng-container *ngIf="!offre?.coffretEnabled">
        <kendo-grid-column title="" [width]="120" *ngIf="offre?.etatCommandeAchatGroupe === 'FIN_SAISIE'">
          <ng-template kendoGridHeaderTemplate>
            <span class="text-wrap">Type Remise</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem>
            <span class="d-block w-100" *ngIf="dataItem.qteCmd > 0 && !!dataItem.selectedTypeRemiseEnum">
              <label for="RF{{dataItem.id}}"> RF </label> <input id="RF{{dataItem.id}}" type="radio"
                name="selectedTypeRemiseEnum{{dataItem.id}}" value="RF" [ngModel]="dataItem.selectedTypeRemiseEnum"
                class="mx-1" (ngModelChange)="selectedRemiseChange($event, dataItem)">

              <label for="UG{{dataItem.id}}"> UG </label> <input id="UG{{dataItem.id}}" type="radio"
                name="selectedTypeRemiseEnum{{dataItem.id}}" value="UG" [ngModel]="dataItem.selectedTypeRemiseEnum"
                class="mx-1" (ngModelChange)="selectedRemiseChange($event, dataItem)">
            </span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-left'" class="text-left" [width]="110">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Conditions</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="row d-flex justify-content-center">
              <span (click)="hasConditions(dataItem) && open(popConditons)" class="actions-icons text-white pointer-cus"
                [ngClass]="{'bg-cstm-info-alt': !hasConditions(dataItem), 'bg-only-cstm-info': hasConditions(dataItem)}"
                title="Afficher Conditions">
                <i class="bi bi-arrows-angle-expand text-white"
                  [ngClass]="{'opacity-light': !hasConditions(dataItem)}"></i>
              </span>
            </div>

            <ng-template #popConditons let-modal>
              <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Conditions du produit</h4>

                <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                  <i class="mdi mdi-close mdi-18px"></i>
                </button>
              </div>

              <div class="modal-body">
                <wph-bloc-offre-conditions [readOnly]="readOnly" [blocOffre]="dataItem">
                </wph-bloc-offre-conditions>
              </div>

              <div class="modal-footer">
                <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">
                  Fermer
                </button>
              </div>
            </ng-template>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-left'" class="text-left" [width]="100">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Paliers</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="row d-flex justify-content-center">
              <span (click)="dataItem?.listePaliers?.length && open(popPaliers)"
                class="actions-icons text-white pointer-cus"
                [ngClass]="{'bg-cstm-info-alt': !dataItem?.listePaliers?.length, 'bg-only-cstm-info': dataItem?.listePaliers?.length}"
                title="Afficher Paliers">
                <i class="bi bi-arrows-angle-expand"
                  [ngClass]="{'opacity-light': !dataItem?.listePaliers?.length }"></i>
              </span>
            </div>

            <ng-template #popPaliers let-modal>
              <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Paliers du produit</h4>

                <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                  <span class="h3">&times;</span>
                </button>
              </div>

              <div class="modal-body">
                <div class="row d-flex justify-content-between p-1 mx-0 w-100 align-items-center"
                  [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-grid-light)'"
                  style="border-radius: 5px">
                  <span class="h5 text-dark">Remises</span>

                  <button *ngIf="!readOnly" (click)="palierViewBloc.openNewPalier()"
                    class="btn btn-sm btn-light text-dark" style="font-weight: 600; border-radius: 10px">
                    <i class="bi bi-plus-lg"></i>
                    <span class="mx-1">Ajouter</span>
                  </button>

                  <div class="col-12 px-0 d-flex gap-2 flex-wrap justify-content-start align-items-center">
                    <wph-paliers-view [editableBadge]="!readOnly" [viewMode]="'badge'" [bloc]="dataItem"
                      #palierViewBloc></wph-paliers-view>
                  </div>
                </div>

              </div>

              <div class="modal-footer">
                <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">
                  Fermer
                </button>
              </div>
            </ng-template>
          </ng-template>
        </kendo-grid-column>
      </ng-container>

      <ng-template kendoGridNoRecordsTemplate>
        <span>Aucun résultat trouvé.</span>
      </ng-template>
    </kendo-grid>
  </div>
</ng-template>

<!-- Produits of bloc commande individuelle grid template-->
<ng-template #produitBlocCmdIndividuelle let-bloc="bloc">
  <div id="produitsCmdUnitaire" style="padding: 0 !important;" [wphGridCellNavigation]="['qteCMD', 'qteUGSaisie']"
    (cellNavigationCommit)="handleCellCommit($event, produitsIndividuelleTemp)">
    <kendo-grid [data]="{ data: bloc?.listeFils, total: bloc?.listeFils?.length}" [resizable]="true"
      class="w-100 h-100 fs-grid fs-grid-white" (cellClick)="cellClickHandler($event)"
      (cellClose)="cellCloseHandler($event)" [rowClass]="rowClassGeneric" #produitsIndividuelleTemp>

      <ng-template kendoGridToolbarTemplate>
        <div class="d-flex row mx-0 w-100 align-items-center justify-content-between grid-top-radius">
          <div class="col-auto p-0 m-0 d-flex align-items-center">
            <span class="h4 text-dark">Liste des produits</span>
          </div>

          <div class="col-auto p-0 m-0 d-flex justify-content-end">
            <div class="row mx-0 d-flex align-items-center">
              <div *ngIf="!coffretEnabled && !readOnly" class="col-auto p-0 mx-2 my-0">
                <span class="accelerator-container d-flex align-items-center">
                  <input type="number" [readOnly]="readOnly" placeholder="0" min="0" [value]="0"
                    (focus)="accQteCmd.select()" autocomplete="off"
                    (keydown.enter)="appliquerLaSaisieAccelere(accQteCmd.value, bloc, 'groupe')"
                    class="form-control form-control-md text-center" style="width: 60px;" #accQteCmd>

                  <span (click)="!readOnly && appliquerLaSaisieAccelere(accQteCmd.value, bloc, 'groupe')"
                    class="px-2 py-1 pointer-cus d-flex align-items-center text-white">
                    <i class="bi bi-save" style="font-size: 16px;"></i>
                  </span>
                </span>
              </div>

              <div *ngIf="coffretEnabled" class="col-auto p-0 mx-2 my-0">
                <span class="coffret-qte-container d-flex align-items-center">
                  <span (click)="!readOnly && qteCmdPackCoffretValueChange(null, false, true)"
                    class="px-2 py-1 pointer-cus d-flex align-items-center text-white">
                    <i class="mdi mdi-minus"></i>
                  </span>

                  <input type="number" [readOnly]="readOnly" (ngModelChange)="qteCmdPackCoffretValueChange($event)"
                    [ngModel]="qteCmdPackCoffret" class="form-control form-control-sm text-center" style="width: 60px;">

                  <span (click)="!readOnly && qteCmdPackCoffretValueChange(null, true)"
                    class="px-2 py-1 pointer-cus d-flex align-items-center text-white">
                    <i class="mdi mdi-plus"></i>
                  </span>
                </span>
              </div>

              <div class="col-auto p-0 m-0">
                <div class="input-group picker-input">
                  <input type="search" placeholder="Rechercher par Designation" [(ngModel)]="bloc.designationProduit"
                    class="form-control form-control-md pl-4 bl-input-search" id="groupeCritere" style="width: 270px"
                    autocomplete="off" />

                  <div class="picker-icons picker-icons-alt">
                    <i class="mdi mdi-magnify pointer"></i>
                  </div>
                </div>
              </div>

            </div>

          </div>
        </div>
      </ng-template>

      <kendo-grid-column [footerStyle]="{'white-space': 'normal'}" class="text-wrap" [footerClass]="'text-right'"
        [style]="{'white-space': 'normal'}" field="libelleProduit" title="Libellé produit" [width]="400">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <div class="d-flex text-wrap align-items-center" [id]="bloc?.id + '-' + dataItem?.id">
            <img
              (click)="dataItem?.docImageBlocOffre && openLargImageModal(largeImgModal, getBlocImage(dataItem?.docImageBlocOffre?.idhash))"
              [src]="dataItem?.docImageBlocOffre ? getBlocImage(dataItem?.docImageBlocOffre?.idhash) : 'assets/images/default-img-alt.png'"
              class="img-fluid-alt mx-1 pointer-cus">
            <span class="mx-1">{{dataItem.libelleProduit}} <span *ngIf="dataItem?.blocObligatoire === 'O'"
                class="text-danger">(Obligatoire**)</span> </span>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [footerClass]="'text-right'" class="text-right" field="ppv" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">PPV</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem?.ppv | number:'1.2-2':'fr-FR'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [footerClass]="'text-right'" class="text-right" field="prixVenteTtc" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">PPH</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem?.prixVenteTtc | number:'1.2-2':'fr-FR'}}
        </ng-template>
      </kendo-grid-column>

      <ng-container *ngIf="!coffretEnabled">
        <kendo-grid-column [footerClass]="'text-center'" class="text-center" field="colisage" [width]="100">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Colisage</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{dataItem?.colisage | number:'1.0-0':'fr-FR'}}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-center'" class="text-center" [width]="100">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Qté Min</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{ dataItem?.qteMin | number: '1.0-0' }}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-center'" class="text-center" field="qteCmd" [width]="100">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Qté Cmd</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="d-flex justify-content-center" [attr.data-prefix]="'qteCMD' + bloc?.id + '-' + rowIndex">
              <span class="bg-white px-2 py-1 text-right columnQte" (click)="readOnly && fireEvent($event)"
                popoverClass="increase-popover-width " placement="left"
                [ngbPopover]="dataItem.messageEtat ? cstmPopoverTemplate : null" container="body" [ngClass]="{
                  'column-success': (dataItem.qteCmd > 0 && dataItem?.etat !== 'I'),
                  'column-error': ((dataItem.qteCmd > 0 || dataItem?.blocObligatoire === 'O') && dataItem?.etat === 'I')
                }" style="border-radius: 10px; width: 120px; border: 1px solid #ccc" #ngbPopover="ngbPopover">
                {{dataItem.qteCmd || 0 | number:'1.0-0':'fr-FR'}}
              </span>

              <ng-template #cstmPopoverTemplate>
                <wph-popover-template [popoverRef]="ngbPopover"
                  [popoverContent]="dataItem?.messageEtat"></wph-popover-template>
              </ng-template>
            </div>
          </ng-template>

          <ng-template kendoGridEditTemplate let-dataItem="dataItem" let-column="column" let-formGroup="formGroup"
            let-rowIndex="rowIndex">
            <div class="d-flex justify-content-center" style="width: auto">
              <input [formControl]="formGroup.get('qteCmd')" wphAllowOnlyNumbers type="number"
                (blur)="produitsIndividuelleTemp?.closeCell()" class="form-control text-right d-block mx-1" id="qteCmd"
                placeholder="Qté Cmd" (focus)="ngbPopover.open()" data-toggle="input-mask" mask="separator.0"
                thousandSeparator=" " popoverClass="increase-popover-width" placement="left" autocomplete="off"
                [ngbPopover]="dataItem.messageEtat ? cstmPopoverTemplate : null" container="body"
                #ngbPopover="ngbPopover" style="max-width: 120px"
                [attr.data-prefix]="'qteCMD' + bloc?.id + '-' + rowIndex" data-reverse="true" [readOnly]="readOnly">

              <ng-template #cstmPopoverTemplate>
                <wph-popover-template [popoverRef]="ngbPopover"
                  [popoverContent]="dataItem?.messageEtat"></wph-popover-template>
              </ng-template>
            </div>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-center'" class="text-center" field="tauxRemise" [width]="120">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Remise(%)</span>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{validCommande && dataItem.qteCmd ? (dataItem.tauxRemise | number:'1.0-0':'fr-FR') : ' '}}
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-center'" class="text-center" field="totalQteUg" [width]="120"
          [hidden]="!blocHasTauxUg(bloc) && !blocHasRatioUg(bloc)">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Qté UG</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            {{dataItem.totalQteUg > 0 ? (dataItem.totalQteUg | number:'1.0-0':'fr-FR') : ' '}}
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-center'" class="text-center" field="qteUgSaisie" [width]="130"
          [hidden]="!hasRatioUg(bloc?.listePaliers)">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Qté UG Saisie</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="d-flex justify-content-center px-1"
              [attr.data-prefix]="'qteUGSaisie' + bloc?.id + '-' + rowIndex">
              <span [ngClass]="{
              'column-success': (dataItem?.qteUgSaisie > 0 && dataItem?.etat !== 'I'),
              'column-error': ((dataItem?.qteUgSaisie > 0 || dataItem?.blocObligatoire === 'O') && dataItem?.etat === 'I')
            }" class="bg-white px-2 py-1 text-right columnQte"
                style="border-radius: 10px; width: 120px; border: 1px solid #ccc">
                {{ dataItem?.qteUgSaisie ? (dataItem?.qteUgSaisie | number:'1.0-0':'fr-FR') : 'Saisir' }}
              </span>
            </div>
          </ng-template>

          <ng-template kendoGridEditTemplate let-dataItem="dataItem" let-column="column" let-formGroup="formGroup"
            let-rowIndex="rowIndex">
            <div class="d-flex justify-content-center px-1" style="width: auto;">
              <input (blur)="produitsIndividuelleTemp?.closeCell()" [readonly]="readOnly" wphAllowOnlyNumbers
                autocomplete="off" [formControl]="formGroup.get('qteUgSaisie')" type="text"
                class="form-control d-block text-right mx-1" id="qteUgSaisie" placeholder="Qté UG"
                data-toggle="input-mask" mask="separator.0" thousandSeparator=" " data-reverse="true"
                style="max-width: 110px" [attr.data-prefix]="'qteUGSaisie' + bloc?.id + '-' + rowIndex">
            </div>
          </ng-template>

        </kendo-grid-column>

        <kendo-grid-column title="" [width]="120" [hidden]="!canSelectTypeRfUg(bloc?.listeFils) || readOnly">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span class="d-block w-100"
              *ngIf="dataItem.qteCmd > 0 && (dataItem?.selectedPalier?.typeSelectionRfUg === 'OR')">
              <label for="RF{{dataItem.id}}"> RF </label> <input id="RF{{dataItem.id}}" type="radio"
                name="selectedTypeRemiseEnum{{dataItem.id}}" value="RF" [ngModel]="dataItem.selectedTypeRemiseEnum"
                class="mx-1" (ngModelChange)="selectedRemiseChange($event, dataItem)">

              <label for="UG{{dataItem.id}}"> UG </label> <input id="UG{{dataItem.id}}" type="radio"
                name="selectedTypeRemiseEnum{{dataItem.id}}" value="UG" [ngModel]="dataItem.selectedTypeRemiseEnum"
                class="mx-1" (ngModelChange)="selectedRemiseChange($event, dataItem)">
            </span>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-left'" class="text-left" [width]="100">
          <ng-template kendoGridHeaderTemplate>
            <span class="d-flex justify-content-center w-100">Paliers</span>
          </ng-template>

          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="row d-flex justify-content-center">
              <span (click)="dataItem?.listePaliers?.length && open(popPaliers)"
                class="actions-icons text-white pointer-cus"
                [ngClass]="{'bg-cstm-info-alt': !dataItem?.listePaliers?.length, 'bg-only-cstm-info': dataItem?.listePaliers?.length}"
                title="Afficher Paliers">
                <i class="bi bi-arrows-angle-expand"
                  [ngClass]="{'opacity-light': !dataItem?.listePaliers?.length }"></i>
              </span>
            </div>

            <ng-template #popPaliers let-modal>
              <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Paliers du produit</h4>

                <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                  <span>&times;</span>
                </button>
              </div>

              <div class="modal-body">
                <div class="row d-flex justify-content-between p-1 mx-0 w-100 align-items-center"
                  [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-grid-light)'"
                  style="border-radius: 5px">
                  <span class="h5 text-dark">Remises</span>

                  <div class="col-12 px-0 d-flex gap-2 flex-wrap justify-content-start align-items-center">
                    <wph-paliers-view [viewMode]="'badge'" [bloc]="dataItem" #palierViewBloc></wph-paliers-view>
                  </div>
                </div>

              </div>

              <div class="modal-footer">
                <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">
                  Fermer
                </button>
              </div>
            </ng-template>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column [footerClass]="'text-center'" class="text-center" [width]="50"
          [hidden]="!hasAnyProduitsConditions(bloc?.listeFils)">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <div class="row d-flex justify-content-center" *ngIf="hasConditions(dataItem)">
              <span
                [ngClass]="{'bg-cstmm-info-wf': (currentPlateforme$ | async) === 'WIN_GROUPE', 'bg-cstmm-info-fs': (currentPlateforme$ | async) === 'FEDERATION_SYNDICAT'}"
                class="actions-icons text-white pointer-cus"
                [popoverClass]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'info-popover-container-wf' : 'info-popover-container-fs'"
                placement="left" container="body" [ngbPopover]="buildDataItemConditions(dataItem, cmdType)">
                <i class="bi bi-info-lg"></i>
              </span>
            </div>
          </ng-template>
        </kendo-grid-column>
      </ng-container>

      <kendo-grid-column *ngIf="coffretEnabled" [footerClass]="'text-center'" class="text-center"
        field="qteFixePrdInCoffret" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <span class="d-flex justify-content-center w-100">Qté Fixe</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <div class="d-flex justify-content-start">
            <span class="bg-white px-2 py-1 text-right"
              style="border-radius: 10px; width: 120px; border: 1px solid #ccc">
              {{dataItem.qteFixePrdInCoffret | number:'1.0-0':'fr-FR'}}
            </span>
          </div>
        </ng-template>
      </kendo-grid-column>

      <ng-template kendoGridNoRecordsTemplate>
        <span>Aucun résultat trouvé.</span>
      </ng-template>

    </kendo-grid>
  </div>
</ng-template>

<!-- Produits par membre of commande groupe grid template -->
<ng-template #produitParMembreBlocCmdGroupe let-bloc="bloc">
  <div id="produitsParMembre" class="p-0" [wphGridCellNavigation]="['member']"
    (cellNavigationCommit)="handleCellCommit($event, membresParProduitGrid)">
    <kendo-grid class="fs-grid fs-grid-white w-100 h-100" (cellClick)="cmdGroupeCellClick($event)"
      (cellClose)="cmdGroupeCellClose($event)" [resizable]="true" [rowClass]="rowClassGeneric"
      [data]="{ data: bloc?.listeFils, total: bloc?.listeFils?.length }" #membresParProduitGrid>
      <ng-template kendoGridToolbarTemplate>
        <div class="d-flex row mx-0 w-100 align-items-center justify-content-between grid-top-radius">
          <div class="col-auto p-0 m-0 d-flex align-items-center">
            <span class="h4 text-dark">{{offre?.etatCommandeAchatGroupe === 'EN_ATTENTE' ? 'Liste des produits' :
              'Total commandé des produits par membre'}}</span>
          </div>

          <div class="col-auto p-0 m-0 d-flex align-items-center">
            <div class="input-group picker-input">
              <input type="search" placeholder="Rechercher par Designation" [(ngModel)]="bloc.designationProduit"
                class="form-control form-control-md pl-4 bl-input-search" id="groupeCritere" style="width: 270px"
                autocomplete="off" />

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>
        </div>
      </ng-template>

      <kendo-grid-column [hidden]="!canModifyGroupConditions || (isInactive$ | async) === true" class="text-center"
        [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <div class="d-flex justify-content-center px-2 align-items-center w-100">Obligatoire</div>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="d-flex justify-content-center">
            <input type="checkbox" #ckObligatoire (change)="updateValueObligatoire(dataItem, ckObligatoire.checked)"
              [checked]="dataItem.conditionCmdUnitaireSpecGroup?.blocObligatoire === 'O' ? true : false"
              style="width: 25px; height: 25px" />
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [hidden]="!canModifyGroupConditions || (isInactive$ | async) === true" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <div class="d-flex justify-content-center px-2 align-items-center w-100">Conditions</div>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="d-flex justify-content-center">
            <span (click)="openConditionsModal(conditionsDuGroupe, dataItem)"
              class="actions-icons text-white pointer-cus"
              [ngClass]="{'bg-cstm-info-alt': !hasGroupConditions(dataItem), 'bg-only-cstm-info': hasGroupConditions(dataItem)}"
              title="Afficher Conditions">
              <i class="bi bi-plus-circle-fill text-white"></i>
            </span>

            <ng-template #conditionsDuGroupe let-modal>
              <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Conditions du produit</h4>

                <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                  <i class="mdi mdi-close mdi-18px"></i>
                </button>
              </div>

              <div class="modal-body">

                <ng-container>
                  <div class="form-group row mx-0 p-0">
                    <div class="col-6 p-0">
                      <label for="qteMin123" class="col-12 col-form-label">Qte Min</label>
                      <div class="col-12">
                        <input [(ngModel)]="dataItem.conditionCmdUnitaireSpecGroup.qteMin"
                          [disabled]="!!dataItem.conditionCmdUnitaireSpecGroup.valeurMin || !!dataItem.conditionCmdUnitaireSpecGroup.valeurMax"
                          type="number" class="form-control text-right" id="qteMin123" data-toggle="input-mask"
                          mask="separator.0" thousandSeparator=" " data-reverse="true">
                      </div>
                    </div>

                    <div class="col-6 p-0">
                      <label for="qteMax123" class="col-12 col-form-label">Qte Max</label>
                      <div class="col-12">
                        <input [(ngModel)]="dataItem.conditionCmdUnitaireSpecGroup.qteMax"
                          [disabled]="!!dataItem.conditionCmdUnitaireSpecGroup.valeurMin || !!dataItem.conditionCmdUnitaireSpecGroup.valeurMax"
                          type="number" class="form-control text-right" id="qteMax123" data-toggle="input-mask"
                          mask="separator.0" thousandSeparator=" " data-reverse="true">
                      </div>
                    </div>
                  </div>


                  <div class="form-group row mx-0">
                    <div class="col-6 p-0">
                      <label for="valMin123" class="col-12 col-form-label">Valeur Min</label>
                      <div class="col-12">
                        <input [(ngModel)]="dataItem.conditionCmdUnitaireSpecGroup.valeurMin"
                          [disabled]="!!dataItem.conditionCmdUnitaireSpecGroup.qteMax || !!dataItem.conditionCmdUnitaireSpecGroup.qteMin"
                          type="number" class="form-control text-right" id="valMin123" data-toggle="input-mask"
                          mask="separator.2" thousandSeparator=" " data-reverse="true" appDecimal2Correct>
                      </div>
                    </div>

                    <div class="col-6 p-0">
                      <label for="valMax123" class="col-12 col-form-label">Valeur Max</label>
                      <div class="col-12">
                        <input [(ngModel)]="dataItem.conditionCmdUnitaireSpecGroup.valeurMax"
                          [disabled]="!!dataItem.conditionCmdUnitaireSpecGroup.qteMax || !!dataItem.conditionCmdUnitaireSpecGroup.qteMin"
                          type="number" class="form-control text-right" id="valMax123" data-toggle="input-mask"
                          mask="separator.2" thousandSeparator=" " data-reverse="true" appDecimal2Correct>
                      </div>
                    </div>
                  </div>
                </ng-container>

              </div>

              <div class="modal-footer">
                <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">
                  Fermer
                </button>
              </div>
            </ng-template>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="libelleProduit" class="text-wrap" title="Libellé de produit" [width]="300">
        <ng-template kendoGridCellTemplate let-dataItem>
          <div [id]="bloc?.id + '-' + dataItem?.id">
            {{ dataItem?.libelleProduit }} <span class="text-danger"
              *ngIf="dataItem?.blocObligatoire === 'O'">(Obligatoire**)</span>
          </div>
        </ng-template>

      </kendo-grid-column>

      <kendo-grid-column [width]="50" [hidden]="(membersPerPage > membresSupporteurs?.length) || !canGoToPreviousPage">
        <ng-template kendoGridHeaderTemplate>
          <button *ngIf="canGoToPreviousPage" (click)="previousPage()"
            class="btn w-100 mx-1 d-flex align-items-center justify-content-center px-2 py-1"
            [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-500)' : 'var(--fs-primary-500)'"
            style="border-radius: 5px">
            <i class="bi bi-chevron-double-left" style="color: white;"></i>
          </button>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [editable]="true"
        *ngFor="let membre of currentMembers; let membreIndex=index; trackBy: trackMembres"
        [title]="'Dr. ' + membre?.nomResponsable" field="_{{membre?.id}}" [width]="180">
        <ng-template kendoGridHeaderTemplate>
          <div class="d-flex text-wrap text-center w-100 align-items-center justify-content-center">
            <b>Dr. {{ membre?.nomResponsable }}</b>
          </div>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <div class="d-flex justify-content-center"
            [attr.data-prefix]="'member' + bloc?.id + '-' + membreIndex + '-' + rowIndex">
            <span class="bg-white px-2 py-1 text-right" [ngClass]="{
                  'column-success': ((dataItem.qteCmd > 0 && dataItem.mapBlocsByMemberId[membre.id]?.qteCmd) && dataItem?.etat !== 'I'),
                  'column-error': (((dataItem.qteCmd > 0 && dataItem.mapBlocsByMemberId[membre.id]?.qteCmd) || dataItem?.blocObligatoire === 'O') && dataItem?.etat === 'I')
                }" style="border-radius: 10px; width: 120px; border: 1px solid #ccc"
              popoverClass="increase-popover-width " placement="left"
              [ngbPopover]="(isAdminUser && dataItem.mapBlocsByMemberId[membre.id]?.qteCmd > 0) ? (dataItem.messageEtat ? cstmPopoverTemplate : '') : ''"
              container="body" #ngbPopover="ngbPopover">
              {{ (dataItem.mapBlocsByMemberId[membre.id]?.qteCmd ?? 0) | number: '1.0-0'}}
            </span>

            <ng-template #cstmPopoverTemplate>
              <wph-popover-template [popoverRef]="ngbPopover"
                [popoverContent]="dataItem?.messageEtat"></wph-popover-template>
            </ng-template>
          </div>
        </ng-template>

        <ng-template kendoGridEditTemplate let-rowIndex="rowIndex" let-dataItem="dataItem" let-column="column"
          let-formGroup="formGroup">
          <div class="d-flex justify-content-center" style="width: auto">
            <input [formControl]="formGroup.get('qteCmd')" type="number" (focus)="ngbPopover?.open()"
              (blur)="membresParProduitGrid?.closeCell()"
              [attr.data-prefix]="'member' + bloc?.id + '-' + membreIndex + '-' + rowIndex" wphAllowOnlyNumbers
              autocomplete="off" class="form-control d-block mx-1 text-right" id="qteCmd" placeholder="Qté Cmd"
              data-toggle="input-mask" mask="separator.0" thousandSeparator=" " data-reverse="true"
              popoverClass="increase-popover-width" placement="left"
              [ngbPopover]="dataItem.messageEtat ? cstmPopoverTemplate : null" container="body" #ngbPopover="ngbPopover"
              [readOnly]="offre?.etatCommandeAchatGroupe !== 'FIN_SAISIE'" style="max-width: 120px;">
          </div>

          <ng-template #cstmPopoverTemplate>
            <wph-popover-template [popoverRef]="ngbPopover"
              [popoverContent]="dataItem?.messageEtat"></wph-popover-template>
          </ng-template>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="50" [hidden]="(membersPerPage > membresSupporteurs?.length) || !canGoToNextPage">
        <ng-template kendoGridHeaderTemplate>
          <button *ngIf="canGoToNextPage" (click)="nextPage()"
            class="btn w-100 mx-1 d-flex align-items-center justify-content-center px-2 py-1"
            [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-500)' : 'var(--fs-primary-500)'"
            style="border-radius: 5px">
            <i class="bi bi-chevron-double-right" style="color: white; "></i>
          </button>
        </ng-template>
      </kendo-grid-column>

      <ng-template kendoGridNoRecordsTemplate>
        <span>Aucun résultat trouvé.</span>
      </ng-template>

    </kendo-grid>
  </div>
</ng-template>

<!-- Afficher tout conditions modal template -->
<ng-template #modalExtraItems let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">{{'Toutes les conditions : ' + extraConditionsTargetBloc?.titre |
      uppercase}}</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span>&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <div class="d-flex gap-2 flex-wrap">
      <button *ngIf="extraConditionsTargetBloc?.qteMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Qté Min</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.qteMin | number: '1.0-0' }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.qteMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Qté Max</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.qteMax | number: '1.0-0' }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.valeurMin"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Valeur Min</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.valeurMin | number: '1.2-2' }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.valeurMax"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Valeur Max</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.valeurMax | number: '1.2-2' }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.nbrObjFilsMin"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Sous blocs CMD Min</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.nbrObjFilsMin }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.nbrObjFilsMax"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Sous blocs CMD Max</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.nbrObjFilsMax }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.nombreProduitsMin"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Nbr Prd CMD Min</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.nombreProduitsMin }}</span>
      </button>

      <button *ngIf="extraConditionsTargetBloc?.nombreProduitsMax"
        class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
        <span>Nbr Prd CMD Max</span>
        &nbsp;

        <i class="mdi mdi-arrow-right mdi-16px"></i>
        &nbsp;

        <span>{{ extraConditionsTargetBloc?.nombreProduitsMax }}</span>
      </button>

      <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
        <wph-conditions-dynamiques *ngIf="extraConditionsTargetBloc?.dynamicScriptCondition" [readOnly]="true"
          [ngModel]="extraConditionsTargetBloc.dynamicScriptCondition"></wph-conditions-dynamiques>
      </ng-container>

    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-outline-dark ml-2" (click)="modal.dismiss('Cross click')">Fermer</button>
  </div>
</ng-template>

<ng-template #badgeBlocConditionsTemplate let-bloc="bloc" let-isGroupeConditions="isGroupeConditions"
  let-displaySimple="displaySimple">
  <div
    *ngIf="(hasConditions(bloc) && (cmdType !== TYPE_CMD.UNITAIRE)) || (isGroupeConditions && hasGroupeConditions(bloc?.conditionCmdUnitaireSpecGroup))"
    class="col-{{displaySimple ? 'auto' : '12'}} rounded-lg d-flex justify-content-start m-0 p-1"
    [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-grid-light)'">
    <div class="col-auto px-0">
      <ng-container *ngIf="displaySimple" [ngTemplateOutlet]="remTitle"></ng-container>

      <ng-template #remTitle>
        <p class="text-start text-dark m-0 mr-1 h5">
          Conditions de
          <span *ngIf="bloc?.typeBloc === 'P'">Pack</span>
          <span *ngIf="bloc?.typeBloc === 'G'">Groupe</span>
          <span *ngIf="bloc?.typeBloc === 'R'">Référence</span>
          <span *ngIf="bloc?.typeBloc === 'F'">Produit</span>
          <span *ngIf="!bloc?.typeBloc">l'offre</span>
        </p>
      </ng-template>
      <div class="d-flex align-items-center gap-2 flex-wrap py-0 px-1 m-0 w-100">
        <ng-container *ngIf="!displaySimple" [ngTemplateOutlet]="remTitle"></ng-container>
        <div *ngIf="isGroupeConditions" class="conditions-row">
          <button *ngIf="bloc?.conditionCmdUnitaireSpecGroup?.qteMin"
            class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Qté Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.conditionCmdUnitaireSpecGroup?.qteMin | number: '1.0-0' }}</span>
          </button>

          <button *ngIf="bloc?.conditionCmdUnitaireSpecGroup?.qteMax"
            class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Qté Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.conditionCmdUnitaireSpecGroup?.qteMax | number: '1.0-0' }}</span>
          </button>

          <button *ngIf="bloc?.conditionCmdUnitaireSpecGroup?.valeurMin"
            class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Valeur Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.conditionCmdUnitaireSpecGroup?.valeurMin | number: '1.2-2' }}</span>
          </button>

          <button *ngIf="bloc?.conditionCmdUnitaireSpecGroup?.valeurMax"
            class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Valeur Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.conditionCmdUnitaireSpecGroup?.valeurMax | number: '1.2-2' }}</span>
          </button>

        </div>

        <div *ngIf="!isGroupeConditions" class="conditions-row">
          <button *ngIf="bloc?.qteMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Qté Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.qteMin | number: '1.0-0' }}</span>
          </button>

          <button *ngIf="bloc?.qteMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Qté Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.qteMax | number: '1.0-0' }}</span>
          </button>

          <button *ngIf="bloc?.valeurMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Valeur Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.valeurMin | number: '1.2-2' }}</span>
          </button>

          <button *ngIf="bloc?.valeurMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Valeur Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.valeurMax | number: '1.2-2' }}</span>
          </button>

          <button *ngIf="bloc?.nbrObjFilsMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Sous blocs CMD Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nbrObjFilsMin }}</span>
          </button>

          <button *ngIf="bloc?.nbrObjFilsMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Sous blocs CMD Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nbrObjFilsMax }}</span>
          </button>

          <button *ngIf="bloc?.nombreProduitsMin" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Nbr Prd CMD Min</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nombreProduitsMin }}</span>
          </button>

          <button *ngIf="bloc?.nombreProduitsMax" class="btn btn-sm bg-conditions b-radius my-1 mr-1 text-white">
            <span>Nbr Prd CMD Max</span>
            &nbsp;

            <i class="mdi mdi-arrow-right mdi-16px"></i>
            &nbsp;

            <span>{{ bloc?.nombreProduitsMax }}</span>
          </button>

        </div>

        <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
          <wph-conditions-dynamiques [readOnly]="true" *ngIf="bloc?.dynamicScriptCondition"
            [ngModel]="bloc.dynamicScriptCondition"></wph-conditions-dynamiques>
        </ng-container>

        <button *ngIf="hasManyConditions(bloc)" (click)="openExtraConditionsModal(modalExtraItems, bloc)"
          class="btn btn-sm bg-see-more my-1 px-2 py-0">
          <span></span>
          <i class="bi bi-plus"></i>
        </button>
      </div>
    </div>

  </div>

  <div *ngIf="bloc.listePaliers?.length || bloc?.listePaliersRemisesAdditionnels?.length"
    class="col-{{displaySimple ? 'auto' : '12'}} h-100 m-0 rounded-lg d-flex justify-content-start p-1"
    [style.backgroundColor]="(currentPlateforme$ | async) === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-grid-light)'">
    <div class="col-auto px-0">
      <p *ngIf="displaySimple" class="m-0 h5 text-dark">{{ bloc.listePaliers?.length ? 'Remises Financières' : "Remises
        d'Offre" }}</p>
      <div class="d-flex align-items-center gap-2 flex-wrap">
        <p *ngIf="!displaySimple" class="m-0 h5 text-dark">{{ bloc.listePaliers?.length ? 'Remises Financières' :
          "Remises d'Offre" }}</p>
        <wph-paliers-view viewMode="badge"
          [paliers]="mergeListePaliers(bloc.listePaliers || bloc?.listePaliersRemisesAdditionnels)"></wph-paliers-view>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #groupeConditions let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Conditions de
      <span *ngIf="selectedGroupeConditionsBloc?.typeBloc === 'P'">Pack</span>
      <span *ngIf="selectedGroupeConditionsBloc?.typeBloc === 'G'">Groupe</span>
      <span *ngIf="selectedGroupeConditionsBloc?.typeBloc === 'R'">Référence</span>
    </h4>

    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <i class="mdi mdi-close mdi-18px"></i>
    </button>
  </div>

  <div class="modal-body">
    <div class="form-group row mx-0 p-0">
      <div class="col-6 p-0">
        <label for="qteMin123" class="col-12 col-form-label">Qte Min</label>
        <div class="col-12">
          <input [(ngModel)]="selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.qteMin"
            [disabled]="!!selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.valeurMin || !!selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.valeurMax"
            type="number" class="form-control text-right" id="qteMin123" data-toggle="input-mask" mask="separator.0"
            thousandSeparator=" " data-reverse="true">
        </div>
      </div>

      <div class="col-6 p-0">
        <label for="qteMax123" class="col-12 col-form-label">Qte Max</label>
        <div class="col-12">
          <input [(ngModel)]="selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.qteMax"
            [disabled]="!!selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.valeurMin || !!selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.valeurMax"
            type="number" class="form-control text-right" id="qteMax123" data-toggle="input-mask" mask="separator.0"
            thousandSeparator=" " data-reverse="true">
        </div>
      </div>
    </div>


    <div class="form-group row mx-0">
      <div class="col-6 p-0">
        <label for="valMin123" class="col-12 col-form-label">Valeur Min</label>
        <div class="col-12">
          <input [(ngModel)]="selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.valeurMin"
            [disabled]="!!selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.qteMax || !!selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.qteMin"
            type="number" class="form-control text-right" id="valMin123" data-toggle="input-mask" mask="separator.2"
            thousandSeparator=" " data-reverse="true" appDecimal2Correct>
        </div>
      </div>

      <div class="col-6 p-0">
        <label for="valMax123" class="col-12 col-form-label">Valeur Max</label>
        <div class="col-12">
          <input [(ngModel)]="selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.valeurMax"
            [disabled]="!!selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.qteMax || !!selectedGroupeConditionsBloc.conditionCmdUnitaireSpecGroup.qteMin"
            type="number" class="form-control text-right" id="valMax123" data-toggle="input-mask" mask="separator.2"
            thousandSeparator=" " data-reverse="true" appDecimal2Correct>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-outline-dark" (click)="modal.close('Close Btn click')">
      Fermer
    </button>
  </div>
</ng-template>

<ng-template #largeImgModal let-modal>
  <img *ngIf="largeImageTargetUrl" [src]="largeImageTargetUrl" style="width: auto; height: auto;"
    alt="Selected post image">

  <div *ngIf="targetItem" class="row mx-0 my-2 w-100 justify-content-center">
    <button (click)="downloadDisplayedLargeImage(targetItem)" class="btn attachement-btn d-flex align-items-center">
      Imprimer <i class="bi bi-printer ml-1"></i>
    </button>
  </div>
</ng-template>

<ng-template #attachmentsModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Pièces jointes de l'offre originale</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span class="h3">&times;</span>
    </button>
  </div>

  <div [id]="(currentPlateforme$ | async) + '-container'" class="modal-body"
    style="max-height: 280px; overflow-y: auto">
    <div class="row w-100 mx-0 flex-wrap k-gap-3">
      <div class="col-12 p-0 m-0">
        <div *ngFor="let item of offre.attachments; let i = index" (click)="openAttachement(item, largeImgModal)"
          [ngClass]="{'border-bottom': i < offre.attachments.length - 1}"
          class="row attachement-row w-100 py-1 px-0 mx-0 mt-0 mb-1 border-bottom pointer-cus">
          <div class="col-auto d-flex align-items-center">
            <span class="attachment-item d-flex align-items-center justify-content-center">
              <i class="bi bi-file-earmark-richtext-fill"></i>
            </span>
          </div>
          <div class="col-auto d-flex align-items-center">
            <span class="text-dark h5 truncate-two-lines">{{ item?.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <div class="d-flex row w-100 mx-0 align-items-center justify-content-end">
      <button type="button" class="btn btn-outline-dark mx-1" (click)="modal.dismiss('Cross click')"
        style="border: 1px solid #6c737c">Fermer</button>
    </div>
  </div>
</ng-template>

<wph-pdf-viewer [src]="blobUrl" [title]="pdfViewerTitle"></wph-pdf-viewer>