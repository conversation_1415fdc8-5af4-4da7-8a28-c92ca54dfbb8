import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { NavController } from '@ionic/angular';
import {AuthService} from "@wph/core/auth";

@Component({
  selector: 'wph-accueil',
  templateUrl: './accueil.page.html',
  styleUrls: ['./accueil.page.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class AccueilPage implements OnInit {
  societe: any;
  principle: any;

  constructor(private navController: NavController, private authService: AuthService) { }

  ngOnInit(): void {
    this.societe = this.authService.getPrincipal().societe;
    this.principle = this.authService.getPrincipal();
  }

  openPage(page:string){
    this.navController.navigateForward(page);
  }

}
