import { EnteteCommandeConsolideeMarche, EnteteCommandeUnitaireMarche } from '@wph/federation-syndicats';
import { Offre, Pagination } from "@wph/data-access";
import { GroupeEntreprise } from "./groupe-entreprise.model";
import { PharmacieEntreprise } from "./pharmacie-entreprise.model";

export type EtatCommande = 'BROUILLON' | 'ACCEPTEE' | 'CLOTUREE' | 'ENVOYEE' | 'CONFIRMEE' | 'ANNULEE' | 'FIN_SAISIE' | 'REFUSEE' | 'EN_LIVRAISON' | 'LIVREE' | 'EN_ATTENTE' | 'TRANSMIS' | 'VALIDEE';

export class FsCommandeCriteria {
    client?: Partial<PharmacieEntreprise>;
    codeCommande?: string;
    etatCommande?: EtatCommande[];
    groupeEntreprise?: GroupeEntreprise;
    etatCommandeAchatGroupe?:EtatCommande[];
    id?: number;
    nomCompletClient?: string;
    titreOffre?: string;
    offre?: Partial<Offre>;
    supporterEntreprise?: PharmacieEntreprise;
    typeCommande?: string;
    dateCreationDebut?:string;
    dateCreationFin?:string;
    distributeurId?:number;
    offreurId?: number;


    constructor(criteria?: Partial<FsCommandeCriteria>) {
        Object.assign(this, criteria);
    }
}

export interface SearchCommandesConsolidee extends Pagination {
    content: Offre[];
}

export interface SearchCommandeUnitaire extends Pagination {
    content: EnteteCommandeUnitaireMarche[];
}
