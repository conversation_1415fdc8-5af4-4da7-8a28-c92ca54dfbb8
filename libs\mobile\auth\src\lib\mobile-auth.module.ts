import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoginComponent } from './pages/login/login.component';
import { MobileAuthRoutingModule } from './mobile-auth-routing.module';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MobileInscriptionComponentsModule } from '@wph/mobile/inscription/components';
import { ForgotPasswordPage } from './pages/forgot-password/forgot-password.page';

@NgModule({
  imports: [
    CommonModule,
    MobileAuthRoutingModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    MobileInscriptionComponentsModule,
  ],
  declarations: [LoginComponent, ForgotPasswordPage],
})
export class MobileAuthModule {}
