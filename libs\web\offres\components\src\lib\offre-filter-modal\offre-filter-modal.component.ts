import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from "@angular/core";
import { ControlContainer, FormGroup } from '@angular/forms';
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { FilterOutput, OffresService, Produit, ProduitCriteria } from "@wph/data-access";
import { DomainEnumeration, SocieteType } from "@wph/shared";
import { Select2 } from "ng-select2-component";
import { Observable, debounceTime, distinctUntilChanged, switchMap, of, map } from "rxjs";

@Component({
    selector: 'wph-offre-filter',
    styleUrls: ['./offre-filter-modal.component.scss'],
    templateUrl: './offre-filter-modal.component.html'
})
export class OffreFilterModalComponent implements OnInit {
    filterForm: FormGroup;
    startsWith: RegExp = new RegExp('^[0-9]*$');
    stautsLabelsValues: any[] = [
        { label: 'Brouillon', value: 'B' },
        { label: 'Annulé', value: 'A' },
        { label: 'Publié', value: 'P' },
        { label: 'Cloturé', value: 'C' }
    ];

    @Input() set categorieProduits(value: DomainEnumeration[]) {
        if (value) {
            this._categorieProduits = value;
        }
    }

    get categorieProduits(): any[] {
        return this._categorieProduits;
    }

    @Input() set forceCloseAllDropdowns(value: boolean) {
        this._forceCloseAllDropdowns = value;
        if (value) {
            this.closeOpenSelect2Dropdowns();
        }
    }
    get forceCloseAllDropdowns(): boolean {
        return this._forceCloseAllDropdowns;
    }
    
    _forceCloseAllDropdowns: boolean = false;
    _categorieProduits: any[] = [];
    @ViewChild('statutSelect') statutSelect: Select2;
    @ViewChild('nonExpireeSelect') nonExpireeSelect: Select2;
    @ViewChild('categorieSelect') categorieSelect: Select2;

    @Output() modalAction: EventEmitter<FilterOutput> = new EventEmitter<FilterOutput>();

    constructor(
        private modalService: NgbModal,
        private offresService: OffresService,
        private controlContainer: ControlContainer
    ) { }

    ngOnInit(): void {
        this.filterForm = this.controlContainer.control as FormGroup<any>;
    }

    closeOpenSelect2Dropdowns(except = null): void {
        if (this.statutSelect?.isOpen && this.statutSelect !== except) {
            this.statutSelect.isOpen = false;
        } else if (this.nonExpireeSelect?.isOpen && this.nonExpireeSelect !== except) {
            this.nonExpireeSelect.isOpen = false;
        } else if (this.categorieSelect?.isOpen && this.categorieSelect !== except) {
            this.categorieSelect.isOpen = false;
        }
    }

    applyFilter(): void {
        this.applyChanges();
        this.modalAction.emit({ filter: true });
    }

    clearFilters(): void {
        this.filterForm.reset({ nonExpireesUniquement: false, categoriePrdId: null }), this.applyChanges();
        this.modalAction.emit({ clear: true });
    }

    applyChanges(): void {
        this.controlContainer.control.setValue({
            ...this.filterForm.value,
            categoriePrdId: this.filterForm.value['categoriePrdId'] || null
        });

        this.controlContainer.control.markAsDirty();
        this.controlContainer.control.markAsTouched();
    }

    dismiss(): void {
        this.modalService.dismissAll();
    }

    filterList(searchQuery: string) {
        let criteriaKey: string, criteria: any = {};
        criteriaKey = this.startsWith.test(searchQuery) ? 'code' : 'raisonSociale';

        criteria = { ...criteria, [criteriaKey]: searchQuery, typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE] };

        return this.offresService.searchSociete(criteria);
    }

    searchFournisseur = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterList(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map(res => res.content.slice(0, 5))
        );

    fournisseurFormatter = (result: { raisonSociale: any; }) => result ? result.raisonSociale : null;

    laboFormatter = (result: { raisonSociale: any; }) => result ? (result.raisonSociale === 'DIVERS' ? null : result.raisonSociale) : null;


    filterListeProduit(searchQuery: string) {
        return this.offresService.searchProduitsPageable(
            new ProduitCriteria({ libelleProduit: searchQuery?.toLowerCase() }), { pageSize: 5, skip: 0 });
    }

    searchOffresByProduit = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                if (term.length > 1) {
                    return this.filterListeProduit(term.toLowerCase());
                }
                return of({ content: [] });
            }),
            map(res => res.content)
        );


    offreFormatteur = (result: Produit | string) => {
        return (typeof result === 'string') ? result : (result as Produit)?.libelleProduit;
    };

}