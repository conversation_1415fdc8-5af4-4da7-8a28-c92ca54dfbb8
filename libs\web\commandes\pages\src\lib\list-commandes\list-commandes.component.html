<!-- Start Of Header -->
<div (click)="closeDropdowns()" class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-12">Commandes </h4>
  </div>
</div>
<!-- END HEADER -->

<div (click)="closeDropdowns()" class="card m-0 w-100 pt-0 pb-1 px-1">
  <div class="card-header py-1 pl-2 mx-0 border">
    <div class="d-flex row justify-content-start align-items-center">
      <div class="col-12 p-0 d-flex justify-content-end">
        <div class="row p-0">
          <div class="col p-0 m-1 d-lg-flex d-none">
            <div class="input-group picker-input">
              <input type="search" placeholder="Titre de l'offre" class="form-control b-radius form-control-md pl-4"
                id="titreOffre" style="border-radius: 8px;" [formControl]="searchControl" />

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
          </div>


          <button type="button" class="btn btn-sm search-btn m-1 b-radius" (click)="displayFilter = !displayFilter">
            <span *ngIf="!displayFilter; else: closeFilter" class="d-flex align-items-center">
              <i class="bi bi-sliders"></i>
              <span class="mx-1">Recherche Avancée</span>
            </span>

            <ng-template #closeFilter>
              <span class="d-flex align-items-center">
                <i class="mdi mdi-close"></i>
                <span class="mx-1">Fermer la recherche</span>
              </span>
            </ng-template>

          </button>
        </div>
      </div>

      <ng-container *ngIf="displayFilter">
        <wph-commandes-filter [formGroup]="commandeForm" [forceCloseAllDropdowns]="forceCloseDropdowns"
          (modalAction)="filterModalAction($event)"></wph-commandes-filter>
      </ng-container>
    </div>
  </div>
  <div class="card-body m-0 p-0">
    <kendo-grid [data]="gridData" (pageChange)="pageChange($event)" [pageable]="{
      buttonCount: 5,
      info: true,
      type: 'numeric',
      pageSizes: pageSizes,
      previousNext: true,
      position: 'bottom'
    }" [pageSize]="navigation.pageSize" [skip]="navigation.skip" [style.minHeight]="'calc(100vh - 198px)'" (cellClick)="cellClick($event)"
      [sortable]="{ mode: 'single' }" [sort]="commandeSort" [resizable]="true" (sortChange)="commandeSortChange($event)"
      #commandeGrid>

      <kendo-grid-column [width]="150" field="codeCommande" title="Code Commande">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap">Code Commande</span>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="250" field="offre.titre" class="text-wrap" title="Titre de l'offre">

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.offre?.titre}}
        </ng-template>
      </kendo-grid-column>


      <kendo-grid-column [width]="180" class="text-wrap" field="client.raisonSociale" title="Client"
        *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.client?.raisonSociale}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="180" class="text-wrap" field="offre.offreur.raisonSociale" title="Offreur">
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <span [ngClass]="{
            'labo-label': dataItem.offre?.offreur?.typeEntreprise === 'FABRIQUANT',
            'grossiste-label': dataItem.offre?.offreur?.typeEntreprise === 'GROSSISTE'
          }">
            <b>{{dataItem.offre?.offreur?.raisonSociale | uppercase}}</b>
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="180" class="text-wrap" field="distributeur.raisonSociale" title="Distributeur">
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <span [ngClass]="{
            'labo-label': dataItem.distributeur?.typeEntreprise === 'FABRIQUANT',
            'grossiste-label': dataItem.distributeur?.typeEntreprise === 'GROSSISTE'
          }">
            <b>{{dataItem.distributeur?.raisonSociale | uppercase}}</b>
          </span>
        </ng-template>

      </kendo-grid-column>

      <kendo-grid-column [width]="170" field="dateCreation" title="Date Création"
        *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']" class="text-right">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap">Date Création</span>
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{ dataItem.dateCreation | momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="170" field="dateConfirmation" title="Date Confirmation" class="text-right">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap">Date Confirmation</span>
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.dateConfirmation | momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="170" field="dateTraitement" title="Date Traitement" class="text-right">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap">Date Traitement</span>
        </ng-template>

        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{ (dataItem?.dateAnnulation || dataItem?.dateAcceptation || dataItem.dateRefus || dataItem.dateSuppression) |
            momentTimezone: "yyyy-MM-DD HH:mm" : "Africa/Casablanca" }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="170" field="valeurCmdNetTtc" title="Montant Net (Dh)" class="text-right">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap">Montant Net (Dh)</span>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{dataItem.valeurCmdNetTtc | number:'1.2-2':'fr-FR' }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [width]="140" field="statut" title="Statut" [sortable]="false" class="text-center">
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
            [extra]="false">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <app-element-status [state]="dataItem.statut"></app-element-status>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Actions" [width]="90"
        *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL', 'ROLE_ASSISTANT']">
        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="d-flex justify-content-center k-gap-2">
            <span (click)="editCommande(dataItem)" [ngClass]="{'opacity-light': dataItem?.statut !== 'B'}"
              class="actions-icons btn-success pointer-cus" title="Modifier Commande">
              <i class="bi bi-pencil-square"></i>
            </span>
          </div>
        </ng-template>

      </kendo-grid-column>

      <kendo-grid-messages pagerItems="lignes" pagerOf="de" pagerItemsPerPage="éléments par page"></kendo-grid-messages>

      <ng-template kendoGridNoRecordsTemplate>
        <span>Aucun résultat trouvé.</span>
      </ng-template>
    </kendo-grid>
  </div>
</div>

<wph-pdf-viewer *ngIf="printing" [src]="blobUrl"></wph-pdf-viewer>

<ng-template #filterModal let-modal>
  <wph-commandes-filter [formGroup]="commandeForm" (modalAction)="filterModalAction($event)"></wph-commandes-filter>
</ng-template>