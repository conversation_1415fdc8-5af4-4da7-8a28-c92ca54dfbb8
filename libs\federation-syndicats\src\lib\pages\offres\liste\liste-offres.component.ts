import { DatePipe } from '@angular/common';
import { AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  CellClickEvent,
  GridDataResult,
  PageChangeEvent,
  SelectionEvent,
} from '@progress/kendo-angular-grid';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AuthService } from '@wph/core/auth';
import {
  Fournisseur,
  Offre,
  OffresService,
  Pagination,
} from '@wph/data-access';
import { AlertService, SocieteType } from '@wph/shared';
import {
  ExportPdf,
  ExportPdfService,
  ScrollService,
  UserInputService,
  getDynamicPageSize,
} from '@wph/web/shared';
import {
  Observable,
  debounceTime,
  distinctUntilChanged,
  switchMap,
  of,
  map,
  Subject,
  takeUntil,
  filter,
  iif,
} from 'rxjs';
import { FsOffreCriteria, SearchOffre } from '../../../models/fs-offre.model';
import {
  EnteteCommandeConsolideeMarche,
  FederationSyndicatService,
  FsOffreService,
  GroupeEntreprise,
  PharmacieEntreprise,
} from '@wph/federation-syndicats';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Avis, TypeAvis } from '../../../models/avis.model';

@Component({
  selector: 'wph-ag-list-offres',
  templateUrl: './liste-offres.component.html',
  styleUrls: ['./liste-offres.component.scss'],
})
export class ListeOffresComponent implements OnInit, OnDestroy {

  unsubscribe$: Subject<boolean> = new Subject<boolean>();

  cardData: Offre[] = [];
  gridData: GridDataResult;

  hasMore: boolean;
  offreSort: SortDescriptor[];
  pageSizes: number[] = [5, 10, 15, 20];
  navigation: Pagination = { pageSize: 10, skip: 0 };
  cardNavigation: Pagination = { pageSize: 10, skip: 0 };

  displayFilter: boolean;
  isListView: boolean = true;
  startsWith: RegExp = new RegExp('^[0-9]*$');

  filterForm: FormGroup;
  searchFilter: FormControl = new FormControl();
  searchCriteria: FsOffreCriteria = new FsOffreCriteria();

  exportPdfRef: ExportPdf;

  selectedFournisseurs: Fournisseur[];
  selectedEnteteCommandeId: number;

  now = new Date();
  isAdmin: boolean;
  isLabo: boolean;

  selectedItem: Offre;

  avis: Avis;
  membreId: number;
  monGroupe: any;

  stautsLabelsValues: any[] = [
    { label: 'Tout', value: null },
    { label: 'Brouillon', value: 'B' },
    { label: 'Annulée', value: 'A' },
    { label: 'Publiée', value: 'P' },
    { label: 'Cloturée', value: 'C' },
  ];

  nonExpireesSelect = [
    { label: 'Tout', value: null },
    { label: 'Oui', value: 'O' },
    { label: 'Non', value: 'N' },
  ];

  natureOffreSelect = [
    { label: 'Toute', value: null },
    { label: 'Offre Groupée', value: 'G' },
    { label: 'Offre Individuelle', value: 'I' }
  ];

  cardViewContainer: HTMLDivElement | null = null;

  @ViewChild('cardViewContainer', { static: false }) scrollContainer: ElementRef<any>;

  constructor(
    private router: Router,
    private fb: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private authService: AuthService,
    private alertService: AlertService,
    private offresService: OffresService,
    private exportPdfServ: ExportPdfService,
    private scrollService: ScrollService,
    private fsOffreService: FsOffreService,
    private userInputService: UserInputService,
    private fedSyndicatService: FederationSyndicatService,
  ) {
    this.initFilterForm();
    this.isAdmin = this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']);
    this.isLabo = this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR']);

    if (!this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN', 'ROLE_AGENT_FOURNISSEUR'])) {
      this.isListView = false;
    }
  }

  ngOnInit(): void {
    // ? Init page content filter/navigation config
    // this.setPageSize();

    this.buildExport();
    this.listenToScrollPosition();
    this.listenToSearchFilterChanges();

    // ? Manage pageMode configuration
    this.route.queryParams.subscribe(qParams => {
      const pageMode = qParams['mode'];

      if (pageMode) {
        this.isListView = (pageMode === 'liste');
      }
    });
    
    // ? Fetch group of current user + Init Avis
    this.membreId = this.authService.getPrincipal()?.societe?.id;

    this.fedSyndicatService.getMyGroupe().then((myGroupe) => {
      this.monGroupe = myGroupe;
      this.initializeAvis();
      this.initOffreGroupe();
    });


  }

  initOffreGroupe(): void {
    if (!this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
      this.searchCriteria = { 
        ...this.searchCriteria, 
        statut: ['P'], 
        nonExpireesUniquement: 'O', 
        groupeConcerne: !this.authService.hasAnyAuthority(['ROLE_NATIONAL']) ? this.monGroupe : null
      };
    } else if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) {
      this.searchCriteria = { ...this.searchCriteria, offreur: this.authService.getPrincipal()?.societe };
    }

    this.searchOffres();
  }

  initializeAvis() {
    this.avis = {
      commentaire: null,
      estResponsable: this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']),
      id: null,
      typeAvis: TypeAvis.Negative,
      raison: '',
      enteteCommandeConsolideeMarche: { id: this.selectedEnteteCommandeId } as EnteteCommandeConsolideeMarche,
      groupeEntreprise: { id: this.monGroupe?.id } as GroupeEntreprise,
      sondeurEntreprise: { id: this.membreId } as PharmacieEntreprise
    };
  }




  setPageSize(currentHeight?: number): void {
    const dynamicSize = getDynamicPageSize(currentHeight, 44);

    if (dynamicSize !== this.navigation.pageSize) {
      this.navigation.pageSize = dynamicSize;

      this.pageSizes.push(dynamicSize);
      this.pageSizes = this.pageSizes.sort((a, b) => a - b);

      currentHeight && this.searchOffres(false);
    }
  }

  OnPageChange(event: number): void {
    this.searchOffres(false);
  }

  initFilterForm(): void {
    this.filterForm = this.fb.group({
      numero: [null],
      offreur: [null],
      distributeur: [null],
      statut: [null],
      natureOffre: [null],
      nonExpireesUniquement: [null]
    });
  }

  listenToSearchFilterChanges(): void {
    this.searchFilter.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(200),
        distinctUntilChanged()
      )
      .subscribe((term: string) => {
        this.searchCriteria = new FsOffreCriteria({
          titre: term?.toLowerCase(),
        });

        if (!this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
          this.searchCriteria['nonExpireesUniquement'] = 'O';
          this.searchCriteria['statut'] = ['P'];
        } else if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) {
          this.searchCriteria = { ...this.searchCriteria, offreur: this.authService.getPrincipal()?.societe };
        }

        this.navigation.skip = 0, this.cardData = [], this.cardNavigation.skip = 0, this.searchOffres();
      });
  }

  listenToScrollPosition(): void {
    this.scrollService.reachedBottom$
      .pipe(
        takeUntil(this.unsubscribe$),
        filter((state) => !!state)
      )
      .subscribe((_state) => {
        if (this.hasMore) {
          if (
            this.cardNavigation.skip !==
            this.cardNavigation.skip + this.cardNavigation.pageSize
          ) {
            this.cardNavigation.skip += this.cardNavigation.pageSize;

            this.searchOffres(true);
          }
        }
      });
  }

  dupliquerOffre(item: Offre): void {
    this.userInputService
      .confirmAlt(
        'Confirmation',
        `Êtes vous sûr de vouloir dupliquer l'offre: <b>${item?.titre}</b> ?`
      )
      .then(
        () => {
          this.router.navigate([`/achats-groupes/offres/saisie/${item?.id}`], {
            queryParams: { cloner: true },
            replaceUrl: true,
          });
        },
        () => null
      );
  }

  searchOffres(isInfiniteScroll = undefined): void {
    this.fsOffreService
      .searchFsOffres(isInfiniteScroll ? this.cardNavigation : this.navigation, this.searchCriteria)
      .subscribe((res) => {

        if (isInfiniteScroll) { // ? Init data for card view
          this.initCardData(res);
        } else if (isInfiniteScroll === false) { // ? Init data for list view
          this.initListData(res);
        } else { // ? Init data for both list + card view
          this.initListData(res), this.initCardData(res);
        }
      });
  }

  initCardData(res: SearchOffre): void {
    this.hasMore = !res?.last;
    this.cardData = this.cardData?.concat(res?.content);
  }

  initListData(res: SearchOffre): void {
    res.content = res?.content?.map(item => {
      if (item?.distributeurs?.length > 1) {
        item['listeDistributeursString'] = this.listeDistributeurs(item?.distributeurs);
      }
      return item;
    });

    this.gridData = {
      data: res?.content,
      total: res?.totalElements,
    };

    this.exportPdfRef.setData(res.content);
  }

  cellClickHandler(event: CellClickEvent): void {
    if (event?.column?.title !== 'Actions') {
      this.consulterOffre(event?.dataItem, true);
    }
  }

  selectionChange(event: SelectionEvent): void {
    const selectedRows = event.selectedRows;
    const deselectedRows = event.deselectedRows;

    if (selectedRows?.length) {
      this.selectedItem = selectedRows[0]?.dataItem;
    }

    if (deselectedRows?.length && !selectedRows?.length) {
      this.selectedItem = null;
    }
  }

  saisirCommande(item: Offre): void {
    if (item?.etatCommandeAchatGroupe === 'ACCEPTEE') {
      this.fedSyndicatService.getMyGroupe().then((myGroupe) => {
        this.router.navigate(['achats-groupes/commandes/edit/cmd-unitaire'], {
          state: { incr: true },
          queryParams: {
            readOnly: false,
            groupeId: myGroupe?.id,
            offreId: item?.id,
          },
        });
      });
    }
  }

  consulterOffre(item: Offre, readOnly = false, enteteCommandeId?: number): void {
    this.router.navigate([`/achats-groupes/offres/${readOnly ? 'edit' : 'saisie'}`, item?.id], {
      state: { incr: true },
      queryParams: {
        readOnly,
        natureOffre: item?.natureOffre,
        etatCommandeAchatGroupe: item?.etatCommandeAchatGroupe,
        enteteCommandeId: item?.enteteCommandeId
      },
    });
  }

  saisirOffre(): void {
    this.router.navigate(['/achats-groupes/offres/saisie'], {
      queryParams: { readOnly: false },
    });
  }

  updatePageParams(): void {
    const mode = this.isListView ? 'liste' : 'cartes';

    if (this.isListView && !this.gridData?.total) this.searchOffres(false);
    if (!this.isListView && !this.cardData?.length) this.searchOffres(true);

    this.router.navigate([], { queryParams: { mode } });
  }

  sortChange(sort: SortDescriptor[]): void {
    this.offreSort = sort;

    if (this.offreSort && this.offreSort.length > 0 && this.offreSort[0].dir) {
      this.navigation.sortField = sort[0].field;
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
    }

    this.searchOffres(false);
  }

  pageChange(event: PageChangeEvent): void {
    if (
      event.skip !== this.navigation.skip ||
      event.take !== this.navigation.pageSize
    ) {
      this.navigation.skip = event.skip;
      this.navigation.pageSize = event.take;

      this.searchOffres(false);
    }
  }

  cloturerOffre(item: Offre): void {
    const action = item?.etatProposant === 'BROUILLON' ? 'Annuler' : 'Cloturer';

    if (
      item?.etatProposant !== 'CLOTURE' &&
      item?.etatProposant !== 'ANNULER' &&
      item?.etat !== 'cloturee'
    ) {
      this.userInputService
        .confirmAlt(
          'Confirmation',
          `Êtes vous sûr de vouloir ${action.toLowerCase()} l'offre: <b>${item?.titre}</b> ?`
        )
        .then(
          () => {
            iif(
              () => item.etatProposant === 'BROUILLON',
              this.offresService.annulerrOffreById(item?.id),
              this.offresService.clotureOffre(item?.id)
            ).subscribe((res) => {
              this.searchOffres(false);

              this.alertService.successAlt(
                `L'offre a été ${item?.etatProposant === 'BROUILLON' ? 'annulée' : 'cloturée'
                } avec succès.`,
                `Offre ${action === 'Annuler' ? 'Annulée' : 'Cloturée'}`,
                'MODAL'
              );
            });
          },
          () => null
        );
    }
  }

  buildExport(): void {
    this.exportPdfRef = this.exportPdfServ
      .ref<Offre>()
      .setTitle('Liste des Offres')
      .addColumn('numeroOffre', 'Numéro')
      .addColumn('titre', "Titre de l'offre")
      .addColumn('*', 'Offreur', {
        transform: (value) => {
          return (value?.offreur?.raisonSociale as string)?.toUpperCase();
        },
      })
      .addColumn('*', 'Distributeur', {
        transform: (value: Offre) => {
          return `${value?.distributeurs
            ?.map((dis) => dis?.raisonSociale)
            ?.join(' ')}`;
        },
      })
      .addColumn('datePublication', 'Date publication', {
        transform: (value) => {
          return this.datePipe.transform(value, 'dd/MM/yyyy') || '--/--/----';
        },
      })
      .addColumn('dateFin', 'Date Limite', {
        transform: (value) => {
          return this.datePipe.transform(value, 'dd/MM/yyyy') || '--/--/----';
        },
      })
      .addColumn('delaiLivraison', 'Délai de Livraison', {
        transform: (value: number) => {
          return value ? `${value} Jours` : '--';
        }
      })
      .addColumn('*', 'Statut', {
        transform: (offre: Offre) => {
          if (
            offre?.etatProposant !== 'ANNULER' &&
            (offre?.etatProposant === 'PUBLIER') && (offre?.etat !== 'cloturee')
          ) {
            return offre?.etatCommandeAchatGroupe !== 'ACCEPTEE' ? 'Publiée' : 'Ouverte';
          } else if (
            offre?.etatProposant !== 'ANNULER' &&
            (offre?.etatProposant === 'BROUILLON') && (offre?.etat !== 'cloturee')
          ) {
            return 'Brouillon';
          } else if (
            offre?.etatProposant !== 'ANNULER' &&
            (offre?.etatProposant === 'CLOTURE' || offre?.etat === 'cloturee')
          ) {
            return 'Cloturée';
          } else if (offre?.etatProposant === 'ANNULER') {
            return 'Annulée';
          } else {
            return offre?.etatProposant;
          }
        },
      })
      .setData([]);
  }

  filterList(searchQuery: string) {
    const criteria = {
      raisonSociale: searchQuery,
      typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE],
    };

    return this.offresService.searchSociete(criteria);
  }

  searchFournisseur = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap((term) => {
        if (term.length > 1) {
          return this.filterList(term.toLowerCase());
        }
        return of({ content: [] });
      }),
      map((res) => res?.content.slice(0, 5))
    );

  fournisseurFormatter = (result: { raisonSociale: any }) =>
    result ? result.raisonSociale : null;

  laboFormatter = (result: { raisonSociale: any }) =>
    result
      ? result.raisonSociale === 'DIVERS'
        ? null
        : result.raisonSociale
      : null;

  appliquerFiltre(): void {
    const payload = this.filterForm?.getRawValue();

    this.searchCriteria = new FsOffreCriteria({
      ...payload,
      statut: payload?.statut ? [payload?.statut] : null,
      offreur: (typeof payload?.offreur === 'object') ? payload?.offreur : null,
      distributeur: (typeof payload?.distributeur === 'object') ? payload?.distributeur : null,
    });

    if (!this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
      this.searchCriteria = { 
        ...this.searchCriteria, 
        nonExpireesUniquement: 'O', 
        groupeConcerne: !this.authService.hasAnyAuthority(['ROLE_NATIONAL']) ? this.monGroupe : null 
      };
    } else if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) {
      this.searchCriteria = { ...this.searchCriteria, offreur: this.authService.getPrincipal()?.societe };
    } else {
      !this.searchCriteria['nonExpireesUniquement'] && (delete this.searchCriteria['nonExpireesUniquement']);
    }

    this.navigation.skip = 0, this.cardData = [], this.cardNavigation.skip = 0, this.searchOffres();
  }

  viderFiltre(): void {
    this.filterForm.reset();
    this.searchCriteria = new FsOffreCriteria();

    if (!this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
      this.searchCriteria['nonExpireesUniquement'] = 'O';
      this.searchCriteria['statut'] = ['P'];
      !this.authService.hasAnyAuthority(['ROLE_NATIONAL']) && (this.searchCriteria['groupeConcerne'] = this.monGroupe);
    } else if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) {
      this.searchCriteria = { ...this.searchCriteria, offreur: this.authService.getPrincipal()?.societe };
    }

    this.navigation.skip = 0, this.cardData = [], this.cardNavigation.skip = 0, this.searchOffres();
  }

  openDistributeurs(fournisseurs: Fournisseur[], content) {
    this.selectedFournisseurs = fournisseurs;
    this.modalService
      .open(content, {
        ariaLabelledBy: 'modal-basic-title',
        windowClass: 'fs-cstm-modal',
      })
      .result.then(
        (result) => {
          console.log(`Closed with: ${result}`);
        },
        (reason) => {
          console.log(`Dismissed ${reason}`);
        }
      );
  }

  listeDistributeurs(distributeurs: Fournisseur[]) {
    return distributeurs?.map(dist => dist?.raisonSociale)?.join(', ');
  }

  reload() {
    this.cardData = [], this.cardNavigation.skip = 0, this.searchOffres();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
