import { CommandeDto } from "libs/commandes-web/commande/src/lib/models/CommandeDto";
import { EntrepriseDTO } from "libs/federation-syndicats/src/lib/models/entreprise.model";
import { StatsData } from "./stats.model";

export class TableauDeBordPartieRetard {
    nbrCommande?: number;
    commandesByFournisseur?: any;
    listDesEntete?: CommandeDto[]; // ? Commande DTO
}

export class TableauDeBordPartieStatistique {
    nbbrPh?: number;
    nmbrCmd?: number;
    valTotalCmd?: number;
    grossiste?: EntrepriseDTO;
    differencesEnvoi?: [number, number, number]; // ? MIN, MAX, MOY
    differencesReception?: [number, number, number]; // ? MIN, MAX, MOY
    differencesExpedition?: [number, number, number]; // ? MIN, MAX, MOY
}

export class TableauDeBordDto {
    partieRetard?: TableauDeBordPartieRetard;
    partieEtatFlux?: TableauPartieEtatFlux[];
    partieStatWinoffre?: StatsData;
    partieEtatConsoext?: TableauConsoExt[];
    partieStatistique?: TableauDeBordPartieStatistique[];
    listEtatsDemandeAcces?: TableauEtatsDemandesAccess[];
}

export class TableauPartieEtatFlux {
    typeFlux?: string;
    raisonSociale?: string;
    originFlux?: string;
    siteEmetteur?: number;
    dateReceptionFlux?: string;
}

export class TableauConsoExt {
    cmdNonTraiteEdi?: number;
    cmdNonTraiteSarphix?: number;
    codeSite?: number;
    etatCmdWeb?: boolean;
    fournisseur?: EntrepriseDTO;
    liaisonSarphix?: boolean;
    timeStamp?: string;
    timestampNonTraiteEdi?: string;
    timestampNonTraiteSarphix?: string;
}

type StatutCmd = 'B' | 'V' | 'A' | 'TR';

export class TableauDeBordCriteria {
    codeSite?: number;
    dateDebut?: string;
    dateFin?: string;
    dureeRetardNonEnvoyee?: number;
    dureeRetardSansBL?: number;
    status?: StatutCmd[];
}

export class TableauEtatsDemandesAccess {
    idGrossiste?: number;
    codeSite?: number;
    raisonSociale?: string;
    countDemandeTotal?: number;
    countDemandeRefusee?: number;
    countDemandeEnAttente?: number;
}