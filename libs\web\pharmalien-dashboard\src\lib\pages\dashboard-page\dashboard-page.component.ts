import { <PERSON><PERSON><PERSON>, Host<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TemplateRef, ViewChild } from "@angular/core";
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GridDataResult, RowClassArgs } from "@progress/kendo-angular-grid";
import * as moment from "moment";
import { Subject, takeUntil } from "rxjs";
import { PharmaLienDashboardService } from "../../services/pharmalien-dashboard.service";
import { CommandeDto } from "libs/commandes-web/commande/src/lib/models/CommandeDto";
import { TableauDeBordCriteria, TableauEtatsDemandesAccess, TableauPartieEtatFlux } from "@wph/data-access";
import { aggregateBy, AggregateResult, SortDescriptor, orderBy } from "@progress/kendo-data-query";
import { StatsData } from "libs/data-access/src/lib/models/stats.model";
import { DeferredActionButtonsService, ElapsedTimeOutOfRange, FluxRangePipe } from "@wph/web/shared";
import { ActivatedRoute, Router } from "@angular/router";

type CmdSwitch = 'NON-ENVOYEES' | 'SANS-BL';
type Flux = "PHARMALIEN" | "CONSO-EXT";
type DisplayedGrid = "CMDS-RETARD" | "STATS" | "ETATS-CONSOEXT" | "ETATS-FLUX" | "STATS-WINOFFRE" | "STATS-COMMANDEWEB" | "LISTE-CMDS" | "ETATS-DEMANDES-ACCES";

@Component({
    selector: 'wph-pharmalien-dashboard-page',
    templateUrl: './dashboard-page.component.html',
    styleUrls: ['./dashboard-page.component.scss']
})
export class DashboardPageComponent implements OnInit, OnDestroy {
    cmdsEnRetardData: GridDataResult = { data: [], total: 0 };
    dureeTraitementData: GridDataResult = { data: [], total: 0 };
    etatsDemandesAccesData: GridDataResult = { data: [], total: 0 };
    statistiquesWinoffreResponse: StatsData;
    statistiquesWinoffreData: GridDataResult = { data: [], total: 0 };
    etatsConsoExtData: GridDataResult = { data: [], total: 0 };
    etatsFluxData: GridDataResult = { data: [], total: 0 };
    unfilteredEtatsFluxData: TableauPartieEtatFlux[];
    unfilteredCmdsAvecRetard: CommandeDto[];

    statsCmdWebSort: SortDescriptor[];
    statsCmdWinOffreSort: SortDescriptor[];
    statsDemandesAccesSort: SortDescriptor[];
    cmdAvecRetardSort: SortDescriptor[];
    etatsConsoExtSort: SortDescriptor[];
    etatsFluxSort: SortDescriptor[] = [];
    listeCmdSort: SortDescriptor[];

    selectedStatsDemandesAcces: string;
    selectedStatsCmdWebFournisseur: string;
    selectedTypeFlux: string;
    statsCmdWebSelectFournissuer: { label: string; value: string }[] = [];
    statsSelectDemandeAcces: { label: string; value: string }[] = [];
    etatsFluxSelect2Data: { label: string; value: string }[] = [];

    originalOrder: Record<DisplayedGrid, GridDataResult> = {
        'CMDS-RETARD': null,
        'ETATS-CONSOEXT': null,
        'ETATS-FLUX': null,
        "LISTE-CMDS": null,
        'STATS': null,
        'STATS-COMMANDEWEB': null,
        'ETATS-DEMANDES-ACCES': null,
        'STATS-WINOFFRE': null
    };

    dureeTraitementAggregate: AggregateResult;
    etatsDemandesAccesAggregate: AggregateResult;

    filterCriteria: { dateDebut: string, dateFin: string };
    unsubscribe$: Subject<boolean> = new Subject<boolean>();

    cmdsSansBlCount: number;
    cmdsNonEnvoyeesCount: number;

    now = new Date();
    displayedDate: Date = new Date();
    datePicker: FormControl = new FormControl();

    switchTypeCommande: CmdSwitch = 'NON-ENVOYEES';
    activeFlux: Flux = "PHARMALIEN";
    selectedFournisseur: CommandeDto;
    selectedFournisseurCmds: GridDataResult;
    displayTitle: boolean;
    activeGridIndex: number = 1;
    activeGrid: DisplayedGrid = "STATS-COMMANDEWEB";
    dashboardFilterFormGroup: FormGroup;
    etatFlux2StateMap: Map<string, string> = new Map<string, string>();
    etatsFlux: string[] = [];

    cmdsAvecRetardMap: { [index: string]: CommandeDto[] };
    @ViewChild('dashboardFilter', { static: true }) dashboardFilter: TemplateRef<any>;

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private route: ActivatedRoute,
        private fluxRangePipe: FluxRangePipe,
        private modalService: NgbModal,
        private dashboardService: PharmaLienDashboardService,
        private elapsedTimeOutOfRangePipe: ElapsedTimeOutOfRange,
        private deferredActionBtnService: DeferredActionButtonsService,
    ) {
        this.dashboardFilterFormGroup = this.fb.group({
            dateDebut: [moment(this.now), Validators.required],
            dateFin: [moment(this.now), Validators.required],
        })
    }

    @HostListener('window:resize', ['$event']) onResize(event: any) {
        this.displayTitle = window.innerWidth < 1110;
    }

    ngOnInit(): void {
        // Extract date from query params if available
        this.initializePageStateFromQueryParams();

        this.pushMobileMenuOptions();
        this.listenToDatePickerChanges();
        this.displayTitle = window.innerWidth < 1110;
    }

    private initializePageStateFromQueryParams() {
        const queryParams = this.route.snapshot.queryParams;

        if (queryParams['tableau']) {
            this.activeGrid = queryParams['tableau'];
            this.activeGridIndex = this.activeGrid === 'STATS-COMMANDEWEB' ? 1 : 2;
        }

        if (queryParams['dateDebut'] && queryParams['dateFin']) {
            const dateDebut = new Date(queryParams['dateDebut']);
            const dateFin = new Date(queryParams['dateFin']);

            this.filterCriteria = {
                dateDebut: moment(dateDebut).startOf('day').format('yyyy-MM-DD HH:mm:ss').toString(),
                dateFin: moment(dateFin).endOf('day').format('yyyy-MM-DD HH:mm:ss').toString(),
            };

            this.dashboardFilterFormGroup.setValue(
                {
                    dateDebut: moment(dateDebut),
                    dateFin: moment(dateFin)
                }
            );

            this.applyDateFilterChanges(this.dashboardFilterFormGroup);
        }
    }

    openModal(content: TemplateRef<any>, selectedItem: any, size = 'xl'): void {
        this.selectedFournisseur = selectedItem;

        const cmdsOfSelectedType = this.getCommandesOfType(
            this.switchTypeCommande,
            this.cmdsAvecRetardMap[this.selectedFournisseur?.codeSite]
        );

        this.selectedFournisseurCmds = { data: cmdsOfSelectedType || [], total: cmdsOfSelectedType?.length || 0 };

        this.modalService.open(content, { size, centered: true, windowClass: 'fs-cstm-modal', modalDialogClass: 'dashboard-modal-container' });
    }

    openFilterModal(content: TemplateRef<any>, size = 'md'): void {
        this.modalService.open(content, { size, centered: true, windowClass: 'fs-cstm-modal' })
    }

    vider(): void {
        // Clear page query params
        this.router.navigate([], {
            queryParams: {
                dateDebut: null,
                dateFin: null,
            },
            queryParamsHandling: 'merge',
        });

        this.resetAllGridStates();
        this.dashboardFilterFormGroup.reset({ dateDebut: moment(this.now), dateFin: moment(this.now) });
    }

    resetAllGridStates(): void {
        this.cmdsEnRetardData = { data: [], total: 0 };
        this.dureeTraitementData = { data: [], total: 0 };
        this.etatsDemandesAccesData = { data: [], total: 0 };
        this.statistiquesWinoffreData = { data: [], total: 0 };
        this.etatsConsoExtData = { data: [], total: 0 };
        this.etatsFluxData = { data: [], total: 0 };

        // Clear grid aggregates
        this.dureeTraitementAggregate = null;
        this.etatsDemandesAccesAggregate = null;
        this.cmdsSansBlCount = 0;
        this.cmdsNonEnvoyeesCount = 0;
        this.selectedStatsCmdWebFournisseur = '';
        this.selectedStatsDemandesAcces = '';
        this.selectedTypeFlux = null;
        this.etatsFluxSelect2Data = [];
        this.selectedFournisseur = null;
        this.selectedFournisseurCmds = { data: [], total: 0 };

        // Reset unfiltered data
        this.unfilteredEtatsFluxData = [];
        this.unfilteredCmdsAvecRetard = [];
    }

    resetSortDescriptors(activeTabIndex: number): void {
        if (activeTabIndex === 1) {
            this.statsCmdWebSort = [];
            this.statsCmdWinOffreSort = [];
            this.statsDemandesAccesSort = [];
        } else if (activeTabIndex === 2) {
            this.cmdAvecRetardSort = [];
            this.etatsConsoExtSort = [];
            this.etatsFluxSort = [];
            this.listeCmdSort = [];
        }
    }

    groupAndMergeCommandes(commandes: CommandeDto[]): CommandeDto[] {
        const groupedMap = new Map<number, CommandeDto>();

        if (commandes?.length) {
            commandes.forEach(commande => {
                const key = commande?.codeSite;

                if (!groupedMap.has(key)) {
                    // Clone the initial command without mutating the original
                    groupedMap.set(key, { ...commande, lignes: [...commande.lignes], nbrCmds: 1 });
                } else {
                    const existingCommande = groupedMap.get(key);
                    if (existingCommande) {
                        existingCommande.nbrCmds += 1;
                        // Concatenate the 'lignes' array
                        existingCommande.lignes.push(...commande.lignes);
                    }
                }
            });

            return Array.from(groupedMap.values());
        }

        return null;
    }

    activeTabChange(index: number) {
        this.activeGridIndex = index;
        this.activeGrid = (index === 1) ? "STATS-COMMANDEWEB" : "CMDS-RETARD";

        this.router.navigate([], {
            queryParams: { tableau: this.activeGrid },
            queryParamsHandling: 'merge',
        });
    }

    groupCommandesByCodeSite(commandes: CommandeDto[]) {
        if (commandes && commandes?.length) {
            const groupedMap: { [index: string]: CommandeDto[] } = {};

            commandes.forEach(commande => {
                const key = commande.codeSite;

                if (!Object.keys(groupedMap).includes(key?.toString())) {
                    groupedMap[key] = [];
                }

                groupedMap[key]?.push(commande);
            });

            return groupedMap;
        }

        return null;
    }

    toggleCmdType(type: CmdSwitch): void {
        this.switchTypeCommande = type;
    }

    toggleFlux(flux: string): void {
        this.selectedTypeFlux = flux;
    }

    listenToDatePickerChanges(): void {
        this.datePicker.valueChanges
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe((res) => {
                const selectedDate = new Date(res);

                if (
                    selectedDate?.getDate() <= this.now?.getDate() ||
                    selectedDate?.getFullYear() !== this.now?.getFullYear() ||
                    selectedDate?.getMonth() !== this.now?.getMonth()
                ) {
                    this.displayedDate = selectedDate;
                    this.applyDateFilterChanges();
                }
            });
    }

    forwardOneDay(): void {
        const nextDate = new Date(this.displayedDate);
        nextDate.setDate(this.displayedDate.getDate() + 1);

        if (
            nextDate.getDate() <= this.now?.getDate() ||
            nextDate.getFullYear() !== this.now?.getFullYear() ||
            nextDate.getMonth() !== this.now?.getMonth()
        ) {
            this.displayedDate = nextDate;
            this.datePicker.setValue(this.getMomentDate(this.displayedDate));
        }
    }

    backwardOneDay(): void {
        const prevDate = new Date(this.displayedDate);
        prevDate.setDate(this.displayedDate.getDate() - 1);
        this.displayedDate = prevDate;
        this.datePicker.setValue(this.getMomentDate(this.displayedDate));
    }

    getMomentDate(newDate: Date) {
        return moment(newDate.getFullYear() + '-' + (newDate.getMonth() + 1) + '-' + newDate.getDate(), 'YYYY-MM-DD');
    }

    updateDisplayedEtatsFlux(typeFlux: Flux): void {
        const filteredEtatsFlux = this.unfilteredEtatsFluxData?.filter(flux => flux?.typeFlux === typeFlux);

        this.etatsFluxData = { data: filteredEtatsFlux || [], total: filteredEtatsFlux?.length || 0 };
    }

    updateDisplayedCmdsAvecRetard(selectedType: CmdSwitch): void {
        const filteredData = this.groupAndMergeCommandes(
            this.unfilteredCmdsAvecRetard?.filter(item =>
                (!item?.dateTraitementBl && item?.dateEnvoiToFournisseur && selectedType === 'SANS-BL') ||
                (!item?.dateEnvoiToFournisseur && selectedType === 'NON-ENVOYEES')
            )
        );

        this.cmdsEnRetardData = { data: filteredData || [], total: filteredData?.length || 0 };
    }

    updateActiveGrid(selectedGrid: DisplayedGrid): void {
        this.activeGrid = selectedGrid;
    }

    getCommandeTypeCount(selectedType: CmdSwitch): number {
        return this.unfilteredCmdsAvecRetard?.filter(item =>
            (!item?.dateTraitementBl && item?.dateEnvoiToFournisseur && selectedType === 'SANS-BL') ||
            (!item?.dateEnvoiToFournisseur && selectedType === 'NON-ENVOYEES')
        )?.length;
    }

    getCommandesOfType(selectedType: CmdSwitch, unfilteredCmdsAvecRetard: CommandeDto[]): CommandeDto[] {
        return unfilteredCmdsAvecRetard?.filter(item =>
            (!item?.dateTraitementBl && item?.dateEnvoiToFournisseur && selectedType === 'SANS-BL') ||
            (!item?.dateEnvoiToFournisseur && selectedType === 'NON-ENVOYEES')
        );
    }

    today() {
        this.dashboardFilterFormGroup.setValue({
            dateDebut: moment(this.now),
            dateFin: moment(this.now)
        });

        this.applyDateFilterChanges(this.dashboardFilterFormGroup);
    }

    yesterday() {
        this.dashboardFilterFormGroup.setValue({
            dateFin: moment(this.now).subtract(1, 'day'),
            dateDebut: moment(this.now).subtract(1, 'day'),
        });

        this.applyDateFilterChanges(this.dashboardFilterFormGroup);
    }

    lastWeek() {
        const startOfLastWeek = moment().subtract(1, 'week').startOf('week');
        const endOfLastWeek = moment().subtract(1, 'week').endOf('week');

        this.dashboardFilterFormGroup.setValue({
            dateFin: endOfLastWeek,
            dateDebut: startOfLastWeek
        });

        this.applyDateFilterChanges(this.dashboardFilterFormGroup);
    }


    thisWeek() {
        this.dashboardFilterFormGroup.setValue({
            dateFin: moment(),
            dateDebut: moment().startOf('week')
        });

        this.applyDateFilterChanges(this.dashboardFilterFormGroup);
    }


    applyDateFilterChanges(filterPayload?: FormGroup): void {
        this.selectedTypeFlux = null;
        const dateFin: moment.Moment = filterPayload?.get('dateFin')?.value || moment(this.displayedDate);
        const dateDebut: moment.Moment = filterPayload?.get('dateDebut')?.value || moment(this.displayedDate);

        this.filterCriteria = {
            dateFin: dateFin.endOf('day').format('yyyy-MM-DD HH:mm:ss').toString(),
            dateDebut: dateDebut.startOf('day').format('yyyy-MM-DD HH:mm:ss').toString(),
        };

        // set page query params based on filter criteria
        this.router.navigate([], {
            queryParams: {
                dateDebut: this.filterCriteria.dateDebut,
                dateFin: this.filterCriteria.dateFin,
            },
            queryParamsHandling: 'merge',
        });

        const criteria: TableauDeBordCriteria = {
            status: ['TR', 'V'],
            dureeRetardSansBL: 180,
            dureeRetardNonEnvoyee: 180,
            dateDebut: this.filterCriteria?.dateDebut,
            dateFin: this.filterCriteria?.dateFin,
        };

        if (this.activeGridIndex === 1) {
            // ? RESET SORT DESCRIPTORS
            this.resetSortDescriptors(this.activeGridIndex);

            // ? FETCH DATA FOR TABLEAU DE BORD BUSINESS
            this.dashboardService.getDashboardContentBusiness(criteria).subscribe(res => {
                // ? INITIALIZE STATISTIQUES COMMANDE_WEB DATA
                this.dureeTraitementData = { data: res?.partieStatistique || [], total: res?.partieStatistique?.length || 0 };
                this.dureeTraitementAggregate = this.generateAggregate();

                if (this.dureeTraitementData.total) {
                    this.statsCmdWebSelectFournissuer = [];

                    const uniqueRaisonSociale = [...new Set(this.dureeTraitementData.data.map(item => item?.grossiste?.raisonSociale))];
                    this.statsCmdWebSelectFournissuer.push({ label: 'TOUS', value: null });

                    uniqueRaisonSociale?.map(item => {
                        this.statsCmdWebSelectFournissuer.push({ label: item, value: item });
                    });
                } else {
                    this.statsCmdWebSelectFournissuer = [];
                    this.selectedStatsCmdWebFournisseur = '';
                }

                // ? INITIALIZE STATISTIQUES ETATS DEMANDES D'ACCES DATA
                this.etatsDemandesAccesData = { data: res?.listEtatsDemandeAcces || [], total: res?.listEtatsDemandeAcces?.length || 0 };
                this.etatsDemandesAccesAggregate = this.aggregateEtatsDemandesAcces();

                if (this.etatsDemandesAccesData?.total) {
                    this.statsSelectDemandeAcces = [];

                    const uniqueRaisonSociale = [...new Set(this.etatsDemandesAccesData.data.map(item => item?.raisonSociale))];
                    this.statsSelectDemandeAcces.push({ label: 'TOUS', value: null });

                    uniqueRaisonSociale?.map(item => {
                        this.statsSelectDemandeAcces.push({ label: item, value: item });
                    });
                } else {
                    this.statsSelectDemandeAcces = [];
                    this.selectedStatsDemandesAcces = '';
                }

                // ? INITIALIZE STATISTIQUES WIN_OFFRE DATA
                this.statistiquesWinoffreResponse = res?.partieStatWinoffre;
                this.statistiquesWinoffreData = { data: res?.partieStatWinoffre?.offreDtos || [], total: res?.partieStatWinoffre?.offreDtos?.length || 0 };

                this.statistiquesWinoffreResponse.nmbrPh = this.aggregateNbrPh();
            });
        } else if (this.activeGridIndex === 2) {
            // ? RESET SORT DESCRIPTORS
            this.resetSortDescriptors(this.activeGridIndex);
            
            // ? FETCH DATA FOR TABLEAU DE BORD TECHNIQUE
            this.dashboardService.getDashboardContentTechnique(criteria).subscribe(res => {
                // ? INITIALIZE STATISTIQUES COMMANDES AVEC RETARD DATA
                this.cmdsEnRetardData = {
                    data: res?.partieRetard?.listDesEntete || [],
                    total: res?.partieRetard?.listDesEntete?.length || 0
                };

                this.unfilteredCmdsAvecRetard = this.cmdsEnRetardData?.data;
                this.updateDisplayedCmdsAvecRetard(this.switchTypeCommande);

                this.cmdsAvecRetardMap = this.groupCommandesByCodeSite(res?.partieRetard?.listDesEntete);

                this.cmdsSansBlCount = this.getCommandeTypeCount('SANS-BL');
                this.cmdsNonEnvoyeesCount = this.getCommandeTypeCount('NON-ENVOYEES');

                // ? INITIALIZE ETATS DES FLUX DATA
                this.etatsFluxData = { data: res?.partieEtatFlux || [], total: res?.partieEtatFlux?.length || 0 };
                this.unfilteredEtatsFluxData = this.etatsFluxData?.data;

                // Clear the map to ensure fresh statuses for the current dataset
                this.etatFlux2StateMap.clear();

                // Group items by typeFlux manually to preserve key types (e.g., null, undefined)
                const groupedByFluxType = new Map<any, TableauPartieEtatFlux[]>();
                for (const item of this.etatsFluxData.data) {
                    const key = item?.typeFlux;
                    if (!groupedByFluxType.has(key)) {
                        groupedByFluxType.set(key, []);
                    }
                    groupedByFluxType.get(key)?.push(item);
                }

                const uniqueEtatsFlux = Array.from(groupedByFluxType.keys());

                uniqueEtatsFlux.forEach(typeFlux => { // typeFlux is the actual key from data (can be null, undefined, string, etc.)
                    const itemsForCurrentFlux = groupedByFluxType.get(typeFlux);
                    let status = 'success';

                    if (itemsForCurrentFlux && itemsForCurrentFlux.length > 0) {
                        const warningLevel = itemsForCurrentFlux.some(itemInGroup =>
                            this.elapsedTimeOutOfRangePipe.transform(this.fluxRangePipe.transform(typeFlux), itemInGroup?.dateReceptionFlux)
                        );

                        const dangerLevel = itemsForCurrentFlux.some(itemInGroup =>
                            this.elapsedTimeOutOfRangePipe.transform(this.fluxRangePipe.transform(typeFlux, true), itemInGroup?.dateReceptionFlux)
                        );

                        if (dangerLevel) {
                            status = 'danger';
                        } else if (warningLevel) {
                            status = 'warning';
                        }
                    }
                    this.etatFlux2StateMap.set(typeFlux, status);
                });

                this.etatsFlux = Array.from(this.etatFlux2StateMap.keys());

                this.etatsFluxSortChange([{ field: 'dateReceptionFlux', dir: 'asc' }]);

                // ? INITIALIZE ETATS CONSO-EXT DATA
                this.etatsConsoExtData = {
                    data: res?.partieEtatConsoext || [],
                    total: res?.partieEtatConsoext?.length || 0
                };
            });
        }
    }

    generateAggregate() {
        return aggregateBy(
            this.dureeTraitementData.data,
            [
                { field: 'nmbrCmd', aggregate: 'sum' },
                { field: 'nbbrPh', aggregate: 'sum' },
                { field: 'valTotalCmd', aggregate: 'sum' },
            ]
        );
    }

    aggregateEtatsDemandesAcces() {
        return aggregateBy(
            this.etatsDemandesAccesData.data,
            [
                { field: 'countDemandeEnAttente', aggregate: 'sum' },
                { field: 'countDemandeRefusee', aggregate: 'sum' },
                { field: 'countDemandeTotal', aggregate: 'sum' },
            ]
        );
    }

    aggregateNbrPh() {
        return (this.statistiquesWinoffreData.total) ? aggregateBy(this.statistiquesWinoffreData.data, [{ field: 'nmbrPh', aggregate: 'sum' }])['nmbrPh']?.sum : 0;
    }

    aggregateCmdNonTraite() {
        return (this.etatsConsoExtData.total) ? aggregateBy(this.etatsConsoExtData.data, [{ field: 'cmdNonTraiteEdi', aggregate: 'sum' }, { field: 'cmdNonTraiteSarphix', aggregate: 'sum' }]) : 0
    }

    statsCmdWebSortChange(sort: SortDescriptor[]) {
        this.statsCmdWebSort = sort;
        const sortConfig = this.statsCmdWebSort?.[0];

        if (sortConfig?.dir) {
            const { field, dir } = sortConfig;

            if (field === 'differencesEnvoi' || field === 'differencesReception' || field === 'differencesExpedition') {
                sortConfig.compare = (a: any, b: any) => {
                    const aValue = a[field][1];
                    const bValue = b[field][1];
                    return dir === 'asc' ? aValue - bValue : bValue - aValue;
                };
            }

            this.dureeTraitementData.data = orderBy(this.dureeTraitementData.data, this.statsCmdWebSort);
        }
    }

    statsWinoffreSortChange(sort: SortDescriptor[]) {
        this.statsCmdWinOffreSort = sort;

        if (this.statsCmdWinOffreSort && this.statsCmdWinOffreSort?.length && this.statsCmdWinOffreSort[0].dir) {
            this.statistiquesWinoffreData.data = orderBy(this.statistiquesWinoffreData.data, this.statsCmdWinOffreSort)
        }
    }

    statsDemandeAccesSortChange(sort: SortDescriptor[]) {
        this.statsDemandesAccesSort = sort;
        if (this.statsDemandesAccesSort && this.statsDemandesAccesSort?.length && this.statsDemandesAccesSort[0].dir) {
            this.etatsDemandesAccesData.data = orderBy(this.etatsDemandesAccesData.data, this.statsDemandesAccesSort);
        }
    }

    etatsConsoExtSortChange(sort: SortDescriptor[]) {
        this.etatsConsoExtSort = sort;

        if (this.etatsConsoExtSort && this.etatsConsoExtSort?.length && this.etatsConsoExtSort[0].dir) {
            this.etatsConsoExtData.data = orderBy(this.etatsConsoExtData.data, this.etatsConsoExtSort)
        }
    }

    cmdAvecRetardSortChange(sort: SortDescriptor[]) {
        this.cmdAvecRetardSort = sort;

        if (this.cmdAvecRetardSort && this.cmdAvecRetardSort?.length && this.cmdAvecRetardSort[0].dir) {
            this.cmdsEnRetardData.data = orderBy(this.cmdsEnRetardData.data, this.cmdAvecRetardSort)
        }
    }

    etatsFluxSortChange(sort: SortDescriptor[]) {
        this.etatsFluxSort = sort;

        if (this.etatsFluxSort && this.etatsFluxSort?.length && this.etatsFluxSort[0].dir) {
            this.etatsFluxData.data = orderBy(this.etatsFluxData.data, this.etatsFluxSort)
        }
    }

    listeCmdSortChange(sort: SortDescriptor[]) {
        this.listeCmdSort = sort;

        if (this.listeCmdSort && this.listeCmdSort?.length && this.listeCmdSort[0].dir) {
            this.selectedFournisseurCmds.data = orderBy(this.selectedFournisseurCmds.data, this.listeCmdSort)
        }
    }

    rowClassStatsCmdWeb = (context: RowClassArgs) => {
        if (this.selectedStatsCmdWebFournisseur?.length) {
            return context?.dataItem?.grossiste?.raisonSociale !== this.selectedStatsCmdWebFournisseur ? 'hidden-row' : '';
        }
        return '';
    };

    rowClassEtatsFlux = (context: RowClassArgs) => {
        if (this.selectedTypeFlux?.length) {
            return context?.dataItem?.typeFlux !== this.selectedTypeFlux ? 'hidden-row' : '';
        }
        return '';
    };

    rowClassEtatsDemandesAcces = (context: RowClassArgs) => {
        if (this.selectedStatsDemandesAcces?.length) {
            return context?.dataItem?.raisonSociale !== this.selectedStatsDemandesAcces ? 'hidden-row' : '';
        }
        return '';
    };

    navigateToDemandesAcces(item: Partial<TableauEtatsDemandesAccess>): void {
        if (item) {
            this.router.navigate(
                ['/pharma-lien/gestion-demandes-acces/demandes/liste'],
                {
                    queryParams: {
                        codeSite: item?.codeSite,
                        fournisseurId: item?.idGrossiste,
                    }
                });
        }
    }

    private pushMobileMenuOptions(): void {
        this.deferredActionBtnService.pushPageOptions([
            {
                label: 'Jour',
                shouldShow: true,
                action: () => this.today(),
                iconClass: 'mdi mdi-calendar',
            },
            {
                label: 'Jour - 1',
                shouldShow: true,
                action: () => this.yesterday(),
                iconClass: 'mdi mdi-calendar',
            },
            {
                label: 'Semaine',
                shouldShow: true,
                action: () => this.thisWeek(),
                iconClass: 'mdi mdi-calendar',
            },
            {
                label: 'Semaine - 1',
                shouldShow: true,
                action: () => this.lastWeek(),
                iconClass: 'mdi mdi-calendar',
            },
            {
                label: 'Filtrer',
                shouldShow: true,
                action: () => this.openFilterModal(this.dashboardFilter),
                iconClass: 'mdi mdi-filter',
            }
        ]);
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
        this.deferredActionBtnService.mobileMenuOptions$.next([]);
    }
}