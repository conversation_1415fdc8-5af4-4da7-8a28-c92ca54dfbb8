import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BlocOffreComponent } from "./bloc-offre/bloc-offre.component";
import { NgbAccordionModule, NgbModule, NgbPopoverModule } from "@ng-bootstrap/ng-bootstrap";
import { GridModule } from "@progress/kendo-angular-grid";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { WebSharedModule } from "@wph/web/shared";
import { CommandesFilterModalComponent } from './commandes-filter-modal/commandes-filter-modal.component';
import { AssistantCommandeComponent } from './assistant-commande/assistant-commande.component';
import { NgApexchartsModule } from 'ng-apexcharts';

@NgModule({
  declarations: [
    BlocOffreComponent,
    CommandesFilterModalComponent,
    AssistantCommandeComponent,
  ],
  imports: [
    CommonModule,
    NgbAccordionModule,
    NgbPopoverModule,
    NgbModule, GridModule,
    ReactiveFormsModule,
    WebSharedModule,
    FormsModule,
    NgApexchartsModule
  ],
  exports: [
    BlocOffreComponent,
    AssistantCommandeComponent,
    CommandesFilterModalComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class WebCommandesComponentsModule { }
