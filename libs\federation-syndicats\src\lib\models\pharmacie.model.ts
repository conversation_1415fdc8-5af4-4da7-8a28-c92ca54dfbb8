import { <PERSON>raire } from "./pharmacie-horraire.model";
import { Service } from "./pharmacie-service.model";

export interface IGerant {
    firstname: string;
    lastname: string;
}

export interface IPharmacie {

    idhash?: number;
    adresse?: string;
    adresse2?: string;
    adresse3?: string;
    adresseAr1?: string;
    adresseAr2?: string;
    adresseAr3?: string;
    localite?: string;
    localiteId?: number;
    clientCible? : IPharmacie

    commentaire?: string;
    fax?: string;
    gsm1?: string;
    whatsapp?: string
    latitude?: number;
    longitude?: number;
    siteweb?: string;
    raisonSociale?: string;
    raisonSocialeAr?: string;
    nomResponsable?: string;
    gerant?: IGerant;
    telephone?: string;
    email?: string;
    ville?: string;
    villeRc?: string;
    statut?: string;
    statutJuridique?: string;
    horaires?: Horaire[];
    services?: Service[];
    listeToutesGardes?: any[];
}



export class Pharmacie implements IPharmacie {
    constructor(public idhash?: number, public adresse?: string, public adresse2?: string, public adresse3?: string, public adresseAr1?: string, public adresseAr2?: string, public adresseAr3?: string, public localite?: string, public telephone?: string,public email?:string ,public ville?: string, public villeRc?: string, public statutJuridique?: string
        , public localiteId?: number, public commentaire?: string, public fax?: string, public whatsapp?: string, public gsm1?: string, public latitude?: number, public longitude?: number, public siteweb?: string, public raisonSociale?: string, public raisonSocialeAr?: string, public nomResponsable?: string
        , public horaires?: Horaire[], public clientCible?:IPharmacie,public services?: Service[], public listeToutesGardes?: any[], public statut?: string, public gerant?: IGerant
    ) { }

}
