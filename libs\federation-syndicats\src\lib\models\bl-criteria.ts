import { TypeCommandeAchatGroupe } from "./entete-commande.model";
import { EntrepriseDTO } from "./entreprise.model";


enum EtatBlAchatGroupeEnum {
    BROUILLON = 'BROUILLON',
    VALIDE = 'VALIDE',
    ANNULE = 'ANNULE',
    CLOTURE = 'CLOTURE',
    REPARTI = 'REPARTI',
}

export interface IBLCriteria {
  codeCommande: string;
  entCmdAchatGroupeId: number;
  enteteBlConsolideeMarche: number;
  fournisseur: EntrepriseDTO;
  groupeEntrepriseId: number;
  supporteurCmdConsolideeId: number;
  id: number;
  numeroBl: string;
  responsableGroupeId: number;
  typeCommande: TypeCommandeAchatGroupe;
  etatBlAchatGroupe: EtatBlAchatGroupeEnum[];
  dateReceptionDebut: string;
  dateReceptionFin: string;
  clientCmdId : number;
 }



export class BLCriteria implements IBLCriteria {
  id: number;
  numeroBl: string;
  codeCommande: string;
  fournisseur: EntrepriseDTO;
  entCmdAchatGroupeId: number;
  responsableGroupeId: number;
  groupeEntrepriseId: number;
  typeCommande: TypeCommandeAchatGroupe;
  dateReceptionDebut: string;
  dateReceptionFin: string;
  enteteBlConsolideeMarche: number;
  supporteurCmdConsolideeId: number;
  etatBlAchatGroupe: EtatBlAchatGroupeEnum[];
  clientCmdId : number;

  constructor(partialCommandeCriteria: Partial<IBLCriteria>) {
    this.id = partialCommandeCriteria.id;
    this.numeroBl = partialCommandeCriteria.numeroBl;
    this.codeCommande = partialCommandeCriteria.codeCommande;
    this.fournisseur = partialCommandeCriteria.fournisseur;
    this.entCmdAchatGroupeId = partialCommandeCriteria.entCmdAchatGroupeId;
    this.responsableGroupeId = partialCommandeCriteria.responsableGroupeId;
    this.groupeEntrepriseId = partialCommandeCriteria.groupeEntrepriseId;
    this.typeCommande = partialCommandeCriteria.typeCommande;
    this.enteteBlConsolideeMarche = partialCommandeCriteria.enteteBlConsolideeMarche;
    this.dateReceptionDebut = partialCommandeCriteria.dateReceptionDebut;
    this.dateReceptionFin = partialCommandeCriteria.dateReceptionFin;
    this.supporteurCmdConsolideeId = partialCommandeCriteria.supporteurCmdConsolideeId;
    this.etatBlAchatGroupe = partialCommandeCriteria.etatBlAchatGroupe;
    this.clientCmdId = partialCommandeCriteria.clientCmdId;
  }
}
