

import { AdminBatchRoutingModule } from './admin-batch-routing.module';
import { ExecutionBatchComponent } from './execution-batch/execution-batch.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GridModule } from '@progress/kendo-angular-grid';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';


import {
  NgbModalModule,
  NgbNavModule,
  NgbDatepickerModule,
  NgbAccordionModule,
  NgbCollapseModule,
  NgbPopoverModule,
  NgbDropdownModule,
} from "@ng-bootstrap/ng-bootstrap";
import { HotkeyModule } from "angular2-hotkeys";
import { Select2Module } from "ng-select2-component";
import { NgxMaskModule } from "ngx-mask";


import { DragulaModule } from 'ng2-dragula';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { ConfigAdminComponent } from './config-admin/config-admin.component';
import { WebSharedModule } from '../shared/src';

@NgModule({
  declarations: [
    ExecutionBatchComponent,
    ConfigAdminComponent
  ],
  imports: [
    CommonModule,
    GridModule,
    ReactiveFormsModule,
    NgbTypeaheadModule,
    FormsModule,
    NgbTypeaheadModule,
    Select2Module,
    NgbModalModule,
    NgbNavModule,
    NgbPopoverModule,
    NgbDatepickerModule,
    NgbAccordionModule,
    NgbCollapseModule,
    NgxMaskModule.forChild(),
    GridModule,
    NgbDropdownModule,
    HotkeyModule,
    DragulaModule,
    InputsModule,
    WebSharedModule,
    AdminBatchRoutingModule

  ],
})
export class AdminBatchModule { }
