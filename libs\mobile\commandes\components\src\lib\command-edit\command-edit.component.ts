import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from "@angular/core";
import { ModalController, NavController } from "@ionic/angular";
import { BlocOffre, Fournisseur, Offre, OffresService } from "@wph/data-access";
import { FormBuilder, FormGroup } from "@angular/forms";
import { AuthService } from "@wph/core/auth";
import { ClientFournisseur } from "@wph/shared";

@Component({
  selector: "wph-command-edit",
  templateUrl: "./command-edit.component.html",
  styleUrls: ["./command-edit.component.scss"],
  encapsulation: ViewEncapsulation.None
})
export class CommandEditComponent implements OnInit {

  @Input() loading: boolean;
  @Input() item: any;

  @Output() changedValue: EventEmitter<any> = new EventEmitter<any>(null);

  @Output() openClientModal: EventEmitter<{ open: boolean; isClientLocal: boolean }> = new EventEmitter<{ open: boolean; isClientLocal: boolean }>();


  modelData: any;
  societe: Fournisseur;
  _selectedClient: Fournisseur;
  _selectedClientLocal: ClientFournisseur | string;
  _hasAccess: boolean;
  isSelectedClientLocalObject: boolean | null = null;
  isClientLocalAutoFilled: boolean = false;

  /**
   * Start of trouble
   */
  paliersData: any;

  constructor(
    private formBuilder: FormBuilder,
    private offresService: OffresService,
    private modalController: ModalController,
    private navController: NavController,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    this.societe = this.authService.getPrincipal().societe;
  }

  @Input('hasAccess')
  set hasAccess(value: boolean) {
    this._hasAccess = value;
  }

  get hasAccess() {
    return this._hasAccess;
  }

  @Input("offre")
  set _offre(offre: Offre) {
    if (offre !== this.offre) {
      this.resultExisteFilsBlocsProduits = {};
      this.filsProduits = {};
      this.filsNonProduits = {};
    }

    this.offre = offre;
    this.setErrorConditions();
  }

  @Input('client')
  set selectedClient(value: Fournisseur) {
    if (value && !this._selectedClient) {
      this._selectedClient = value;
    } else if (value && this._selectedClient) {
      this._selectedClient = (value?.id === this._selectedClient?.id) ? this._selectedClient : value;
    }
  }

  get selectedClient() {
    return this._selectedClient;
  }

  @Input('clientLocal')
  set selectedClientLocal(value: ClientFournisseur | string) {
    if (value) {
      this._selectedClientLocal = value;
      this.isSelectedClientLocalObject = (value instanceof Object);
    }
  }

  get selectedClientLocal() {
    return this._selectedClientLocal;
  }

  @Input("readOnly")
  set _readOnly(readOnly: boolean) {
    this.readOnly = readOnly;
  }


  @Input("listeBlocsOffreProduits")
  set _listeBlocsOffreProduits(blocsProduits: BlocOffre[]) {
    this.listeBlocsOffreProduits = blocsProduits;

    if (this.listeBlocsOffreProduits && this.listeBlocsOffreProduits.length > 0) {
      this.parentOfProduits = this.listeBlocsOffreProduits[0].parent;
    }

    let currentbloc = this.parentOfProduits;
    while (currentbloc) {
      if (currentbloc.listePaliers && currentbloc.listePaliers.length > 0) {
        for (const iterator of currentbloc.listePaliers) {
          if (iterator.ratioUg) {
            this.EnableSaisiUG = "p";

          }
          break;
        }

      }
      currentbloc = currentbloc.parent;
    }
  }


  offre: Offre;

  @Input()
  blocOffre: BlocOffre;


  /********** Partie Liste Porduit **********/
  listeBlocsOffreProduits: BlocOffre[];
  EnableSaisiUG = "f";
  parentOfProduits: BlocOffre;
  readOnly: boolean;

  public formGroup: FormGroup;
  validCommande = true;
  private resultExisteFilsBlocsProduits = {};


  private filsProduits = {};


  private filsNonProduits = {};


  existeFilsBlocsProduits(currentBloc: BlocOffre = null) {

    if (!currentBloc) {
      currentBloc = this.blocOffre;
    }

    if (this.resultExisteFilsBlocsProduits[currentBloc.id] != null) {
      return this.resultExisteFilsBlocsProduits[currentBloc.id];
    }


    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc === "F") {
          this.resultExisteFilsBlocsProduits[currentBloc.id] = true;
          return true;
        }
      }
      this.resultExisteFilsBlocsProduits[currentBloc.id] = false;
      return false;
    } else {
      this.resultExisteFilsBlocsProduits[currentBloc.id] = false;
      return false;
    }
  }

  getListeFilsBlocsProduits(currentBloc: BlocOffre = null) {

    if (!currentBloc) {
      currentBloc = this.blocOffre;
    }

    if (this.filsProduits[currentBloc.id]) {
      return this.filsProduits[currentBloc.id];
    }


    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      this.filsProduits[currentBloc.id] = [];
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc === "F") {
          this.filsProduits[currentBloc.id].push(bloc);
        }
      }

    } else {
      this.filsProduits[currentBloc.id] = [];
    }

    return this.filsProduits[currentBloc.id];
  }

  getListeFilsBlocsNonProduits(currentBloc: BlocOffre = null) {

    if (!currentBloc) {
      currentBloc = this.blocOffre;
    }

    if (this.filsNonProduits[currentBloc.id]) {
      return this.filsNonProduits[currentBloc.id];
    }


    if (currentBloc && currentBloc.listeFils && currentBloc.listeFils.length) {
      this.filsNonProduits[currentBloc.id] = [];
      for (const bloc of currentBloc.listeFils) {
        if (bloc.typeBloc !== "F") {
          this.filsNonProduits[currentBloc.id].push(bloc);
        }
      }

    } else {
      this.filsNonProduits[currentBloc.id] = [];
    }

    return this.filsNonProduits[currentBloc.id];
  }

  fireEvent(e: Event) {
    e.stopPropagation();
    e.preventDefault();
    return false;
  }


  public cellClickHandler({ sender, rowIndex, columnIndex, dataItem, isEdited }) {

    if (columnIndex !== 5 && columnIndex !== 4 && columnIndex !== 7 && columnIndex !== 8) {
      return;
    }

    if (!isEdited) {
      sender.editCell(rowIndex, columnIndex, this.createFormGroup(dataItem));
    }
  }

  public cellCloseHandler(value: any, dataItem: any) {
    this.changedValue.emit(null);

    dataItem.qteCmd = value || value === 0 ? +value : null;
    dataItem.qteUgSaisie = dataItem.qteUgSaisie ? +dataItem.qteUgSaisie : null;
    dataItem.totalQteUg = dataItem.qteUgSaisie;
    this.offresService.refreshEtatBlocOffre(dataItem);

    this.offresService.subject.next(null);
    this.checkifvalid(dataItem);
    this.offresService.palierOffreTraitement(this.offresService.getOffreRacine(dataItem));
  }


  public createFormGroup(dataItem: any): FormGroup {
    return this.formBuilder.group({
      qteCmd: dataItem.qteCmd,
      qteUgSaisie: dataItem.qteUgSaisie
    });
  }


  public checkifvalid(currentbloc: BlocOffre) {
    const currentstat = currentbloc.parent;

    if (currentstat.etat === "I") {
      this.validCommande = false;
    } else {
      this.validCommande = true;
    }


  }
  
  setErrorConditions() {
    this.offre.listeBlocs.forEach(bloc => this.setBlocErrorConditions(bloc));
  }

  setBlocErrorConditions(bloc: any) {
    const conditionErrors: { name: string, value: number }[] = [];
    if (bloc.qteMin && bloc.qteMin > bloc.totalQteCmd) {
      conditionErrors.push({ name: "min", value: bloc.qteMin });
    }
    if (bloc.qteMax && bloc.qteMax < bloc.totalQteCmd) {
      conditionErrors.push({ name: "max", value: bloc.qteMax });
    }
    if (bloc.valeurMin && bloc.valeurMin > bloc.totalValeurBruteCmd) {
      conditionErrors.push({ name: "vMin", value: bloc.valeurMin });
    }
    if (bloc.valeurMax && bloc.valeurMax < bloc.totalValeurBruteCmd) {
      conditionErrors.push({ name: "vMax", value: bloc.valeurMax });
    }

    if (bloc.nbrObjFilsMin && bloc.nbrObjFilsMin > bloc.totalNbrObjFils) {
      conditionErrors.push({ name: "nbrSousBlockMin", value: bloc.nbrObjFilsMin });
    }
    if (bloc.nbrObjFilsMax && bloc.nbrObjFilsMax < bloc.totalNbrObjFils) {
      conditionErrors.push({ name: "nbrSousBlockMax", value: bloc.nbrObjFilsMax });
    }

    if (bloc.nombreProduitsMin && bloc.nombreProduitsMin > bloc.totalNombreProduits) {
      conditionErrors.push({ name: "nbrPrdMin", value: bloc.nbrObjFilsMin });
    }
    if (bloc.nombreProduitsMax && bloc.nombreProduitsMax < bloc.totalNombreProduits) {
      conditionErrors.push({ name: "nbrPrdMax", value: bloc.nombreProduitsMax });
    }

    bloc.conditionErrors = conditionErrors;
  }

  async openIonModal(modalEle: any, pd: any) {
    this.paliersData = pd;
    await this.modalController.create({
      showBackdrop: true,
      component: modalEle.template
    });
    modalEle.onDidDismiss().then((modelData) => {
      // if (modelData !== null) {
      //   this.modelData = modelData;
      //   console.log('Modal Data : ' + modelData.id);
      // }
    });
    return await modalEle.present();
  }


  openPage(page: string) {
    this.navController.navigateForward([page], {});
  }

  ajouterClient(isClientLocal = false) {
    this.openClientModal.emit({ open: true, isClientLocal });
  }

}
