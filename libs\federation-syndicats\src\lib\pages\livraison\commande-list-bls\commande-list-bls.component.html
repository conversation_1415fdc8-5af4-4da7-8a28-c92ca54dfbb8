<!-- Start Of Header -->
<div class="rowline mb-0" *ngIf="!loading">
  <div class="page-title-box ">
      <div class="d-flex k-gap-2 align-items-center ">
          <button class="actions-icons action-back btn text-white" (click)="back()">
              <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
          </button>
          <h4 class="page-title fw-4 ps-2 truncate" >
            <span *ngIf="!loading && listEnteteBl.length === 0; else: noResult">---</span>
            <ng-template #noResult>
              <span class="d-none d-md-inline">BLS de Commande N° {{listEnteteBl[0]?.codeCommande}}</span>
            </ng-template>
           </h4>
      </div>
  </div>
</div>
<!-- END HEADER -->
<div class="row mx-2" *ngIf="!loading && listEnteteBl.length > 0">
  <div class="card bg-transparent my-1 w-100 ">
  <div class="d-flex align-items-center">
    <button *ngIf="!isLeftDisabled" class="btn h-100 px-1" [style.backgroundColor]="currentPlateforme === 'FEDERATION_SYNDICAT' ? 'var(--fs-grid-primary)' : 'var(--fs-group-grid)'" (click)="scrollToDirection('left')">
      <i class="bi bi-chevron-left text-white" style="font-size: 22px; line-height: 1;"></i>
    </button>
    <ul ngbNav #infoPharamcieNav="ngbNav" #blScroller class="nav-tabs pharmacie-tab flex-nowrap w-full overflow-hidden" style="gap: 0 !important; scroll-behavior: smooth;" (shown)="checkScrollPosition()" [activeId]="activeId" (activeIdChange)="tabChange($event)">
      <ng-container  *ngFor="let bl of listEnteteBl; let i = index">

        <li  [ngbNavItem]="i + 1" [ngClass]="{'active-tab rounded': activeId === i+1}" class="flex-shrink-0" style="flex:0 !important;">
          <a ngbNavLink class="w-100 d-flex align-items-center justify-content-between bls-taps text-nowrap" [ngStyle]="{'color': activeId === i+1 ? '#fff !important' : '#000 !important'}">
            <div class="d-flex row align-items-center px-2 flex-grow-1">
              <b class="d-none d-md-inline">Bon de Livraison {{bl.numeroBl}}</b>
              <b class="d-md-none">BL {{bl.numeroBl}}</b>
            </div>
          </a>
          <ng-template ngbNavContent>
            <ng-container [ngTemplateOutlet]="BLPage" [ngTemplateOutletContext]="{ $implicit: bl }"></ng-container>
          </ng-template>
        </li>
        <li class="tabs-separate flex-grow-0" *ngIf="i !== listEnteteBl.length - 1"></li>
      </ng-container>

    </ul>
    <button *ngIf="!isRightDisabled" class="btn h-100 px-1" [style.backgroundColor]="currentPlateforme === 'FEDERATION_SYNDICAT' ? 'var(--fs-grid-primary)' : 'var(--fs-group-grid)'" (click)="scrollToDirection('right')">
      <i class="bi bi-chevron-right text-white" style="font-size: 22px; line-height: 1;"></i>
    </button>
  </div>
    <div [ngbNavOutlet]="infoPharamcieNav"></div>
  </div>
</div>

<ng-template #BLPage let-bl>
  <div class="card">
    <div class="card-body p-1">
      <form class="p-0 m-0" autocomplete="off">
        <div class="px-1 bg-white mb-sm-0">
          <div class="row divider-y" wphFocusTrap>
            <div class="col-md-4">
              <div class="col-sm-12  my-1 p-0">
                <div class="form-group mb-0">
                  <label for="raisonSociale" class="form-label p-0 col-12">
                    Distributeur
                  </label>
                  <div class="input-group picker-input">
                    <input type="text" id="raisonSociale"  [value]="bl?.fournisseur?.raisonSociale"
                      class="form-control pl-4 " [readOnly]="true" />
                    <div class="picker-icons picker-icons-alt">
                      <i class="bi bi-truck"></i>
                    </div>
                  </div>

                </div>
              </div>
              <div class="col-sm-12  my-1 p-0">
                <div class="form-group mb-0">
                  <label for="raisonSociale" class="form-label p-0 col-12">
                    Commande
                  </label>
                  <div class="input-group picker-input">
                    <input type="text" id="raisonSociale" [readOnly]="true" class="form-control pl-4" [value]="bl?.enteteCommandeAchatGroupe?.codeCommande ?? bl.codeCommande">
                    <div class="picker-icons picker-icons-alt">
                      <i class="bi bi-receipt"></i>
                    </div>

                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-8">
              <div class="row">
                <div class="col-12  my-1">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Montant BL
                        </label>
                        <div class="input-group picker-input">
                          <input type="number" id="raisonSociale"
                             class="form-control pl-4" [readOnly]="true" [value]="bl?.montantSaisi"
                            placeholder="" />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-cash"></i>
                          </div>

                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Remise
                        </label>
                        <div class="input-group picker-input">
                          <input type="text" id="raisonSociale"
                            class="form-control pl-4" [readOnly]="true"  [value]="bl?.tauxRf" placeholder=""
                            />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-gift"></i>
                          </div>
                          <div class="picker-icons picker-icons-end" style="right: 2px;">
                            <i class="bi bi-percent" style="font-size: 15px;opacity: 0.85;color: black;"></i>
                          </div>

                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Montant BL Calculé
                        </label>
                        <div class="input-group picker-input">
                          <input type="text" id="raisonSociale"
                            class="form-control pl-4"  [value]="bl?.montantCalcule"  placeholder="" [readOnly]="true" />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-search text-dark"></i>
                          </div>
                        </div>

                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="transporteur" class="form-label p-0 col-12">Transporteur</label>
                    <div class="input-group picker-input">
                      <input type="text" id="transporteur" [readOnly]="true" [value]="bl?.raisonSocialeTransporteur" class="form-control pl-4" placeholder=""
                        />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-hash text-dark"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">N° BL
                    </label>
                    <div class="input-group picker-input">
                      <input type="text" id="raisonSociale" class="form-control pl-4"
                        placeholder="Entrez N° BL"
                        [value]="bl?.numeroBl" [readOnly]="true"
                        />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-hash text-dark"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">
                      Date De BL
                    </label>
                    <div class="input-group">
                      <input type="text" [readOnly]="true" class="form-control form-control-md"
                      id="dateDebut" [value]="bl?.dateReceptionBl | date: 'dd/MM/yyyy'">
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="card-footer">
      <kendo-grid [data]="filteredLignes" class="fs-grid fs-grid-white" wphScrollCheck [sortable]="{ mode: 'single'}"
      style="max-height:calc(100vh - 200px); min-height: calc(100vh - 300px);" [rowClass]="choseRowClass">
      <ng-template kendoGridToolbarTemplate>
        <div class="d-flex justify-content-center justify-content-md-between px-2 align-items-center w-100 flex-wrap k-gap-2">
          <div>
            <h3 class="text-white font-20 m-0 text-center">Liste Des Produits</h3>
          </div>
          <div class="m-0 d-flex justify-content-center align-items-center k-gap-2 bl-header-actions  bl-input-search-wrapper">

            <div class="input-group picker-input">
              <input [ngModel]="filter" (ngModelChange)="onFilterBLProduit($event)" type="search"
                placeholder="Rechercher par Designation" class="form-control form-control-md pl-4 bl-input-search" id="groupeCritere" />

              <div class="picker-icons picker-icons-alt">
                <i class="mdi mdi-magnify pointer"></i>
              </div>
            </div>
            <div class="d-none d-md-inline">
            <!-- <app-export-pdf [exportRef]="exportPdfRef" class="m-0 "></app-export-pdf> -->
            </div>
          </div>
        </div>

      </ng-template>
      <kendo-grid-column field="designation" title="Désignation" class="text-wrap" [sortable]="false" [width]="300">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.designation}} <span *ngIf="dataItem.isCadeau" class="text-success font-weight-bold">(Offert)</span>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="pph" title="PPH" [width]="100" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <input type="number" class="form-control py-0 px-1  text-right" [readOnly]="true" [ngModel]="dataItem.pph"
          />
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="ppv" title="PPV" [width]="90" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem  let-rowIndex="rowIndex">
          <input type="number" class="form-control py-0 px-1  text-right" [readOnly]="true" [ngModel]="dataItem.ppv" />
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="quantiteCommandee" [width]="90"  title="Qté Cmd" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          <input type="number" class="form-control py-0 px-1"  *ngIf="!dataItem.isCadeau"  tabindex="-1" [readOnly]="true"
            value="{{dataItem.quantiteCommandee}}" />
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="quantiteUgCmd" title="UG Cmd" [width]="90" [sortable]="false" class="end-phase" headerClass="end-phase">
        <ng-template kendoGridCellTemplate let-dataItem>
          <input type="number" class="form-control py-0 px-1  text-right" tabindex="-1" [readOnly]="true" [value]="dataItem.quantiteUgCmd ?? 0" />
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="quantiteLivree" title="Qté Livré" [width]="80" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem let-column="column" let-rowIndex="rowIndex">
          <input type="number" class="form-control py-0 px-1 text-right"
            [ngClass]="{'invalid-input': dataItem.quantiteLivree > dataItem.quantiteCommandee }"
            [readOnly]="true" [(ngModel)]="dataItem.quantiteLivree"
            />
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="quantiteUg"  title="UG Livré" [width]="80" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <input type="number" class="form-control py-0 px-1  text-right"
            [(ngModel)]="dataItem.quantiteUg" [readOnly]="true" />
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="pphRemise" wphAllowOnlyNumbers   title="PPH remisé" [width]="80" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <input type="number" class="form-control py-0 px-1  text-right" [readOnly]="true" [(ngModel)]="dataItem.pphRemise"
          *ngIf="!dataItem.isCadeau"
          />
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="tauxRf" title="Remise" [width]="70" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
         <!-- picker-input-->
          <div class="input-group picker-input">
            <input type="text" class="form-control py-0 px-1  text-right pr-3" [value]="dataItem.tauxRf ?? 0"
            *ngIf="!dataItem.isCadeau"
            [readOnly]="true"

            />
            <div class="picker-icons picker-icons-end" style="right: -4px;"  *ngIf="!dataItem.isCadeau">
              <i class="bi bi-percent" style="font-size: 15px;opacity: 0.85;color: black;"></i>
            </div>
          </div>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="montant" title="Total" [width]="100" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          <input type="text" class="form-control py-0 px-1  text-right" [(ngModel)]="dataItem.montant" [readOnly]="true"
          />
        </ng-template>
      </kendo-grid-column>
      <!-- <kendo-grid-column field="montant" title="Montant" [width]="100" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          <input type="text" class="form-control py-0 px-1" [(ngModel)]="dataItem.montant" />
        </ng-template>
      </kendo-grid-column> -->
      <!-- <kendo-grid-column field="datePeremption" title="Data peromption" [sortable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          <input type="text" class="form-control py-0 px-1" value="{{dataItem.datePeremption | date: 'dd/MM/yyyy'}}" />
        </ng-template>
      </kendo-grid-column> -->
      <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
        pagerItemsPerPage="éléments par page"></kendo-grid-messages>

      <ng-template kendoGridNoRecordsTemplate>
        <span>Aucun résultat trouvé.</span>
      </ng-template>
    </kendo-grid>
    <div>
      <div  class="d-flex mt-2 align-items-center synthese-container flex-wrap ">
        <div class="border-right px-3 flex-grow-1 py-2 m-0 d-flex justify-content-between">
          <span class="font-18">Quantité Livré :</span> <span class="font-18 font-weight-bold">{{synthese?.qLivree}}</span>
        </div>
        <div class="border-right px-3 flex-grow-1 py-2 m-0 d-flex justify-content-between">
          <span class="font-18">Montant Brut :</span> <span class="font-18 font-weight-bold">{{synthese?.mntBrut | number:'1.2-2':'en-US'}}</span>
        </div>
        <div class="border-right px-3 flex-grow-1 py-2 m-0 d-flex justify-content-between">
          <span class="font-18">Montant Net :</span> <span class="font-18 font-weight-bold">{{synthese?.mntNet | number:'1.2-2':'en-US'}}</span>
        </div>
        <div class=" px-3 flex-grow-1 py-2 m-0 d-flex justify-content-between">
          <span class="font-18">Montant Remise :</span> <span class="font-18 font-weight-bold">{{synthese?.mntRemise | number:'1.2-2':'en-US'}}</span>
        </div>
      </div>
    </div>
    </div>
  </div>
</ng-template>


<div *ngIf="!loading && listEnteteBl.length === 0" class="d-flex justify-content-center align-items-center w-100 h-100">
  <h3 class="text-center mt-4">Aucun résultat trouvé</h3>
</div>
