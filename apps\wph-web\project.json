{"name": "wph-web", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/wph-web/src", "prefix": "wph", "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/wph-web", "index": "apps/wph-web/src/index.html", "main": "apps/wph-web/src/main.ts", "polyfills": "apps/wph-web/src/polyfills.ts", "tsConfig": "apps/wph-web/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["apps/wph-web/src/favicon.ico", "apps/wph-web/src/assets"], "styles": [{"input": "node_modules/@progress/kendo-theme-default/dist/all.css"}, {"input": "node_modules/leaflet/dist/leaflet.css"}, {"input": "node_modules/ngx-toastr/toastr.css"}, "apps/wph-web/src/assets/scss/app.scss", "apps/wph-web/src/assets/scss/icons.scss"], "allowedCommonJsDependencies": ["events", "node_modules/apexcharts/dist/apexcharts.min.js", "moment", "lodash", "event", "apexcharts", "can-use-dom", "js-interpreter", "leaflet", "html2canvas", "pdfmake/build/pdfmake", "pdfmake/build/vfs_fonts", "lodash.debounce", "core-js/es/array/from", "core-js/es/map", "core-js/es/object/assign", "core-js/es/set", "core-js/es/weak-map", "lodash.clamp", "<PERSON><PERSON><PERSON><PERSON>", "mousetrap", "dragula", "moment-timezone", "libs/data-access/src/lib/services/versions.service"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"], "webWorkerTsConfig": "apps/wph-web/tsconfig.worker.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "500kb", "maximumError": "15mb"}], "fileReplacements": [{"replace": "apps/wph-web/src/environments/environment.ts", "with": "apps/wph-web/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "fs-prod": {"fileReplacements": [{"replace": "apps/wph-web/src/environments/environment.ts", "with": "apps/wph-web/src/environments/environment.fs-prod.ts"}], "outputHashing": "all"}, "wg-dev": {"fileReplacements": [{"replace": "apps/wph-web/src/environments/environment.ts", "with": "apps/wph-web/src/environments/environment.wg-dev.ts"}], "outputHashing": "all"}, "wg-prod": {"fileReplacements": [{"replace": "apps/wph-web/src/environments/environment.ts", "with": "apps/wph-web/src/environments/environment.wg-prod.ts"}], "outputHashing": "all"}, "fs-dev": {"fileReplacements": [{"replace": "apps/wph-web/src/environments/environment.ts", "with": "apps/wph-web/src/environments/environment.fs-dev.ts"}], "outputHashing": "all"}, "preprod": {"fileReplacements": [{"replace": "apps/wph-web/src/environments/environment.ts", "with": "apps/wph-web/src/environments/environment.preprod.ts"}], "outputHashing": "all"}, "dev": {"fileReplacements": [{"replace": "apps/wph-web/src/environments/environment.ts", "with": "apps/wph-web/src/environments/environment.dev.ts"}], "outputHashing": "all"}, "recette": {"fileReplacements": [{"replace": "apps/wph-web/src/environments/environment.ts", "with": "apps/wph-web/src/environments/environment.recette.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "wph-web:build:production"}, "development": {"browserTarget": "wph-web:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "wph-web:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["apps/wph-web/**/*.ts", "apps/wph-web/**/*.html"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/wph-web"], "options": {"jestConfig": "apps/wph-web/jest.config.ts", "passWithNoTests": true}}}, "tags": []}