<!-- Start Of Header -->
<div class="rowline mb-0">
  <div class="page-title-box row">
    <h4 class="page-title fw-4 ps-2 col-4">Pharmacies du Maroc</h4>

    <div class="col-8 px-1">
      <div class="row justify-content-end align-items-center">
        <button *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" (click)="synchroniser()" type="button"
          class="btn btn-sm btn-success m-1">
          <i class="bi bi-arrow-repeat"></i>
          Synchroniser
        </button>
        <button type="button" class="btn btn-sm btn-info m-1" (click)="openFilterModal(pharmaciesFilterModal)">
          <i class="mdi mdi-filter-variant"></i>
          Filtrer
        </button>
      </div>
    </div>
  </div>
</div>
<!-- END HEADER -->

<div class="card">
  <kendo-grid [data]="gridData" [pageable]="{
    buttonCount: 5,
    info: true,
    type: 'numeric',
    pageSizes: pageSizes,
    previousNext: true,
    position: 'bottom'
  }" [selectable]="false" (pageChange)="pageChange($event)" [pageSize]="navigation.pageSize" [skip]="navigation.skip"
    style="min-height: calc(100vh - 123px)" [resizable]="true" [sort]="gridSort" [sortable]="{mode: 'single'}"
    (sortChange)="gridSortChange($event)">

    <kendo-grid-column [width]="110" field="code" class="text-wrap">
      <ng-template kendoGridHeaderTemplate>
        <span class="text-wrap text-center">Code Pharmacie du Maroc</span>
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.clientGroupe?.code }}
      </ng-template>

    </kendo-grid-column>

    <kendo-grid-column [width]="180" field="raisonSociale" class="text-wrap">
      <ng-template kendoGridHeaderTemplate>
        <span class="text-wrap d-flex align-items-center">Raison Sociale</span>
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.clientGroupe?.raisonSociale }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column [width]="180" field="nomResponsable" class="text-wrap">
      <ng-template kendoGridHeaderTemplate>
        <span class="text-wrap d-flex align-items-center">Nom Responsable</span>
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.clientGroupe?.nomResponsable }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column [width]="180" field="ville" class="text-wrap">
      <ng-template kendoGridHeaderTemplate>
        <span class="text-wrap d-flex align-items-center">Ville</span>
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem?.clientGroupe?.ville }}
      </ng-template>

    </kendo-grid-column>

    <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR']">
      <kendo-grid-column [hidden]="isFournisseurLabo" [width]="80" [sortable]="false" class="text-center no-ellipsis">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap text-center d-flex align-items-center">Est Client</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <span *ngIf="!dataItem?.accesClient || (dataItem?.accesClient && !!dataItem?.accesClient?.dateDesactivation)"
            class="badge badge-light rounded-pill py-1 px-2">Non</span>
          <span *ngIf="dataItem?.accesClient && !dataItem?.accesClient?.dateDesactivation"
            class="badge badge-success rounded-pill py-1 px-2">Oui</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [hidden]="isFournisseurLabo" [width]="80" [sortable]="false" class="text-center no-ellipsis">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap text-center">Actif dans le groupe</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <span *ngIf="!dataItem?.clientGroupe?.isEnrolled" class="badge badge-light rounded-pill py-1 px-2">Non</span>
          <span *ngIf="dataItem?.clientGroupe?.isEnrolled" class="badge badge-success rounded-pill py-1 px-2">Oui</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column [hidden]="isFournisseurLabo" [width]="80" [sortable]="false" title="Action"
        class="text-center no-ellipsis">
        <ng-template kendoGridCellTemplate let-dataItem>
          <span class="badge badge-warning rounded-pill py-1 px-2" style="cursor: pointer;"
            (click)="openEditServicesPage(dataItem)">Gérer</span>
        </ng-template>
      </kendo-grid-column>
    </ng-container>


    <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
      <kendo-grid-column [width]="80" field="countTranscoClient" class="text-wrap" [sortable]="false">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap d-flex align-items-center">Nbr. Filiale Client</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.clientGroupe?.countTranscoClient }}
        </ng-template>
      </kendo-grid-column>
      
      <kendo-grid-column [width]="80" field="countAccesClient" class="text-wrap" [sortable]="false">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap d-flex align-items-center">Nbr. Filiale Accès</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem?.clientGroupe?.countAccesClient }}
        </ng-template>
      </kendo-grid-column>
      
      <kendo-grid-column [width]="80" field="isEnrolled" class="text-center no-ellipsis" [sortable]="false">
        <ng-template kendoGridHeaderTemplate>
          <span class="text-wrap d-flex align-items-center">Passer Cmd</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <span *ngIf="!dataItem?.clientGroupe?.isEnrolled" class="badge badge-light rounded-pill py-1 px-2">Non</span>
          <span *ngIf="dataItem?.clientGroupe?.isEnrolled" class="badge badge-success rounded-pill py-1 px-2">Oui</span>
        </ng-template>
      </kendo-grid-column>
    </ng-container>


    <kendo-grid-messages pagerItems="lignes" pagerOf="de" pagerItemsPerPage="éléments par page"></kendo-grid-messages>

    <ng-template kendoGridNoRecordsTemplate>
      <span>Aucun résultat trouvé.</span>
    </ng-template>

  </kendo-grid>
</div>

<!-- Start Filter Modal -->
<ng-template #pharmaciesFilterModal let-modal>
  <wph-pharmacie-filter-modal [formGroup]="pharmacieCriteriaForm"
    (modalAction)="filterModalAction($event)"></wph-pharmacie-filter-modal>
</ng-template>
<!-- End Filter Modal -->