import { BlocOffre } from "@wph/data-access";

export interface SelectPackValuePair {
    label: string;
    value: SelectProduitValuePair[];
}

export interface SelectProduitValuePair {
    label: string;
    value: BlocOffre;
}

export class ConditionBlocOffreCommandeConsolidee {
    id?: number;
    blocOffreId?: number;
    qteMin?: number;
    qteMax?: number;
    valeurMin?: number;
    valeurMax?: number;
    blocObligatoire?: string;


    constructor(conditions?: Partial<ConditionBlocOffreCommandeConsolidee>) {
        this.id = conditions?.id || null;
        this.blocOffreId = conditions?.blocOffreId || null;
        this.qteMin = conditions?.qteMin || null;
        this.qteMax = conditions?.qteMax || null;
        this.valeurMin = conditions?.valeurMin || null;
        this.valeurMax = conditions?.valeurMax || null;
        this.blocObligatoire = conditions?.blocObligatoire;
    }
}

export interface IMapConditionByBlocId {
    [index: number]: ConditionBlocOffreCommandeConsolidee;
}