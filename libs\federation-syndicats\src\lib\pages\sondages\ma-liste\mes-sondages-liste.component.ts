import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import {
  GridDataResult,
  PageChangeEvent,
} from '@progress/kendo-angular-grid';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AvisCriteria, AvisDTO, TypeAvis } from '../../../models/avis.model';
import { FsCommandesService } from '../../../services/fs-commandes.service';
import { FederationSyndicatService } from '../../../services/federation-syndicats.service';
import { AuthService } from '@wph/core/auth';
import { FormControl } from '@angular/forms';
import { debounceTime } from 'rxjs';

@Component({
  selector: 'wph-mes-sondages-liste',
  templateUrl: './mes-sondages-liste.component.html',
  styleUrls: ['./mes-sondages-liste.component.scss'],
  encapsulation: ViewEncapsulation.None

})
export class MesSondagesListeComponent implements OnInit {

  isInterested: boolean = true;
  pageSizes: number[] = [5, 10, 15, 20];
  navigation = {
    pageSize: 15,
    skip: 0,
    sortMethod: 'desc'
  };
  gridData: GridDataResult = { total: 0, data: [] };
  groupeSort: SortDescriptor[] = [];
  monGroupe: any;
  membreId: number; 
  searchCriteria: AvisCriteria  = {};
  commandeConsolideeIds: number[] = []; 
  searchControl: FormControl = new FormControl('');
  filteredData: AvisDTO[] = [];
 
  



  constructor(private avisService: FsCommandesService, private fedSyndicatService : FederationSyndicatService, private authService: AuthService ) {}

  ngOnInit(): void {
    this.membreId = this.authService.getPrincipal()?.societe?.id;

    this.fedSyndicatService.getMyGroupe().then(myGroupe => {
      this.monGroupe = myGroupe;
      this.searchCriteria = { sondeurId: this.membreId };
      this.loadAvis();
    });

    this.searchControl.valueChanges.pipe(
      debounceTime(300)
    ).subscribe(value => {
      this.filterAvis(value);
    });

  }
  onSwitchChange(): void {
    this.loadAvis();
  }


  pageChange(event: PageChangeEvent): void {
    this.navigation.skip = event.skip;
    this.navigation.pageSize = event.take;
    this.loadAvis();
  }

  sortChange(sort: SortDescriptor[]): void {
    this.groupeSort = sort;
    if (sort.length) {
      this.navigation.sortMethod = sort[0].dir;
    } else {
      this.navigation.sortMethod = null;
    }
    this.loadAvis();
  }

  OnPageChange(event: number): void {
    this.loadAvis();
  }


  private loadAvis(): void {
    const typeAvis = this.isInterested ? TypeAvis.Positive : TypeAvis.Negative;
    const criteria: AvisCriteria = {
      ...this.searchCriteria,
      typeAvis: typeAvis,
      sondeurId: this.membreId
    };
  
    this.avisService.rechercherAvisParCritere(criteria).subscribe(
      (response: { content: AvisDTO[], totalElements: number }) => {
        if (Array.isArray(response.content)) {
          this.gridData = {
            total: response.totalElements,
            data: response.content.slice(this.navigation.skip, this.navigation.skip + this.navigation.pageSize),
          };
          this.filteredData = [...this.gridData.data]; 
        } else {
          console.error('Response content is not an array:', response);
          this.gridData = {
            total: 0,
            data: []
          };
          this.filteredData = [];
        }
      },
      (error) => {
        console.error('Error fetching Avis:', error);
        this.gridData = {
          total: 0,
          data: []
        };
        this.filteredData = [];
      }
    );
  }
  

  private filterAvis(searchValue: string): void {
    if (!searchValue) {
      this.filteredData = [...this.gridData.data];
    } else {
      this.filteredData = this.gridData.data.filter(avis =>
        avis.enteteCommandeConsolideeMarche?.offre?.titre.toLowerCase().includes(searchValue.toLowerCase())
      );
    }
  }


  defaultImageUrl = 'path/to/default-image.jpg'; 
}