$primary: #1B75BB;

.active-icon {
  color: var(--primary);
}

.bg-cstm-danger {
  color: #A93535;
}

.input-error {
  border: 1px solid #A93535 !important;
}


button {
  background: none;
  border: none;
  font: inherit;
  color: inherit;
  cursor: pointer;
  outline: inherit;
}


.img-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
  position: absolute;
  top: 0;
  left: 0;
}


::ng-deep .increase-popover-width {
  background-color: rgb(169, 53, 53);
  border-color: rgb(128, 39, 39);
  width: auto !important;
}

::ng-deep .popover-body {
  color: #fff !important;
  font-weight: 700;
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  background: var(--win-offre-bg-light-2) !important;
  color: var(--winoffre-light-shade) !important;
  border-color: var(--win-offre-primary-tint) !important;
  border-bottom-width: 2px;
  font-weight: 600;
}

#pack-tab-nav {
  .nav-tabs .nav-link.pack-success {
    color: #5E9E71 !important;
  }

  .nav-tabs .nav-link.active.pack-success,
  .nav-tabs .nav-item.show .nav-link {
    color: #5E9E71 !important;
    border-color: #5E9E71 !important;
    border-bottom-width: 2px;
    font-weight: 600;
  }

  .nav-tabs .nav-link.pack-error {
    color: #C24B4B !important;
  }

  .nav-tabs .nav-link.pack-warning {
    color: #EE8245 !important;
  }


  .nav-tabs .nav-link.active.pack-error,
  .nav-tabs .nav-item.show .nav-link {
    color: #C24B4B !important;
    border-color: #C24B4B !important;
    border-bottom-width: 2px;
    font-weight: 600;
  }

  .nav-tabs .nav-link.active.pack-warning,
  .nav-tabs .nav-item.show .nav-link {
    color: #EE8245 !important;
    border-color: #EE8245 !important;
    border-bottom-width: 2px;
    font-weight: 600;
  }

}

.bg-motif {
  background: #e9797918 !important;
  border: 1px solid #e97979 !important;
  color: darken(#e97979, 20);

}

.bg-message-cmd {
  border: 1px solid #595959 !important;
}

.bg-message-cmd-alt {
  position: absolute;
  bottom: 0;
  width: 100%;
}

.text-sub {
  color: #6b727d;
}


.p-badge {
  padding-block: 13px !important;
}

.text-success-alt {
  color: var(--winoffre-text-primary) !important;
}

.bg-success-alt {
  color: #fff;
  background: #0f8fa3 !important;
}

button.collapsable-icon svg path {
  stroke-width: 25px !important;
}



.rows-divider> :not(:nth-last-child(-n+2)) {
  border-bottom: 1px solid #d8e1ef;
}

.rows-divider-alt {
  border-bottom: 1px solid #d8e1ef;
}

.fixed-synthese {
  border-radius: var(--winoffre-base-border-radius);
  position: fixed;
  bottom: 0;
  border-width: 2px;
  z-index: 999;
}

.conditions-row {
  .b-radius {
    border-radius: 10px !important;
  }
}

.conditions-row>*:nth-child(n+3) {
  display: none !important;
}

.bg-see-more.b-radius {
  border-radius: 10px;
}

img.img-fluid {
  width: fit-content;
  height: auto;
  object-fit: contain;
  border-radius: var(--winoffre-base-border-radius) !important;
}

img.img-fluid-alt {
  width: 70px;
  height: 50px;
  object-fit: cover;
  border-radius: var(--winoffre-base-border-radius);
}

.bg-min-width {
  min-width: 125px !important;
}



::ng-deep #WIN_GROUPE-container {
  .fixed-synthese {
    .bg-cstm-info {
      background: var(--wf-primary-500);
      color: #fff !important;
      border-width: 2px;
      border-color: var(--wf-primary-500) !important;
      border-style: solid;
      cursor: pointer;
    }

    .bg-cstm-info:hover {
      background: #fff;
      color: var(--wf-primary-500) !important;
      border-width: 2px;
      border-color: var(--wf-primary-500) !important;
      border-style: solid;
    }
  }

  .fs-grid .k-grid-toolbar {
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
    border: 1px solid var(--fs-group-grid) !important;
    border-bottom: 2px solid var(--fs-group-grid) !important;
  }

  .bg-obligatoire {
    background: #fff;
    color: var(--wf-primary-400) !important;
    border-width: 2px;
    border-color: var(--wf-primary-400) !important;
    border-style: solid;
  }

  .card-border {
    border: 2px solid var(--wf-primary-500) !important;
  }

  .card-border-alt {
    border: 1px solid var(--wf-primary-500) !important;
    border-top-width: 0px !important;
    border-radius: 8px;
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
  }



  .card-border-bg-primary {
    border-radius: 10px;
    border: 1px solid var(--fs-group-grid) !important;

    .card-header {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      background: var(--fs-group-grid) !important;
      color: #fff;
      padding: 0.3rem 1rem !important;
      font-size: 1.2rem;
      font-weight: 700;
      border-bottom: 1px solid var(--fs-group-grid) !important;
    }
  }

  .bg-conditions {
    color: #fff !important;
    font-size: 14px;
    font-weight: 700;
    background: var(--wf-primary-500) !important;
  }

  .bg-see-more {
    color: #fff !important;
    font-size: 14px;
    font-weight: 700;
    border-radius: 10px;
    background: var(--wf-primary-400) !important;
  }

  .bg-enabled {
    background-color: var(--wf-primary-400);
  }

  .bg-disabled {
    background-color: var(--wf-primary-700);
  }

  .bg-cstm-info-fs-icon-only {
    margin: 5px 0;
    font-size: .7rem !important;
    font-weight: 800;
    background: var(--wf-secondary);
    color: #000000 !important;
    border-width: 2px;
    border-radius: 8px;
    border-color: var(--wf-secondary);
    border-style: solid;
    cursor: pointer;
  }

  .bg-cstm-info-fs {
    margin: 5px 0;
    font-size: .7rem !important;
    font-weight: 800;
    background: var(--wf-secondary);
    color: #000000 !important;
    border-width: 2px;
    border-radius: 8px;
    border-color: var(--wf-secondary);
    border-style: solid;
    cursor: pointer;

    span>i {
      font-size: .6rem;
      margin-left: 5px;
    }

    &:hover {
      background: #fff;
      color: #000000 !important;
      border-width: 2px;
      border-color: var(--wf-secondary);
      border-style: solid;
    }
  }

  .bg-cstm-info-alt {
    background: var(--wf-primary-500);
  }

  .bg-only-cstm-info {
    background: var(--wf-secondary);
  }

  .mtn-pack {
    border: 2px solid var(--fs-group-grid);

    background: var(--fs-group-grid);
    color: #fff;

    border-radius: 10px;
  }


  .accelerator-container {
    input {
      border-top-right-radius: 0px !important;
      border-bottom-right-radius: 0px !important;
    }

    span {
      background: var(--wf-primary-400);
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;

      height: 38px;
    }
  }

  .coffret-qte-container {

    input {
      border-radius: 0px !important;
    }

    span:first-child {
      background: var(--wf-primary-400);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;

      height: 31.9px;
    }

    span:last-child {
      background: var(--wf-primary-400);
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;

      height: 31.9px;
    }
  }

  // ? NAV TABS OVERRIDE
  #pack-tab-nav-commande-pack {

    ::ng-deep .nav-tabs {
      width: 100% !important;
      border-bottom: none !important;
      gap: 0px !important;
      padding: 0 !important;
      margin: 0 !important;
      border-bottom: 2px solid var(--wf-primary-500) !important;
    }

    ::ng-deep .nav-tabs .nav-link,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      font-weight: 800 !important;
      padding: 6px 8px !important;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      position: relative !important;
      border-bottom: 2px solid var(--fs-group-grid);

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 80%;
        margin-left: -10px;
        background-color: var(--fs-group-grid);
      }
    }

    ::ng-deep .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      background-color: var(--fs-group-grid) !important;
      color: #fff !important;
      padding: 6px 8px !important;
      border-color: transparent !important;
      font-weight: 800 !important;
      margin: 0 !important;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;

      &::before {
        content: none;
      }
    }

    .nav-tabs .nav-link.pack-success {
      color: #5E9E71 !important;
    }

    .nav-tabs .nav-link.active.pack-success,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #5E9E71 !important;
      border: 1px solid #5E9E71 !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }

    .nav-tabs .nav-link.pack-error {
      color: #C24B4B !important;
    }

    .nav-tabs .nav-link.active.pack-error,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #C24B4B !important;
      border: 1px solid #C24B4B !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }
  }

  #pack-tab-nav-commande {

    ::ng-deep .nav-tabs {
      width: 100% !important;
      border-bottom: none !important;
      gap: 0px !important;
      padding: 0 !important;
      margin: 0 !important;
      border-bottom: 2px solid var(--wf-primary-500) !important;
    }

    ::ng-deep .nav-tabs .nav-link,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      font-weight: 800 !important;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      position: relative !important;
      border-bottom: 2px solid var(--fs-group-grid);

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 80%;
        margin-left: -10px;
        background-color: var(--fs-group-grid);
      }
    }

    ::ng-deep .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      background-color: var(--fs-group-grid) !important;
      color: #fff !important;
      border-color: transparent !important;
      font-weight: 800 !important;
      margin: 0 !important;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;

      &::before {
        content: none;
      }
    }

    .nav-tabs .nav-link.pack-success {
      color: #5E9E71 !important;
    }

    .nav-tabs .nav-link.active.pack-success,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #5E9E71 !important;
      border: 1px solid #5E9E71 !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }

    .nav-tabs .nav-link.pack-error {
      color: #C24B4B !important;
    }

    .nav-tabs .nav-link.active.pack-error,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #C24B4B !important;
      border: 1px solid #C24B4B !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }
  }

  #pack-tab-nav-commande-groupe {

    ::ng-deep .nav-tabs {
      width: 100% !important;
      border-bottom: none !important;
      gap: 0px !important;
      padding: 0 !important;
      margin: 0 !important;
      border-bottom: 2px solid var(--wf-primary-500) !important;
    }

    ::ng-deep .nav-tabs .nav-link,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      font-weight: 800 !important;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      position: relative !important;
      border-bottom: 2px solid var(--fs-group-grid);

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 80%;
        margin-left: -10px;
        background-color: var(--fs-group-grid);
      }
    }

    ::ng-deep .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      background-color: var(--wf-primary-400) !important;
      color: #fff !important;
      padding: 6px 8px !important;
      border-color: transparent !important;
      font-weight: 800 !important;
      margin: 0 !important;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;

      &::before {
        content: none;
      }
    }

    .nav-tabs .nav-link.pack-success {
      color: #5E9E71 !important;
    }

    .nav-tabs .nav-link.active.pack-success,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #5E9E71 !important;
      border: 1px solid #5E9E71 !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }

    .nav-tabs .nav-link.pack-error {
      color: #C24B4B !important;
    }

    .nav-tabs .nav-link.active.pack-error,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #C24B4B !important;
      border: 1px solid #C24B4B !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }
  }

  #pack-tab-nav-commande-reference {

    ::ng-deep .nav-tabs {
      width: 100% !important;
      border-bottom: none !important;
      gap: 0px !important;
      padding: 0 !important;
      margin: 0 !important;
      border-bottom: 2px solid var(--wf-primary-500) !important;
    }

    ::ng-deep .nav-tabs .nav-link,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      font-weight: 800 !important;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      position: relative !important;
      border-bottom: 2px solid var(--fs-group-grid);

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 80%;
        margin-left: -10px;
        background-color: var(--fs-group-grid);
      }
    }

    ::ng-deep .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      background-color: var(--wf-primary-300) !important;
      color: #fff !important;
      border-color: transparent !important;
      font-weight: 800 !important;
      margin: 0 !important;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;

      &::before {
        content: none;
      }
    }

    .nav-tabs .nav-link.pack-success {
      color: #5E9E71 !important;
    }

    .nav-tabs .nav-link.active.pack-success,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #5E9E71 !important;
      border: 1px solid #5E9E71 !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }

    .nav-tabs .nav-link.pack-error {
      color: #C24B4B !important;
    }

    .nav-tabs .nav-link.active.pack-error,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #C24B4B !important;
      border: 1px solid #C24B4B !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }
  }

  #packsParMembre {
    ::ng-deep .k-grid.fs-grid .k-grid-footer tr td:not(.odd-row) {
      color: black !important;
      background-color: var(--wf-primary-100) !important;
    }
  
    ::ng-deep .k-grid.fs-grid.fs-grid-white .k-grid-header .k-header {
      border-bottom: 3px solid var(--fs-group-grid) !important;
    }
  
    ::ng-deep .k-grid.fs-grid.fs-grid-white tr th:first-child {
      border-left: 1px solid var(--fs-group-grid);
    }
  }

}

::ng-deep #FEDERATION_SYNDICAT-container {
  .fixed-synthese {
    .bg-cstm-info {
      background: #8B5AB0;
      color: #fff !important;
      border-width: 2px;
      border-color: #8B5AB0;
      border-style: solid;
      cursor: pointer;
    }

    .bg-cstm-info:hover {
      background: #fff;
      color: #8B5AB0 !important;
      border-width: 2px;
      border-color: #8B5AB0;
      border-style: solid;
    }
  }

  .fs-grid .k-grid-toolbar {
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
    border: 1px solid var(--fs-grid-primary) !important;
    border-bottom: 2px solid var(--fs-grid-primary) !important;
  }

  .bg-obligatoire {
    background: #fff;
    color: var(--fs-primary-600) !important;
    border-width: 2px;
    border-color: var(--fs-primary-600);
    border-style: solid;
  }

  .card-border {
    border: 2px solid var(--fs-grid-primary) !important;
  }

  .card-border-alt {
    border: 1px solid var(--fs-grid-primary) !important;
    border-top-width: 0px !important;
    border-radius: 8px;
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
  }



  .card-border-bg-primary {
    border-radius: 10px;
    border: 1px solid var(--fs-grid-primary) !important;

    .card-header {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      background: var(--fs-grid-primary) !important;
      color: #fff;
      padding: 0.3rem 1rem !important;
      font-size: 1.2rem;
      font-weight: 700;
      border-bottom: 1px solid var(--fs-grid-primary) !important;
    }
  }


  .bg-conditions {
    color: #fff !important;
    font-size: 14px;
    font-weight: 700;
    background: var(--fs-primary-500) !important;
  }

  .bg-see-more {
    color: #fff !important;
    font-size: 14px;
    font-weight: 700;
    border-radius: 10px;
    background: var(--fs-primary-400) !important;
  }

  .bg-enabled {
    background-color: var(--fs-primary-400);
  }

  .bg-disabled {
    background-color: var(--fs-primary-700);
  }

  .bg-cstm-info-fs-icon-only {
    margin: 5px 0;
    font-size: .7rem !important;
    font-weight: 800;
    background: var(--fs-primary-500);
    color: #000000 !important;
    border-width: 2px;
    border-radius: 8px;
    border-color: var(--fs-primary-500);
    border-style: solid;
    cursor: pointer;
  }

  .bg-cstm-info-fs {
    margin: 5px 0;
    font-size: .7rem !important;
    font-weight: 800;
    background: var(--fs-primary-600);
    color: #fff !important;
    border-width: 2px;
    border-radius: 8px;
    border-color: var(--fs-primary-600);
    border-style: solid;
    cursor: pointer;

    span>i {
      font-size: .6rem;
      margin-left: 5px;
    }

    &:hover {
      background: #fff;
      color: var(--fs-primary-600) !important;
      border-width: 2px;
      border-color: var(--fs-primary-600);
      border-style: solid;
    }
  }

  .bg-cstm-info-alt {
    background: var(--fs-grid-primary);
  }

  .bg-only-cstm-info {
    background: var(--fs-primary-500);
  }

  .mtn-pack {
    border: 2px solid var(--fs-group-grid);

    background: var(--fs-group-grid);
    color: #fff;

    border-radius: 10px;
  }


  .accelerator-container {
    input {
      border-top-right-radius: 0px !important;
      border-bottom-right-radius: 0px !important;
    }

    span {
      background: var(--fs-primary-500);
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;

      height: 38px;
    }
  }

  .coffret-qte-container {

    input {
      border-radius: 0px !important;
    }

    span:first-child {
      background: var(--fs-primary-500);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;

      height: 31.9px;
    }

    span:last-child {
      background: var(--fs-primary-500);
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;

      height: 31.9px;
    }
  }

  #packsParMembre {
    ::ng-deep .k-grid.fs-grid .k-grid-footer tr td {
      color: black !important;
      background-color: var(--fs-primary-200) !important;
    }
  
    ::ng-deep .k-grid.fs-grid.fs-grid-white .k-grid-header .k-header {
      border-bottom: 3px solid var(--fs-grid-primary) !important;
    }
  
    ::ng-deep .k-grid.fs-grid.fs-grid-white tr th:first-child {
      border-left: 1px solid var(--fs-grid-primary);
    }
  }

  // ? NAV TABS OVERRIDE
  #pack-tab-nav-commande-pack {

    ::ng-deep .nav-tabs {
      width: 100% !important;
      border-bottom: none !important;
      gap: 0px !important;
      padding: 0 !important;
      margin: 0 !important;
      border-bottom: 2px solid var(--fs-grid-primary) !important;
    }

    ::ng-deep .nav-tabs .nav-link,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      font-weight: 800 !important;
      padding: 6px 8px !important;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      position: relative !important;
      border-bottom: 2px solid var(--fs-grid-primary);

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 80%;
        margin-left: -10px;
        background-color: var(--fs-grid-primary);
      }
    }

    ::ng-deep .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      background-color: var(--fs-grid-primary) !important;
      color: #fff !important;
      padding: 6px 8px !important;
      border-color: transparent !important;
      font-weight: 800 !important;
      margin: 0 !important;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;

      &::before {
        content: none;
      }
    }

    .nav-tabs .nav-link.pack-success {
      color: #5E9E71 !important;
    }

    .nav-tabs .nav-link.active.pack-success,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #5E9E71 !important;
      border: 1px solid #5E9E71 !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }

    .nav-tabs .nav-link.pack-error {
      color: #C24B4B !important;
    }

    .nav-tabs .nav-link.active.pack-error,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #C24B4B !important;
      border: 1px solid #C24B4B !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }
  }

  #pack-tab-nav-commande {

    ::ng-deep .nav-tabs {
      width: 100% !important;
      border-bottom: none !important;
      gap: 0px !important;
      padding: 0 !important;
      margin: 0 !important;
      border-bottom: 2px solid var(--fs-grid-primary) !important;
    }

    ::ng-deep .nav-tabs .nav-link,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      font-weight: 800 !important;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      position: relative !important;
      border-bottom: 2px solid var(--fs-grid-primary);

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 80%;
        margin-left: -10px;
        background-color: var(--fs-grid-primary);
      }
    }

    ::ng-deep .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      background-color: var(--fs-grid-primary) !important;
      color: #fff !important;
      border-color: transparent !important;
      font-weight: 800 !important;
      margin: 0 !important;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;

      &::before {
        content: none;
      }
    }

    .nav-tabs .nav-link.pack-success {
      color: #5E9E71 !important;
    }

    .nav-tabs .nav-link.active.pack-success,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #5E9E71 !important;
      border: 1px solid #5E9E71 !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }

    .nav-tabs .nav-link.pack-error {
      color: #C24B4B !important;
    }

    .nav-tabs .nav-link.active.pack-error,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #C24B4B !important;
      border: 1px solid #C24B4B !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }
  }

  #pack-tab-nav-commande-groupe {

    ::ng-deep .nav-tabs {
      width: 100% !important;
      border-bottom: none !important;
      gap: 0px !important;
      padding: 0 !important;
      margin: 0 !important;
      border-bottom: 2px solid var(--fs-grid-primary) !important;
    }

    ::ng-deep .nav-tabs .nav-link,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      font-weight: 800 !important;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      position: relative !important;
      border-bottom: 2px solid var(--fs-grid-primary);

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 80%;
        margin-left: -10px;
        background-color: var(--fs-grid-primary);
      }
    }

    ::ng-deep .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      background-color: var(--fs-grid-primary) !important;
      color: #fff !important;
      padding: 6px 8px !important;
      border-color: transparent !important;
      font-weight: 800 !important;
      margin: 0 !important;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;

      &::before {
        content: none;
      }
    }

    .nav-tabs .nav-link.pack-success {
      color: #5E9E71 !important;
    }

    .nav-tabs .nav-link.active.pack-success,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #5E9E71 !important;
      border: 1px solid #5E9E71 !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }

    .nav-tabs .nav-link.pack-error {
      color: #C24B4B !important;
    }

    .nav-tabs .nav-link.active.pack-error,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #C24B4B !important;
      border: 1px solid #C24B4B !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }
  }

  #pack-tab-nav-commande-reference {

    ::ng-deep .nav-tabs {
      width: 100% !important;
      border-bottom: none !important;
      gap: 0px !important;
      padding: 0 !important;
      margin: 0 !important;
      border-bottom: 2px solid var(--fs-grid-primary) !important;
    }

    ::ng-deep .nav-tabs .nav-link,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      font-weight: 800 !important;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      position: relative !important;
      border-bottom: 2px solid var(--fs-grid-primary);

      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 80%;
        margin-left: -10px;
        background-color: var(--fs-grid-primary);
      }
    }

    ::ng-deep .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
      margin: 0 !important;
      padding: 6px 8px !important;
      background-color: var(--fs-grid-primary) !important;
      color: #fff !important;
      border-color: transparent !important;
      font-weight: 800 !important;
      margin: 0 !important;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;

      &::before {
        content: none;
      }
    }

    .nav-tabs .nav-link.pack-success {
      color: #5E9E71 !important;
    }

    .nav-tabs .nav-link.active.pack-success,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #5E9E71 !important;
      border: 1px solid #5E9E71 !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }

    .nav-tabs .nav-link.pack-error {
      color: #C24B4B !important;
    }

    .nav-tabs .nav-link.active.pack-error,
    .nav-tabs .nav-item.show .nav-link {
      color: #fff !important;
      background-color: #C24B4B !important;
      border: 1px solid #C24B4B !important;
      border-bottom-width: 2px;
      font-weight: 600;
      border-top-left-radius: 6px !important;
      border-top-right-radius: 6px !important;
    }
  }

}

.col-cstm-radius {
  border-radius: 10px;
}


.bg-cstm-danger {
  color: #A93535;
}

.bg-cstm-inactive {
  opacity: 0.6;
  color: #ccc;
}

.form-check>label,
.form-control {
  font-size: 1rem;
  font-weight: 600;
  color: black;
}

.form-control {
  border-radius: var(--winoffre-base-border-radius) !important;
}

.tabs-separate {
  background: #f0f2f5;
  width: 20px;
  flex: none !important;

  @media screen and (max-width: 768px) {
    display: none;

  }
}

kendo-multiselect {
  border: 1px solid #ddd !important;
  min-height: 32px !important;
  padding: 5px 2px !important;
  background: transparent
}

// ::ng-deep #palier-badge {

//   .bg-success,
//   .bg-success:hover,
//   .bg-success:focus {
//     color: #fff;
//     font-size: 14px;
//     font-weight: 700;
//     border-radius: 10px;
//     background: var(--wf-primary-400) !important;
//   }
// }

.card-bloc-radius {
  border-radius: 10px;
}

// ? Sélectionner un supporteur

.table-container {
  max-height: 200px;
  overflow-y: auto;

  scrollbar-width: thin !important;
  scrollbar-color: var(--fs-secondary-light) white !important;
  border-radius: var(--winoffre-base-border-radius)
}

.table-row {
  display: flex;
  margin-bottom: 8px;
  cursor: pointer;
  background: var(--fs-member-bg);
  border-radius: 10px;
  align-items: center;
}

.table-cell {
  display: flex;
  align-items: center;

  flex: 1;
  padding: 5px 10px;

  color: black;
  font-weight: 600;
  width: 170px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-cell:last-child {
  border-bottom: none;
}

.selected-member {
  background: var(--fs-group-bg) !important;

  .table-cell {
    color: #fff;
  }
}

// ? Nav-tabs override
#pack-tab-nav-alt {
  ::ng-deep .nav-tabs {
    width: auto !important;
    border: 1px solid #ccc;
    border-bottom: none !important;
    gap: 0px !important;
    padding: 0 !important;
    margin: 0 !important;
    border-top-left-radius: 0.25rem !important;
    border-top-right-radius: 0.25rem !important;
  }

  ::ng-deep .nav-tabs .nav-link,
  .nav-tabs .nav-item.show .nav-link {
    margin: 0;
    background-color: #ddd;
    border: 1px solid #ccc !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
    height: 100%;
  }

  ::ng-deep .nav-tabs .nav-link.active,
  .nav-tabs .nav-item.show .nav-link {
    background-color: transparent !important;
    color: black !important;
    border-color: transparent !important;
    font-weight: 600;
    margin: 0 !important;
  }

  .nav-tabs .nav-link.pack-success {
    color: #5E9E71 !important;
  }

  .nav-tabs .nav-link.active.pack-success,
  .nav-tabs .nav-item.show .nav-link {
    color: #5E9E71 !important;
    border-color: #5E9E71 !important;
    border-bottom-width: 2px;
    font-weight: 600;
  }

  .nav-tabs .nav-link.pack-error {
    color: #C24B4B !important;
  }

  .nav-tabs .nav-link.pack-warning {
    color: #EE8245 !important;
  }


  .nav-tabs .nav-link.active.pack-error,
  .nav-tabs .nav-item.show .nav-link {
    color: #C24B4B !important;
    border-color: #C24B4B !important;
    border-bottom-width: 2px;
    font-weight: 600;
  }

  .nav-tabs .nav-link.active.pack-warning,
  .nav-tabs .nav-item.show .nav-link {
    color: #EE8245 !important;
    border-color: #EE8245 !important;
    border-bottom-width: 2px;
    font-weight: 600;
  }
}

::ng-deep .status-switch-align.k-switch-md .k-switch-track {
  width: 100px !important;
}

.bg-cstm-info {
  background: var(--wf-primary-400);
}

.bg-cstmm-info-wf {
  background: var(--wf-secondary);
}

.bg-cstmm-info-fs {
  background: var(--fs-primary-600);
}

::ng-deep .info-popover-container-wf {
  background-color: var(--wf-primary-400);
  border-color: var(--wf-primary-500);
  width: auto;
  max-width: 150px !important;
}

::ng-deep .info-popover-container-fs {
  background-color: var(--fs-primary-500);
  border-color: var(--fs-primary-600);
  width: auto;
  max-width: 150px !important;
}

::ng-deep .popover-body {
  color: #fff !important;
  font-weight: 700;
}


// ? Conditions Sélectionnées

.selected-conditions-container {
  background: #EAEDF5;
  overflow-y: auto;
  overflow-x: hidden;

  border-radius: 10px;
  padding: 5px;

  max-height: 250px;
}

.selected-item-container {
  color: #fff;
  font-weight: 600;
  border-radius: 10px;
  background: var(--fs-group-bg);

  i {
    position: absolute;
    right: 20px;
  }
}

::ng-deep .badQte .columnQte {
  background: transparent;
  padding: 0;
  border-radius: 0;

  span {
    background: rgba(255, 165, 0, 0.4) !important;
  }
}

.opacity-light {
  opacity: 0.4;
}

#produitsParMembre,
#produitsCmdUnitaire,
#produitsCommande,
#packsParMembre {
  ::ng-deep .fs-grid .k-grid-toolbar {
    background-color: transparent;
  }
}

.attachements-container {
  padding: 4px 0px;
  position: absolute;
  top: 0;
  left: 0;
}

.attachement-btn {
  font-weight: 600;
  font-size: 1rem;
  border-radius: 10px;
}

.attachement-row {
  border-radius: 10px;
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

#WIN_GROUPE-container {
  .attachement-row:hover {
    background: var(--wf-primary-50);
  }

  .attachment-item {
    height: 70px;
    width: 70px;
    background: var(--wf-primary-100);
    border-radius: 10px;

    i {
      color: var(--wf-primary-400);
      font-size: 2rem;
    }
  }
}


#FEDERATION_SYNDICAT-container {
  .attachement-row:hover {
    background: var(--fs-primary-50);
  }

  .attachment-item {
    height: 70px;
    width: 70px;
    background: var(--fs-primary-100);
    border-radius: 10px;

    i {
      color: var(--fs-primary-400);
      font-size: 2rem;
    }
  }
}

#WIN_OFFRE-container {
  .attachement-row:hover {
    background: var(--wo-primary-50);
  }

  .attachment-item {
    height: 70px;
    width: 70px;
    background: var(--wo-primary-100);
    border-radius: 10px;

    i {
      color: var(--wo-primary-400);
      font-size: 2rem;
    }
  }
}

.attachement-btn,
.attachement-btn:hover {
  color: darken($primary, 10);
  background-color: lighten($primary, 45);
  border: 1px solid lighten($primary, 15);
}

.download-btn {
  color: white;
  font-size: 1rem;
  border-radius: 10px;
  background-color: transparent;
  border: 2px solid white;
}

.input-shell {
  border-radius: 10px;
  width: 150px;
  border: 1px solid #ccc
}

.synthese-transition {
  transition: all 0.3s ease-in-out;
}

.icon-bounce {
  animation: bounce 0.5s infinite alternate;
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-2px);
  }
}