.active-icon {
  color: var(--primary);
}


#offre-header-container {

  .nav-tabs .nav-link.active.pack-error,
  .nav-tabs .nav-item.show .nav-link {
    color: #fff !important;
    background-color: #C24B4B !important;
    border: 1px solid #C24B4B !important;
    border-bottom-width: 2px;
    font-weight: 600;
  }

  .nav-tabs .nav-link.active,
  .nav-tabs .nav-item.show .nav-link {
    background: transparent !important;
    color: black !important;
    border-color: transparent !important;
    border-bottom-width: 2px;
    font-weight: 600;
    margin: 0 !important;
    border-top-left-radius: var(--winoffre-base-border-radius);
    border-top-right-radius: var(--winoffre-base-border-radius);
  }

  .nav-tabs .nav-link.active.pack-success,
  .nav-tabs .nav-item.show .nav-link {
    color: #fff !important;
    background-color: #4fba6f !important;
    font-weight: 600;
  }
}

.bg-motif {
  background: #e9797918 !important;
  border: 1px solid #e97979 !important;
  color: darken(#e97979, 20);

}

.bg-message-cmd {
  border: 1px solid #595959 !important;
}

.bg-message-cmd-alt {
  position: absolute;
  bottom: 0;
  width: 100%;
}

.text-sub {
  font-size: .85em;
  color: #6b727d;
}


.p-badge {
  padding-block: 13px !important;
}

.text-success-alt {
  color: #1db198 !important;
}

.bg-success-alt {
  color: #fff;
  background: #0f8fa3 !important;
}

.b-radius {
  border-radius: 20px;
}

.bg-conditions {
  color: #fff !important;
  font-size: 14px;
  font-weight: 700;
  background: var(--wo-primary-400) !important;
}

.bg-see-more {
  color: #fff !important;
  font-size: 14px;
  font-weight: 700;
  background: var(--wo-primary-600) !important;
}

.row-border {
  border-top: 1px solid #d8e1ef;
}

.rows-divider> :not(:nth-last-child(-n+2)) {
  border-bottom: 1px solid #d8e1ef;
}

.rows-divider-alt {
  border-bottom: 1px solid #d8e1ef;
}

.fixed-synthese {
  border-radius: var(--winoffre-base-border-radius);
  position: fixed;
  bottom: 0;
  z-index: 3;
}

.conditions-row>*:nth-child(n+4) {
  display: none !important;
}

img.img-fluid-header {
  width: 100%;
  max-width: 400px;
  height: auto;
  object-fit: contain;
  border-radius: var(--winoffre-base-border-radius);
}

.form-check>label,
.form-control {
  font-size: 1rem;
  font-weight: 600;
  color: black;
}

.form-control {
  border-radius: var(--winoffre-base-border-radius) !important;
}

#pack-tab-nav-commande {

  ::ng-deep .nav-tabs {
    width: 100% !important;
    border-bottom: none !important;
    gap: 0px !important;
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: 2.5px solid var(--wo-primary-400) !important;
  }

  ::ng-deep .nav-tabs .nav-link,
  .nav-tabs .nav-item.show .nav-link {
    margin: 0 !important;
    height: 100%;
    font-weight: 800 !important;
    padding: 6px 8px !important;
    position: relative !important;
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
    border-bottom: 2px solid var(--wo-primary-400) !important;

    &::before {
      content: '';
      display: block;
      position: absolute;
      width: 2px;
      height: 80%;
      margin-left: -10px;
      background-color: var(--wo-primary-400);
    }
  }

  .actions-icons-ov {
    background: var(--wo-secondary);
  }

  ::ng-deep .nav-tabs .nav-link.active,
  .nav-tabs .nav-item.show .nav-link {
    background-color: var(--wo-primary-400) !important;
    color: #fff !important;
    border-color: transparent !important;
    font-weight: 800 !important;
    padding: 6px 8px !important;
    height: 100%;
    margin: 0 !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;

    &::before {
      content: none;
    }
  }

  .nav-tabs .nav-link.pack-success {
    color: #4fba6f !important;
  }

  .nav-tabs .nav-link.active.pack-success,
  .nav-tabs .nav-item.show .nav-link {
    color: #fff !important;
    background-color: #4fba6f !important;
    border: 1px solid #4fba6f !important;
    border-bottom-width: 2px;
    font-weight: 600;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
  }

  .nav-tabs .nav-link.pack-warning {
    color: #EE8245 !important;
  }

  .nav-tabs .nav-link.active.pack-warning,
  .nav-tabs .nav-item.show .nav-link {
    color: #fff !important;
    background-color: #EE8245 !important;
    border: 1px solid #EE8245 !important;
    border-bottom-width: 2px;
    font-weight: 600;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
  }

  .nav-tabs .nav-link.pack-error {
    color: #C24B4B !important;
  }

  .nav-tabs .nav-link.active.pack-error,
  .nav-tabs .nav-item.show .nav-link {
    color: #fff !important;
    background-color: #C24B4B !important;
    border: 1px solid #C24B4B !important;
    border-bottom-width: 2px;
    font-weight: 600;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
  }
}

.card-border-alt {
  border-top-width: 0px !important;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

button {
  background: none;
  border: none;
  font: inherit;
  color: inherit;
  cursor: pointer;
  outline: inherit;
}

#wo-bloc-produit {
  .bg-cstm-info {
    background: var(--wo-secondary);
  }

  .bg-only-cstm-info {
    background: var(--wo-secondary);
  }

  .bg-cstm-info-alt {
    background: var(--wo-primary-300);
  }

  .btn-ajouter {
    color: var(--wo-primary-400);
    border-radius: 10px;
  }

  .bg-cstm-danger {
    color: #A93535;
  }

  .bg-cstm-inactive {
    opacity: 0.6;
    color: #ccc;
  }

  .opacity-light {
    opacity: 0.4;
  }

  .mtn-pack {
    border: 2px solid var(--wo-primary-400);
    border-top: none !important;

    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
  }

  .produits-card {
    border: 1px solid var(--wo-primary-400) !important;
  }

  img.img-fluid-dataItem {
    width: 70px !important;
    height: 60px !important;
    object-fit: cover;
    border-radius: var(--winoffre-base-border-radius);
  }

  .card-border-bg-primary {
    border-radius: 10px;
    border: 1px solid var(--wo-primary-400) !important;

    .card-header {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      background: var(--wo-primary-400) !important;
      color: #fff;
      padding: 0.3rem 1rem !important;
      font-size: 1.2rem;
      font-weight: 700;
      border: 1px solid var(--wo-primary-400) !important;
    }
  }

  #produitsCommande {
    padding: 0 !important;
    margin: 0 !important;

    ::ng-deep .k-grid.fs-grid {
      border-top-right-radius: 6px !important;
      border-top-left-radius: 6px !important;
    }

    ::ng-deep .fs-grid .k-grid-toolbar {
      background-color: transparent !important;
      border-bottom: 2px solid var(--wo-primary-400) !important;
    }

    ::ng-deep .k-grid.fs-grid.fs-grid-white {
      border: 2px solid var(--wo-primary-400) !important;
    }

    ::ng-deep .k-grid.fs-grid.fs-grid-white .k-grid-header .k-header {
      border-bottom: 3px solid var(--wo-primary-400) !important;
      background-color: #fff;
      color: #000;
      padding-block: 12px;
    }

    ::ng-deep .k-grid.fs-grid.fs-grid-white .k-master-row td {
      border-bottom: 2px solid var(--wo-primary-400) !important;

    }

    ::ng-deep .k-grid.fs-grid.fs-grid-white tr th {
      border-right: 2px solid var(--wo-primary-400) !important;

    }

    ::ng-deep .k-grid.fs-grid.fs-grid-white tr th:first-child {
      border-right: 2px solid var(--wo-primary-400) !important;
      border-left: none !important;
    }

    ::ng-deep .k-grid.fs-grid.fs-grid-white tr th:last-child {
      border-top-right-radius: 0 !important;
      border-right: none !important;
    }

    ::ng-deep .k-grid.fs-grid.fs-grid-white tr td:first-child {
      border-top-left-radius: 0 !important;
      border-right: 2px solid var(--wo-primary-400) !important;
      border-left: none !important;
    }

    ::ng-deep .k-grid.fs-grid.fs-grid-white tr td:last-child {
      border-top-right-radius: 0 !important;
      border-right: none !important;
    }

    ::ng-deep .k-grid.fs-grid.fs-grid-white tr td {
      border-right: 2px solid var(--wo-primary-400) !important;
    }
  }
}

::ng-deep .increase-popover-width {
  background-color: rgb(255, 255, 255) !important;
  border-color: rgb(128, 39, 39);
  border-radius: 10px !important;
  width: auto !important;
  font-size: .9rem;
  z-index: 98 !important;
  
  .popover-body {
    color: rgb(169, 53, 53);
  }
}

::ng-deep .info-popover-container {
  background-color: rgba(106, 156, 137, 0.9);
  border-color: rgb(106, 156, 137);
  width: auto;
  max-width: 150px !important;
}

::ng-deep .popover-body {
  color: #fff;
  font-weight: 700;
}

.card-border-cstm-wo {
  border: 3px solid var(--wo-primary-400) !important;
}

.card-radius {
  border-radius: 10px !important;
}

.bg-see-more {
  color: #fff;
  border-radius: 10px;
  background: var(--wf-primary-400) !important;
}

.bg-see-more-wo {
  color: #fff;
  border-radius: 10px;
  background-color: #6A9C89 !important;
}

.conditions-row-wo {
  .bg-conditions {
    color: #fff !important;
    font-size: 14px;
    font-weight: 700;
    background: #6A9C89 !important;
  }
}

.conditions-row-wo.slice>*:nth-child(n+4),
.conditions-row.slice>*:nth-child(n+4) {
  display: none !important;
}

.accelerator-container {
  input {
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
  }

  span {
    background: var(--wo-primary-700);
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;

    height: 38px;
  }
}

.coffret-qte-container {

  input {
    border-radius: 0px !important;
  }

  span:first-child {
    background: var(--wo-primary-700);
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;

    height: 31.9px;
  }

  span:last-child {
    background: var(--wo-primary-700);
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;

    height: 31.9px;
  }
}

.bg-cstm-info-synthese {
  background: var(--wo-primary-400);
  color: #fff !important;
  border-width: 2px;
  border-color: var(--wo-primary-600);
  border-style: solid;
  cursor: pointer;

  &:hover {
    background: #fff;
    color: var(--wo-primary-400) !important;
    border-width: 2px;
    border-color: var(--wo-primary-600);
    border-style: solid;
  }
}

.col-cstm-radius {
  min-width: 130px;
  border-radius: 10px;
}

.card-btm-radius {
  border-bottom-left-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
}

.count-down-container {
  font-weight: 600;
  color: #5E9E71;
}

.count-down-will-expire {
  font-weight: 600;
  color: #c43030;
}

.mini-counter-cnt {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 55px;
  height: 45px;
  border-radius: 10px;
  background: var(--wo-primary-400);
  color: #fff;

  p {
    line-height: 1em;

    &>span:first-child {
      font-size: 1em;
      font-weight: 800;
    }

    &>span:last-child {
      font-weight: 400;
      font-size: 0.7em;
    }
  }

  ;

}

.btn-statistiques {
  font-weight: 600;
  border-radius: 8px;
  background: var(--wo-secondary);
}

.bg-cstm-info-fs {
  margin: 5px 0;
  font-weight: 600;
  background: #8B5AB0;
  color: #fff !important;
  border-width: 2px;
  border-radius: 8px;
  border-color: #8B5AB0;
  border-style: solid;
  cursor: pointer;

  &:hover {
    background: #fff;
    color: #8B5AB0 !important;
    border-width: 2px;
    border-color: #8B5AB0;
    border-style: solid;
  }
}

.bg-cstm-info-wo {
  margin: 5px 0;
  font-weight: 800;
  background: var(--wo-secondary);
  color: #fff !important;
  border-width: 2px;
  border-radius: 8px;
  border-color: var(--wo-secondary);
  border-style: solid;
  cursor: pointer;

  &:hover {
    background: #fff;
    color: var(--wo-secondary) !important;
    border-width: 2px;
    border-color: var(--wo-secondary);
    border-style: solid;
  }
}

.synthese-transition {
  transition: all 0.3s ease-in-out;
}

.icon-bounce {
  animation: bounce 0.5s infinite alternate;
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-2px);
  }
}

.motif-refus-container {
  padding: 5px 10px;
  margin-bottom: -2px;
  border-radius: 10px;
  background: rgba(210, 0, 0, 0.1);

  i {
    color: #A93535;
  }
}