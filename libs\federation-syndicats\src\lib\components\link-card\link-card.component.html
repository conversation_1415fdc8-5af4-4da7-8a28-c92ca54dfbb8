<div (click)="navigateToPage()" class="card shadow-sm h-100 pointer-cus {{ badgeValue ? 'p-0' : 'p-1'}} simple-value-card">
    <div *ngIf="badgeValue" class="card-badge">
        <div class="row">
            <div class="col-12">
                <i class="mdi mdi-information"></i>
                <span class="mx-1">{{ badgeValue }} Nouvelles Offres</span>
            </div>
        </div>
    </div>
    <div *ngIf="!singleLine; else: multiLine" class="row pb-2">
        <span class="col-12 text-center title">{{ label  }}</span>

        <span class="col-12 text-center value">{{ value | number }}</span>
    </div>

    <ng-template #multiLine>
        <div class="row pb-4">
            <span class="col-sm-8 col-12 text-left title-alt">{{ label }}</span>

            <span class="col-sm-4 col-12 text-center value-alt align-items-center">{{ value | number }}</span>
        </div>
    </ng-template>
    

    <div class="row w-100" style="position: absolute; bottom: 0">
        <div class="col-12 d-flex justify-content-end">
            <span class="text-success link-text d-flex align-items-center">
                <span>{{ linkLabel | titlecase }}</span>
                <i class="mdi mdi-arrow-right mx-1"></i>
            </span>
        </div>
    </div>
</div>