import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { Reclamation } from '../models/reclamation';
import { ReclamationCriteria } from '../models/reclamation';
import { Pagination } from '../models/reclamation';
import { ReclamationList } from '../models/reclamation';

@Injectable({
    providedIn: 'root'
})
export class ReclamationsServiceTest {
    private reclamations: Reclamation[] = []; // In-memory array to store Reclamations
    private idCounter = 1; // To simulate auto-increment IDs

    constructor() {
        // Initialize with some mock data
        this.reclamations = [
            {
                id: this.idCounter++,
                dateCreation: '2023-10-01 10:00:00',
                dateModification: null,
                dateSuppression: null,
                dateCloture: null,
                typeReclamationId: 'TR1',
                statut: 'N',
                codeSite: 1,
                codeClient: 1001,
                ville: 'Casablanca',
                raisonSociale: 'Pharmacie ABC',
                nomPharmacien: '<PERSON>',
                message: 'This is a sample message',
                reponse: null,
                clientId: '1001',
                managerId: null,
                userSuppressionId: null,
            },
            // Add more mock Reclamations as needed
        ];
    }

    createOrEdit(reclamation: Reclamation): Observable<Reclamation> {
        if (reclamation.id) {
            // Edit existing Reclamation
            const index = this.reclamations.findIndex(r => r.id === reclamation.id);
            if (index !== -1) {
                this.reclamations[index] = { ...this.reclamations[index], ...reclamation };
            }
        } else {
            // Create new Reclamation
            reclamation.id = this.idCounter++;
            this.reclamations.push(reclamation);
        }
        return of(reclamation);
    }

    cancelOrCheck(id: number): Observable<Reclamation> {
        // For mock purposes, let's find the Reclamation and change its statut to 'S' (Sans suite)
        const reclamation = this.reclamations.find(r => r.id === id);
        if (reclamation) {
            reclamation.statut = 'S';
            reclamation.dateModification = new Date().toISOString();
        }
        return of(reclamation);
    }

    // getAllReclamation(criteria: ReclamationCriteria, pagination: Pagination): Observable<ReclamationList> {
    //     // For mock purposes, filter the in-memory array based on criteria
    //     let filteredReclamations = [...this.reclamations];

    //     if (criteria.typeReclamationId) {
    //         filteredReclamations = filteredReclamations.filter(r => r.typeReclamationId === criteria.typeReclamationId);
    //     }

    //     if (criteria.statut) {
    //         filteredReclamations = filteredReclamations.filter(r => r.statut === criteria.statut);
    //     }

    //     if (criteria.dateDu) {
    //         const startDate = new Date(criteria.dateDu);
    //         filteredReclamations = filteredReclamations.filter(r => new Date(r.dateCreation) >= startDate);
    //     }

    //     if (criteria.dateAu) {
    //         const endDate = new Date(criteria.dateAu);
    //         filteredReclamations = filteredReclamations.filter(r => new Date(r.dateCreation) <= endDate);
    //     }

    //     // Implement sorting
    //     if (pagination.sortField && pagination.sortMethod) {
    //         filteredReclamations.sort((a, b) => {
    //             if (a[pagination.sortField] < b[pagination.sortField]) return pagination.sortMethod === 'asc' ? -1 : 1;
    //             if (a[pagination.sortField] > b[pagination.sortField]) return pagination.sortMethod === 'asc' ? 1 : -1;
    //             return 0;
    //         });
    //     }

    //     // Implement pagination
    //     const totalElements = filteredReclamations.length;
    //     const startIndex = pagination.skip * pagination.pageSize;
    //     const endIndex = startIndex + pagination.pageSize;
    //     const pageData = filteredReclamations.slice(startIndex, endIndex);

    //     const reclamationList: ReclamationList = {
    //         content: pageData,
    //         totalElements: totalElements,
    //     };

    //     return of(reclamationList);
    // }
}