import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { FormGroup, FormControl, FormBuilder } from "@angular/forms";
import { CellClickEvent, GridDataResult } from "@progress/kendo-angular-grid";
import { SortDescriptor } from "@progress/kendo-data-query";
import { OffresService, Pagination } from "@wph/data-access";
import { ExportPdf, ExportPdfService } from "@wph/web/shared";
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from "rxjs";
import { Router } from "@angular/router";
import { SocieteType } from "@wph/shared";
import { EntrepriseCriteriaDto, EntrepriseDTO } from "../../../models/entreprise.model";


@Component({
    selector: 'wph-liste-fournisseurs',
    templateUrl: './liste-fournisseurs.component.html',
    styleUrls: ['./liste-fournisseurs.component.scss']
})
export class ListeFournisseursComponent implements OnInit, OnD<PERSON>roy {
    unsubscribe$: Subject<boolean> = new Subject<boolean>();
    gridView: GridDataResult = { total: 0, data: [] };
    pharmacieSort: SortDescriptor[];
    editForm: FormGroup;
    isFormReady = false;
    filterList: FormControl = new FormControl();
    searchCriteria: EntrepriseCriteriaDto;

    navigation: Pagination = {
        skip: 0,
        pageSize: 15,
        sortField: null,
        sortMethod: null,
        totalElements: 0,
        last: false,
        unpaged: false,
    };

    selectTypeEntr = [
        { label: 'Fabriquant', value: 'FABRIQUANT' },
        { label: 'Grossiste', value: 'GROSSISTE' },
    ];

    displayFilter: boolean;
    filterForm: FormGroup;

    exportPdfRef: ExportPdf;

    constructor(
        private router: Router,
        private fb: FormBuilder,
        private offresService: OffresService,
        private exportPdfServ: ExportPdfService,
    ) {
        this.initFilterGroup();
    }

    ngOnInit(): void {
        this.buildExport();
        this.searchCriteria = new EntrepriseCriteriaDto({
            typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE, SocieteType.SOCIETE]
        });

        this.searchSociete();

        this.listenToSearchFilterChanges();
    }

    searchSociete(): void {
        this.offresService.searchSociete(this.searchCriteria, this.navigation).subscribe(res => {
            this.gridView = {
                data: res?.content,
                total: res?.totalElements
            };

            this.exportPdfRef.setData(this.gridView?.data);
        });
    }

    initFilterGroup(): void {
        this.filterForm = this.fb.group({
            code: new FormControl(null),
            nomResponsable: new FormControl(null),
            ville: new FormControl(null),
            localite: new FormControl(null),
            email: new FormControl(null),
            typeEntreprise: new FormControl(null)
        });
    }

    buildExport(): void {
        this.exportPdfRef = this.exportPdfServ
            .ref<EntrepriseDTO>()
            .setTitle('Liste des Fournisseurs')
            .addColumn('code', 'Code', { width: 60 })
            .addColumn('raisonSociale', 'Raison Sociale', { width: 170 })
            .addColumn('*', 'Ville / Localité', {
                width: 150,
                transform: (dataItem) => dataItem?.ville || dataItem?.localite || 'Indisponible'
            })
            .addColumn('email', 'E-mail', { transform: (value) => value ?? 'Indisponible' })
            .addColumn('telephone', 'Téléphone', { transform: (value) => value ?? 'Indisponible' })
            .addColumn('typeEntreprise', 'Type Entreprise')

        // Set the data for the export
        this.exportPdfRef.setData(this.gridView.data);
    }

    filterListe(searchQuery: string) {
        const criteria = {
            raisonSociale: searchQuery,
            typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE, SocieteType.SOCIETE],
        };

        return this.offresService.searchSociete(criteria);
    }

    cellClickHandler(event: CellClickEvent): void {
        if (event?.column?.title !== 'Action') {
            this.consulterFournisseur(event?.dataItem?.id, true);
        }
    }

    consulterFournisseur(id: number, readOnly: boolean): void {
        this.router.navigate(['achats-groupes/gestion-fournisseurs/edit', id], { queryParams: { readOnly } });
    }

    ajouterFournisseur(): void {
        this.router.navigate(['achats-groupes/gestion-fournisseurs/edit'], { queryParams: { readOnly: false } });
    }

    listenToSearchFilterChanges(): void {
        this.filterList.valueChanges
            .pipe(
                takeUntil(this.unsubscribe$),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe((res: string) => {
                if (res?.length > 1) {
                    this.filterListe(res).subscribe(ret => {
                        this.gridView = { data: ret?.content, total: ret?.totalElements };
                    });
                } else {
                    this.filterListe(null).subscribe(ret => {
                        this.gridView = { data: ret?.content, total: ret?.totalElements };
                    });
                }
            });
    }

    pharmacieGridSort(sort: SortDescriptor[]) {
        this.pharmacieSort = sort;
        if (
            this.pharmacieSort &&
            this.pharmacieSort.length > 0 &&
            this.pharmacieSort[0].dir
        ) {
            this.navigation.sortField = sort[0].field;
            this.navigation.sortMethod = sort[0].dir;
        } else {
            this.navigation.sortField = null;
            this.navigation.sortMethod = null;
        }
        this.searchSociete();
    }

    appliquerFiltre(): void {
        const formValues = this.filterForm.getRawValue();

        this.navigation.skip = 0;
        this.searchCriteria = { ...this.searchCriteria, ...formValues };

        this.searchSociete();
    }

    vider(): void {
        this.filterForm.reset();

        this.navigation.skip = 0;
        this.searchCriteria = new EntrepriseCriteriaDto({ typeEntreprises: [SocieteType.FABRIQUANT, SocieteType.GROSSISTE, SocieteType.SOCIETE] });

        this.searchSociete();
    }

    onPageChange($event: any): void {
        this.searchSociete();
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }
}