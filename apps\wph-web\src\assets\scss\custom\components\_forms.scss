//
// forms.scss
//

// Form-control light
.form-control-light {
    background-color: $gray-100 !important;
    border-color: $gray-100 !important;
}


// Form elements (Color and Range)
input.form-control[type="color"],
input.form-control[type="range"] {
    min-height: 39px;
}


// Custom select
.custom-select.is-invalid, .form-control.is-invalid,.custom-select:invalid, .form-control:invalid,
.custom-select.is-valid, .form-control.is-valid, .custom-select:valid,.form-control:valid {
    &:focus {
        box-shadow: none !important;
    }
}

select.form-control {
    &:not([size]):not([multiple]) {
        height: $input-height;
    }
}

select.form-control-sm {
&:not([size]):not([multiple]) {
    height: $input-height-sm;
}
}

// .form-control {

// border:0;
// border-bottom: 1px solid #dee2e6;
// padding-left: 0;
// font-weight: bold;

// }
// Show Password
.password-eye {
  &:before {
      font-family: "Material Design Icons";
      content: "\F06D0";
      font-style: normal;
      font-weight: 400;
      font-variant: normal;
      vertical-align: middle;
      line-height: 1.2;
      font-size: 16px;
  }
}
.password-eye-off {
  &:before {
      font-family: "Material Design Icons";
      content: "\F06D1";
      font-style: normal;
      font-weight: 400;
      font-variant: normal;
      vertical-align: middle;
      line-height: 1.2;
      font-size: 16px;
  }
}

#WIN_OFFRE-container {
    .form-control:disabled, .form-control[readonly] {
        background: #f3f3f3;
    }

    .form-check-input {
        background: #f3f3f3;
    }
}
