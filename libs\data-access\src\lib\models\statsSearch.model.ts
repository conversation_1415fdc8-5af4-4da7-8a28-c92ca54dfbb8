import { EntrepriseDTO } from "libs/federation-syndicats/src/lib/models/entreprise.model";
import { Fournisseur } from "./fournisseur.model";
import { Offre } from "./offre.model";

export interface StatsSearch {
  codeClient?: string;
  commercialId?: number;
  client?: EntrepriseDTO;
  dateCommande?: Date;
  dateFin?: Date;
  offre?: Offre;
  distributeurId?: number;
  distributeur?: Fournisseur;
  localite?: string;
  nomPharmacie?: string;
  numeroCommande?: string;
  offreId?: number;
  offreurId?: number;
  offreur?: Fournisseur;
  raisonSociale?: string;
  statut?: string;
  ville?: string;
}
