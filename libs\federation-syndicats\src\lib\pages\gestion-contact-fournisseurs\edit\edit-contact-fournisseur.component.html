<!-- start page title -->
<div class="rowline mb-0">
    <div class="page-title-box row">

        <ng-container *ngIf="!isReadOnly">
            <div class="d-flex  align-items-center col-4 k-gap-4">
                <button class="actions-icons action-back btn text-white" (click)="back()">
                    <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
                </button>
                <h4 class="page-title fw-4 ps-2">{{ contactId ? 'Modifier Contact Fournisseur' : 'Ajouter Contact
                    Fournisseur' }}
                </h4>
            </div>

            <div class="col-8 px-1">
                <div class="row d-flex justify-content-end align-items-center">
                    <button (click)="submitContactFournisseurGroupe()" style="padding-block: 6px;" class="btn btn-sm btn-success" title="Enregistrer Contact Fournisseur">
                        <i class="bi bi-bookmark-check-fill"></i>
                        Enregistrer
                    </button>

                    <button (click)="back()" type="button" style="padding-block: 6px;"
                        class="btn btn-sm btn-dark text-white m-1">
                        <i class="mdi mdi-close"></i>
                        Quitter
                    </button>
                </div>
            </div>
        </ng-container>

        <ng-container *ngIf="isReadOnly">
            <div class="d-flex  align-items-center col-auto k-gap-4">
                <button class="actions-icons action-back btn text-white" (click)="back()">
                    <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
                </button>
                <h4 class="page-title fw-4 ps-2 col-12">Consultation Contact Fournisseur</h4>
            </div>

            <div class="col-auto px-1">
                <div class="row d-flex justify-content-end align-items-center">
                    <button (click)="back()" type="button" style="padding-block: 6px;"
                        class="btn btn-sm btn-dark text-white m-1">
                        <i class="mdi mdi-close"></i>
                        Quitter
                    </button>
                </div>
            </div>
        </ng-container>
    </div>
</div>
<!-- end page title -->

<div class="row mx-2">
    <div class="card bg-transparent my-1 w-100">
        <form [formGroup]="editContactFournisseurForm" class="p-0 m-0" autocomplete="off">
            <ul ngbNav #infoPharamcieNav="ngbNav" class="nav-tabs  pharmacie-tab" style="gap: 0 !important;">
                <li [ngbNavItem]="1">
                    <a ngbNavLink class="w-100">
                        <span class="d-flex row align-items-center px-0 px-sm-2">
                            <b>Informations Générales</b>
                            <i *ngIf="submitted && editContactFournisseurForm?.invalid"
                                class="bi bi-exclamation-diamond-fill text-danger mx-2 fs-1"></i>
                            <i *ngIf="editContactFournisseurForm.valid"
                                class="bi bi-check-circle-fill text-success mx-2 fs-1"></i>
                        </span>
                    </a>

                    <ng-template ngbNavContent>
                        <div class="card-body px-1 bg-white mb-4 mb-sm-0">
                            <div class="form-row mx-0 p-1">
                                <div *ngIf="!contactId" class="col-md-4 my-1">
                                    <div class="form-group mb-0 mb-0">
                                        <label for="raisonSocialeFourn" class="form-label p-0 col-12">Raison Sociale Fournisseur <ng-container *ngIf="!isReadOnly">
                                            <span style="color: #b4b4b4; font-size: 0.85rem">(Obligatoire)</span> <span class="text-danger">*</span>
                                        </ng-container></label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="raisonSocialeFourn" [ngbTypeahead]="searchFournisseur"
                                                [inputFormatter]="fournisseurFormatter" [formControl]="searchFournInput"
                                                [resultFormatter]="fournisseurFormatter" class="form-control pl-4"
                                                placeholder="Rechercher par raison sociale" [readonly]="isReadOnly" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="mdi mdi-magnify text-dark"></i>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                                <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                                    <div class="col-md-4 my-1">
                                        <div class="form-group mb-0 mb-0">
                                            <label for="groupeEntreprise" class="form-label p-0 col-12">Groupe Entreprise <ng-container *ngIf="!isReadOnly">
                                                <span style="color: #b4b4b4; font-size: 0.85rem">(Obligatoire)</span> <span class="text-danger">*</span>
                                            </ng-container></label>
    
                                            <div class="input-group picker-input">
                                                <input type="text" id="groupeEntreprise" [ngbTypeahead]="searchGroupeEntreprise"
                                                    [inputFormatter]="groupeEntrepriseFormatter" formControlName="groupe"
                                                    [resultFormatter]="groupeEntrepriseFormatter" class="form-control pl-4"
                                                    placeholder="Rechercher par raison sociale" [readonly]="isReadOnly" />
    
                                                <div class="picker-icons picker-icons-alt">
                                                    <i class="mdi mdi-magnify text-dark"></i>
                                                </div>
    
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>
                                

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0">
                                        <label for="email" class="form-label p-0 col-12">
                                            Email Fournisseur <ng-container *ngIf="!isReadOnly">
                                                <span style="color: #b4b4b4; font-size: 0.85rem">(Obligatoire)</span> <span class="text-danger">*</span>
                                            </ng-container>
                                        </label>
                                        <div class="input-group picker-input">
                                            <input type="text" id="email" formControlName="emailFournisseur"
                                                [readonly]="isReadOnly" class="form-control pl-4"
                                                placeholder="Ex: <EMAIL>"
                                                [ngClass]="{'is-invalid': f['emailFournisseur']?.invalid && (f['emailFournisseur']?.dirty || f['emailFournisseur']?.touched || submitted)}" />
                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-envelope-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['emailFournisseur']?.invalid && (f['emailFournisseur']?.dirty || f['emailFournisseur']?.touched || submitted)"
                                        class="text-danger">
                                        <div *ngIf="f['emailFournisseur'].errors['required']">
                                            champ obligatoire
                                        </div>
                                        <div *ngIf="f['emailFournisseur'].errors['emailFournisseur']">
                                            Email Invalide.
                                        </div>
                                        <div *ngIf="f['emailFournisseur'].errors['maxlength']">
                                            Email ne peut pas depasser 40 caractére.
                                        </div>
                                        <div *ngIf="f['emailFournisseur'].errors['pattern']">
                                            Email doit être au format: Ex : example&#64;domain.com
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0">
                                        <label for="gsm1" class="form-label p-0 col-12">
                                            GSM Fournisseur <span *ngIf="!isReadOnly" style="color: #b4b4b4; font-size: 0.85rem">(Facultatif)</span>
                                        </label>
                                        <div class="input-group picker-input">
                                            <input type="text" id="gsm1" formControlName="gsmFournisseur"
                                                [readonly]="isReadOnly" class="form-control pl-4"
                                                placeholder="Ex : 06XXXXXXXX" maxlength="10"
                                                [ngClass]="{'is-invalid': f['gsmFournisseur']?.invalid && (f['gsmFournisseur']?.dirty || f['gsmFournisseur']?.touched || submitted)}" />
                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-phone-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="f['gsmFournisseur']?.invalid && (f['gsmFournisseur']?.dirty || f['gsmFournisseur']?.touched || submitted)"
                                        class="text-danger">
                                        <span>GSM Invalide.</span>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0">
                                        <label for="nomDelegue" class="form-label p-0 col-12">
                                            Nom Délégué <span *ngIf="!isReadOnly" style="color: #b4b4b4; font-size: 0.85rem">(Facultatif)</span>
                                        </label>
                                        <div class="input-group picker-input">
                                            <input type="text" id="nomDelegue" formControlName="nomDelegue"
                                                [readonly]="isReadOnly" class="form-control pl-4"
                                                placeholder="Nom délégué fournisseur" />
                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-person text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0">
                                        <label for="prenomDelegue" class="form-label p-0 col-12">
                                            Prénom Délégué <span *ngIf="!isReadOnly" style="color: #b4b4b4; font-size: 0.85rem">(Facultatif)</span>
                                        </label>
                                        <div class="input-group picker-input">
                                            <input type="text" id="prenomDelegue" formControlName="prenomDelegue"
                                                [readonly]="isReadOnly" class="form-control pl-4"
                                                placeholder="Prénom délégué fournisseur" />
                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-person text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <hr class="w-100">

                            <div class="form-row mx-0 p-1" [formGroup]="$any(f['fournisseur'])">
                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0 mb-0">
                                        <label for="codeFourn" class="form-label p-0 col-12">Code Fournisseur</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="codeFourn" formControlName="code"
                                                class="form-control pl-4" placeholder="Code fournisseur"
                                                [readonly]="true" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-info-circle-fill text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0 mb-0">
                                        <label for="raisonSociale" class="form-label p-0 col-12">Raison Sociale</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="raisonSociale" formControlName="raisonSociale"
                                                class="form-control pl-4" placeholder="Raison sociale"
                                                [readonly]="true" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-info-circle-fill text-dark"></i>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div class="form-group mb-0">
                                        <label for="nomResponsable" class="form-label p-0 col-12">Nom
                                            Responsable</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="nomResponsable" formControlName="nomResponsable"
                                                class="form-control pl-4" placeholder="Nom responsable"
                                                [readonly]="true" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-person-circle text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 my-1">
                                    <div id="edit-pharmacie-statut">
                                        <div class="form-group mb-0">
                                            <label for="typeEntreprise" class="form-label p-0 col-12">Type
                                                Entreprise</label>

                                            <div class="input-group picker-input">
                                                <input type="text" id="typeEntreprise" placeholder="Type Entreprise" formControlName="typeEntreprise"
                                                    class="form-control pl-4" [readonly]="true" />

                                                <div class="picker-icons picker-icons-alt">
                                                    <i class="bi bi-building-fill text-dark"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 mt-1 mb-2">
                                    <div class="form-group mb-0">
                                        <label for="ville" class="form-label col-12 p-0">Ville</label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="ville" formControlName="ville"
                                                class="form-control pl-4" placeholder="Ville Fournisseur"
                                                [readonly]="true" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-geo-alt text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 mt-1 mb-2">
                                    <div class="form-group mb-0">
                                        <label for="localite" class="form-label col-12 p-0">Localité </label>

                                        <div class="input-group picker-input">
                                            <input type="text" id="localite" formControlName="localite"
                                                class="form-control pl-4" placeholder="Localité Fournisseur"
                                                [readonly]="true" />

                                            <div class="picker-icons picker-icons-alt">
                                                <i class="bi bi-geo-alt text-dark"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </ng-template>
                </li>
            </ul>
        </form>
        <div [ngbNavOutlet]="infoPharamcieNav"></div>
    </div>
</div>