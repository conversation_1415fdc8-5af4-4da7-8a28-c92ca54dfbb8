import { BalanceAchatGroupeDto, PharmacieEntreprise, SearchPharmacieEntreprise } from '@wph/federation-syndicats';
import { PharmacieEntrepriseCriteria } from './../../../../models/pharmacie-entreprise-criteria.model';
import { FederationSyndicatService } from './../../../../services/federation-syndicats.service';
import { Component, ElementRef, OnDestroy, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { CellClickEvent, GridComponent, GridDataResult, RowClassArgs } from "@progress/kendo-angular-grid";
import { Pagination } from "@wph/data-access";
import { AlertService, ICity, PlateformeService, Societe, SocieteType, StaticDataService } from "@wph/shared";
import { Observable, Subject, debounceTime, distinctUntilChanged, filter, forkJoin, map, of, switchMap, takeUntil } from "rxjs";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { GroupeEntreprise } from '../../../../models/groupe-entreprise.model';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { ExportPdf, ExportPdfService, FeatureFlagService, ScrollService, UserInputService } from '@wph/web/shared';
import { SelectedPlateforme } from '@wph/web/layout';

type PageMode = 'S' | 'P';
@Component({
    selector: 'app-saisie-member',
    templateUrl: './saisie-member.component.html',
    styleUrls: ['./saisie-member.component.scss']
})
export class SaisieMemberComponent implements OnInit, OnDestroy {
    unsubscribe$: Subject<boolean> = new Subject<boolean>();

    mode: PageMode = 'S';
    pageTitle: string = ''
    displayFilter: boolean;
    villes: ICity[];
    navigation: Pagination = { pageSize: 10, skip: 0 };
    searchMembreNavigation: Pagination = { pageSize: 10, skip: 0 };
    gridData: GridDataResult = { total: 0, data: [] };
    filterForm: FormGroup;
    readOnly: boolean;
    filterResult: PharmacieEntreprise[] = [];
    hasMore: boolean;
    activeTabIndex: string = '0';

    gridDataResult: GridDataResult = { total: 0, data: [] };

    gridFilterForm: FormGroup;

    filterSearch: FormControl = new FormControl();

    criteria: PharmacieEntrepriseCriteria;

    stautsLabelsValues: any[] = [
        { label: 'Tout', value: null },
        { label: 'Actif', value: true },
        { label: 'Inactif', value: false },
    ];

    membreStatutLabelsValues: any[] = [
        { label: 'Tout', value: null },
        { label: 'Déjà membre', value: true },
        { label: 'Disponible', value: false },
    ];

    exportPdfRef: ExportPdf;

    managedGroupe: GroupeEntreprise;
    currentPlateforme: SelectedPlateforme;
    plateformeAsync$: Observable<SelectedPlateforme>;

    isEditingBalance: boolean = false;

    members: PharmacieEntreprise[] = [];
    originalMembreBalanceMap: Record<number, BalanceAchatGroupeDto> = {};


    @ViewChild('grid') grid: GridComponent;
    @ViewChild('gridContainer') gridContainer: ElementRef;

    constructor(
        private fb: FormBuilder,
        private router: Router,
        private modalService: NgbModal,
        private alertService: AlertService,
        private scrollService: ScrollService,
        private exportPdfServ: ExportPdfService,
        private userInputService: UserInputService,
        private plateformeService: PlateformeService,
        private staticDataService: StaticDataService,
        private featureFlagService: FeatureFlagService,
        private federationSyndicatService: FederationSyndicatService
    ) {
        this.initFilterForm();
        this.initGridFilterForm();
        this.currentPlateforme = this.plateformeService.getCurrentPlateforme();

        this.plateformeAsync$ = this.plateformeService.currentPlateforme$;
    }

    ngOnInit(): void {
        this.getListeVilles(), this.buildExport();
        this.filterResult = [];
        this.federationSyndicatService.getMyGroupe().then(myGroupe => {
            this.managedGroupe = myGroupe;
            this.criteria = new PharmacieEntrepriseCriteria({ groupeEntrepriseDTO: this.managedGroupe, typeEntreprises: [SocieteType.CLIENT] });

            this.fetchMembreOfCurrentGroupe();
        });

        this.listenToScrollPosition(), this.listenToFilterSearchChanges(), this.fetchParametresGroupe();
    }

    private fetchParametresGroupe(): void {
        this.gridDataResult = this.featureFlagService.toGridList();
    }

    vider(): void {
        this.navigation.skip = 0;

        this.gridFilterForm.reset();
        this.criteria = new PharmacieEntrepriseCriteria({ groupeEntrepriseDTO: this.managedGroupe, typeEntreprises: [SocieteType.CLIENT] });

        this.fetchMembreOfCurrentGroupe()
    }

    buildExport(): void {
        this.exportPdfRef = this.exportPdfServ
            .ref<PharmacieEntreprise>()
            .setTitle('Liste des Membres')
            .addColumn('raisonSociale', 'Raison Sociale', { width: 140 })
            .addColumn('nomResponsable', 'Pharmacien(ne)', {
                width: 160, transform: (value) => {
                    return `Dr. ${value}`;
                }
            })
            .addColumn('*', 'Ville / Localité', {
                width: 100, transform: (value) => {
                    return value?.ville || value?.localite;
                }
            })
            .addColumn('gsm1', 'GSM', {
                width: 100, transform: (value) => {
                    return value || 'GSM indisponible';
                }
            })
            .addColumn('telephone', 'Téléphone', {
                width: 100, transform: (value) => {
                    return value || 'Tél indisponible';
                }
            })
            .addColumn('email', 'Email', {
                width: 100, transform: (value) => {
                    return value || 'Email indisponible';
                }
            })
            .addColumn('*', 'Rôle', {
                width: 60, transform: (value) => {
                    return (value?.id === this.managedGroupe?.responsablesGroupe[0]?.id) ? 'Responsable' : 'Membre';
                }
            })
            .addColumn('statutMembreGroupe', 'Statut', {
                width: 60, transform: (value) => {
                    return value ? 'Actif' : 'Inactif';
                }
            })
            .setData([]);
    }

    fetchMembreOfCurrentGroupe(): void {
        forkJoin([
            this.federationSyndicatService.searchPharmacieEntreprise(this.navigation, this.criteria),
            this.fetchBalanceMembresGroupe(this.managedGroupe?.id)
        ])
            .subscribe(([res, balance]) => {
                this.members = res.content || [];
                
                this.members.forEach((membre) => this.calculateBalanceForMember(membre, balance));
                this.members = res.content?.filter(mem => mem?.id !== this.managedGroupe?.responsablesGroupe[0]?.id);
                
                this.setGridData();

                this.exportPdfRef.setData(this.gridData.data);
                this.exportPdfRef.setHeaderInfo([{
                    title: 'Nom du groupe',
                    value: this.managedGroupe?.raisonSociale,
                },
                {
                    title: 'Nombre de membres',
                    value: this.managedGroupe?.nbrMembres.toString(),
                }, {
                    title: "Ville",
                    value: `${this.managedGroupe?.ville} ${this.managedGroupe?.localite !== null ? `, ${this.managedGroupe?.localite}` : ''}`,
                }
                ]);
            });
    }

    setGridData() {
        this.gridData = {
            total: !this.navigation.skip ? this.members.length + 1 : this.members.length,
            data: !this.navigation.skip ? [this.managedGroupe?.responsablesGroupe[0], ...this.members] : this.members
        };
    }

    rowClass(row: RowClassArgs): string {
        return (row.index === 0) ? 'row-responsable' : '';
    }

    pageChange(_event: any) {
        this.fetchMembreOfCurrentGroupe();
    }

    listenToFilterSearchChanges(): void {
        this.filterSearch.valueChanges
            .pipe(
                takeUntil(this.unsubscribe$),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe((term: string) => {
                this.navigation.skip = 0;
                this.criteria = new PharmacieEntrepriseCriteria({ nomResponsable: term, groupeEntrepriseDTO: this.managedGroupe, typeEntreprises: [SocieteType.CLIENT] });

                this.fetchMembreOfCurrentGroupe();
            });
    }

    listenToScrollPosition(): void {
        this.scrollService.reachedBottom$
            .pipe(
                takeUntil(this.unsubscribe$),
                filter((state) => !!state)
            )
            .subscribe((_state) => {
                if (this.hasMore) {
                    if (
                        this.searchMembreNavigation.skip !==
                        (this.searchMembreNavigation.skip + this.searchMembreNavigation.pageSize)
                    ) {
                        this.searchMembreNavigation.skip += this.searchMembreNavigation.pageSize;

                        this.appliquerFiltre(true);
                    }
                }
            });
    }

    searchTerm: string = '';


    appliquerFiltre(loadMore = false): void {
        const { ville, raisonSociale, ...payload } = this.filterForm.getRawValue();
        this.searchTerm = raisonSociale || '';

        const criteria = new PharmacieEntrepriseCriteria({ ...payload, raisonSociale: raisonSociale, ville: ville?.labelFr || ville, typeEntreprises: [SocieteType.CLIENT] });

        this.federationSyndicatService.searchPharmacieEntreprise(this.searchMembreNavigation, criteria).subscribe({
            next: (res) => {
                this.hasMore = !res?.last;
                if (loadMore) {
                    this.filterResult.push(...res?.content);
                } else {
                    this.filterResult = res?.content || [];
                }
            },
            error: (err) => {
                console.error('Error fetching results:', err);
            }
        });
    }

    appliquerGridFiltre(): void {
        const { ville, ...payload } = this.gridFilterForm.getRawValue();

        this.navigation.skip = 0;
        this.criteria = new PharmacieEntrepriseCriteria({ ...payload, groupeEntrepriseDTO: this.managedGroupe, ville: ville?.labelFr || ville, typeEntreprises: [SocieteType.CLIENT] });

        this.fetchMembreOfCurrentGroupe();
    }

    viderFiltre(): void {
        this.filterForm.reset();
        this.filterResult = [];
        this.searchTerm = '';

        this.hasMore = false;
        this.searchMembreNavigation.skip = 0;
    }


    getListeVilles(): void {
        this.staticDataService.getListCitiesByCountryId(1)
            .subscribe(res => {
                this.villes = res;
            })
    }

    filterListePharmacien(term: string): Observable<SearchPharmacieEntreprise> {
        const payload = new PharmacieEntrepriseCriteria({ nomResponsable: term?.toLowerCase() });

        return this.federationSyndicatService.searchPharmacieEntreprise({ pageSize: 10, skip: 0 }, payload);
    }

    searchChefGroupe = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                return term?.length > 1 ?
                    this.filterListePharmacien(term) :
                    of({ content: [] });
            }),
            map(res => {
                return res?.content?.slice(0, 10);
            })
        );

    selectItem(item: PharmacieEntreprise): void {
        const selected: PharmacieEntreprise = item;

        if (!selected?.statutMembreGroupe) {
            this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir attacher la pharmacie: <b>${selected?.raisonSociale}</b> comme membre au groupe ?`).then(
                () => {
                    // this.modalService.dismissAll();

                    this.attacherMembre(selected);
                },
                () => null
            );
        }
    }

    sendMailToUser(item: PharmacieEntreprise) {
        if (!item?.gsm1 && !item?.email) {
            this.alertService.identifiantsManquants("Les données de l'utilisateur sélectionné sont incomplètes.", 'Données Indisponible', item?.id);
        } else {
            this.federationSyndicatService.envoyerIdentifiantsParMail([item.id]).subscribe(res => {
                this.alertService.mailSent(`Un message avec login et mot de passe est envoyer à la pharmacie <b>${item?.raisonSociale}</b>`, 'Identifiants Envoyés');
            });
        }
    }

    attacherMembre(membre: PharmacieEntreprise): void {
        this.federationSyndicatService.ajouterMembreAuGroupeEntreprise(this.managedGroupe?.id, membre?.id).subscribe(res => {
            this.alertService.successAlt(`La pharmacie: ${membre?.raisonSociale} a été attachée comme membre du groupe avec succès.`, 'Membre Attaché', 'MODAL');
            this.appliquerFiltre();
            this.fetchMembreOfCurrentGroupe();
        });
    }

    detacherMembre(membre: PharmacieEntreprise): void {
        this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir détacher la pharmacie <b>${membre?.raisonSociale}</b> du groupe ?`).then(
            () => {
                this.modalService.dismissAll();

                this.federationSyndicatService.retirerMembreDuGroupeEntreprise(membre.id, this.managedGroupe?.id).subscribe(res => {
                    this.fetchMembreOfCurrentGroupe();
                    this.alertService.successAlt(`La pharmacie <b>${membre?.raisonSociale}</b> a été détachée du groupe avec succès.`, 'Membre Detaché', 'MODAL');
                });
            },
            () => null
        );
    }

    initFilterForm(): void {
        this.filterForm = this.fb.group({
            raisonSociale: [null],
            nomResponsable: [null],
            ville: [null],
            localite: [null],
            statutMembreEntreprise: [null]
        });
    }

    initGridFilterForm(): void {
        this.gridFilterForm = this.fb.group({
            raisonSociale: [null],
            nomResponsable: [null],
            ville: [null],
            localite: [null],
            statutMembreEntreprise: [null]
        });
    }

    chefOuMembreFormatter = (result: Societe) => `${result?.nomResponsable}`

    searchMembre = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                return term?.length > 1 ?
                    this.filterListePharmacien(term) :
                    of({ content: [] });
            }),
            map(res => res?.content)
        );

    searchVilleOuLocalite = (text$: Observable<string>) =>
        text$.pipe(
            debounceTime(200),
            distinctUntilChanged(),
            switchMap(term => {
                return term?.length < 1 ? of([]) :
                    of(this.villes?.filter(ville => ville?.labelFr?.toLowerCase()?.includes(term?.toLowerCase())));
            }),
            map(res => res?.slice(0, 10))
        );

    villeFormatter = (result: ICity) => result?.labelFr;

    back(): void {
        this.router.navigateByUrl('/achats-groupes/groupes/membres');
    }


    suggererPharmacie(id?: number): void {
        if (id) {
            this.router.navigate([`/achats-groupes/pharmacies/edit/${id}`], {
                queryParams: { suggerer: true, from: 'param-memeber' },
            });
        } else {
            this.router.navigateByUrl('/achats-groupes/pharmacies/suggerer?from=param-memeber');
        }


    }
    modalRef: NgbModalRef;

    suggestPharmacyAndCloseModal() {
        this.suggererPharmacie();
        this.modalService.dismissAll();
    }


    openModal(content: TemplateRef<any>, size = 'xl'): void {
        this.hasMore = false;
        this.searchMembreNavigation.skip = 0;
        this.modalService.open(content, { size, windowClass: 'custom-modal-width', modalDialogClass: 'fs-radius-modal' }).result.then(
            (_result) => this.viderFiltre(),
            (_reason) => this.viderFiltre()
        );
    }

    activerOuDesactiverParametreGroupe(dataItem: any): void {
        this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir ${dataItem?.enabled ? 'désactiver' : 'activer'} le paramètre <b>${dataItem?.libelle}</b> ?`).then(
            () => { },
            () => null
        );
    }

    fetchBalanceMembresGroupe(groupId: number): Observable<BalanceAchatGroupeDto[]> {
        return this.federationSyndicatService.getBalanceGroupeEntrepriseById(groupId);
    }

    cellCloseHandler(args: any) {
        const { formGroup, dataItem } = args;

        if (formGroup.dirty) {
            const formValue = formGroup.value;

            dataItem.montantConsomme = formValue?.montantConsomme || 0;
            dataItem.montantSupportee = formValue?.montantSupportee || 0;
            dataItem.balance = formValue?.montantSupportee - formValue?.montantConsomme || 0;
        }
    }

    cellClick(event: CellClickEvent): void {
        const { sender, rowIndex, columnIndex, dataItem, column, isEdited } = event;

        if (column.field === 'montantSupportee' || column.field === 'montantConsomme') {
            if (!isEdited) {
                sender.editCell(rowIndex, columnIndex, this.createFormGroup(dataItem));
            }
        } else {
            return;
        }
    }

    handleCellCommit(event: KeyboardEvent, grid: GridComponent | null): void {
        if (grid && (event.key === 'Enter' || event.key === 'Tab' || event.key === 'Escape')) {
            if (grid.isEditing()) {
                grid.closeCell();
            }
        }
    }

    createFormGroup(dataItem: PharmacieEntreprise): FormGroup {
        return this.fb.group({
            balance: [dataItem?.balance || 0],
            montantConsomme: [dataItem?.montantConsomme || 0],
            montantSupportee: [dataItem?.montantSupportee || 0],
        });
    }

    initialiserBalance(): void {
        const gridContainerEl: HTMLDivElement = this.gridContainer?.nativeElement as HTMLDivElement;
        this.isEditingBalance = true;

        if (gridContainerEl) {
            setTimeout(() => {
                const itemAtIndex0 = gridContainerEl.querySelector(`div[data-prefix="montantSupportee-0"]`) as HTMLDivElement;

                if (itemAtIndex0) {
                    itemAtIndex0?.parentElement?.click();
                }
            }, 200);
        }
    }

    restoreOriginalMembreBalance(): void {
        this.members.forEach((membre) => {
            const originalBalance = this.originalMembreBalanceMap[membre.id];
            if (originalBalance !== undefined) {
                const responsableGroupe = this.managedGroupe.responsablesGroupe.find(
                    (responsable) => responsable.id === membre.id
                );

                if (responsableGroupe) {
                    responsableGroupe.montantConsomme = originalBalance.montantConsomme || 0;
                    responsableGroupe.montantSupportee = originalBalance.montantSupportee || 0;
                    responsableGroupe.balance = originalBalance.montantSupportee - originalBalance.montantConsomme || 0;
                } else {
                    membre.montantConsomme = originalBalance.montantConsomme || 0;
                    membre.montantSupportee = originalBalance.montantSupportee || 0;
                    membre.balance = originalBalance.montantSupportee - originalBalance.montantConsomme || 0;
                }
            }
        });

        this.isEditingBalance = false;
        this.setGridData();
    }

    enregistrerBalanceMembres(): void {
        this.userInputService.confirmAlt('Confirmation', 'Êtes-vous sûr de vouloir enregistrer les balances des membres ?').then(
            () => {
                const payload = this.retrieveBalanceMembresPayload();

                this.federationSyndicatService.saveMultipleBalanceGroupeEntreprise(payload).subscribe(res => {
                    this.alertService.successAlt(`Les balances des membres du groupe ont été enregistrées avec succès.`, 'Balance Enregistrée', 'MODAL');

                    this.isEditingBalance = false, this.originalMembreBalanceMap = {};
                    this.fetchMembreOfCurrentGroupe();
                });
            },
            () => null
        );
    }

    retrieveBalanceMembresPayload(): BalanceAchatGroupeDto[] {
        const balanceMembresUncommitted = this.gridData.data as PharmacieEntreprise[];
        const balanceMembres = balanceMembresUncommitted.map((membre) => {
            const { montantConsomme, montantSupportee, balance, ...membreGroupe } = membre as PharmacieEntreprise;
            return new BalanceAchatGroupeDto({
                ...this.originalMembreBalanceMap[membre.id],
                membreGroupe,
                groupeAchatGroupe: this.managedGroupe,
                montantConsomme: membre.montantConsomme || 0,
                montantSupportee: membre.montantSupportee || 0,
            });
        });

        return balanceMembres;
    }

    private calculateBalanceForMember(member: PharmacieEntreprise, balance: BalanceAchatGroupeDto[]) {
        const balanceMembre = this.findBalanceByMembreId(member.id, balance) || new BalanceAchatGroupeDto({});

        // Use the member reference from responsablesGroupe if it exists
        const responsableGroupe = this.managedGroupe.responsablesGroupe.find(
            (responsable) => responsable.id === member.id
        );
        const targetMember = responsableGroupe || member;
        
        this.originalMembreBalanceMap[targetMember.id] = balanceMembre;
        targetMember.montantConsomme = balanceMembre.montantConsomme || 0;
        targetMember.montantSupportee = balanceMembre.montantSupportee || 0;
        targetMember.balance = (balanceMembre.montantSupportee || 0) - (balanceMembre.montantConsomme || 0);
    }

    findBalanceByMembreId(membreId: number, balanceMembres: BalanceAchatGroupeDto[]): BalanceAchatGroupeDto | undefined {
        return balanceMembres.find((balance) => balance.membreGroupe?.id === membreId);
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }

}
